// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    ext.kotlin_version = "1.6.21"

    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:7.1.2'
        classpath 'org.jetbrains.kotlin:kotlin-gradle-plugin:1.6.21'
        //classpath "io.insert-koin:koin-core:3.5.3"
        classpath 'com.google.gms:google-services:4.3.10'  // Google Services plugin
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        //classpath 'com.jfrog.bintray.gradle:gradle-bintray-plugin:1.4'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.9.1'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }


}
allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url {'https://raw.github.com/takuseno/android-countrylist/master/repository'}}
        maven { url 'https://maven.google.com' }
        maven { url 'https://clojars.org/repo'}
        maven { url "https://jitpack.io" }
        maven { url 'https://maven.testfairy.com' }
        maven {url "https://maven.java.net/content/groups/public/" }
        flatDir {
            dirs 'libs'
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}