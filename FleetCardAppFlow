<mxfile host="app.diagrams.net" modified="2021-12-05T14:00:36.075Z" agent="5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.45 Safari/537.36" etag="QE6G8u71KeGcS6y4L9lD" version="15.9.1" type="gitlab">
  <diagram id="********************" name="Page-1">
    <mxGraphModel dx="2062" dy="702" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1200" pageHeight="1600" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="zdY7vLtB09_v8gJyN3xG-1" value="Fleet Card App &lt;br style=&quot;font-size: 16px&quot;&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=24;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="441" y="40" width="320" height="40" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=18;startArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-81">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-44" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;startArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-102" target="zdY7vLtB09_v8gJyN3xG-45">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="230" y="320" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-2" value="" style="image;html=1;image=img/lib/clip_art/people/Suit_Man_128x128.png;fontSize=18;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="30" y="80" width="80" height="80" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-7" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=18;startArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-85">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-5" value="&lt;font style=&quot;font-size: 12px&quot;&gt;Customer wants to Fuel&amp;nbsp;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;fontSize=18;labelBackgroundColor=none;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="10" y="250" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;startArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-87" target="zdY7vLtB09_v8gJyN3xG-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-8" value="" style="dashed=0;outlineConnect=0;html=1;align=center;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;shape=mxgraph.webicons.android;gradientColor=#DFDEDE;labelBackgroundColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="18.799999999999955" y="390" width="102.4" height="102.4" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;startArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-89" target="zdY7vLtB09_v8gJyN3xG-15">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-11" value="&lt;div&gt;&lt;span&gt;Select&lt;/span&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;&lt;span&gt;&amp;nbsp;Product Fuel&lt;/span&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;dashed=0;verticalAlign=top;fillColor=#b1ddf0;strokeColor=#10739e;fontStyle=1;align=center;horizontal=1;" vertex="1" parent="1">
          <mxGeometry x="11" y="593.6" width="119" height="46.4" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-15" target="zdY7vLtB09_v8gJyN3xG-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-15" value="&lt;br&gt;Enter Attendant Code" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#9673a6;fillColor=#e1d5e7;" vertex="1" parent="1">
          <mxGeometry x="12.75" y="707.5" width="114.5" height="62.5" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-36" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-19" target="zdY7vLtB09_v8gJyN3xG-35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-19" target="zdY7vLtB09_v8gJyN3xG-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-19" value="&lt;br&gt;Full Tank" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#0e8088;fillColor=#b0e3e6;" vertex="1" parent="1">
          <mxGeometry x="390" y="680" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-40" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-22" target="zdY7vLtB09_v8gJyN3xG-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-22" value="&lt;br&gt;Amount" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#0e8088;fillColor=#b0e3e6;" vertex="1" parent="1">
          <mxGeometry x="390" y="760" width="120" height="50" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-26" target="zdY7vLtB09_v8gJyN3xG-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-34" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.25;entryDx=0;entryDy=0;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-26" target="zdY7vLtB09_v8gJyN3xG-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-26" value="&lt;br&gt;Select Fuel Mode" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#10739e;fillColor=#b1ddf0;" vertex="1" parent="1">
          <mxGeometry x="190" y="707.5" width="120" height="62.5" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-76" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-29" target="zdY7vLtB09_v8gJyN3xG-35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-29" value="&lt;br&gt;Enter Amount" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#9673a6;fillColor=#e1d5e7;" vertex="1" parent="1">
          <mxGeometry x="610" y="780" width="120" height="53.75" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-43" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-35" target="zdY7vLtB09_v8gJyN3xG-42">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="670" y="610" />
              <mxPoint x="670" y="610" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-35" value="&lt;br&gt;Mode of Payment" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#9673a6;fillColor=#e1d5e7;" vertex="1" parent="1">
          <mxGeometry x="610" y="652.5" width="120" height="55" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-79" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-42" target="zdY7vLtB09_v8gJyN3xG-45">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-42" value="Customer Selected&amp;nbsp;Fleet Card&amp;nbsp;Payment" style="whiteSpace=wrap;html=1;dashed=0;fontStyle=1;verticalAlign=top;strokeColor=#6c8ebf;fillColor=#dae8fc;" vertex="1" parent="1">
          <mxGeometry x="610" y="515" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-47" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;startArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-103" target="zdY7vLtB09_v8gJyN3xG-46">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="345" y="400" />
              <mxPoint x="345" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-45" value="Customer Insert the Fleet Card to allow app to capture the data" style="rounded=0;whiteSpace=wrap;html=1;labelBackgroundColor=none;fontSize=12;fillColor=#fff2cc;strokeColor=#d6b656;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="280" y="280" width="130" height="70" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-51" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-46" target="zdY7vLtB09_v8gJyN3xG-50">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-46" value="App Check the Fleet Card Authorization and Request for Card Pin Code" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="270" y="420" width="140" height="70" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-53" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-50" target="zdY7vLtB09_v8gJyN3xG-52">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-58" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-50" target="zdY7vLtB09_v8gJyN3xG-57">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-50" value="Is Authorized" style="rhombus;whiteSpace=wrap;html=1;rounded=0;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="500" y="397.5" width="120" height="115" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-60" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-52" target="zdY7vLtB09_v8gJyN3xG-59">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-52" value="Payment Processing and Check the Card Limits&amp;nbsp;" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#b0e3e6;strokeColor=#0e8088;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="700" y="425" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-57" value="Abort Transaction" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8cecc;strokeColor=#b85450;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="500" y="230" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-62" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-59" target="zdY7vLtB09_v8gJyN3xG-61">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-59" value="Select Pump for Fueling" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#b1ddf0;strokeColor=#10739e;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="425" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-64" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-61" target="zdY7vLtB09_v8gJyN3xG-63">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-61" value="Select Nozzle" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#b1ddf0;strokeColor=#10739e;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="565" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-71" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-63" target="zdY7vLtB09_v8gJyN3xG-70">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-63" value="Authorize Pump and Start Fueling" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="705" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-65" value="" style="sketch=0;aspect=fixed;html=1;points=[];align=center;image;fontSize=12;image=img/lib/mscae/Key_Vaults.svg;labelBackgroundColor=none;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="390" y="470" width="19.2" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-75" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1;startArrow=none;" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-122" target="zdY7vLtB09_v8gJyN3xG-74">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-70" value="Once Fuel Done Debit amount from the Card&amp;nbsp;" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#f8cecc;strokeColor=#b85450;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="845" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-74" value="Print Ticket for Customer and Attendant" style="whiteSpace=wrap;html=1;rounded=0;fillColor=#e1d5e7;strokeColor=#9673a6;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="1010" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-77" value="Yes" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="640" y="425" width="40" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-78" value="No" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="570" y="370" width="30" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-81" value="Step 1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="45" y="190" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-82" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=18;endArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-2" target="zdY7vLtB09_v8gJyN3xG-81">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="160" as="sourcePoint" />
            <mxPoint x="70" y="235" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-85" value="Step 1" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="45" y="340" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-86" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=18;endArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-5" target="zdY7vLtB09_v8gJyN3xG-85">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="310" as="sourcePoint" />
            <mxPoint x="70" y="390" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-87" value="Step 2" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="45" y="530" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-88" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;endArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-8" target="zdY7vLtB09_v8gJyN3xG-87">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70" y="492.4000000000001" as="sourcePoint" />
            <mxPoint x="70.5" y="593.5999999999999" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-89" value="Step 3" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="45" y="660" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-90" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;endArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-11" target="zdY7vLtB09_v8gJyN3xG-89">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="70.5" y="640" as="sourcePoint" />
            <mxPoint x="70.09999999999991" y="707.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-91" value="Step 4" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="127.25" y="740" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-92" value="Step 5" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="350" y="740" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-93" value="Step 6" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="530" y="740" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-95" value="Step 7" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="675" y="740" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-99" value="Step 8" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="645" y="606.8" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-100" value="Step 9" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="345" y="550" width="50" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-103" value="Step 11" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="315" y="377.5" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-104" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;endArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-45" target="zdY7vLtB09_v8gJyN3xG-103">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="345" y="350" as="sourcePoint" />
            <mxPoint x="345" y="420" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-102" value="Step 10" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="315" y="170" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-105" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;endArrow=none;fontStyle=1" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-2" target="zdY7vLtB09_v8gJyN3xG-102">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="345" y="280" as="targetPoint" />
            <mxPoint x="110" y="120" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-106" value="Step 12" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="430" y="431.2" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-107" value="Step 14" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="620" y="460" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-108" value="Step 15" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="830" y="465" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-109" value="Step 16" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="930" y="520" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-110" value="Step 17" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="930" y="660" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-111" value="Step 18" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="930" y="796.88" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-112" value="Step 19" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="900" y="920" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-113" value="Step 13" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="530" y="340" width="60" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-114" value="" style="shape=image;verticalLabelPosition=bottom;labelBackgroundColor=#ffffff;verticalAlign=top;aspect=fixed;imageAspect=0;image=data:image/jpeg,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;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1140" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-115" value="&lt;b style=&quot;font-size: 12px;&quot;&gt;(Before Transaction Flow)&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;" vertex="1" parent="1">
          <mxGeometry x="515" y="80" width="160" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-118" value="" style="aspect=fixed;perimeter=ellipsePerimeter;html=1;align=center;shadow=0;dashed=0;spacingTop=3;image;image=img/lib/active_directory/web_server.svg;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1055" y="80" width="85" height="106.25" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-120" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-119" target="zdY7vLtB09_v8gJyN3xG-118">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-119" value="Is &lt;br&gt;Network &lt;br&gt;available" style="rhombus;whiteSpace=wrap;html=1;labelBackgroundColor=none;fillColor=#d5e8d4;strokeColor=#82b366;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1040" y="300" width="112.5" height="100" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-121" value="Transaction send to FBS" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1020" y="230" width="160" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-124" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;fontSize=12;fontColor=#000000;" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-122" target="zdY7vLtB09_v8gJyN3xG-119">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1096" y="960" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-122" value="Save Transaction Locally" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="880" y="950" width="160" height="20" as="geometry" />
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-123" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontSize=12;fontColor=#000000;fontStyle=1;endArrow=none;" edge="1" parent="1" source="zdY7vLtB09_v8gJyN3xG-70" target="zdY7vLtB09_v8gJyN3xG-122">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="960" y="905" as="sourcePoint" />
            <mxPoint x="960" y="1010" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="zdY7vLtB09_v8gJyN3xG-125" value="Step 20" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=12;fontColor=#000000;labelBackgroundColor=default;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="1066.25" y="650" width="60" height="20" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
