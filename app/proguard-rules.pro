# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

-target 1.6
-optimizations method/*,!code/simplification/arithmetic,code/*
-optimizationpasses 2
-allowaccessmodification
-mergeinterfacesaggressively
-useuniqueclassmembernames
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,SourceFile
#-renamesourcefileattribute SourceFile
-adaptresourcefilenames **.properties
-adaptresourcefilecontents **.properties,META-INF/MANIFEST.MF
-verbose
-dontwarn android.os.**,android.net.**



-keep class android.support.v4.*

-keep public class * {
    public protected <fields>;
    public <fields>;
    public protected <methods>;
    public <methods>;
}

-keepclassmembers class * extends java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# android
-keep public class * extends android.app.Activity

-keep public class * extends android.app.Application

-keep public class * extends android.app.Service

-keep public class * extends android.content.BroadcastReceiver

-keep public class * extends android.content.ContentProvider

-keep public class * extends android.app.Fragment

-keep public class * extends LinearLayout

-keep public class * extends android.view.View {
    public <init>(android.content.Context);
    public <init>(android.content.Context,android.util.AttributeSet);
    public <init>(android.content.Context,android.util.AttributeSet,int);
    public void set*(...);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context,android.util.AttributeSet);
}

-keepclasseswithmembers class * {
    public <init>(android.content.Context,android.util.AttributeSet,int);
}

-keepclassmembers class * extends android.content.Context {
    public void *(android.view.View);
    public void *(android.view.MenuItem);
}

-keepclassmembers class * extends android.os.Parcelable {
    static ** CREATOR;
}

-keepclassmembers class * extends android.webkit.JavascriptInterface{
    <methods>;
}

-keep public abstract class * extends @android.os.IInterface * {
    public <fields>;
    public <methods>;
}

# -----------------keep all NeptuneLiteApi packages
-keep class com.pax.dal.** {
    <fields>;
    <methods>;
}

-keep class com.pax.nep.** {
    <fields>;
    <methods>;
}

-keep class com.pax.neptunelite.** {
    <fields>;
    <methods>;
}

-keep class com.pax.neptuneliteapi.** {
    <fields>;
    <methods>;
}

# Keep - Library. Keep all public and protected classes, fields, and methods.
-keep public class * {
    public protected <fields>;
    public protected <methods>;
}

# Also keep - Enumerations. Keep the special static methods that are required in
# enumeration classes.
-keepclassmembers enum  * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# Keep names - Native method names. Keep all native class/method names.
-keepclasseswithmembers,allowshrinking class * {
    native <methods>;
}

-keep class com.google.firebase.provider.** { *; }


-optimizations !method/removal/parameter


-dontwarn javax.annotation.Nullable
-dontwarn javax.annotation.ParametersAreNonnullByDefault

-dontwarn javax.annotation.**
-dontwarn javax.swing.**
-dontwarn javax.security.**
-dontwarn javax.script.**
-dontwarn javax.management.**
-dontwarn javax.transaction.**


-dontwarn java.awt.**
-dontwarn java.rmi.**
-dontwarn java.applet.**
-dontwarn java.beans.**
-dontwarn org.conscrypt.**

-keep class app.rht.petrolcard.helpers.fuelpos.** { *; }
-keep interface app.rht.petrolcard.helpers.fuelpos.** { *; }
-dontwarn app.rht.petrolcard.helpers.fuelpos.**


-keepattributes *Annotation*, Signature, Exception
-repackageclasses

-dontwarn javax.naming.**
-dontwarn javax.servlet.**
-dontwarn org.slf4j.**


-optimizationpasses 5



######## debuging
#-dontobfuscate
#-addconfigurationdebugging

-keep class net.sqlcipher.** { *; }
-keep class net.sqlcipher.database.* { *; }

-keep,includedescriptorclasses class net.sqlcipher.** { *; }
-keep,includedescriptorclasses interface net.sqlcipher.** { *; }

-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase
-keepnames class org.jsoup.nodes.Entities

-keep class in.uncod.android.bypass.Document { *; }
-keep class in.uncod.android.bypass.Element { *; }


-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

-keep class org.apache.** { *; }
-keep interface org.apache.http.**

-dontwarn org.apache.**


-keep class org.jaxen.** { *; }
-dontwarn org.jaxen.**

-keep class com.sun.** { *; }
-dontwarn com.sun.**

-keep class org.jline.** { *; }
-dontwarn org.jline.**

-keep class bsh.** { *; }
-dontwarn bsh.**

-keep class javassist.** { *; }
-dontwarn javassist.**



#-keep class sun.misc.** { *; }
#-dontwarn sun.misc.**

-keep class com.ibm.icu.** { *; }
-dontwarn com.ibm.icu.**

-keep class org.osgi.** { *; }
-dontwarn org.osgi.**

-keep class org.slf4j.** { *; }
-dontwarn org.slf4j.**

-keep class com.sleepycat.** { *; }
-dontwarn com.sleepycat.**

-keep class java.lang.** { *; }
-dontwarn java.lang.**

-keep class org.jasypt.** { *; }
-dontwarn org.jasypt.**


-keep class org.checkerframework.** { *; }
-dontwarn org.checkerframework.**


##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**

# Gson specific classes
-keep class sun.misc.Unsafe { *; }

#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson

-keep class app.rht.petrolcard.models.** {*;}
-keep class app.rht.petrolcard.parsers.** {*;}

#-keep class app.rht.petrolcard.models.Transaction
#-keep class app.rht.petrolcard.models.TransactioTaxi

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
#-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}

##---------------End: proguard configuration for Gson  ----------





-keepattributes EnclosingMethod


-keep class net.gotev.uploadservice.** { *; }



-keep class com.squareup.** { *; }
-keep interface com.squareup.** { *; }
-keep class retrofit2.** { *; }
-keep class okhttp3.** { *; }
-keep interface retrofit2.** { *;}

-keep class com.google.android.gms.gcm.** {*;}
-dontwarn com.google.android.gms.gcm.**

#-keep interface org.apache.mina.core.** { *;}
#-dontwarn org.apache.mina.core.**



#-keepclasseswithmembers,allowshrinking,allowobfuscation class * {
#    @retrofit2.http.* <methods>;
#}
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

-dontwarn rx.**
-dontwarn retrofit2.**
-dontwarn okhttp3.**
-dontwarn okio.**

### conflit between retrofit and gson
#version of retrofit and gson
#ext.retrofit2_version = '2.3.0'
#ext.gson_version = '2.8.1'

##related proguard setting
##----------retrofit for storelletcommonlib----##
## BEGIN RETROFIT
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions
## END RETROFIT


## BEGIN VIMEO-NETWORKING
-dontwarn javax.annotation.**
-dontwarn javax.inject.**
-dontwarn sun.misc.Unsafe
## END VIMEO-NETWORKING


## BEGIN GSON
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-keep class sun.misc.Unsafe { *; }
-keep class com.google.gson.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.vimeo.networking.** { *; }
-keepclassmembers enum * { *; }

## END GSON
##---------------------------------------------##


-keep public class * implements org.apache.mina.core.future.IoFutureListener
-keep class org.apache {*;}
-keepclassmembers class org.apache.** {
    public *;
}

#To prevent a warning message:
-dontwarn org.ietf.jgss.**

#To prevent supporting jars from getting obfuscated you would need something like:
-keep class javax.** { *; }
-keep class org.** { *; }
-keep class android.** { *; }

-dontwarn javax.management.**
-dontwarn org.apache.commons.logging.**



# ***** Apache


#From here Apache Commons
-keep class org.apache.http.**
-keep interface org.apache.http.**

-dontwarn org.apache.commons.**

#From here Apache mira
-dontwarn javax.security.sasl.*
-dontwarn org.ietf.jgss.*
-dontwarn org.apache.mina.core.session.DefaultIoSessionDataStructureFactory$DefaultIoSessionAttributeMap #Java 8 not implememnted
-dontwarn org.apache.mina.util.ExpiringMap #Java 8 not implememnted
-keepclassmembers class * implements org.apache.mina.core.service.IoProcessor {
    public <init>(java.util.concurrent.ExecutorService);
    public <init>(java.util.concurrent.Executor);
    public <init>();
}
-dontwarn org.apache.sshd.**
-dontwarn org.apache.mina.**
-dontwarn org.slf4j.**
-dontwarn io.netty.**

-keepattributes SourceFile,LineNumberTable,Signature,*Annotation*

-keep class org.spongycastle.** {*;}

# SSHd requires mina, and mina uses reflection so some classes would get deleted
-keep class org.apache.mina.** {*;}
-keep class org.apache.sshd.** {*;}

-keep class org.kde.kdeconnect.** {*;}

-dontwarn org.mockito.**
-dontwarn sun.reflect.**
-dontwarn android.test.**
-dontwarn java.lang.management.**
-dontwarn javax.**


# PDF CREATOR

 -keep class javax.xml.crypto.dsig.** { *; }
 -dontwarn javax.xml.crypto.dsig.**
 -keep class javax.xml.crypto.** { *; }
 -dontwarn javax.xml.crypto.**
 -keep class org.spongycastle.** { *; }
 -dontwarn org.spongycastle.**

#Retrofit
# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
# Retain generic type information for use by reflection by converters and adapters.
-keepattributes Signature
# Retain declared checked exceptions for use by a Proxy instance.
-keepattributes Exceptions
-keep class app.rht.petrolcard.modeN.** {
    <fields>;
    <methods>;
}
# TestFairy
 -keep class com.testfairy.** { *; }
 -dontwarn com.testfairy.**
 -keepattributes Exceptions, Signature, LineNumberTable
 -dontusemixedcaseclassnames

-keep public class app.rht.petrolcard.baseClasses.model.** {*;}
-keep public class app.rht.petrolcard.ui.esdsign.model.** {*;}
-keep public class app.rht.petrolcard.ui.iccpayment.model.** {*;}
-keep public class app.rht.petrolcard.ui.loyalty.model.** {*;}
-keep public class app.rht.petrolcard.ui.menu.model.** {*;}
-keep public class app.rht.petrolcard.ui.modepay.model.** {*;}
-keep public class app.rht.petrolcard.ui.nfc.model.** {*;}
-keep public class app.rht.petrolcard.ui.product.model.** {*;}
-keep public class app.rht.petrolcard.ui.reference.model.** {*;}
-keep public class app.rht.petrolcard.ui.settings.appupdate.model.** {*;}
-keep public class app.rht.petrolcard.ui.settings.card.common.model.** {*;}
-keep public class  app.rht.petrolcard.ui.settings.card.history.model.** {*;}
-keep public class  app.rht.petrolcard.ui.settings.card.unblockcard.model.** {*;}
-keep public class  app.rht.petrolcard.ui.settings.common.model.** {*;}
-keep public class  app.rht.petrolcard.ui.settings.operations.model.** {*;}
-keep public class  app.rht.petrolcard.ui.startup.model.** {*;}
-keep public class  app.rht.petrolcard.ui.ticket.model.** {*;}
-keep public class  app.rht.petrolcard.ui.transactionlist.model.** {*;}
-keep public class  app.rht.petrolcard.ui.updatecard.model.** {*;}
-keep public class  app.rht.petrolcard.apimodel.apiresponsel.** {*;}
