<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <LinearLayout
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/promptLayout"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:padding="10dp"
                android:layout_marginTop="5dp"
                android:orientation="horizontal"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >

                <com.github.ybq.android.spinkit.SpinKitView
                    android:id="@+id/progressBar"
                    style="@style/SpinKitView"
                    android:layout_width="wrap_content"
                    android:layout_gravity="center"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="9dp"
                    android:visibility="gone"
                    app:SpinKit_Color="@color/colorPrimary"
                    />

                <TextView
                    android:id="@+id/prompt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="0dp"
                    android:layout_marginStart="5dp"
                    android:gravity="center"
                    android:maxLines="2"
                    android:text="@string/ticket_printing"
                    android:textColor="@color/colorPrimary"
                    android:textSize="20sp"
                    android:textStyle="bold" />

            </LinearLayout>


            <LinearLayout
                android:id="@+id/insertCardImageLayout"
                android:gravity="center"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="0dp"
                android:visibility="visible"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">


                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/printing_view"
                    android:layout_width="300dp"
                    android:layout_height="300dp"
                    android:visibility="visible"
                    app:lottie_rawRes="@raw/ticket_blue"
                    app:lottie_repeatMode="restart"
                    app:lottie_enableMergePathsForKitKatAndAbove="true"
                    app:lottie_loop="true"
                    app:lottie_speed="0.9"
                    android:layout_gravity="center"
                    app:lottie_autoPlay="false"/>


                <ImageView
                    android:id="@+id/insertCardImageView"
                    android:layout_width="260dp"
                    android:layout_height="260dp"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    android:adjustViewBounds="false"
                    android:cropToPadding="false"
                    android:src="@drawable/invoice" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/animation_view_online"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="gone"
                    app:lottie_rawRes="@raw/payment_success"
                    app:lottie_repeatMode="restart"
                    app:lottie_enableMergePathsForKitKatAndAbove="true"
                    app:lottie_loop="false"
                    app:lottie_speed="1.2"
                    android:layout_gravity="center"
                    app:lottie_autoPlay="false"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/outCardImageLayout"
                android:layout_alignParentStart="true"
                android:gravity="center"
                android:visibility="gone"
                android:layout_marginTop="30dp"
                android:layout_gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.airbnb.lottie.LottieAnimationView
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginBottom="-80dp"
                    android:layout_marginEnd="100dp"
                    android:rotation="270"
                    android:layout_marginStart="-50dp"
                    android:visibility="visible"
                    android:layout_gravity="start"
                    app:lottie_rawRes="@raw/arrow_out"
                    app:lottie_repeatMode="restart"
                    app:lottie_enableMergePathsForKitKatAndAbove="true"
                    app:lottie_loop="true"
                    app:lottie_speed="0.9"
                    app:lottie_autoPlay="false"/>


                <ImageView
                    android:id="@+id/CardImageView"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="0dp"
                    android:layout_gravity="center"
                    android:visibility="visible"
                    android:adjustViewBounds="false"
                    android:cropToPadding="false"
                    android:src="@drawable/card_inn" />




            </LinearLayout>



        </LinearLayout>

    </RelativeLayout>
</layout>