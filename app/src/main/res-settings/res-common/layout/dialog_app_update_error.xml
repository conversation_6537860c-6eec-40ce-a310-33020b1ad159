<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="320dp"
            android:layout_centerInParent="true"
            android:background="@drawable/formulaire_activation_fidelity"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_80sdp"
                android:src="@drawable/update">

            </ImageView>

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="Title"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/server_not_connected"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/greyDark"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/not_connected_to_server"
                android:textColor="@color/greyDark"
                android:textSize="12sp"
                android:textStyle="bold"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title" />

            <TextView
                android:id="@+id/btn_update_cancel"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="10dp"
                android:layout_marginTop="@dimen/_20sdp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/okay"
                android:textColor="@color/colorWhite"
                android:textSize="16sp" />
        </LinearLayout>

    </RelativeLayout>
</layout>