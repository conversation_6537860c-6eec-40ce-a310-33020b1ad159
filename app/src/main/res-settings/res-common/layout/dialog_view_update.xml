<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="280dp"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_custom_update"
        android:paddingBottom="15dp">

        <TextView
            android:id="@+id/tv_update_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="150dp"
            android:textColor="@color/text_title"
            android:textSize="16sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.502"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textStyle="bold"
            tools:text="New Version available!"/>

        <ScrollView
            android:id="@+id/scrollView2"
            android:layout_width="match_parent"
            android:layout_height="150dp"
            android:layout_marginTop="10dp"
            android:overScrollMode="never"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tv_update_title">

            <TextView
                android:id="@+id/releaseNotes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="top"
                android:gravity="start"
                android:lineSpacingExtra="5dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:textColor="@color/text_content"
                android:textSize="14sp"
                tools:text="1. Come and upgrade to the latest version\n2, this time is more beautiful\n3, come on"/>
        </ScrollView>

        <TextView
            android:id="@+id/btn_update_sure"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/bg_update_btn"
            android:gravity="center"
            android:text="@string/update_now"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/scrollView2"/>

        <TextView
            android:id="@+id/btn_update_cancel"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginTop="10dp"
            android:gravity="center"
            android:text="@string/cancel_update"
            android:textColor="@color/black"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/btn_update_sure"/>

        <TextView
            android:id="@+id/tv_version_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:layout_marginEnd="15dp"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:textStyle="bold"
            tools:text="V1.0.0"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


</LinearLayout>

