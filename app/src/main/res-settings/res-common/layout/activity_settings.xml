<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.settings.common.viewmodel.SettingsViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarView"
            layout="@layout/toolbar" />

        <RelativeLayout xmlns:tools="http://schemas.android.com/tools"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/toolbarView"
            android:layout_above="@+id/versionLayout"
            android:background="#ffffff"
            android:gravity="center"
            android:orientation="vertical"
            tools:context=".ui.settings.common.activity.SettingsActivity">

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/rvSettings"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_setting_card">



            </androidx.recyclerview.widget.RecyclerView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:gravity="center"
                android:id="@+id/settingMenuLayout"
                android:visibility="gone"
                android:background="@color/white">

                <ImageView
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:src="@drawable/exception_handler_icon"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/settings_menu_configuration_is_missing"
                    android:textColor="#212121"
                    android:textSize="16sp"/>

            </LinearLayout>

        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:id="@+id/versionLayout"
            android:gravity="center"
            android:padding="5dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/tvAppName"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/app_name"
                android:textColor="@color/black"
                android:layout_marginEnd="3dp"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/tvAppVersion"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="v3.3.3"
                android:textColor="@color/black"
                android:textSize="10sp" />
        </LinearLayout>

    </RelativeLayout>
</layout>