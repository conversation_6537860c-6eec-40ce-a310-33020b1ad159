<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/formulaire_activation_fidelity"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="20dp">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/msgTxt"
            android:layout_width="match_parent"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="16sp"
            android:textColor="@color/colorPrimary"
            android:text="@string/update_unit_price"
            android:layout_height="wrap_content"/>

        <ImageView
            android:id="@+id/icon"
            android:layout_width="128dp"
            android:visibility="gone"
            android:layout_height="128dp"
            android:layout_marginTop="25dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="16sp"
            app:textAllCaps="true"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="10dp"
            android:textColor="@color/colorPrimary"
            android:text=""
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="end"
            android:orientation="horizontal">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/inputlayout"
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/dp10"
                android:layout_weight="0.2"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_height="wrap_content">

                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/enterValue"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:layout_marginTop="22dp"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="12dp"
                    android:padding="@dimen/_5sdp"
                    android:textAlignment="center"
                    android:textAllCaps="true"
                    android:textColor="@color/black"
                    android:inputType="numberDecimal"
                    android:textColorHint="@color/black"
                    android:hint="@string/unit_price"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:maxLength="10"
                    android:textSize="16sp" />

            </com.google.android.material.textfield.TextInputLayout>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:id="@+id/tvQty"
                android:layout_margin="10dp"
                android:textStyle="bold"
                android:textSize="16sp"
                android:textColor="@color/colorPrimary"
                android:text="QTY"/>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:weightSum="1"
            android:layout_marginBottom="10dp"
            android:layout_height="match_parent">

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="wrap_content"
                android:background="@drawable/round_valid_btn"
                android:clickable="true"
                android:onClick="onClick"
                app:backgroundTint="@color/redLight"
                android:stateListAnimator="@null"
                android:text="@string/cancel"
                android:textAllCaps="true"
                android:textColor="#fff" />
            <Button
                android:id="@+id/submitButton"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_marginStart="@dimen/dp10"
                android:layout_height="wrap_content"
                android:background="@drawable/round_valid_btn"
                android:clickable="true"
                android:onClick="onClick"
                android:stateListAnimator="@null"
                android:text="@string/submit"
                android:textAllCaps="true"
                android:textColor="#fff" />
        </LinearLayout>


    </LinearLayout>

</RelativeLayout>