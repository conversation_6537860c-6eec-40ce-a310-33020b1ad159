<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.settings.common.model.SettingItemModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/transactionListLayout"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="10dp"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:padding="3dp"
            android:gravity="center"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iconBg"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:src="@drawable/circular_bg"
                    android:tint="@{item.color}"
                    tools:tint="@color/black" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iconImage"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginStart="11dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="9dp"
                    android:layout_marginBottom="8dp"
                    android:padding="1dp"
                    app:imageResource="@{item.icon}"
                    app:tint="@color/white"
                    tools:src="@drawable/ic_card" />

            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="7dp"
                android:layout_marginEnd="7dp"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center_vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/titleTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@{item.title}"
                        tools:text="Product Name"
                        android:layout_marginEnd="5dp"
                        android:textColor="@color/black"
                        android:maxLines="3"
                        android:textSize="14sp"
                        android:textStyle="normal" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="3dp"
                        android:backgroundTint="@color/white"
                        android:src="@drawable/ic_forward_arrow" />



                </LinearLayout>


            </LinearLayout>


        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</layout>