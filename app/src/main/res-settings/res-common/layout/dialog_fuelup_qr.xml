<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="320dp"
            android:layout_centerInParent="true"
            android:background="@drawable/formulaire_activation_fidelity"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:id="@+id/ivQrCode"
                android:layout_width="@dimen/_120sdp"
                android:layout_height="@dimen/_120sdp"
                android:src="@drawable/s_scanning">

            </ImageView>


            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/please_scan_qr_code"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/greyDark"
                android:textSize="14sp"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/btn_update_cancel"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_20sdp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/done"
                android:textColor="@color/colorWhite"
                android:textSize="16sp" />
        </LinearLayout>

    </RelativeLayout>
</layout>