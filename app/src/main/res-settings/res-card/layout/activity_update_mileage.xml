<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent"
        tools:context=".MainActivity">
        <include
            android:id="@+id/toolbarMileage"
            layout="@layout/toolbar" />
        <LinearLayout
            android:id="@+id/mileageLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="20dp">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:gravity="center"
                android:textStyle="bold"
                android:textSize="@dimen/_12sdp"
                android:textColor="@color/colorPrimary"
                android:text="@string/kilo_plz"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>

            <ImageView
                android:layout_width="128dp"
                android:layout_height="128dp"
                app:tint="#444B54"
                android:layout_marginTop="25dp"
                android:src="@drawable/ic_speed" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/enteredMileage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_20sdp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/lightGrey"
                android:paddingHorizontal="20dp"
                android:paddingVertical="12dp"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="@color/black"
                android:inputType="number"
                android:hint="@string/enter_mileage"
                android:textColorHint="@color/black"
                android:maxLines="1"
                android:singleLine="true"
                android:maxLength="6"
                android:textSize="16sp" />
            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:layout_gravity="center"
                android:layout_marginTop="20dp"
                android:weightSum="1"
                android:layout_marginBottom="10dp"
                android:layout_height="match_parent">

                <Button
                    android:id="@+id/cancelButton"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_height="wrap_content"
                    android:background="@drawable/round_valid_btn"
                    android:clickable="true"
                    android:onClick="onClick"
                    app:backgroundTint="@color/light_red"
                    android:stateListAnimator="@null"
                    android:text="@string/cancel"
                    android:textAllCaps="true"
                    android:textColor="#fff" />
                <Button
                    android:id="@+id/submitButton"
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:layout_marginStart="@dimen/_10sdp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/round_valid_btn"
                    android:clickable="true"
                    android:onClick="onClick"
                    android:stateListAnimator="@null"
                    android:text="@string/submit"
                    android:textAllCaps="true"
                    android:textColor="#fff" />
            </LinearLayout>


        </LinearLayout>
        <LinearLayout
            android:id="@+id/progressBarLayout"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:visibility="gone"
            android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/please_wait_your_card_update_is_under_process"
                android:gravity="center"
                android:textSize="@dimen/_14sdp"
                android:layout_marginTop="@dimen/_100sdp"
                android:textColor="@color/colorPrimary"
                android:textStyle="bold"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_250sdp"
                android:layout_marginTop="@dimen/_70sdp"
                android:layout_marginHorizontal="@dimen/_30sdp"
                android:layout_gravity="center"
                app:lottie_rawRes="@raw/card_processing3"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"/>
        </LinearLayout>

    </LinearLayout>
</layout>