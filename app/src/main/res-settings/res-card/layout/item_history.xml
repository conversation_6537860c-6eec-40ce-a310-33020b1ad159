<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.settings.card.history.model.HistoryItemModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
<androidx.cardview.widget.CardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:shadowRadius="5"
    android:layout_marginVertical="2dp"
    android:id="@+id/cardview"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:padding="@dimen/_5sdp"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/txtDate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@{item.dateTrx}"
            android:textSize="14sp"
            android:padding="5dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:weightSum="1"
            android:orientation="horizontal"
            android:padding="5dp"
            >
            <TextView
                android:id="@+id/txtLibele"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:textColor="@color/black"
                android:layout_height="wrap_content"
                android:text="@{item.getTypeName()}"
                android:textSize="14sp" />
            <TextView
                android:id="@+id/txtMontant"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_weight="0.5"
                android:textStyle="bold"
                android:textColor="@{item.getTypeColor()}"
                android:layout_gravity="end"
                android:text="@{item.montant}"
                android:textSize="14sp"
                />


        </LinearLayout>
        <TextView
            android:id="@+id/productName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:text="@{item.productName}"
            android:textSize="14sp"
            android:padding="5dp" />


    </LinearLayout>

</androidx.cardview.widget.CardView>
</layout>
