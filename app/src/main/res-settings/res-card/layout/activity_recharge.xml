<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.settings.card.recharge.viewmodel.RechargeViewModel" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <include
            android:id="@+id/toolbarPayment"
            layout="@layout/toolbar" />
     <LinearLayout
         android:id="@+id/successLayout"
         android:layout_width="wrap_content"
         android:orientation="vertical"
         android:layout_height="wrap_content">

             <androidx.appcompat.widget.AppCompatTextView
                 android:id="@+id/message"
                 android:layout_width="match_parent"
                 android:layout_height="wrap_content"
                 android:text="@string/please_wait_your_payment_verification_is_under_process"
                 android:gravity="center"
                 android:textSize="@dimen/_14sdp"
                 android:layout_marginTop="@dimen/_100sdp"
                 android:textColor="@color/colorPrimary"
                 android:textStyle="bold"
                 android:layout_marginHorizontal="@dimen/_20sdp"
                 app:layout_constraintBottom_toBottomOf="parent"
                 app:layout_constraintStart_toStartOf="parent"
                 app:layout_constraintEnd_toEndOf="parent"
                 app:layout_constraintTop_toTopOf="parent" />
             <com.airbnb.lottie.LottieAnimationView
                 android:id="@+id/animationView"
                 android:layout_width="match_parent"
                 android:layout_height="@dimen/_250sdp"
                 android:layout_marginTop="@dimen/_10sdp"
                 android:layout_marginHorizontal="@dimen/_30sdp"
                 android:layout_gravity="center"
                 app:lottie_rawRes="@raw/card_processing3"
                 app:lottie_autoPlay="true"
                 app:lottie_loop="true"/>


         <com.airbnb.lottie.LottieAnimationView
             android:id="@+id/paymentSuccess"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:visibility="gone"
             app:lottie_rawRes="@raw/payment_success"
             app:lottie_autoPlay="true"
             app:lottie_loop="true"/>

     </LinearLayout>

        <ScrollView
            android:id="@+id/previewLayout"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="visible"
                android:padding="0dp"
                android:layout_gravity="top"
                android:layout_margin="10dp"
                android:id="@+id/ivTicketPreview"/>

        </ScrollView>
    </LinearLayout>

</layout>