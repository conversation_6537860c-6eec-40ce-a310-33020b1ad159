<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:background="@color/background_light_white"
        android:layout_height="match_parent"
        >
        <include layout="@layout/toolbar" android:id="@+id/toolbarHistory" />
        <ScrollView
            android:id="@+id/DetailsLayout"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent">
            
        <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_height="wrap_content">
                <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_margin="10dp"
                android:padding="10dp"
                android:background="@drawable/ic_card_bg"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:layout_width="40dp"
                        android:layout_gravity="start"
                        android:src="@drawable/chip"
                        android:onClick="sampleCode"
                        android:layout_height="30dp">

                    </ImageView>
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/cardType"
                        android:layout_width="match_parent"
                        android:text="----"
                        android:layout_gravity="end|center"
                        android:gravity="end|center"
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:layout_height="wrap_content">

                    </androidx.appcompat.widget.AppCompatTextView>

                </LinearLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="match_parent"
                    android:text="@string/fleet_card_number"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:textColor="@color/white"
                    android:layout_height="wrap_content">
                </androidx.appcompat.widget.AppCompatTextView>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/cardNumber"
                    android:layout_width="match_parent"
                    android:text="----------"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_marginTop="5dp"
                    android:textStyle="bold"
                    android:layout_height="wrap_content">
                </androidx.appcompat.widget.AppCompatTextView>
                <View
                    android:layout_width="wrap_content"
                    android:background="#B8ABAB"
                    android:layout_marginTop="5dp"
                    android:layout_height="1dp">
                </View>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="2"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1.4"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:text="@string/card_holder_name"
                            android:layout_marginTop="20dp"
                            android:textColor="@color/white"
                            android:layoutDirection="ltr"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/cardHolderName"
                            android:layout_width="match_parent"
                            android:text="----------"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:layoutDirection="ltr"
                            android:maxLines="1"
                            android:layout_marginTop="5dp"
                            android:textStyle="bold"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <View
                            android:layout_width="wrap_content"
                            android:background="#B8ABAB"
                            android:layout_marginTop="5dp"
                            android:layout_height="1dp">
                        </View>

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="0.6"
                        android:layout_marginStart="10dp"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:text="@string/expiry_date"
                            android:layout_marginTop="20dp"
                            android:layoutDirection="ltr"
                            android:layout_gravity="end"
                            android:gravity="start"
                            android:textColor="@color/white"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/expiryDate"
                            android:layout_width="match_parent"
                            android:text="----------"
                            android:textColor="@color/white"
                            android:layoutDirection="ltr"
                            android:textSize="14sp"
                            android:maxLines="1"
                            android:layout_marginTop="5dp"
                            android:textStyle="bold"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <View
                            android:layout_width="wrap_content"
                            android:background="#B8ABAB"
                            android:layout_marginTop="5dp"
                            android:layout_height="1dp">
                        </View>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:layout_marginHorizontal="@dimen/_10sdp"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:id="@+id/ceilingLayout"
                        android:layout_width="match_parent"
                        android:weightSum="2"
                        android:padding="@dimen/_10sdp"
                        android:background="@color/light_green"
                        android:orientation="horizontal"
                        android:layout_height="match_parent">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_gravity="center"
                            android:textStyle="bold"
                            android:layout_weight="1.9"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:gravity="center"
                            android:text="@string/card_ceiling_limits"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <ImageView
                            android:id="@+id/expandIconCC"
                            android:layout_width="0dp"
                            android:layout_weight="0.1"
                            app:tint="@color/black"
                            android:src="@drawable/ic_arrow_down"
                            android:layout_height="match_parent">
                            
                        </ImageView>
                    </LinearLayout>
                   <LinearLayout
                       android:id="@+id/ceilingLayoutDetails"
                       android:layout_width="match_parent"
                       android:orientation="vertical"
                       android:visibility="gone"
                       android:background="@color/light_green"
                       android:layout_height="match_parent">
                       <LinearLayout
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/monthly_limit"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/monthlyCeilingTxt"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                       <LinearLayout
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:layout_marginTop="2dp"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/weekly_limit"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/weeklyCeilingTxt"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                       <LinearLayout
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:layout_marginTop="2dp"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/daily_limit"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/dailyCeilingTxt"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                       <LinearLayout
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:layout_marginTop="2dp"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/monthly_trx_count"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/mtrx"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                       <LinearLayout
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:layout_marginTop="2dp"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/weekly_trx_count"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/wtrx"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                       <LinearLayout
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:layout_marginTop="2dp"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/daily_trx_count"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/dtrx"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                       <LinearLayout
                           android:id="@+id/layoutCardBalance"
                           android:layout_width="match_parent"
                           android:background="@color/white"
                           android:padding="20dp"
                           android:weightSum="2"
                           android:layout_marginTop="2dp"
                           android:orientation="horizontal"
                           android:layout_height="wrap_content">
                           <androidx.appcompat.widget.AppCompatTextView
                               android:layout_width="0dp"
                               android:text="@string/card_balance_label"
                               android:textStyle="bold"
                               android:layout_weight="1"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                           <androidx.appcompat.widget.AppCompatTextView
                               android:id="@+id/cardBalanceTxt"
                               android:layout_width="0dp"
                               android:layout_weight="1"
                               android:gravity="end"
                               android:layout_gravity="end"
                               android:text="----------"
                               android:textStyle="bold"
                               android:textSize="12sp"
                               android:textColor="@color/black"
                               android:layout_height="wrap_content">
                           </androidx.appcompat.widget.AppCompatTextView>
                       </LinearLayout>
                   </LinearLayout>
                </LinearLayout>
            <LinearLayout
                android:id="@+id/cardHistoryLayout"
                android:layout_width="match_parent"
                android:weightSum="2"
                android:background="@color/light_blue_color"
                android:padding="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:layout_marginTop="@dimen/_10sdp"
                android:orientation="horizontal"
                android:layout_height="match_parent">
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_gravity="center"
                    android:textStyle="bold"
                    android:layout_weight="1.9"
                    android:textSize="14sp"
                    android:textColor="@color/black"
                    android:gravity="center"
                    android:text="@string/card_history"
                    android:layout_height="wrap_content">
                </androidx.appcompat.widget.AppCompatTextView>
                <ImageView
                    android:id="@+id/iconCh"
                    android:layout_width="0dp"
                    android:layout_weight="0.1"
                    app:tint="@color/black"
                    android:src="@drawable/ic_arrow_down"
                    android:layout_height="match_parent">

                </ImageView>
            </LinearLayout>
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mListView"
                tools:listitem="@layout/item_history"
                android:layout_width="match_parent"
                android:visibility="visible"
                android:background="@color/light_blue_color"
                android:layout_marginTop="@dimen/_1sdp"
                android:layout_marginHorizontal="@dimen/_9sdp"
                android:layout_height="match_parent"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/emptyList"
            android:layout_width="match_parent"
            android:text="@string/no_history_found"
            android:gravity="center"
            android:visibility="gone"
            android:textSize="@dimen/_16sdp"
            android:textColor="@color/black"
            android:layout_height="match_parent">
        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
        </ScrollView>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:id="@+id/progressBar"
            android:background="@color/white"
            android:visibility="visible"
            android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/history_loading"
                android:gravity="center"
                android:textSize="@dimen/_14sdp"
                android:visibility="visible"
                android:layout_marginTop="@dimen/_100sdp"
                android:textColor="@color/colorPrimary"
                android:textStyle="bold"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationView"
                android:layout_width="@dimen/_50sdp"
                android:layout_height="@dimen/_50sdp"
                android:layout_marginTop="@dimen/_20sdp"
                app:lottie_rawRes="@raw/loading_animation"
                app:lottie_autoPlay="true"
                android:layout_gravity="center"
                android:visibility="visible"
                app:lottie_loop="true"/>
        </LinearLayout>

    </LinearLayout>
</layout>