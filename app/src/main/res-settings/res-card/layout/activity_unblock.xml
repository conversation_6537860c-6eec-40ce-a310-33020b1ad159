<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.settings.card.unblockcard.viewmodel.UnblockViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent"
        tools:context=".MainActivity">
        <include
            android:id="@+id/toolbarPayment"
            layout="@layout/toolbar" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/msgText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/please_insert_your_card"
            android:gravity="center"
            android:textSize="@dimen/_12sdp"
            android:layout_marginTop="50dp"
            android:textColor="@color/colorPrimary"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <ImageView
            android:id="@+id/imageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_margin="@dimen/_40sdp"
            android:src="@drawable/insert_card_image">

        </ImageView>



    </LinearLayout>
</layout>