<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.settings.card.pendingtrx.model.PendingRefundModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/cardview"
        app:cardCornerRadius="12dp"
        app:cardElevation="6dp"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        android:layout_marginHorizontal="8dp"
        android:layout_marginVertical="4dp"
        app:strokeColor="@color/lightGrey"
        app:strokeWidth="1dp"
        app:cardUseCompatPadding="false">

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:paddingHorizontal="8dp"
            android:paddingVertical="5dp"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:weightSum="10"
                android:visibility="gone"
                android:layout_height="match_parent">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/productName"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_gravity="end"
                    android:layout_weight="8"
                    android:gravity="center|start"
                    android:textStyle="bold"
                    app:textAllCaps="true"
                    android:text="@{item.getProductName()}"
                    android:textColor="@color/black"
                    android:textSize="12sp" />

                <ImageView
                    android:id="@+id/star"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_gravity="end|center"
                    android:layout_weight="1"
                    android:visibility="gone"
                    android:src="@drawable/ic_baseline_star_24" />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_weight="9"
                android:orientation="vertical"
                android:padding="1dp"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:orientation="horizontal"
                    android:padding="1dp"
                    >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="Date &amp; Time"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/dateTime"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_gravity="end"
                        android:text="@{item.transactionModel.dateTransaction}"
                        tools:text="dd-mm-yyyy"
                        android:textSize="12sp"
                        />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:id="@+id/cardLayout"
                    android:orientation="horizontal"
                    android:padding="1dp"
                    >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/card_number"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/cardNumber"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:text="@{item.transactionModel.pan}"
                        android:textColor="@color/black"
                        android:layout_gravity="end"
                        tools:text="21232324e23434343434"
                        android:textSize="12sp"
                        />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:orientation="horizontal"
                    android:padding="1dp">
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/mode_of_payment"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/paymentMode"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:text="@{item.transactionModel.getModePayment()}"
                        android:textColor="@color/black"
                        android:layout_gravity="end"
                        tools:text="Cash"
                        android:textSize="12sp"
                        />


                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:orientation="horizontal"
                    android:padding="1dp"
                    >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/reference_no"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/referenceNo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:layout_marginStart="10dp"
                        android:text="@{item.transactionModel.reference}"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_gravity="end"
                        tools:text="1234445"
                        android:textSize="12sp"
                        />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:orientation="horizontal"
                    android:padding="1dp"
                    >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/locking_amount"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/paidAmount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="@{item.transactionModel.preAuthAmount.toString()}"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_gravity="end"
                        tools:text="500"
                        android:textSize="12sp" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:orientation="horizontal"
                    android:padding="1dp">
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/transaction_amount"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/trxAmount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="@{item.transactionModel.amount.toString()}"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        android:layout_gravity="end"
                        tools:text="500"
                        android:textSize="12sp" />


                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:id="@+id/exportStatusLayout"
                    android:orientation="horizontal"
                    android:padding="1dp">
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/status"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/trxStatus"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="@{item.statusMessage}"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:textColor="@color/orange"
                        android:layout_gravity="end"
                        android:textSize="12sp" />

                </LinearLayout>

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/grey"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="1"
                    android:gravity="center"
                    android:orientation="horizontal"
                    android:padding="1dp">
                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_weight="0.35"
                        android:textColor="@color/black"
                        android:layout_height="wrap_content"
                        android:text="@string/refund_amount"
                        android:textSize="12sp" />
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/refundAmount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="end"
                        android:text="@{item.getRefundAmount()}"
                        android:layout_weight="0.65"
                        android:textStyle="bold"
                        android:textColor="@color/colorPrimary"
                        android:layout_gravity="end"
                        tools:text="500"
                        android:textSize="16sp" />

                </LinearLayout>

            </LinearLayout>
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</layout>