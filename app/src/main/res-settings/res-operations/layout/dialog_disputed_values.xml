<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:background="@drawable/formulaire_activation_fidelity"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="20dp">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/msgTxt"
            android:layout_width="match_parent"
            android:gravity="center"
            android:textStyle="bold"
            android:textSize="@dimen/_12sdp"
            android:textColor="@color/colorPrimary"
            android:text="@string/disputed_trx"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/_15sdp"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:gravity="start"
                android:textStyle="bold"
                android:textSize="11sp"
                android:layout_marginBottom="@dimen/_5sdp"
                android:textColor="@color/black"
                android:text="@string/enter_quantity"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:gravity="start"
                android:textStyle="bold"
                android:textSize="11sp"
                android:layout_marginStart="@dimen/_2sdp"
                android:textColor="@color/red"
                android:text="*"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/enterQty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/lightGrey"
            android:paddingHorizontal="20dp"
            android:paddingVertical="12dp"
            android:textAlignment="textStart"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:hint="0"
            android:inputType="numberDecimal"
            android:textColorHint="@color/grey"
            android:maxLines="1"
            android:singleLine="true"
            android:maxLength="20"
            android:textSize="16sp" />
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:gravity="start"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginBottom="@dimen/_5sdp"
            android:textStyle="bold"
            android:textSize="11sp"
            android:textColor="@color/black"
            android:text="@string/enter_amount"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/enterAmount"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/lightGrey"
            android:paddingHorizontal="20dp"
            android:paddingVertical="12dp"
            android:textAlignment="textStart"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:hint="@string/_1234"
            android:inputType="numberDecimal"
            android:textColorHint="@color/grey"
            android:maxLines="1"
            android:singleLine="true"
            android:maxLength="20"
            android:textSize="16sp" />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/errorMessage"
            android:layout_width="wrap_content"
            tools:text="@string/error"
            android:textSize="11sp"
            android:layout_margin="@dimen/_5sdp"
            android:textColor="@color/red"
            android:layout_height="wrap_content">

        </androidx.appcompat.widget.AppCompatTextView>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginTop="0dp"
            android:weightSum="1"
            android:layout_marginBottom="10dp"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_height="wrap_content"
                android:background="@drawable/round_valid_btn"
                android:clickable="true"
                android:onClick="onClick"
                app:backgroundTint="@color/light_red"
                android:stateListAnimator="@null"
                android:text="@string/cancel"
                android:textAllCaps="true"
                android:textColor="#fff" />
            <com.google.android.material.button.MaterialButton
                android:id="@+id/submitButton"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_height="wrap_content"
                android:background="@drawable/round_valid_btn"
                android:clickable="true"
                android:onClick="onClick"
                android:stateListAnimator="@null"
                android:text="@string/submit"
                android:textAllCaps="true"
                android:textColor="#fff" />

        </LinearLayout>


    </LinearLayout>

</RelativeLayout>