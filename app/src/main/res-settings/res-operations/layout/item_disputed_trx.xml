<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.settings.operations.model.PendingTransactionModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />
        <variable
            name="product"
            type="app.rht.petrolcard.utils.constant.PRODUCT" />
        <import type="android.text.TextUtils" />
        <import type="android.view.View"/>

    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:id="@+id/cardview"
        android:background="@color/white"
        app:cardCornerRadius="12dp"
        android:layout_margin="5dp"
        app:cardElevation="6dp"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        app:cardUseCompatPadding="true"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:padding="5dp"
            android:background="@drawable/new_border_gray_line"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/product_name"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/productName"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    android:text="@{item.productModel.libelle}"
                    tools:text="montant"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="Date &amp; Time"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/dateTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    android:text="@{item.transactionModel.dateTransaction}"
                    tools:text="2022-01-21"
                    android:textSize="14sp"
                    />


            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/fuel_amount"
                    tools:text="@string/fuel_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{item.transactionModel.amount.toString()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    tools:text="500"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{item.transactionModel.isDiscountTransaction() == 1? View.GONE : View.GONE, default=gone}"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/paid_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/paidAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="@{item.transactionModel.amount.toString()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    tools:text="500"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{item.transactionModel.isDiscountTransaction() == 1? View.VISIBLE : View.GONE, default=visible}"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/discount_amount"
                    tools:text="@string/discount_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/discountAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{item.transactionModel.discountAmount.toString()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    tools:text="500"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/pre_auth_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/preAuthAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:text="@{item.transactionModel.preAuthAmount.toString()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    tools:text="-"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{item.isCardTransaction? View.VISIBLE : View.GONE, default=visible}"
                android:id="@+id/cardLayout"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/card_number"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/cardNumber"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:text="@{item.transactionModel.pan}"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    tools:text="21232324e23434343434"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/mode_of_payment"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/paymentMode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:text="@{item.transactionModel.getModePayment()}"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    tools:text="Cash"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{TextUtils.isEmpty(item.transactionModel.sequenceController)? View.GONE : View.VISIBLE}"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/sequence_no"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:text="@{item.transactionModel.sequenceController}"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    tools:text="1234445"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/reference_no"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/referenceNo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:text="@{item.transactionModel.reference}"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:layout_gravity="end"
                    tools:text="1234445"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/transaction_status"
                    android:textSize="14sp" />
                <TextView
                    android:id="@+id/transactionStatus"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="end"
                    android:layout_weight="0.5"
                    android:text="@string/disputed_trx"
                    android:textStyle="bold"
                    tools:textColor="@color/red"
                    android:textColor="@color/red"
                    android:layout_gravity="end"
                    tools:text="1234445"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:layout_width="match_parent"
                android:textColor="@color/white"
                android:id="@+id/markComplete"
                android:layout_height="wrap_content"
                android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
                android:visibility="@{item.transactionModel.isDisputedTrx == 1? View.VISIBLE : View.GONE, default=gone}"
                android:backgroundTint="@color/green"
                android:text="@string/mark_as_complete"
                android:textSize="14sp" />
        </LinearLayout>



    </LinearLayout>
</layout>