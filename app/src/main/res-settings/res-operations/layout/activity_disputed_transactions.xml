<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <LinearLayout
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/activity_about"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent"
        >
        <include
            android:id="@+id/toolbarView"
            layout="@layout/toolbar" />

        <LinearLayout
            android:id="@+id/listViewLayout"
            android:layout_marginBottom="10dp"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >

            <TextView
                android:id="@+id/selectDate"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="right"
                android:layout_marginBottom="@dimen/_10sdp"
                android:background="@drawable/border_material_design"
                android:drawableEnd="@drawable/ic_calendar"
                android:backgroundTint="@color/colorPrimary"
                android:drawablePadding="@dimen/_10sdp"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:text="Select Date"
                android:textColor="@color/white"
                android:drawableTint="@color/white"
                android:textStyle="bold"
                android:visibility="visible"
                android:padding="@dimen/_5sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/top_tabs" />
            <LinearLayout
                android:id="@+id/progressBarLayout"
                android:gravity="center"
                android:visibility="gone"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">
                <ProgressBar
                    android:id="@+id/progressBar"
                    style="@style/SpinKitView.Large"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    />

                <TextView
                    android:id="@+id/progres_msg"
                    android:text="@string/processing"
                    android:layout_width="wrap_content"
                    android:textColor="@color/colorAccent"
                    android:layout_height="wrap_content"
                    />
            </LinearLayout>
            <TextView
                android:id="@+id/emptyTXT"
                android:layout_width="match_parent"
                android:gravity="center|center_vertical"
                android:layout_margin="20dp"
                android:layout_gravity="center"
                android:visibility="gone"
                android:text="@string/transactions_not_available"
                android:layout_height="match_parent" />
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mListView"
                tools:listitem="@layout/item_disputed_trx"
                android:layout_width="match_parent"
                android:layout_margin="@dimen/_5sdp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                android:layout_height="match_parent" />
        </LinearLayout>
    </LinearLayout>
</layout>
