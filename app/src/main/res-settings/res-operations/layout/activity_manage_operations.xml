<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarView"
            layout="@layout/toolbar" />

        <LinearLayout xmlns:tools="http://schemas.android.com/tools"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/toolbarView"
            android:layout_alignParentBottom="true"
            android:background="#ffffff"
            android:gravity="center"
            android:orientation="vertical"
            tools:context=".ui.settings.common.activity.SettingsActivity">

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/rvSettings"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_manage_card">

            </androidx.recyclerview.widget.RecyclerView>

        </LinearLayout>

    </RelativeLayout>
</layout>