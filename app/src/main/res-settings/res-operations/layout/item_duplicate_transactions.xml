<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.settings.operations.model.DuplicateTransactionModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="5dp"
        android:id="@+id/cardview"
        android:background="@color/white"
        app:cardCornerRadius="12dp"
        android:layout_margin="5dp"
        app:cardElevation="6dp"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        app:cardUseCompatPadding="true"
        >
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:padding="5dp"
            android:background="@drawable/new_border_gray_line"
            android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:weightSum="10"
            android:paddingHorizontal="5dp"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/productImage"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_gravity="top"
                android:layout_height="@dimen/dp50"
                android:layout_marginEnd="@dimen/dp10"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:visibility="gone">
            </ImageView>


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/productName"
                android:layout_width="0dp"
                android:layout_height="match_parent"
                android:layout_weight="8"
                android:textStyle="bold"
                android:text="@{item.productModel.libelle}"
                tools:text="Product name"
                android:gravity="start|center"
                android:layoutDirection="ltr"
                android:layout_gravity="end"
                android:textColor="@color/black"
                android:layout_marginEnd="@dimen/dp10"
                app:layout_constraintStart_toEndOf="@+id/productImage"
                app:layout_constraintEnd_toStartOf="@+id/star"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                android:textSize="14sp" />

            <ImageView
                android:id="@+id/star"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:layout_gravity="end|center"
                android:layout_weight="1"

                android:visibility="@{item.showStarIfLoyaltyTrx()}"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                android:src="@drawable/ic_loyalty_card" />


        </androidx.constraintlayout.widget.ConstraintLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_weight="9"
            android:orientation="vertical"
            android:padding="5dp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/date_amp_time"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/dateTime"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:text="@{item.transactionModel.dateTransaction}"
                    tools:text="2022-22-02"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/fuel_amount"
                    tools:text="@string/fuel_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/totalAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{item.transactionModel.amount.toString()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    tools:text="500"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{item.transactionModel.isDiscountTransaction() == 1? View.VISIBLE : View.GONE, default=visible}"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/paid_amount"
                    tools:text="@string/paid_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/paidAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{item.getPaidAmount()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    tools:text="500"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{item.transactionModel.isDiscountTransaction() == 1? View.VISIBLE : View.GONE, default=visible}"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/discount_amount"
                    tools:text="@string/discount_amount"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/discountAmount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:text="@{item.transactionModel.discountAmount.toString()}"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    tools:text="500"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:visibility="@{item.isCardTransaction()? View.VISIBLE : View.GONE, default=visible}"
                android:id="@+id/cardLayout"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/card_number"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/cardNumber"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:text="@{item.transactionModel.pan}"
                    android:textColor="@color/black"
                    android:gravity="end"
                    android:layout_gravity="end"
                    tools:text="21232324e23434343434"
                    android:textSize="14sp"
                    />


            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/mode_of_payment"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/paymentMode"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:textStyle="bold"
                    android:gravity="end"
                    android:layout_gravity="end"
                    android:text="@{item.transactionModel.getModePayment()}"
                    android:textColor="@color/black"
                    tools:text="Cash"
                    android:textSize="14sp"
                    />


            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:padding="5dp"
                >
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_weight="0.5"
                    android:textColor="@color/black"
                    android:layout_height="wrap_content"
                    android:text="@string/reference_no"
                    android:textSize="14sp" />
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/referenceNo"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:gravity="end"
                    android:layoutDirection="ltr"
                    android:layout_gravity="end"
                    android:text="@{item.transactionModel.reference}"
                    android:textStyle="bold"
                    android:textColor="@color/black"
                    tools:text="1234445"
                    android:textSize="14sp"
                    />


            </LinearLayout>

        </LinearLayout>
    </LinearLayout>


</LinearLayout>
</layout>