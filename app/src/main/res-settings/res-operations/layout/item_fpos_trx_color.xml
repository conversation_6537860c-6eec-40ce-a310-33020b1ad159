<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:background="@color/colorPrimaryTr"
    android:layout_width="wrap_content"
    android:layout_height="40dp">

    <TextView
        android:id="@+id/tvId"
        android:layout_width="@dimen/cell_width_small"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:text="@string/id"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvTrxToken"
        android:layout_width="@dimen/cell_width"
        android:text="@string/trx_token"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvPump"
        android:layout_width="@dimen/cell_width"
        android:text="@string/pump"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvSuccessCode"
        android:layout_width="@dimen/cell_width"
        android:text="@string/success_code"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvCurrency"
        android:layout_width="@dimen/cell_width"
        android:text="@string/currency"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvChecksum"
        android:layout_width="@dimen/cell_width"
        android:text="@string/checksum"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvDateTime"
        android:layout_width="@dimen/cell_width"
        android:text="@string/date_time"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvProduct"
        android:layout_width="@dimen/cell_width"
        android:text="@string/product"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvProductCode"
        android:layout_width="@dimen/cell_width"
        android:text="@string/product_code"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvQty"
        android:layout_width="@dimen/cell_width"
        android:text="@string/qty"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvUnitPrice"
        android:layout_width="@dimen/cell_width"
        android:text="@string/unit_price"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvVatPercent"
        android:layout_width="@dimen/cell_width"
        android:text="@string/vat"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvVatAmount"
        android:layout_width="@dimen/cell_width"
        android:text="@string/vat_amount"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"

        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvTotalAmount"
        android:layout_width="@dimen/cell_width"
        android:text="@string/total_amount"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>

    <TextView
        android:id="@+id/tvStatus"
        android:layout_width="@dimen/cell_width"
        android:text="@string/telecollect"
        android:layout_marginStart="@dimen/margin"
        android:gravity="center|start"
        android:textColor="@color/black"
        android:layout_height="match_parent"/>


</LinearLayout>