<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>

        <variable
            name="model"
            type="app.rht.petrolcard.ui.settings.terminal_settings.viewmodel.TerminalSettingsViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorLightGrey2"
        tools:context=".ui.settings.terminal_settings.activity.TerminalSettingsActivity">

        <include
            android:id="@+id/toolbarView"
            layout="@layout/toolbar" />

        <com.dandan.jsonhandleview.library.JsonViewLayout
            android:id="@+id/jsonView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbarView"/>

    </RelativeLayout>

</layout>