<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
       <!-- <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />-->
        <variable
            name="model"
            type="app.rht.petrolcard.ui.settings.maintenance.viewmodel.MaintenanceViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorLightGrey2"
        tools:context=".ui.settings.maintenance.activity.MaintenanceActivity">

        <include
            android:id="@+id/toolbarView"
            layout="@layout/toolbar" />

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/toolbarView"
            android:scrollbars="none">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="@dimen/_10sdp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/clickable_light_rounded_item"
                    android:backgroundTint="@color/white"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <LinearLayout
                        android:id="@+id/btnCheckInternet"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_globe"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_internet_connection"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />


                    </LinearLayout>

                    <include layout="@layout/separator_view" />

                    <LinearLayout
                        android:id="@+id/btnCheckServer"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_network"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_server_connection"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/clickable_light_rounded_item"
                    android:backgroundTint="@color/white"
                    android:id="@+id/fccPanel"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <LinearLayout
                        android:id="@+id/btnCheckFusion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_fcc"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_fusion_connection"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                    <include layout="@layout/separator_view" android:id="@+id/fccSeparator"/>

                    <LinearLayout
                        android:id="@+id/btnCheckFuelPos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_fcc"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_fuelpos_connection"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/clickable_light_rounded_item"
                    android:backgroundTint="@color/white"
                    android:orientation="vertical"
                    android:padding="10dp">

                    <LinearLayout
                        android:id="@+id/btnCheckParameters"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_check_params"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_parameters"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                    <include layout="@layout/separator_view" />

                    <!--  <LinearLayout
                        android:id="@+id/btnCheckTelecollect"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:orientation="horizontal"
                        android:onClick="btnClick"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_check_telecollect"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_telecollect"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                    <include layout="@layout/separator_view"/>-->

                    <LinearLayout
                        android:id="@+id/btnCheckLogsUpload"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_logs"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_logs_upload"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                    <include layout="@layout/separator_view" />

                    <LinearLayout
                        android:id="@+id/btnCheckCardUpdate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_check_card_update"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_card_update"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />

                    </LinearLayout>

                    <include layout="@layout/separator_view" android:id="@+id/discountSeparator"/>

                    <LinearLayout
                        android:id="@+id/btnCheckDiscount"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_discount"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="@string/check_discount"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />


                    </LinearLayout>
                    <include layout="@layout/separator_view" />
                    <LinearLayout
                        android:id="@+id/btnCheckPrinterStatus"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:background="@drawable/clickable_item"
                        android:backgroundTint="@color/white"
                        android:clickable="true"
                        android:focusable="true"
                        android:onClick="btnClick"
                        android:orientation="horizontal"
                        android:padding="10dp">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="24dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/s_printer_icon"
                            app:tint="@color/greyDark" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginEnd="5dp"
                            android:layout_weight="1"
                            android:maxLines="3"
                            android:text="Check Fiscal Device Status"
                            android:textColor="@color/greyDark"
                            android:textSize="14sp"
                            android:textStyle="normal" />

                        <ImageView
                            android:layout_width="wrap_content"
                            android:layout_height="16dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:src="@drawable/ic_forward_arrow"
                            app:tint="@color/greyDark" />


                    </LinearLayout>


                </LinearLayout>

            </LinearLayout>


        </ScrollView>

    </RelativeLayout>

</layout>