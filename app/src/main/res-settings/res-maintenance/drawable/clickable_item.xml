<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
<item android:state_pressed="true" android:drawable="@drawable/clickable_dark_rounded_item"/>
<item android:state_enabled="false" android:drawable="@drawable/clickable_dark_rounded_item"/>
<item android:drawable="@drawable/clickable_light_rounded_item"/>
</selector>

    <!--<?xml version="1.0" encoding="utf-8"?>
    <selector xmlns:android="http://schemas.android.com/apk/res/android">
        <item android:state_focused="true" android:state_pressed="false" android:drawable="@drawable/clickable_light_rounded_item" />
        <item android:state_focused="true" android:state_pressed="true" android:drawable="@drawable/clickable_dark_rounded_item" />
        <item android:state_focused="false" android:state_pressed="true" android:drawable="@drawable/clickable_dark_rounded_item" />
        <item android:drawable="@drawable/clickable_light_rounded_item"/>
    </selector>-->
