<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.settings.common.viewmodel.SettingsViewModel" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <include
            android:id="@+id/toolbarPayment"
            layout="@layout/toolbar" />
        <FrameLayout
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:visibility="visible"
            android:layout_height="match_parent"
            >

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Please Wait.. Backup is under process.."
                android:gravity="center"
                android:textSize="@dimen/_14sdp"
                android:layout_marginTop="@dimen/_100sdp"
                android:textColor="@color/colorPrimary"
                android:textStyle="bold"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_250sdp"
                android:layout_marginTop="@dimen/_70sdp"
                android:layout_marginHorizontal="@dimen/_30sdp"
                android:layout_gravity="center"
                app:lottie_rawRes="@raw/backup_loading"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"/>

        </FrameLayout>
        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:visibility="gone"
            android:id="@+id/paymentSuccess"
            android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Remove Card"
                android:gravity="center"
                android:textSize="@dimen/_14sdp"
                android:layout_marginTop="@dimen/_100sdp"
                android:textColor="@color/red"
                android:textStyle="bold"
                android:layout_marginHorizontal="@dimen/_20sdp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
            <com.airbnb.lottie.LottieAnimationView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="visible"
                app:lottie_rawRes="@raw/payment_success"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"/>
        </androidx.appcompat.widget.LinearLayoutCompat>

    </LinearLayout>

</layout>