<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:dist="http://schemas.android.com/apk/distribution"
    xmlns:tools="http://schemas.android.com/tools"
    package="app.rht.petrolcard">

    <dist:module dist:instant="true" />

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.FLASHLIGHT" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.READ_SMS" />
    <uses-permission android:name="android.permission.NFC" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" /> <!-- mode Kiosk -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- M-PESA -->
    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" /> <!-- LANDI PERMISSIONS -->
    <uses-permission android:name="com.usdk.deviceinfo.perm.NORMAL_FUNC" />
    <uses-permission android:name="com.usdk.emvkernel.perm.NORMAL_FUNC" />
    <uses-permission android:name="com.usdk.rfcard.perm.NORMAL_FUNC" />

    <uses-feature android:name="android.hardware.camera" />
    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="com.pax.permission.ICC" />
    <uses-permission android:name="com.pax.permission.PICC" />
    <uses-permission android:name="com.pax.permission.MAGCARD" />
    <uses-permission android:name="com.pax.permission.PRINTER" />
    <uses-permission android:name="com.pax.permission.PED" />
    <uses-permission android:name="com.pax.permission.app" />
    <uses-permission android:name="com.pax.permission.UPDATE_APP" />
    <uses-permission android:name="com.pax.permission.BOOT_COMPLETED" />
    <uses-feature android:name="android.hardware.usb.host" />
    <uses-feature
        android:name="android.hardware.location.network"
        android:required="false" />
    <uses-feature
        android:name="android.hardware.location.gps"
        android:required="false" />

    <application
        android:name=".MainApp"
        android:fitsSystemWindows="true"
        android:hardwareAccelerated="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:networkSecurityConfig="@xml/network_security_config"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher"
        android:supportsRtl="true"
        android:theme="@style/AppTheme"
        android:usesCleartextTraffic="true"
        android:windowSoftInputMode="adjustResize|stateAlwaysHidden"
        tools:ignore="AllowBackup"
        tools:replace="android:roundIcon,android:name,android:theme"
        tools:targetApi="n">
        <activity
            android:name=".ui.Dw14PrinterActivity"
            android:exported="false">
            <meta-data
                android:name="android.app.lib_name"
                android:value="" />
        </activity>
        <activity
            android:name=".ui.settings.operations.activity.fpos.PendingFuelPosTrxActivity"
            android:exported="false" />
        <activity
            android:name=".ui.settings.terminal_settings.activity.TerminalSettingsActivity"
            android:exported="false" />
        <activity
            android:name=".ui.settings.maintenance.activity.MaintenanceActivity"
            android:exported="false" />
        <activity
            android:name=".ui.settings.operations.activity.BankTrxHistoryActivity"
            android:exported="false" />
        <activity
            android:name=".ui.settings.card.pendingtrx.activity.PendingRefundsActivity"
            android:exported="false" />
        <activity
            android:name=".ui.settings.card.changepin.activity.ChangePinActivity"
            android:exported="false" />
        <activity
            android:name=".ui.loyalty.activity.CardChangeActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.MiddleMarketActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.QrScanActivity"
            android:exported="true" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <activity
            android:name=".ui.loyalty.activity.AuthoriseNfcActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.TicketLoyaltyActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.LoyaltyDashboardActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.LoyaltyActivationActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.NfcScanActivity"
            android:exported="true" />
        <activity
            android:name=".ui.loyalty.activity.LoyaltyBalanceActivity"
            android:exported="true" />
        <activity
            android:name=".ui.iccpayment.activity.CheckCardCeilingsLimitsActivity"
            android:exported="true" />
        <activity
            android:name=".ui.esdsign.activity.EsdSignActivity"
            android:exported="true" />
        <activity
            android:name=".ui.nfc.activity.NfcActivity"
            android:exported="true" />
        <activity
            android:name=".ui.iccpayment.activity.CheckCardRestrictionsActivity"
            android:exported="true" />
        <activity
            android:name=".ui.settings.card.common.activity.ManageCardActivity"
            android:exported="true" />
        <activity
            android:name=".ui.settings.common.activity.SettingsActivity"
            android:exported="true" />
        <activity
            android:name=".ui.settings.operations.activity.ManageOperationsActivity"
            android:exported="true" />
        <activity
            android:name=".ui.menu.activity.MenuActivity"
            android:launchMode="standard" />
        <activity android:name=".ui.reference.activity.ReferenceActivity" />
        <activity android:name=".ui.attendantcode.activity.AttendantCodeActivity" />
        <activity android:name=".ui.transactionlist.activity.FuelposTransactionListActivity" />
        <activity android:name=".ui.transactionlist.activity.FusionTransactionListActivity" />
        <activity
            android:name=".ui.startup.activity.SplashScreenActivity"
            android:exported="true"
            android:screenOrientation="fullSensor">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.MONKEY" />
                <category android:name="android.intent.category.APP_MARKET" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED" />
            </intent-filter>

            <meta-data
                android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
                android:resource="@xml/usb_device_filter" />
        </activity>
        <activity android:name=".ui.modepay.activity.ModePayActivity" />

        <service
            android:name=".service.FusionService"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name=".utils.fuelpos.FuelPosService"
            android:enabled="true"
            android:exported="true" />
        <service
            android:name=".service.scheduleTeleCollect.ScheduledTeleCollectService"
            android:enabled="true"
            android:exported="true" />

        <activity android:name=".ui.attendantcode.activity.AttendantTagActivity" />
        <activity android:name=".ui.cardpincode.activity.VerifyPinActivity" />
        <activity android:name=".ui.badge.activity.BadgeActivity" />
        <activity android:name=".ui.updatemilage.activity.UpdateMilageActivity" />
        <activity android:name=".ui.amountselection.activity.EnterAmountActivity" />
        <activity android:name=".ui.transactionlist.activity.OfflineTransactionListActivity" />
        <activity android:name=".ui.iccpayment.activity.DebitCardLimitsActivity" />
        <activity android:name=".ui.ticket.activity.TicketActivity" />
        <activity android:name=".ui.product.activity.ProductSelectionActivity" />
        <activity android:name=".ui.product.activity.PumpSelectionActivity" />
        <activity android:name=".ui.amountselection.activity.AmountFullTankActivity" />
        <activity android:name=".ui.loyalty.activity.ICCLoyaltyActivity" />
        <activity android:name=".ui.updatecard.activity.UpdateCardActivity" />
        <activity android:name=".ui.menu.activity.ServiceMenuActivity" />
        <activity android:name=".ui.settings.card.recharge.activity.RechargeCardActivity" />
        <activity android:name=".ui.settings.card.history.activity.HistoryActivity" />
        <activity android:name=".ui.settings.card.unblockcard.activity.UnblockActivity" />
        <activity android:name=".ui.settings.card.unlockpin.activity.UnlockPinActivity" />
        <activity android:name=".ui.settings.card.unlockpin.activity.EnterNewPinActivity" />
        <activity android:name=".ui.menu.activity.LoyaltyMenuActivity" />
        <activity android:name=".ui.settings.operations.activity.DuplicateTransactionActivity" />
        <activity android:name=".ui.transactionlist.activity.TPhotoActivity" />
        <activity android:name=".ui.settings.fuelprice.activity.FuelPriceActivity" />
        <activity android:name=".ui.modepay.activity.SplitPaymentActivity" />
        <activity android:name=".ui.modepay.activity.UnattendantModePayActivity" />
        <activity android:name=".ui.modepay.activity.BankPaymentProgressActivity" />
        <activity android:name=".ui.settings.bankcard.activity.ManageBankCardActivity" />
        <activity android:name=".ui.settings.backup.activity.BackupActivity" />
        <activity android:name=".ui.settings.card.common.activity.ManageUninstallActivity" />
        <activity android:name=".ui.settings.language.activity.ManageLanguageActivity" />
        <activity android:name=".ui.updatecard.activity.UpdateCardUtility" />
        <activity android:name=".ui.startup.activity.SampleActivity" />
        <activity
            android:name=".ui.badge.activity.ManagerCodeActivity"
            android:exported="true" />
        <activity android:name=".ui.badge.activity.ManagerTagActivity" />
        <activity android:name=".ui.settings.operations.activity.PendingTransactionsActivity" />
        <activity android:name=".ui.timssign.activity.TIMSSignActivity" />
        <activity android:name=".ui.settings.operations.activity.DisputedTransactionActivity" />
        <activity
            android:name=".ui.qrcodeticket.activity.QrCodeTicketActivity"
            android:exported="true" />

        <service
            android:name=".utils.firebase.MyFirebaseMessagingService"
            android:directBootAware="true"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </service>
        <service
            android:name=".service.NetworkSchedulerService"
            android:exported="true"
            android:permission="android.permission.BIND_JOB_SERVICE" /> <!-- <meta-data -->
        <!-- android:name="com.google.firebase.messaging.default_notification_icon" -->
        <!-- android:resource="@drawable/ic_check" /> -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/colorAccent" />

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <receiver
            android:name=".service.BootReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".service.RestartApplicationReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">

                <!-- <action android:name="RESUME_ACTION" /> -->
                <!-- <action android:name="PAUSE_ACTION" /> -->
                <action android:name="DESTROY_ACTION" />
                <action android:name="STOP_ACTION" />
            </intent-filter>
        </receiver>
        <receiver
            android:name=".service.BatteryBroadcastReceiver"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BATTERY_CHANGED" />
            </intent-filter>
        </receiver>

        <provider
            android:name=".utils.MyPreferenceProvider"
            android:authorities="app.rht.authority"
            android:exported="true" /> <!-- Uncaught exception handler activity -->
        <activity
            android:name=".utils.uncaughtExceptionHandler.UCEDefaultActivity"
            android:exported="true"
            android:process=":error_activity" />
        <activity
            android:name=".ui.settings.card.recharge.activity.RechargeModeofPaymentActivity"
            android:exported="true" />

        <service
            android:name=".utils.job.QuickPeriodicJobRunner"
            android:exported="false"
            android:permission="android.permission.BIND_JOB_SERVICE" />

        <receiver
            android:name=".utils.job.UpgradeReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
            </intent-filter>
        </receiver>
    </application>

</manifest>