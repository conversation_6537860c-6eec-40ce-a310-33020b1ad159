<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="320dp"
        android:layout_height="320dp"
        android:background="@drawable/formulaire_activation_fidelity"
        android:backgroundTint="@color/white"
        android:padding="10dp">



        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp35"
            android:layout_height="@dimen/dp35"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:visibility="gone"
            android:src="@drawable/close_red"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:orientation="vertical">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/icon"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_120sdp"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/_20sdp"
                android:visibility="visible"
                app:lottie_autoPlay="true"
                app:lottie_enableMergePathsForKitKatAndAbove="true"
                app:lottie_loop="false"
                app:lottie_rawRes="@raw/failed"
                app:lottie_repeatMode="restart" />


            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/app_name"
                android:textColor="@android:color/holo_red_light"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Success !!" />

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                tools:text="Message" />

            <TextView
                android:id="@+id/action_done"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="10dp"
                android:layout_marginVertical="10dp"
                android:background="@drawable/formulaire_activation_fidelity"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                android:paddingHorizontal="10dp"
                android:paddingVertical="10dp"
                android:text="@string/okay"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="@color/white"
                app:backgroundTint="@android:color/holo_red_light"
                app:cornerRadius="10dp" />
        </LinearLayout>


    </RelativeLayout>
</layout>