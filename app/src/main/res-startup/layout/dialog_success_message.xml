<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="320dp"
        android:layout_height="320dp"
        android:background="@drawable/formulaire_activation_fidelity"
        android:backgroundTint="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp30"
            android:layout_height="@dimen/dp30"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_gravity="end"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:src="@drawable/close_green"
            android:visibility="gone" />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/icon"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_centerHorizontal="true"
            android:layout_gravity="center"
            android:layout_marginTop="-50dp"
            android:visibility="visible"
            app:lottie_autoPlay="true"
            app:lottie_enableMergePathsForKitKatAndAbove="true"
            app:lottie_loop="false"
            app:lottie_rawRes="@raw/success_new"
            app:lottie_repeatMode="restart" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/app_name"
                android:textColor="@color/green"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Success !!" />

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black"
                android:textSize="14sp"
                android:layout_marginBottom="10dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                tools:text="Message" />


            <TextView
                android:id="@+id/action_done"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:backgroundTint="@color/green"
                android:insetTop="0dp"
                android:insetBottom="0dp"
                app:cornerRadius="10dp"
                android:background="@drawable/formulaire_activation_fidelity"
                android:paddingHorizontal="10dp"
                android:paddingVertical="10dp"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:layout_marginHorizontal="10dp"
                android:layout_marginVertical="10dp"
                android:textColor="@color/white"
                android:text="@string/okay" />


        </LinearLayout>

    </RelativeLayout>
</layout>