<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
<RelativeLayout
    android:id="@+id/FrameLayout_congrats"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="10dp"
    android:background="@drawable/formulaire_activation_fidelity"
    android:backgroundTint="@color/colorWhite">


    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            android:padding="5dp"
            android:layout_marginHorizontal="@dimen/_30sdp"
            android:textAllCaps="true"
            android:textColor="@color/colorPrimary"
            android:textSize="18sp"
            android:gravity="center"
            android:textStyle="bold"
            tools:text="@string/title" />

        <TextView
            android:id="@+id/message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/_20sdp"
            android:textColor="#1d1d26"
            android:textSize="14sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginHorizontal="@dimen/_10sdp"
            tools:text="@string/please_confirm_to_redeem_your_gift" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/_20sdp"
            android:gravity="center"
            android:orientation="horizontal"
            android:weightSum="1">

            <TextView
                android:id="@+id/myTextViewKo"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="10dp"
                android:layout_weight="0.4"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/redLight"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/cancel"
                android:textColor="@color/colorWhite"
                android:textSize="16sp"
                android:visibility="gone" />

            <TextView

                android:id="@+id/actionDone"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="10dp"
                android:layout_marginTop="@dimen/_10sdp"
                android:layout_weight="0.4"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/okay"
                android:textColor="@color/colorWhite"
                android:textSize="16sp" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
</layout>