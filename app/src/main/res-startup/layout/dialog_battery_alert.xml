<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="320dp"
            android:orientation="vertical"
            android:background="@drawable/formulaire_activation_fidelity"
            android:gravity="center"
            android:padding="10dp"
            android:layout_centerInParent="true">

<!--            <com.airbnb.lottie.LottieAnimationView-->
<!--                android:id="@+id/battery_alert"-->
<!--                android:layout_width="@dimen/_128sdp"-->
<!--                android:layout_height="@dimen/_128sdp"-->
<!--                android:layout_gravity="center"-->
<!--                android:visibility="visible"-->
<!--                app:lottie_autoPlay="true"-->
<!--                app:lottie_enableMergePathsForKitKatAndAbove="true"-->
<!--                app:lottie_loop="true"-->
<!--                app:lottie_rawRes="@raw/battery_low_alert"-->
<!--                app:lottie_repeatMode="restart"-->
<!--                app:lottie_speed="1" />-->
            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="@dimen/dp80"
                android:src="@drawable/low_battery"
                android:layout_height="@dimen/dp80">

            </androidx.appcompat.widget.AppCompatImageView>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/title"
                android:text="Title"
                android:textAlignment="center"
                android:textStyle="bold"
                android:textSize="20sp"
                android:layout_marginTop="10dp"
                android:textAllCaps="true"
                android:textColor="@color/colorPrimary"/>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/tvGiftPoints"
                android:text="@string/battery_low_message_1"
                android:textAlignment="center"
                android:textStyle="bold"
                android:textSize="14sp"
                android:layout_marginTop="10dp"
                android:textAllCaps="false"
                android:textColor="@color/red"/>
            <TextView
                android:id="@+id/message2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/battery_low_message_2"
                android:textColor="@color/greyDark"
                android:textSize="12sp"
                android:layout_marginTop="@dimen/_10sdp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                />

        </LinearLayout>

    </RelativeLayout>
</layout>