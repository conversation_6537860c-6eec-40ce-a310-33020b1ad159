<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
   >
    <data>
        <variable
            name="model"

            type="app.rht.petrolcard.ui.startup.viewmodel.StartupViewModel" />

    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <ImageView
            android:layout_width="130dp"
            android:layout_height="130dp"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="150dp"
            android:src="@drawable/fbs_pay_big" />

        <LinearLayout
            android:layout_alignParentBottom="true"
            android:id="@+id/progressBarLayout"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_centerVertical="true"
            android:visibility="visible"
            android:orientation="vertical"
            android:layout_marginBottom="80dp"
            android:layout_centerHorizontal="true"
            android:gravity="center">


            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationView"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_margin="10dp"
                app:lottie_rawRes="@raw/loading_animation"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"/>

            <TextView
                android:id="@+id/message"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:layout_gravity="center"
                android:textSize="14sp"
                android:padding="5dp"
                android:visibility="visible"
                android:textColor="@color/colorAccent"
                android:text="@string/please_wait_saving_transaction_details" />


        </LinearLayout>


    </RelativeLayout>
</layout>