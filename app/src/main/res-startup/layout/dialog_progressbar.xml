<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="match_parent"
        android:saveEnabled="false"
        android:focusableInTouchMode="false"
        android:layout_height="wrap_content">
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:contentPaddingBottom="18dp"
            app:cardBackgroundColor="@color/white"
            app:contentPaddingTop="15dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/arrow_animation"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_180sdp"
                    android:layout_gravity="center"
                    android:visibility="visible"
                    app:lottie_autoPlay="true"
                    app:lottie_enableMergePathsForKitKatAndAbove="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/telecollect_animation"
                    app:lottie_repeatMode="restart"
                    app:lottie_speed="1" />
                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/arrow_success"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_180sdp"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    app:lottie_autoPlay="true"
                    app:lottie_enableMergePathsForKitKatAndAbove="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/success"
                    app:lottie_repeatMode="restart"
                    app:lottie_speed="1" />

                <TextView
                    android:id="@+id/title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_20sdp"
                    android:gravity="center"
                    android:text="@string/processing"
                    android:textColor="@color/colorPrimary"
                    android:textSize="@dimen/_20sdp"
                    android:textStyle="bold"
                    tools:text="Processing ..." />

                <TextView
                    android:id="@+id/message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/_30sdp"
                    android:gravity="center"
                    android:padding="24dp"
                    android:text=""
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/title"
                    tools:text="Message" />

                <TextView
                    android:id="@+id/action_done"
                    android:layout_width="150dp"
                    android:layout_height="@dimen/_40sdp"
                    android:layout_gravity="center"
                    android:background="@drawable/bg_button_gradient"
                    android:gravity="center"
                    android:text="@string/submit"
                    android:textAllCaps="false"
                    android:textColor="@color/white"
                    android:textSize="@dimen/_16sdp"
                    android:visibility="invisible"
               />
            </LinearLayout>

        </androidx.cardview.widget.CardView>
    </RelativeLayout>
</layout>