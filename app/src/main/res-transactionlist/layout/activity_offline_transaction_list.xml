<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

<RelativeLayout
    android:id="@+id/activity_about"
    android:layout_width="match_parent"
    android:background="@color/background_light_white_1"
    android:layout_height="match_parent"
 >
    <include
        android:id="@+id/toolbarofflineList"
        layout="@layout/toolbar" />

    <LinearLayout
        android:id="@+id/promptLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/toolbarofflineList"
        android:layout_alignParentStart="true"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_marginBottom="10dp"
        android:gravity="center">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Select Your Product"
            android:textColor="@color/colorPrimary"
            android:textStyle="bold" />
    </LinearLayout>

    <include
        android:id="@+id/news_title"
        layout="@layout/layout_offline_list_header"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginHorizontal="@dimen/_5sdp"
        android:layout_below="@+id/promptLayout" />

    <LinearLayout
        android:id="@+id/listViewLayout"
        android:layout_below="@+id/news_title"
        android:layout_marginBottom="0dp"
        android:gravity="center"
        android:layout_marginTop="0dp"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mListView"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:layout_width="match_parent"
            android:layout_margin="@dimen/_5sdp"

            tools:listitem="@layout/item_offline_transaction_list"
            android:layout_height="match_parent" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/progressBarLayout"
        android:gravity="center"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <ProgressBar
            android:id="@+id/progressBar"
            style="@style/SpinKitView.Large"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            />

        <TextView
            android:id="@+id/progres_msg"
            android:text="@string/processing"
            android:layout_width="wrap_content"
            android:textColor="@color/colorAccent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/progressBar"
            android:layout_centerInParent="true"
            />
    </LinearLayout>


</RelativeLayout>
</layout>
