<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:paddingStart="20dp"
    android:paddingEnd="20dp"
    android:paddingVertical="@dimen/dp20"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:gravity="center"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:text="@string/enter_quantity"
        android:layout_height="wrap_content"
        > </androidx.appcompat.widget.AppCompatTextView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="20dp"
        android:orientation="horizontal">
        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/inputBox"
            android:layout_width="match_parent"
            android:maxLength="3"
            android:maxLines="1"
            android:layout_weight="7"
            android:inputType="number"
            android:paddingHorizontal="10dp"
            android:background="@drawable/new_border_gray_line"
            android:layout_height="50dp"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvQty"
            android:layout_width="60dp"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_weight="3"
            android:textColor="@color/colorPrimary"
            android:textSize="18sp"
            android:text="LTR"
            android:layout_height="50dp"
            > </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:gravity="center"
        android:layout_marginTop="@dimen/dp10"
        android:layout_height="60dp">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/cancelButton"
            android:layout_width="wrap_content"
            android:text="@string/cancel"
            android:layout_gravity="center"
            android:textColor="@color/white"
            android:background="@color/red_btn_bg_color"
            android:layout_height="35dp">

        </androidx.appcompat.widget.AppCompatButton>
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/submitButton"
            android:layout_width="wrap_content"
            android:text="@string/submit"
            android:layout_marginStart="@dimen/dp10"
            android:layout_gravity="center"
            android:textColor="@color/white"
            android:background="@color/colorPrimary"
            android:layout_height="35dp">

        </androidx.appcompat.widget.AppCompatButton>
    </LinearLayout>

</LinearLayout>