<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />
        <import type="android.text.TextUtils" />
        <import type="android.view.View"/>
    </data>
    <com.google.android.material.card.MaterialCardView

        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_marginTop="3dp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="@dimen/_3sdp"
        android:gravity="center">

        <LinearLayout
            android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
            android:id="@+id/transactionListLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="5dp"
            android:gravity="center"
            android:orientation="horizontal">


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/hose"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:src="@drawable/rounded_rectangle_button"
                    android:tint="@{item.color}"
                    tools:tint="@color/black" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iconImage"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginTop="15dp"
                    android:layout_marginEnd="9dp"
                    android:layout_marginStart="15dp"
                    android:layout_marginBottom="8dp"
                    android:padding="1dp"
                    app:tint="@color/white"
                    android:src="@drawable/ic_waterdrops"
                    tools:src="@drawable/ic_waterdrops" />
            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="7dp"
                android:orientation="vertical">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:baselineAligned="false">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/titleTextView"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="@{item.produit}"
                            tools:text="Product Name"
                            android:layout_marginEnd="5dp"
                            android:textColor="@color/black"
                            android:maxLines="3"
                            android:textSize="12sp"
                            android:textStyle="normal" />


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_weight="2"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:id="@+id/tvCurrency"
                            android:textAlignment="textEnd"
                            android:text="@{item.currency}"
                            tools:text="AED"
                            android:textSize="12sp"
                            android:textColor="@color/black"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/montantTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{item.getAmountValue}"
                            android:layout_marginStart="@dimen/_2sdp"
                            tools:text="0000"
                            android:gravity="end"
                            android:textSize="12sp"
                            android:textColor="@color/black"
                            android:maxLines="1"/>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:gravity="center_vertical"
                    android:baselineAligned="false">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center_vertical"
                        android:layout_marginEnd="5dp"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:src="@drawable/ic_fuel_pump"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@string/pump_"
                            android:textAllCaps="true"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvPump"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{Integer.toString(item.pump)}"
                            tools:text="0"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center|start"
                        android:layout_marginEnd="5dp"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:src="@drawable/ic_drop"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@string/qty_"
                            app:textAllCaps="true"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/volumeTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{item.getQuantityValue}"
                            tools:text="0"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@string/ltr_"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="center|end"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:src="@drawable/ic_fuel_nozzle"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@string/nozzle_"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvNozzle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{Integer.toString(item.hose)}"
                            tools:text="0"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:orientation="horizontal"
                    android:baselineAligned="false">


                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start|center"
                        android:visibility="visible"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:src="@drawable/calendar"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvDate"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{item.date}"
                            tools:text="2021-08-26"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="2"/>

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start|end"
                        android:visibility="visible"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:layout_gravity="center"
                            android:src="@drawable/ic_time"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvTime"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{item.time}"
                            tools:text="13:27:56"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="end|center"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:layout_gravity="center"
                            android:src="@drawable/bill"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@string/seq"
                            android:textAllCaps="true"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/tvSeqNo"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{item.ref_transaction}"
                            tools:text="1234"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                    </LinearLayout>

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_marginTop="@dimen/_2sdp"
                    android:orientation="horizontal"
                    android:baselineAligned="false">

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:visibility="@{TextUtils.isEmpty(item.fusionSaleId)? View.GONE : View.VISIBLE}"
                        android:layout_weight="1">

                        <ImageView
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="3dp"
                            android:layout_gravity="center"
                            android:src="@drawable/bill"
                            app:tint="@color/newColorLightHint"
                            android:contentDescription="@string/emptyText" />


                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@string/sale_id"
                            android:textAllCaps="true"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="11sp"
                            android:text="@{item.fusionSaleId}"
                            tools:text="1234"
                            android:textColor="@color/newColorLightHint"
                            android:maxLines="1"/>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>


        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</layout>