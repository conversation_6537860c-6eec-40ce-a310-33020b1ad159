<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="60dp"
    android:weightSum="1"
    android:background="@color/white"
    android:gravity="center">

    <TextView
        android:id="@+id/produitTextView"
        android:layout_width="0dp"
        android:layout_weight="0.65"
        android:layout_height="wrap_content"
        style="@style/Base.TextAppearance.AppCompat.Body1"
        android:textSize="16sp"
        android:textAllCaps="true"
        android:text="@string/product"
        android:textColor="@color/colorPrimary"
        android:maxLines="2"
        android:textStyle="normal|bold"
        android:gravity="center"/>
    <View
        android:layout_width="2dp"
        android:visibility="gone"
        android:background="@color/colorPrimary"
        android:layout_height="30dp"/>
    <TextView
        android:id="@+id/volumeTextView"
        android:layout_width="70dp"
        android:layout_margin="3dp"
        android:visibility="gone"
        android:layout_height="wrap_content"
        style="@style/Base.TextAppearance.AppCompat.Body1"
        android:textSize="14sp"
        android:text="@string/quantity"
        android:textStyle="normal|bold"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimary"
        android:maxLines="2"
        android:gravity="center"/>
    <View
        android:layout_width="2dp"
        android:visibility="gone"
        android:background="@color/colorPrimary"
        android:layout_height="30dp"/>

    <TextView
        android:id="@+id/montantTextView"
        android:layout_width="0dp"
        android:layout_weight="0.35"
        android:layout_height="wrap_content"
        style="@style/Base.TextAppearance.AppCompat.Body1"
        android:textSize="16sp"
        android:text="@string/ppu"
        android:textAllCaps="true"
        android:textColor="@color/colorPrimary"
        android:textStyle="bold"
        android:maxLines="2"
        android:gravity="center"/>





</LinearLayout>