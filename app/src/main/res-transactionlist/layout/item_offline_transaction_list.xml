<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.transactionlist.model.OfflineProductsModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        app:cardCornerRadius="@dimen/_5sdp"
        android:shadowRadius="5"
        android:layout_marginBottom="@dimen/_5sdp"
        android:layout_height="wrap_content">

<RelativeLayout
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="100dp"
    android:background="@color/white"
    android:id="@+id/offlineListLayout"
    android:padding="@dimen/_5sdp"
    android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
    android:layout_marginBottom="3dp"
    android:gravity="center">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/hose"
        android:layout_alignParentStart="true"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:layout_margin="5dp"
        android:layout_centerInParent="true"
        android:tint="@{item.getColorValue}"
        android:layout_gravity="center"
        setFuelProduct="@{item.icon}"/>
    <View
        android:layout_margin="5dp"
        android:id="@+id/view"
        android:layout_width="2dp"
        android:layout_height="20dp"
        android:layout_toEndOf="@+id/hose"
        android:layout_centerInParent="true"
        android:visibility="gone"
        android:background="#3C3B3B" />

    <TextView
        android:id="@+id/produitTextView"
        style="@style/Base.TextAppearance.AppCompat.Body1"
        android:layout_width="140dp"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        android:layout_toEndOf="@+id/view"
        android:gravity="center"
        android:maxLines="2"
        android:text="@{item.productsModel.libelle}"
        android:textColor="#303131"
        android:textSize="16sp"
        android:textStyle="normal|bold"
        tools:text="----" />

    <View
        android:layout_margin="5dp"
        android:id="@+id/viewww"
        android:layout_width="2dp"
        android:layout_height="20dp"
        android:layout_toEndOf="@+id/produitTextView"
        android:layout_centerInParent="true"
        android:visibility="invisible"
        android:background="#3C3B3B" />

    <TextView
        android:id="@+id/prixUnitaireTV"
        android:layout_width="130dp"
        android:layout_height="wrap_content"
        style="@style/Base.TextAppearance.AppCompat.Body1"
        android:textSize="16sp"
        android:layout_alignParentEnd="true"
        tools:text="----"
        android:text="@{item.priceFullValue}"
        android:textColor="#303131"
        android:layout_toEndOf="@+id/viewww"
        android:gravity="center"
        android:layout_centerInParent="true"
        android:maxLines="2"
        android:textStyle="normal|bold" />


</RelativeLayout>
    </androidx.cardview.widget.CardView>

</layout>