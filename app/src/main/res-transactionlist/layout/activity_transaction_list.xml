<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
<RelativeLayout
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <include
        android:id="@+id/toolbarTransactionList"
        layout="@layout/toolbar" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:id="@+id/progressBarLayout"
        android:layout_below="@+id/toolbarTransactionList"
        android:background="@color/white"
        android:orientation="vertical"
        android:padding="3dp"
        android:visibility="gone"
        android:gravity="center">

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="30dp"
            android:layout_gravity="center"
            android:layout_height="30dp"
            android:layout_marginTop="5dp"
            android:indeterminate="true"
            android:indeterminateTintMode="src_atop"
            android:indeterminateTint="@color/colorPrimary" />
        <TextView
            android:id="@+id/progressMessage"
            android:text="@string/processing"
            android:layout_width="wrap_content"
            android:textSize="14sp"
            android:textAllCaps="true"
            android:layout_margin="5dp"
            android:textColor="@color/colorPrimary"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <LinearLayout
        android:layout_below="@id/progressBarLayout"
        android:id="@+id/listViewLayout"
        android:layout_marginBottom="0dp"
        android:gravity="center"
        android:layout_marginTop="0dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <TextView
            android:id="@+id/clickHere"
            android:text="@string/click_here_to_sign_all_pending_transactions"
            android:layout_width="wrap_content"
            android:textSize="11sp"
            android:textAllCaps="false"
            android:visibility="gone"
            android:layout_margin="5dp"
            android:textColor="@color/colorPrimary"
            android:layout_height="wrap_content" />
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mListView"
            tools:listitem="@layout/item_transaction_list"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>
    </LinearLayout>


    <LinearLayout
        android:id="@+id/no_transaction_layout"
        android:gravity="center"
        android:visibility="gone"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ImageView
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:visibility="visible"
            android:layout_margin="10dp"
            android:src="@drawable/ic_no_trx_found_2" />
        <TextView
            android:id="@+id/tvFccConnectionMessage"
            android:text="@string/no_transaction_found"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />
        <TextView
            android:text="@string/no_transactions_received_from_fcc_nclick_on_try_again_to_get_transaction_from_fcc"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />
        <TextView
            android:layout_width="210dp"
            android:layout_height="wrap_content"
            android:text="@string/try_again"
            android:textAllCaps="true"
            android:textStyle="bold"
            android:layout_marginTop="40dp"
            android:paddingVertical="10dp"
            android:paddingHorizontal="30dp"
            android:onClick="noFccConnLayoutButtonClick"
            android:textAlignment="center"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/colorPrimary"
            android:id="@+id/btnRefreshTrx"
            android:textColor="@color/white" />
        <TextView
            android:layout_width="210dp"
            android:layout_height="wrap_content"
            android:text="@string/go_to_offline_mode"
            android:textAllCaps="true"
            android:textStyle="bold"
            android:visibility="gone"
            android:textAlignment="center"
            android:layout_marginTop="15dp"
            android:paddingVertical="10dp"
            android:paddingHorizontal="30dp"
            android:onClick="noFccConnLayoutButtonClick"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/redLight"
            android:id="@+id/btnOfflinePage"
            android:textColor="@color/white" />
        <TextView
            android:layout_width="210dp"
            android:layout_height="wrap_content"
            android:text="@string/go_to_home"
            android:textAllCaps="true"
            android:textStyle="bold"
            android:textAlignment="center"
            android:layout_marginTop="15dp"
            android:paddingVertical="10dp"
            android:paddingHorizontal="30dp"
            android:onClick="noFccConnLayoutButtonClick"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/redLight"
            android:id="@+id/btnHome"
            android:textColor="@color/white" />

        <ProgressBar
            android:id="@+id/fccProgressBar"
            android:layout_width="wrap_content"
            android:layout_margin="20dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="invisible"
            android:indeterminateTint="@color/colorPrimary"/>

    </LinearLayout>


</RelativeLayout>
</layout>