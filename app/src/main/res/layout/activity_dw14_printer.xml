<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.Dw14PrinterActivity"
    android:orientation="vertical"
    android:gravity="center">

    <Button
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="Init Printer"
        android:background="@drawable/clickable_dark_rounded_item"
        android:layout_margin="5dp"
        android:id="@+id/btnInit"/>

    <Button
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="Print"
        android:background="@drawable/clickable_dark_rounded_item"
        android:layout_margin="5dp"
        android:id="@+id/btnPrint"/>

    <Button
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="Close Printer"
        android:background="@drawable/clickable_dark_rounded_item"
        android:layout_margin="5dp"
        android:id="@+id/btnClose"/>

</LinearLayout>