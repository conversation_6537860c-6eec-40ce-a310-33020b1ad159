<?xml version="1.0" encoding="utf-8"?>
<LinearLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="260dp"
    android:gravity="center"
    android:padding="20dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/rounded_rectangle"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <TextView
        android:layout_width="wrap_content"
        android:text="TITLE"
        android:id="@+id/tvTitle"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/colorPrimary"
        android:layout_height="wrap_content">

    </TextView>

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="60dp"
        android:layout_height="60dp"
        android:indeterminate="true"
        android:indeterminateTintMode="src_atop"
        android:indeterminateTint="@color/colorPrimary"/>

    <TextView
        android:id="@+id/tvSubTitle"
        android:layout_width="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="SUBTITLE"
        android:layout_height="wrap_content">

    </TextView>
</LinearLayout>