<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"

    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data> </data>


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/white"
        android:elevation="0dp"
        android:gravity="center"
        android:theme="@style/ThemeOverlay.AppCompat.ActionBar"
        app:navigationIcon="@drawable/ic_arrow_left"
        app:navigationIconTint="@color/black"
        app:popupTheme="@style/ThemeOverlay.AppCompat.Dark"
        app:contentInsetLeft="0dp"
        app:contentInsetStart="0dp"
        app:contentInsetStartWithNavigation="0dp">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:fitsSystemWindows="true">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_gravity="center"
                android:layout_marginEnd="5dp"
                android:layout_toStartOf="@+id/rightSideLayout"
                android:textAlignment="center"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:visibility="visible"
                app:textAllCaps="true"
                tools:text="Title" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:id="@+id/rightSideLayout"
                android:layout_alignParentEnd="true"
                android:layout_marginEnd="14dp"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/toolbar_right_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="5dp"
                    android:text="@string/label_titre_refresh"
                    android:textColor="@color/black"
                    android:visibility="gone"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/toolbar_right_image"
                    android:layout_width="35dp"
                    android:layout_height="35dp"
                    android:padding="5dp"
                    android:src="@drawable/ic_baseline_refresh_24"
                    android:visibility="invisible"
                    app:tint="@color/black" />

            </LinearLayout>

        </RelativeLayout>



    </com.google.android.material.appbar.MaterialToolbar>


</layout>