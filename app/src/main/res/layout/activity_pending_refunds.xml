<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>


    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarView"
            layout="@layout/toolbar" />

        <LinearLayout
            xmlns:tools="http://schemas.android.com/tools"
            android:id="@+id/activity_about"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_below="@+id/toolbarView"
            android:layout_above="@+id/bottomLayout"
            android:layout_height="match_parent">

            <LinearLayout
                android:id="@+id/search_layout"
                android:layout_marginBottom="0dp"
                android:gravity="center"
                android:visibility="gone"
                android:weightSum="10"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:layout_marginTop="0dp"
                android:background="@drawable/new_border_gray_line"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >

                <EditText
                    android:id="@+id/search_txt"
                    android:layout_width="0dp"
                    android:layout_weight="9"
                    android:layout_height="wrap_content"
                    android:ems="10"
                    android:layout_margin="10dp"
                    android:hint="@string/enter_card_number"
                    android:background="@null"
                    android:text=""
                    android:textSize="14sp"
                    android:textColor="@color/darkGrey"
                    android:backgroundTint="@color/colorPrimary"
                    android:inputType="number"
                    />

                <ImageView
                    android:id="@+id/search_btn"
                    android:layout_width="@dimen/_25sdp"
                    android:layout_weight="1"
                    android:layout_height="@dimen/_25sdp"
                    android:layout_margin="10dp"
                    android:src="@drawable/ic_baseline_search_24"
                    app:tint="@color/colorPrimary"
                    />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/listViewLayout"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:id="@+id/progressBarLayout"
                    android:gravity="center"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">
                    <ProgressBar
                        android:id="@+id/progressBar"
                        style="@style/SpinKitView.Large"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                    <TextView
                        android:id="@+id/progres_msg"
                        android:text="@string/processing"
                        android:layout_width="wrap_content"
                        android:textColor="@color/colorAccent"
                        android:layout_height="wrap_content" />
                </LinearLayout>

                <TextView
                    android:id="@+id/emptyTXT"
                    android:layout_width="match_parent"
                    android:gravity="center|center_vertical"
                    android:layout_margin="20dp"
                    android:layout_gravity="center"
                    android:visibility="gone"
                    android:text="@string/transactions_not_available"
                    android:layout_height="wrap_content" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/mListView"
                    tools:listitem="@layout/item_refund_status"
                    android:layout_width="match_parent"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    android:layout_height="match_parent" />

            </LinearLayout>

        </LinearLayout>

       <LinearLayout
           android:layout_width="match_parent"
           android:layout_height="wrap_content"
           android:layout_alignParentBottom="true"
           android:background="@color/white"
           android:id="@+id/bottomLayout"
           android:orientation="vertical">

           <Button
               android:visibility="gone"
               android:id="@+id/exportBtn"
               android:layout_width="match_parent"
               android:text="@string/export_refund_file"
               android:layout_marginHorizontal="6dp"
               android:backgroundTint="#4CAF50"
               android:onClick="exportRefundFile"
               android:layout_height="wrap_content"/>

       </LinearLayout>

    </RelativeLayout>

</layout>
