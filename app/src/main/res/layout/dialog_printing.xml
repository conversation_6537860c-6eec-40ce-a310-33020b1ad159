<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center"
    android:orientation="vertical"
    android:background="@drawable/formulaire_activation_fidelity"
    android:padding="10dp">

    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/imageView1"
        android:layout_width="220dp"
        android:layout_height="220dp"
        android:layout_gravity="center"
        android:visibility="visible"
        app:lottie_autoPlay="true"
        app:lottie_enableMergePathsForKitKatAndAbove="true"
        app:lottie_loop="true"
        app:lottie_rawRes="@raw/printing"
        app:lottie_repeatMode="restart"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/please_wait_receipt_is_printing"
        android:textAlignment="center"
        android:textAllCaps="true"
        android:textColor="@color/black"
        android:textSize="15sp" />

</LinearLayout>