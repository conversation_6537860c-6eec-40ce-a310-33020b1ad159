<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
                                                       
                                                   

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@color/white"
        app:contentPaddingBottom="18dp"
        app:contentPaddingTop="15dp" android:tag="layout/dialog_confirmation_0" >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:lineHeight="23dp"
                android:text="@string/confirm"
                android:textColor="@color/main_color"
                android:textSize="18sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Confirm" />

            <TextView
                android:id="@+id/message"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:padding="24dp"
                android:text="Message"
                android:textColor="@color/text_color_gray"
                android:textSize="15sp"
                app:layout_constraintBottom_toTopOf="@+id/action_cancel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                tools:text="Message" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/action_cancel"
                android:layout_width="0dp"
                android:layout_height="@dimen/_40sdp"
                android:layout_marginStart="@dimen/_15sdp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="@dimen/_8sdp"
                android:background="@drawable/bg_button_gradient"
                android:text="@string/cancel"
                android:gravity="center"
                android:textSize="@dimen/_16sdp"
                android:textAllCaps="false"
                android:textColor="@color/white"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/action_done"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/message" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/action_done"
                android:layout_width="0dp"
                android:layout_height="@dimen/_40sdp"
                android:layout_marginStart="@dimen/_8sdp"
                android:layout_marginEnd="@dimen/_15sdp"
                android:text="@string/ok"
                android:background="@drawable/bg_button_gradient"
                android:textColor="@color/white"
                android:textAllCaps="false"
                android:gravity="center"
                android:textSize="@dimen/_16sdp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/action_cancel"
                app:layout_constraintTop_toTopOf="@+id/action_cancel" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>