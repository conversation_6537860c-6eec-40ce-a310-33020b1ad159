<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
   >
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="350dp"
            android:layout_centerInParent="true"
            android:background="@drawable/formulaire_activation_fidelity"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:layout_width="@dimen/_80sdp"
                android:layout_height="@dimen/_80sdp"
                android:src="@drawable/ic_no_fcc_connected">

            </ImageView>

            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/opps_fcc_disconnected"
                android:textAlignment="center"
                android:textAllCaps="true"
                android:textColor="@color/colorPrimary"
                android:textSize="16sp"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/terminal_not_connected_with_fusion"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/greyDark"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/not_connected_to_server"
                android:textColor="@color/greyDark"
                android:visibility="gone"
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title" />

            <TextView
                android:id="@+id/btnRetryFcc"
                android:layout_width="@dimen/_80sdp"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center"
                android:padding="10dp"
                android:layout_gravity="center"
                android:text="@string/okay"
                android:textColor="@color/colorWhite"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:textSize="16sp"
                />

            <ProgressBar
                android:id="@+id/fccProgressBar"
                android:layout_width="wrap_content"
                android:layout_margin="20dp"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                android:visibility="gone"
                android:indeterminateTint="@color/colorPrimary"/>

        </LinearLayout>

    </RelativeLayout>
</layout>