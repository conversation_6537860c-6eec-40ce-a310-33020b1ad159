<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarTicket"
            layout="@layout/toolbar" />

        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="match_parent"
            android:gravity="center"
            android:padding="15dp"
            tools:context=".ui.qrcodeticket.activity.QrCodeTicketActivity">


            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/imageLayout">

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/animationView"
                    android:layout_width="80dp"
                    android:layout_height="80dp"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="10dp"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"
                    app:lottie_rawRes="@raw/loading_animation" />

                <ImageView
                    android:visibility="gone"
                    android:id="@+id/ivQrCode"
                    android:background="@color/white"
                    android:padding="10dp"
                    android:layout_width="@dimen/_170sdp"
                    android:layout_height="@dimen/_170sdp"/>

            </RelativeLayout>

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/processing_your_transaction"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/greyDark"
                android:textSize="14sp" />

            <TextView
                android:id="@+id/tvTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="00"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/colorPrimary"
                android:textSize="32sp"
                android:textStyle="bold" />


            <TextView
                android:id="@+id/btnClose"
                android:layout_width="@dimen/_120sdp"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_20sdp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:gravity="center"
                android:padding="10dp"
                android:text="@string/close"
                android:textColor="@color/colorWhite"
                android:textSize="16sp" />


        </LinearLayout>

    </RelativeLayout>



</layout>