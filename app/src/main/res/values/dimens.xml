<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Live Activity -->
    <!-- Default screen margins, per the Android Design guidelines. -->

    <dimen name="dp10">10dp</dimen>
    <dimen name="dp20">20dp</dimen>
    <dimen name="dp30">30dp</dimen>
    <dimen name="dp35">35dp</dimen>
    <dimen name="dp50">50dp</dimen>
    <dimen name="dp80">80dp</dimen>
    <dimen name="dp100">100dp</dimen>

    <!--SIR-->

    <!-- Segment Buttons -->

    <dimen name="dashboard_card_width">85dp</dimen>
    <dimen name="dashboard_card_height">100dp</dimen>
    <dimen name="material_dialog_corner">16dp</dimen>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="my_corner_radius">16dp</dimen>
    <dimen name="loyalty_card_label_size">14sp</dimen>
    <dimen name="loyalty_dashboard_card_height">110dp</dimen>
    <dimen name="menu_card_subtitle">10sp</dimen>
    <dimen name="worth_label_size">10sp</dimen>

    <dimen name="header_height">40dp</dimen>
    <dimen name="cell_width">120dp</dimen>
    <dimen name="cell_width_small">60dp</dimen>
    <dimen name="cell_width_medium">80dp</dimen>
    <dimen name="margin">5dp</dimen>
</resources>