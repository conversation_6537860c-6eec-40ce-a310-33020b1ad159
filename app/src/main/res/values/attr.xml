<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <attr name="item_post_like_small" format="reference" />
    <attr name="sidebar_banner" format="reference" />

    <declare-styleable name="DatePickerTimeline" tools:ignore="ResourceName">
        <attr name="monthTextColor" format="color" />
        <attr name="dateTextColor" format="color" />
        <attr name="dayTextColor" format="color" />
        <attr name="disabledColor" format="color" />
        <!--
        -->
        <!--<attr name="monthTextSize" format="dimension"/>-->
        <!--<attr name="dateTextSize" format="dimension"/>-->
        <!--<attr name="dayTextSize" format="dimension"/>-->
    </declare-styleable>

    <attr name="attr_toolbar_color" format="color"/>
    <attr name="searchBg" format="color"/>
    <attr name="progress_bar_color" format="color"/>
    <attr name="hamburger_color" format="color"/>
    <attr name="attr_bg_color" format="color"/>
    <attr name="btn_color_primary" format="color"/>
    <attr name="btn_color_secondary" format="color"/>
    <attr name="text_color_primary" format="color"/>
    <attr name="text_color_secondary" format="color"/>
    <attr name="link_color_primary" format="color"/>
    <attr name="link_color_secondary" format="color"/>

    <attr name="toolbar_icon_tint_primary" format="color"/>
    <attr name="toolbar_icon_tint_secondary" format="color"/>
    <attr name="toolbar_text_color_primary" format="color"/>
    <attr name="toolbar_text_color_secondary" format="color"/>

    <attr name="hint_color_primary" format="color"/>
    <attr name="hint_color_secondary" format="color"/>
    <attr name="img_tint_primary" format="color"/>
    <attr name="attr_hamburger_text_color" format="color"/>

    <attr name="img_tint_secondary" format="color"/>
    <attr name="searchTextViewOnToolbar" format="color"/>
    <attr name="mcpToolbarTheme" format="reference"/>
    <attr name="mcpListPopupWindowStyleTheme" format="reference"/>
</resources>