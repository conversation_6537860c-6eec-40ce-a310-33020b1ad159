<?xml version="1.0" encoding="utf-8"?>
<resources>

    <declare-styleable name="DualTextButton">
        <attr name="text_line_one" format="string" />
        <attr name="text_line_two" format="string" />
    </declare-styleable>

    <declare-styleable name="DividerEditText">
        <attr name="drawableStart" format="reference" />
        <attr name="showDivider_" format="boolean" />
        <attr name="hint" format="string" />
        <attr name="text" format="string" />
        <attr name="textColor" format="reference|color" />
    </declare-styleable>

    <declare-styleable name="MultiSpinnerSearch">
        <attr name="hintText" format="string" />
    </declare-styleable>


    <declare-styleable name="SegmentedButton">
        <attr name="textSize" format="dimension"/>
        <attr name="segmentHeight" format="dimension"/>
        <attr name="textColors" format="color"/>
        <attr name="textColorChecked" format="color"/>
        <attr name="segmentColor" format="color"/>
        <attr name="segmentColorChecked" format="color"/>
        <attr name="rippleColor" format="color"/>
        <attr name="rippleColorChecked" format="color"/>
        <attr name="borderColor" format="color"/>
        <attr name="borderWidth" format="dimension"/>
        <attr name="cornerRadius" format="dimension"/>
        <attr name="segmentFont" format="reference"/>
        <attr name="segmentFontChecked" format="reference"/>

        <attr name="spreadType" format="enum">
            <enum name="evenly" value="0"/>
            <enum name="wrap" value="1"/>
        </attr>
    </declare-styleable>
    <declare-styleable name="CustomSpinner" >
        <attr name="labelText" format="string" />
        <attr name="widgetColor" format="color" />

    </declare-styleable>

    <declare-styleable name="MarqueeTextView">
        <attr name="space" format="dimension" />
        <attr name="speed" format="integer" />
    </declare-styleable>


</resources>