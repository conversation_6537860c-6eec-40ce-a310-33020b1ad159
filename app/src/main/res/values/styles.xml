<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="AppTheme" parent="Theme.MaterialComponents.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/transparent</item>
        <item name="android:fontFamily">@font/poppins_medium</item>
        <item name="colorAccent">@color/App_color</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowBackground">@color/white</item>
        <item name="popupMenuStyle">@style/MyPopupMenu</item>
        <item name="android:windowDisablePreview">true</item>

    </style>

    <style name="MyPopupMenu" parent="@style/Widget.AppCompat.PopupMenu">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:dropDownVerticalOffset">4dp</item>
    </style>


    <style name="WindowAnimationTransition">
        <item name="android:windowEnterAnimation">@android:anim/fade_in</item>
        <item name="android:windowExitAnimation">@android:anim/fade_out</item>
    </style>
    <style name="DialogAnimation">
        <item name="android:windowEnterAnimation">@anim/flip_up</item>
        <item name="android:windowExitAnimation">@anim/flip_down</item>
    </style>

    <style name="CustomRadioButton" parent="@android:style/Widget.CompoundButton.RadioButton">
        <item name="android:button">@drawable/custom_radio_button</item>
    </style>
    <style name="MyStyleDialog" parent="Theme.AppCompat.Light.Dialog">
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@drawable/background_corner_white</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowNoTitle">true</item>
        <!--<item name="android:padding">2dp</item>-->
        <item name="android:windowCloseOnTouchOutside">false</item>
    </style>

    <style name="MyThemeDialog" parent="@style/ThemeOverlay.AppCompat.Dialog.Alert">
<!--        <item name="android:windowBackground">@drawable/bg_view_shadow_border</item>-->
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowContentOverlay">@null</item>

    </style>

</resources>
