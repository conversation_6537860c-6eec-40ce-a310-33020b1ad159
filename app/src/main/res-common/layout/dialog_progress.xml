<?xml version="1.0" encoding="utf-8"?>
<LinearLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:gravity="center"
    android:padding="20dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:text="@string/invoice_request"
        android:textStyle="bold"
        android:textSize="16sp"
        android:textColor="@color/colorPrimary"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
    <ticker.views.com.ticker.widgets.circular.timer.view.CircularView
        android:id="@+id/circular_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:m_circle_radius="30"
        android:layout_marginVertical="20dp"
        app:m_cicle_stroke_width="8"
        app:m_arc_stroke_color="@android:color/white"
        app:m_circle_stroke_color="@color/colorPrimary"
        />
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/dialogMessage"
        android:layout_width="match_parent"
        android:layout_gravity="center"
        android:gravity="center"
        android:text="@string/please_wait_n_your_invoice_request_is_under_processing"
        android:layout_height="wrap_content">

    </androidx.appcompat.widget.AppCompatTextView>
</LinearLayout>