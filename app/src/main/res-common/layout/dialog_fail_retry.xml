<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <RelativeLayout
        android:layout_width="320dp"
        android:layout_height="400dp"
        android:background="@drawable/formulaire_activation_fidelity"
        android:backgroundTint="@color/white"
        android:padding="10dp">

        <ImageView
            android:id="@+id/close"
            android:layout_width="@dimen/dp35"
            android:layout_height="@dimen/dp35"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:visibility="gone"
            android:src="@drawable/close_red"/>


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:gravity="center"
            android:orientation="vertical">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/icon"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_120sdp"
                android:layout_gravity="center"
                android:layout_marginBottom="@dimen/_20sdp"
                android:visibility="visible"
                app:lottie_autoPlay="true"
                app:lottie_enableMergePathsForKitKatAndAbove="true"
                app:lottie_loop="false"
                app:lottie_rawRes="@raw/failed"
                app:lottie_repeatMode="restart" />


            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/app_name"
                android:textColor="@android:color/holo_red_light"
                android:textSize="14sp"
                android:textStyle="bold"
                tools:text="Success !!" />

            <TextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:text=""
                android:textColor="@color/black"
                android:textSize="14sp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title"
                tools:text="Message" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:orientation="horizontal"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/action_done"
                    android:layout_width="@dimen/_70sdp"
                    android:layout_height="wrap_content"
                    app:backgroundTint="@android:color/holo_red_light"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    app:cornerRadius="10dp"
                    android:background="@drawable/formulaire_activation_fidelity"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="10dp"
                    android:textAlignment="center"
                    android:textAllCaps="true"
                    android:visibility="gone"
                    android:textColor="@color/white"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginVertical="10dp"
                    android:text="@string/cancel" />
                <TextView
                    android:id="@+id/btnRetry"
                    android:layout_width="@dimen/_70sdp"
                    android:layout_height="wrap_content"
                    app:backgroundTint="@color/colorPrimary"
                    android:insetTop="0dp"
                    android:insetBottom="0dp"
                    app:cornerRadius="10dp"
                    android:background="@drawable/formulaire_activation_fidelity"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="10dp"
                    android:textAlignment="center"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginVertical="10dp"
                    android:text="@string/retry" />
            </LinearLayout>
        </LinearLayout>


    </RelativeLayout>
</layout>