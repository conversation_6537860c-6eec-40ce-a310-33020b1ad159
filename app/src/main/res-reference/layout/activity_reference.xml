<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.ReferenceViewModel" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.common.startup.activity.ReferenceActivity"
        android:orientation="vertical"
        android:id="@+id/main">



       <RelativeLayout
           android:layout_width="match_parent"
           android:layout_height="match_parent"
           tools:ignore="UselessParent">


           <androidx.constraintlayout.widget.ConstraintLayout
               android:layout_width="match_parent"
               android:layout_height="match_parent"
               android:orientation="vertical">

               <ImageView
                   android:id="@+id/ivLanguageLogo"
                   android:layout_width="130dp"
                   android:layout_height="130dp"
                   android:layout_centerHorizontal="true"
                   android:layout_marginTop="90dp"
                   android:backgroundTint="#3300dfff"
                   android:background="@drawable/rounded_rectangle"
                   android:src="@drawable/ic_language_choice"
                   app:tint="@color/colorPrimary"
                   android:padding="@dimen/_5sdp"
                   app:layout_constraintEnd_toEndOf="parent"
                   app:layout_constraintStart_toStartOf="parent"
                   app:layout_constraintTop_toTopOf="parent"
                   android:contentDescription="@string/language_logo" />


               <RelativeLayout
                   android:layout_width="250dp"
                   android:layout_height="250dp"
                   android:layout_centerInParent="true"
                   android:layout_marginTop="60dp"
                   android:background="@drawable/rounded_rectangle"
                   android:backgroundTint="#f4f5f9"
                   android:orientation="vertical"
                   android:padding="20dp"
                   app:layout_constraintEnd_toEndOf="parent"
                   app:layout_constraintStart_toStartOf="parent"
                   app:layout_constraintTop_toBottomOf="@+id/ivLanguageLogo">

                   <TextView
                       android:id="@+id/tvSelectLanguage"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:layout_alignParentTop="true"
                       android:layout_marginBottom="20dp"
                       android:text="@string/select_your_language"
                       android:textColor="@color/black"
                       android:textSize="16sp" />

                   <RadioGroup
                       android:id="@+id/rbGroupLanguage"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:layout_centerVertical="true"
                       android:orientation="vertical">

                       <RadioButton
                           android:id="@+id/rbFrench"
                           style="@style/CustomRadioButton"
                           android:layout_width="wrap_content"
                           android:layout_height="wrap_content"
                           android:layout_marginBottom="5dp"
                           android:paddingStart="10dp"
                           android:text="@string/french"
                           tools:ignore="RtlSymmetry" />

                       <RadioButton
                           android:id="@+id/rbEnglish"
                           style="@style/CustomRadioButton"
                           android:layout_width="wrap_content"
                           android:layout_height="wrap_content"
                           android:paddingStart="10dp"
                           android:text="@string/english"
                           tools:ignore="RtlSymmetry" />

                       <RadioButton
                           android:id="@+id/rbarabic"
                           style="@style/CustomRadioButton"
                           android:layout_width="wrap_content"
                           android:visibility="gone"
                           android:layout_height="wrap_content"
                           android:paddingStart="10dp"
                           android:text="@string/arabic"
                           tools:ignore="RtlSymmetry" />

                   </RadioGroup>

                   <LinearLayout
                       android:id="@+id/buttonReference"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:layout_alignParentEnd="true"
                       android:layout_alignParentBottom="true"
                       android:layout_marginTop="@dimen/_20sdp"
                       android:background="@drawable/rounded_rectangle_button"
                       android:backgroundTint="@color/colorPrimary"
                       android:clickable="true"
                       android:focusable="true"
                       android:gravity="center"
                       android:orientation="horizontal"
                       android:paddingStart="12dp"
                       android:paddingTop="8dp"
                       android:paddingEnd="12dp"
                       android:paddingBottom="8dp"
                       tools:targetApi="m">

                       <TextView
                           android:id="@+id/tvSubmitButton"
                           android:layout_width="wrap_content"
                           android:layout_height="wrap_content"
                           android:layout_marginEnd="5dp"
                           android:text="@string/submit"
                           android:textAllCaps="true"
                           android:textColor="@color/white" />

                       <ImageView
                           android:layout_width="15dp"
                           android:layout_height="15dp"
                           android:src="@drawable/ic_arrow_right_rounded"
                           android:contentDescription="@string/next_button" />

                   </LinearLayout>

               </RelativeLayout>


           </androidx.constraintlayout.widget.ConstraintLayout>

           <RelativeLayout
               android:id="@+id/loadingLayout"
               android:layout_width="match_parent"
               android:layout_height="match_parent"
               android:background="@color/white"
               android:clickable="true"
               android:visibility="gone"
               android:focusable="true">

               <com.airbnb.lottie.LottieAnimationView
                   android:id="@+id/animationView"
                   android:layout_width="250dp"
                   android:layout_height="180dp"
                   android:layout_alignParentTop="true"
                   android:layout_centerHorizontal="true"
                   android:layout_marginStart="10dp"
                   android:layout_marginTop="90dp"
                   android:layout_marginEnd="10dp"
                   app:lottie_autoPlay="true"
                   app:lottie_loop="true"
                   app:lottie_rawRes="@raw/referencing_amination" />

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:layout_below="@+id/animationView"
                   android:layout_centerHorizontal="true"
                   android:layout_marginTop="-30dp"
                   android:layout_marginBottom="20dp"
                   android:text="@string/referencing"
                   android:textColor="@color/black"
                   android:textSize="14sp"
                   android:textStyle="bold" />

               <TextView
                   android:layout_width="wrap_content"
                   android:layout_height="wrap_content"
                   android:layout_alignParentBottom="true"
                   android:layout_centerHorizontal="true"
                   android:layout_marginBottom="90dp"
                   android:gravity="center"
                   android:text="@string/please_wait_until_nfinishes_referencing"
                   android:textColor="@color/grey" />

           </RelativeLayout>

       </RelativeLayout>

    </LinearLayout>
</layout>
