<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarVerifyPin"
            layout="@layout/toolbar" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbarVerifyPin"
            android:background="#ffffff"
            android:gravity="center"
            android:layout_marginTop="@dimen/_20sdp"
            android:orientation="vertical"
            tools:context=".MainActivity">

            <ImageView
                android:id="@+id/ivAttendant"
                android:layout_width="@dimen/_120sdp"
                android:layout_marginTop="@dimen/_20sdp"
                app:tint="@color/colorPrimary"
                android:layout_height="@dimen/_50sdp"
                android:src="@drawable/ic_change_pin"
                />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/pinMessage"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_20sdp"
                android:gravity="center"
                android:layout_marginHorizontal="@dimen/_5sdp"
                android:text=""
                android:textColor="@color/green"
                android:visibility="gone">

            </androidx.appcompat.widget.AppCompatTextView>

            <app.rht.petrolcard.utils.passwordview.PasswordView
                android:id="@+id/password_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_30sdp"
                app:layout_constraintEnd_toEndOf="@id/ivAttendant"
                app:layout_constraintStart_toStartOf="@id/ivAttendant"
                app:layout_constraintTop_toBottomOf="@id/ivAttendant"
                app:password_between_margin="30dp"
                app:password_correct_color="@color/green"
                app:password_count="4"
                app:password_filled_color="@color/light_blue"
                app:password_incorrect_color="@color/red" />
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="match_parent"
                android:visibility="gone"
                android:layout_height="wrap_content">

            </ProgressBar>

        </LinearLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:gravity="bottom"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/text_1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />
                    <TextView
                        android:id="@+id/text_2"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_3"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/text_4"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="4"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_5"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="5"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_6"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="6"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/text_7"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="7"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_8"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="8"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_9"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="9"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/text_d"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        app:tint="@color/red"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:padding="18dp"
                        android:src="@drawable/ic_delete_key"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_0"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />


                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/text_submit"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:tint="@color/green"
                        android:padding="18dp"
                        android:src="@drawable/ic_tick_done"
                        android:textSize="@dimen/input_button_text_size"
                        android:visibility="visible" />
                </LinearLayout>
            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>
</layout>