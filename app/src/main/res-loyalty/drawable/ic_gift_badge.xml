<vector android:autoMirrored="true" android:height="79.1dp"
    android:viewportHeight="79.1" android:viewportWidth="61.39"
    android:width="61.39dp" xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#f6921e" android:pathData="M61.39,70.9l-10.37,0.46l-6.92,7.74l-18.35,-38.67l17.3,-8.2l18.34,38.67z"/>
    <path android:fillColor="#d0871e" android:pathData="M43.05,32.23l-17.3,8.2 6.55,13.8a3.84,3.84 0,0 1,1.44 0.66,3.8 3.8,0 0,0 5.58,-1.11h0A3.81,3.81 0,0 1,43.56 52a3.82,3.82 0,0 0,4.73 -3.16,3.76 3.76,0 0,1 1.47,-2.5Z"/>
    <path android:fillColor="#f6921e" android:pathData="M0,70.9l10.37,0.46l6.93,7.74l18.34,-38.67l-17.3,-8.2l-18.34,38.67z"/>
    <path android:fillColor="#d0871e" android:pathData="M18.34,32.23 L11.93,45.75a3.8,3.8 0,0 1,2.66 3.12A3.82,3.82 0,0 0,19.32 52a3.83,3.83 0,0 1,4.25 1.75,3.79 3.79,0 0,0 5.07,1.42l7,-14.77Z"/>
    <path android:pathData="M57.56,24.6A3.8,3.8 0,0 0,56.45 19a3.8,3.8 0,0 1,-1.75 -4.24A3.82,3.82 0,0 0,51.53 10,3.8 3.8,0 0,1 48.29,6.8a3.81,3.81 0,0 0,-4.73 -3.16,3.8 3.8,0 0,1 -4.24,-1.76h0A3.82,3.82 0,0 0,33.74 0.77a3.81,3.81 0,0 1,-4.6 0,3.8 3.8,0 0,0 -5.57,1.11 3.82,3.82 0,0 1,-4.25 1.76A3.81,3.81 0,0 0,14.59 6.8,3.8 3.8,0 0,1 11.35,10a3.81,3.81 0,0 0,-3.16 4.74A3.82,3.82 0,0 1,6.43 19,3.8 3.8,0 0,0 5.32,24.6a3.83,3.83 0,0 1,0 4.59,3.8 3.8,0 0,0 1.11,5.58A3.81,3.81 0,0 1,8.19 39a3.8,3.8 0,0 0,3.16 4.73A3.8,3.8 0,0 1,14.59 47a3.82,3.82 0,0 0,4.73 3.16,3.82 3.82,0 0,1 4.25,1.76A3.79,3.79 0,0 0,29.14 53a3.84,3.84 0,0 1,4.6 0,3.81 3.81,0 0,0 5.58,-1.1h0a3.8,3.8 0,0 1,4.24 -1.76A3.82,3.82 0,0 0,48.29 47a3.8,3.8 0,0 1,3.24 -3.25A3.81,3.81 0,0 0,54.7 39a3.79,3.79 0,0 1,1.75 -4.24,3.8 3.8,0 0,0 1.11,-5.58A3.82,3.82 0,0 1,57.56 24.6Z">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="9.16" android:endY="63.69"
                android:startX="46.23" android:startY="2.48" android:type="linear">
                <item android:color="#FFF3AB3F" android:offset="0"/>
                <item android:color="#FFFFDB4F" android:offset="0.19"/>
                <item android:color="#FFFFF882" android:offset="0.36"/>
                <item android:color="#FFFFDB4F" android:offset="0.74"/>
                <item android:color="#FFD0871E" android:offset="0.96"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#fff882" android:pathData="M54.7,15a3.82,3.82 0,0 0,-3.17 -4.73A3.8,3.8 0,0 1,48.29 7a3.81,3.81 0,0 0,-4.73 -3.16,3.8 3.8,0 0,1 -4.24,-1.76h0A3.82,3.82 0,0 0,33.74 1a3.81,3.81 0,0 1,-4.6 0,3.8 3.8,0 0,0 -5.57,1.11 3.82,3.82 0,0 1,-4.25 1.76A3.81,3.81 0,0 0,14.59 7a3.8,3.8 0,0 1,-3.24 3.24A3.81,3.81 0,0 0,8.19 15a3.83,3.83 0,0 1,-1.76 4.25,3.8 3.8,0 0,0 -1.11,5.58 3.82,3.82 0,0 1,0 4.59,3.79 3.79,0 0,0 -0.7,3h0a3.74,3.74 0,0 1,0.2 -1.82,3.43 3.43,0 0,1 0.41,-0.81c0.16,-0.24 0.39,-0.47 0.55,-0.76A4.09,4.09 0,0 0,6 25.22a3.57,3.57 0,0 1,1.18 -5.7A4.46,4.46 0,0 0,9 15.72,3.55 3.55,0 0,0 9,15.16l-0.09,-0.48a2.7,2.7 0,0 1,0 -0.75,2.9 2.9,0 0,1 0.45,-1.4 3.07,3.07 0,0 1,1.05 -1,2.67 2.67,0 0,1 0.67,-0.29c0.24,-0.07 0.42,-0.08 0.85,-0.16a4.73,4.73 0,0 0,3.38 -2.88,5.45 5.45,0 0,0 0.29,-1.08 2.56,2.56 0,0 1,0.21 -0.68,2.71 2.71,0 0,1 0.85,-1.1 3,3 0,0 1,1.27 -0.58,3.26 3.26,0 0,1 0.7,0 2,2 0,0 1,0.34 0l0.45,0.09a4.78,4.78 0,0 0,4.33 -1.29,5 5,0 0,0 0.71,-0.93 3.15,3.15 0,0 1,0.43 -0.57A2.86,2.86 0,0 1,26 1.34a2.89,2.89 0,0 1,2.71 0.5,4.68 4.68,0 0,0 4.43,0.54 4.4,4.4 0,0 0,1 -0.56l0.35,-0.24a2.29,2.29 0,0 1,0.31 -0.18,2.9 2.9,0 0,1 1.39,-0.29 3,3 0,0 1,2.43 1.42l0.26,0.39h0a4.5,4.5 0,0 0,2.36 1.62A4.28,4.28 0,0 0,43 4.69a4,4 0,0 0,0.81 -0.15,5.4 5.4,0 0,1 0.58,-0.1 3.22,3.22 0,0 1,2.21 0.64,3.28 3.28,0 0,1 1.23,2 4.23,4.23 0,0 0,1.61 2.65,4.33 4.33,0 0,0 1.39,0.7 4.94,4.94 0,0 0,0.75 0.14,2.91 2.91,0 0,1 0.64,0.13A3.72,3.72 0,0 1,54.7 15Z"/>
    <path android:fillColor="#f3ab3f" android:pathData="M56,35.12Z"/>
    <path android:fillColor="#f3ab3f" android:pathData="M48.29,47a3.8,3.8 0,0 1,3.24 -3.24A3.82,3.82 0,0 0,54.7 39a3.8,3.8 0,0 1,1.23 -3.88,3.88 3.88,0 0,0 -1.65,2.95 4,4 0,0 0,0 0.91,5.5 5.5,0 0,1 0.07,0.78 3.34,3.34 0,0 1,-0.34 1.43A3.26,3.26 0,0 1,51.93 43a2,2 0,0 1,-0.34 0.06l-0.43,0.07a4,4 0,0 0,-1 0.27,4.67 4.67,0 0,0 -1.65,1.16 4.57,4.57 0,0 0,-1 1.79c-0.05,0.17 -0.08,0.33 -0.12,0.5l-0.07,0.35a3.13,3.13 0,0 1,-0.24 0.6,3 3,0 0,1 -2,1.49 2.82,2.82 0,0 1,-1.26 0l-0.45,-0.1a4.34,4.34 0,0 0,-0.52 -0.07,4.71 4.71,0 0,0 -4.12,1.87h-0.06l-0.26,0.48a3,3 0,0 1,-3.59 1.25,6.82 6.82,0 0,1 -1.09,-0.62 4.34,4.34 0,0 0,-1.56 -0.55,4.57 4.57,0 0,0 -3.15,0.68c-0.11,0.07 -0.22,0.16 -0.33,0.24l-0.24,0.18a2.87,2.87 0,0 1,-0.48 0.28,3 3,0 0,1 -1.1,0.31 3.37,3.37 0,0 1,-2.24 -0.61,3.83 3.83,0 0,1 -0.87,-0.88A3.91,3.91 0,0 0,20.1 50h0.06a3.82,3.82 0,0 1,3.41 1.87A3.8,3.8 0,0 0,29.14 53a3.81,3.81 0,0 1,4.6 0,3.82 3.82,0 0,0 5.58,-1.11h0a3.8,3.8 0,0 1,4.24 -1.76A3.81,3.81 0,0 0,48.29 47Z"/>
    <path android:pathData="M31.37,26.68m-15.67,0a15.67,15.67 0,1 1,31.34 0a15.67,15.67 0,1 1,-31.34 0">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="43.38" android:endY="41.96"
                android:startX="20.12" android:startY="12.35" android:type="linear">
                <item android:color="#FFF3AB3F" android:offset="0"/>
                <item android:color="#FFFFDB4F" android:offset="0.19"/>
                <item android:color="#FFFFF882" android:offset="0.36"/>
                <item android:color="#FFF3AB3F" android:offset="0.54"/>
                <item android:color="#FFFFDB4F" android:offset="0.74"/>
                <item android:color="#FFD0871E" android:offset="0.96"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#fff882" android:pathData="M31.44,11.29a15.6,15.6 0,0 0,-15.61 15.6,15.43 15.43,0 0,0 2,7.67 16,16 0,0 1,-1.08 -11.78,15.38 15.38,0 0,1 7.39,-9.14 15.08,15.08 0,0 1,5.7 -1.83,16.33 16.33,0 0,1 6,0.5 16.07,16.07 0,0 1,9.38 7.29A15.6,15.6 0,0 0,31.44 11.29Z"/>
    <path android:fillColor="#faaf3b" android:pathData="M11.71,46.21l-10.43,24.09l9.09,1.06l-10.37,-0.46l11.71,-24.69z"/>
    <path android:fillColor="#faaf3b" android:pathData="M34.06,57.94l10.04,21.16l6.47,-7.24l-6.2,5.19l-10.31,-19.11z"/>
</vector>
