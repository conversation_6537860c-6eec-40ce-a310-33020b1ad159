<vector android:autoMirrored="true" android:height="512dp"
    android:viewportHeight="512" android:viewportWidth="512"
    android:width="512dp" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#000000"
        android:pathData="M80,17.3c-4.9,1.6 -13.3,10.2 -14.8,15.3 -0.9,3.1 -1.2,24.6 -1.2,85.6l0,81.5 -9.1,0.5c-7.8,0.5 -10,1.1 -16.3,4.2 -8.8,4.3 -14.6,10.2 -18.9,19.4l-3.2,6.7 -0.3,21c-0.3,19.9 0.1,25.5 7.8,104.9 7.6,79.7 8,85.2 8,109.3l0,25.4 2.5,2.4 2.4,2.5 91.1,-0 91.1,-0 2.4,-2.5 2.5,-2.4 0,-24.3 0,-24.3 14.8,-21.2 14.7,-21.2 8.6,-0.1c10,-0 8.2,-1.9 14.4,15.4l3.5,9.8 0,33 0,32.9 2.5,2.4 2.4,2.5 83.1,-0 83.1,-0 2.4,-2.5 2.5,-2.4 0,-32.8 0,-32.9 18.9,-53c10.4,-29.1 19.5,-55.7 20.1,-59 0.8,-3.9 1,-13.7 0.8,-27.9 -0.3,-21 -0.5,-22.2 -2.8,-27 -3.1,-6.4 -9.1,-12.4 -15.5,-15.5 -4.1,-2 -6.5,-2.5 -13.5,-2.5 -7.7,-0 -9.1,0.3 -14.5,3.3l-5.9,3.2 -4.5,-4.6c-6.4,-6.8 -13.7,-9.9 -23.1,-9.9 -6.5,-0 -8.5,0.4 -14,3.2l-6.5,3.2 -4.4,-4.6c-8.3,-8.6 -20.7,-12.1 -31.2,-8.9 -3,1 -5.9,2 -6.6,2.2 -1,0.4 -1.3,-15 -1.5,-69.8l-0.3,-70.3 -3.4,-6.3c-8.6,-15.8 -25.1,-21 -42.8,-13.6 -1,0.4 -1.3,-3 -1.3,-15.2 0,-9.4 -0.5,-17.3 -1.2,-19.8 -1.6,-5.5 -9.9,-13.8 -15.4,-15.4 -5.6,-1.6 -202.1,-1.5 -207.4,0.1zM285.5,34.5l2.5,2.4 0,83.6c0,66.6 -0.3,83.6 -1.2,83.1 -0.7,-0.2 -3.7,-1.2 -6.7,-2.2 -14.1,-4.3 -30.2,3 -37.1,17.1l-2.5,5 -0.3,42c-0.2,29.1 0,43.8 0.8,48 0.7,3.3 6.3,20.4 12.6,37.9 6.3,17.6 11.4,32.1 11.4,32.3 0,0.2 -40.5,0.3 -90,0.3l-90.1,-0 -2.4,-2.5c-2.4,-2.3 -2.5,-2.9 -2.5,-16l0,-13.5 72,-0 72,-0 0,-8 0,-8 -72,-0 -72,-0 0,-27.9 0,-27.9 20.8,-0.4 20.8,-0.3 7.6,-3.7c9.1,-4.5 14.7,-10.3 19.1,-19.6 2.9,-6.1 3.2,-7.6 3.2,-16.2 0,-8.6 -0.3,-10.1 -3.2,-16.2 -4.4,-9.3 -10,-15.1 -19.1,-19.6l-7.6,-3.7 -20.8,-0.3 -20.8,-0.4 0,-81.4 0,-81.5 2.5,-2.4 2.4,-2.5 99.1,-0 99.1,-0 2.4,2.5zM329.6,83.5c6.8,5.2 6.4,-1.1 6.4,111l0,101.5 8,-0 8,-0 0,-21.6c0,-24.2 0.5,-26.4 6.8,-31.2 2.9,-2.2 4.6,-2.7 9.2,-2.7 4.7,-0 6.3,0.5 9.4,2.9 6.1,4.6 6.6,6.8 6.6,31.1l0,21.5 7.9,-0 8,-0 0.3,-18.9c0.3,-17.3 0.5,-19.2 2.4,-21.7 3.9,-5.2 7.1,-6.9 13.3,-6.9 4.8,-0 6.4,0.5 9.5,2.9 6.1,4.6 6.6,6.8 6.6,31.1l0,21.5 8,-0 8,-0 0,-17.6c0,-19.8 0.7,-22.5 6.8,-27.2 2.9,-2.2 4.6,-2.7 9.2,-2.7 4.6,-0 6.3,0.5 9.2,2.7 6.3,4.8 6.8,7.1 6.8,29.5 0,12.5 -0.5,21.5 -1.3,24.4 -0.6,2.5 -9.1,26.7 -18.7,53.7l-17.5,49.2 -74.5,-0 -74.5,-0 -18.5,-51.8 -18.5,-51.7 0,-43.2c0,-42 0.1,-43.2 2.1,-45.9 3.9,-5.3 7.1,-6.9 13.4,-6.9 6.3,-0 9.5,1.6 13.4,6.9 2,2.6 2.1,4.2 2.4,37.7l0.3,34.9 7.9,-0 8,-0 0.2,-102.8 0.3,-102.9 2.7,-3.5c5.6,-7.4 15.2,-8.8 22.4,-3.3zM119.4,217.2c8.6,2.6 16.6,13.6 16.6,22.8 0,9.2 -8,20.2 -16.6,22.8 -2.7,0.8 -12,1.2 -27.3,1.2l-23.2,-0 -2.4,2.5 -2.5,2.4 0,55.2c0,39.4 0.3,56.3 1.2,59.1 1.5,4.9 7.6,12 12.5,14.4 3.6,1.8 8,1.9 80,2.4l76.1,0.5 -11,15.7 -11,15.8 -82.4,-0c-74.7,-0 -82.4,-0.1 -82.4,-1.6 0,-0.9 -3.4,-36.8 -7.5,-79.9 -6.1,-63.8 -7.5,-81.5 -7.5,-96 0,-11 0.5,-19.3 1.2,-21.9 1.5,-5.1 9.9,-13.7 14.8,-15.3 5.1,-1.6 66,-1.7 71.4,-0.1zM320,456l0,24 -12,-0 -12,-0 0,-24 0,-24 12,-0 12,-0 0,24zM360,456l0,24 -12,-0 -12,-0 0,-24 0,-24 12,-0 12,-0 0,24zM400,456l0,24 -12,-0 -12,-0 0,-24 0,-24 12,-0 12,-0 0,24zM440,456l0,24 -12,-0 -12,-0 0,-24 0,-24 12,-0 12,-0 0,24zM208,464l0,16 -80,-0 -80,-0 0,-16 0,-16 80,-0 80,-0 0,16z" android:strokeColor="#00000000"/>
    <path android:fillColor="#000000"
        android:pathData="M169.5,49.4c-21.8,4.3 -40.5,16.8 -52.5,35.3 -8.9,13.6 -12.5,25.9 -12.5,43.3 0,17.4 3.6,29.7 12.4,43.2 24.3,37.1 73.7,47.8 110.4,23.8 42.7,-27.8 49,-87.5 13.1,-123.4 -10.3,-10.4 -22.2,-17.1 -37.4,-21.2 -7.8,-2.2 -25,-2.6 -33.5,-1zM197.4,65.5c36.7,7.8 59.4,47.4 47.3,82.4 -3.6,10.2 -8.1,17.4 -15.7,25.1 -25.4,25.3 -64.7,25.3 -90.1,-0.1 -25.1,-24.9 -25.1,-64.8 0,-90 15.6,-15.5 37.1,-21.9 58.5,-17.4z" android:strokeColor="#00000000"/>
    <path android:fillColor="#000000"
        android:pathData="M176,84.9c0,4.1 -0.3,5.1 -2.1,5.6 -3.3,1.1 -8.8,6.5 -11.2,10.9 -2.9,5.6 -3,15.5 0,21.1 4.1,7.7 12.9,13.4 20.6,13.5 7.7,-0 11.5,8.3 6.2,13.5 -1.5,1.6 -3.6,2.5 -5.5,2.5 -3.3,-0 -8,-3.8 -8,-6.5 0,-1.2 -1.5,-1.5 -8,-1.5 -8.9,-0 -9,-0 -6.8,7.2 1.4,4.8 7.6,12.1 11.8,13.8 2.8,1.1 3,1.6 3,6.1l0,4.9 8,-0 8,-0 0,-4.9c0,-4.1 0.3,-5.1 2.1,-5.6 6,-1.9 12.4,-10.9 13.6,-19 1.8,-13.1 -10.1,-26.5 -23.6,-26.5 -4.2,-0 -8.1,-3.8 -8.1,-8 0,-4.1 3.9,-8 8,-8 3.3,-0 8,3.8 8,6.5 0,1.2 1.5,1.5 8,1.5 8.9,-0 9,-0 6.8,-7.2 -1.4,-4.8 -7.6,-12.1 -11.8,-13.8 -2.8,-1.1 -3,-1.6 -3,-6.1l0,-4.9 -8,-0 -8,-0 0,4.9z" android:strokeColor="#00000000"/>
    <path android:fillColor="#000000"
        android:pathData="M455.7,55.8c-2.6,2.6 -4.7,5.2 -4.7,5.7 0,0.5 3.9,4.9 8.7,9.7l8.8,8.8 -46.3,-0 -46.2,-0 0,8 0,8 46.2,-0 46.3,-0 -9,9 -9,9 5.8,5.8 5.7,5.7 17,-17.1c15.3,-15.3 17,-17.4 17,-20.4 0,-3 -1.7,-5.1 -16.7,-20.2 -9.2,-9.2 -17.2,-16.8 -17.8,-16.8 -0.6,-0 -3.2,2.2 -5.8,4.8z" android:strokeColor="#00000000"/>
    <path android:fillColor="#000000"
        android:pathData="M384.7,131.8c-15,15.1 -16.7,17.2 -16.7,20.2 0,3 1.7,5.1 17,20.4l17,17.1 5.7,-5.7 5.8,-5.8 -9,-9 -9,-9 46.3,-0 46.2,-0 0,-8 0,-8 -46.2,-0 -46.3,-0 8.8,-8.8c4.8,-4.8 8.7,-9.2 8.7,-9.8 0,-1.3 -9.2,-10.4 -10.6,-10.4 -0.5,-0 -8.5,7.6 -17.7,16.8z" android:strokeColor="#00000000"/>
</vector>
