<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="320dp"
            android:orientation="vertical"
            android:background="@drawable/formulaire_activation_fidelity"
            android:gravity="center"
            android:padding="10dp"
            android:layout_centerInParent="true">

            <ImageView
                android:layout_width="128dp"
                android:layout_height="128dp"
                android:src="@drawable/gift_box_color" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/tvGiftName"
                android:text="@string/gift_name"
                android:textAlignment="center"
                android:textStyle="bold"
                android:textSize="20sp"
                android:layout_marginTop="10dp"
                android:textAllCaps="true"
                android:textColor="@color/colorPrimary"/>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/tvGiftPoints"
                android:text="@string/points"
                android:textAlignment="center"
                android:textStyle="bold"
                android:textSize="16sp"
                android:layout_marginTop="10dp"
                android:textAllCaps="true"
                android:textColor="@color/black"/>

            <Button
                android:id="@+id/btnVerify"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_gravity="center"
                android:layout_marginStart="10dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/gold"
                android:clickable="true"
                android:onClick="onClick"
                android:stateListAnimator="@null"
                android:text="@string/verify"
                android:textAllCaps="true"
                android:textColor="#fff" />

        </LinearLayout>

</RelativeLayout>