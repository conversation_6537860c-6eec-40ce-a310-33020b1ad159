<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center">
        <include layout="@layout/toolbar" android:id="@+id/toolbarLayout"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical">



            <LinearLayout
                android:id="@+id/promptLayout"
                android:layout_marginBottom="10dp"
                android:gravity="center"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:layout_alignParentStart="true">

                <TextView
                    android:text="@string/please_authenticate_with_your_nfc"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:id="@+id/prompt"
                    android:textSize="18sp"
                    android:textAllCaps="true"
                    android:textColor="@color/black"/>

            </LinearLayout>

            <LinearLayout
                android:id="@+id/insertCardImageLayout"
                android:layout_below="@+id/promptLayout"
                android:gravity="center"
                android:layout_marginBottom="10dp"
                android:layout_marginTop="20dp"
                android:padding="20dp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/insertCardImageView"
                    android:layout_width="300dp"
                    android:layout_height="300dp"
                    android:layout_gravity="center"
                    android:adjustViewBounds="false"
                    android:cropToPadding="false"
                    android:src="@drawable/nfc_logo_manager" />
            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

</layout>
