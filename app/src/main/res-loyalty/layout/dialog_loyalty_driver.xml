<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="320dp"
        android:layout_height="300dp"
        android:layout_centerInParent="true"
        android:background="@drawable/formulaire_activation_fidelity"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="20dp">

        <ImageView
            android:layout_width="128dp"
            android:layout_height="128dp"
            android:src="@drawable/driver_license_id" />


        <EditText
            android:id="@+id/etDriverId"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/lightGrey"
            android:paddingHorizontal="20dp"
            android:paddingVertical="12dp"
            android:textAlignment="center"
            android:textAllCaps="true"
            android:textColor="@color/black"
            android:hint="@string/enter_driver_id"
            android:textColorHint="@color/black"
            android:maxLines="1"
            android:singleLine="true"
            android:maxLength="20"
            android:textSize="16sp" />

        <Button
            android:id="@+id/btnVerify"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:layout_marginTop="20dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/gold"
            android:clickable="true"
            android:onClick="onClick"
            android:stateListAnimator="@null"
            android:text="@string/verify"
            android:textAllCaps="true"
            android:textColor="#fff" />

    </LinearLayout>

</RelativeLayout>