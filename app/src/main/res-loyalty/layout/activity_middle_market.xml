<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ui.loyalty.activity.MiddleMarketActivity">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.loyalty.viewmodel.MiddleMarketViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">
        <include
            layout="@layout/toolbar"
            android:id="@+id/toolbarLayout"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_alignParentTop="true" />

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/cardView"
            android:layout_width="match_parent"
            android:layout_height="100dp"
            app:cardCornerRadius="10dp"
            app:cardElevation="10dp"
            android:clickable="true"
            android:focusable="true"
            android:layout_margin="10dp"
            android:layout_below="@+id/toolbarLayout">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <RelativeLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:id="@+id/ivCard"
                    android:layout_margin="10dp">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/hose"
                        android:layout_width="80dp"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:src="@drawable/rounded_rectangle_button"
                        android:tint="@color/gold" />

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:layout_centerInParent="true"
                        android:layout_marginStart="5dp"
                        android:layout_marginTop="5dp"
                        android:layout_marginEnd="5dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/ic_card"
                        app:tint="@color/white" />

                </RelativeLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_toEndOf="@+id/ivCard"
                    android:paddingVertical="10dp"
                    android:orientation="vertical">


                    <TextView
                        android:id="@+id/tvMasterLoyaltyCard"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentTop="true"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="5dp"
                        android:text="@string/master_loyalty_card"
                        android:textAllCaps="true"
                        android:textColor="@color/grey"/>

                    <TextView
                        android:id="@+id/tvCardHolder"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="10dp"
                        android:text="XX XX XX"
                        android:textAllCaps="true"
                        android:textColor="@color/black"
                        android:textSize="15sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvCardNumber"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerHorizontal="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="10dp"
                        android:text="0000 0000 0000 0000"
                        android:textAllCaps="true"
                        android:textColor="@color/black"
                        android:textSize="18sp"
                        android:textStyle="bold" />

                </LinearLayout>

            </RelativeLayout>

        </com.google.android.material.card.MaterialCardView>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/cardView"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            android:orientation="vertical"
            tools:listitem="@layout/item_child_loyalty_card_item">


        </androidx.recyclerview.widget.RecyclerView>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            style="design"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentBottom="true"
            android:layout_marginStart="30dp"
            android:layout_marginTop="24dp"
            android:layout_marginEnd="24dp"
            android:layout_marginBottom="30dp"
            android:contentDescription="@string/add_child_loyalty_card"
            android:onClick="addChildLoyalty"
            android:src="@drawable/ic_add"
            app:backgroundTint="@color/gold"
            app:elevation="10dp"
            app:fabCustomSize="64dp"
            app:maxImageSize="32dp"
            app:tint="@color/white" />

    </RelativeLayout>


</layout>