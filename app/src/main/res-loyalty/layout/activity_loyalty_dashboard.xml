<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include layout="@layout/toolbar" android:id="@+id/toolbarLayout"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="10dp">

            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="@dimen/loyalty_dashboard_card_height"
                app:cardCornerRadius="6dp"
                app:cardElevation="0dp"
                android:layout_marginHorizontal="5dp"
                android:layout_marginVertical="5dp"
                android:id="@+id/transactionsCard"
                app:cardPreventCornerOverlap="true"
                android:clickable="true"
                android:focusable="true"
                android:onClick="btnClick"
                app:cardBackgroundColor="@color/colorPrimaryTr">

                <RelativeLayout

                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:targetApi="m">

                    <LinearLayout
                        android:id="@+id/loyaltyTransactionLayout"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:onClick="btnClick"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="45dp"
                            android:layout_height="45dp"
                            android:layout_alignParentTop="true"
                            android:layout_centerHorizontal="true"
                            android:layout_marginTop="25dp"
                            android:layout_marginBottom="5dp"
                            android:src="@drawable/ic_money_transfer"
                            app:tint="@color/colorPrimary" />

                        <TextView
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_below="@+id/ivCategoryImage"
                            android:layout_alignParentBottom="true"
                            android:layout_centerHorizontal="true"
                            android:layout_marginStart="10dp"
                            android:layout_marginEnd="10dp"
                            android:layout_marginBottom="15dp"
                            android:gravity="center"
                            android:lineSpacingExtra="-5dp"
                            android:text="@string/loyalty_transactions"
                            android:textColor="@color/text_color"
                            android:textSize="@dimen/loyalty_card_label_size" />
                    </LinearLayout>

                </RelativeLayout>

            </com.google.android.material.card.MaterialCardView>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/loyalty_dashboard_card_height"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginVertical="5dp"
                    android:id="@+id/activationCard"
                    app:cardPreventCornerOverlap="true"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="btnClick"
                    app:cardBackgroundColor="@color/orangeTr">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:targetApi="m">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="25dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_activate"
                                app:tint="@color/orange" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/ivCategoryImage"
                                android:layout_alignParentBottom="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:lineSpacingExtra="-5dp"
                                android:text="@string/loyalty_activation"
                                android:textColor="@color/text_color"
                                android:textSize="@dimen/loyalty_card_label_size" />
                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/loyalty_dashboard_card_height"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginVertical="5dp"
                    android:id="@+id/myBalanceCard"
                    app:cardPreventCornerOverlap="true"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="btnClick"
                    app:cardBackgroundColor="@color/amberTr">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:targetApi="m">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="25dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_loyalty_card"
                                app:tint="@color/amber" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/ivCategoryImage"
                                android:layout_alignParentBottom="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:lineSpacingExtra="-5dp"
                                android:text="@string/loyalty_balance"
                                android:textColor="@color/text_color"
                                android:textSize="@dimen/loyalty_card_label_size" />
                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/loyalty_dashboard_card_height"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginVertical="5dp"
                    android:id="@+id/loyaltyGiftCard"
                    app:cardPreventCornerOverlap="true"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="btnClick"
                    app:cardBackgroundColor="@color/purpleLightTr">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:targetApi="m">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="25dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_shopping_bag"
                                app:tint="@color/purpleLight" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/ivCategoryImage"
                                android:layout_alignParentBottom="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:lineSpacingExtra="-5dp"
                                android:text="@string/redeem_gift"
                                android:textColor="@color/text_color"
                                android:textSize="@dimen/loyalty_card_label_size" />
                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/loyalty_dashboard_card_height"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginVertical="5dp"
                    android:id="@+id/cardChangeCard"
                    app:cardPreventCornerOverlap="true"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="btnClick"
                    android:visibility="invisible"
                    app:cardBackgroundColor="@color/blue2Tr">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:targetApi="m">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="25dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_card_change"
                                app:tint="@color/blue2" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/ivCategoryImage"
                                android:layout_alignParentBottom="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:lineSpacingExtra="-5dp"
                                android:text="@string/change_card"
                                android:textColor="@color/text_color"
                                android:textSize="@dimen/loyalty_card_label_size" />
                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="invisible"
                android:orientation="horizontal">

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/loyalty_dashboard_card_height"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginVertical="5dp"
                    android:id="@+id/middleMarketCard"
                    app:cardPreventCornerOverlap="true"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="btnClick"
                    app:cardBackgroundColor="@color/red2Tr">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:targetApi="m">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="25dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_shopping_bag"
                                app:tint="@color/red2" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/ivCategoryImage"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:lineSpacingExtra="-5dp"
                                android:text="@string/middle_market"
                                android:textColor="@color/text_color"
                                android:textSize="@dimen/loyalty_card_label_size" />
                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/loyalty_dashboard_card_height"
                    app:cardCornerRadius="6dp"
                    app:cardElevation="0dp"
                    android:layout_weight="1"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginVertical="5dp"
                    app:cardPreventCornerOverlap="true"
                    android:clickable="true"
                    android:focusable="true"
                    android:onClick="btnClick"
                    app:cardBackgroundColor="@color/blue2Tr">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        tools:targetApi="m">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:gravity="center"
                            android:orientation="vertical">

                            <ImageView
                                android:layout_width="45dp"
                                android:layout_height="45dp"
                                android:layout_alignParentTop="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginTop="25dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_card_change"
                                app:tint="@color/blue2" />

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_below="@+id/ivCategoryImage"
                                android:layout_alignParentBottom="true"
                                android:layout_centerHorizontal="true"
                                android:layout_marginStart="10dp"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="15dp"
                                android:gravity="center"
                                android:lineSpacingExtra="-5dp"
                                android:text="@string/loyalty_transactions"
                                android:textColor="@color/text_color"
                                android:textSize="@dimen/loyalty_card_label_size" />
                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </LinearLayout>
    </LinearLayout>

</layout>

