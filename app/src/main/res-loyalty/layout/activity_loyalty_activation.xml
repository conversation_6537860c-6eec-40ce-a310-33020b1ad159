<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.loyalty.viewmodel.LoyaltyActivationViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include layout="@layout/toolbar" android:id="@+id/toolbarLayout"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:id="@+id/scrollView"
                    android:fillViewport="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="fill_parent"
                        android:paddingStart="20dp"
                        android:paddingEnd="20dp"
                        android:paddingTop="10dp"
                        android:paddingBottom="220dp"
                        android:orientation="vertical">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.8"
                                android:textSize="16sp"
                                android:text="@string/card_number"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:paddingStart="16dp"
                                android:textSize="16sp"
                                android:textAlignment="textEnd"
                                android:id="@+id/tvPanNumber"
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                android:text=""/>

                        </LinearLayout>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.8"
                                android:textSize="16sp"
                                android:text="@string/nfc_tag"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:paddingStart="16dp"
                                android:textSize="16sp"
                                android:textAlignment="textEnd"
                                android:id="@+id/tvTagNumber"
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                android:textAllCaps="false"
                                android:text=""/>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="10dp">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1.4"
                                android:textSize="16sp"
                                android:text="@string/activation_station"/>

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:paddingStart="16dp"
                                android:textSize="16sp"
                                android:textAlignment="textEnd"
                                android:id="@+id/tvActivationStation"
                                android:textColor="@color/black"
                                android:textStyle="bold"
                                android:textAllCaps="false"
                                android:text=""/>

                        </LinearLayout>


                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:hint="@string/first_name"
                            android:layout_marginBottom="5dp">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="50"
                                android:inputType="text"
                                android:id="@+id/edittext_nom"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="@string/last_name">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="50"
                                android:inputType="text"
                                android:id="@+id/edittext_prenom"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="5dp"
                            android:gravity="center">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                                android:layout_marginEnd="5dp"
                                android:hint="Driver ID*">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:maxLines="1"
                                    android:maxLength="20"
                                    android:inputType="text"
                                    android:id="@+id/edittext_cin"/>

                            </com.google.android.material.textfield.TextInputLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:gravity="center"
                                android:orientation="horizontal">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btn_driver_id"
                                    style="@style/Widget.MaterialComponents.Button.TextButton"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginEnd="5dp"
                                    android:text="Upload*"
                                    android:onClick="onClick"
                                    app:icon="@drawable/ic_baseline_attach_file_24"
                                    app:iconGravity="textStart" />

                                <ImageView
                                    android:id="@+id/ivDriverID"
                                    android:layout_width="52dp"
                                    android:background="@drawable/ic_file"
                                    android:padding="2dp"
                                    android:layout_height="52dp"
                                    android:scaleType="fitXY"
                                    android:layout_alignParentEnd="true"
                                    android:visibility="visible"/>

                            </LinearLayout>

                        </LinearLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="Telephone*">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="15"
                                android:inputType="phone"
                                android:id="@+id/edittext_tel"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="Plate Number*">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="20"
                                android:inputType="text"
                                android:id="@+id/edittext_matricule"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <RelativeLayout android:layout_width="match_parent"
                            android:layout_height="60dp"
                            android:visibility="gone"
                            android:gravity="center">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:background="@drawable/formulaire_activation_fidelity">


                                <Spinner
                                    android:id="@+id/spinner_lettres"
                                    android:layout_width="60dp"
                                    android:layout_height="50dp"
                                   android:layout_marginEnd="20dp"
                                    android:padding="10dp"
                                    android:visibility="invisible" />

                                <EditText
                                    android:id="@+id/edittext_matricule_2"
                                    android:layout_width="115dp"
                                    android:layout_height="50dp"
                                    android:background="@android:color/transparent"
                                    android:hint="789"
                                    android:inputType="number"
                                    android:maxLength="3"
                                    android:padding="10dp"
                                    android:visibility="invisible"
                                    android:text=" "/>

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                 android:layout_marginStart="10dp"
                               android:layout_marginEnd="10dp"
                                android:layout_marginTop="-8dp"
                                android:background="#fff"
                                android:paddingStart="5dp"
                                android:paddingEnd="5dp"
                                android:text="Plate number *"
                                android:textColor="@color/colorPrimary" />

                        </RelativeLayout>

                        <RelativeLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:layout_marginBottom="5dp"
                                android:clickable="true"
                                android:gravity="center">

                                <com.google.android.material.textfield.TextInputLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_weight="1"
                                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                                    android:hint="Date of Birth">

                                    <com.google.android.material.textfield.TextInputEditText
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:maxLines="1"
                                        android:maxLength="20"
                                        android:inputType="text"
                                        android:enabled="false"
                                        android:textColor="@color/black"
                                        android:id="@+id/date_of_birth"/>

                                </com.google.android.material.textfield.TextInputLayout>

                                <ImageView
                                    android:layout_width="52dp"
                                    android:src="@drawable/ic_calendar"
                                    android:padding="10dp"
                                    android:layout_height="52dp"
                                    android:layout_marginStart="5dp"
                                    android:layout_alignParentEnd="true"
                                    android:visibility="visible"/>


                            </LinearLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="55dp"
                                android:layout_centerInParent="true"
                                android:clickable="true"
                                android:onClick="onClick"
                                android:id="@+id/dobLayout"
                                android:orientation="vertical" />

                        </RelativeLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="Place of Birth">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="50"
                                android:inputType="text"
                                android:id="@+id/edittext_lieu_naissance"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="Address">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="3"
                                android:maxLength="100"
                                android:inputType="textMultiLine"
                                android:id="@+id/edittext_adresse"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="City">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="50"
                                android:inputType="text"
                                android:id="@+id/edittext_ville"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:visibility="gone"
                            android:hint="Activation Station">
                            <!--style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                            <com.google.android.material.textfield.MaterialAutoCompleteTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:enabled="false"
                                android:id="@+id/spinner_station_activation" />-->

                            <!--<com.google.android.material.textfield.MaterialAutoCompleteTextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:inputType="none"
                                android:enabled="false"
                                android:id="@+id/spinner_station_activation" />-->

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="20"
                                android:inputType="text"
                                android:enabled="false"
                                android:textColor="@color/black"
                                android:id="@+id/etActivationStation"/>


                        </com.google.android.material.textfield.TextInputLayout>

                        <com.google.android.material.textfield.TextInputLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                            android:layout_marginBottom="5dp"
                            android:hint="License number*">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:maxLines="1"
                                android:maxLength="20"
                                android:inputType="text"
                                android:id="@+id/edittext_permis"/>

                        </com.google.android.material.textfield.TextInputLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="5dp"
                            android:orientation="horizontal">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                                android:layout_marginEnd="5dp"
                                android:hint="Category">

                                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="none"
                                    android:id="@+id/spinner_categorie" />

                            </com.google.android.material.textfield.TextInputLayout>

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu"
                                android:layout_marginStart="5dp"
                                android:hint="Fuel">

                                <com.google.android.material.textfield.MaterialAutoCompleteTextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="none"
                                    android:id="@+id/spinner_carburant" />

                            </com.google.android.material.textfield.TextInputLayout>


                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginEnd="5dp"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                                android:layout_marginBottom="5dp"
                                android:hint="Permit Number">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:maxLines="1"
                                    android:maxLength="20"
                                    android:inputType="text"
                                    android:id="@+id/edittext_argement"/>

                            </com.google.android.material.textfield.TextInputLayout>

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:layout_marginStart="5dp"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                                android:layout_marginBottom="5dp"
                                android:hint="Fiscal Horse Power">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:maxLines="1"
                                    android:maxLength="2"
                                    android:inputType="number"
                                    android:id="@+id/edittext_cv_fiscaux"/>

                            </com.google.android.material.textfield.TextInputLayout>
                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="5dp"
                            android:gravity="center">

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                                android:layout_marginEnd="5dp"
                                android:hint="Vehicle Reg. No.">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:maxLines="1"
                                    android:maxLength="20"
                                    android:inputType="text"
                                    android:id="@+id/edittext_carte_grise"/>

                            </com.google.android.material.textfield.TextInputLayout>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:orientation="horizontal">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btn_vehicle_reg_number"
                                    style="@style/Widget.MaterialComponents.Button.TextButton"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Upload"
                                    android:clickable="true"
                                    android:onClick="onClick"
                                    android:layout_marginEnd="5dp"
                                    app:icon="@drawable/ic_baseline_attach_file_24"
                                    app:iconGravity="textStart"/>

                                <ImageView
                                    android:id="@+id/img_carte_grise"
                                    android:layout_width="52dp"
                                    android:background="@drawable/ic_file"
                                    android:padding="2dp"
                                    android:layout_height="52dp"
                                    android:scaleType="fitXY"
                                    android:layout_alignParentEnd="true"
                                    android:visibility="visible"/>

                            </LinearLayout>

                        </LinearLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:layout_marginBottom="5dp"
                            android:gravity="center">

                            <TextView
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:textSize="16sp"
                                android:text="Signed Document*"/>

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:layout_weight="1"
                                android:orientation="horizontal">

                                <com.google.android.material.button.MaterialButton
                                    android:id="@+id/btn_signed_doc"
                                    style="@style/Widget.MaterialComponents.Button.TextButton"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="Upload*"
                                    android:clickable="true"
                                    android:onClick="onClick"
                                    android:layout_marginEnd="5dp"
                                    app:icon="@drawable/ic_baseline_attach_file_24"
                                    app:iconGravity="textStart"/>

                                <ImageView
                                    android:id="@+id/img_souche"
                                    android:layout_width="52dp"
                                    android:background="@drawable/ic_file"
                                    android:padding="2dp"
                                    android:layout_height="52dp"
                                    android:scaleType="fitXY"
                                    android:layout_alignParentEnd="true"
                                    android:visibility="visible"/>

                            </LinearLayout>

                        </LinearLayout>

                    </LinearLayout>

                </ScrollView>

                <RelativeLayout
                    android:id="@+id/bottom_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_alignParentBottom="true"
                    android:layout_weight="0"
                    android:orientation="vertical">

                    <Button
                        android:id="@+id/btnActivate"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_centerInParent="true"
                        android:layout_gravity="center"
                        android:layout_marginStart="10dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginEnd="10dp"
                        android:layout_marginBottom="10dp"
                        android:background="@drawable/round_valid_btn"
                        android:backgroundTint="@color/gold"
                        android:clickable="true"
                        android:onClick="onClick"
                        android:padding="10dp"
                        android:stateListAnimator="@null"
                        android:text="@string/activate"
                        android:textAllCaps="true"
                        android:textColor="#fff" />

                </RelativeLayout>

            </LinearLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/loadingLayout"
                android:gravity="center"
                android:visibility="gone"
                android:orientation="vertical"
                android:background="@color/white">

                <com.github.ybq.android.spinkit.SpinKitView
                    android:id="@+id/spin_kit"
                    style="@style/SpinKitView.Large.FadingCircle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="20sp"
                    app:SpinKit_Color="@color/colorAccent" />

                <TextView
                    android:text="@string/please_wait"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="15sp"
                    android:textAllCaps="true"
                    android:textColor="@color/black"/>

            </androidx.appcompat.widget.LinearLayoutCompat>




        </RelativeLayout>
    </LinearLayout>

</layout>

