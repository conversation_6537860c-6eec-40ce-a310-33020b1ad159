<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="model"
            type="app.rht.petrolcard.ui.loyalty.viewmodel.RedeemHistoryViewModel" />

        <import type="android.text.TextUtils"/>

        <import type="android.view.View"/>
    </data>

    <RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvGifts"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="5dp"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="2"
            tools:listitem="@layout/item_loyalty_gift_history" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/loadingLayout"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="vertical"
            android:background="@color/white">

            <com.github.ybq.android.spinkit.SpinKitView
                android:id="@+id/spin_kit"
                style="@style/SpinKitView.Large.FadingCircle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="20sp"
                app:SpinKit_Color="@color/colorAccent" />

            <TextView
                android:text="@string/please_wait"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:textAllCaps="true"
                android:textColor="@color/black"/>

        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/noDataLayout"
            android:gravity="center"
            android:visibility="gone"
            android:orientation="vertical"
            android:background="@color/white">

            <ImageView
                android:layout_width="180dp"
                android:layout_height="180dp"
                android:layout_gravity="center"
                android:layout_margin="20dp"
                android:src="@drawable/loyalty_gift_box"
                app:tint="@color/grey" />

            <TextView
                android:text="@string/not_found"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="15sp"
                android:textAllCaps="true"
                android:textColor="@color/grey"/>

        </androidx.appcompat.widget.LinearLayoutCompat>

    </RelativeLayout>

</layout>