<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.loyalty.model.ChildLoyaltyCardModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/transactionListLayout"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="10dp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="@color/recycler_view_bg"
        android:gravity="center">

        <RelativeLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:padding="3dp"
            android:gravity="center"
            android:orientation="horizontal">

            <RelativeLayout
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:id="@+id/cardIcon"
                android:background="@drawable/rounded_rectangle_small"
                android:backgroundTint="@color/lightGrey">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iconImage"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:src="@drawable/ic_gift_card" />

            </RelativeLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:layout_toStartOf="@+id/btnDelete"
                android:layout_toEndOf="@+id/cardIcon"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCardName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="5dp"
                    android:layout_weight="1"
                    android:maxLines="3"
                    android:text="@{item.cardHolder}"
                    android:textColor="@color/grey"
                    android:textSize="11sp"
                    android:textStyle="normal" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvCardNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="5dp"
                    android:layout_weight="1"
                    android:maxLines="3"
                    android:text="@{item.cardNumber}"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="normal" />

            </LinearLayout>

            <ImageView
                android:id="@+id/btnDelete"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:layout_alignParentEnd="true"
                android:layout_centerInParent="true"
                android:layout_margin="5dp"
                android:background="@drawable/rounded_rectangle_small"
                android:backgroundTint="@color/lightGrey"
                android:foreground="?attr/selectableItemBackgroundBorderless"
                android:padding="6dp"
                android:src="@drawable/ic_delete"
                android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
                app:tint="@color/red" />

        </RelativeLayout>

    </com.google.android.material.card.MaterialCardView>
</layout>