<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include layout="@layout/toolbar" android:id="@+id/toolbarLayout"/>
        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:gravity="center">


            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxHeight="150dp"
                android:maxWidth="150dp"
                android:minWidth="100dp"
                android:minHeight="100dp"
                android:src="@drawable/ic_loyalty_card_v2"
                android:id="@+id/ivCardImage"/>

            <TextView
                android:id="@+id/tvMessage"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/insert_card"
                android:textSize="20sp"
                android:visibility="gone"
                android:textColor="@color/black"
                android:layout_marginTop="10dp"/>

            <com.github.ybq.android.spinkit.SpinKitView
                android:id="@+id/spin_kit"
                style="@style/SpinKitView.Large.ThreeBounce"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:SpinKit_Color="@color/dark_black"
                android:layout_marginTop="20dp"/>

        </androidx.appcompat.widget.LinearLayoutCompat>
    </LinearLayout>

</layout>
