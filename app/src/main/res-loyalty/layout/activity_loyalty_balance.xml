<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.loyalty.viewmodel.LoyaltyBalanceViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include layout="@layout/toolbar" android:id="@+id/toolbarLayout"/>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_alignParentTop="true"
                android:id="@+id/loyaltyCardLayout"
                android:layout_marginBottom="10dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivLoyaltyCard"
                    android:layout_width="300dp"
                    android:layout_height="200dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_loyalty_card_bg" />

                <TextView
                    android:id="@+id/tvBalanceTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="30dp"
                    android:fontFamily="@font/card_font"
                    android:text="@string/loyalty_balance"
                    android:textAllCaps="true"
                    android:textColor="@color/gold"
                    app:layout_constraintStart_toStartOf="@id/ivLoyaltyCard"
                    app:layout_constraintTop_toTopOf="@id/ivLoyaltyCard" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginTop="5dp"
                    android:fontFamily="@font/card_font"
                    android:text="120"
                    android:id="@+id/tvPoint"
                    android:textAllCaps="true"
                    android:textColor="@color/gold"
                    android:textSize="16sp"
                    app:layout_constraintStart_toStartOf="@id/ivLoyaltyCard"
                    app:layout_constraintTop_toBottomOf="@id/tvBalanceTitle" />

                <TextView
                    android:id="@+id/tvCardHolderName"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginBottom="5dp"
                    android:fontFamily="@font/card_font"
                    android:text="@string/card_holder_name"
                    android:textAllCaps="true"
                    android:textColor="@color/gold"
                    app:layout_constraintBottom_toTopOf="@id/tvCardNumber"
                    app:layout_constraintStart_toStartOf="@id/ivLoyaltyCard" />

                <TextView
                    android:id="@+id/tvCardNumber"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="25dp"
                    android:layout_marginBottom="30dp"
                    android:fontFamily="@font/card_font"
                    android:text="1211 1221 1212 1212"
                    android:textAllCaps="true"
                    android:textColor="@color/gold"
                    android:textSize="12sp"
                    app:layout_constraintBottom_toBottomOf="@id/ivLoyaltyCard"
                    app:layout_constraintStart_toStartOf="@id/ivLoyaltyCard" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_below="@+id/loyaltyCardLayout">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                    <include layout="@layout/tabs_loyalty_balance" />
                </LinearLayout>

                <app.rht.petrolcard.utils.NonSwipeableViewPager
                    android:id="@+id/viewPager"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

            </LinearLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/loadingLayout"
                android:gravity="center"
                android:visibility="gone"
                android:orientation="vertical"
                android:background="@color/white">

                <com.github.ybq.android.spinkit.SpinKitView
                    android:id="@+id/spin_kit"
                    style="@style/SpinKitView.Large.FadingCircle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_margin="20sp"
                    app:SpinKit_Color="@color/colorAccent" />

                <TextView
                    android:text="@string/please_wait"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="15sp"
                    android:textAllCaps="true"
                    android:textColor="@color/black"/>

            </androidx.appcompat.widget.LinearLayoutCompat>

        </RelativeLayout>
    </LinearLayout>

</layout>

