<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.loyalty.model.LoyaltyGift" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <androidx.cardview.widget.CardView
        xmlns:card_view="http://schemas.android.com/apk/res-auto"
        android:id="@+id/card_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        card_view:cardElevation="5dp"
        android:outlineAmbientShadowColor="@color/colorPrimary"
        card_view:cardUseCompatPadding="true"
        card_view:cardCornerRadius="9dp"
        tools:targetApi="p">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivGiftImage"
                setNormalImage="@{item.picture}"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:background="@{item.color}" />


            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/ivGiftImage"
                android:background="@color/lightGrey"
                android:padding="10dp">

                <LinearLayout
                    android:id="@+id/giftLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="start"
                    android:orientation="vertical">

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Points:"
                            android:textAllCaps="true"
                            android:textColor="@color/black"
                            android:textSize="11sp" />
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@{item.point.toString()}"
                            android:textAllCaps="true"
                            android:textColor="@color/black"
                            android:textSize="11sp" />
                    </LinearLayout>

                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:singleLine="true"
                        android:text="@{item.name.toString()}"
                        android:textColor="@color/black"
                        android:textSize="13sp"
                        android:textStyle="bold" />



                </LinearLayout>

                <TextView
                    android:id="@+id/ivBtnRedeem"
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:layout_below="@+id/giftLayout"
                    android:layout_alignParentEnd="true"
                    android:layout_marginTop="5dp"
                    android:background="@drawable/rounded_rectangle_button"
                    android:backgroundTint="@color/greenLight"
                    android:clickable="true"
                    android:focusable="true"
                    android:foreground="?attr/selectableItemBackgroundBorderless"
                    android:gravity="center"
                    android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
                    android:text="@string/redeem"
                    android:textColor="@color/white" />

            </RelativeLayout>

            <ImageView
                android:id="@+id/ivGiftBadge"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:layout_below="@+id/ivGiftImage"
                android:layout_alignParentEnd="true"
                android:layout_marginTop="-20dp"
                android:layout_marginEnd="15dp"
                android:background="@drawable/circle_with_stroke"
                android:padding="10dp"
                android:src="@drawable/ic_gift_badge" />





        </RelativeLayout>

    </androidx.cardview.widget.CardView>

</layout>