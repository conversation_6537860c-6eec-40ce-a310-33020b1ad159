<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".ui.loyalty.activity.CardChangeActivity">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.loyalty.viewmodel.CardChangeViewModel" />
    </data>

    <RelativeLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">
        <include
            layout="@layout/toolbar"
            android:id="@+id/toolbarLayout"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_alignParentTop="true" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbarLayout"
            android:orientation="vertical"
            android:padding="10dp">


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.8"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:layout_marginBottom="5dp"
                android:text="@string/driver_details"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.8"
                    android:textSize="14sp"
                    android:text="@string/card_number"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:textSize="14sp"
                    android:textAlignment="textEnd"
                    android:id="@+id/tvCardNumber"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:text=""/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.8"
                    android:textSize="14sp"
                    android:text="@string/first_name_optional_label"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:textSize="14sp"
                    android:textAlignment="textEnd"
                    android:id="@+id/tvFirstName"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:text=""/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.8"
                    android:textSize="14sp"
                    android:text="@string/last_name_optional_label"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:textSize="14sp"
                    android:textAlignment="textEnd"
                    android:id="@+id/tvLastName"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:text=""/>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="10dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.8"
                    android:textSize="14sp"
                    android:text="@string/telephone"/>

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:paddingStart="16dp"
                    android:textSize="14sp"
                    android:textAlignment="textEnd"
                    android:id="@+id/tvTel"
                    android:textColor="@color/black"
                    android:textStyle="bold"
                    android:text=""/>

            </LinearLayout>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.8"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="5dp"
                android:text="@string/new_card_details"/>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:id="@+id/btnAddNewCard"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:layout_margin="10dp"
                    android:background="@drawable/formulaire_activation_fidelity"
                    android:backgroundTint="@color/lightGrey"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center"
                    android:onClick="onClick"
                    android:orientation="vertical">

                    <ImageView
                        android:layout_width="42dp"
                        android:layout_height="42dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/ic_card_change"
                        app:tint="@color/colorPrimary" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/add_new_card"
                        android:textColor="@color/colorPrimary"
                        android:textSize="13sp" />

                </LinearLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/newLoyaltyCard"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="6dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    android:layout_margin="10dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <RelativeLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:id="@+id/ivCard"
                            android:layout_margin="10dp">

                            <androidx.appcompat.widget.AppCompatImageView
                                android:id="@+id/hose"
                                android:layout_width="80dp"
                                android:layout_height="match_parent"
                                android:layout_gravity="center"
                                android:src="@drawable/rounded_rectangle_button"
                                android:tint="@color/gold" />

                            <ImageView
                                android:layout_width="50dp"
                                android:layout_height="50dp"
                                android:layout_centerInParent="true"
                                android:layout_marginStart="5dp"
                                android:layout_marginTop="5dp"
                                android:layout_marginEnd="5dp"
                                android:layout_marginBottom="5dp"
                                android:src="@drawable/ic_card"
                                app:tint="@color/white" />

                        </RelativeLayout>

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_toEndOf="@+id/ivCard"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:paddingVertical="10dp">

                            <TextView
                                android:id="@+id/tvMasterLoyaltyCard"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_alignParentTop="true"
                                android:layout_marginEnd="10dp"
                                android:layout_marginBottom="5dp"
                                android:paddingHorizontal="5dp"
                                android:text="@string/new_loyalty_card"
                                android:textAllCaps="true"
                                android:textColor="@color/grey"
                                android:textSize="12sp" />

                            <TextView
                                android:id="@+id/tvNewCardHolder"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginEnd="10dp"
                                android:paddingHorizontal="5dp"
                                android:text="XX XX XX"
                                android:textAllCaps="true"
                                android:visibility="gone"
                                android:textColor="@color/black"
                                android:textSize="13sp"
                                android:textStyle="bold" />

                            <TextView
                                android:id="@+id/tvNewCardNumber"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_centerHorizontal="true"
                                android:layout_centerVertical="true"
                                android:layout_marginEnd="10dp"
                                android:paddingHorizontal="5dp"
                                android:text="0000 0000 0000 0000"
                                android:textAllCaps="true"
                                android:textColor="@color/black"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                        </LinearLayout>

                    </RelativeLayout>

                </com.google.android.material.card.MaterialCardView>

            </RelativeLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1.8"
                android:textSize="14sp"
                android:textColor="@color/black"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="5dp"
                android:text="@string/required_documents"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="5dp"
                android:gravity="center">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.8"
                    android:textSize="14sp"
                    android:text="@string/driver_id_lebel"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center|end"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_driver_id"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:text="Upload*"
                        android:onClick="onClick"
                        app:icon="@drawable/ic_baseline_attach_file_24"
                        app:iconGravity="textStart" />

                    <ImageView
                        android:id="@+id/ivDriverID"
                        android:layout_width="52dp"
                        android:background="@drawable/ic_file"
                        android:padding="2dp"
                        android:layout_height="52dp"
                        android:scaleType="fitXY"
                        android:layout_alignParentEnd="true"
                        android:visibility="visible"/>

                </LinearLayout>

            </LinearLayout>
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="5dp"
                android:gravity="center">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.8"
                    android:textSize="14sp"
                    android:text="@string/vehicle_registration"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center|end"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/btn_vehicle_reg"
                        style="@style/Widget.MaterialComponents.Button.TextButton"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="5dp"
                        android:text="Upload*"
                        android:onClick="onClick"
                        app:icon="@drawable/ic_baseline_attach_file_24"
                        app:iconGravity="textStart" />

                    <ImageView
                        android:id="@+id/ivVehicleReg"
                        android:layout_width="52dp"
                        android:background="@drawable/ic_file"
                        android:padding="2dp"
                        android:layout_height="52dp"
                        android:scaleType="fitXY"
                        android:layout_alignParentEnd="true"
                        android:visibility="visible"/>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <Button
            android:id="@+id/btnActivate"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerInParent="true"
            android:layout_gravity="center"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:layout_marginBottom="10dp"
            android:background="@drawable/round_valid_btn"
            android:backgroundTint="@color/gold"
            android:clickable="true"
            android:onClick="onClick"
            android:padding="10dp"
            android:stateListAnimator="@null"
            android:text="@string/submit"
            android:textAllCaps="true"
            android:textColor="#fff" />

    </RelativeLayout>




</layout>