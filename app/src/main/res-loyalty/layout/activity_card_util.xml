<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

  <data>
    <variable
        name="model"
        type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
  </data>
<LinearLayout
    android:id="@+id/activity_etat"
    android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_gravity="center"
    android:gravity="center"
    android:layout_height="match_parent"
   >

 <androidx.appcompat.widget.AppCompatButton
     android:id="@+id/updateCardType"
     android:layout_width="wrap_content"
     android:text="Update Card Type"
     android:layout_height="wrap_content">

 </androidx.appcompat.widget.AppCompatButton>

</LinearLayout>

</layout>