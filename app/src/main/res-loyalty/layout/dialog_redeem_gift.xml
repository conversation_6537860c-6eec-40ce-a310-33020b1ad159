<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:custom="http://schemas.android.com/apk/res-auto"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/FrameLayout_congrats"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:padding="10dp"
    android:background="@drawable/formulaire_activation_fidelity"
    android:backgroundTint="@color/colorWhite">


    <com.airbnb.lottie.LottieAnimationView
        android:id="@+id/imageView1"
        android:layout_width="320dp"
        android:layout_height="320dp"
        android:layout_marginTop="-100dp"
        android:layout_marginBottom="-70dp"
        android:visibility="visible"
        app:lottie_rawRes="@raw/gift"
        app:lottie_repeatMode="restart"
        app:lottie_enableMergePathsForKitKatAndAbove="true"
        app:lottie_loop="true"
        app:lottie_speed="1.5"
        android:layout_gravity="center"
        app:lottie_autoPlay="true"
        android:layout_centerHorizontal="true"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_below="@+id/imageView1"
            android:gravity="center">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="5dp"
                android:text="@string/confirm"
                android:textColor="@color/colorPrimary"
                android:textSize="20sp"
                android:textAllCaps="true"
                android:textStyle="bold" />
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/please_confirm_to_redeem_your_gift"
                android:textColor="#1d1d26"
                android:textSize="14sp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:weightSum="1"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_gravity="center">

                <TextView
                    android:id="@+id/myTextViewKo"
                    android:layout_width="0dp"
                    android:layout_weight="0.4"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:padding="10dp"
                    android:layout_margin="10dp"
                    android:layout_gravity="center"
                    android:text="@string/cancel"
                    android:textColor="@color/colorWhite"
                    android:background="@drawable/round_valid_btn"
                    android:backgroundTint="@color/redLight"
                    android:textSize="16sp"
                    />
                <TextView
                    android:id="@+id/myTextViewOk"
                    android:layout_width="0dp"
                    android:layout_weight="0.4"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:gravity="center"
                    android:padding="10dp"
                    android:layout_gravity="center"
                    android:text="@string/redeem"
                    android:textColor="@color/colorWhite"
                    android:background="@drawable/round_valid_btn"
                    android:backgroundTint="@color/colorPrimary"
                    android:textSize="16sp"
                    />

            </LinearLayout>

        </LinearLayout>

</RelativeLayout>