<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
<LinearLayout
    android:id="@+id/activity_etat"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.badge.activity.BadgeActivity">
    <include
        android:id="@+id/toolbarBadge"
        layout="@layout/toolbar" />
    <LinearLayout
        android:id="@+id/promptLayout"
        android:layout_marginBottom="10dp"
        android:gravity="center"
        android:layout_marginTop="@dimen/_10sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
      >

        <TextView
            android:text="@string/scan_the_station_manager_tag"
            android:layout_width="wrap_content"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/_12sdp"
            android:layout_height="wrap_content"
            android:textStyle="bold"
            android:id="@+id/prompt" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/insertCardImageLayout"
        android:layout_below="@+id/abortMessageLayout"
        android:gravity="center"
        android:layout_marginBottom="10dp"
        android:padding="10dp"
        android:layout_margin="@dimen/_40sdp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/insertCardImageView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:adjustViewBounds="false"
            android:cropToPadding="false"
            android:src="@drawable/nfc_logo_manager" />
    </LinearLayout>

</LinearLayout>
</layout>
