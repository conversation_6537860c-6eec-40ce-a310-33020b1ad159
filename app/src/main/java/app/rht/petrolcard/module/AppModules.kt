package app.rht.petrolcard.module

import android.content.Context
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.AppPreferencesHelper
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module


val appModule = module {
    single {
        val PREF_NAME = AppConstant.PREF_NAME
        val MODE = Context.MODE_PRIVATE
        androidContext().getSharedPreferences(PREF_NAME, MODE)
    }
    single {
        AppPreferencesHelper(get())
    }

}