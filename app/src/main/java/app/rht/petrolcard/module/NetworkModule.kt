package app.rht.petrolcard.module

import android.content.Context
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Build
import android.util.Log
import androidx.annotation.RequiresApi
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.utils.AppPreferencesHelper
import com.datatheorem.android.trustkit.TrustKit
import com.datatheorem.android.trustkit.pinning.OkHttp2Helper
import com.datatheorem.android.trustkit.pinning.OkHttp3Helper
import com.google.gson.GsonBuilder
import com.readystatesoftware.chuck.ChuckInterceptor
import okhttp3.*
import okhttp3.logging.HttpLoggingInterceptor
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module
import retrofit2.Retrofit
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.io.IOException
import java.net.InetAddress
import java.util.concurrent.TimeUnit


val networkModule = module {
    single {
        val pref: AppPreferencesHelper = get()
        if(pref.baseUrl.isEmpty())
        {
            pref.baseUrl= NetworkRequestEndPoints.BASE_URL
        }
        Log.i("NETWORK MODULE 1", pref.baseUrl)
        val fac = GsonConverterFactory.create(GsonBuilder().setLenient().create())
        Retrofit.Builder()
            .client(get())
            .baseUrl(pref.baseUrl)
            .addConverterFactory(fac)
            .addConverterFactory(ScalarsConverterFactory.create())
            .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
            .build()
    }
    single {

        val pref: AppPreferencesHelper = get()
        Log.i("NETWORK MODULE 2", pref.baseUrl)

        val logging = HttpLoggingInterceptor()
        logging.level = HttpLoggingInterceptor.Level.BODY

        TrustKit.initializeWithNetworkSecurityConfiguration(androidContext().applicationContext)

       val builder= OkHttpClient.Builder()
          //  .sslSocketFactory(OkHttp3Helper.getSSLSocketFactory(), OkHttp3Helper.getTrustManager())
        builder.addInterceptor(OkHttp3Helper.getPinningInterceptor())
        builder.followRedirects(true)
        builder.followSslRedirects(false)
        if(BuildConfig.DEBUG) {
            builder.addInterceptor(ChuckInterceptor(androidContext().applicationContext).showNotification(true))
        }
        builder.addInterceptor(logging)
        builder.readTimeout(60, TimeUnit.SECONDS)
        builder.connectTimeout(30, TimeUnit.SECONDS)
           /* .addInterceptor(ConnectivityInterceptor(androidContext().applicationContext))
            .addInterceptor { chain ->
                val pref: AppPreferencesHelper = get()
                val request = chain.request().newBuilder()
                    .addHeader("version", BuildConfig.VERSION_NAME)
                    .build()
                chain.proceed(request)
            }*/
        builder.build()
    }

    single {
        val ret: Retrofit = get()
        ret.create(ApiService::class.java)
    }
}

class ConnectivityInterceptor(private val context: Context) : Interceptor {
    @RequiresApi(Build.VERSION_CODES.M)
    override fun intercept(chain: Interceptor.Chain): Response {
        Log.i("NETWORK MODULE 2", chain.request().url.host)
        if(Build.VERSION.SDK_INT < Build.VERSION_CODES.M)
        {
            if (!isInternetAvailable()) {
                throw NoConnectivityException()
            }
        }
        else
        {
            if (!isOnline(context)) {
                throw NoConnectivityException()
            }
        }

        val builder: Request.Builder = chain.request().newBuilder()
        return chain.proceed(builder.build())
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun isOnline(context: Context): Boolean {
        val connectivityManager =
            context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val network = connectivityManager.activeNetwork ?: return false
        val networkCapabilities =
            connectivityManager.getNetworkCapabilities(network) ?: return false
        return when {
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) -> true
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) -> true
            //for other device how are able to connect with Ethernet
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET) -> true
            //for check internet over Bluetooth
            networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH) -> true
            else -> false
        }
    }

    private fun isInternetAvailable(): Boolean {
        return try {
            val ipAddr: InetAddress = InetAddress.getByName("www.google.com")
            //You can replace it with your name
            !ipAddr.equals("")
        } catch (e: Exception) {
            false
        }
    }

}
class NoConnectivityException : IOException() {
    override val message: String?
        get() = "No Internet Connection - Try Again"
}