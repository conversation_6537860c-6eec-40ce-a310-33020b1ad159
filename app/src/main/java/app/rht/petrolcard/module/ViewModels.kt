package app.rht.petrolcard.module


import app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel
import app.rht.petrolcard.ui.loyalty.viewmodel.*
import app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel
import app.rht.petrolcard.ui.modepay.viewmodel.ModePayViewmodel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.reference.viewmodel.ReferenceViewModel
import app.rht.petrolcard.ui.settings.card.changepin.viewmodel.ChangePinViewModel
import app.rht.petrolcard.ui.settings.card.recharge.viewmodel.RechargeViewModel
import app.rht.petrolcard.ui.settings.card.unblockcard.viewmodel.UnblockViewModel
import app.rht.petrolcard.ui.settings.card.unlockpin.viewmodel.UnlockViewModel
import app.rht.petrolcard.ui.settings.common.viewmodel.SettingsViewModel
import app.rht.petrolcard.ui.settings.maintenance.viewmodel.MaintenanceViewModel
import app.rht.petrolcard.ui.settings.terminal_settings.viewmodel.TerminalSettingsViewModel
import app.rht.petrolcard.ui.startup.viewmodel.StartupViewModel
import app.rht.petrolcard.ui.ticket.viewmodel.LoyaltyDialogViewModel


import org.koin.androidx.viewmodel.dsl.viewModel
import org.koin.dsl.module


val myViewModel = module {

   viewModel { CommonViewModel(get(),get()) }

   viewModel { PaymentViewModel(get(),get()) }
   viewModel { ReferenceViewModel(get(),get()) }
   viewModel { MenuViewModel(get(),get()) }
   viewModel { StartupViewModel(get(),get()) }
   viewModel { LoyaltyDialogViewModel(get(),get()) }
   viewModel { LoyaltyBalanceViewModel(get(),get()) }
   viewModel { AvailableGiftsViewModel(get(),get()) }
   viewModel { RedeemHistoryViewModel(get(),get()) }
   viewModel { LoyaltyActivationViewModel(get(),get()) }
   viewModel { RechargeViewModel(get(),get()) }
   viewModel { UnblockViewModel(get(),get()) }
   viewModel { UnlockViewModel(get(),get()) }
   viewModel { GiftRedeemViewModel(get(),get()) }
   viewModel { MiddleMarketViewModel(get(),get()) }
   viewModel { CardChangeViewModel(get(),get()) }
   viewModel { SettingsViewModel(get(),get()) }
   viewModel { ChangePinViewModel(get(),get()) }
   viewModel { MaintenanceViewModel(get(),get()) }
   viewModel { ModePayViewmodel(get(),get()) }
   viewModel { TerminalSettingsViewModel(get(),get()) }
}