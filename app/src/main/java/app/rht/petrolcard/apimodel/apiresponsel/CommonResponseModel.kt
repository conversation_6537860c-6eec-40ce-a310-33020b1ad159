package app.rht.petrolcard.apimodel.apiresponsel

import androidx.annotation.Keep

@Keep
data class CommonResponseModel(
    val message:String? = "",val status: Int=1
)

@Keep
data class SuccessResponseModel(
    val data:String = "",val status: Int=1
)

@Keep
data class DialogSuccessModel(
    val mClass: Class<*>? = null,
    val arguments: HashMap<String, Any?>? = null,
    val isTopFinish: Boolean = false,
    val flagIsTopClearTask: Boolean = false,
    val goToBackScreen: Boolean = false,
    val title: String? = null,
    val message: String? = null
)

@Keep
data class RedirectModel(
    val mClass: Class<*>,
    val arguments: HashMap<String, Any?>? = null,
    val isTopFinish: Boolean = false,
    val flagIsTopClearTask: Boolean = false
)