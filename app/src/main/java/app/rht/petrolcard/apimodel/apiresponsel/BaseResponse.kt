package app.rht.petrolcard.apimodel.apiresponsel

import androidx.annotation.Keep
import app.rht.petrolcard.networkRequest.ApiService
import com.google.gson.annotations.SerializedName

@Keep
data class BaseResponse<T>(
    val contenu: T ?= null,
    val error: String?,
    @SerializedName("response", alternate = ["reponse"])
    val reponse:String ?= null
)

@Keep
data class ErrorData(
    val message:String ?= "",
    val statusCode:Int = 0,
    val priority:Int = ApiService.PRIORITY_DEFAULT
)