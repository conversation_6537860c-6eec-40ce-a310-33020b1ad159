package app.rht.petrolcard.service

import android.app.ActivityManager
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import androidx.multidex.MultiDexApplication
import app.rht.petrolcard.ui.startup.activity.SplashScreenActivity
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Events

class RestartApplicationReceiver: BroadcastReceiver() {
    override fun onReceive(context: Context, intent: Intent) {
//        if (intent.action == Events.RESUME_ACTION) {
//            stopAutoRestartTimer()
//        }
//        if (intent.action == Events.STOP_ACTION) {
//            if(isAppBackround())
//            {
//                if(MainApp.autoRestartTimer == null)
//                {
//                    startTimerToAutoRun(context)
//                }
//                else
//                {
//                    MainApp.autoRestartTimer!!.start()
//                }
//            }
//        }
        if (intent.action == Events.DESTROY_ACTION) {
            if(isAppBackround()) {
                restartActivity(context)
            }
        }
//        if (intent.action == Events.START_ACTION) {
//            stopAutoRestartTimer()
//        }

    }

    fun isAppBackround():Boolean
    {
        val myProcess = ActivityManager.RunningAppProcessInfo()
        ActivityManager.getMyMemoryState(myProcess)
        val isInBackground = myProcess.importance != ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
        return isInBackground
    }

     fun restartActivity(context: Context) {
         val pref = AppPreferencesHelper(context.getSharedPreferences(AppConstant.PREF_NAME, MultiDexApplication.MODE_PRIVATE))
         if(pref.isCurrentActivityisMenu)
         {
             val intent2 = Intent(context, SplashScreenActivity::class.java)
             intent2.flags = Intent.FLAG_ACTIVITY_NEW_TASK

             context.startActivity(intent2)
         }

    }
}