package app.rht.petrolcard.service;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import app.rht.petrolcard.ui.menu.activity.MenuActivity;
import app.rht.petrolcard.ui.startup.activity.SplashScreenActivity;


public class BootReceiver extends BroadcastReceiver{
    @Override
    public void onReceive(Context context, Intent intent) {

        Log.d("ysh", "onReceive: ");
        if(intent.getAction().equals("android.intent.action.BOOT_COMPLETED")){
            Log.d("ysh", "BOOT_COMPLETED: ");
            Intent intent2 = new Intent(context, SplashScreenActivity.class);
            intent2.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent2);
        }
    }
}
