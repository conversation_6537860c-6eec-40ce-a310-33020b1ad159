package app.rht.petrolcard.service.scheduleTeleCollect

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.net.Uri
import android.os.*
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.content.ContextCompat
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.ui.iccpayment.model.PhotoModel
import app.rht.petrolcard.ui.menu.model.TeleCollectFormatModel
import app.rht.petrolcard.ui.menu.model.TelecollectDataModel
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.startup.activity.SplashScreenActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.LocaleManager
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.fuelpos.FuelPosService
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.sqlcipher.SQLException
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.File
import java.io.IOException
import java.lang.ref.WeakReference
import java.security.NoSuchAlgorithmException
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.system.exitProcess


class ScheduledTeleCollectService : Service() {

    companion object {
        private val TAG = ScheduledTeleCollectService::class.simpleName
        private const val ACTION_START_FOREGROUND_SERVICE = "ACTION_START_FOREGROUND_SERVICE"
        private const val ACTION_STOP_FOREGROUND_SERVICE = "ACTION_STOP_FOREGROUND_SERVICE"
        const val ACTION_START_TELECOLLECT_TIMER = "ACTION_START_TELECOLLECT_TIMER"
        const val ACTION_STOP_TELECOLLECT_TIMER = "ACTION_STOP_TELECOLLECT_TIMER"
        const val ACTION_SCHEDULE_TELECOLLECT_TIME = "ACTION_SCHEDULE_TELECOLLECT_TIME"
        const val ACTION_SETTINGS_SCHEDULE_TELECOLLECT_TIME = "ACTION_SETTINGS_SCHEDULE_TELECOLLECT_TIME"
        const val ACTION_START_TELECOLLECT_SETTINGS = "ACTION_START_TELECOLLECT_SETTINGS"
        const val ACTION_START_TELECOLLECT_MENU = "ACTION_START_TELECOLLECT_MENU"
        const val ACTION_START_REFERENCING = "ACTION_START_REFERENCING"
        const val ACTION_REFERENCING_DONE = "ACTION_REFERENCING_DONE"
        const val ACTION_SAVE_DATA_IN_DB = "ACTION_SAVE_DATA_IN_DB"
        private lateinit var broadcastThread : Thread
        private val logWriter = LogWriter("ScheduleTeleCollectService")
        private var screenTimer : CountDownTimer? = null
        private var referenceModel : ReferenceModel? = null

        var isReferencingRequest = false

        @JvmStatic fun start(context: Context){
            val ctx = WeakReference(context).get()!!
            val mainApp = MainApp.appContext as MainApp
            mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME)

            val mIntent = Intent(ctx, ScheduledTeleCollectService::class.java)
            mIntent.action = ACTION_START_FOREGROUND_SERVICE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(ctx,mIntent)
            } else {
                ctx.startService(mIntent)
            }
        }
        @JvmStatic fun saveDataInDB(context: Context, referenceModel: ReferenceModel){
            val ctx = WeakReference(context).get()!!
            val jsonData = Gson().toJson(referenceModel)

            val intent = Intent(context, ScheduledTeleCollectService::class.java)
            intent.action = ACTION_SAVE_DATA_IN_DB
            intent.putExtra("REFERENCE_JSON",jsonData)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(ctx,intent)
            } else {
                ctx.startService(intent)
            }
        }

        @JvmStatic fun stop(context: Context) {
            val ctx = WeakReference(context).get()!!
            val intent = Intent(context, ScheduledTeleCollectService::class.java)
            intent.action = ACTION_STOP_FOREGROUND_SERVICE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(ctx,intent)
                //context.startForegroundService(intent)
            } else {
                ctx.startService(intent)
            }
        }
        @JvmStatic fun isRunning(context: Context): Boolean {
            val ctx = WeakReference(context).get()!!
            val activityManager = ctx.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            for (service in activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (ScheduledTeleCollectService::class.java.name == service.service.className) {
                    logWriter.appendLog(TAG, "RUNNING " + ScheduledTeleCollectService::class.java.simpleName)
                    return true
                }
            }
            return false
        }

        fun checkTransactionAvailable() {
            try {
                val mTransactionTaxiDAO = TransactionDao()
                mTransactionTaxiDAO.open()
                val mTransactionList = mTransactionTaxiDAO.getRebateTransactions(2)
                if(mTransactionList.isNotEmpty())
                {
                    sendTransactionOnline(SendTransactionModel(mTransactionList,MainApp.sn))
                }
                mTransactionTaxiDAO.close()
                Log.e(TAG,Gson().toJson(mTransactionList))
            } catch (ex: SQLiteException) {
                ex.printStackTrace()
            }
        }
        fun sendTransactionOnline(sendTransactionModel: SendTransactionModel) {
            MainApp.longApiService.sendTransactionOnline( url = MainApp.getPrefs().baseUrl+ NetworkRequestEndPoints.SEND_TRANSACTION_ONLINE,
                mTaxis = sendTransactionModel)
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe ({
                    if (it != null && it.reponse != "0") {
                        try {
                            val mTransactionTaxiDAO = TransactionDao()
                            mTransactionTaxiDAO.open()
                            for(trx in sendTransactionModel.mTaxis)
                            {
                                trx.flagTelecollecte = 1
                                mTransactionTaxiDAO.updateTransactionsByReferenceID(trx)
                                Log.e(TAG, Gson().toJson(trx))
                            }
                            mTransactionTaxiDAO.close()

                        } catch (ex: SQLiteException) {
                            ex.printStackTrace()
                        }
                    }
                },
                    {
                        it.printStackTrace()
                    })
        }
    }

    private var prefs : AppPreferencesHelper = MainApp.getPrefs()
    override fun onCreate() {
        super.onCreate()
        startForegroundNotification()
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try{
            logWriter.appendLog(TAG,"ON START COMMAND: ${intent!!.action} -- $flags -- $startId")
        } catch (e:java.lang.Exception){
            e.printStackTrace()
        }
        if(intent != null) {
            when (intent.action) {
                ACTION_START_FOREGROUND_SERVICE -> {
                    startForegroundNotification()
                    startCountDownTimer()    // starting timer for first time
                }
                ACTION_START_TELECOLLECT_TIMER -> {
                    startCountDownTimer()
                }
                ACTION_STOP_TELECOLLECT_TIMER -> {
                    stopCountDownTimer()
                }
                ACTION_STOP_FOREGROUND_SERVICE -> {
                    stopForegroundService()
                }
                ACTION_START_TELECOLLECT_MENU -> {
                    isFromSettings = false
                    val referenceValue = "TLC" + Support.generateReference()
                    val scheduleTask = ScheduledTeleCollectTask(referenceValue)
                    scheduleTask.execute()
                }
                ACTION_START_TELECOLLECT_SETTINGS -> {
                    isFromSettings = true
                    val referenceValue = "TLC" + Support.generateReference()
                    val scheduleTask = ScheduledTeleCollectTask(referenceValue,)
                    scheduleTask.execute()
                }
                ACTION_START_REFERENCING -> {
                    isFromSettings = false
                    val referenceValue = "TLC" + Support.generateReference()
                    val scheduleTask = ScheduledTeleCollectTask(referenceValue)
                    scheduleTask.execute()
                }
                ACTION_SAVE_DATA_IN_DB -> {
                   try{
                       logWriter.appendLog(TAG,"Saving telecollect data in db")
                       val jsonString = intent.getStringExtra("REFERENCE_JSON")
                        if(jsonString!=null){
                            val it = Gson().fromJson(jsonString, ReferenceModel::class.java)
                            saveDataInDB(it)
                        } else {
                            logWriter.appendLog(TAG,"NULL data received in ACTION_SAVE_DATA_IN_DB intent")
                        }
                   } catch (e: Exception){
                       e.printStackTrace()
                   }
                }
            }
        }

        return START_NOT_STICKY
    }


    //region foreground notification
    private val CHANNEL_ID = "scheduledTeleCollectService"
    private val CHANNEL_NAME = "Scheduled TeleCollect Service"
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(): String {
        val channel = NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_DEFAULT)
        channel.lightColor = Color.BLUE
        channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val service = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        service.createNotificationChannel(channel)
        return CHANNEL_ID
    }
    private fun startForegroundNotification() {

        /*val intent = Intent(this, MenuActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, 0)

        val defaultSoundUri = Uri.parse("android.resource://app.rht.petrolcard/" + R.raw.connect)

        val channelId =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel()
            } else { "" }

        val notificationBuilder = NotificationCompat.Builder(this, channelId )

        val notification = notificationBuilder.setOngoing(true)
            .setSmallIcon(android.R.drawable.ic_menu_upload)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setOnlyAlertOnce(true)
            //.setCategory(Notification.CATEGORY_SERVICE)
            .setContentTitle(MainApp.appContext.getString(R.string.app_name))
            .setContentText("Service Running")
            .setSound(defaultSoundUri)
            .setSilent(true)
            .setFullScreenIntent(pendingIntent,true)
            .build()

        startForeground(101, notification)*/
    }
    private fun stopForegroundService() {
        stopForeground(true)
        stopSelf()
    }
    //endregion


    override fun onDestroy() {
        stopCountDownTimer()
        logWriter.appendLog(TAG, "On Destroy Called")
        try{broadcastThread.interrupt()} catch (e:Exception) {}
        super.onDestroy()
    }

    var minutes = 0L
    var seconds = 0L
    fun startCountDownTimer() {
        try {
            if(screenTimer != null)
            {
                stopCountDownTimer()
            }
            val referenceModel=prefs.getReferenceModel()
            var startTime = TimeUnit.MINUTES.toMillis(if(referenceModel != null && referenceModel.TELECOLLECT_TIME != null)referenceModel.TELECOLLECT_TIME else 15)
            Log.i(TAG, "startTime:: $startTime")

//            if(BuildConfig.DEBUG)
//                startTime = 60000L  /// 1 minutes in debug mode

          //  if(screenTimer == null){
                screenTimer = object : CountDownTimer(startTime, 1000) {
                    override fun onTick(millisUntilFinished: Long) {
                        minutes = millisUntilFinished / 1000 / 60
                        seconds = (millisUntilFinished / 1000 % 60)
                        if(BuildConfig.DEBUG)
                        {
                            Log.e(TAG, "Time Remaining: $minutes:$seconds for schedule tele collect")
                        }
                    }
                    override fun onFinish() {
                        this.start()
                        isReferencingRequest = false
                        if(prefs.isCurrentActivityisMenu)
                        {
                            val referenceValue = "TLC" + Support.generateReference()
                            val scheduleTask = ScheduledTeleCollectTask(
                                referenceValue,
                                isScheduleTelecollect = true
                            )
                            scheduleTask.execute()
                        }

                    }
                }
          //  } else {
                screenTimer!!.cancel()
                screenTimer!!.start()
          //  }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    fun stopCountDownTimer() {
        try{ screenTimer!!.cancel() } catch (e:Exception) {}
    }

    private lateinit var mTransactionTaxiDAO: TransactionDao
    var mTeleCollectData: TelecollectDataModel=TelecollectDataModel()
    private var mesTransactionsTaxi: List<TransactionModel> = ArrayList()
    private var auditLogs= ArrayList<AuditModel>()
    private var loyaltyTransactions: ArrayList<TransactionModel> = ArrayList()
    var isFromSettings:Boolean=false
    var isTelecollectSuccess = false
    var isSendTelecollectFile = false
    inner class ScheduledTeleCollectTask(val referenceValue:String,val isScheduleTelecollect:Boolean=false,val isReferenceRequired:Boolean=true) : CoroutineAsyncTask<String, String, Boolean>() {
        var teleCollectValidation = false
        var validationParametrage = false
        private var sn = ""
        private var hashkey = ""
        private var hashTelecollecte = ""
        private var tlcFile: File? = null
        private var nbTransactions: Int = 0
        private var nbTransactionsLoyalty: Int? = null
        private var nbUpdateMileage: Int = 0
        private var nbAnnulationsTransactions: Int = 0
        private var nbRecharges: Int = 0
        private var nbAnnulationsRecharges: Int = 0
        private var nbTransactionsTaxis: Int = 0
        private var totalTransactions: Double = 0.0
        private var totalAnnulationsTransactions: Double = 0.0
        private var totalRecharges: Double = 0.0
        private var totalAnnulationsRecharges: Double = 0.0
        private var totalTransactionsTaxis: Double = 0.0
        private var nbRechargesTicket: Int = 0
        private var nbTransactionsTicket: Int = 0
        private var totalTransactionsTicket: Double = 0.0
        private var totalRechargesTicket: Double = 0.0
        private lateinit var teleCollectFormat: TeleCollectFormatModel
        private lateinit var teleCollectJSON: String
        private var mesPhotos: List<PhotoModel>? = null
        var prefs  = MainApp.getPrefs()
        override fun doInBackground(vararg params: String): Boolean {
            mesTransactionsTaxi = ArrayList()
            loyaltyTransactions = ArrayList()
            try {
                prefs.transactionCount = 0
                isTelecollectSuccess = false
                isSendTelecollectFile = true
                teleCollectValidation = true
                validationParametrage = true
                hashTelecollecte = "no_hash_calculated"
                tlcFile = null
                totalTransactionsTicket = 0.0
                nbTransactionsTicket = 0
                nbRechargesTicket = 0
                totalRechargesTicket = 0.0
                nbTransactions = 0
                nbTransactionsLoyalty = 0
                nbUpdateMileage = 0
                nbAnnulationsTransactions = 0
                nbRecharges = 0
                nbAnnulationsRecharges = 0
                nbTransactionsTaxis = 0
                nbUpdateMileage = 0
                totalTransactions = 0.0
                totalAnnulationsTransactions = 0.0
                totalRecharges = 0.0
                totalAnnulationsRecharges = 0.0
                totalTransactionsTaxis = 0.0
                Handler(Looper.getMainLooper()).post {
                    //get Transactions data from DB
                    try {
                        mTransactionTaxiDAO = TransactionDao()
                        mTransactionTaxiDAO.open()
                        mesTransactionsTaxi = mTransactionTaxiDAO.getTeleCollectTransactionByFlag(0)
                        mTransactionTaxiDAO.close()
                    } catch (Ex: SQLException) {
                        Ex.printStackTrace()
                    }
                    //get Loyalty transactions data from DB
                    try {
                        mTransactionTaxiDAO = TransactionDao()
                        mTransactionTaxiDAO.open()
                        loyaltyTransactions = mTransactionTaxiDAO.getTeleCollectLoyaltyByFlag(0)
                        mTransactionTaxiDAO.close()
                    } catch (Ex: SQLException) {
                        Ex.printStackTrace()
                    }
                    if(isScheduleTelecollect && mesTransactionsTaxi.isEmpty() && loyaltyTransactions.isEmpty())
                    {
                        isSendTelecollectFile = false
                    }
                    else
                    {
                        var i = 0
                        while (i < mesTransactionsTaxi.size && (mesTransactionsTaxi[i].detailArticle != "loy")) {
                            if (mesTransactionsTaxi[i].idTypeTransaction == 1) { // =1 trx
                                nbTransactions++
                                totalTransactions += mesTransactionsTaxi[i].amount!!
                                if(mesTransactionsTaxi[i].transactionStatus == 1)
                                {
                                    nbTransactionsTicket++
                                    totalTransactionsTicket += mesTransactionsTaxi[i].amount!!
                                }
                            }
                            if (mesTransactionsTaxi[i].idTypeTransaction == 2) { // =2 ann trx
                                nbAnnulationsTransactions++
                                totalAnnulationsTransactions += mesTransactionsTaxi[i].amount!!
                            }
                            if (mesTransactionsTaxi[i].idTypeTransaction == 3) { // =3 recharge
                                nbRecharges++
                                totalRecharges += mesTransactionsTaxi[i].amount!!
                                if(mesTransactionsTaxi[i].transactionStatus == 1 && mesTransactionsTaxi[i].idProduit != 99)
                                {
                                    nbRechargesTicket++
                                    totalRechargesTicket  += mesTransactionsTaxi[i].amount!!
                                }
                            }
                            if (mesTransactionsTaxi[i].idTypeTransaction == 4) { // =4 ann recharge
                                nbAnnulationsRecharges++
                                totalAnnulationsRecharges += mesTransactionsTaxi[i].amount!!
                            }
                            if (mesTransactionsTaxi[i].idTypeTransaction == 5) { // =5 Update Mileage
                                nbUpdateMileage++
                            }
                            i++
                        }
                        i = 0
                        while (i < loyaltyTransactions.size) {
                            if (loyaltyTransactions[i].amount!! > 0) {
                                nbTransactionsTaxis++ // trx loyalty
                                totalTransactionsTaxis += loyaltyTransactions[i].amount!! // amount loyalty
                            }
                            i++
                        }
                        mTeleCollectData = TelecollectDataModel(
                            dateTelecollecte = Support.dateToString(Date()),
                            nombreTransactions = nbTransactions,
                            nombreAnnulationsTransactions = nbAnnulationsTransactions,
                            nombreRecharges = nbRecharges,
                            nombreAnnulationsRecharges = nbAnnulationsRecharges,
                            totalTransactions = totalTransactions,
                            totalAnnulationsTransactions = totalAnnulationsTransactions,
                            totalRecharges = totalRecharges,
                            totalAnnulationsRecharges = totalAnnulationsRecharges,
                            nbTransactionsTaxis = nbTransactionsTaxis,
                            totalTransactionsTaxis = totalTransactionsTaxis,
                            reference = referenceValue,
                            nbUpdateMileage = nbUpdateMileage,
                            nbTransactionsTicket = nbTransactionsTicket,
                            nbRechargesTicket = nbRechargesTicket,
                            totalTransactionsTicket = totalTransactionsTicket,
                        )

                        try {
                            val auditDao = AuditDao()
                            auditDao.open()
                            auditLogs = auditDao.getAudits()
                            auditDao.close()
                        } catch (e:Exception){
                            e.printStackTrace()
                        }

                        teleCollectFormat = TeleCollectFormatModel(
                            mTeleCollectData,
                            loyaltyTransactions,
                            mesTransactionsTaxi,
                            auditLogs
                        )
                        teleCollectJSON = AppUtils.generateTeleCollectJson(teleCollectFormat)

                        val jsonFile = teleCollectFormat.mTelecollecte.reference + ".json"
                        Support.writeFile(teleCollectJSON, jsonFile, MainApp.appContext)
                        File("${MainApp.appContext.filesDir}/$jsonFile").also { tlcFile = it }

                        logWriter.appendLog(TAG, "jsonFile:: $jsonFile\n\n")
                        logWriter.appendLog(TAG,"teleCollectJSON:: $teleCollectJSON\n\n")
                        logWriter.appendLog(TAG,"tlcFile::: $tlcFile\n\n")

                        try {
                            hashTelecollecte = Support.generateSHA1(tlcFile).toString()
                        } catch (e: NoSuchAlgorithmException) {
                            e.printStackTrace()
                            teleCollectValidation = false
                        } catch (e: IOException) {
                            e.printStackTrace()
                            teleCollectValidation = false
                        }

                        sn = Support.getSN()!!

                        //String hashkey = "";
                        hashkey = Support.generateMD5("abcde" + sn + "fghij")!!
                        logWriter.appendLog("NOT HASHED KEY => ", "abcde" + sn + "fghij")
                        logWriter.appendLog("HASHED KEY => ", hashkey)
                        sendOfflinePhotos()
                        if (hashkey == "") {
                            teleCollectValidation = false
                        }
                    }
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                return false
            }
            return teleCollectValidation
        }

        override fun onPreExecute() {
         
        }

        override fun onPostExecute(result: Boolean?) {
            when (result) {
                false -> {
                    generateLogs("",0)
                }
                true -> {
                    if(isScheduleTelecollect && !isSendTelecollectFile)
                   {
                       getTelecollectData()
                   }
                    else
                    {
                        if(::teleCollectFormat.isInitialized){
                            val jsonFile = teleCollectFormat.mTelecollecte.reference + ".json"
                            val file = File("${MainApp.appContext.filesDir}/$jsonFile")
                            sendTeleCollectFile(hashkey, hashTelecollecte, file)
                        }
                        else
                        {
                            logWriter.appendLog(TAG,"teleCollectFormat Not Initialized")
                        }
                    }

                }
            }
        }
        private fun scheduleTelecollectSuccess() {
            logWriter.appendLog(TAG,"Get tele collect Params Success")
            isTelecollectSuccess = true
            stopCountDownTimer()
            startCountDownTimer()
            sendListnerMessage(isScheduleTelecollect, MainApp.appContext.resources.getString(R.string.terminal_setting_success), MainApp.appContext.resources.getString(R.string.telecollecte_ok), true)
        }
        fun sendOfflinePhotos() {
            var mPhotoDAO = PhotoDao()
            mPhotoDAO.open()
            mesPhotos = mPhotoDAO.selectionnerByFlag(0)
            mPhotoDAO.close()
            if(!mesPhotos.isNullOrEmpty())
            {
                for(photo in mesPhotos!!)
                {
                    if(photo.fieldsOptionalString != null)
                    {
                        val imageUri = Uri.parse(photo.fieldsOptionalString) //mCurrentPhotoPath
                        val fileKDO = File(imageUri.path)

                        MainApp.longApiService.offlinePhotoUpload(
                            url = prefs.baseUrl + NetworkRequestEndPoints.OFFLINEUPLOAD,
                            file = AppUtils.createFormData(fileKDO, "file", "*/*"),
                            sn = AppUtils.createFormData( MainApp.sn!!),
                            hashkey = AppUtils.createFormData(hashkey),
                            name = AppUtils.createFormData(photo.referenceTransaction!!)
                        ).subscribeOn(rx.schedulers.Schedulers.io())
                            .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                            .subscribe({
                                if (it != null && it.reponse != "OK") {
                                    logWriter.appendLog(TAG,"Photo Upload Success")
                                    mPhotoDAO = PhotoDao()
                                    mPhotoDAO.open()
                                    mPhotoDAO.updateFlagPhotoById(photo.id, 1)
                                    mPhotoDAO.close()
                                }
                            },
                                {
                                    logWriter.appendLog(TAG,"Photo Upload Failed")
                                    it.printStackTrace()
                                })
                    }
                }
            }
        }
        fun sendTeleCollectFile(key: String, hash: String, file: File) {
            logWriter.appendLog(TAG, "calling  sendTeleCollectFile")
            MainApp.longApiService.sendTeleCollectFile(
                url = prefs.baseUrl + NetworkRequestEndPoints.SEND_TELECOLLECT_FILE,
                file = AppUtils.createFormData(file, "file", "*/*"),
                sn = AppUtils.createFormData( MainApp.sn!!),
                key = AppUtils.createFormData(key),
                hash = AppUtils.createFormData(hash)
            ).subscribeOn(rx.schedulers.Schedulers.io())
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe({
                    if (it != null && it.reponse != "0") {
                        logWriter.appendLog(TAG,"Scheduled Send Telecollect Success")
                        validateTeleCollect(true)
                        if(isReferenceRequired)
                        {
                            logWriter.appendLog(TAG, "calling getTelecollectData ")
                            getTelecollectData()
                        }
                        else
                        {
                            generateLogs("Success",0)
                            logWriter.appendLog(TAG, "generating logs")
                        }

                    } else {
                        logWriter.appendLog(TAG,"Scheduled Send Telecollect Failed : ${it.error!!}")
                        showErrorOnProgress(MainApp.appContext.getString(R.string.error), MainApp.appContext.getString(R.string.error_occured_while_sending_telecollect_file))
                        generateLogs(it.error!!,1)
                    }
                },{
                    try {
                        logWriter.appendLog(TAG, "ERROR Caught in: sendTeleCollectFile() ---> ${it.message}")
                        Log.e("sendTeleCollectFile", "${it.message}")
                        showErrorOnProgress(MainApp.appContext.getString(R.string.error), getString(R.string.error_occured_while_sending_telecollect_file))
                    } catch (e:Exception) {
                        e.printStackTrace()
                    }
                })
        }
        //region save data in db
        fun showErrorOnProgress(title: String, message: String) {
            sendListnerMessage(
                isScheduleTelecollect,
                title,
                message,
                false
            )
        }
        fun getTelecollectData() {
            logWriter.appendLog(TAG, "calling getTelecollectData")
            val language =  LocaleManager.getLanguage(MainApp.appContext).lowercase()
            MainApp.longApiService.getAllReferenceData(
                prefs.baseUrl + NetworkRequestEndPoints.GET_ALL_REFERENCE_DATA,
                MainApp.sn!!,
                prefs.getPreferenceModel()!!.blockListVersionNo!!.toString(),
                prefs.getPreferenceModel()!!.greyListVersionNo!!.toString(),
                language
            ).subscribeOn(rx.schedulers.Schedulers.io())
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe({
                    if (it != null && it.reponse != "0") {
                        logWriter.appendLog(TAG,"Scheduled Get Telecollect Success =========>\n\n")
                        logWriter.appendLog(TAG,"Parameters Response:::::: ${Gson().toJson(it.contenu)}")
                        // To Check Should USe SD Card
                        if (it.contenu!!.USE_SD_CARD != null) {
                            prefs.isUseSdCard = it.contenu.USE_SD_CARD!!
                        }
                        // Save Terminal Details
                        // Save Terminal Details
                        if (it.contenu.terminal != null) {
                            val stationTitle = BuildConfig.VERSION_NAME + " | " + it.contenu.terminal.stationName + " (" + it.contenu.terminal.stationId + ")"
                            // Should Check this model Later
                            prefs.savePreferenceModel(
                                PreferenceModel(
                                    blockListVersionNo = it.contenu.blackListVersion,
                                    greyListVersionNo = it.contenu.GreyListVersion,
                                    firstTimeLoadingApp = false,
                                    aReferencer = true,
                                    blockage = false,
                                    suggestedPrice = false,
                                    tlcTimeStamp = System.currentTimeMillis(),
                                    transactionLimit = it.contenu.terminal.maxRefillAmount,
                                    rechargeLimit = it.contenu.terminal.maxRechargeLimit,
                                    stationTitle = stationTitle,
                                    //badgeGrants = it.contenu.badge,
                                    stationID = it.contenu.terminal.stationId,
                                    terminalID = it.contenu.terminal.terminalId,
                                    sectorId = it.contenu.terminal.sectorId,
                                    occuranceNetwork = true,
                                    BATTERY_ALERT = it.contenu.BATTERY_ALERT,
                                    RFID_VERIFICATION_TYPE = it.contenu.RFID_VERIFICATION_TYPE,
                                    TELECOLLECT_TIME = it.contenu.TELECOLLECT_TIME,
                                    MAX_REFILL_AMNT = it.contenu.MAX_REFILL_AMNT
                                )
                            )

                            saveDataInDB(it)
                            referenceModel = it.contenu

                            if(isReferencingRequest){ //sending broadcast to reference screen
                                try {
                                    sendListnerMessage(false,"","",true)
                                } catch (e: java.lang.Exception) {
                                    e.printStackTrace()
                                }
                            } else {                                                        // checking for menu and settings screen
                                if (prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!!) {
                                    getDiscountDetailsData()
                                } else {
                                    scheduleTelecollectSuccess()
                                }
                                if(!isScheduleTelecollect)
                                {
                                    restartFccService(it.contenu)
                                }
                                generateLogs("",1)

                            }
                        }

                    }
                    else {
                        logWriter.appendLog(TAG, "calling generateLogs")
                        generateLogs(it.error!!,1)
                        logWriter.appendLog(TAG, "sendLogFiles failed: ${it.error}")
                        showErrorOnProgress(MainApp.appContext.getString(R.string.error), MainApp.appContext.getString(R.string.error_occured_while_sending_telecollect_file))
                    }

                },{
                    try {
                        //Log.e("sendLogFiles", "${it.message}")
                        logWriter.appendLog(TAG, "sendLogFiles failed: ${it.message}")
                        showErrorOnProgress(MainApp.appContext.getString(R.string.error), getString(R.string.error_occurred_while_sending_logs))
                    } catch (e:Exception) {
                        e.printStackTrace()
                    }
                })

        }
        private fun getDiscountDetailsData() {
            MainApp.longApiService.getAllDiscountDetails(
                prefs.baseUrl + NetworkRequestEndPoints.GET_DISCOUNT_DETAILS,
                MainApp.sn!!
            ).subscribeOn(rx.schedulers.Schedulers.io())
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe({
                    if (it != null && it.success != "0") {
                        scheduleTelecollectSuccess()
                        prefs.saveDiscountDetails(it.discount_details!!)
                    } else {
                        if(prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!!)
                        {
                            logWriter.appendLog(TAG,"Failed to get discount details ${it.error}")
                            showErrorOnProgress(MainApp.appContext.getString(R.string.error), MainApp.appContext.getString(R.string.failed_to_get_discount_details))
                            generateLogs(it.error!!,1)
                        }
                        else
                        {
                            logWriter.appendLog(TAG,"Schedule Telecollect Success")
                            scheduleTelecollectSuccess()
                        }
                    }
                },{
                    logWriter.appendLog(TAG,"Failed to get discount details ${it.message} ${it.cause}")
                    showErrorOnProgress(MainApp.appContext.getString(R.string.failed_to_get_discount_details), MainApp.appContext.getString(R.string.failed_to_get_discount_details))
                })
        }
        private fun validateTeleCollect(isSuccess: Boolean) {
            if (isSuccess) {
                Support.updateBlockage(MainApp.appContext)
                /*  val mTelecollecteDAO = TeleCollectDao()
                  mTelecollecteDAO.open()*/
                /* val telecollecteRow = mTelecollecteDAO.insertTeleCollectDao(mTeleCollectData)
                 mTelecollecteDAO.close()*/

                val mTransactionTaxiDAO = TransactionDao()
                mTransactionTaxiDAO.open()
                for (i in mesTransactionsTaxi.indices) {
                    mTransactionTaxiDAO.updateTeleCollectFlagById(mesTransactionsTaxi[i].id!!, 1)
                }
                for (i in loyaltyTransactions.indices) {
                    mTransactionTaxiDAO.updateTeleCollectFlagById(loyaltyTransactions[i].id!!, 1)
                }
                /*logWriter.appendLog(TAG,"mesTransactionsTaxi => " + Gson().toJson(mesTransactionsTaxi))
                logWriter.appendLog(TAG,"loyaltyTransactions => " + Gson().toJson(loyaltyTransactions))
                val mesTransactionsTaxiTelecollectees = mTransactionTaxiDAO.getTransactions()

                CoroutineScope(Dispatchers.IO).launch {
                    for (i in mesTransactionsTaxiTelecollectees.indices) {
                        logWriter.appendLog(TAG,"mesTransactionsTaxiTelecollectees => " + Gson().toJson(mesTransactionsTaxiTelecollectees[i]))
                    }
                }*/

                val auditDao = AuditDao()
                auditDao.open()
                for(item in auditLogs){
                    item.teleCollectStatus = 1
                    auditDao.updateAudit(item)
                }
                auditDao.close()

                mTransactionTaxiDAO.close()

            }
        }
    }


    fun generateLogs(message:String,isErrorLog:Int) {
        val model =CommonViewModel(MainApp.longApiService,prefs)
        model.generateLogs(message,isErrorLog)
    }


    fun saveDataInDB(it: BaseResponse<ReferenceModel>) {
        if(it.contenu!=null){
            saveDataInDB(it.contenu)
        } else {
            logWriter.appendLog(TAG,"saveDataInDB(it: BaseResponse<ReferenceModel>) received null data in base response")
        }
    }

    private fun saveDataInDB(referenceModel:ReferenceModel){
        logWriter.appendLog(TAG, "saving data in db")
        try {

            SQLiteDatabase.loadLibs(MainApp.appContext)
            saveTerminalDetails(referenceModel.terminal!!)
            prefs.saveReferenceModel(referenceModel)
            prefs.isSendLogs = referenceModel.LOG!!
            if(referenceModel.is_delete_log_success_transaction != null)
            {
                prefs.isDeleteLogSuccessTransaction = referenceModel.is_delete_log_success_transaction
            }
            else
            {
                prefs.isDeleteLogSuccessTransaction = false
            }
            if(referenceModel.log_delete_schedule_hour != null)
            {
                prefs.logDeleteScheduleHour = referenceModel.log_delete_schedule_hour
            }
            else
            {
                prefs.logDeleteScheduleHour = 76
            }

            if(referenceModel.mtn_pay_credentials != null)
            {
                prefs.saveMtnPayCredentials(referenceModel.mtn_pay_credentials)
            }

            saveBlackListData(referenceModel.blacklist, referenceModel.blackListVersion)
            saveGreyListData(referenceModel.GreyList, referenceModel.GreyListVersion)
            saveGasStationAttendantDetails(
                referenceModel.pompiste,
                referenceModel.station!!.mode_pompiste
            )
            saveCompanyDetails(referenceModel.COMPANY)
            saveFuelPosDetails(referenceModel.FUELPOS)
            saveProductDetails(referenceModel.category_list)
            savePriceDetails(referenceModel.prix)

            if (referenceModel.FUSION != null) { //Save Fusion Model in SharedPreferences
                prefs.saveFusionModel(referenceModel.FUSION)
            }
            /*if (it.contenu.badge != null) { //Save Badge data in SharedPreferences
            //prefs.badge = it.contenu.badge
            prefs.saveManagerBadges(it.contenu.badge)
        }*/
            if (referenceModel.debug != null) {//To Check Terminal is Debug
                prefs.saveIntegerSharedPreferences(AppConstant.IS_DEBUGGABLE, referenceModel.debug)
            }
            if (referenceModel.ASKFORBADGE != null) {
                prefs.saveBooleanSharedPreferences(
                    AppConstant.IS_ASK_FOR_BADGE,
                    referenceModel.ASKFORBADGE
                )
            }
            if (referenceModel.WITHPICTURE != null) {
                prefs.saveBooleanSharedPreferences(AppConstant.wITHPICTURE, referenceModel.WITHPICTURE)
            }
            if (referenceModel.OFFLINEONLY != null) {
                prefs.saveBooleanSharedPreferences(
                    AppConstant.IS_OFFLINE_ONLY,
                    referenceModel.OFFLINEONLY
                )
            }
            if (referenceModel.`4G` != null) {
                prefs.saveIntegerSharedPreferences(AppConstant.IS_4G, referenceModel.`4G`)
            }
            if (referenceModel.LogTCP != null) {
                prefs.saveIntegerSharedPreferences(AppConstant.IS_LOGTCP, referenceModel.LogTCP)
            }
            if (referenceModel.LOG != null) {
                prefs.saveIntegerSharedPreferences(AppConstant.IS_LOGGABLE, referenceModel.LOG)
            }
            if (referenceModel.SERVER != null) {
                prefs.saveServerModel(referenceModel.SERVER)
            }
            if (referenceModel.category_list != null) {
                if (!referenceModel.category_list.isNullOrEmpty()) {
                    for (prod in referenceModel.category_list!!) {
                        if (prod.category_id == PRODUCT.FUEL_CATEGORY_ID) {
                            prefs.saveFuelProductList(prod.sub_products!!)
                        }
                    }
                }
            }

            if (referenceModel.prixConseille != null && referenceModel.prixConseille == "1") {
                prefs.saveBooleanSharedPreferences(AppConstant.PRICE_CONSOLATION, true)
            } else {
                prefs.saveBooleanSharedPreferences(AppConstant.PRICE_CONSOLATION, false)
            }

            if (referenceModel.station != null) {//Save Station Model in SharedPreferences
                prefs.saveStationModel(referenceModel.station)
                prefs.currency = referenceModel.station.currency
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            logWriter.appendLog(TAG, e.message+ExceptionUtils.getStackTrace(e))
            generateLogs("Failed to save the data on Db",1)
        }
    }

    private fun saveTerminalDetails(terminal: TerminalModel) {

        val mTerminalDAO = TerminalDao()
        mTerminalDAO.open()
        mTerminalDAO.clearTerminalTableData()
        mTerminalDAO.insertTerminalData(
            TerminalModel(
                terminalId = terminal.terminalId,
                serialNumber = terminal.serialNumber,
                teleCollectTime = Support.stringToTime("00:00:00")!!,
                max_transaction_amount = 10000,
                min_transaction_amount = 10,
                pays = "Maroc",
                region = "Region El Jadida",
                city = terminal.city,
                sectorName = "Zone Nord",
                sectorId = Integer.valueOf(terminal.sectorId),
                sectorBit = 0,
                stationId = Integer.valueOf(terminal.stationId),
                stationBit = 2,
                stationType = 0,  // typeStation
                stationName = terminal.stationName,
                address = terminal.address,
                fiscalId = terminal.fiscalId,
                shopFlag = 1,
                purchaseFlag = 1,
                cancellationFlag = 1,
                unlockTerminalFlag = 1,
                rechargeFlag = 1,
                lockTerminalFlag = 0,
                ipController = "**************",
                portController = "80",
                shopIp = "",
                shopPort = "",
                ipAddress = "",
                netMask = "",
                gateway = "",
                ssid = "",
                wifiPassword = "",
                inventoryCount = 140562,
                geoFence = "",
                currency = " " + prefs.currency,
                maxRefillAmount = terminal.maxRefillAmount,
                maxRechargeLimit = terminal.maxRechargeLimit
            )
        )

        val currentTerminal: TerminalModel? = mTerminalDAO.getCurrent()
        mTerminalDAO.close()
    }

    private fun saveBlackListData(blacklistData: ArrayList<BlackListModel>, blackListVersion: Int) {
      //  logWriter.appendLog(TAG, "BLACK LIST SIZE: ${blacklistData.size}")

        if (blacklistData.isNotEmpty()) {
            try {
                //save blacklist version in sp
                prefs.blackListVersion = blackListVersion


                val mBlackListDao = BlackListDao()
                mBlackListDao.open()
                mBlackListDao.clearBlackListData()
                mBlackListDao.insertBlackListArrayData(blacklistData)
                mBlackListDao.close()
                logWriter.appendLog(TAG, "Blacklist inserted in DB...")
                prefs.saveBlackListData(blacklistData)
            } catch (ex: SQLiteException) {
                ex.printStackTrace()
            }
        }
    }

    private fun saveGreyListData(greyListModel: ArrayList<GreyListModel>, greyListVersion: Int) {
        if (greyListModel.isNotEmpty()) {
            prefs.saveGreyList(greyListModel)
            //logWriter.appendLog(TAG, "Grey list inserted in DB...")
        }
    }

    private fun saveGasStationAttendantDetails(attendantModel: ArrayList<GasStationAttendantModel>, mode: String) {
        if (attendantModel.isNotEmpty()) {
            try {
              //  logWriter.appendLog(TAG,"ATTENDANTS: ${Gson().toJson(attendantModel)}")
                val mUsersDao = UsersDao()
                mUsersDao.open()
                mUsersDao.clearUsersTableData()
                mUsersDao.insertAttendiesArrayData(attendantModel, mode)
                mUsersDao.close()

            } catch (ex: SQLiteException) {
                ex.printStackTrace()
            }
        }
    }

    private fun saveCompanyDetails(companyModel: CompanyModel?) {
        if (companyModel != null) {
            prefs.saveBooleanSharedPreferences(AppConstant.COMPANY_RELOAD, companyModel.reload)
            prefs.saveStringSharedPreferences(AppConstant.COMPANY_LOGO, companyModel.logo)
            prefs.saveStringSharedPreferences(AppConstant.COMPANY_LOGO, companyModel.footer)
        }
    }
    private fun saveFuelPosDetails(fuelPOSModel: FuelPOSModel?) {

        if (fuelPOSModel != null) {
            prefs.saveFuelPosModel(fuelPOSModel)

            val mFuelPosDAO = FuelPosDao()
            mFuelPosDAO.open()
            mFuelPosDAO.clearFuelPosTableData()
            if (mFuelPosDAO.isOpen()) {
                mFuelPosDAO.insertFuelPosData(fuelPOSModel)
                mFuelPosDAO.close()
            }
        }
    }
    private fun saveProductDetails(categoryListModel: ArrayList<CategoryListModel>?) {
        if (categoryListModel != null) {

            val mProductDAO = ProductsDao()
            mProductDAO.open()
            mProductDAO.clearProductsTableData()

            val productModel = ArrayList<ProductModel>()

            for (model in categoryListModel) {
                when (model.category_id) {
                    PRODUCT.FUEL_CATEGORY_ID -> {
                        for (submodel in model.sub_products!!) {
                            productModel.add(
                                ProductModel(
                                    productID = submodel.id,
                                    fcc_prod_id = submodel.fcc_prod_id.toInt(),
                                    libelle = submodel.label,
                                    code = submodel.code,
                                    bit = 0,
                                    categorie = model.category_name,
                                    isAvailable = submodel.isAvailable.toString(),
                                    color_code = submodel.color_code,
                                    icon = submodel.icon,
                                    categoryId = model.category_id,
                                    hs_code = submodel.hs_code
                                )
                            )
                        }
                    }
                    PRODUCT.SERVICE_CATEGORY_ID -> {
                        for (submodel in model.sub_categories!!) {
                            productModel.add(
                                ProductModel(
                                    productID = submodel.id,
                                    fcc_prod_id = submodel.id,
                                    libelle = submodel.label,
                                    code = submodel.code,
                                    bit = 0,
                                    categorie = model.category_name,
                                    isAvailable = submodel.isAvailable.toString(),
                                    color_code = submodel.color_code,
                                    icon = submodel.icon,
                                    categoryId = model.category_id,
                                    hs_code = submodel.hs_code
                                )
                            )
                        }
                    }
                    else -> {
                        productModel.add(
                            ProductModel(
                                productID = model.category_id,
                                fcc_prod_id = model.category_id,
                                libelle = model.category_name,
                                code = model.category_id.toString(),
                                bit = 0,
                                categorie = model.category_name,
                                isAvailable = "true",
                                color_code = model.color_code,
                                icon = model.icon,
                                categoryId = model.category_id,
                                hs_code = model.hs_code
                            )
                        )
                    }
                }
            }
            mProductDAO.insertProductsArrayData(productModel)
            mProductDAO.close()
        }
    }
    private fun savePriceDetails(priceModel: ArrayList<PriceModel>) {
        if (priceModel.isNotEmpty()) {

            val mPriceDao = PriceDao()
            mPriceDao.open()
            mPriceDao.clearPriceData()
            mPriceDao.insertPriceArrayData(priceModel)
            mPriceDao.close()
        }
    }


    /*Region Listner*/
    private var telecollectLisner: TelecollectListner? = null
    private val mBinder: IBinder = LocalBinder()

    fun setTelecollectMessageListner(telecollectListner: TelecollectListner) {
        this.telecollectLisner = telecollectListner
    }
    public interface TelecollectListner {
        fun onTelecollectSuccess(title: String,message: String)
        fun onTelecollectFailed(title: String,message: String)
    }
    override fun onBind(intent: Intent): IBinder {
        return mBinder
    }
    fun restartApplicationIdle() {
        prefs.isRestartApplication = "true"
        prefs.isCurrentActivityisMenu = false
        //sending broadcast to launcher app
//        val intent = Intent()
//        intent.action = BuildConfig.APPLICATION_ID
//        intent.putExtra("isShowLoader", "true")
//        intent.putExtra("message", getString(R.string.please_wait_saving_transaction_details))
//        intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
//        intent.component = ComponentName("app.rht.pax.mylauncher", "app.rht.pax.mylauncher.service.BroadCastReceiver")
//        sendBroadcast(intent)

        val mIntent = Intent(applicationContext, SplashScreenActivity::class.java)
        val mPendingIntentId = 123456
        val mPendingIntent = PendingIntent.getActivity(
            applicationContext,
            mPendingIntentId,
            mIntent,
            PendingIntent.FLAG_CANCEL_CURRENT
        )
        val mgr = applicationContext.getSystemService(ALARM_SERVICE) as AlarmManager
        mgr[AlarmManager.RTC, System.currentTimeMillis() + 3000] = mPendingIntent
        exitProcess(0)
    }

    private fun sendListnerMessage(isScheduleTelecollect: Boolean,title: String, message: String, isSuccess: Boolean) {
        try {
            Log.i(TAG,"isScheduleTelecollect :: $isScheduleTelecollect \n title :: $title \n message :: $message \n isSuccess :: $isSuccess")
                if(isScheduleTelecollect)
                {
                    prefs.isCurrentActivityisMenu = true
//                    prefs.isRestartApplication = "true"
//                    Handler(Looper.getMainLooper()).postDelayed({
//                        restartApplicationIdle()
//                    },2000)
                }
            else {
                    if (telecollectLisner != null) {
                        if (isSuccess) {
                            telecollectLisner!!.onTelecollectSuccess(title, message)
                        } else {
                            telecollectLisner!!.onTelecollectFailed(title, message)
                        }
                    } else {
                        Log.i(TAG, "telecollectLisner is null")
                        Log.i(TAG, "telecollectLisner is null")
                    }
                }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    /*Region Listner*/
    private fun restartFccService(referenceModel: ReferenceModel) {
        Log.i(TAG,"restartFccService")
        if(referenceModel.FUSION!!.EXIST) {
            if(FusionService.isRunning(MainApp.appContext))
                FusionService.stop(MainApp.appContext)

            Handler(Looper.getMainLooper()).postDelayed({
                FusionService.start(MainApp.appContext)
            },5000)
        }
        else if(referenceModel.FUELPOS != null && referenceModel.FUELPOS.isExist) {

            if(FuelPosService.isRunning(MainApp.appContext))
                FuelPosService.stop(MainApp.appContext)

            Handler(Looper.getMainLooper()).postDelayed({
                FuelPosService.start(MainApp.appContext)
            },5000)
        }
    }
    //endregion

    inner class LocalBinder : Binder() {
        // Return this instance of LocalService so clients can call public methods
        val service: ScheduledTeleCollectService
            get() =// Return this instance of LocalService so clients can call public methods
                this@ScheduledTeleCollectService
    }

}