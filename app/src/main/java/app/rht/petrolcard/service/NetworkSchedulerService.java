package app.rht.petrolcard.service;

import android.app.job.JobParameters;
import android.app.job.JobService;
import android.content.Intent;
import android.content.IntentFilter;

import android.net.ConnectivityManager;

import app.rht.petrolcard.utils.Utils;
import app.rht.petrolcard.utils.constant.AppConstant;

public class NetworkSchedulerService extends JobService implements ConnectivityReceiver.ConnectivityReceiverListener {

    private static final String TAG = NetworkSchedulerService.class.getSimpleName();
    public static String  BROADCAST_KEY_NETWORK_AVAILABILITY = "broadcast_key_network_availability_com.app.rht.petrolcard.modeN";

    private ConnectivityReceiver mConnectivityReceiver;

    @Override
    public void onCreate() {
        super.onCreate();
        Utils.log(TAG, "Service created");
        mConnectivityReceiver = new ConnectivityReceiver(this);
    }



    /**
     * When the app's NetworkConnectionActivity is created, it starts this service. This is so that the
     * activity and this service can communicate back and forth. See "setUiCallback()"
     */
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Utils.log(TAG, "onStartCommand");
        return START_NOT_STICKY;
    }


    @Override
    public boolean onStartJob(JobParameters params) {
        Utils.log(TAG, "onStartJob" + mConnectivityReceiver);
        try{
            registerReceiver(mConnectivityReceiver, new IntentFilter(ConnectivityManager.CONNECTIVITY_ACTION));
        } catch (Exception e) { e.printStackTrace(); }
        return true;
    }

    @Override
    public boolean onStopJob(JobParameters params) {
        Utils.log(TAG, "onStopJob");
        unregisterReceiver(mConnectivityReceiver);
        return true;
    }

    @Override
    public void onNetworkConnectionChanged(boolean isConnected) {
        String message = isConnected ? "Good! Connected to Internet" : "Sorry! Not connected to internet";
        sendBroadcast(new Intent(BROADCAST_KEY_NETWORK_AVAILABILITY).putExtra(AppConstant.INTENT_KEY_DATA, isConnected));
    }

    @Override
    public void onDestroy() {
        try { unregisterReceiver(mConnectivityReceiver);} catch (Exception ex) {}
        super.onDestroy();
    }
}