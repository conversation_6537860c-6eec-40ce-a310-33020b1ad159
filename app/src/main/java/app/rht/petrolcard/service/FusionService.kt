package app.rht.petrolcard.service

import android.app.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.PRIORITY_DEFAULT
import androidx.core.content.ContextCompat
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.model.FuelPrice
import app.rht.petrolcard.baseClasses.model.FuelProduct
import app.rht.petrolcard.baseClasses.model.FuelPump
import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.database.baseclass.FCCTransactionsDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.timssign.activity.TIMSInvoiceGenerate
import app.rht.petrolcard.ui.transactionlist.model.*
import app.rht.petrolcard.utils.LogWriter
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Support.Companion.formatString
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.FUSION_PAYMENT_TYPES
import app.rht.petrolcard.utils.constant.FUSION_PAYMENT_TYPES.CASH_VALUE
import app.rht.petrolcard.utils.tax.TaxModel
import app.rht.petrolcard.utils.tax.TaxUtils
import app.rht.petrolcard.utils.tims.CloseReceiptRes
import com.altafrazzaque.ifsfcomm.*
import com.altafrazzaque.ifsfcomm.ifsf.models.*
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.w3c.dom.Document
import org.xml.sax.SAXException
import java.io.ByteArrayInputStream
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.util.*
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.parsers.ParserConfigurationException
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.utils.fuelpos.FuelPosService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


class FusionService : Service() {

    companion object {
        private val TAG = FusionService::class.simpleName
        private const val ACTION_START_FOREGROUND_SERVICE = "ACTION_START_FOREGROUND_SERVICE"
        private const val ACTION_STOP_FOREGROUND_SERVICE = "ACTION_STOP_FOREGROUND_SERVICE"
        private const val ACTION_START_FCC_SERVICE = "ACTION_START_FCC_SERVICE"
        private const val ACTION_STOP_FCC_SERVICE = "ACTION_STOP_FCC_SERVICE"
        private var fusionProductList: ArrayList<ProductPT> = ArrayList()
        private var productColors: ArrayList<ProductDSP> = ArrayList()
        private val logWriter = LogWriter("FuelServiceLogs")
        var fusionManager: IfsfManeger = IfsfManeger()
        var fiscalPrinterModel: FiscalPrinterModel? = null
        val prefs = MainApp.getPrefs()
        var terminal: TerminalModel? = null
        var ifsfManager: IfsfManeger? = null
        var fuelQtyUnit = "L"
        var referenceModel:ReferenceModel?=null
        var fuelVat = VatModel()
        var stationMode = 0

        @JvmStatic fun start(context: Context){
            val mainApp = MainApp.appContext as MainApp
            if(prefs.logReferenceNo.isNullOrEmpty())
            {
                mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME)
            }
            else
            {
                mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
            }
            val mIntent = Intent(context, FusionService::class.java)
            mIntent.action = ACTION_START_FOREGROUND_SERVICE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,mIntent)
            } else {
                context.startService(mIntent)
            }
        }
        @JvmStatic fun stop(context: Context) {

            val intent = Intent(context, FusionService::class.java)
            intent.action = ACTION_STOP_FOREGROUND_SERVICE

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
                //context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        @JvmStatic fun isRunning(context: Context): Boolean {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            for (service in activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (FusionService::class.java.name == service.service.className) {
//                    logWriter.appendLog(TAG, "RUNNING " + FusionService::class.java.name)
                    return true
                }
            }
            return false
        }
        @JvmStatic fun connectFcc(context: Context){
            val intent = Intent(context, FusionService::class.java)
            intent.action = ACTION_START_FCC_SERVICE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
            } else {
                context.startService(intent)
            }
        }
        @JvmStatic fun disconnectFcc(context: Context){
            val intent = Intent(context, FusionService::class.java)
            intent.action = ACTION_STOP_FCC_SERVICE

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
            } else {
                context.startService(intent)
            }
        }

        private class GetTrxTimerTask : TimerTask() {
            override fun run() {
                val message = ifsfManager!!.availableTrxDetailMsg("*")
                logWriter.appendLog(TAG,"sent: $message")
                IfsfService.sendIfsfMessage(message) // * = all devices ids
            }
        }
        //region ifsf message
        @JvmStatic fun GetAllFuelSaleTrxDetails(mPumpNumber: String) {
            try{
                val params = FuelSaleTrxPrams(mPumpNumber + "", "*")
                val message = ifsfManager!!.fuelTrxDetailMsg(params)
                logWriter.appendLog(TAG,"sent: $message")
                IfsfService.sendIfsfMessage(message)
            } catch (e:Exception) {
                e.printStackTrace()
            }
        }
        @JvmStatic fun getAllAvailableTransactions() {
            IfsfService.sendIfsfMessage(ifsfManager!!.getProductTable()) // get list of available products in  fusion
            logWriter.appendLog(TAG, "sending ifsf command to get latest transactions from fusion")
            Timer().schedule(GetTrxTimerTask(), 3000)
        }

        @JvmStatic fun getFusionErrorMessage(errorCode:String) :String {
            return ifsfManager!!.getErrorMessage(errorCode)
        }

        @JvmStatic fun sendFuelTrxDetailMsg(params: FuelSaleTrxPrams, context: Context) {
            val message = ifsfManager!!.fuelTrxDetailMsg(params)
            logWriter.appendLog(TAG,"sent: $message")
            IfsfService.sendIfsfMessage(message)
        }
        @JvmStatic fun sendPauseFuellingMsg(mPumpNumber: String) {
            val message = ifsfManager!!.getPauseFuelling(mPumpNumber+ "")
            logWriter.appendLog(TAG,"sent: $message")
            IfsfService.sendIfsfMessage(message)
        }
        @JvmStatic fun sendResumeFuellingMsg(mPumpNumber: String) {
            val message = ifsfManager!!.getResumeFuelling(mPumpNumber+ "")
            logWriter.appendLog(TAG,"sent: $message")
            IfsfService.sendIfsfMessage(message)
        }
        @JvmStatic fun getAuthRequest(mPumpNumber: String,articleID:String,mAmount:String,releaseToken:String):String {
            val message = ifsfManager!!.getAuthFPMsg(AuthFpPrams(mPumpNumber, articleID + "", mAmount,releaseToken))
            Log.e(TAG,"AUTH COMMAND: $message")
            logWriter.appendLog(TAG, "auth request: $message")
            //IfsfService.sendIfsfMessage(message)
            return message
        }
        @JvmStatic fun stopFuelPoint(mPumpNumber: String) {
            val message = ifsfManager!!.getStopFuelPoint(mPumpNumber+ "")
            logWriter.appendLog(TAG,"sent: $message")
            IfsfService.sendIfsfMessage(message)
            Handler(Looper.getMainLooper()).postDelayed({
                val message = ifsfManager!!.getStartFuelPoint(mPumpNumber+ "")
                IfsfService.sendIfsfMessage(message)
            },1000)
        }
        @JvmStatic fun startFuelPoint(mPumpNumber: String) {
            logWriter.appendLog(TAG, "mPumpNumber::$mPumpNumber")
            val message = ifsfManager!!.getStartFuelPoint(mPumpNumber+ "")
            logWriter.appendLog(TAG,"sent: $message")
            IfsfService.sendIfsfMessage(message)
        }
        @JvmStatic fun getPumpStatus(mPumpNumber: String) {
            val message = ifsfManager!!.getFpState(mPumpNumber+ "")
            logWriter.appendLog(TAG,"sent: $message")
            IfsfService.sendIfsfMessage(message)
        }
        @JvmStatic fun getAuthVolumeRequest(mPumpNumber: String,articleID:String,volume:String,releaseToken:String):String {
            val message = ifsfManager!!.getAuthFPVolumeMsg(AuthFpVolPrams(mPumpNumber, articleID + "", volume,releaseToken))
            logWriter.appendLog(TAG, "auth request: $message")
            return message
        }
        @JvmStatic fun getAvailableTransactionList(mPumpNumber: String) {
            try{
                var message = ifsfManager!!.availableTrxDetailMsg(mPumpNumber)
                logWriter.appendLog(TAG, "sent:getAvailableTransactionList $message")
                IfsfService.sendIfsfMessage(message)
            } catch (e:Exception) {
                e.printStackTrace()
            }
        }
        @JvmStatic fun clearTrxDetails(fuelSaleTrxPrams: ClearFuelSaleTrxPrams) {
            val message = ifsfManager!!.clearTrxDetailMsg(fuelSaleTrxPrams)
            logWriter.appendLog(TAG, "sent: $message")
            IfsfService.sendIfsfMessage(message)
        }

        @JvmStatic fun clearTrxDetailsAfterDelay(fuelSaleTrxPrams: ClearFuelSaleTrxPrams) {  //Added to resolve Terminal Power off during saving of transaction
            logWriter!!.appendLog(TAG,"TRX WILL CLEAR IN 10 SEC")
            Handler(Looper.getMainLooper()).postDelayed({
                val message = ifsfManager!!.clearTrxDetailMsg(fuelSaleTrxPrams)
                logWriter!!.appendLog(TAG, "sent: $message")
                IfsfService.sendIfsfMessage(message)
            },10000)
        }

        @JvmStatic fun getProductTable(){
            if(ifsfManager != null)
            {
                val message = ifsfManager!!.getProductTable()
                logWriter.appendLog(TAG,"sent: $message")
                IfsfService.sendIfsfMessage(message)
            }
        }
        @JvmStatic fun getLastTransactionsFromFusion() {
            getAllAvailableTransactions()
        }
        @JvmStatic fun getDSPConfigurationList() {
            if(ifsfManager != null)
            {
                val message = ifsfManager!!.getDispenserConfigMsg()
                logWriter.appendLog(TAG,"sent: $message")
                IfsfService.sendIfsfMessage(message)
            }
        }
        @JvmStatic fun getConfiguration() {
            if(ifsfManager!=null){
                val message = ifsfManager!!.getConfiguration()
                logWriter.appendLog(TAG,"sent: $message")
                IfsfService.sendIfsfMessage(message)
            }
        }
        @JvmStatic fun fccConnected(): Boolean {
            return try{
                IfsfService.isConnected()
            } catch (e:Exception){
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                e.printStackTrace()
                false
            }
        }
        private var checkClearTrx = false
        private var checkClearTrx1 = false
        @JvmStatic fun checkTransactionsToClear(){
            Log.e(TAG, "Checking transactions to clear")
            checkClearTrx = true
            getAvailableTransactionList("*")
        }

        @JvmStatic fun getErrorMessage(errorCode:String) : String {
            val message = when(errorCode){
                "FDC_ERRORSTATE" -> "FDC_ERRORSTATE - Error on Pump"
                "ERRCD_NOTRANS" -> "ERRCD_NOTRANS - Transactions not recorded on Fusion"
                "ERRCD_UPDFAILED" -> "ERRCD_UPDFAILED - Data update on forecourt hardware failed"
                "ERRCD_NOMONEYPRES" -> "ERRCD_NOMONEYPRES - Amount-Prepay not allowed"
                "ERRCD_NOVOLUMEPRES" -> "ERRCD_NOVOLUMEPRES - Volume preset not allowed"
                "ERRCD_GENAUTHLIMIT" -> "ERRCD_GENAUTHLIMIT - Maximum number of possible  authorisations exceeded (global)"
                "ERRCD_POSAUTHLIMIT" -> "ERRCD_POSAUTHLIMIT - Maximum number of possible authorisations exceeded (per POS)"
                "ERRCD_OTHER" -> "ERRCD_OTHER - Unspecified error"
                "ERRCD_MAXSTACKLIMIT" -> "ERRCD_MAXSTACKLIMIT - Maximum number of unpaid transactions reached (result of failed authorization due to reached limit)"
                "ERRCD_FPLOCK" -> "ERRCD_FPLOCK - Referenced Fuelling point locked"
                "ERRCD_RESUMEFUEL" -> "ERRCD_RESUMEFUEL - rror resume Fuelling"
                "ERRCD_NODATA" -> "ERRCD_NODATA - No data to return e. g. request for all Trx , no transactons available"
                "ERRCD_BADDEVID" -> "ERRCD_BADDEVID - Bad Device ID. Device ID does not exist"
                "ERRCD_BADTYPE" -> "ERRCD_BADTYPE - Bad Type. Type does not exist e.g. Type=”FFP”"
                "ERRCD_DEVICEUNAVAILABLE" -> "ERRCD_DEVICEUNAVAILABLE - Request not possible, because device is unavailable, e.g. wrong device number"
                "ERRCD_DEVICEDISABLED" -> "ERRCD_DEVICEDISABLED - Complete failure. Requested device is disabled"
                "ERRCD_WRONGDEVICENO" -> "ERRCD_WRONGDEVICENO - Complete failure. Requested device number is not available"
                "ERRCD_NOTPOSSIBLE" -> "ERRCD_NOTPOSSIBLE - Selected Pump is not available for fuelling"
                "VALIDATIONERROR" -> "VALIDATIONERROR - Validation error"
                else -> " $errorCode "
            }
            return message
        }
        //endregion
        private fun addFuelTransactionToList(serviceResponseGFST: FDCMessageResponse)
        {
            try {
            val currentTransaction: FDCMessageDeviceClassFST = serviceResponseGFST.fdcMessageResponse.fDCdata.deviceClass
            val productsModel= getProductModel(currentTransaction.productNo.toString())
            //Calculate VAT
            if (fuelVat.enabled) {
                isInclusive = fuelVat.type == 0
                type = if (isInclusive) "Incl." else "Excl."
                taxModel = TaxUtils.calculate(
                    currentTransaction.amount,
                    fuelVat.percentage!!.toDouble(),
                    isInclusive
                )
                vatAmount = formatString(taxModel!!.taxAmount)!!.toDouble()
            }
            val fusionTransaction = TransactionFromFcc()
            fusionTransaction.releaseToken = currentTransaction.releaseToken
            fusionTransaction.amount = currentTransaction.amount
            fusionTransaction.dh_transaction=currentTransaction.endTimeStamp
            fusionTransaction.hose=currentTransaction.nozzleNo.toInt()
            fusionTransaction.pompiste=""
            if(!currentTransaction.ProductName.isNullOrEmpty()) { fusionTransaction.produit = currentTransaction.ProductName }
            else { fusionTransaction.produit = getFusionProductName(currentTransaction.productNo) }
            fusionTransaction.pu=currentTransaction.unitPrice
            fusionTransaction.pump=currentTransaction.pumpNo
            fusionTransaction.fusionSaleId=currentTransaction.fusionSaleId
            fusionTransaction.releaseToken=currentTransaction.releaseToken
            fusionTransaction.quantite=currentTransaction.volume
            fusionTransaction.ref_transaction= currentTransaction.transactionSeqNo + ""
            fusionTransaction.rfid=""
            fusionTransaction.currency=prefs.currency
            fusionTransaction.hexColor = getFusionProductColor(currentTransaction.productNo)
            fusionTransaction.fccProductId = currentTransaction.productNo
            val timestamp = Support.convertToTimeStamp(fusionTransaction.dh_transaction!!)
            fusionTransaction.transactionTimestamp = timestamp
            if(!currentTransaction.ProductUM.isNullOrEmpty()) { fusionTransaction.fuelQtyUnit = currentTransaction.ProductUM }
            else { fusionTransaction.fuelQtyUnit = fuelQtyUnit }
            fusionTransaction.vatAmount = vatAmount.toString()

            if(productsModel != null) {
                fusionTransaction.produit = productsModel.libelle
                fusionTransaction.hsCode = productsModel.hs_code
                fusionTransaction.productId = productsModel.productID
            }
            else
            {
                fusionTransaction.produit = "Product ${currentTransaction.productNo}"
            }
            val fuelTransactionStatusDAO = FCCTransactionsDao()
            fuelTransactionStatusDAO.open()
            fusionTransaction.id=fuelTransactionStatusDAO.insert(fusionTransaction)
            fuelTransactionStatusDAO.close()
            if(prefs.isSignInBackground && fiscalPrinterModel!!.isAvailable && fiscalPrinterModel!!.isTIMSRequired == AppConstant.TIMS_REQUIRED && stationMode == AppConstant.AFTER_TRX_MODE) {
                checkTransactionCountPerPump(fusionTransaction.pump)
            }
            } catch (e: Exception) {
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
        }
        private fun getFusionProductColor(productNo: Int): String {
            for (product in productColors) {
                if (productNo == product.productNo) {
                    return "#${product.productColour}"
                }
            }
            return "#FF8212"
        }
        private fun getFusionProductName(productNo: Int): String {
            if (fusionProductList.isNotEmpty()) {
                for (product in fusionProductList) {

                    if (productNo == product.productNo) {
                        return product.productName
                    }
                }
            }
            return ""
        }
        /* TIMS region */
        var isInclusive = true
        var type = "Incl."
        var taxModel: TaxModel? = null
        var vatAmount = 0.0
        fun checkTransactionCountPerPump(pumpNo:Int)
        {
            var buffer_sign_transaction_count = 2
            if(FuelPosService.referenceModel != null && FuelPosService.referenceModel!!.buffer_sign_transaction_count != null)
            {
                buffer_sign_transaction_count = FuelPosService.referenceModel!!.buffer_sign_transaction_count!!
            }
            if(prefs.isSignInBackground && fiscalPrinterModel!!.isAvailable && fiscalPrinterModel!!.isTIMSRequired == AppConstant.TIMS_REQUIRED && stationMode == AppConstant.AFTER_TRX_MODE) {
                Handler(Looper.getMainLooper()).post {
                    try {
                        val fuelTransactionStatusDAO = FCCTransactionsDao()
                        fuelTransactionStatusDAO.open()
                        val count = fuelTransactionStatusDAO.checkTransactionCountPerPump(pumpNo)
                        if(count >= buffer_sign_transaction_count)
                        {
                            val transactionFromFcc = fuelTransactionStatusDAO.getPumpUnsignedTransaction(pumpNo)
                            if (transactionFromFcc != null) {
                                generateFPInvoice(transactionFromFcc, fuelTransactionStatusDAO, fuelVat)
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

        }
        private fun generateFPInvoice(transactionFromFcc: TransactionFromFcc?, fuelTransactionStatusDAO: FCCTransactionsDao, fuelVatModel: VatModel) {
            Handler(Looper.getMainLooper()).post {
                val timsGenerate = TIMSInvoiceGenerate(MainApp.appContext)
                timsGenerate.invoiceReceiptListner = object : TIMSInvoiceGenerate.InvoiceReceiptListner {
                    override fun onSuccess(model: CloseReceiptRes?, mTransaction: TransactionModel?) {
                        val clearTrxDetails = ClearFuelSaleTrxPrams(deviceId = mTransaction!!.pumpId!!, trxSequenceNo = mTransaction.sequenceController!!, paymentType = FUSION_PAYMENT_TYPES.CASH_VALUE, attributeName = "referenceNo", attributeValue = mTransaction.reference!!)
                        clearTrxDetails(clearTrxDetails)
                        fuelTransactionStatusDAO.deleteTransactionFusion(mTransaction.fccSaleId!!)
                        checkTransactionCountPerPump(mTransaction.pumpId!!.toInt())
                    }
                    override fun onFailed(message: String,method:String) {
                        prefs.isTimsStarted = false
                       checkTransactionCountPerPump(transactionFromFcc!!.pump)
                        logWriter.appendLog(TAG, "Failed to Sign ::$message")
                    }

                }
                // timsGenerate.readPrinterStatus(fiscalPrinterModel!!, transactionFromFcc!!,fuelVatModel)
                timsGenerate.startTIMSServer(fiscalPrinterModel!!, transactionFromFcc!!,fuelVatModel)
            }
        }
        /* End TIMS region */

        private fun getProductModel(id:String) : ProductModel? {
            var product : ProductModel? = null
            try {
                val productDAO = ProductsDao()
                productDAO.open()
                product = productDAO.getProductByFCCId(id.toInt())
                productDAO.close()

            } catch (e:Exception) {
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                e.printStackTrace()
            }
            return product
        }

    }

    override fun onBind(p0: Intent?): IBinder? {
        return null
    }
    override fun onCreate() {
        super.onCreate()

        startForegroundNotification()
    }
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        try{
            if(logWriter!=null)
                logWriter.appendLog(TAG,"ON START COMMAND: ${intent!!.action} -- $flags -- $startId")
        } catch (e:java.lang.Exception){
            e.printStackTrace()
        }
        if(intent != null) {
            when (intent.action) {
                ACTION_START_FOREGROUND_SERVICE -> {
                    startIfsfService()
                }
                ACTION_STOP_FOREGROUND_SERVICE -> {
                    stopForegroundService()
                }
                ACTION_START_FCC_SERVICE -> {
                    startIfsfService()
                }
                ACTION_STOP_FCC_SERVICE -> {
                    stopIfsfService()
                }
            }
        }

        return START_NOT_STICKY
    }
    //region foreground notification
    private val CHANNEL_ID = "10009"
    private val CHANNEL_NAME = "FBS Pay"
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(channelId: String, channelName: String): String {
        val name = getString(R.string.app_name)
        val channel = NotificationChannel(channelId, name, NotificationManager.IMPORTANCE_DEFAULT)
        channel.lightColor = Color.BLUE
        channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val service = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        service.createNotificationChannel(channel)
        return channelId
    }
    private fun startForegroundNotification() {
        if(logWriter!=null)
            logWriter.appendLog(TAG, "Start Fusion Service.")

        val intent = Intent(this, MenuActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, 0)

        val defaultSoundUri = Uri.parse("android.resource://app.rht.petrolcard/" + R.raw.connect)

        val channelId =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel(CHANNEL_ID, CHANNEL_NAME)
            } else { "" }

        val notificationBuilder = NotificationCompat.Builder(this, channelId )

        val notification = notificationBuilder.setOngoing(true)
            .setSmallIcon(android.R.drawable.stat_notify_sync)
            .setPriority(PRIORITY_DEFAULT)
            .setOnlyAlertOnce(true)
            //.setCategory(Notification.CATEGORY_SERVICE)
            .setContentTitle("Fusion")
            .setContentText("Service Running")
            .setSound(defaultSoundUri)
            .setSilent(true)
            .setFullScreenIntent(pendingIntent,true)
            .build()

        startForeground(101, notification)
    }
    private fun stopForegroundService() {
        if(logWriter != null) {
            logWriter.appendLog(TAG, "Stop ")
        }
        stopIfsfService()
        stopForeground(true)
        stopSelf()
    }
    //endregion
    override fun onDestroy() {
        super.onDestroy()
        try { unregisterReceiver(ifsfRecever) } catch (e:Exception) {}
        logWriter.appendLog(TAG, "On Destroy Called")
    }
    //region fusion methods
    private var fusionAddress = "**************"
    private var fusionPort = 4710
    var senderId = ""

    private fun startIfsfService(){
        try {
            stationMode= prefs.getStationModel()!!.mode
            if(IfsfService.isRunning(MainApp.appContext))
        {
             logWriter.appendLog(TAG,"ALREADY RUNNING")
            if(IfsfService != null && IfsfService.isConnected() != null && !IfsfService.isConnected()){
                IfsfService.connectFcc()
            }
            else {
                 logWriter.appendLog(TAG,"ALREADY CONNECTED")
            }
        }
        else{
             logWriter.appendLog(TAG,"STARTING IFSF SERVICE")
            ifsfManager = IfsfManeger()
             logWriter.appendLog(TAG, "Server IP: $fusionAddress")
            if (prefs.getFusionModel()!= null) {
                if(prefs.getFusionModel()!!.API != null)
                {
                    var fusionUrl = Support.splitIpAndPortFromUrl(prefs.getFusionModel()!!.API)
                    fusionAddress = fusionUrl[0]
                    fusionPort = fusionUrl[1].toInt()
                }
            }
                if(BuildConfig.DEBUG)
                {
                     var fusionAddress = "**************"
                     var fusionPort = 4710
                }
             logWriter.appendLog(TAG, "fusionPort: $fusionPort")
             logWriter.appendLog(TAG, "fusionAddress: $fusionAddress")
            val intent = Intent(this, IfsfService::class.java)
            senderId = Support.getSN()!!
            if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)
            intent.putExtra(IFSF_CONFIG, IfsfConfig(
                    senderId,
                    "TERMINAL",
                    fusionAddress,
                    fusionPort,
                    2,
                    retryOnTimeout = false,
                    autoConnectOnDisconnect = false
                ))
            if (!IfsfService.isRunning(MainApp.appContext)) {
                MainApp.appContext.startService(intent)

            }
            if(!isReceiverStarted)
                startIfsfReceiver()
        }
            referenceModel=prefs.getReferenceModel()
            fiscalPrinterModel =referenceModel!!.fiscal_printer
            stationMode= referenceModel!!.station!!.mode
            Log.i(TAG, "referenceModel!!.station!!.mode:: ${referenceModel!!.station!!.mode}")
            Log.i(TAG, "stationMode:: $stationMode")
            if(fiscalPrinterModel!!.isAvailable && fiscalPrinterModel!!.isTIMSRequired == AppConstant.TIMS_REQUIRED)
            {
                fuelQtyUnit = referenceModel!!.FUEL_QTY_UNIT ?: "L"
                fuelVat = referenceModel!!.fuelVat!!
            }
        }
        catch (e:Exception)
        {
            logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            e.printStackTrace()
        }
    }
    private fun stopIfsfService() {
        if (IfsfService.isRunning(this)) {
            stopService(Intent(this, IfsfService::class.java))
        }
        else {
            logWriter.appendLog(TAG,"Already stopped")
        }

        stopIfsfReceiver()

    }

    private var isReceiverStarted = false
    private fun startIfsfReceiver(){
        logWriter.appendLog(TAG,"IFSF RECEIVER STARTED")
        val filter = IntentFilter()
        filter.addAction(ACTION_IFSF_READ_DATA)
        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
        try{
            registerReceiver(ifsfRecever, filter)
        } catch (e:Exception){
            logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            e.printStackTrace()
        }
        isReceiverStarted = true
        //Handler(Looper.getMainLooper()).postDelayed({
            getPumpStatus("*")
            getDSPConfigurationList()      // getting product colors from fusion
        //},2000)
    }
    private fun stopIfsfReceiver(){
        try {
            unregisterReceiver(ifsfRecever)
            isReceiverStarted = false
        } catch (e:Exception)
        {
            logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            e.printStackTrace()
        }
        logWriter.appendLog(TAG,"IFSF RECEIVER STOPPED")
    }
    private var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (action == ACTION_IFSF_CONNECTION_STATE) {
                val msg = intent.getStringExtra(IFSF_CONNECTION_STATE)!!

                logWriter.appendLog(TAG,"ACTION $action")
                logWriter.appendLog(TAG,"DATA $msg")
                logWriter.appendLog(TAG, "fusionPort: $fusionPort")
                logWriter.appendLog(TAG, "fusionAddress: $fusionAddress")
                if(msg == "Connected") {
                    logWriter.appendLog(TAG,"LOGIN TO FUSION")
                    IfsfService.sendIfsfMessage(fusionManager.getLogOnMsg())

                }
                else if(msg == "Disconnected") {
                    //disconnectFcc(this@FusionService)
                    connectFcc(this@FusionService)
                }
            }
            if (action == ACTION_IFSF_READ_DATA) {
                val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
                 logWriter.appendLog(TAG,"Data Received:: $msg")
                val bytes = intent.getByteArrayExtra(IFSF_BYTE_MESSAGE)
                logWriter.appendLog(TAG,"BYTES: $bytes")

                if(msg!=null){
                    if(msg.contains("FDCMessage")){
                        if (msg.contains("<DeviceState Stopped=\"true\">")) {
                            logWriter.appendLog(TAG,"XML DATA: ${Support.xmlToJsonString(formatFDCMessage(msg))}")
                            val pumpId = StringUtils.substringBetween(msg, "<DeviceClass Type=\"FP\" DeviceID=\"","\">")
                            logWriter.appendLog(TAG,"PUMP ID:::: $pumpId")
                        }
                    }

                    /* val bIntent = Intent(RFID_ACTION_IFSF_READ_DATA)
                     bIntent.putExtra(RFID_IFSF_STRING_MESSAGE, msg)
                     sendBroadcast(bIntent)*/
                    performNextStep(msg)
                }
                else {
                    logWriter.appendLog(TAG,"Null message received")
                }
            }

        }
    }

    private var productColors: ArrayList<ProductDSP> = ArrayList()
    private fun performNextStep(message: String) {


        val errorCode = StringUtils.substringBetween(message, "<ErrorCode>", "</ErrorCode>")
        logWriter.appendLog(TAG, "Error Code: $errorCode")
        logWriter.appendLog(TAG, "Received: $errorCode")
        logWriter.appendLog(TAG, "message: $message")

        if (errorCode != null && errorCode.contains("ERRCD_OK")) {
            if (message.contains("FDCMessage")) {
                logWriter.appendLog(TAG, "Received: $message")
                val jsonString = Support.xmlToJsonString(formatFDCMessage(message))
                logWriter.appendLog(TAG, "JSON Format: $jsonString")
                val messageType = StringUtils.substringBetween(message, "MessageType=\"", "\"")
                val gson = Gson()
                if (messageType.equals("FuelSaleTrx")) {
                    val pumpNo = StringUtils.substringBetween(message, "PumpNo=\"", "\"")
                    val state = StringUtils.substringBetween(message, "<State>", "</State>")
                    if (state == "Payable") {
                        if (stationMode == AppConstant.AFTER_TRX_MODE) {
                            if (prefs.getReferenceModel()!!.RFID_TERMINALS != null) {
                                val pumpsModels = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
                                logWriter.appendLog(TAG, "RFID Terminals:: $pumpsModels")
                                for (terminalPump in pumpsModels) {
                                    logWriter.appendLog(
                                        TAG,
                                        "TERMINAL PUMP: ${terminalPump.pump_number} TRX PUMP: $pumpNo"
                                    )
                                    if (pumpNo == "${terminalPump.pump_number}") {
                                        logWriter.appendLog(TAG, "${terminalPump.pump_number} = $pumpNo")
                                        for (nozzle in terminalPump.nozzles) {
                                            try {
//                                        val transaction: ServiceResponseGFST = gson.fromJson(jsonString, ServiceResponseGFST::class.java)
                                                val transaction: FDCMessageResponse = gson.fromJson(
                                                    jsonString,
                                                    FDCMessageResponse::class.java
                                                )
                                                val currentTransaction: FDCMessageDeviceClassFST =
                                                    transaction.fdcMessageResponse.fDCdata.deviceClass
                                                if (nozzle.nozzle_number == currentTransaction.nozzleNo.toInt()) {
                                                    addFuelTransactionToList(transaction)
                                                }
                                            } catch (e: JsonSyntaxException) {
                                                e.printStackTrace()
                                                logWriter.appendLog(TAG, "Json syntax exception: " + e.message + " " + e.stackTrace)
                                            }
                                        }

                                    } else {
                                        logWriter.appendLog(TAG, "${terminalPump.pump_number} != $pumpNo")
                                    }
                                }
                            }
                        }
                    }
                }

            }
            if (message.contains("ServiceResponse")) {
                val msg = formatMessage(message)
                val overallResult = StringUtils.substringBetween(msg, "OverallResult=\"", "\"")
                val requestType = StringUtils.substringBetween(msg, "RequestType=\"", "\"")
                if(overallResult == "Success" && errorCode == "ERRCD_OK"){
                    if (requestType == "GetProductTable") {
                        logWriter.appendLog(TAG, "ServiceResponse: Saving product table in sp")
                        val jsonString = Support.xmlToJsonString(msg)
                        try {
                            val fusionProductList = Gson().fromJson(
                                jsonString,
                                ServiceResponseGPT::class.java
                            ).serviceResponse.fDCdata.fuelProducts.product
                            if (fusionProductList.isNotEmpty()) {
                                prefs.saveProductTable(jsonString)
                                logWriter.appendLog(TAG, "Product table update")
                            }
                            logWriter.appendLog(TAG, "fusionProductList $fusionProductList")
                        } catch (e: java.lang.Exception) {
                            val stacktrace = ExceptionUtils.getStackTrace(e)
                            logWriter.appendLog(TAG, "Error occurred while fetching products from fusion: $stacktrace")
                            //  e.printStackTrace()
                        }
                    }
                    else if (requestType == "GetDSPConfiguration") {
                        CoroutineScope(Dispatchers.IO).launch {
                            try {
                                val jsonString: String = Support.xmlToJsonString(message)!!
                             //   logWriter.appendLog(TAG,"DSP CONFIG::: $jsonString")
                                val dspConfiguration = Gson().fromJson(jsonString, DSPConfiguration::class.java)
                                val devices = dspConfiguration.serviceResponse.fDCdata.deviceClasses
                                for(device in devices)
                                {
                                    val products = device.products
                                    for(product in products)
                                    {
                                        if(!productColors.contains(product))
                                        {
                                            productColors.add(product)
                                            logWriter.appendLog(TAG,"##### Product color added in list (Product: ${product.productName} ---- #${product.productColour})")
                                        }
                                    }
                                    prefs.saveProductColors(productColors)
                                }
                            } catch (e: java.lang.Exception) {
                                //logWriter!!.appendLog(TAG, "Error occurred while fetching products from fusion: " + e.message)
                                //e.printStackTrace()
                            }
                        }
                    }
                    else if (requestType == "GetConfiguration") {
                        readFuelPrices(msg)
                    }
                    else if (requestType == "GetAvailableFuelSaleTrxs"  && checkClearTrx && stationMode != AppConstant.AFTER_TRX_MODE) {
                        checkClearTrx = false
                        checkClearTrx1 = true
                        checkTransactionSequenceNumbers(msg)
                    }
                    else if (requestType == "GetFuelSaleTrxDetails" && checkClearTrx1 && stationMode != AppConstant.AFTER_TRX_MODE) {
                        checkTransactionDetails(msg)
                    }
                    else if (requestType == "ClearFuelSaleTrx") {
                        trxClearLog.appendLog(TAG, "Transaction cleared: $msg")
                        checkNextTransaction()
                    }
                }
                else {
                    logWriter.appendLog(TAG,msg)
                }
            }
        }

    }
    private fun formatMessage(message: String): String {
        var message = message
        message = if (message.contains("</ServiceResponse>")) {
            //split = response.split("</ServiceResponse>");
            "<ServiceResponse " + StringUtils.substringBetween(
                message,
                "<ServiceResponse",
                "</ServiceResponse>"
            ) + "</ServiceResponse>"
        } else {
            val msgArr =
                message.split("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />").toTypedArray()
            if (msgArr.size > 2) println(msgArr[0] + ", " + msgArr[1])
            if (!msgArr[0].contains("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />")) "<ServiceResponse>" + StringUtils.substringBetween(
                msgArr[0], "<ServiceResponse>", "</ServiceResponse>"
            ) + "</ServiceResponse>" else msgArr[0] + "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />"
        }
        return message
    }
    private fun formatFDCMessage(message: String): String {
        var message = message
        message = if (message.contains("</FDCMessage>")) {
            //split = response.split("</ServiceResponse>");
            "<FDCMessage " + StringUtils.substringBetween(
                message,
                "<FDCMessage",
                "</FDCMessage>"
            ) + "</FDCMessage>"
        } else {
            val msgArr =
                message.split("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />").toTypedArray()
            if (msgArr.size > 2) println(msgArr[0] + ", " + msgArr[1])
            if (!msgArr[0].contains("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />")) "<FDCMessage>" + StringUtils.substringBetween(
                msgArr[0], "<FDCMessage>", "</FDCMessage>"
            ) + "</FDCMessage>" else msgArr[0] + "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />"
        }
        return message
    }
    //endregion
    private fun readFuelPrices(message: String) {
        try {

            val stream = ByteArrayInputStream(message.toByteArray(StandardCharsets.UTF_8))
            val fuelProductList = ArrayList<FuelPump>()
            try {
                val dbFactory = DocumentBuilderFactory.newInstance()
                val dBuilder = dbFactory.newDocumentBuilder()
                val doc: Document = dBuilder.parse(stream)
                val element = doc.documentElement
                element.normalize()
                val nList = doc.getElementsByTagName("DeviceClass")
                for (i in 0 until nList.length) {
                    val node = nList.item(i)
                    if(node.hasAttributes()){
                        val attributes = node.attributes
                        val type = attributes.item(0).nodeValue
                        if(type  == "DSP") // PUMP
                        {
                            var count = 0
                            val pumpId = attributes.item(1).nodeValue
                            val childNodes = node.childNodes

                            val products = ArrayList<FuelProduct>()
                            for(j in 0 until childNodes.length){
                                val child = childNodes.item(j)
                                if(child.nodeName == "Product"){
                                    count++
                                    val prices = child.childNodes
                                    for(k in 0 until prices.length){
                                        val price = prices.item(k)
                                        if(price.nodeName == "FuelPrice"){
                                            var fuelPrices = ArrayList<FuelPrice>()
                                            if(price.hasAttributes()){
                                                val mode = price.attributes.item(0).nodeValue
                                                val fuelPrice = price.firstChild.nodeValue
                                                fuelPrices.add(FuelPrice(mode = mode,price = fuelPrice))
                                            }
                                            val number = child.attributes.item(0).nodeValue
                                            val name = child.attributes.item(1).nodeValue
                                            val color = child.attributes.item(2).nodeValue
                                            val product = FuelProduct(name,color,number,fuelPrices)
                                            products.add(product)
                                        }
                                    }
                                }
                            }
                            println("PUMP $pumpId has $count products")

                            val item = FuelPump(pumpId = pumpId, product = products)
                            fuelProductList.add(item)
                        }
                    }

                }
                println("Total Fuel Product prices: ${Gson().toJson(fuelProductList)}")
                prefs.saveFuelProductPrices(fuelProductList)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: ParserConfigurationException) {
            e.printStackTrace()
        } catch (e: SAXException) {
            e.printStackTrace()
        }
    }
    private val trxClearLog = LogWriter("fusion_transaction_to_clear")
    private var trxSequenceList = ArrayList<DeviceClassGAFT>()
    private fun checkTransactionSequenceNumbers(message: String){
        val gson = Gson()
        val jsonString: String = Support.xmlToJsonString(message)!!
        trxSequenceList.clear()
        try {
            val serviceResponse: ServiceResponseGAFT = gson.fromJson(jsonString,ServiceResponseGAFT::class.java)
            val trxList: List<DeviceClassGAFT> = serviceResponse.serviceResponse.fDCdata.deviceClasses
            if (prefs.getReferenceModel()!!.RFID_TERMINALS != null) {
                val pumpsModels: List<RFIDPumpsModel> =prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
                for (pumpsModel in pumpsModels) {
                    for (deviceClassGAFT in trxList) {
                        if (deviceClassGAFT.pumpNo == pumpsModel.pump_number) {
                            trxSequenceList.add(deviceClassGAFT)
                            trxClearLog.appendLog(TAG, "Pump Available:: " + deviceClassGAFT.pumpNo)
                        }
                    }
                }
                trxClearLog.appendLog(TAG, "pumpsModels from API :: ${Gson().toJson(pumpsModels)}")
            } else {
                trxClearLog.appendLog(TAG, "Pump List Not Available")
            }
        }
        catch (ex:Exception) {
            val serviceResponse = gson.fromJson(jsonString, ServiceResponseGAFT2::class.java)
            if (serviceResponse != null) {
                val deviceClassGAFT /*: List<DeviceClassGAFT>*/ = serviceResponse.serviceResponse.fDCdata.deviceClass
                if (prefs.getReferenceModel()!!.RFID_TERMINALS != null) {
                    val pumpsModels: List<RFIDPumpsModel> = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
                    for (pumpsModel in pumpsModels) {
                        //for (deviceClassGAFT in trxList) {
                        if (deviceClassGAFT.pumpNo == pumpsModel.pump_number) {
                            trxSequenceList.add(deviceClassGAFT)
                            trxClearLog.appendLog(TAG, "Pump Available:: " + deviceClassGAFT.pumpNo)
                        }
                        //}
                    }
                    trxClearLog.appendLog(TAG, "pumpsModels from API :: $pumpsModels")
                } else {
                    trxClearLog.appendLog(TAG, "Pump List Not Available")
                }
            }
        }

        if(trxSequenceList.isNotEmpty()){
            checkNextTransaction()
        } else {
            trxClearLog.appendLog(TAG, "Transaction Seq list empty")
        }
    }
    private fun checkNextTransaction() {
        if(trxSequenceList.size > 0){
            val pumpNumber = "${trxSequenceList[trxSequenceList.size-1].pumpNo}"
            val trxSeq = "${trxSequenceList[trxSequenceList.size-1].transactionSeqNo}"
            trxClearLog.appendLog(TAG, "Sending Detail Trx Cmd :: Pump: $pumpNumber -- SeqNo: $trxSeq")
            val params = FuelSaleTrxPrams(pumpNumber + "", trxSeq)
            sendFuelTrxDetailMsg(params, applicationContext)
        } else {
            trxClearLog.appendLog(TAG, "Transaction Seq list empty")
        }
    }

    private fun checkTransactionDetails(message:String){

        if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)

        val authorizationSenderID = StringUtils.substringBetween(message, "<AuthorisationApplicationSender>","</AuthorisationApplicationSender>")
        trxClearLog.appendLog(TAG, "Check AuthorisationApplicationSender:: $authorizationSenderID == $senderId")

        val fusionSaleId = StringUtils.substringBetween(message, "FusionSaleId=\"", "\"")
        val seqNo = StringUtils.substringBetween(message, "TransactionSeqNo=\"", "\"")
        val fpPump = StringUtils.substringBetween(message, "DeviceID=\"", "\"")
        val fpProduct = StringUtils.substringBetween(message, "<ProductNo>", "</ProductNo>")
        val fpAmount = StringUtils.substringBetween(message, "<Amount>", "</Amount>")
        val unitPrice = StringUtils.substringBetween(message, "<UnitPrice>", "</UnitPrice>")
        val productUM = StringUtils.substringBetween(message, "<ProductUM>", "</ProductUM>")
        val productName = StringUtils.substringBetween(message, "<ProductName>", "</ProductName>")
        val endTimeStamp = StringUtils.substringBetween(message, "<EndTimeStamp>", "</EndTimeStamp>")
        val releaseToken = StringUtils.substringBetween(message, "<ReleaseToken>", "</ReleaseToken>")
        val fpVolume = StringUtils.substringBetween(message, "<Volume>", "</Volume>")
        var paymentType = CASH_VALUE
        var referenceNo = ""
        try {
            if (senderId == authorizationSenderID) // works in normal flow of transaction
            {
                val transactionDao = TransactionDao()
                var transactionModel = transactionDao.getTransactionByReleaseToken(releaseToken)
                if (transactionModel != null) {
                    paymentType = Support.getFusionPaymentType(transactionModel.modepay!!)
                    referenceNo = transactionModel.reference!!
                }
               else
                {
                    transactionModel = TransactionModel()
                    transactionModel.reference=Support.generateNewReferenceNumber(this)
                }
                transactionModel.pumpId = fpPump
                transactionModel.sequenceController = seqNo
                transactionModel.amount = fpAmount.toDouble()
                transactionModel.quantite = fpVolume.toDouble()
                transactionModel.unitPrice = unitPrice.toDouble()
                transactionModel.fccProductId = fpProduct
                transactionModel.fccSaleId = fusionSaleId
                transactionModel.productName = productName
                transactionModel.timsSignDetails!!.fuelQtyUnit = productUM
                transactionModel.dateTransaction = endTimeStamp
                transactionModel.fccReleaseToken = releaseToken

                transactionModel.transactionStatus = 1                  // added to make trx completed
                transactionModel.flagTelecollecte = 0                   // Added to resolve fuelUP Missing trx Issue (if transaction is not completed due to pump error,
                transactionModel.isPumpError = 0                        // after that incomplete transaction is tele-collected and after tele-collection same transaction cleared
                                                                        // and got complete details so it was not going to FBS because same trx is already tele-collected)
                                                                        // added explanation here https://app.clickup.com/t/32c9mx7

                transactionDao.updateTransactionsByReferenceID(transactionModel)
                if (trxSequenceList.isNotEmpty()) {
                    trxSequenceList.removeLast()
                    trxClearLog.appendLog(TAG, "Success authorizationSenderID:: $authorizationSenderID == $senderId")
                    val clearTrxDetails = ClearFuelSaleTrxPrams(
                        deviceId = fpPump,
                        trxSequenceNo = seqNo,
                        paymentType = paymentType,
                        attributeName = "referenceNo",
                        attributeValue = referenceNo
                    )
                    clearTrxDetails(clearTrxDetails)
                } else {
                    checkClearTrx1 = false
                }
            }
        }
            catch (e:Exception)
            {
                e.printStackTrace()
            }

    }
    //endregion


}