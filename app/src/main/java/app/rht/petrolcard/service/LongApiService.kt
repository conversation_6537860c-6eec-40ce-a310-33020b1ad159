package app.rht.petrolcard.service

import app.rht.petrolcard.BuildConfig.URL_INITIAL
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.utils.constant.AppConstant
import com.facebook.stetho.okhttp3.StethoInterceptor
import com.google.gson.GsonBuilder
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava.RxJavaCallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import java.util.concurrent.TimeUnit

class LongApiService {
    companion object {
        fun create(
            baseUrl: String = URL_INITIAL,
            connectTimeoutInSec: Long = AppConstant.DEFAULT_CONNECTION_TIMEOUT_SEC,
            readTimeoutInSec: Long = AppConstant.DEFAULT_READ_TIMEOUT_SEC,
            writeTimeoutInSec: Long = AppConstant.DEFAULT_WRITE_TIMEOUT_SEC
        ): ApiService {
            val client = OkHttpClient.Builder()
            val loggingInterceptor = HttpLoggingInterceptor()
            loggingInterceptor.level = HttpLoggingInterceptor.Level.BODY
            client.addInterceptor(loggingInterceptor)
            client.addNetworkInterceptor(StethoInterceptor())

            client.connectTimeout(connectTimeoutInSec, TimeUnit.SECONDS)
            client.readTimeout(readTimeoutInSec, TimeUnit.SECONDS)
            client.writeTimeout(writeTimeoutInSec, TimeUnit.SECONDS)
            val gson = GsonBuilder().setLenient().create()

            val retrofit = Retrofit.Builder()
                .addCallAdapterFactory(RxJavaCallAdapterFactory.create())
                .addConverterFactory(GsonConverterFactory.create(gson))
//                    .addConverterFactory(ScalarsConverterFactory.create())
                .client(client.build())
                .baseUrl(baseUrl)
                .build()
            return retrofit.create(ApiService::class.java)
        }
    }
}