package app.rht.petrolcard.database.baseclass

import android.content.Context
import android.util.Log
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteOpenHelper

class DatabaseHandler
/**
 * constructor should be private to prevent direct instantiation.
 * make call to static factory method "getInstance()" instead.
 */ private constructor(
    private val mCtx: Context,
    name: String,
    factory: SQLiteDatabase.CursorFactory?,
    version: Int
) :
    SQLiteOpenHelper(mCtx, name, null, version) {
    override fun onCreate(db: SQLiteDatabase) {
        Log.v(TAG, "onCreate DB")

        db.execSQL(TransactionDao.TRANSACTION_TABLE_CREATE)
        db.execSQL(BlackListDao.BLACKLIST_TABLE_CREATE)
        db.execSQL(FuelPosDao.FUEL_POS_TABLE_CREATE)
        db.execSQL(GreyListDao.GREYLIST_TABLE_CREATE)
        db.execSQL(PriceDao.PRICE_TABLE_CREATE)
        db.execSQL(ProductsDao.PRODUCT_TABLE_CREATE)
        db.execSQL(TerminalDao.TERMINAL_TABLE_CREATE)
        db.execSQL(UsersDao.USERS_TABLE_CREATE)
        db.execSQL(FCCTransactionsDao.TRANSACTION_STATUS_TABLE_CREATE)
        db.execSQL(FuelTrxCountDao.FUEL_TRX_COUNT_TABLE_CREATE)
        db.execSQL(TeleCollectDao.TELECOLLECTE_TABLE_CREATE)
        db.execSQL(PhotoDao.PHOTO_TABLE_CREATE)
        db.execSQL(ESDSignatureDao.ESD_SIGN_TABLE_CREATE)
        db.execSQL(AuditDao.AUDIT_TABLE_CREATE)
        db.execSQL(LogFilesDao.POS_FILE_TABLE_CREATE)
        db.execSQL(PendingFPosTrxDao.PENDING_FPOS_TRX_TABLE_CREATE)

    }

    override fun onUpgrade(db: SQLiteDatabase, oldVersion: Int, newVersion: Int) {
        db.execSQL("DROP TABLE IF EXISTS ${TransactionDao.TRANSACTION_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${BlackListDao.BLACKLIST_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${FuelPosDao.FUEL_POS_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${GreyListDao.GREYLIST_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${PriceDao.PRICE_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${ProductsDao.PRODUCT_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${TerminalDao.TERMINAL_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${UsersDao.USERS_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${FCCTransactionsDao.FCC_TRANSACTION_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${FuelTrxCountDao.FUEL_TRX_COUNT_TABLE}")
        db.execSQL("DROP TABLE IF EXISTS ${TeleCollectDao.TELECOLLECTE_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${PhotoDao.PHOTO_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${ESDSignatureDao.ESD_SIGN_TABLE}")
        db.execSQL("DROP TABLE IF EXISTS ${AuditDao.AUDIT_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${LogFilesDao.LOG_TABLE_NAME}")
        db.execSQL("DROP TABLE IF EXISTS ${PendingFPosTrxDao.PENDING_TRX_TABLE_NAME}")
        onCreate(db)
    }

    companion object {
        private val TAG = DatabaseHandler::class.java.simpleName
        private var mInstance: DatabaseHandler? = null
        @Synchronized
        fun getInstance(
            context: Context,
            name: String,
            factory: SQLiteDatabase.CursorFactory?,
            version: Int
        ): DatabaseHandler? {
            /**
             * use the application context as suggested by CommonsWare.
             * this will ensure that you dont accidentally leak an Activitys
             * context (see this article for more information:
             * http://android-developers.blogspot.nl/2009/01/avoiding-memory-leaks.html)
             */
            if (mInstance == null) {
                mInstance = DatabaseHandler(context, name, null, version)
            }
            return mInstance
        }
    }
}