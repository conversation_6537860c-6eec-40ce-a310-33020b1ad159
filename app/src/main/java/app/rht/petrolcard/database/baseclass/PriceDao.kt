package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context


import app.rht.petrolcard.ui.reference.model.PriceModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.helpers.BaseUtils
import net.sqlcipher.database.SQLiteException
import kotlin.collections.ArrayList

class PriceDao : MainDataBaseClass()
{
    companion object {
         val PRICE_TABLE_NAME = "price"
        private val PRICE_ID = "id"
        private val PRICE_ID_PRODUCT = "product_id"
        private val PRICE_ID_FCC_PRODUCT = "fcc_product_id"
        private val PRICE_UNIT_PRICE = "unitPrice"
        private val PRICE_DATE_DEBIT = "debitDate"
        private val PRICE_DATE_FIN = "dateFin"

        val PRICE_TABLE_CREATE =
            "CREATE TABLE " + PRICE_TABLE_NAME + " (" +
                    PRICE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    PRICE_ID_PRODUCT + " INTEGER, " +
                    PRICE_UNIT_PRICE + " REAL, " +
                    PRICE_DATE_DEBIT + " TEXT, " +
                    PRICE_DATE_FIN + " TEXT, "+
                    PRICE_ID_FCC_PRODUCT + " INTEGER);"
    }


    fun PriceDao(pContext: Context?) {
        super.getContext()
    }

    fun clearPriceData(): Int {
        val newRowId = mDb!!.delete(PRICE_TABLE_NAME, null, null)
        if ( isSdCARD()) {
          mDb2!!.delete(PRICE_TABLE_NAME, null, null)
          }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(PRICE_TABLE_NAME, null, null)
          }
        return newRowId
    }
    fun insertPriceData(model: PriceModel): Int {
        try {
            // Create a new map of values, where column names are the keys
            val values = ContentValues()
            values.put(PRICE_ID_PRODUCT, model.idproduit)

            if(model.fcc_prod_id!=null){
                values.put(PRICE_ID_FCC_PRODUCT, model.fcc_prod_id)
            } else {
                values.put(PRICE_ID_FCC_PRODUCT, model.idproduit)
            }


            values.put(PRICE_UNIT_PRICE, model.unitPrice)

            if (model.datedebut != null) {
                values.put(PRICE_DATE_DEBIT, model.datedebut)
            }
            if (model.datefin != null) {
                values.put(PRICE_DATE_FIN, model.datefin)
            }
            val newRowId = mDb!!.insert(PRICE_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(PRICE_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(PRICE_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(PRICE_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun insertPriceArrayData(mesItems: List<PriceModel?>) {
        if (mesItems.isNotEmpty()) {
            for (list in mesItems) {
                this.insertPriceData(list!!)
            }
        }
    }

    fun selectionnerLastPriceByIdProduit(mIdProduit: Int): PriceModel? {
        var mPrix: PriceModel?
        val id: Int
        val idProduit: Int 
        val unitPrice: Double
        val dateDebut: String?
        val dateFin: String?
        val fcc_prod_id: Int

        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf(
            PRICE_ID,
            PRICE_ID_PRODUCT,
            PRICE_UNIT_PRICE,
            PRICE_DATE_DEBIT,
            PRICE_DATE_FIN,
            PRICE_ID_FCC_PRODUCT
        )

        val sortOrder = "$PRICE_ID DESC"
        val selection = "$PRICE_ID_PRODUCT = ?"
        val selectionArgs = arrayOf(mIdProduit.toString())
        val cursor = mDb!!.query(
            PRICE_TABLE_NAME,
            projection,
            selection,
            selectionArgs,
            null,
            null,
            sortOrder
        )
        var temp: String?
        if (cursor.moveToFirst()) {
            id = cursor.getInt(0)
            idProduit = cursor.getInt(1)
            unitPrice = cursor.getDouble(2)
            temp = cursor.getString(3)
            dateDebut = if (temp == null) {
                null
            } else {
                cursor.getString(3)
            }
            temp = cursor.getString(4)
            dateFin = if (temp == null) {
                null
            } else {
                cursor.getString(4)
            }
            fcc_prod_id = cursor.getInt(5)

            mPrix = PriceModel(
                datedebut = dateDebut,
                datefin = dateFin,
                id = id,
                idproduit = idProduit,
                fcc_prod_id = fcc_prod_id.toString(), //idProduit.toString(),
                unitPrice = unitPrice)
        } else {
            // cursor is empty
            mPrix = null
        }
        cursor.close()
        return mPrix
    } //end of public Prix selectionnerLastPriceByIdProduit(int mIdProduit)

    fun getAllpricelist(): ArrayList<PriceModel> {
        val mesItems: ArrayList<PriceModel> = ArrayList<PriceModel>()
        var id: Int
        var idProduit: Int
        var unitPrice: Double
        var dateDebut: String?
        var dateFin: String?
        var fcc_prod_id: Int

        val projection = arrayOf(
            PRICE_ID,
            PRICE_ID_PRODUCT,
            PRICE_UNIT_PRICE,
            PRICE_DATE_DEBIT,
            PRICE_DATE_FIN,
            PRICE_ID_FCC_PRODUCT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$PRICE_ID DESC"
        val cursor = mDb!!.query(
            PRICE_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            id = cursor.getInt(0)
            idProduit = cursor.getInt(1)
            unitPrice = cursor.getDouble(2)
            var temp = cursor.getString(3)
            dateDebut = if (temp == null) {
                null
            } else {
               cursor.getString(3)
            }
            temp = cursor.getString(4)
            dateFin = if (temp == null) {
                null
            } else {
                cursor.getString(4)
            }

            fcc_prod_id = cursor.getInt(5)
            mesItems.add(
                PriceModel(
                    datedebut = dateDebut,
                    datefin = dateFin,
                    id = id,
                    idproduit = idProduit,
                    fcc_prod_id = fcc_prod_id.toString(), //idProduit.toString(),
                    unitPrice = unitPrice)
            )
        }
        cursor.close()
        return mesItems
    }

    fun getProductPriceById(fccProductId: String): PriceModel? {
        var mPrix: PriceModel?
        val id: Int
        val idProduit: Int
        val unitPrice: Double
        val dateDebut: String?
        val dateFin: String?
        val fcc_prod_id: Int

        val projection = arrayOf(
            PRICE_ID,
            PRICE_ID_PRODUCT,
            PRICE_UNIT_PRICE,
            PRICE_DATE_DEBIT,
            PRICE_DATE_FIN,
            PRICE_ID_FCC_PRODUCT
        )

        val sortOrder = "$PRICE_ID DESC"
        val selection = "$PRICE_ID_PRODUCT = ?"
        val selectionArgs = arrayOf(fccProductId)
        val cursor = mDb!!.query(
            PRICE_TABLE_NAME,
            projection,
            selection,
            selectionArgs,
            null,
            null,
            sortOrder
        )
        var temp: String?
        if (cursor.moveToFirst()) {
            id = cursor.getInt(0)
            idProduit = cursor.getInt(1)
            unitPrice = cursor.getDouble(2)
            temp = cursor.getString(3)
            dateDebut = if (temp == null) {
                null
            } else {
                cursor.getString(3)
            }
            temp = cursor.getString(4)
            dateFin = if (temp == null) {
                null
            } else {
                cursor.getString(4)
            }
            fcc_prod_id = cursor.getInt(5)
            mPrix = PriceModel(
                datedebut = dateDebut,
                datefin = dateFin,
                id = id,
                idproduit = idProduit,
                fcc_prod_id = fcc_prod_id.toString(),//idProduit.toString(),
                unitPrice = unitPrice)
        } else {
            mPrix = null
        }
        cursor.close()
        return mPrix
    }

    fun updatePrice(priceModel: PriceModel){
        val values = ContentValues()
        values.put(PRICE_UNIT_PRICE,priceModel.unitPrice)
        val newRowId = mDb!!.update(PRICE_TABLE_NAME,values, "$PRICE_ID_PRODUCT = ?",arrayOf("${priceModel.idproduit}"))
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(PRICE_TABLE_NAME,values, "$PRICE_ID_PRODUCT = ?",arrayOf("${priceModel.idproduit}"))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(PRICE_TABLE_NAME,values, "$PRICE_ID_PRODUCT = ?",arrayOf("${priceModel.idproduit}"))
        }
    }
}