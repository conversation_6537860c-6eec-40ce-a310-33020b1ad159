package app.rht.petrolcard.database.baseclass
import androidx.annotation.Keep
@Keep
data class FiscalPrinterModel(
    val IPESD: String,
    val PORTESD: Int,
    val isAvailable: Boolean,
    val isOPTESD: Boolean,
    val isNonFuelProductsESD: Boolean,
    val fiscal_serial_number : String?,
    val isTIMSRequired : String?,
    val TIMS_API_URL : String?,
    val ExemptionNumber : String?,
    val customer_pin_authentication : String?,
    val invoice_buffer_time : Int?=15,
    val printerPassword : String="Password"
)