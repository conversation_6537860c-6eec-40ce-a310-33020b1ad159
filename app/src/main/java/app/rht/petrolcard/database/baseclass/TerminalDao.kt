package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.reference.model.TerminalModel
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant

import net.sqlcipher.Cursor
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteException
import java.util.*
import kotlin.collections.ArrayList

class TerminalDao(var ctx: Context? = null) : MainDataBaseClass()
{

    companion object {
         const val TERMINAL_TABLE_NAME = "terminal"
        private const val ID = "id"
        private const val TERMINAL_ID = "idTerminal"
        private const val TERMINAL_SN = "serialNumber"
        private const val TELE_COLLECT_TIME = "tele_collect_time"
        private const val TERMINAL_PAYS = "pays"

        private const val STATION_ID = "station_id"
        private const val STATION_NAME = "station_name"
        private const val STATION_ADDRESS = "station_address"
        private const val STATION_BIT = "station_bit"
        private const val STATION_TYPE = "station_type"

        private const val CITY_NAME = "city"
        private const val REGION = "region"

        private const val SECTOR_ID = "sector_id"
        private const val SECTOR_NAME = "sector_name"
        private const val SECTOR_BIT = "sector_bit"

        private const val FISCAL_ID = "fiscal_id"
        private const val MAX_TRANSACTION_AMOUNT = "max_transaction_amount"
        private const val MIN_TRANSACTION_AMOUNT = "min_transaction_amount"

        private const val SHOP_FLAG = "shop_flag" //int
        private const val PURCHASE_FLAG = "purchase_flag" //int
        private const val CANCELLATION_FLAG = "cancellation_flag" //int
        private const val UNLOCK_FLAG = "unlock_flag" //int
        private const val RECHARGE_FLAG = "recharge_flag" //int
        private const val LOCK_FLAG = "lock_flag" //int

        private const val TERMINAL_IP_CONTROLLER = "ip_controller" //String

        private const val TERMINAL_PORT_CONTROLLER = "port_controller" //String

        private const val SHOP_IP = "ip_shop" //String
        private const val SHOP_PORT = "port_shop" //String

        private const val TERMINAL_IP = "terminal_ip" //String
        private const val TERMINAL_NETMASK = "terminal_netmask" //String
        private const val TERMINAL_GATEWAY = "terminal_gateway" //String
        private const val TERMINAL_SSID = "ssid" //String
        private const val TERMINAL_WIFI_PASSWORD = "wifi_password" //String
        private const val TERMINAL_GEOFENCE = "geofence" //String
        private const val TERMINCAL_CURRENCY = "device" //String

        private const val INVENTORY_COUNT = "inventory_count" //int

        private const val CEILING_TRANSACTION = "ceiling_transaction"
        private const val CEILING_RECHARGE = "ceiling_recharge"


        const val TERMINAL_TABLE_CREATE =
            "CREATE TABLE " + TERMINAL_TABLE_NAME + " (" +
                    ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //int
                    TERMINAL_ID + " INTEGER, " +  //int
                    TERMINAL_SN + " TEXT, " +  //String
                    TELE_COLLECT_TIME + " TEXT, " +  //Date
                    MAX_TRANSACTION_AMOUNT + " INTEGER, " +  //int
                    MIN_TRANSACTION_AMOUNT + " INTEGER, " +  //int
                    TERMINAL_PAYS + " TEXT, " +  //String
                    REGION + " TEXT, " +  //String
                    CITY_NAME + " TEXT, " +  //String
                    SECTOR_NAME + " TEXT, " +  //String
                    SECTOR_ID + " INTEGER, " +  //int
                    SECTOR_BIT + " INTEGER, " +  //int
                    STATION_ID + " INTEGER, " +  //int
                    STATION_BIT + " INTEGER, " +  //int
                    STATION_TYPE + " INTEGER, " +  //int
                    STATION_NAME + " TEXT, " +  //String
                    STATION_ADDRESS + " TEXT, " +  //String
                    FISCAL_ID + " TEXT, " +  //String
                    SHOP_FLAG + " INTEGER, " +  //int
                    PURCHASE_FLAG + " INTEGER, " +  //int
                    CANCELLATION_FLAG + " INTEGER, " +  //int
                    UNLOCK_FLAG + " INTEGER, " +  //int
                    RECHARGE_FLAG + " INTEGER, " +  //int
                    LOCK_FLAG + " INTEGER, " +  //int
                    TERMINAL_IP_CONTROLLER + " TEXT, " +  //String
                    TERMINAL_PORT_CONTROLLER + " TEXT, " +  //String
                    SHOP_IP + " TEXT, " +  //String
                    SHOP_PORT + " TEXT, " +  //String
                    TERMINAL_IP + " TEXT, " +  //String
                    TERMINAL_NETMASK + " TEXT, " +  //String
                    TERMINAL_GATEWAY + " TEXT, " +  //String
                    TERMINAL_SSID + " TEXT, " +  //String
                    TERMINAL_WIFI_PASSWORD + " TEXT, " +  //String
                    INVENTORY_COUNT + " INTEGER, " +  //int
                    TERMINAL_GEOFENCE + " TEXT, " +  //String
                    TERMINCAL_CURRENCY + " TEXT, " +  //String
                    CEILING_TRANSACTION + " TEXT, " +  //String
                    CEILING_RECHARGE + " TEXT);" //String

    }

    fun clearTerminalTableData(): Int {
        val newRowId = mDb!!.delete(TERMINAL_TABLE_NAME, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(TERMINAL_TABLE_NAME, null, null)
        }.run()*/
        if (isSdCARD()) {
            mDb2!!.delete(TERMINAL_TABLE_NAME, null, null)
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(TERMINAL_TABLE_NAME, null, null)
        }
        return newRowId
    }

    fun insertTerminalData(terminal: TerminalModel): Int {
        try {
          
            val varues = ContentValues()

            varues.put(TERMINAL_ID, terminal.terminalId)
            varues.put(TERMINAL_SN, terminal.serialNumber)
            if (terminal.teleCollectTime != null) {
                varues.put(TELE_COLLECT_TIME, Support.dateToString(terminal.teleCollectTime)) }
            varues.put(MAX_TRANSACTION_AMOUNT, terminal.max_transaction_amount)
            varues.put(MIN_TRANSACTION_AMOUNT, terminal.min_transaction_amount)
            varues.put(TERMINAL_PAYS, terminal.city)

            varues.put(REGION, terminal.region)
            varues.put(CITY_NAME, terminal.city)
            varues.put(SECTOR_NAME, terminal.sectorName)
            varues.put(SECTOR_ID, terminal.sectorId)
            varues.put(SECTOR_BIT, terminal.sectorBit)
            varues.put(STATION_ID, terminal.stationId)
            varues.put(STATION_BIT, terminal.stationBit)
            varues.put(STATION_TYPE, terminal.stationType)
            varues.put(STATION_NAME, terminal.stationName)
            varues.put(STATION_ADDRESS, terminal.address)

            varues.put(FISCAL_ID, terminal.fiscalId)
            varues.put(SHOP_FLAG, terminal.shopFlag)
            varues.put(PURCHASE_FLAG, terminal.purchaseFlag)
            varues.put(CANCELLATION_FLAG, terminal.cancellationFlag)
            varues.put(UNLOCK_FLAG, terminal.unlockTerminalFlag)
            varues.put(RECHARGE_FLAG, terminal.rechargeFlag)
            varues.put(LOCK_FLAG, terminal.lockTerminalFlag)
            varues.put(TERMINAL_IP_CONTROLLER, terminal.ipController)
            varues.put(TERMINAL_PORT_CONTROLLER, terminal.portController)
            varues.put(SHOP_IP, terminal.shopIp)
            varues.put(SHOP_IP, terminal.shopIp)
            varues.put(SHOP_PORT, terminal.shopPort)
            varues.put(TERMINAL_IP, terminal.ipAddress)
            varues.put(TERMINAL_NETMASK, terminal.netMask)
            varues.put(TERMINAL_SSID, terminal.ssid)
            varues.put(TERMINAL_WIFI_PASSWORD, terminal.wifiPassword)
            varues.put(INVENTORY_COUNT, terminal.inventoryCount)
            varues.put(TERMINAL_GEOFENCE, terminal.geoFence)
            varues.put(TERMINCAL_CURRENCY, terminal.currency)
            varues.put(CEILING_TRANSACTION, terminal.maxRefillAmount)
            varues.put(CEILING_RECHARGE, terminal.maxRechargeLimit)

            val newRowId = mDb!!.insert(TERMINAL_TABLE_NAME, null, varues).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(TERMINAL_TABLE_NAME, null, varues).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(TERMINAL_TABLE_NAME, null, varues).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(TERMINAL_TABLE_NAME, null, varues).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }

    fun isTableExists(tableName: String, db : SQLiteDatabase): Boolean {
        val query = "select DISTINCT tbl_name from sqlite_master where tbl_name = '$tableName'"
        db.rawQuery(query, null).use { cursor ->
            if (cursor != null) {
                if (cursor.count > 0) {
                    return true
                }
            }
            return false
        }
    }

    fun getCurrent(): TerminalModel? {
        var mTerminal: TerminalModel? = null
        val id: Int // id dans ma base SQLite
        val idTerminal: Int
        val serialNumber: String?
        val teleCollectTime: Date?
        val maxTransactionAmount: Int
        val minTransactionAmount: Int
        val pays: String
        val region: String
        val ville: String
        val sectorName: String
        val sectorId: Int
        val sectorBit: Int
        val stationId: Int
        val stationBit: Int
        val stationType: Int
        val stationName: String
        val address: String
        val idFiscal: String?
        val shopFlag: Int
        val purchaseFlag: Int
        val cancellationFlag: Int
        val unlockTerminalFlag: Int
        val rechargeFlag: Int
        val lockTerminalFlag: Int
        val ipController: String
        val portController: String
        val shopIp: String
        val shopPort: String
        val ipAddress: String
        val netmaskTerminal: String
        val gatewayTerminal: String?
        val ssid: String
        val wifiPassword: String
        val inventoryCount: Int
        val geofence: String
        val currency: String
        val ceilingTransaction: String
        val ceilingRecharge: String
        val temp: String

        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf<String>(
            ID,
            TERMINAL_ID,
            TERMINAL_SN,
            TELE_COLLECT_TIME,
            MAX_TRANSACTION_AMOUNT,
            MIN_TRANSACTION_AMOUNT,
            TERMINAL_PAYS,
            REGION,
            CITY_NAME,
            SECTOR_NAME,
            SECTOR_ID,
            SECTOR_NAME,
            STATION_ID,
            STATION_BIT,
            STATION_TYPE,
            STATION_NAME,
            STATION_ADDRESS,
            FISCAL_ID,
            SHOP_FLAG,
            PURCHASE_FLAG,
            CANCELLATION_FLAG,
            UNLOCK_FLAG,
            RECHARGE_FLAG,
            LOCK_FLAG,
            TERMINAL_IP_CONTROLLER,
            TERMINAL_PORT_CONTROLLER,
            SHOP_IP,
            SHOP_PORT,
            TERMINAL_IP,
            TERMINAL_NETMASK,
            TERMINAL_GATEWAY,
            TERMINAL_SSID,
            TERMINAL_WIFI_PASSWORD,
            INVENTORY_COUNT,
            TERMINAL_GEOFENCE,
            TERMINCAL_CURRENCY,
            CEILING_TRANSACTION,
            CEILING_RECHARGE
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder = "$ID DESC"
        var cursor: Cursor? = null
        var count = 0
        if (mDb != null && mDb!!.isOpen) {
            try {
                cursor = mDb!!.query(
                    TERMINAL_TABLE_NAME,  // The table to query
                    projection,  // The columns to return
                    null,  // The columns for the WHERE clause
                    null,  // The varues for the WHERE clause
                    null,  // don't group the rows
                    null,  // don't filter by row groups
                    sortOrder // The sort order
                )
                if (cursor != null && !cursor.isClosed && cursor.moveToFirst()) { // the cursor is full
                    id = cursor.getInt(0) //id dans ma base SQLite
                    idTerminal = cursor.getInt(1)
                    serialNumber = cursor.getString(2)
                    temp = cursor.getString(3)
                    if (temp == null) {
                        teleCollectTime = null
                    } else {
                        teleCollectTime = Support.stringToDate(cursor.getString(3))!!
                    }
                    maxTransactionAmount = cursor.getInt(4)
                    minTransactionAmount = cursor.getInt(5)
                    pays = cursor.getString(6)
                    region = cursor.getString(7)
                    ville = cursor.getString(8)
                    sectorName = cursor.getString(9)
                    sectorId = cursor.getInt(10)
                    sectorBit = cursor.getInt(11)
                    stationId = cursor.getInt(12)
                    stationBit = cursor.getInt(13)
                    stationType = cursor.getInt(14)
                    stationName = cursor.getString(15)
                    address = cursor.getString(16)
                    idFiscal = cursor.getString(17)
                    shopFlag = cursor.getInt(18)
                    purchaseFlag = cursor.getInt(19)
                    cancellationFlag = cursor.getInt(20)
                    unlockTerminalFlag = cursor.getInt(21)
                    rechargeFlag = cursor.getInt(22)
                    lockTerminalFlag = cursor.getInt(23)
                    ipController = cursor.getString(24)
                    portController = cursor.getString(25)
                    shopIp = cursor.getString(26)
                    shopPort = cursor.getString(27)
                    ipAddress = cursor.getString(28)
                    netmaskTerminal = cursor.getString(29)
                    gatewayTerminal = cursor.getString(30)
                    ssid = cursor.getString(31)
                    wifiPassword = cursor.getString(32)
                    inventoryCount = cursor.getInt(33)
                    geofence = cursor.getString(34)
                    currency = cursor.getString(35)
                    ceilingTransaction = cursor.getString(36)
                    ceilingRecharge = cursor.getString(37)
                    mTerminal = TerminalModel(
                        id = id,
                        terminalId = idTerminal,
                        serialNumber = serialNumber,
                        teleCollectTime = teleCollectTime!!,
                        max_transaction_amount = maxTransactionAmount,
                        min_transaction_amount = minTransactionAmount,
                        pays = pays,
                        region = region,
                        city = ville,
                        sectorName = sectorName,
                        sectorId = sectorId,
                        sectorBit = sectorBit,
                        stationId = stationId,
                        stationBit = stationBit,
                        stationType = stationType,
                        stationName = stationName,
                        address = address,
                        fiscalId = idFiscal,
                        shopFlag = shopFlag,
                        purchaseFlag = purchaseFlag,
                        cancellationFlag = cancellationFlag,
                        unlockTerminalFlag = unlockTerminalFlag,
                        rechargeFlag = rechargeFlag,
                        lockTerminalFlag = lockTerminalFlag,
                        ipController = ipController,
                        portController = portController,
                        shopIp = shopIp,
                        shopPort = shopPort,
                        ipAddress = ipAddress,
                        netMask = netmaskTerminal,
                        gateway = gatewayTerminal,
                        ssid = ssid,
                        wifiPassword = wifiPassword,
                        inventoryCount = inventoryCount,
                        geoFence = geofence,
                        currency = currency,
                        maxRefillAmount = ceilingTransaction,
                        maxRechargeLimit = ceilingRecharge,
                    )
                }
            } catch (E: SQLiteException) {
                log("SQLiteAbortException", "SQLiteAbortException ---> " + E.message)
            } finally {
                if (cursor != null && !cursor.isClosed) {
                    count = cursor.count
                    cursor.close()
                }
                log("TERMINAL DAO", "count cursor ---> $count")
            }
        }
        return mTerminal
    }

    fun getTerminalById(terminalId:Int): TerminalModel? {
        var mTerminal: TerminalModel? = null
        val id: Int // id dans ma base SQLite
        val idTerminal: Int
        val serialNumber: String
        val teleCollectTime: Date?
        val maxTransactionAmount: Int
        val minTransactionAmount: Int
        val pays: String
        val region: String
        val ville: String
        val sectorName: String
        val sectorId: Int
        val sectorBit: Int
        val stationId: Int
        val stationBit: Int
        val stationType: Int
        val stationName: String
        val address: String
        val idFiscal: String?
        val shopFlag: Int
        val purchaseFlag: Int
        val cancellationFlag: Int
        val unlockTerminalFlag: Int
        val rechargeFlag: Int
        val lockTerminalFlag: Int
        val ipController: String
        val portController: String
        val shopIp: String
        val shopPort: String
        val ipAddress: String
        val netmaskTerminal: String
        val gatewayTerminal: String
        val ssid: String
        val wifiPassword: String
        val inventoryCount: Int
        val geofence: String
        val currency: String
        val ceilingTransaction: String
        val ceilingRecharge: String
        val temp: String

        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf<String>(
            TERMINAL_ID,
            TERMINAL_SN,
            TELE_COLLECT_TIME,
            MAX_TRANSACTION_AMOUNT,
            MIN_TRANSACTION_AMOUNT,
            TERMINAL_PAYS,
            REGION,
            CITY_NAME,
            SECTOR_NAME,
            SECTOR_ID,
            SECTOR_NAME,
            STATION_ID,
            STATION_BIT,
            STATION_TYPE,
            STATION_NAME,
            STATION_ADDRESS,
            FISCAL_ID,
            SHOP_FLAG,
            PURCHASE_FLAG,
            CANCELLATION_FLAG,
            UNLOCK_FLAG,
            RECHARGE_FLAG,
            LOCK_FLAG,
            TERMINAL_IP_CONTROLLER,
            TERMINAL_PORT_CONTROLLER,
            SHOP_IP,
            SHOP_PORT,
            TERMINAL_IP,
            TERMINAL_NETMASK,
            TERMINAL_GATEWAY,
            TERMINAL_SSID,
            TERMINAL_WIFI_PASSWORD,
            INVENTORY_COUNT,
            TERMINAL_GEOFENCE,
            TERMINCAL_CURRENCY
        )


        // Filter results WHERE "title" = 'My Title'
        val selection: String = "$ID = ?"
        val selectionArgs = arrayOf("$terminalId")

        var cursor: Cursor? = null
        var count = 0
        if (mDb != null && mDb!!.isOpen) {
            try {
                cursor = mDb!!.query(
                    TERMINAL_TABLE_NAME,  // The table to query
                    projection,  // The columns to return
                    selection,  // The columns for the WHERE clause
                    selectionArgs,  // The varues for the WHERE clause
                    null,  // don't group the rows
                    null,  // don't filter by row groups
                    null // The sort order
                )
                if (cursor != null && !cursor.isClosed && cursor.moveToFirst()) { // the cursor is full
                    id = cursor.getInt(0) //id dans ma base SQLite
                    idTerminal = cursor.getInt(1)
                    serialNumber = cursor.getString(2)
                    temp = cursor.getString(3)
                    if (temp == null) {
                        teleCollectTime = null
                    } else {
                        teleCollectTime = Support.stringToDate(cursor.getString(3))!!
                    }
                    maxTransactionAmount = cursor.getInt(4)
                    minTransactionAmount = cursor.getInt(5)
                    pays = cursor.getString(6)
                    region = cursor.getString(7)
                    ville = cursor.getString(8)
                    sectorName = cursor.getString(9)
                    sectorId = cursor.getInt(10)
                    sectorBit = cursor.getInt(11)
                    stationId = cursor.getInt(12)
                    stationBit = cursor.getInt(13)
                    stationType = cursor.getInt(14)
                    stationName = cursor.getString(15)
                    address = cursor.getString(16)
                    idFiscal = cursor.getString(17)
                    shopFlag = cursor.getInt(18)
                    purchaseFlag = cursor.getInt(19)
                    cancellationFlag = cursor.getInt(20)
                    unlockTerminalFlag = cursor.getInt(21)
                    rechargeFlag = cursor.getInt(22)
                    lockTerminalFlag = cursor.getInt(23)
                    ipController = cursor.getString(24)
                    portController = cursor.getString(25)
                    shopIp = cursor.getString(26)
                    shopPort = cursor.getString(27)
                    ipAddress = cursor.getString(28)
                    netmaskTerminal = cursor.getString(29)
                    gatewayTerminal = cursor.getString(30)
                    ssid = cursor.getString(31)
                    wifiPassword = cursor.getString(32)
                    inventoryCount = cursor.getInt(33)
                    geofence = cursor.getString(34)
                    currency = cursor.getString(35)
                    ceilingTransaction = cursor.getString(36)
                    ceilingRecharge = cursor.getString(37)
                    mTerminal = TerminalModel(
                        id = id,
                        terminalId = idTerminal,
                        serialNumber = serialNumber,
                        teleCollectTime = teleCollectTime!!,
                        max_transaction_amount = maxTransactionAmount,
                        min_transaction_amount = minTransactionAmount,
                        pays = pays,
                        region = region,
                        city = ville,
                        sectorName = sectorName,
                        sectorId = sectorId,
                        sectorBit = sectorBit,
                        stationId = stationId,
                        stationBit = stationBit,
                        stationType = stationType,
                        stationName = stationName,
                        address = address,
                        fiscalId = idFiscal,
                        shopFlag = shopFlag,
                        purchaseFlag = purchaseFlag,
                        cancellationFlag = cancellationFlag,
                        unlockTerminalFlag = unlockTerminalFlag,
                        rechargeFlag = rechargeFlag,
                        lockTerminalFlag = lockTerminalFlag,
                        ipController = ipController,
                        portController = portController,
                        shopIp = shopIp,
                        shopPort = shopPort,
                        ipAddress = ipAddress,
                        netMask = netmaskTerminal,
                        gateway = gatewayTerminal,
                        ssid = ssid,
                        wifiPassword = wifiPassword,
                        inventoryCount = inventoryCount,
                        geoFence = geofence,
                        currency = currency,
                        maxRefillAmount = ceilingTransaction,
                        maxRechargeLimit = ceilingRecharge
                    )
                }
            } catch (E: SQLiteException) {
                log("SQLiteAbortException", "SQLiteAbortException ---> " + E.message)
            } finally {
                if (cursor != null && !cursor.isClosed) {
                    count = cursor.count
                    cursor.close()
                }
                log("TERMINAL DAO", "count cursor ---> $count")
            }
        }
        return mTerminal
    }

    fun getTerminalList(): List<TerminalModel>? {
        val terminalList: ArrayList<TerminalModel> = ArrayList<TerminalModel>()
        var mTerminal: TerminalModel? = null
        var id: Int // id dans ma base SQLite
        var idTerminal: Int
        var serialNumber: String
        var teleCollectTime: Date?
        var maxTransactionAmount: Int
        var minTransactionAmount: Int
        var pays: String
        var region: String
        var ville: String
        var sectorName: String
        var sectorId: Int
        var sectorBit: Int
        var stationId: Int
        var stationBit: Int
        var stationType: Int
        var stationName: String
        var address: String
        var idFiscal: String?
        var shopFlag: Int
        var purchaseFlag: Int
        var cancellationFlag: Int
        var unlockTerminalFlag: Int
        var rechargeFlag: Int
        var lockTerminalFlag: Int
        var ipController: String
        var portController: String
        var shopIp: String
        var shopPort: String
        var ipAddress: String
        var netmaskTerminal: String
        var gatewayTerminal: String
        var ssid: String
        var wifiPassword: String
        var inventoryCount: Int
        var geofence: String
        var currency: String
        var ceilingTransaction: String
        var ceilingRecharge: String
        var temp: String

        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf(
            TERMINAL_ID,
            TERMINAL_SN,
            TELE_COLLECT_TIME,
            MAX_TRANSACTION_AMOUNT,
            MIN_TRANSACTION_AMOUNT,
            TERMINAL_PAYS,
            REGION,
            CITY_NAME,
            SECTOR_NAME,
            SECTOR_ID,
            SECTOR_NAME,
            STATION_ID,
            STATION_BIT,
            STATION_TYPE,
            STATION_NAME,
            STATION_ADDRESS,
            FISCAL_ID,
            SHOP_FLAG,
            PURCHASE_FLAG,
            CANCELLATION_FLAG,
            UNLOCK_FLAG,
            RECHARGE_FLAG,
            LOCK_FLAG,
            TERMINAL_IP_CONTROLLER,
            TERMINAL_PORT_CONTROLLER,
            SHOP_IP,
            SHOP_PORT,
            TERMINAL_IP,
            TERMINAL_NETMASK,
            TERMINAL_GATEWAY,
            TERMINAL_SSID,
            TERMINAL_WIFI_PASSWORD,
            INVENTORY_COUNT,
            TERMINAL_GEOFENCE,
            TERMINCAL_CURRENCY
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$ID DESC"
        val cursor = mDb!!.query(
            TERMINAL_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The varues for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            id = cursor.getInt(0) //id dans ma base SQLite
            idTerminal = cursor.getInt(1)
            serialNumber = cursor.getString(2)
            temp = cursor.getString(3)
            teleCollectTime = if (temp == null) {
                null
            } else {
                Support.stringToDate(cursor.getString(3))!!
            }
            maxTransactionAmount = cursor.getInt(4)
            minTransactionAmount = cursor.getInt(5)
            pays = cursor.getString(6)
            region = cursor.getString(7)
            ville = cursor.getString(8)
            sectorName = cursor.getString(9)
            sectorId = cursor.getInt(10)
            sectorBit = cursor.getInt(11)
            stationId = cursor.getInt(12)
            stationBit = cursor.getInt(13)
            stationType = cursor.getInt(14)
            stationName = cursor.getString(15)
            address = cursor.getString(16)
            idFiscal = cursor.getString(17)
            shopFlag = cursor.getInt(18)
            purchaseFlag = cursor.getInt(19)
            cancellationFlag = cursor.getInt(20)
            unlockTerminalFlag = cursor.getInt(21)
            rechargeFlag = cursor.getInt(22)
            lockTerminalFlag = cursor.getInt(23)
            ipController = cursor.getString(24)
            portController = cursor.getString(25)
            shopIp = cursor.getString(26)
            shopPort = cursor.getString(27)
            ipAddress = cursor.getString(28)
            netmaskTerminal = cursor.getString(29)
            gatewayTerminal = cursor.getString(30)
            ssid = cursor.getString(31)
            wifiPassword = cursor.getString(32)
            inventoryCount = cursor.getInt(33)
            geofence = cursor.getString(34)
            currency = cursor.getString(35)
            ceilingTransaction = cursor.getString(36)
            ceilingRecharge = cursor.getString(37)
            mTerminal = TerminalModel(
                id = id,
                terminalId = idTerminal,
                serialNumber = serialNumber,
                teleCollectTime = teleCollectTime!!,
                max_transaction_amount = maxTransactionAmount,
                min_transaction_amount = minTransactionAmount,
                pays = pays,
                region = region,
                city = ville,
                sectorName = sectorName,
                sectorId = sectorId,
                sectorBit = sectorBit,
                stationId = stationId,
                stationBit = stationBit,
                stationType = stationType,
                stationName = stationName,
                address = address,
                fiscalId = idFiscal,
                shopFlag = shopFlag,
                purchaseFlag = purchaseFlag,
                cancellationFlag = cancellationFlag,
                unlockTerminalFlag = unlockTerminalFlag,
                rechargeFlag = rechargeFlag,
                lockTerminalFlag = lockTerminalFlag,
                ipController = ipController,
                portController = portController,
                shopIp = shopIp,
                shopPort = shopPort,
                ipAddress = ipAddress,
                netMask = netmaskTerminal,
                gateway = gatewayTerminal,
                ssid = ssid,
                wifiPassword = wifiPassword,
                inventoryCount = inventoryCount,
                geoFence = geofence,
                currency = currency,
                maxRefillAmount = ceilingTransaction,
                maxRechargeLimit = ceilingRecharge,
            )

            terminalList.add(mTerminal)
        }
        cursor.close()
        return terminalList
    }


}