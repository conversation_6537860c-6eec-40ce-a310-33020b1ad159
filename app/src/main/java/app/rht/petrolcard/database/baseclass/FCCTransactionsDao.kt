package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.database.Cursor
import android.util.Log
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc
import app.rht.petrolcard.utils.constant.AppConstant
import java.lang.String
import java.util.ArrayList
import java.util.*
import kotlin.Boolean
import kotlin.Exception
import kotlin.Int
import kotlin.arrayOf

class FCCTransactionsDao : MainDataBaseClass() {

    companion object {
        val TAG = FCCTransactionsDao::class.java.simpleName
        const val FCC_TRANSACTION_TABLE_NAME = "tbl_fcc_transaction"

        const val ID = "id" //int
        const val REFERENCE_TRANSACTION_ID = "reference_transaction_id"
        const val PUMP_NO = "pump_no"
        const val TRANSACTION_STATUS = "payment_status" // 1 - Paid 0 - un Paid
        const val TRANSACTION_AMOUNT = "amount" // 1 - Paid 0 - un Paid
        const val TRANSACTION_DATE = "dh_transaction" // 1 - Paid 0 - un Paid
        const val TRANSACTION_HOSE = "hose" // 1 - Paid 0 - un Paid
        const val TRANSACTION_PRODUCT_NAME = "product_name" // 1 - Paid 0 - un Paid
        const val TRANSACTION_UNIT_PRICE = "unit_price" // 1 - Paid 0 - un Paid
        const val TRANSACTION_QTY = "qty" // 1 - Paid 0 - un Paid
        const val TRANSACTION_PRODUCT_ID = "product_id" // 1 - Paid 0 - un Paid
        const val TRANSACTION_CURRENCY = "currency" // 1 - Paid 0 - un Paid
        const val HS_CODE = "hsCode"
        const val FUEL_QTY_UNIT = "fuelQtyUnit"
        const val VAT_AMOUNT = "vatAmount"
        const val TRANSACTION_FCC_PRODUCT_ID = "fccProductId"
        const val TRANSACTION_FUSION_SALE_ID = "fusionSaleId"
        const val TRANSACTION_RELEASE_TOKEN = "releaseToken"
        const val TRANSACTION_TIME_STAMP = "transactionTimeStamp"

        const val TRANSACTION_STATUS_TABLE_CREATE = "CREATE TABLE " + FCC_TRANSACTION_TABLE_NAME + " (" +
                ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                REFERENCE_TRANSACTION_ID + " TEXT, " +
                PUMP_NO + " TEXT, " +
                TRANSACTION_AMOUNT + " TEXT, " +
                TRANSACTION_DATE + " TEXT, " +
                TRANSACTION_HOSE + " TEXT, " +
                TRANSACTION_PRODUCT_NAME + " TEXT, " +
                TRANSACTION_UNIT_PRICE + " TEXT, " +
                TRANSACTION_QTY + " TEXT, " +
                TRANSACTION_PRODUCT_ID + " TEXT, " +
                TRANSACTION_STATUS + " INTEGER, " +
                TRANSACTION_CURRENCY + " TEXT, " +
                HS_CODE + " TEXT, " +
                FUEL_QTY_UNIT + " TEXT, " +
                VAT_AMOUNT + " TEXT, " +
                TRANSACTION_FCC_PRODUCT_ID + " INT, " +
                TRANSACTION_FUSION_SALE_ID + " TEXT, " +
                TRANSACTION_RELEASE_TOKEN + " TEXT, " +
                TRANSACTION_TIME_STAMP + " TEXT) ;"

    }

    fun insert(transaction: TransactionFromFcc): Int {
        // Create a new map of values, where column names are the keys
        val pValues = ContentValues()
        pValues.put(REFERENCE_TRANSACTION_ID, transaction.ref_transaction)
        pValues.put(PUMP_NO, transaction.pump)
        pValues.put(TRANSACTION_AMOUNT, transaction.amount)
        pValues.put(TRANSACTION_DATE, transaction.dh_transaction)
        pValues.put(TRANSACTION_HOSE, transaction.hose)
        pValues.put(TRANSACTION_PRODUCT_NAME, transaction.produit)
        pValues.put(TRANSACTION_UNIT_PRICE, transaction.pu)
        pValues.put(TRANSACTION_QTY, transaction.quantite)
        pValues.put(TRANSACTION_PRODUCT_ID, transaction.productId)
        pValues.put(TRANSACTION_STATUS, transaction.transactionStatus)
        pValues.put(TRANSACTION_CURRENCY, transaction.currency)
        pValues.put(HS_CODE, transaction.hsCode)
        pValues.put(FUEL_QTY_UNIT, transaction.fuelQtyUnit)
        pValues.put(VAT_AMOUNT, transaction.vatAmount)
//        pValues.put(TRANSACTION_SIGNED_STATUS, transaction.signedStatus)
        pValues.put(TRANSACTION_FCC_PRODUCT_ID, transaction.fccProductId)
        pValues.put(TRANSACTION_FUSION_SALE_ID, transaction.fusionSaleId)
        pValues.put(TRANSACTION_RELEASE_TOKEN, transaction.releaseToken)
        pValues.put(TRANSACTION_TIME_STAMP,transaction.transactionTimestamp)
        var newRowId = mDb!!.update(FCC_TRANSACTION_TABLE_NAME, pValues, "$TRANSACTION_FUSION_SALE_ID = ?", arrayOf(transaction.fusionSaleId))
        /*Thread{
            val backupRowId = backupDb!!.update(FCC_TRANSACTION_TABLE_NAME, pValues, "$REFERENCE_TRANSACTION_ID = ?", arrayOf(transaction.ref_transaction))
        }.run()*/
        if (newRowId <= 0) {
            newRowId = mDb!!.insert(FCC_TRANSACTION_TABLE_NAME, null, pValues).toInt()

            if( isSdCARD() && mDb2!=null) {
                mDb2!!.insert(FCC_TRANSACTION_TABLE_NAME, null, pValues)
            }
            if(AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3!=null) {
                mDb3!!.insert(FCC_TRANSACTION_TABLE_NAME, null, pValues)
            }
        }
        return newRowId
    }

    fun getAllTransactionList(): ArrayList<TransactionFromFcc> {
        val transactionFromFccs: ArrayList<TransactionFromFcc> = ArrayList<TransactionFromFcc>()
        val selectQuery = "SELECT  * FROM $FCC_TRANSACTION_TABLE_NAME WHERE $TRANSACTION_STATUS = '0'"
        val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
        if (cursor.moveToFirst()) {
            do {
                if(getItems(cursor) != null)
                {
                    val transactionFromFcc = getItems(cursor)
                    transactionFromFccs.add(transactionFromFcc!!)
                }

            } while (cursor.moveToNext())
        }
        cursor.close()
        return transactionFromFccs
    }

    private fun getItems(cursor: Cursor): TransactionFromFcc? {
        var transactionFromFcc:TransactionFromFcc?=null
        if(cursor.getDouble(8) != 0.0) {
            transactionFromFcc = TransactionFromFcc()
            transactionFromFcc.id = cursor.getInt(0)
            transactionFromFcc.ref_transaction = cursor.getString(1)
            transactionFromFcc.pump = cursor.getInt(2)
            transactionFromFcc.amount = cursor.getString(3).toDouble()
            transactionFromFcc.dh_transaction = cursor.getString(4)
            transactionFromFcc.hose = cursor.getInt(5)
            transactionFromFcc.produit = cursor.getString(6)
            transactionFromFcc.pu = cursor.getInt(7).toDouble()
            transactionFromFcc.quantite = cursor.getString(8).toDouble()
            transactionFromFcc.productId = cursor.getString(9).toInt()
            transactionFromFcc.transactionStatus = cursor.getInt(10)
            transactionFromFcc.currency = cursor.getString(11)
            transactionFromFcc.hsCode = cursor.getString(12)
            transactionFromFcc.fuelQtyUnit = cursor.getString(13)
            transactionFromFcc.vatAmount = cursor.getString(14)
            transactionFromFcc.fccProductId = cursor.getInt(15)
            transactionFromFcc.fusionSaleId = cursor.getString(16)
            transactionFromFcc.releaseToken = cursor.getString(17)
            transactionFromFcc.transactionTimestamp = cursor.getString(18)
        }
        return transactionFromFcc
    }

    fun isTransactionAvailable(fusionSaleId: kotlin.String): Int? {
        val selectQuery = "SELECT * FROM $FCC_TRANSACTION_TABLE_NAME WHERE $TRANSACTION_FUSION_SALE_ID = '$fusionSaleId' "
        val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
        val count = cursor.count
        cursor.close()
        return count
    }
    fun getPumpUnsignedTransaction(pumpNo: Int): TransactionFromFcc? {
        var transactionFromFcc: TransactionFromFcc? = null
        try {
            val selectQuery = "SELECT  * FROM $FCC_TRANSACTION_TABLE_NAME WHERE $TRANSACTION_STATUS = 0 AND $PUMP_NO = $pumpNo ORDER BY $TRANSACTION_DATE ASC LIMIT 1"
            val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
            if (cursor.moveToFirst()) {
                do {
                     transactionFromFcc = getItems(cursor)
                } while (cursor.moveToNext())
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return transactionFromFcc
    }
    fun checkTransactionCountPerPump(pumpNo:Int): Int {
        var count= 0
        try {
            val selectQuery = "SELECT  * FROM $FCC_TRANSACTION_TABLE_NAME WHERE $TRANSACTION_STATUS = '0' AND $PUMP_NO = $pumpNo"
            var cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
            count = cursor.count

            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return count
    }

    fun deleteTransactionFusion(saleID:kotlin.String): Int {
        val newRowId = mDb!!.delete(FCC_TRANSACTION_TABLE_NAME,"$TRANSACTION_FUSION_SALE_ID = ?", arrayOf(saleID))
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            val newRowId3 = mDb3!!.delete(FCC_TRANSACTION_TABLE_NAME, "$TRANSACTION_FUSION_SALE_ID = ?", arrayOf(saleID))
        }
        return newRowId
    }
    fun deleteTransactionFuelpos(refrence_no:kotlin.String): Int {
        val newRowId = mDb!!.delete(FCC_TRANSACTION_TABLE_NAME,"$REFERENCE_TRANSACTION_ID = ?", arrayOf(refrence_no))
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            val newRowId3 = mDb3!!.delete(FCC_TRANSACTION_TABLE_NAME, "$REFERENCE_TRANSACTION_ID = ?", arrayOf(refrence_no))
        }
        return newRowId
    }

}