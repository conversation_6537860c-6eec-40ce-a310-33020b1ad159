package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.reference.model.BlackListModel
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.Cursor
import net.sqlcipher.database.SQLiteException

class BlackListDao : MainDataBaseClass()
{
    companion object {
         val BLACKLIST_TABLE_NAME = "blackList"
        private val BLACKLIST_ID = "id"
        private val BLACKLIST_PAN = "pan"
        private val BLACKLIST_CARD_ID = "cardId"
        private val BLACKLIST_ID_MOTIF = "idMotif"
        private val BLACKLIST_OPTION_STRING = "optString1"
        private val BLACKLIST_OPTION_INT = "optInt1"


        val BLACKLIST_TABLE_CREATE = "CREATE TABLE " + BLACKLIST_TABLE_NAME + " (" +
                BLACKLIST_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +   // 0
                BLACKLIST_PAN + " TEXT, " +                               // 1
                BLACKLIST_CARD_ID + " TEXT, " +                           //
                BLACKLIST_ID_MOTIF + " INTEGER, " +                       //
                BLACKLIST_OPTION_STRING + " INTEGER, " +                  //
                BLACKLIST_OPTION_INT + " TEXT);"                      //
    }

    fun clearBlackListData(): Int {
        val newRowId = mDb!!.delete(BLACKLIST_TABLE_NAME, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(BLACKLIST_TABLE_NAME, null, null)
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.delete(BLACKLIST_TABLE_NAME, null, null)
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(BLACKLIST_TABLE_NAME, null, null)
        }
        return newRowId
    }
    private fun insertBlackListData(model: BlackListModel): Int {
        try {
            val values = ContentValues()
            values.put(BLACKLIST_ID, model.id)
            values.put(BLACKLIST_PAN, model.pan)
            values.put(BLACKLIST_CARD_ID, model.idCarte)
            values.put(BLACKLIST_ID_MOTIF, model.idMotif)
            values.put(BLACKLIST_OPTION_STRING, model.optString1)
            values.put(BLACKLIST_OPTION_INT, model.optInt1)

            val newRowId = mDb!!.insert(BLACKLIST_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(BLACKLIST_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(BLACKLIST_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(BLACKLIST_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun insertBlackListArrayData(mesItems: List<BlackListModel?>) {
        if (mesItems.isNotEmpty()) {
            for (blacklist in mesItems) {
                this.insertBlackListData(blacklist!!)
            }
        }
    }
    fun selectionnerByPan(mPan: String): BlackListModel? {
        var mListeNoire: BlackListModel? = null
        val id: Int? // id dans ma base SQLite
        val pan: String? // String
        val idCarte: String? // int
        val idMotif: String? // int
        val optString1: String? // String
        val optInt1: String? // int

        val projection = arrayOf(
            BLACKLIST_ID,
            BLACKLIST_PAN,
            BLACKLIST_CARD_ID,
            BLACKLIST_ID_MOTIF,
            BLACKLIST_OPTION_STRING,
            BLACKLIST_OPTION_INT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$BLACKLIST_ID DESC"

        // Filter results WHERE "title" = 'My Title'
        val selection: String = "$BLACKLIST_PAN = ?"
        val selectionArgs = arrayOf(mPan)
        var cursor: Cursor? = null
        var count = 0
        if (mDb != null && mDb!!.isOpen) {
            try {
                cursor = mDb!!.query(
                    BLACKLIST_TABLE_NAME,  // The table to query
                    projection,  // The columns to return
                    selection,  // The columns for the WHERE clause
                    selectionArgs,  // The values for the WHERE clause
                    null,  // don't group the rows
                    null,  // don't filter by row groups
                    sortOrder // The sort order
                )
                if (cursor != null && !cursor.isClosed && cursor.moveToFirst()) { // le curseur est plein
                    id = cursor.getInt(0) // id dans ma base SQLite
                    pan = cursor.getString(1)
                    idCarte = cursor.getString(2)
                    idMotif = cursor.getString(3)
                    optString1 = cursor.getString(4)
                    optInt1 = cursor.getString(5)
                    mListeNoire = BlackListModel(id, pan, idCarte, idMotif, optString1, optInt1)
                } else {
                    // cursor is empty
                    mListeNoire = null
                }
            } catch (E: SQLiteException) {
                log("SQLiteAbortException", "SQLiteAbortException ---> " + E.message)
            } finally {
                if (cursor != null && !cursor.isClosed) {
                    count = cursor.count
                    cursor.close()
                }
                log("LISTENOIRE DAO", "count cursor ---> $count")
            }
        }
        return mListeNoire
    } //public ListeNoire selectionnerByPan(String mPan)


}