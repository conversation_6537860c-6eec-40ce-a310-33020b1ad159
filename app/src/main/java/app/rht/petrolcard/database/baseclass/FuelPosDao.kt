package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.reference.model.FuelPOSModel
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.Cursor
import net.sqlcipher.database.SQLiteException
import java.util.ArrayList

class FuelPosDao : MainDataBaseClass()
{

    companion object {
         const val FUEL_POS_TABLE_NAME = "fuelpos"
        private const val FUEL_POS_TABLE_ID = "id"
        private const val FUEL_POS_EXIST = "pos_exist"
        private const val FUEL_POS_ID = "pos_id"
        private const val FUEL_POS_IP = "pos_ip"
        private const val FUEL_POS_TPE_MASTER = "pos_tpe_master"
        private const val FUEL_POS_FTP_USER = "pos_ftp_user"
        private const val FUEL_POS_FTP_PWD = "pos_ftp_pwd"
        private const val FUEL_POS_TCP_USER = "pos_tcp_user"
        private const val FUEL_POS_TCP_PWD = "pos_tcp_pwd"
        private const val FUEL_POS_PORT = "pos_ftp_port"
        private const val FUEL_POS_FTP_RPATH = "pos_ftp_r_path"
        private const val FUEL_POS_FTP_WPATH = "pos_ftp_w_path"
        private const val FUEL_POS_SCHEDULE = "pos_ftp_schedule"
        private const val FUEL_POS_DELETE_FILES = "pos_delete_files"
        private const val FUEL_POS_TCP_PORT = "pos_tcp_port"
        private const val FUEL_POS_TCP_PORT_LISTNER = "pos_tcp_port_listner"
        private const val FUELPOST_TRANSACTION_DATA_TIMEOUT = "transactionDataTimeout"

        const val FUEL_POS_TABLE_CREATE =
            "CREATE TABLE " + FUEL_POS_TABLE_NAME + " (" +
                    FUEL_POS_TABLE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //int
                    FUEL_POS_ID + " TEXT, " +  //String
                    FUEL_POS_IP + " TEXT, " +  //String
                    FUEL_POS_EXIST + " INTEGER, " +  //Boolean
                    FUEL_POS_TPE_MASTER + " INTEGER, " +  //Boolean
                    FUEL_POS_FTP_USER + " TEXT, " +  //int
                    FUEL_POS_FTP_PWD + " TEXT, " +  //String
                    FUEL_POS_TCP_USER + " TEXT, " +  //String
                    FUEL_POS_TCP_PWD + " TEXT, " +  //String
                    FUEL_POS_PORT + " INTEGER, " +  //int
                    FUEL_POS_FTP_RPATH + " TEXT, " +  //String
                    FUEL_POS_FTP_WPATH + " TEXT, " +  //String
                    FUEL_POS_SCHEDULE + " INTEGER, " +  //int
                    FUEL_POS_DELETE_FILES + " INTEGER," +
                    FUEL_POS_TCP_PORT + " INTEGER, " +  //int
                    FUEL_POS_TCP_PORT_LISTNER + " INTEGER, " +//Boolean
                    FUELPOST_TRANSACTION_DATA_TIMEOUT + " TEXT)" //Boolean
    }

    fun clearFuelPosTableData(): Int {
        val newRowId = mDb!!.delete(FUEL_POS_TABLE_NAME, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(FUEL_POS_TABLE_NAME, null, null)
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.delete(FUEL_POS_TABLE_NAME, null, null)
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(FUEL_POS_TABLE_NAME, null, null)
        }
        return newRowId
    }
    fun insertFuelPosData(model: FuelPOSModel): Int {
        try {
            val values = ContentValues()
            values.put(FUEL_POS_ID, model.id)
            values.put(FUEL_POS_IP, model.ipAddress)
            values.put(FUEL_POS_EXIST, model.isExist)
            values.put(FUEL_POS_TPE_MASTER, model.tpeMaster)
            values.put(FUEL_POS_FTP_USER, model.ftpUser)
            values.put(FUEL_POS_FTP_PWD, model.ftpPass)
            values.put(FUEL_POS_TCP_USER, model.tcpUser)
            values.put(FUEL_POS_TCP_PWD, model.tcpPass)
            values.put(FUEL_POS_PORT, model.ftpPort)
            values.put(FUEL_POS_FTP_RPATH, model.ftpReadPath)
            values.put(FUEL_POS_FTP_WPATH, model.ftpWritePath)
            values.put(FUEL_POS_SCHEDULE, model.jobSchedule)
            values.put(FUEL_POS_DELETE_FILES, model.deleteFiles)
            values.put(FUEL_POS_TCP_PORT, model.tcpPort)
            values.put(FUEL_POS_TCP_PORT_LISTNER, model.tcpListener)
            values.put(FUELPOST_TRANSACTION_DATA_TIMEOUT, model.transactionDataTimeout)

            val newRowId = mDb!!.insert(FUEL_POS_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(FUEL_POS_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(FUEL_POS_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(FUEL_POS_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun insertFuelPosArrayData(mesItems: List<FuelPOSModel?>) {
        if (mesItems.isNotEmpty()) {
            for (fuelPosList in mesItems) {
                this.insertFuelPosData(fuelPosList!!)
            }
        }
    }

    fun getCurrent(): FuelPOSModel? {
        var fuelPos: FuelPOSModel? = null
        val id: Int // id dans ma base SQLite
        val fuelPosID: String?
        val fuelPosIP: String?
        val fuelPosExist: Int
        val fuelPosMaster: Int
        val ftpUser: String?
        val ftpPwd: String?
        val tcpUser: String?
        val tcpPwd: String?
        val port: Int
        val ftpRpath: String?
        val ftpWpath: String?
        val jobScheduel: Int
        val deletefILES: Int
        val portTcp: Int //tcp
        val porTcpL: Int // tcp listner
        var temp: String?
        var transactionDataTimeout: String?

        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf(
            FUEL_POS_TABLE_ID,
            FUEL_POS_ID,
            FUEL_POS_IP,
            FUEL_POS_EXIST,
            FUEL_POS_TPE_MASTER,
            FUEL_POS_FTP_USER,
            FUEL_POS_FTP_PWD,
            FUEL_POS_TCP_USER,
            FUEL_POS_TCP_PWD,
            FUEL_POS_PORT,
            FUEL_POS_FTP_RPATH,
            FUEL_POS_FTP_WPATH,
            FUEL_POS_SCHEDULE,
            FUEL_POS_DELETE_FILES,
            FUEL_POS_TCP_PORT,
            FUEL_POS_TCP_PORT_LISTNER,
            FUELPOST_TRANSACTION_DATA_TIMEOUT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$FUEL_POS_ID DESC"
        var cursor: Cursor? = null
        var count = 0
        if (mDb != null && mDb!!.isOpen) {
            try {
                cursor = mDb!!.query(
                    FUEL_POS_TABLE_NAME,  // The table to query
                    projection,  // The columns to return
                    null,  // The columns for the WHERE clause
                    null,  // The values for the WHERE clause
                    null,  // don't group the rows
                    null,  // don't filter by row groups
                    sortOrder // The sort order
                )
                if (cursor != null && !cursor.isClosed && cursor.moveToFirst()) { // le curseur est plein
                    id = cursor.getInt(0) // id in SQLite database
                    fuelPosID = cursor.getString(1)
                    fuelPosIP = cursor.getString(2)
                    fuelPosExist = cursor.getInt(3)
                    fuelPosMaster = cursor.getInt(4)
                    ftpUser = cursor.getString(5)
                    ftpPwd = cursor.getString(6)
                    tcpUser = cursor.getString(7)
                    tcpPwd = cursor.getString(8)
                    port = cursor.getInt(9)
                    ftpRpath = cursor.getString(10)
                    ftpWpath = cursor.getString(11)
                    jobScheduel = cursor.getInt(12)
                    deletefILES = cursor.getInt(13)
                    portTcp = cursor.getInt(14) //tcp
                    porTcpL = cursor.getInt(15) // tcp listner
                    transactionDataTimeout = cursor.getString(16) // tcp listner

                    fuelPos = FuelPOSModel(
                        isExist = (fuelPosExist == 1),
                        id =  fuelPosID,
                        ipAddress = fuelPosIP,
                        tpeMaster = fuelPosMaster == 1,
                        ftpUser = ftpUser,
                        ftpPass = ftpPwd,
                        tcpUser = tcpUser,
                        tcpPass = tcpPwd,
                        tcpPort = portTcp,
                        tcpListener = porTcpL,
                        ftpPort = port,
                        ftpReadPath = ftpRpath,
                        ftpWritePath = ftpWpath,
                        jobSchedule = jobScheduel,
                        deleteFiles = deletefILES == 1,
                        decimal = 1,
                        totalAmountDecimal = 1,
                        quantityDecimal = 1,
                        unitPriceDecimal = 1,
                        transactionDataTimeout = transactionDataTimeout
                    )
                }
            } catch (E: SQLiteException) {
                log("SQLiteAbortException", "SQLiteAbortException ---> " + E.message)
            } finally {
                if (cursor != null && !cursor.isClosed) {
                    count = cursor.count
                    cursor.close()
                }
                log("FUELPOS DAO", "count cursor ---> $count")
            }
        }
        return fuelPos
    }

    fun getAll(): List<FuelPOSModel>? {
        val mesItems: MutableList<FuelPOSModel> = ArrayList<FuelPOSModel>()
        var id: Int // id dans ma base SQLite
        var fuelPosID: String
        var fuelPosIP: String
        var fuelPosExist: Int
        var fuelPosMaster: Int
        var ftpUser: String
        var ftpPwd: String
        var tcpUser: String
        var tcpPwd: String
        var port: Int // ftp
        var ftpRpath: String
        var ftpWpath: String
        var jobScheduel: Int
        var deletefILES: Int
        var portTcp: Int //tcp
        var porTcpL: Int // tcp listner
        var transactionDataTimeout: String // tcp listner


        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf<String>(
            FUEL_POS_TABLE_ID,
            FUEL_POS_ID,
            FUEL_POS_IP,
            FUEL_POS_EXIST,
            FUEL_POS_TPE_MASTER,
            FUEL_POS_FTP_USER,
            FUEL_POS_FTP_PWD,
            FUEL_POS_TCP_USER,
            FUEL_POS_TCP_PWD,
            FUEL_POS_PORT,
            FUEL_POS_FTP_RPATH,
            FUEL_POS_FTP_WPATH,
            FUEL_POS_SCHEDULE,
            FUEL_POS_DELETE_FILES,
            FUEL_POS_TCP_PORT,
            FUEL_POS_TCP_PORT_LISTNER,
            FUELPOST_TRANSACTION_DATA_TIMEOUT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$FUEL_POS_ID DESC"
        val cursor = mDb!!.query(
            FUEL_POS_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            id = cursor.getInt(0) //id dans ma base SQLite
            fuelPosID = cursor.getString(1)
            fuelPosIP = cursor.getString(2)
            fuelPosExist = cursor.getInt(3)
            fuelPosMaster = cursor.getInt(4)
            ftpUser = cursor.getString(5)
            ftpPwd = cursor.getString(6)
            tcpUser = cursor.getString(7)
            tcpPwd = cursor.getString(8)
            port = cursor.getInt(9)
            ftpRpath = cursor.getString(10)
            ftpWpath = cursor.getString(11)
            jobScheduel = cursor.getInt(12)
            deletefILES = cursor.getInt(13)
            portTcp = cursor.getInt(14) //tcp
            porTcpL = cursor.getInt(15) // tcp listner
            transactionDataTimeout = cursor.getString(16) // tcp listner

            mesItems.add(
                FuelPOSModel(
                    isExist = fuelPosExist == 1,
                    id =  fuelPosID,
                    ipAddress = fuelPosIP,
                    tpeMaster = fuelPosMaster == 1,
                    ftpUser = ftpUser,
                    ftpPass = ftpPwd,
                    tcpUser = tcpUser,
                    tcpPass = tcpPwd,
                    tcpPort = portTcp,
                    tcpListener = porTcpL,
                    ftpPort = port,
                    ftpReadPath = ftpRpath,
                    ftpWritePath = ftpWpath,
                    jobSchedule = jobScheduel,
                    deleteFiles = deletefILES == 1,
                    decimal = 1,
                    totalAmountDecimal = 1,
                    quantityDecimal = 1,
                    unitPriceDecimal = 1,
                    transactionDataTimeout = transactionDataTimeout
                )
            )
        }
        cursor.close()
        return mesItems
    }

}