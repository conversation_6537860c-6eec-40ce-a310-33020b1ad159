package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.reference.model.GasStationAttendantModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.ATTENDANT_ROLE
import net.sqlcipher.database.SQLiteException
import java.util.*
import kotlin.collections.ArrayList

class UsersDao : MainDataBaseClass()
{

    companion object {
         val USERS_TABLE_NAME = "users"
        private val USERS_ID = "id"
        private val USERS_ID_USERS = "idUser"
        private val USERS_ID_ROLE = "idRole"
        private val USERS_CODE = "code"
        private val USERS_AUTHENTICATION_METHOD = "authenticationMethod"
        private val USERS_AUTHENTICATION_KEY = "authenticationKey"
        private val USERS_E_BUTTON = "eButton"
        //private val USERS_FULL_NAME = "fullName"
        private val USERS_FIRST_NAME = "firstName"
        private val USERS_LAST_NAME = "lastName"
        private val USERS_doCashTrxUnAttendedOPT = "doCashTrxUnAttendedOPT"

        val USERS_TABLE_CREATE =
            "CREATE TABLE " + USERS_TABLE_NAME + " (" +
                    USERS_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //
                    USERS_ID_USERS + " INTEGER, " +  //
                    USERS_ID_ROLE + " INTEGER, " +  //
                    USERS_CODE + " TEXT, " +  //
                    USERS_AUTHENTICATION_METHOD + " INTEGER, " +  //
                    USERS_AUTHENTICATION_KEY + " TEXT, " +  //
                    USERS_E_BUTTON + " TEXT, " +  //
                    USERS_FIRST_NAME + " TEXT," +
                    USERS_LAST_NAME + " TEXT," +
                    USERS_doCashTrxUnAttendedOPT +" TEXT );" //
    }

    fun clearUsersTableData(): Int {
        val newRowId = mDb!!.delete(USERS_TABLE_NAME, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(USERS_TABLE_NAME, null, null)
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.delete(USERS_TABLE_NAME, null, null)
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(USERS_TABLE_NAME, null, null)
        }
        return newRowId
    }
    fun insertAttendiesData(model: GasStationAttendantModel,code: String?): Int {
        try {

            val values = ContentValues()
            values.put(USERS_ID_USERS, model.id)

            var role = 0
            if(model.role.contains(ATTENDANT_ROLE))
            {
                role = 1
            }
            if(model.role.contains(AppConstant.MANAGER_ROLE)){
                role = 2
            }

            /* if(code == "CODE" && role != 2)
            {
                values.put(USERS_AUTHENTICATION_KEY, model.codepompiste)
            }
            else
            {
                values.put(USERS_AUTHENTICATION_KEY, model.tag)
            }*/

            values.put(USERS_ID_ROLE, role)
            values.put(USERS_CODE, model.codepompiste)
            values.put(USERS_AUTHENTICATION_METHOD, model.authenticationMethod)
            values.put(USERS_AUTHENTICATION_KEY, model.tag)
            values.put(USERS_E_BUTTON, model.usersButton)
            values.put(USERS_FIRST_NAME, model.firstname)
            values.put(USERS_LAST_NAME, model.lastname)
            values.put(USERS_doCashTrxUnAttendedOPT, model.doCashTrxUnAttendedOPT)

            val newRowId = mDb!!.insert(USERS_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(USERS_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(USERS_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(USERS_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun insertAttendiesArrayData(mesItems: List<GasStationAttendantModel?>,code:String) {
        if (mesItems.isNotEmpty()) {
            for (list in mesItems) {
                this.insertAttendiesData(list!!,code)
            }
        }
    }
    fun getAttendantListByCode(authKey: String): GasStationAttendantModel? {
        val mUsers: GasStationAttendantModel?
        val id: Int?
        val idUser: Int?
        val idRole: Int?
        val code: String?
        val authenticationMethod: Int?
        val authenticationKey: String?
        val eButton: String?
        val firstName: String?
        val lastName: String?
        val doCashTrxUnAttendedOPT: String?

        val projection = arrayOf<String>(
           USERS_ID,
           USERS_ID_USERS,
           USERS_ID_ROLE,
           USERS_CODE,
           USERS_AUTHENTICATION_METHOD,
           USERS_AUTHENTICATION_KEY,
           USERS_E_BUTTON,
           USERS_FIRST_NAME,
           USERS_LAST_NAME,
            USERS_doCashTrxUnAttendedOPT
        )

        // Filter results WHERE "title" = 'My Title'
        val selection: String =
            "$USERS_CODE = ? "
        val selectionArgs = arrayOf(authKey.uppercase(Locale.getDefault()))

        val cursor = mDb!!.query(
           USERS_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            null // The sort order
        )
        if (cursor.moveToFirst()) { // le curseur est plein
            id = cursor.getInt(0) //id dans ma base SQLite
            idUser = cursor.getInt(1)
            idRole = cursor.getInt(2)
            code = cursor.getString(3)
            authenticationMethod = cursor.getInt(4)
            authenticationKey = cursor.getString(5)
            eButton = cursor.getString(6)
            firstName = cursor.getString(7)
            lastName = cursor.getString(8)
            doCashTrxUnAttendedOPT = cursor.getString(9)
            mUsers = GasStationAttendantModel(
                id= idUser,
                role= "$idRole",
                codepompiste = code,
                authenticationMethod = authenticationMethod,
                authenticationKey = authenticationKey,
                usersButton = eButton,
                firstname = firstName,
                lastname = lastName,
                tag = authenticationKey,
                doCashTrxUnAttendedOPT = doCashTrxUnAttendedOPT
            )
        } else {
            mUsers = null
        }
        cursor.close()
        return mUsers
    }
    fun getAttendantListByTag(tagId: String): GasStationAttendantModel? {
        val mUsers: GasStationAttendantModel?
        val id: Int?
        val idUser: Int?
        val idRole: Int?
        val code: String?
        val authenticationMethod: Int?
        val authenticationKey: String?
        val eButton: String?
        val firstName: String?
        val lastName: String?
        val doCashTrxUnAttendedOPT: String?

        val projection = arrayOf<String>(
            USERS_ID,
            USERS_ID_USERS,
            USERS_ID_ROLE,
            USERS_CODE,
            USERS_AUTHENTICATION_METHOD,
            USERS_AUTHENTICATION_KEY,
            USERS_E_BUTTON,
            USERS_FIRST_NAME,
            USERS_LAST_NAME,
            USERS_doCashTrxUnAttendedOPT
        )

        // Filter results WHERE "title" = 'My Title'
        val selection: String =
            "$USERS_AUTHENTICATION_KEY = ? "
        val selectionArgs = arrayOf(tagId.uppercase(Locale.getDefault()))

        val cursor = mDb!!.query(
            USERS_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            null // The sort order
        )
        if (cursor.moveToFirst()) { // le curseur est plein
            id = cursor.getInt(0) //id dans ma base SQLite
            idUser = cursor.getInt(1)
            idRole = cursor.getInt(2)
            code = cursor.getString(3)
            authenticationMethod = cursor.getInt(4)
            authenticationKey = cursor.getString(5)
            eButton = cursor.getString(6)
            firstName = cursor.getString(7)
            lastName = cursor.getString(8)
            doCashTrxUnAttendedOPT = cursor.getString(9)
            mUsers = GasStationAttendantModel(
                id= idUser,
                role= "$idRole",
                codepompiste = code,
                authenticationMethod = authenticationMethod,
                authenticationKey = authenticationKey,
                usersButton = eButton,
                firstname = firstName,
                lastname = lastName,
                tag = authenticationKey,
                doCashTrxUnAttendedOPT =doCashTrxUnAttendedOPT
            )
        } else {
            mUsers = null
        }
        cursor.close()
        return mUsers
    }
    fun getAllList(): ArrayList<GasStationAttendantModel> {
        val mesItems: ArrayList<GasStationAttendantModel> = ArrayList()
        var id: Int?
        var idUser: Int?
        var idRole: Int?
        var code: String?
        var authenticationMethod: Int?
        var authenticationKey: String?
        var eButton: String?
        var firstName: String?
        var lastName: String?
        var doCashTrxUnAttendedOPT: String?

        val projection = arrayOf<String>(
            USERS_ID,
            USERS_ID_USERS,
            USERS_ID_ROLE,
            USERS_CODE,
            USERS_AUTHENTICATION_METHOD,
            USERS_AUTHENTICATION_KEY,
            USERS_E_BUTTON,
            USERS_FIRST_NAME,
            USERS_LAST_NAME,
            USERS_doCashTrxUnAttendedOPT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "${USERS_ID_ROLE} DESC"
        val cursor = mDb!!.query(
            USERS_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {

            id = cursor.getInt(0) //id dans ma base SQLite
            idUser = cursor.getInt(1)
            idRole = cursor.getInt(2)
            code = cursor.getString(3)
            authenticationMethod = cursor.getInt(4)
            authenticationKey = cursor.getString(5)
            eButton = cursor.getString(6)
            firstName = cursor.getString(7)
            lastName = cursor.getString(8)
            doCashTrxUnAttendedOPT = cursor.getString(9)
            mesItems.add(GasStationAttendantModel(id= idUser,
                role= "$idRole",
                codepompiste = code,
                authenticationMethod = authenticationMethod,
                authenticationKey = authenticationKey,
                usersButton = eButton,
                firstname = firstName,
                lastname = lastName,
                tag = authenticationKey,
                doCashTrxUnAttendedOPT =doCashTrxUnAttendedOPT
            ))
        }
        cursor.close()
        return mesItems
    }
    fun getTagsByRole(roleId: Int): ArrayList<GasStationAttendantModel> {
        val mesItems: ArrayList<GasStationAttendantModel> = ArrayList<GasStationAttendantModel>()
        var id: Int?
        var idUser: Int?
        var idRole: Int?
        var code: String?
        var authenticationMethod: Int?
        var authenticationKey: String?
        var eButton: String?
        var firstName: String?
        var lastName: String?
        var doCashTrxUnAttendedOPT: String?

        val projection = arrayOf<String>(
            USERS_ID,
            USERS_ID_USERS,
            USERS_ID_ROLE,
            USERS_CODE,
            USERS_AUTHENTICATION_METHOD,
            USERS_AUTHENTICATION_KEY,
            USERS_E_BUTTON,
            USERS_FIRST_NAME,
            USERS_LAST_NAME,
            USERS_doCashTrxUnAttendedOPT
        )
        val selection: String = "$USERS_ID_ROLE = ? "
        val selectionArgs = arrayOf(roleId.toString())

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "${USERS_ID_ROLE} DESC"
        val cursor = mDb!!.query(
            USERS_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {

            id = cursor.getInt(0) //id dans ma base SQLite
            idUser = cursor.getInt(1)
            idRole = cursor.getInt(2)
            code = cursor.getString(3)
            authenticationMethod = cursor.getInt(4)
            authenticationKey = cursor.getString(5)
            eButton = cursor.getString(6)
            firstName = cursor.getString(7)
            lastName = cursor.getString(8)
            doCashTrxUnAttendedOPT = cursor.getString(9)
            mesItems.add(GasStationAttendantModel(id= idUser,
                role= "$idRole",
                codepompiste = code,
                authenticationMethod = authenticationMethod,
                authenticationKey = authenticationKey,
                usersButton = eButton,
                firstname = firstName,
                lastname = lastName,
                tag = authenticationKey,
                        doCashTrxUnAttendedOPT=doCashTrxUnAttendedOPT
            ))
        }
        cursor.close()
        return mesItems
    }
    fun getAttendantListByID(attendantId: String): GasStationAttendantModel? {
        val mUsers: GasStationAttendantModel?
        val id: Int?
        val idUser: Int?
        val idRole: Int?
        val code: String?
        val authenticationMethod: Int?
        val authenticationKey: String?
        val eButton: String?
        val firstName: String?
        val lastName: String?
        val doCashTrxUnAttendedOPT: String?

        val projection = arrayOf<String>(
            USERS_ID,
            USERS_ID_USERS,
            USERS_ID_ROLE,
            USERS_CODE,
            USERS_AUTHENTICATION_METHOD,
            USERS_AUTHENTICATION_KEY,
            USERS_E_BUTTON,
            USERS_FIRST_NAME,
            USERS_LAST_NAME,
            USERS_doCashTrxUnAttendedOPT
        )

        // Filter results WHERE "title" = 'My Title'
        val selection: String = "$USERS_ID_USERS = ? "
        val selectionArgs = arrayOf(attendantId)

        val cursor = mDb!!.query(
            USERS_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            null // The sort order
        )
        if (cursor.moveToFirst()) { // le curseur est plein
            id = cursor.getInt(0) //id dans ma base SQLite
            idUser = cursor.getInt(1)
            idRole = cursor.getInt(2)
            code = cursor.getString(3)
            authenticationMethod = cursor.getInt(4)
            authenticationKey = cursor.getString(5)
            eButton = cursor.getString(6)
            firstName = cursor.getString(7)
            lastName = cursor.getString(8)
            doCashTrxUnAttendedOPT = cursor.getString(9)
            mUsers = GasStationAttendantModel(
                id= idUser,
                role= "$idRole",
                codepompiste = code,
                authenticationMethod = authenticationMethod,
                authenticationKey = authenticationKey,
                usersButton = eButton,
                firstname = firstName,
                lastname = lastName,
                tag = authenticationKey,
                doCashTrxUnAttendedOPT = doCashTrxUnAttendedOPT
            )
        } else {
            mUsers = null
        }
        cursor.close()
        return mUsers
    }

}