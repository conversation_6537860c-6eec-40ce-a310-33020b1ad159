package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import app.rht.petrolcard.ui.reference.model.ProductModel


import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import net.sqlcipher.database.SQLiteException
import kotlin.collections.ArrayList

class ProductsDao : MainDataBaseClass()
{

    companion object {

        const val PRODUCT_TABLE_NAME = "products"
        private const val ID = "id"
        private const val PRODUCT_ID_PRODUIT = "product_id"
        private const val PRODUCT_LABEL = "product_label"
        private const val PRODUCT_CODE = "code"
        private const val PRODUCT_BIT = "product_bit"
        private const val PRODUCT_CATEGORY = "category"
        private const val PRODUCT_AVAILABLE = "isAvailable"
        private const val PRODUCT_COLOR = "product_color"
        private const val PRODUCT_ICON = "product_icon"
        private const val FCC_PRODUCT_ID = "fccProdId"
        private const val HS_CODE = "hs_code"
        private const val CATEGORY_ID = "categoryId"

        val PRODUCT_TABLE_CREATE = "CREATE TABLE " + PRODUCT_TABLE_NAME + " (" +
                ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //int
                PRODUCT_ID_PRODUIT + " INTEGER, " +  //int
                PRODUCT_LABEL + " TEXT, " +  //String
                PRODUCT_CODE + " TEXT, " +  //String
                PRODUCT_BIT + " INTEGER, " +  //int
                PRODUCT_CATEGORY + " TEXT, " +  //String
                PRODUCT_AVAILABLE + " TEXT, " +
                PRODUCT_COLOR + " TEXT, " +
                PRODUCT_ICON + " TEXT, " +
                FCC_PRODUCT_ID + " INTEGER, " +
                CATEGORY_ID + " INTEGER, " +
                HS_CODE + " TEXT);" //String
    }


    fun clearProductsTableData(): Int {
        val newRowId = mDb!!.delete(PRODUCT_TABLE_NAME, null, null)
        if ( isSdCARD()) {
            mDb2!!.delete(PRODUCT_TABLE_NAME, null, null)
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(PRODUCT_TABLE_NAME, null, null)
        }
        return newRowId
    }
    fun insertProductsData(model: ProductModel): Int {
        try {
            // Create a new map of values, where column names are the keys
            val values = ContentValues()
            values.put(PRODUCT_ID_PRODUIT, model.productID)
            values.put(PRODUCT_LABEL, model.libelle)
            values.put(PRODUCT_CODE, model.code)
            values.put(PRODUCT_BIT, model.bit)
            values.put(PRODUCT_CATEGORY, model.categorie)
            values.put(PRODUCT_AVAILABLE, model.isAvailable)
            values.put(PRODUCT_COLOR, model.color_code)
            values.put(PRODUCT_ICON, model.icon)
            values.put(FCC_PRODUCT_ID, model.fcc_prod_id)
            values.put(HS_CODE, model.hs_code)
            values.put(CATEGORY_ID, model.categoryId)

            val newRowId = mDb!!.insert(PRODUCT_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(PRODUCT_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(PRODUCT_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(PRODUCT_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun insertProductsArrayData(mesItems: List<ProductModel?>) {
        if (mesItems.isNotEmpty()) {
            for (list in mesItems) {
                this.insertProductsData(list!!)
            }
        }
    }
    fun getProductsByCategoryId(categorieP: String): ProductModel? {
        val mProduit: ProductModel?
        val productId: Int
        val fccProductId: Int
        val libelle: String
        val code: String
        val bit: Int
        val categorie: String
        val isAvailable: String
        val productColor: String
        val productIcon: String
        var hsCode: String?=""
        val categoryId: Int

        val projection = arrayOf<String>(
            ID,
            PRODUCT_ID_PRODUIT,
            PRODUCT_LABEL,
            PRODUCT_CODE,
            PRODUCT_BIT,
            PRODUCT_CATEGORY,
            PRODUCT_AVAILABLE,
            PRODUCT_COLOR,
            PRODUCT_ICON,
            FCC_PRODUCT_ID,
            CATEGORY_ID,
            HS_CODE
        )
        val selection = "$PRODUCT_CATEGORY = ?"
        val selectionArgs = arrayOf(categorieP)
        val cursor = mDb!!.query(
            PRODUCT_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            null // The sort order
        )

        if (cursor.moveToFirst()) { // le curseur est plein
            productId = cursor.getInt(1)
            libelle = cursor.getString(2)
            code = cursor.getString(3)
            bit = cursor.getInt(4)
            categorie = cursor.getString(5)
            isAvailable = cursor.getString(6)
            productColor = cursor.getString(7)
            productIcon = cursor.getString(8)
            fccProductId = cursor.getInt(9)
            categoryId = cursor.getInt(10)
            hsCode = cursor.getString(11)
            mProduit = ProductModel(productId,fccProductId, libelle, code, bit, categorie, isAvailable,productColor,productIcon,categoryId,hsCode)
        } else {
            // cursor is empty
            mProduit = null
        }
        cursor.close() //a tester dans le cas ou le curseur est vide
        return mProduit
    } //end of public Produit selectionner(int mId)
    fun selectByProductName(product: String): ProductModel? {
        val mProduit: ProductModel?
        val productId: Int
        var fccProductID: Int =0
        val libelle: String
        var code = ""
        var bit = 0
        var categorie = ""
        var isAvailable = "yes"
        var productColor = ""
        var productIcon = ""
        var categoryId:Int = 0
        var hsCode: String?=""
        val selectQuery = "SELECT  * FROM $PRODUCT_TABLE_NAME WHERE $PRODUCT_LABEL LIKE '%$product%'"
        val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
        if (cursor.moveToFirst()) {
            productId = cursor.getInt(1)
            libelle = cursor.getString(2)

            if(cursor.getString(3) != null)
            {
                code = cursor.getString(3)
            }
            if(cursor.getString(4) != null)
            {
                bit = cursor.getInt(4)
            }
            if(cursor.getString(5) != null)
            {
                categorie = cursor.getString(5)
            }
            if(cursor.getString(6) != null)
            {
                isAvailable = cursor.getString(6)
            }
            if(cursor.getString(7) != null)
            {
                productColor = cursor.getString(7)
            }
            if(cursor.getString(8) != null)
            {
                productIcon = cursor.getString(8)
            }
            if(cursor.getString(9) != null)
            {
                fccProductID = cursor.getInt(9)
            }
            if(cursor.getString(11) != null)
            {
                hsCode = cursor.getString(11)
            }
            categoryId = cursor.getInt(10)


            mProduit = ProductModel(productId, fccProductID, libelle, code, bit, categorie, isAvailable,productColor,productIcon, categoryId,hsCode)
        } else {
            mProduit = null
        }
        cursor.close()
        return mProduit
    }
    fun getAllProducts(): ArrayList<ProductModel> {
        val mesItems: ArrayList<ProductModel> = ArrayList<ProductModel>()
        var productID: Int
        var fccProductId: Int =0
        var libelle: String
        var code = ""
        var bit = 0
        var categorie = ""
        var isAvailable = "yes"
        var productColor = ""
        var productIcon = ""
        var hsCode:String? = ""
        var categoryId = 0
        val projection = arrayOf<String>(
            ID,
            PRODUCT_ID_PRODUIT,
            PRODUCT_LABEL,
            PRODUCT_CODE,
            PRODUCT_BIT,
            PRODUCT_CATEGORY,
            PRODUCT_AVAILABLE,
            PRODUCT_COLOR,
            PRODUCT_ICON,
            FCC_PRODUCT_ID,
            CATEGORY_ID,
            HS_CODE
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$ID DESC"
        val cursor = mDb!!.query(
            PRODUCT_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {

            productID = cursor.getInt(1)
            libelle = cursor.getString(2)
            if(cursor.getString(3) != null)
            {
                code = cursor.getString(3)
            }
            if(cursor.getString(4) != null)
            {
                bit = cursor.getInt(4)
            }
            if(cursor.getString(5) != null)
            {
                categorie = cursor.getString(5)
            }
            if(cursor.getString(6) != null)
            {
                isAvailable = cursor.getString(6)
            }
            if(cursor.getString(7) != null)
            {
                productColor = cursor.getString(7)
            }
            if(cursor.getString(8) != null)
            {
                productIcon = cursor.getString(8)
            }
            if(cursor.getString(9) != null)
            {
                fccProductId = cursor.getInt(9)
            }
            categoryId = cursor.getInt(10)
            if(cursor.getString(11) != null)
            {
                hsCode = cursor.getString(11)
            }
            mesItems.add(ProductModel(productID, fccProductId, libelle, code, bit, categorie, isAvailable,productColor,productIcon,categoryId,hsCode))
        }
        cursor.close()
        return mesItems
    }

    fun getProductById(productId: Int): ProductModel? {
        val mProduct: ProductModel?
        var productIDKey: Int = 0
        var fccProductId: Int =0
        var label: String?
        var code: String=""
        var bit: Int=0
        var category: String=""
        var isAvailable: String="true"
        var productColor = ""
        var productIcon = ""
        var hsCode:String? = ""
        var categoryId = 0

        val projection = arrayOf<String>(
            ID,
            PRODUCT_ID_PRODUIT,
            PRODUCT_LABEL,
            PRODUCT_CODE,
            PRODUCT_BIT,
            PRODUCT_CATEGORY,
            PRODUCT_AVAILABLE,
            PRODUCT_COLOR,
            PRODUCT_ICON,
            FCC_PRODUCT_ID,
            CATEGORY_ID,
            HS_CODE
        )

        val selection = "$PRODUCT_ID_PRODUIT = ?"
        val selectionArgs = arrayOf(productId.toString())
        val cursor = mDb!!.query(
            PRODUCT_TABLE_NAME,
            projection,
            selection,
            selectionArgs,
            null,
            null,
            null
        )
        if (cursor.moveToFirst()) {

            productIDKey = cursor.getInt(1)
            val fccProductID = cursor.getInt(9)
            label = cursor.getString(2)
            val fusionProductName = Support.getFusionProductName(fccProductID)

            if (fusionProductName != null && fusionProductName.isNotEmpty()) {
                label = fusionProductName
            }

            if(cursor.getString(3) != null)
            {
                code = cursor.getString(3)
            }
            if(cursor.getString(4) != null)
            {
                bit = cursor.getInt(4)
            }
            if(cursor.getString(5) != null)
            {
                category = cursor.getString(5)
            }
            if(cursor.getString(6) != null)
            {
                isAvailable = cursor.getString(6)
            }
            if(cursor.getString(7) != null)
            {
                productColor = cursor.getString(7)
            }
            if(cursor.getString(8) != null)
            {
                productIcon = cursor.getString(8)
            }
            if(cursor.getString(9) != null)
            {
                fccProductId = cursor.getInt(9)
            }
            categoryId = cursor.getInt(10)
            if(cursor.getString(11) != null)
            {
                hsCode = cursor.getString(11)
            }
            mProduct = ProductModel(productIDKey, fccProductId, label, code, bit, category, isAvailable,productColor,productIcon,categoryId,hsCode)


        } else {
            mProduct = null
        }
        cursor.close()
        return mProduct
    }

    fun getFuelProducts(): ArrayList<ProductModel> {
        val mesItems: ArrayList<ProductModel> = ArrayList()
        var productID: Int
        var fccProductId: Int =0
        var libelle: String?
        var code: String?=""
        var bit: Int?=0
        var categorie: String?=""
        var isAvailable: String?="true"
        var productColor = ""
        var productIcon = ""
        var categoryId = 0
        var hsCode:String? = ""

        val projection = arrayOf<String>(
            ID,
            PRODUCT_ID_PRODUIT,
            PRODUCT_LABEL,
            PRODUCT_CODE,
            PRODUCT_BIT,
            PRODUCT_CATEGORY,
            PRODUCT_AVAILABLE,
            PRODUCT_COLOR,
            PRODUCT_ICON,
            FCC_PRODUCT_ID,
            CATEGORY_ID,
            HS_CODE
        )
        //val selectQuery = "SELECT  * FROM $PRODUCT_TABLE_NAME WHERE $PRODUCT_CATEGORY LIKE '%${"FUEL"}%'"
        val selectQuery = "SELECT  * FROM $PRODUCT_TABLE_NAME WHERE $CATEGORY_ID = ${PRODUCT.FUEL_CATEGORY_ID}"   // using category id to identify product
        val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
        while (cursor.moveToNext()) {
            val fcc_prod_id = cursor.getInt(9)
            productID = cursor.getInt(1)
            libelle = cursor.getString(2)
            val fusionProductName = Support.getFusionProductName(fcc_prod_id)
            if (fusionProductName != null && !fusionProductName.isEmpty()) {
                libelle = fusionProductName
            }

            if(cursor.getString(3) != null)
            {
                code = cursor.getString(3)
            }
            if(cursor.getString(4) != null)
            {
                bit = cursor.getInt(4)
            }
            if(cursor.getString(5) != null)
            {
                categorie = cursor.getString(5)
            }
            if(cursor.getString(6) != null)
            {
                isAvailable = cursor.getString(6)
            }
            if(cursor.getString(7) != null)
            {
                productColor = cursor.getString(7)
            }
            if(cursor.getString(8) != null)
            {
                productIcon = cursor.getString(8)
            }

            if(cursor.getString(9) != null)
            {
                fccProductId = cursor.getInt(9)
            }
            if(cursor.getString(11) != null)
            {
                hsCode = cursor.getString(11)
            }
            categoryId = cursor.getInt(10)

            mesItems.add(ProductModel(productID, fccProductId, libelle, code, bit, categorie, isAvailable,productColor,productIcon,categoryId,hsCode))

        }
        cursor.close()
        return mesItems
    }
    fun getProductByFCCId(productId: Int): ProductModel? {
        val mProduct: ProductModel?
        var productID: Int
        var fccProductId =0
        var label: String?
        var code = ""
        var bit =0
        var category =""
        var isAvailable ="true"
        var productColor = ""
        var productIcon = ""
        var categoryId = 0
        var hsCode:String? = ""

        val projection = arrayOf<String>(
            ID,
            PRODUCT_ID_PRODUIT,
            PRODUCT_LABEL,
            PRODUCT_CODE,
            PRODUCT_BIT,
            PRODUCT_CATEGORY,
            PRODUCT_AVAILABLE,
            PRODUCT_COLOR,
            PRODUCT_ICON,
            FCC_PRODUCT_ID,
            CATEGORY_ID,
            HS_CODE
        )

        val selection = "$FCC_PRODUCT_ID = ?"
        val selectionArgs = arrayOf(productId.toString())
        val cursor = mDb!!.query(
            PRODUCT_TABLE_NAME,
            projection,
            selection,
            selectionArgs,
            null,
            null,
            null
        )
        if (cursor.moveToFirst()) {
            val fcc_prod_id = cursor.getInt(9)
            productID = cursor.getInt(1)
            label = cursor.getString(2)
            val fusionProductName = Support.getFusionProductName(fcc_prod_id)

            if (fusionProductName != null && fusionProductName.isNotEmpty()) {
                label = fusionProductName
            }

            if(cursor.getString(3) != null)
            {
                code = cursor.getString(3)
            }
            if(cursor.getString(4) != null)
            {
                bit = cursor.getInt(4)
            }
            if(cursor.getString(5) != null)
            {
                category = cursor.getString(5)
            }
            if(cursor.getString(6) != null)
            {
                isAvailable = cursor.getString(6)
            }
            if(cursor.getString(7) != null)
            {
                productColor = cursor.getString(7)
            }
            if(cursor.getString(8) != null)
            {
                productIcon = cursor.getString(8)
            }
            if(cursor.getString(9) != null)
            {
                fccProductId = cursor.getInt(9)
            }
            if(cursor.getString(11) != null)
            {
                hsCode = cursor.getString(11)
            }
            categoryId = cursor.getInt(10)

            mProduct = ProductModel(productID, fccProductId, label, code, bit, category, isAvailable,productColor,productIcon,categoryId,hsCode)

        } else {
            mProduct = null
        }
        cursor.close()
        return mProduct
    }

}