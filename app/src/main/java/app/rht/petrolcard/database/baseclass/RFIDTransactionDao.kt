package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import android.database.Cursor
import app.rht.petrolcard.service.model.RFIDTransactionModel
import app.rht.petrolcard.utils.constant.AppConstant
import java.lang.Exception
import java.util.ArrayList

class RFIDTransactionDao : MainDataBaseClass()
{
    companion object {
        const val TRANSACTION_LIST_TABLE_NAME = "rfid_transaction_list"

        const val ID = "id"
        const val NOZZLE_UNIT_ID = "nozzle_unit_id"
        const val VEHICLE_TAG_ID = "vehicle_tag_id"
        const val PUMP_NO = "pump_no"
        const val TRANSACTION_SEQUENCE_NO = "transaction_sequence_no"
        const val TIMESTAMP = "timestamp"

        const val RFID_TRANSACTION_LIST_TABLE =
            "CREATE TABLE " + TRANSACTION_LIST_TABLE_NAME + " (" +
                    ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    NOZZLE_UNIT_ID + " INTEGER, " +
                    PUMP_NO + " INTEGER, " +
                    VEHICLE_TAG_ID + " TEXT, " +
                    TRANSACTION_SEQUENCE_NO + " TEXT, " +
                    TIMESTAMP + " TEXT) ;"

          }

    fun RFIDTransactionDao(context: Context?) {
        super.getContext()
    }

    fun insertRFIDTransactionList(rfidTransactionList: RFIDTransactionModel): Int {
        val pValues = ContentValues()
        pValues.put(PUMP_NO, rfidTransactionList.pump_no)
        pValues.put(NOZZLE_UNIT_ID, rfidTransactionList.nozzle_unit_id)
        pValues.put(VEHICLE_TAG_ID, rfidTransactionList.vehicle_tag_id)
        pValues.put(TRANSACTION_SEQUENCE_NO, rfidTransactionList.transaction_sequence_no)
        pValues.put(TIMESTAMP, rfidTransactionList.timestamp)
        /*Thread{
            val backupRowId = backupDb!!.insert(TRANSACTION_LIST_TABLE_NAME, null, pValues).toInt()
        }.run()*/
        return mDb!!.insert(TRANSACTION_LIST_TABLE_NAME, null, pValues).toInt()
    }

    fun updateRFIDTransactionList(trxSeqNo: String?, rowId: Int) {
        val values = ContentValues()
        values.put(ID, rowId)
        values.put(TRANSACTION_SEQUENCE_NO, trxSeqNo)
        mDb!!.update(TRANSACTION_LIST_TABLE_NAME, values, "$ID = ?", arrayOf(rowId.toString()))
        /*Thread{
            val backupRowId = backupDb!!.update(TRANSACTION_LIST_TABLE_NAME, values, "$ID = ?", arrayOf(rowId.toString()))
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.update(
                TRANSACTION_LIST_TABLE_NAME,
                values,
                "$ID = ?",
                arrayOf(rowId.toString())
            )
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.update(
                TRANSACTION_LIST_TABLE_NAME,
                values,
                "$ID = ?",
                arrayOf(rowId.toString())
            )
        }
    }

    fun getTransactionByNozzleId(nozzleId: String, trxNo: String): RFIDTransactionModel? {
        var rfidTransactionModels: RFIDTransactionModel? = null
        val selectQuery =
            "SELECT  * FROM $TRANSACTION_LIST_TABLE_NAME WHERE $NOZZLE_UNIT_ID = $nozzleId AND $TRANSACTION_SEQUENCE_NO = $trxNo ORDER BY $ID DESC LIMIT 1"
        val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
        if (cursor.moveToFirst()) {
            do {
                rfidTransactionModels = RFIDTransactionModel(
                    cursor.getInt(0),
                    cursor.getInt(1),
                    cursor.getInt(2),
                    cursor.getString(3),
                    cursor.getString(4),
                    cursor.getString(5)
                )
            } while (cursor.moveToNext())
        }
        cursor.close()
        return rfidTransactionModels
    }

    fun getTransactionCount(
        timestamp: String?,
        vehicle_id: String,
        nozzleId: Int,
        trxNo: String
    ): Int {
        var count = 0
        try {
            val selectQuery = ("SELECT  * FROM " + TRANSACTION_LIST_TABLE_NAME + " WHERE "
                    + VEHICLE_TAG_ID + " = " + vehicle_id + " AND " +
                    NOZZLE_UNIT_ID + " = " + nozzleId + " AND " + TRANSACTION_SEQUENCE_NO + " = " + trxNo + " ORDER BY " + ID + " DESC LIMIT 1")
            val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransactionCount ", selectQuery)
            count = cursor.count
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return count
    }

    fun getTransactionByFuelSaleTrx(
        nozzleId: String,
        trxNo: String,
        PumpNo: String
    ): RFIDTransactionModel? {
        var rfidTransactionModels: RFIDTransactionModel? = null
        try {
            val selectQuery =
                "SELECT  * FROM $TRANSACTION_LIST_TABLE_NAME WHERE $NOZZLE_UNIT_ID = $nozzleId AND $TRANSACTION_SEQUENCE_NO = $trxNo AND $PUMP_NO = $PumpNo ORDER BY $ID DESC LIMIT 1"
            val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransactionByFuelSaleTrx Query :: ", selectQuery)
            if (cursor.moveToFirst()) {
                do {
                    rfidTransactionModels = RFIDTransactionModel(
                        cursor.getInt(0),
                        cursor.getInt(1),
                        cursor.getInt(2),
                        cursor.getString(3),
                        cursor.getString(4),
                        cursor.getString(5)
                    )
                } while (cursor.moveToNext())
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return rfidTransactionModels
    }

    fun deleteVerifiedTransaction(rowID: Int): Boolean {
        return mDb!!.delete(
            TRANSACTION_LIST_TABLE_NAME,
            ID + "=?",
            arrayOf(rowID.toString() + "")
        ) > 0
    }

    fun getAllRFIDTransactionList(): ArrayList<RFIDTransactionModel>? {
        val rfidTransactionModels: ArrayList<RFIDTransactionModel> =
            ArrayList<RFIDTransactionModel>()
        val selectQuery = "SELECT  * FROM $TRANSACTION_LIST_TABLE_NAME"
        val cursor: Cursor = mDb!!.rawQuery(selectQuery, null)
        if (cursor.moveToFirst()) {
            do {
                val rfidTransactionModel = RFIDTransactionModel(
                    cursor.getInt(0),
                    cursor.getInt(1),
                    cursor.getInt(2),
                    cursor.getString(3),
                    cursor.getString(4),
                    cursor.getString(5)
                )
                rfidTransactionModels.add(rfidTransactionModel)
            } while (cursor.moveToNext())
        }
        cursor.close()
        return rfidTransactionModels
    }


}