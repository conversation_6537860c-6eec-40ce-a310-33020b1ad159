package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import app.rht.petrolcard.ui.reference.model.AuditModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.helpers.BaseUtils
import net.sqlcipher.Cursor
import kotlin.collections.ArrayList

class AuditDao : MainDataBaseClass() {

    private var auditItems: ArrayList<AuditModel> = ArrayList()

    fun insertAudit(auditModel: AuditModel): Int {
        val values = ContentValues()

        values.put(AUDIT_ACTIVITY,auditModel.activity)
        values.put(AUDIT_ACTION,auditModel.action)
        values.put(AUDIT_PERFORMED_BY,auditModel.performedBy)
        values.put(AUDIT_TIMESTAMP,auditModel.timestamp)
        values.put(AUDIT_TELECOLLECT_STATUS,0)

        val newRowId = mDb!!.insert(AUDIT_TABLE_NAME,null,values).toInt()
        /*Thread{
            val backupRowId = backupDb!!.insert(AUDIT_TABLE_NAME,null,values).toInt()
        }.run()*/
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.insert(AUDIT_TABLE_NAME,null,values).toInt() // returns the id of the created record
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.insert(AUDIT_TABLE_NAME,null,values).toInt() // returns the id of the created record
        }
        return newRowId
    }
    fun updateAudit(auditModel: AuditModel) {
        val values = ContentValues()

        values.put(AUDIT_ACTIVITY,auditModel.activity)
        values.put(AUDIT_ACTION,auditModel.action)
        values.put(AUDIT_PERFORMED_BY,auditModel.performedBy)
        values.put(AUDIT_TIMESTAMP,auditModel.timestamp)
        values.put(AUDIT_TELECOLLECT_STATUS,auditModel.teleCollectStatus)

        val newRowId = mDb!!.update(AUDIT_TABLE_NAME,values, "$ID = ?",arrayOf("${auditModel.id}"))
        /*Thread{
            val backupRowId = backupDb!!.update(AUDIT_TABLE_NAME,values, "$ID = ?",arrayOf("${auditModel.id}"))
        }.run()*/
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(AUDIT_TABLE_NAME,values, "$ID = ?",arrayOf("${auditModel.id}"))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(AUDIT_TABLE_NAME,values, "$ID = ?",arrayOf("${auditModel.id}"))
        }
    }
    fun getAudits(status: Int = 0): ArrayList<AuditModel> {
        auditItems.clear()

        val sortOrder = "$ID DESC"
        val cursor = mDb!!.query(
            AUDIT_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            "$AUDIT_TELECOLLECT_STATUS = $status",  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            auditItems.add(item)
        }
        cursor.close()
        return auditItems
    }

   companion object {
       const val AUDIT_TABLE_NAME = "tblAudit"

        private const val ID = "id"
        private const val AUDIT_ACTIVITY = "activity"
        private const val AUDIT_ACTION = "action"
        private const val AUDIT_PERFORMED_BY = "performedBy"
        private const val AUDIT_TIMESTAMP = "timestamp"
        private const val AUDIT_TELECOLLECT_STATUS = "teleCollectStatus"


       const val AUDIT_TABLE_CREATE = "CREATE TABLE " + AUDIT_TABLE_NAME + " (" +
                    ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    AUDIT_ACTIVITY + " TEXT, " +
                    AUDIT_ACTION + " TEXT, " +
                    AUDIT_PERFORMED_BY + " TEXT, " +
                    AUDIT_TIMESTAMP + " TEXT, " +
                    AUDIT_TELECOLLECT_STATUS + " INTEGER); "

        private val projection = arrayOf(
            ID,                                     //0     //int
            AUDIT_ACTIVITY,                          //1     //string
            AUDIT_ACTION,                           //2     //string
            AUDIT_PERFORMED_BY,                     //3     //string
            AUDIT_TIMESTAMP,                        //4     //string
            AUDIT_TELECOLLECT_STATUS,               //5     //int
        )
    }
   private fun getItem(cursor: Cursor) : AuditModel {
        val item = AuditModel()
        item.id = cursor.getInt(projection.indexOf(ID))
        item.activity = cursor.getString(projection.indexOf(AUDIT_ACTIVITY))
        item.action = cursor.getString(projection.indexOf(AUDIT_ACTION))
        item.performedBy = cursor.getString(projection.indexOf(AUDIT_PERFORMED_BY))
        item.timestamp = cursor.getString(projection.indexOf(AUDIT_TIMESTAMP))
        item.teleCollectStatus = cursor.getInt(projection.indexOf(AUDIT_TELECOLLECT_STATUS))

        return item
    }

}