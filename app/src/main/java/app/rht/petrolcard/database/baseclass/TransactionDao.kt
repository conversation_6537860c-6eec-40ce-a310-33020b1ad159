package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.util.Log
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketResponse
import app.rht.petrolcard.ui.reference.model.FuelPOSModel
import app.rht.petrolcard.ui.reference.model.QrCodeTicket
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.settings.operations.model.TotalProductCount
import app.rht.petrolcard.ui.settings.operations.model.TotalTransactionCountModel
import app.rht.petrolcard.ui.timssign.model.TIMSSignModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.fuelpos.decimal
import app.rht.petrolcard.utils.helpers.BaseUtils
import com.google.gson.Gson
import net.sqlcipher.Cursor
import net.sqlcipher.database.SQLiteException
import java.text.SimpleDateFormat
import java.util.*

class TransactionDao : MainDataBaseClass() {

    private var mesItems: ArrayList<TransactionModel> = ArrayList()
    var TAG=TransactionDao::class.java.simpleName

    fun insertTransaction(mTransaction: TransactionModel): Int {
        var transactionModel = mTransaction
        if(checkReferenceNumberAvailable(transactionModel.reference!!))
        {
            transactionModel.reference = "TRX"+prefs.logReferenceNo
            log(TAG,"updateTransactionsByReferenceID:: new reference created ${transactionModel.reference}")
        }
        val values = getValues(transactionModel)
        val newRowId = mDb!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt() // returns the id of the created record
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt() // returns the id of the created record
        }
        return newRowId
    }

    fun updateTransactionsByReferenceID(mTransaction: TransactionModel): Int {
        var transactionModel = mTransaction

        transactionModel.flagTelecollecte = 0       // to send updated transaction again in telecollect

        var values = getValues(transactionModel)
        var newRowId = mDb!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_REFERENCE = ?",arrayOf("${transactionModel.reference}"))
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_REFERENCE = ?",arrayOf("${transactionModel.reference}"))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(TRANSACTION_TABLE_NAME,values,"$TRANSACTION_REFERENCE = ?",arrayOf("${transactionModel.reference}"))
        }
        if (newRowId <= 0) {
            if(checkReferenceNumberAvailable(transactionModel.reference!!))
            {
                transactionModel.reference = "TRX"+prefs.logReferenceNo
                values = getValues(transactionModel)
                log(TAG,"checkReferenceNumberAvailable:: new reference created ${transactionModel.reference}")
            }
            log(TAG,"updateTransactionsByReferenceID:: Inserted New Transaction")
            newRowId = mDb!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            if ( BaseUtils.isSdCARD() && mDb2 != null ) {
                val newRowId2 = mDb2!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
                val newRowId3 = mDb3!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            }
        }
        return newRowId
    }
    fun checkNUpdateByReference(mTransaction: TransactionModel): Int {
        var transactionModel = mTransaction
        if(checkTransactionCompleted(transactionModel.reference!!))
        {
            transactionModel.reference = "TRX"+prefs.logReferenceNo
            log(TAG,"checkTransactionCompleted:: new reference created ${transactionModel.reference}")
        }
        val values = getValues(transactionModel)
        var newRowId = mDb!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_REFERENCE = ?",arrayOf("${transactionModel.reference}"))
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_REFERENCE = ?",arrayOf("${transactionModel.reference}"))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(TRANSACTION_TABLE_NAME,values,"$TRANSACTION_REFERENCE = ?",arrayOf("${transactionModel.reference}"))
        }
        if (newRowId <= 0) {
            log(TAG,"updateTransactionsByReferenceID:: Inserted New Transaction")
            newRowId = mDb!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            if ( BaseUtils.isSdCARD() && mDb2 != null ) {
                val newRowId2 = mDb2!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
                val newRowId3 = mDb3!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            }
        }
        return newRowId
    }
    fun checkNUpdateByCheckSum(mTransaction: TransactionModel): Int {
        val values = getValues(mTransaction)
        var newRowId = mDb!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_CHECKSUM = ?",arrayOf("${mTransaction.transactionChecksum}"))
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
             newRowId = mDb2!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_CHECKSUM = ?",arrayOf("${mTransaction.transactionChecksum}"))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
             newRowId = mDb3!!.update(TRANSACTION_TABLE_NAME,values,"$TRANSACTION_CHECKSUM = ?",arrayOf("${mTransaction.transactionChecksum}"))
        }
        if (newRowId <= 0) {
            log(TAG,"updateTransactionsByReferenceID:: Inserted New Transaction")
            newRowId = mDb!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            if ( BaseUtils.isSdCARD() && mDb2 != null ) {
                newRowId = mDb2!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
                newRowId = mDb3!!.insert(TRANSACTION_TABLE_NAME,null,values).toInt()
            }
        }
        return newRowId
    }

    fun updateTeleCollectFlagById(mId: Int, teleCollectFlag: Int) {
        val values = ContentValues()
        values.put(TRANSACTION_FLAG_TELECOLLECT,teleCollectFlag)
        val newRowId = mDb!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_ID = ?",arrayOf(mId.toString()))
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(TRANSACTION_TABLE_NAME,    values, "$TRANSACTION_ID = ?",    arrayOf(mId.toString()))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(TRANSACTION_TABLE_NAME,    values, "$TRANSACTION_ID = ?",    arrayOf(mId.toString()))
        }
    }


    fun getTransactions(): List<TransactionModel> {
        mesItems.clear()

        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }
    fun getTeleCollectLoyaltyByFlag(mFlagTelecollecte: Int): ArrayList<TransactionModel> {
        mesItems.clear()

        val sortOrder = "$TRANSACTION_ID DESC"

        val selection = "$TRANSACTION_FLAG_TELECOLLECT  =? "
        val selectionArgs = arrayOf(mFlagTelecollecte.toString())
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  //null,                                     // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        if (cursor != null) {
            while (cursor.moveToNext()) {
                val item = getItem(cursor)
                if (item.detailArticle != null && item.detailArticle == "loy") {
                    mesItems.add(item)
                }
            }
            cursor.close()
        }
        return mesItems
    }
    fun getTeleCollectTransactionByFlag(mFlagTelecollecte: Int): List<TransactionModel> {
        mesItems.clear()

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$TRANSACTION_ID DESC"

        // Filter results WHERE "title" = 'My Title'
        var selection = "$TRANSACTION_FLAG_TELECOLLECT  =? "
        var selectionArgs = arrayOf(mFlagTelecollecte.toString())

        var cursor: Cursor? = null
        var count = 0
        if (mDb != null && mDb!!.isOpen) {
            try {
                cursor = mDb!!.query(
                    TRANSACTION_TABLE_NAME,  // The table to query
                    projection,  // The columns to return
                    selection,  // The columns for the WHERE clause
                    selectionArgs,  // The values for the WHERE clause
                    null,  // don't group the rows
                    null,  // don't filter by row groups
                    sortOrder // The sort order
                )
                while (cursor != null && cursor.moveToNext()) {
                    val item = getItem(cursor)
                    if (item.detailArticle == null || item.detailArticle != "loy")
                    {
                        mesItems.add(item)
                    }
                }
            } catch (E: SQLiteException) {
                log("SQLiteAbortException", "SQLiteAbortException ---> " + E.message)
            } finally {
                if (cursor != null && !cursor.isClosed) {
                    count = cursor.count
                    cursor.close()
                }
                log("TransactionTaxi DAO", "count cursor ---> $count")
            }
        }
        return mesItems
    }

    fun getJournalOperation(): List<TransactionModel> {
        mesItems.clear()

        val sortOrder = "$TRANSACTION_ID DESC"
        val today = SimpleDateFormat("yyyyMMdd", Locale.ENGLISH).format(Date())
        val selection =
            "strftime('%Y%m%d', " + TRANSACTION_DATE_TRANSACTION + ")" + " =? AND " +
                    TRANSACTION_STATUS + " =?"
        val selectionArgs = arrayOf(today, 1.toString())
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }
    fun getTotalTransactions(): List<TransactionModel> {
        mesItems.clear()


        val today = SimpleDateFormat("yyyyMMdd", Locale.ENGLISH).format(Date())
        val selection = "strftime('%Y%m%d', $TRANSACTION_DATE_TRANSACTION) =? AND $TRANSACTION_STATUS =? AND $TRANSACTION_ID_TYPE_TRANSACTION = '1'"
        val selectionArgs = arrayOf(today, 1.toString())
        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }
    fun getTotalRechargeTransactions(): List<TransactionModel> {
        mesItems.clear()
        val today = SimpleDateFormat("yyyyMMdd", Locale.ENGLISH).format(Date())
        val selection = "strftime('%Y%m%d', $TRANSACTION_DATE_TRANSACTION) =? AND $TRANSACTION_STATUS =? AND $TRANSACTION_ID_TYPE_TRANSACTION = '3' AND $TRANSACTION_ID_PRODUCT = '17'"
        val selectionArgs = arrayOf(today, 1.toString())
        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }

    fun getTotalFuelTransaction(): TotalTransactionCountModel {
        val trx = TotalTransactionCountModel()
        val today = SimpleDateFormat("yyyyMMdd", Locale.ENGLISH).format(Date())
        val query = "SELECT COUNT(CASE WHEN $TRANSACTION_ID_TYPE_TRANSACTION != 3 THEN 0 END) total_transaction_count, " +
                " COUNT(CASE WHEN $TRANSACTION_CATEGORY = ${PRODUCT.FUEL_CATEGORY_ID} THEN 0 END) fuel_transaction_count," +
                " COUNT(CASE WHEN $TRANSACTION_CATEGORY = ${PRODUCT.SERVICE_CATEGORY_ID} THEN 0  END) service_transaction_count," +
                " COUNT(CASE WHEN $TRANSACTION_CATEGORY = ${PRODUCT.SHOP_CATEGORY_ID} THEN 0 END) shop_transaction_count," +
                " COUNT(CASE WHEN $TRANSACTION_ID_TYPE_TRANSACTION = 3 THEN 0 END) recharge_transaction_count," +
                " COUNT(CASE WHEN $TRANSACTION_CATEGORY != ${PRODUCT.FUEL_CATEGORY_ID} AND $TRANSACTION_ID_TYPE_TRANSACTION != 3 THEN 0 END) non_fuel_transaction_count," +
                " SUM(CASE WHEN $TRANSACTION_ID_TYPE_TRANSACTION = 3 THEN $TRANSACTION_AMOUNT END) total_recharge_amount," +
                " SUM(CASE WHEN $TRANSACTION_ID_TYPE_TRANSACTION != 3 THEN $TRANSACTION_AMOUNT END) total_transaction_amount" +
                " FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_STATUS = '1' AND strftime('%Y%m%d', $TRANSACTION_DATE_TRANSACTION) ='$today'"
        val cursor = mDb!!.rawQuery(query, null)
        if (cursor.moveToFirst()) {
            do {

                if(cursor.getString(7) == null)
                {
                    trx.total_transaction_amount = "0"
                }
                else
                {
                    trx.total_transaction_amount = cursor.getString(7)
                }
                if(cursor.getString(6) == null)
                {
                    trx.total_recharge_amount = "0"
                }
                else
                {
                    trx.total_recharge_amount = cursor.getString(6)
                }
                if(cursor.getString(5) == null)
                {
                    trx.non_fuel_transaction_count = "0"
                }
                else
                {
                    trx.non_fuel_transaction_count = cursor.getString(5)
                }
                if(cursor.getString(4) == null)
                {
                    trx.recharge_transaction_count = "0"
                }
                else
                {
                    trx.recharge_transaction_count = cursor.getString(4)
                }
                if(cursor.getString(3) == null)
                {
                    trx.shop_transaction_count = "0"
                }
                else
                {
                    trx.shop_transaction_count = cursor.getString(3)
                }
                if(cursor.getString(2) == null)
                {
                    trx.service_transaction_count = "0"
                }
                else
                {
                    trx.service_transaction_count = cursor.getString(2)
                }
                if(cursor.getString(1) == null)
                {
                    trx.fuel_transaction_count = "1"
                }
                else
                {
                    trx.fuel_transaction_count = cursor.getString(1)
                }
                if(cursor.getString(0) == null)
                {
                    trx.total_transaction_count = "0"
                }
                else
                {
                    trx.total_transaction_count = cursor.getString(0)
                }
            } while (cursor.moveToNext())
        }
        cursor.close()
        return trx
    }
    fun getTotalProductTransactions():ArrayList<TotalProductCount>{
        val productCounts: ArrayList<TotalProductCount> = ArrayList()
        val today = SimpleDateFormat("yyyyMMdd", Locale.ENGLISH).format(Date())
        val query = "SELECT COUNT(*) productCount,${TRANSACTION_ID_PRODUCT} FROM $TRANSACTION_TABLE_NAME" +
                " WHERE $TRANSACTION_STATUS = 1 AND $TRANSACTION_CATEGORY != ${PRODUCT.RECHARGE}" +
                " AND $TRANSACTION_CATEGORY != ${PRODUCT.SHOP_CATEGORY_ID}" +
                " AND strftime('%Y%m%d', $TRANSACTION_DATE_TRANSACTION) ='$today'"+
                " GROUP BY ${TRANSACTION_ID_PRODUCT};"
        val cursor = mDb!!.rawQuery(query, null)
        if (cursor.moveToFirst()) {
            do {
                try{
                    val product = TotalProductCount()
                    product.productCount = if(cursor.getString(0)==null) "0" else cursor.getString(0)
                    product.productID = if(cursor.getString(1)==null) "0" else cursor.getString(1)
                    productCounts.add(product)
                } catch (e:Exception){
                    e.printStackTrace()
                }
            } while (cursor.moveToNext())
        }
        cursor.close()
        return  productCounts
    }
    fun updateTransactionStatus(mId: Int) {
        val values = ContentValues()
        values.put(TRANSACTION_STATUS,1)
        val newRowId = mDb!!.update(TRANSACTION_TABLE_NAME,values, "$TRANSACTION_ID = ?",arrayOf(mId.toString()))
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(TRANSACTION_TABLE_NAME,    values, "$TRANSACTION_ID = ?",    arrayOf(mId.toString()))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(TRANSACTION_TABLE_NAME,    values, "$TRANSACTION_ID = ?",    arrayOf(mId.toString()))
        }
    }
    fun getDuplicateTransaction(nb: Int): ArrayList<TransactionModel> {
        var number = nb
        mesItems.clear()

        val selection = "$TRANSACTION_STATUS = ?"
        val selectionArgs = arrayOf(1.toString())
        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext() && nb > 0) {
            number--
            val item = getItem(cursor)
            mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }
    fun getFleetCardPendingTransaction(): ArrayList<TransactionModel> {
        mesItems.clear()

        val selection = "$TRANSACTION_STATUS = ? AND $TRANSACTION_REFUND_STATUS = ? AND $TRANSACTION_FLAG_TELECOLLECT = ?"
        val selectionArgs = arrayOf("1","0","0")
        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            if(item.preAuthAmount!=null && item.preAuthAmount!!.isNotEmpty() && item.amount!=null && item.modepay == AppConstant.CARD_VALUE){ // checking card payments only
                val preAuthAmt = item.preAuthAmount!!.toDouble()
                if(preAuthAmt > item.amount!!){
                    mesItems.add(item)
                }
            }
            //mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }
    fun getDuplicateByPAN(nb: Int, panCarte: String?): ArrayList<TransactionModel> {
        var number = nb

        mesItems.clear()

        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext() && number > 0) {

            val item = getItem(cursor)

            if (item.pan != null && item.pan!!.contains(panCarte!!)) {
                mesItems.add(item)
                number--
            }
        }
        cursor.close()
        return mesItems
    } //public List<Transaction> selectionner()
    fun getRebateTransactions(teleCollectFlag: Int): List<TransactionModel> {
        mesItems.clear()

        val sortOrder = "$TRANSACTION_ID DESC"
        val selection = "$TRANSACTION_FLAG_TELECOLLECT =? AND $DISCOUNT_TYPE = ?"
        val selectionArgs = arrayOf(teleCollectFlag.toString(), DISCOUNT_TYPE.REBATE_DISCOUNT.toString())
        var cursor: Cursor? = null
        var count = 0

        if (mDb != null && mDb!!.isOpen) {
            try {
                cursor = mDb!!.query(
                    TRANSACTION_TABLE_NAME,  // The table to query
                    projection,  // The columns to return
                    selection,  // The columns for the WHERE clause
                    selectionArgs,  // The values for the WHERE clause
                    null,  // don't group the rows
                    null,  // don't filter by row groups
                    sortOrder // The sort order
                )
                while (cursor != null && cursor.moveToNext()) {
                    val item = getItem(cursor)
                    if (item.detailArticle.isNullOrEmpty() || item.detailArticle != "loy")
                    {
                        mesItems.add(item)
                    }
                }
            } catch (E: SQLiteException) {
                log("SQLiteAbortException", "SQLiteAbortException ---> " + E.message)
            } finally {
                if (cursor != null && !cursor.isClosed) {
                    count = cursor.count
                    cursor.close()
                }
                log("TransactionTaxi DAO", "count cursor ---> $count")
            }
        }
        return mesItems
    }
    fun checkTransactionCount(saleId:String): Int {
        var count = 0
        val status =0
        try {
            val selectQuery = ("SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE "
                    + TRANSACTION_FUSION_SALEID + " = '" + saleId + "' AND " +
                     TRANSACTION_STATUS + " = " + status)
            val cursor = mDb!!.rawQuery(selectQuery, null)
            if(cursor != null)
            {
                count = cursor.count
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return count
        }
        return count
    }
    fun checkAvailableTransactions(pumpID: String?, trxNo: String): TransactionModel? {
        var item : TransactionModel? = TransactionModel()
        val status =0
        try {
            val selectQuery = ("SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHERE "
                    + TRANSACTION_PUMP_ID + " = " + pumpID + " AND " +
                    TRANSACTION_SEQUENCE_CONTROLLER + " = '" + trxNo + "' AND " + TRANSACTION_STATUS + " = " + status)
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransaction ", selectQuery)
            if(cursor.moveToNext()){
                item = getItem(cursor)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return item!!
    }
    fun checkTransactionSigned(referenceNumber: String): Boolean {
        var count = 0
        val status =0
        try {
            val selectQuery = ("SELECT  * FROM " + TRANSACTION_TABLE_NAME + " WHER`E " + TRANSACTION_REFERENCE + " = '" + referenceNumber + "' AND " + TRANSACTION_TIMS_INVOICE_STATUS + " = " + 1)
            val cursor: android.database.Cursor = mDb!!.rawQuery(selectQuery, null)
            log("checkTransactionAvailable ", selectQuery)
            count = cursor.count
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return count > 0
    }
    fun checkReferenceNumberAvailable(referenceNumber: String): Boolean {
        var count = 0
        try {
            val selectQuery = ("SELECT $TRANSACTION_REFERENCE FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_REFERENCE = '$referenceNumber'")
            val cursor: android.database.Cursor = mDb!!.rawQuery(selectQuery, null)
            log("checkReferenceNumberAvailable ", selectQuery)
            count = cursor.count
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return count > 0
    }
    fun checkTransactionCompleted(referenceNumber: String): Boolean {
        var count = 0
        try {
            val selectQuery = ("SELECT $TRANSACTION_REFERENCE FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_REFERENCE = '$referenceNumber' AND $TRANSACTION_STATUS = '1'")
            val cursor: android.database.Cursor = mDb!!.rawQuery(selectQuery, null)
            log("checkTransactionCompleted ", selectQuery)
            count = cursor.count
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return count > 0
    }
    fun checksumAvailable(checksum: String): Boolean {
        var count = 0
        try {
            val selectQuery = ("SELECT $TRANSACTION_REFERENCE FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_CHECKSUM = '$checksum'")
            val cursor: android.database.Cursor = mDb!!.rawQuery(selectQuery, null)
            log("checksumAvailable ", selectQuery)
            count = cursor.count
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
            return false
        }
        return count > 0
    }
    fun getTransaction(pumpID: String?, trxNo: String): TransactionModel? {
        var item : TransactionModel? = null
        try {
            val selectQuery = ("SELECT  * FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_PUMP_ID = '$pumpID' AND $TRANSACTION_SEQUENCE_CONTROLLER = '$trxNo' AND $TRANSACTION_STATUS = '1'")
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransaction ", selectQuery)
            if(cursor.moveToNext()){
                item = getItem(cursor)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return item
    }
    fun getTransactionByReferenceId(referenceID:String): TransactionModel? {
        var item : TransactionModel? = null
        try {
            val selectQuery = ("SELECT  * FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_REFERENCE = '$referenceID'")
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransaction ", selectQuery)
            if(cursor.moveToNext()){
                item = getItem(cursor)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return item
    }
    fun getTotalRechargeAmount(): Double {
        var amount = 0.0

        val trxItems = getTotalRechargeTransactions()
        for(trx in trxItems){
            if(trx.idTypeTransaction == 3 && trx.reference!!.contains("RCH"))
                amount += trx.amount!!
        }

        return amount
    }
    fun getTotalAmount(): Double {
        var amount = 0.0

        val trxItems = getTotalTransactions()
        for(trx in trxItems){
            if(trx.idTypeTransaction == 1 && trx.reference!!.contains("TRX"))
                amount += trx.amount!!
        }

        return amount
    }
    fun getTotalTransactionsCount(): Int {
        val trxItems = getTotalTransactions()
        return trxItems.size
    }
    fun getCanceledTransactionsCount(): Int {
        val list = ArrayList<TransactionModel>()
        val trxItems = getTotalTransactions()

        for(trx in trxItems){
            if(trx.idTypeTransaction == 2)
                list.add(trx)
        }

        return list.size
    }
    fun getRechargeTransactionsCount(): Int {
        val list = getTotalRechargeTransactions()
        return list.size
    }
    fun getRechargeCancelTransactionsCount(): Int {
        val list = ArrayList<TransactionModel>()
        val trxItems = getTotalTransactions()

        for(trx in trxItems){
            if(trx.idTypeTransaction == 4)
                list.add(trx)
        }

        return list.size
    }
    fun getNotTelecollectedTransactionsOld(dateTrx: String): ArrayList<TransactionModel> {

        mesItems.clear()
        val selection = "strftime('%Y%m%d', $TRANSACTION_DATE_TRANSACTION) =? AND $TRANSACTION_FLAG_TELECOLLECT =?"
        val selectionArgs = arrayOf(dateTrx,0.toString())
        val sortOrder = "$TRANSACTION_ID DESC"
        val cursor = mDb!!.query(
            TRANSACTION_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext() ) {
            val item = getItem(cursor)
            mesItems.add(item)
        }
        cursor.close()
        return mesItems
    }
    fun getNotTelecollectedTransactions(dateTrx:String):  ArrayList<TransactionModel>? {
        val fromDate = "$dateTrx 00:00:00"
        val toDate = "$dateTrx 23:59:59"
        val status = 0
        mesItems.clear()
        try {
            val selectQuery = ("SELECT  * FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_FLAG_TELECOLLECT = $status AND $TRANSACTION_DATE_TRANSACTION BETWEEN '$fromDate' AND '$toDate'")
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransaction ", selectQuery)
            while (cursor.moveToNext() ) {
                val item = getItem(cursor)
                mesItems.add(item)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mesItems
    }
    fun getDisputedTransactions(dateTrx:String):  ArrayList<TransactionModel>? {
        val fromDate = "$dateTrx 00:00:00"
        val toDate = "$dateTrx 23:59:59"
        val status = 0
        val disputed_status = 1
        mesItems.clear()
        try {
            val selectQuery = ("SELECT  * FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_FLAG_TELECOLLECT = $status AND $TRANSACTION_DISPUTED = $disputed_status AND $TRANSACTION_DATE_TRANSACTION BETWEEN '$fromDate' AND '$toDate'")
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getDisputedTransactions ", selectQuery)
            while (cursor.moveToNext() ) {
                val item = getItem(cursor)
                mesItems.add(item)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return mesItems
    }
    fun getTransactionByReleaseToken(releaseToken: String?): TransactionModel? {
        var item : TransactionModel? = null
        try {
            val selectQuery = ("SELECT  * FROM $TRANSACTION_TABLE_NAME WHERE $TRANSACTION_RELEASE_TOKEN = '$releaseToken'")
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransaction ", selectQuery)
            if(cursor.moveToNext()){
                item = getItem(cursor)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return item
    }

    fun getLastSavedTransaction(): TransactionModel? {
        var item : TransactionModel? = null
        try {
            val selectQuery = ("SELECT  * FROM $TRANSACTION_TABLE_NAME ORDER BY $TRANSACTION_ID DESC LIMIT 1")
            val cursor = mDb!!.rawQuery(selectQuery, null)
            log("getTransaction ", selectQuery)
            if(cursor.moveToNext()){
                item = getItem(cursor)
            }
            cursor.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return item
    }

    companion object {
        const val TRANSACTION_TABLE_NAME = "transactions" //table name 32 fields
        private const val TRANSACTION_ID = "id" //int id dans ma table sqlite
        private const val TRANSACTION_ID_TERMINAL = "terminalId" //int
        private const val TRANSACTION_ID_TYPE_TRANSACTION = "transactionTypeId" //int id dans ma table sqlite
        private const val TRANSACTION_ID_PRODUCT = "productId" //int
        private const val TRANSACTION_ATTENDANT_CODE = "attendantCode" //String
        private const val TRANSACTION_DATE_TRANSACTION = "transactionDate" //Date
        private const val TRANSACTION_AMOUNT = "amount" //double
        private const val TRANSACTION_QUANTITY = "quantity" //double
        private const val TRANSACTION_UNIT_PRICE = "unitPrice" //double
        private const val TRANSACTION_REFERENCE = "reference" //String
        private const val TRANSACTION_FLAG_CONTROLLER = "flagController" //int
        private const val TRANSACTION_SEQUENCE_CONTROLLER = "sequenceController" //String
        private const val TRANSACTION_TAG_NFC = "tagNFC" //String
        private const val TRANSACTION_KILOMETRAGE = "kilometrage" //String
        private const val TRANSACTION_PAN = "pan" //String
        private const val TRANSACTION_NOM_PORTEUR = "nomPorteur" //String
        private const val TRANSACTION_DATE_EXP = "dateExp" //String
        private const val TRANSACTION_DETAIL_ARTICLE = "detailArticle" //String
        private const val TRANSACTION_FLAG_TELECOLLECT = "flagTeleCollect" //int
        private const val TRANSACTION_MODE_PAY = "modePay" //String
        private const val TRANSACTION_PAN_LOYALTY = "panLoyalty" //String
        private const val TRANSACTION_SOLDE_CARD = "cardBalance" //String
        private const val TRANSACTION_STATUS = "transactionStatus" //String
        private const val TRANSACTION_SIGNATURE = "transactionSignature" //String
        private const val TRANSACTION_PUMP_ID = "pumpId" //String
        private const val IS_SPLIT_PAYMENT = "isSplitPayment" //String
        private const val TRANSACTION_DISCOUNT_AMOUNT = "discountAmount" //String
        private const val TRANSACTION_IS_DISCOUNT_TRANSACTIONS = "isDiscountTransactions" //Boolean
        private const val TRANSACTION_ECR_REFERENCE_NO = "ecrReferenceNo" //Boolean
        private const val TRANSACTION_PRE_AUTH_AMOUNT = "preAuthAmount" //String
        private const val TRANSACTION_REFUND_STATUS = "refundStatus" //String
        private const val TRANSACTION_VAT = "vatAmount" //String
        private const val TRANSACTION_NET_AMOUNT = "netAmount" //String
        private const val TRANSACTION_ATTENDANT_ID = "AttendantId" //String
        private const val TRANSACTION_DISCOUNT_ID = "discountId" //String
        private const val TRANSACTION_CATEGORY = "categoryId" //String
        private const val TRANSACTION_REFUND_EXPORTED = "trx_refund_exported" //int
        private const val TRANSACTION_VAT_PERCENTAGE = "vatPercentage" //int
        private const val TRANSACTION_VAT_TYPE = "vatType" //int
        private const val TRANSACTION_FCC_PRODUCT_ID = "fccProductId" //int
        private const val TRANSACTION_PRODUCT_NAME = "productName"
        private const val TRANSACTION_REFUND_AMOUNT = "refundAmount"
        private const val TRANSACTION_DAILY_LIMIT = "dailyLimit"
        private const val TRANSACTION_WEEKLY_LIMIT = "weeklyLimit"
        private const val TRANSACTION_MONTHLY_LIMIT = "monthlyLimit"
        private const val TRANSACTION_ROUNDING_ADJUSTMENT = "roundingAdjustment"
        private const val TRANSACTION_REMAINING_MONTHLY_COUNT= "remainingMonthlyCount"
        private const val  TRANSACTION_REMAINING_DAILY_COUNT  = "remainingDailyCount"
        private const val  TRANSACTION_REMAINING_WEEKLY_COUNT  = "remainingWeeklyCount"
        private const val  TRANSACTION_FPOS_CARD_ID  = "fposCardId"
        private const val  TRANSACTION_FUSION_SALEID  = "fusionSaleId"
        private const val  TRANSACTION_RELEASE_TOKEN  = "transactionReleaseToken"
        private const val  TRANSACTION_DISCOUNT_PERCENTAGE = "discountPercentage"
        private const val  TRANSACTION_DISPUTED  = "fposTrxDisputed"
        private const val  VEHICLE_NUMBER  = "vehicleNumber"
        private const val  TRANSACTION_TIMS_INVOICE_STATUS  = "timsInvoiceStatus"
        private const val  TRANSACTION_TIMS_SIGN_DETAILS  = "timsSignDetails"
        private const val  TRANSACTION_AUTHORIZED_MANAGER_ID  = "authorizedManagerID"
        private const val  TRANSACTION_QR_CODE_TICKET  = "qrCodeTicektDetails"
        private const val  TRANSACTION_DISCOUNT_TYPE  = "discountType"
        private const val  TRANSACTION_PREAUTH_ID  = "preAuthId"
        private const val  TRANSACTION_TOTAL_PAID_AMOUNT  = "totalPaidAmount"
        private const val  TRANSACTION_CHECKSUM = "transactionChecksum"
        private const val  TRANSACTION_CREDIT_STATUS  = "creditStatus"

        const val TRANSACTION_TABLE_CREATE =
            "CREATE TABLE " + TRANSACTION_TABLE_NAME + " (" +
                    TRANSACTION_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //int
                    TRANSACTION_ID_TERMINAL + " INTEGER, " +  //int
                    TRANSACTION_ID_TYPE_TRANSACTION + " INTEGER, " +
                    TRANSACTION_ID_PRODUCT + " INTEGER, " +  //int
                    TRANSACTION_ATTENDANT_CODE + " TEXT, " +  //int
                    TRANSACTION_DATE_TRANSACTION + " TEXT, " +  //Date
                    TRANSACTION_AMOUNT + " REAL, " +  //double
                    TRANSACTION_QUANTITY + " REAL, " +  //double
                    TRANSACTION_UNIT_PRICE + " REAL, " +  //double
                    TRANSACTION_REFERENCE + " TEXT, " +  //String
                    TRANSACTION_FLAG_CONTROLLER + " INTEGER, " +  //int
                    TRANSACTION_SEQUENCE_CONTROLLER + " TEXT, " +  //String
                    TRANSACTION_TAG_NFC + " TEXT, " +  //String
                    TRANSACTION_KILOMETRAGE + " TEXT, " +  //String
                    TRANSACTION_PAN + " TEXT, " +  //int
                    TRANSACTION_DETAIL_ARTICLE + " TEXT, " +  //int
                    TRANSACTION_FLAG_TELECOLLECT + " INTEGER," +
                    TRANSACTION_NOM_PORTEUR + " TEXT, " +  //String
                    TRANSACTION_DATE_EXP + " TEXT," +
                    TRANSACTION_MODE_PAY + " TEXT, " +  //String
                    TRANSACTION_PAN_LOYALTY + " TEXT, " +  //String
                    TRANSACTION_SOLDE_CARD + " TEXT, " +  //String
                    TRANSACTION_STATUS + " INTEGER, " +
                    TRANSACTION_SIGNATURE + " TEXT, " +
                    TRANSACTION_PUMP_ID + " TEXT, " +
                    IS_SPLIT_PAYMENT + " TEXT, " +
                    TRANSACTION_DISCOUNT_AMOUNT + " TEXT, " +
                    TRANSACTION_IS_DISCOUNT_TRANSACTIONS + " TEXT, " +
                    TRANSACTION_ECR_REFERENCE_NO + " TEXT, " +
                    TRANSACTION_PRE_AUTH_AMOUNT + " TEXT, " +
                    TRANSACTION_REFUND_STATUS + " TEXT, " +
                    TRANSACTION_VAT + " TEXT, " +
                    TRANSACTION_NET_AMOUNT + " TEXT, " +
                    TRANSACTION_ATTENDANT_ID + " TEXT, " +
                    TRANSACTION_DISCOUNT_ID + " TEXT, " +
                    TRANSACTION_CATEGORY + " TEXT, " +
                    TRANSACTION_REFUND_EXPORTED + " TEXT, " +
                    TRANSACTION_PRODUCT_NAME + " TEXT, " +
                    TRANSACTION_REFUND_AMOUNT + " TEXT, " +
                    TRANSACTION_DAILY_LIMIT + " TEXT, " +
                    TRANSACTION_WEEKLY_LIMIT + " TEXT, " +
                    TRANSACTION_MONTHLY_LIMIT + " TEXT, " +
                    TRANSACTION_ROUNDING_ADJUSTMENT + " TEXT, " +
                    TRANSACTION_REMAINING_MONTHLY_COUNT + " TEXT, " +
                    TRANSACTION_REMAINING_DAILY_COUNT + " TEXT, " +
                    TRANSACTION_REMAINING_WEEKLY_COUNT + " TEXT, " +
                    TRANSACTION_VAT_PERCENTAGE + " TEXT," +
                    TRANSACTION_VAT_TYPE + " TEXT," +
                    TRANSACTION_FCC_PRODUCT_ID + " TEXT," +
                    TRANSACTION_FPOS_CARD_ID + " TEXT," +
                    TRANSACTION_FUSION_SALEID + " TEXT," +
                    TRANSACTION_RELEASE_TOKEN + " TEXT," +
                    TRANSACTION_DISCOUNT_PERCENTAGE + " TEXT," +
                    TRANSACTION_DISPUTED + " INTEGER," +
                    VEHICLE_NUMBER + " TEXT," +
                    TRANSACTION_TIMS_SIGN_DETAILS + " TEXT," +
                    TRANSACTION_TIMS_INVOICE_STATUS + " TEXT," +
                    TRANSACTION_AUTHORIZED_MANAGER_ID + " INTEGER," +
                    TRANSACTION_QR_CODE_TICKET + " TEXT," +
                    TRANSACTION_DISCOUNT_TYPE + " INTEGER," +
                    TRANSACTION_PREAUTH_ID + " INTEGER," +
                    TRANSACTION_TOTAL_PAID_AMOUNT + " INTEGER," +
                    TRANSACTION_CHECKSUM + " TEXT," +
                    TRANSACTION_CREDIT_STATUS + " INTEGER); "
        private val projection = arrayOf(
            TRANSACTION_ID,                         //0     //int
            TRANSACTION_ID_TERMINAL,                //1     //int
            TRANSACTION_ID_TYPE_TRANSACTION,        //2     //int
            TRANSACTION_ID_PRODUCT,                 //3     //int
            TRANSACTION_ATTENDANT_CODE,             //4     //string
            TRANSACTION_DATE_TRANSACTION,           //5     //String
            TRANSACTION_AMOUNT,                     //6     //double
            TRANSACTION_QUANTITY,                   //7     //double
            TRANSACTION_UNIT_PRICE,                 //8     //double
            TRANSACTION_REFERENCE,                  //9     //string
            TRANSACTION_FLAG_CONTROLLER,            //10    //int
            TRANSACTION_SEQUENCE_CONTROLLER,        //11    //string
            TRANSACTION_TAG_NFC,                    //12    //string
            TRANSACTION_KILOMETRAGE,                //13    //string
            TRANSACTION_PAN,                        //14    //string
            TRANSACTION_DETAIL_ARTICLE,             //15    //string
            TRANSACTION_FLAG_TELECOLLECT,           //16    //int
            TRANSACTION_NOM_PORTEUR,                //17    //string
            TRANSACTION_DATE_EXP,                   //18    //string
            TRANSACTION_MODE_PAY,                   //19    //string
            TRANSACTION_PAN_LOYALTY,                //20    //string
            TRANSACTION_SOLDE_CARD,                 //21    //string
            TRANSACTION_STATUS,                     //22    //int
            TRANSACTION_SIGNATURE,                  //23    //string
            TRANSACTION_PUMP_ID,                    //24    //string
            IS_SPLIT_PAYMENT,                       //25    //string
            TRANSACTION_DISCOUNT_AMOUNT,            //26    //string
            TRANSACTION_IS_DISCOUNT_TRANSACTIONS,   //27    //string
            TRANSACTION_ECR_REFERENCE_NO,           //28    //string
            TRANSACTION_PRE_AUTH_AMOUNT,            //29    //string
            TRANSACTION_REFUND_STATUS,              //30    //string
            TRANSACTION_VAT,                        //31    //string
            TRANSACTION_NET_AMOUNT  ,               //32    //string
            TRANSACTION_ATTENDANT_ID ,              //33    //string
            TRANSACTION_DISCOUNT_ID,                //34    //string
            TRANSACTION_CATEGORY,                   //35    //string
            TRANSACTION_REFUND_EXPORTED  ,           //36    //int
            TRANSACTION_PRODUCT_NAME  ,                     //37
            TRANSACTION_REFUND_AMOUNT  ,                    //38   //String
            TRANSACTION_DAILY_LIMIT  ,                      //39   //String
            TRANSACTION_WEEKLY_LIMIT  ,                     //40      //String
            TRANSACTION_MONTHLY_LIMIT  ,                    //41   //String
            TRANSACTION_ROUNDING_ADJUSTMENT  ,                //42   //String
            TRANSACTION_REMAINING_MONTHLY_COUNT  ,            //43   //String
            TRANSACTION_REMAINING_DAILY_COUNT  ,              //44   //String
            TRANSACTION_REMAINING_WEEKLY_COUNT  ,            //45   //String
            TRANSACTION_VAT_PERCENTAGE,                     //46  //String
            TRANSACTION_VAT_TYPE,                           //47  //String
            TRANSACTION_FCC_PRODUCT_ID,                      //48   //String
            TRANSACTION_FPOS_CARD_ID    ,                    //49   //String
            TRANSACTION_FUSION_SALEID  ,                     //50   //String
            TRANSACTION_RELEASE_TOKEN  ,                     //50   //String
            TRANSACTION_DISCOUNT_PERCENTAGE,                       //51   //String
            TRANSACTION_DISPUTED,                        //53   //STRING
            VEHICLE_NUMBER,                        //54   //STRING
           TRANSACTION_TIMS_SIGN_DETAILS,                       //55  //String
            TRANSACTION_TIMS_INVOICE_STATUS,                       //56  //Int
            TRANSACTION_AUTHORIZED_MANAGER_ID,                       //57  //Int
            TRANSACTION_QR_CODE_TICKET,                       //58  //Int
            TRANSACTION_DISCOUNT_TYPE,                       //59  //Int
            TRANSACTION_PREAUTH_ID,                       //60  //String
            TRANSACTION_TOTAL_PAID_AMOUNT,                       //61  //String
            TRANSACTION_CHECKSUM,                       //62  //String
            TRANSACTION_CREDIT_STATUS,                       //63  //String

        )
    }
    private fun getItem(cursor: Cursor) : TransactionModel {
        val item = TransactionModel()
        item.id = cursor.getInt(projection.indexOf(TRANSACTION_ID))
        item.idTerminal = cursor.getInt(projection.indexOf(TRANSACTION_ID_TERMINAL))
        item.idTypeTransaction = cursor.getInt(projection.indexOf(TRANSACTION_ID_TYPE_TRANSACTION))
        item.idProduit = cursor.getInt(projection.indexOf(TRANSACTION_ID_PRODUCT))
        item.codePompiste = cursor.getString(projection.indexOf(TRANSACTION_ATTENDANT_CODE))
        item.dateTransaction = cursor.getString(projection.indexOf(TRANSACTION_DATE_TRANSACTION)) ?: ""
        item.amount = cursor.getDouble(projection.indexOf(TRANSACTION_AMOUNT))
        item.quantite = cursor.getDouble(projection.indexOf(TRANSACTION_QUANTITY))
        item.unitPrice = cursor.getDouble(projection.indexOf(TRANSACTION_UNIT_PRICE))
        item.reference = cursor.getString(projection.indexOf(TRANSACTION_REFERENCE))
        item.flagController = cursor.getInt(projection.indexOf(TRANSACTION_FLAG_CONTROLLER))
        item.sequenceController = cursor.getString(projection.indexOf(TRANSACTION_SEQUENCE_CONTROLLER))
        item.tagNFC = cursor.getString(projection.indexOf(TRANSACTION_TAG_NFC)) ?: ""
        item.kilometrage = cursor.getString(projection.indexOf(TRANSACTION_KILOMETRAGE))
        item.pan = cursor.getString(projection.indexOf(TRANSACTION_PAN)) ?: ""
        item.detailArticle = cursor.getString(projection.indexOf(TRANSACTION_DETAIL_ARTICLE)) ?: ""
        item.flagTelecollecte = cursor.getInt(projection.indexOf(TRANSACTION_FLAG_TELECOLLECT))
        item.nomPorteur = cursor.getString(projection.indexOf(TRANSACTION_NOM_PORTEUR))
        item.dateExp = cursor.getString(projection.indexOf(TRANSACTION_DATE_EXP))
        item.modepay = cursor.getString(projection.indexOf(TRANSACTION_MODE_PAY))
        item.panLoyalty = cursor.getString(projection.indexOf(TRANSACTION_PAN_LOYALTY)) ?: ""
        item.soldeCard = cursor.getString(projection.indexOf(TRANSACTION_SOLDE_CARD)) ?: ""
        item.transactionStatus = cursor.getInt(projection.indexOf(TRANSACTION_STATUS))
        item.transactionSignature = cursor.getString(projection.indexOf(TRANSACTION_SIGNATURE)) ?: ""
        item.pumpId = cursor.getString(projection.indexOf(TRANSACTION_PUMP_ID))
        item.isSplitPayment = cursor.getString(projection.indexOf(IS_SPLIT_PAYMENT)) == "1"
        item.discountAmount = cursor.getString(projection.indexOf(TRANSACTION_DISCOUNT_AMOUNT))
        val discount = cursor.getString(projection.indexOf(TRANSACTION_IS_DISCOUNT_TRANSACTIONS))
        item.isDiscountTransaction = discount?.toInt() ?: 0
        item.bank_reference_num = cursor.getString(projection.indexOf(TRANSACTION_ECR_REFERENCE_NO))
        item.preAuthAmount = cursor.getString(projection.indexOf(TRANSACTION_PRE_AUTH_AMOUNT))
        item.refundStatus = cursor.getString(projection.indexOf(TRANSACTION_REFUND_STATUS))
        item.vatAmount = cursor.getString(projection.indexOf(TRANSACTION_VAT))
        item.netAmount = cursor.getString(projection.indexOf(TRANSACTION_NET_AMOUNT))
        item.idPompiste = cursor.getString(projection.indexOf(TRANSACTION_ATTENDANT_ID))
        item.discountId = cursor.getInt(projection.indexOf(TRANSACTION_DISCOUNT_ID))
        item.categoryId = cursor.getInt(projection.indexOf(TRANSACTION_CATEGORY))
        item.transactionRefundExported = cursor.getInt(projection.indexOf(TRANSACTION_REFUND_EXPORTED))
        item.productName = cursor.getString(projection.indexOf(TRANSACTION_PRODUCT_NAME))
        item.refundAmount = cursor.getString(projection.indexOf(TRANSACTION_REFUND_AMOUNT))
        item.dailyCeiling = cursor.getString(projection.indexOf(TRANSACTION_DAILY_LIMIT))
        item.weeklyCeiling = cursor.getString(projection.indexOf(TRANSACTION_WEEKLY_LIMIT))
        item.monthlyCeiling = cursor.getString(projection.indexOf(TRANSACTION_MONTHLY_LIMIT))
        item.roundingAdjustment = cursor.getString(projection.indexOf(TRANSACTION_ROUNDING_ADJUSTMENT))
        item.remainingMonthlyCount = cursor.getString(projection.indexOf(TRANSACTION_REMAINING_MONTHLY_COUNT))
        item.remainingDailyCount = cursor.getString(projection.indexOf(TRANSACTION_REMAINING_DAILY_COUNT))
        item.remainingWeeklyCount = cursor.getString(projection.indexOf(TRANSACTION_REMAINING_WEEKLY_COUNT))
        item.vatPercentage = cursor.getInt(projection.indexOf(TRANSACTION_VAT_PERCENTAGE))
        item.vatType = cursor.getInt(projection.indexOf(TRANSACTION_VAT_TYPE))
        item.fccProductId = cursor.getString(projection.indexOf(TRANSACTION_FCC_PRODUCT_ID))
        item.fposCardId = cursor.getString(projection.indexOf(TRANSACTION_FPOS_CARD_ID))
        item.fccSaleId = cursor.getString(projection.indexOf(TRANSACTION_FUSION_SALEID))
        item.fccReleaseToken = cursor.getString(projection.indexOf(TRANSACTION_RELEASE_TOKEN))
        item.discountPercentage = cursor.getString(projection.indexOf(TRANSACTION_DISCOUNT_PERCENTAGE))
        item.isDisputedTrx = cursor.getInt(projection.indexOf(TRANSACTION_DISPUTED))
        item.vehicleNumber = cursor.getString(projection.indexOf(VEHICLE_NUMBER))
        item.authorizedManagerID = cursor.getInt(projection.indexOf(TRANSACTION_AUTHORIZED_MANAGER_ID))
        item.timsInvoiceStatus = cursor.getInt(projection.indexOf(TRANSACTION_TIMS_INVOICE_STATUS))
        item.discountType = cursor.getInt(projection.indexOf(TRANSACTION_DISCOUNT_TYPE))
        item.preAuthId = cursor.getString(projection.indexOf(TRANSACTION_PREAUTH_ID))
        item.totalPaidAmount = cursor.getDouble(projection.indexOf(TRANSACTION_TOTAL_PAID_AMOUNT))
        item.transactionChecksum = cursor.getString(projection.indexOf(TRANSACTION_CHECKSUM))
        item.creditStatus = cursor.getInt(projection.indexOf(TRANSACTION_CREDIT_STATUS))
        val timsSignSting =cursor.getString(projection.indexOf(TRANSACTION_TIMS_SIGN_DETAILS))
        val qrCodeTicketString =cursor.getString(projection.indexOf(TRANSACTION_QR_CODE_TICKET))
        try {
            if(timsSignSting != null) {
                item.timsSignDetails = Gson().fromJson(timsSignSting, TIMSSignModel::class.java)
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

        try {
            if(qrCodeTicketString != null) {
                item.qrCodeTicket = Gson().fromJson(qrCodeTicketString, QRCodeTicketResponse::class.java)
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

        return item
    }
    private fun getValues(mTransaction:TransactionModel):ContentValues
    {
        val values = ContentValues()
         /*  Discount Calculation*/
        var totalPaidAmount = mTransaction.amount
        if(!mTransaction.discountAmount.isNullOrEmpty() && mTransaction.isDiscountTransaction!! == 1)
        {
            totalPaidAmount = if(mTransaction.amount!! <= 0) {
                0.0
            } else {
                mTransaction.amount!!.toDouble() -  mTransaction.discountAmount!!.toDouble()

            }
        }
        if (mTransaction.creditStatus != null ) {
            values.put(TRANSACTION_CREDIT_STATUS,mTransaction.creditStatus)
        }
        if (mTransaction.transactionChecksum != null ) {
            values.put(TRANSACTION_CHECKSUM,mTransaction.transactionChecksum)
        }
        if (totalPaidAmount != null ) {
            values.put(TRANSACTION_TOTAL_PAID_AMOUNT,totalPaidAmount)
        }
        if (mTransaction.preAuthId != null ) {
            values.put(TRANSACTION_PREAUTH_ID,mTransaction.preAuthId)
        }
        if (mTransaction.vatPercentage != null ) {
            values.put(TRANSACTION_VAT_PERCENTAGE,mTransaction.vatPercentage)
        }
        if (mTransaction.vatType != null) {
            values.put(TRANSACTION_VAT_TYPE,mTransaction.vatType)
        }
        if (!mTransaction.fccProductId.isNullOrEmpty()) {
            values.put(TRANSACTION_FCC_PRODUCT_ID,mTransaction.fccProductId)
        }
        if (mTransaction.idTerminal != null) {
            values.put(TRANSACTION_ID_TERMINAL,mTransaction.idTerminal)
        } else { values.put(TRANSACTION_ID_TERMINAL,0) }

        if (mTransaction.idProduit != null) {
            values.put(TRANSACTION_ID_PRODUCT,mTransaction.idProduit)
        }
        if (mTransaction.codePompiste != null) {
            values.put(TRANSACTION_ATTENDANT_CODE,mTransaction.codePompiste)
        }
        if (mTransaction.dateTransaction != null) {
            values.put(TRANSACTION_DATE_TRANSACTION, mTransaction.dateTransaction)
        }
        if (mTransaction.idTypeTransaction != null) {
            values.put(TRANSACTION_ID_TYPE_TRANSACTION,mTransaction.idTypeTransaction)
        }
        if (mTransaction.amount != null) {
            values.put(TRANSACTION_AMOUNT,mTransaction.amount)
        }

        if (mTransaction.quantite != null) {
            values.put(TRANSACTION_QUANTITY,mTransaction.quantite)
        } else { values.put(TRANSACTION_QUANTITY,0.0)  }

        if (mTransaction.unitPrice != null) {
            values.put(TRANSACTION_UNIT_PRICE,mTransaction.unitPrice)
        }

        if (mTransaction.reference != null) {
            values.put(TRANSACTION_REFERENCE,mTransaction.reference)
        }

        if (mTransaction.sequenceController != null) {
            values.put(TRANSACTION_SEQUENCE_CONTROLLER,mTransaction.sequenceController)
        }

        if (mTransaction.flagController != null) {
            values.put(TRANSACTION_FLAG_CONTROLLER,mTransaction.flagController)
        }
        if (mTransaction.tagNFC != null) {
            values.put(TRANSACTION_TAG_NFC, mTransaction.tagNFC)
        }
        if (mTransaction.kilometrage != null) {
            values.put(TRANSACTION_KILOMETRAGE,mTransaction.kilometrage)
        }

        if (mTransaction.pan != null) {
            values.put(TRANSACTION_PAN,mTransaction.pan)
        } else { values.put(TRANSACTION_PAN,"") }

        if (mTransaction.nomPorteur != null) {
            values.put(TRANSACTION_NOM_PORTEUR,mTransaction.nomPorteur)
        }
        if (mTransaction.dateExp != null) {
            values.put(TRANSACTION_DATE_EXP,mTransaction.dateExp)
        }
        if (mTransaction.detailArticle != null) {
            values.put(TRANSACTION_DETAIL_ARTICLE,mTransaction.detailArticle)
        }
        if (mTransaction.flagTelecollecte != null) {
            values.put(TRANSACTION_FLAG_TELECOLLECT,mTransaction.flagTelecollecte)
        }
        if (mTransaction.modepay != null) {
            values.put(TRANSACTION_MODE_PAY,mTransaction.modepay)
        }
        if (mTransaction.panLoyalty != null) {
            values.put(TRANSACTION_PAN_LOYALTY,mTransaction.panLoyalty)
        } else { values.put(TRANSACTION_PAN_LOYALTY,"") }

        if (mTransaction.soldeCard != null && mTransaction.soldeCard!!.isNotEmpty()) {
            values.put(TRANSACTION_SOLDE_CARD,mTransaction.soldeCard!!.replace(" ",""))
        }
        if (mTransaction.transactionStatus != null) {
            values.put(TRANSACTION_STATUS,mTransaction.transactionStatus!!)
        }
        if (mTransaction.transactionSignature != null) {
            values.put(TRANSACTION_SIGNATURE, mTransaction.transactionSignature)
        }
        if (mTransaction.pumpId != null) {
            values.put(TRANSACTION_PUMP_ID,mTransaction.pumpId!!)
        }
        if (mTransaction.isSplitPayment != null) {
            values.put(IS_SPLIT_PAYMENT,mTransaction.isSplitPayment)
        }
        else {
            values.put(IS_SPLIT_PAYMENT,false)
        }
        if (mTransaction.isDiscountTransaction != null) {
            values.put(TRANSACTION_IS_DISCOUNT_TRANSACTIONS,mTransaction.isDiscountTransaction)
        }
        if (mTransaction.discountAmount != null && mTransaction.isDiscountTransaction == 1) {
            values.put(TRANSACTION_DISCOUNT_AMOUNT,mTransaction.discountAmount)
            values.put(TRANSACTION_DISCOUNT_ID,mTransaction.discountId)
        }
        if(!mTransaction.bank_reference_num.isNullOrEmpty()) {
            values.put(TRANSACTION_ECR_REFERENCE_NO,mTransaction.bank_reference_num)
        }
        if (mTransaction.preAuthAmount != null) {
            values.put(TRANSACTION_PRE_AUTH_AMOUNT,mTransaction.preAuthAmount)
        }
        if (mTransaction.refundStatus != null) {
            values.put(TRANSACTION_REFUND_STATUS,mTransaction.refundStatus)
        }
        if(mTransaction.idPompiste != null)
        {
            values.put(TRANSACTION_ATTENDANT_ID,mTransaction.idPompiste)
        }
        if (mTransaction.vatAmount != null) {
            values.put(TRANSACTION_VAT,mTransaction.vatAmount)
        }
        if (mTransaction.netAmount != null) {
            values.put(TRANSACTION_NET_AMOUNT,mTransaction.netAmount)
        }
        if (mTransaction.categoryId != null) {
            values.put(TRANSACTION_CATEGORY,mTransaction.categoryId)
        }
        if (mTransaction.transactionRefundExported != null) {
            values.put(TRANSACTION_REFUND_EXPORTED,mTransaction.transactionRefundExported)
        }
        if (mTransaction.timsSignDetails != null) {
            Log.i(TAG,"timsSignDetails:: "+Gson().toJson(mTransaction.timsSignDetails).toString())
            values.put(TRANSACTION_TIMS_SIGN_DETAILS,Gson().toJson(mTransaction.timsSignDetails).toString())
        }
        if (mTransaction.productName != null) {
            values.put(TRANSACTION_PRODUCT_NAME,mTransaction.productName)
        }
        if (mTransaction.idTypeTransaction == 3 || mTransaction.idTypeTransaction == 4)
            values.put(TRANSACTION_STATUS, 1)

        if (mTransaction.refundAmount != null) {
            values.put(TRANSACTION_REFUND_AMOUNT,mTransaction.refundAmount)
        }
        if (mTransaction.dailyCeiling != null) {
            values.put(TRANSACTION_DAILY_LIMIT,mTransaction.dailyCeiling)
        }
        if (mTransaction.weeklyCeiling != null) {
            values.put(TRANSACTION_WEEKLY_LIMIT,mTransaction.weeklyCeiling)
        }
        if (mTransaction.monthlyCeiling != null) {
            values.put(TRANSACTION_MONTHLY_LIMIT,mTransaction.monthlyCeiling)
        }
        if (mTransaction.roundingAdjustment != null) {
            values.put(TRANSACTION_ROUNDING_ADJUSTMENT,mTransaction.roundingAdjustment)
        }
        if (mTransaction.remainingMonthlyCount != null) {
            values.put(TRANSACTION_REMAINING_MONTHLY_COUNT,mTransaction.remainingMonthlyCount)
        }
        if (mTransaction.remainingWeeklyCount != null) {
            values.put(TRANSACTION_REMAINING_WEEKLY_COUNT,mTransaction.remainingWeeklyCount)
        }
        if (mTransaction.remainingDailyCount != null) {
            values.put(TRANSACTION_REMAINING_DAILY_COUNT,mTransaction.remainingDailyCount)
        }
        if (mTransaction.fposCardId != null) {
            values.put(TRANSACTION_FPOS_CARD_ID,mTransaction.fposCardId)
        }
        if (mTransaction.fccSaleId != null) {
            values.put(TRANSACTION_FUSION_SALEID,mTransaction.fccSaleId)
        }
        if (mTransaction.fccReleaseToken != null) {
            values.put(TRANSACTION_RELEASE_TOKEN,mTransaction.fccReleaseToken)
        }
        if (mTransaction.discountPercentage != null) {
            values.put(TRANSACTION_DISCOUNT_PERCENTAGE,mTransaction.discountPercentage)
        }
        if (mTransaction.discountType != null) {
            values.put(TRANSACTION_DISCOUNT_TYPE,mTransaction.discountType)
        }
        if (mTransaction.isDisputedTrx != null) {
            values.put(TRANSACTION_DISPUTED,mTransaction.isDisputedTrx)
        }
        if (mTransaction.vehicleNumber != null) {
            values.put(VEHICLE_NUMBER,mTransaction.vehicleNumber)
        }
        if (mTransaction.authorizedManagerID != null) {
            values.put(TRANSACTION_AUTHORIZED_MANAGER_ID,mTransaction.authorizedManagerID)
        }
        if (mTransaction.timsInvoiceStatus != null) {
            values.put(TRANSACTION_TIMS_INVOICE_STATUS,mTransaction.timsSignDetails!!.invoice_details!!.timsInvoiceStatus)
        }
        if (mTransaction.qrCodeTicket != null) {
            val jsonString = Gson().toJson(mTransaction.qrCodeTicket)
            values.put(TRANSACTION_QR_CODE_TICKET,jsonString)
        }

        return values
    }

}