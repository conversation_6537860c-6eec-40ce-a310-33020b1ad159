package app.rht.petrolcard.database.baseclass

import android.content.Context
import android.content.Intent
import android.os.Environment
import android.util.Log
import android.widget.Toast

import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.database.MainDatabaseClassException
import app.rht.petrolcard.ui.reference.activity.ReferenceActivity
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.LogWriter
import app.rht.petrolcard.utils.Utils.isExternalStorageAvailable
import app.rht.petrolcard.utils.UtilsCardInfo

import app.rht.petrolcard.utils.constant.AppConstant
import com.an.deviceinfo.device.model.App
import net.sqlcipher.database.SQLiteDatabase
import wangpos.sdk4.libbasebinder.Core
import java.text.SimpleDateFormat
import java.util.*


open class MainDataBaseClass{


    var mHandler: DatabaseHandler? = null
    var mHandler2: DatabaseHandler? = null
    var mHandler3: DatabaseHandler? = null
    var backupDbHandler: DatabaseHandler? = null

    var backupDb: SQLiteDatabase? = null
    val mcore: Core? = null


    var dbPath = ""
    lateinit var prefs : AppPreferencesHelper

    companion object {
        private val VERSION = 28

        // The name of the file that represents my database
        val DatabaseFileName = "petrolCardAppV3.db"
        val DatabaseFileNameV3 = "FBSPAYV3.db"
        val DatabaseFileName2: String = AppConstant.SD_CARD + "petrolCardAppV3_db2.db"
        val DatabaseFileName3: String = AppConstant.SECURE_INTERNAL_STORAGE + "petrolCardAppV3_db3.db"

        lateinit var appContext: Context
            private set

        var sn: String? = null
        var mDb: SQLiteDatabase? = null
            private set
        var mDb2: SQLiteDatabase? = null
            private set
        var mDb3: SQLiteDatabase? = null
            private set
    }
    init {
      prefs = MainApp.getPrefs()
    }

     open fun getContext(): Context? {
         return appContext
     }
     fun isOpen(): Boolean {
         return mDb != null && mDb!!.isOpen
     }

     init {

         appContext = MainApp.appContext

         sn = MainApp.sn

         try {
            val path = Environment.getExternalStorageDirectory().path
             dbPath= path+ AppConstant.EXTERNAL_STORAGE_DB_PATH
            // Log.i("MainDataBaseClass", "dbPath:: $dbPath")

             mHandler = DatabaseHandler.getInstance(appContext, dbPath, null, VERSION)
//             backupDbHandler = DatabaseHandler.getInstance(mcontext!!, dbPath, null, VERSION)
             if (isSdCARD()) {
                 mHandler2 = DatabaseHandler.getInstance(appContext, DatabaseFileName2, null, VERSION)
             }
             if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                 mHandler3 = DatabaseHandler.getInstance(appContext, DatabaseFileName3, null, VERSION)
             }
         } catch (e:Exception){
             e.printStackTrace()
         }
     }

     fun close() {
         /*if (mDb != null && mDb!!.isOpen) mDb!!.close()
         if (isSdCARD(mcontext) && mDb2 != null && mDb2!!.isOpen) {
             mDb2!!.close()
         }
         if (mDb3 != null && AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3!!.isOpen) {
             mDb3!!.close()
         }*/
     }
     fun open(): SQLiteDatabase? {
         try {
             val mCore = MainApp.mCore
             val posType = BuildConfig.POS_TYPE
             // No need to close the last database since getWritableDatabase takes care of it
             if (mDb == null || !mDb!!.isOpen) {
                 SQLiteDatabase.loadLibs(appContext)

                 mDb = if (posType != "LANDI") {
                     val password = UtilsCardInfo.genEncryptKey(mCore, sn!!)
                     Log.e("MainDataBaseClass", "Password:: $password")
                     mHandler!!.getWritableDatabase(password)
                 } else {
                     mHandler!!.getWritableDatabase(sn)
                 }
             }
             if (isSdCARD() && (mDb2 == null || mDb2 != null && !mDb2!!.isOpen)) {
                 SQLiteDatabase.loadLibs(appContext)
                 mDb2 = if (BuildConfig.POS_TYPE != "LANDI") mHandler2!!.getWritableDatabase(
                     UtilsCardInfo.genEncryptKey(mCore, sn!!)
                 ) else mHandler2!!.getWritableDatabase(sn)
             }
             if (mDb3 == null && AppConstant.USE_SECURE_INTERNAL_STORAGE || mDb3 != null && !mDb3!!.isOpen) {
                 SQLiteDatabase.loadLibs(appContext)
                 mDb3 = if (BuildConfig.POS_TYPE != "LANDI") mHandler3!!.getWritableDatabase(
                     UtilsCardInfo.genEncryptKey(mCore, sn!!)
                 ) else mHandler3!!.getWritableDatabase(sn)
             }
         }
         catch (e:Exception)
         {
             e.printStackTrace()
          //   Toast.makeText(appContext, appContext.getString(R.string.no_database_access),Toast.LENGTH_LONG).show()
            // appContext.startActivity((Intent(appContext,ReferenceActivity::class.java)))
         }
         return mDb
     }
     fun getDb(): SQLiteDatabase? {
         return mDb
     }

    fun isSdCARD():Boolean {
        return isExternalStorageAvailable() && MainApp.getPrefs().isUseSdCard
    }

    fun log(tag: String?, msg: String) {
        Log.i(tag, msg)
        var date = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault()).format(Calendar.getInstance().time)
        date = prefs.logReferenceNo.ifEmpty {
            val count = prefs.logCount
            "${date}_${count}"
        }
        if(prefs.getReferenceModel() != null && prefs.getReferenceModel()!!.LOG == 1) {
            val logWriter = LogWriter(date.toString())
            logWriter.appendLog(tag, msg)
        }
    }
 }