package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.DatabaseUtils

class FuelTrxCountDao : MainDataBaseClass() {
    companion object {
         val FUEL_TRX_COUNT_TABLE = "tbl_fuel_trx_count" //old table name compteurFuel
        private val ID = "id" //int
        private val AMOUNT_TRX = "amount"
        val FUEL_TRX_COUNT_TABLE_CREATE =
            "CREATE TABLE " + FUEL_TRX_COUNT_TABLE + " (" +
                    ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    AMOUNT_TRX + " TEXT) ;"

    }
    fun insert(amount: String?): Long {
        val values = ContentValues()
        values.put(AMOUNT_TRX, amount)
        val newRowId: Long = mDb!!.replace(FUEL_TRX_COUNT_TABLE, null, values)

        /*Thread{
            val backupRowId = backupDb!!.replace(FUEL_TRX_COUNT_TABLE, null, values)
        }.run()*/
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && isSdCARD() && mDb2!=null)
            mDb2!!.replace(FUEL_TRX_COUNT_TABLE, null, values)

        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && isSdCARD() && mDb3!=null)
            mDb3!!.replace(FUEL_TRX_COUNT_TABLE, null, values)

        return newRowId
    }
    fun delete(): Int {
        val newRowId = mDb!!.delete(FUEL_TRX_COUNT_TABLE, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(FUEL_TRX_COUNT_TABLE, null, null)
        }.run()*/
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && isSdCARD() && mDb2!=null)
            mDb2!!.delete(FUEL_TRX_COUNT_TABLE, null, null)

        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && isSdCARD() && mDb3!=null)
            mDb3!!.delete(FUEL_TRX_COUNT_TABLE, null, null)

        return newRowId
    }


    fun count(): Long {
        return DatabaseUtils.queryNumEntries(mDb, FUEL_TRX_COUNT_TABLE)
    }

    fun get(): Int {
        var id = 0 // id dans ma base SQLite
        val projection = arrayOf<String>(
            ID
        )
        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = "$ID ASC"
        val cursor = mDb!!.query(
            FUEL_TRX_COUNT_TABLE,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            id = cursor.getInt(0) //id dans ma base SQLite
        }
        cursor.close()
        return id
    } //public compteur selectionner()

}