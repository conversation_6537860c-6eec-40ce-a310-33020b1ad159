package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.iccpayment.model.PhotoModel
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.Cursor
import net.sqlcipher.database.SQLiteException

class PhotoDao : MainDataBaseClass()
{
    companion object {
        // 6 fields
        const val PHOTO_TABLE_NAME = "photo" //table name 5 fields
        const val PHOTO_ID = "id" //int id dans ma table sqlite
        const val PHOTO_FILE_NAME = "fileName" //String
        const val PHOTO_REFERENCE_TRANSACTION = "referenceTransaction" //String
        const val PHOTO_OPT_STRING = "fieldsOptionalString" //String
        const val PHOTO_OPT_INT = "optionalInteger" //int

        const val PHOTO_TABLE_CREATE = "CREATE TABLE " + PHOTO_TABLE_NAME + " (" +
                PHOTO_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //int
                PHOTO_FILE_NAME + " TEXT, " +  //String
                PHOTO_REFERENCE_TRANSACTION + " TEXT, " +  //String
                PHOTO_OPT_STRING + " TEXT, " +  //String
                PHOTO_OPT_INT + " INTEGER);" // int
    }



    fun insertPhotoDao(model: PhotoModel): Int {
        try {
            val values = ContentValues()
            values.put(PHOTO_FILE_NAME, model.fileName!!)
            values.put(PHOTO_REFERENCE_TRANSACTION, model.referenceTransaction)
            values.put(PHOTO_OPT_STRING, model.fieldsOptionalString)
            values.put(PHOTO_OPT_INT, model.optionalInteger)
            val newRowId = mDb!!.insert(PHOTO_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(PHOTO_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(PHOTO_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(PHOTO_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun selectData(): List<PhotoModel> {
        val mesItems: MutableList<PhotoModel> = ArrayList<PhotoModel>()
        var id: Int // id dans ma base SQLite
        var nomFichier: String
        var referenceTransaction: String
        var champsOptionnelString: String
        var champsOptionnelInteger: Int

        val projection = arrayOf<String>(
           PHOTO_ID,
            PHOTO_FILE_NAME,
           PHOTO_REFERENCE_TRANSACTION,
           PHOTO_OPT_STRING,
           PHOTO_OPT_INT
        )

        val sortOrder: String = "$PHOTO_ID ASC"
        val cursor = mDb!!.query(
           PHOTO_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor != null && cursor.moveToNext()) {
            id = cursor.getInt(0) //id dans ma base SQLite
            nomFichier = cursor.getString(1)
            referenceTransaction = cursor.getString(2)
            champsOptionnelString = cursor.getString(3)
            champsOptionnelInteger = cursor.getInt(4)
            mesItems.add(
                PhotoModel(
                    id,
                    nomFichier,
                    referenceTransaction,
                    champsOptionnelString,
                    champsOptionnelInteger
                )
            )
        }
        cursor!!.close()
        return mesItems
    }
    fun selectionnerByFlag(flag: Int): List<PhotoModel>? {
        val mesItems: MutableList<PhotoModel> = ArrayList<PhotoModel>()
        var id: Int // id dans ma base SQLite
        var nomFichier: String
        var referenceTransaction: String
        var champsOptionnelString: String
        var champsOptionnelInteger: Int

        // Define a projection that specifies which columns from the database
        // you will actually use after this query.
        val projection = arrayOf<String>(
          PHOTO_ID,
            PHOTO_FILE_NAME,
          PHOTO_REFERENCE_TRANSACTION,
          PHOTO_OPT_STRING,
          PHOTO_OPT_INT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder: String = PHOTO_ID + " ASC"

        // Filter results WHERE "title" = 'My Title'
        val selection: String = PHOTO_OPT_INT + " = ?"
        val selectionArgs = arrayOf(flag.toString())
        var cursor: Cursor? = null
        if (mDb!!.isOpen) cursor = mDb!!.query(
          PHOTO_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor != null && cursor.moveToNext()) {
            id = cursor.getInt(0) //id dans ma base SQLite
            nomFichier = cursor.getString(1)
            referenceTransaction = cursor.getString(2)
            champsOptionnelString = cursor.getString(3)
            champsOptionnelInteger = cursor.getInt(4)
            mesItems.add(
                PhotoModel(
                    id,
                    nomFichier,
                    referenceTransaction,
                    champsOptionnelString,
                    champsOptionnelInteger
                )
            )
        }
        cursor!!.close()
        return mesItems
    }
    fun updateFlagPhotoById(mId: Int, mFlagTelecollecte: Int) {
        val values = ContentValues()
        values.put(PHOTO_OPT_INT, mFlagTelecollecte)
        val newRowId = mDb!!.update(
            PHOTO_TABLE_NAME,
            values,
            PHOTO_ID + " = ?",
            arrayOf(mId.toString())
        )
        if ( isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(
                PHOTO_TABLE_NAME,
                values,
                PHOTO_ID + " = ?",
                arrayOf(mId.toString())
            )
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(
                PHOTO_TABLE_NAME,
                values,
                PHOTO_ID + " = ?",
                arrayOf(mId.toString())
            )
        }
    }

}