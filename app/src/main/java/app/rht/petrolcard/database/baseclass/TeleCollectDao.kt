package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.menu.model.TelecollectDataModel
import app.rht.petrolcard.utils.constant.AppConstant
import java.util.ArrayList
import net.sqlcipher.DatabaseUtils

class TeleCollectDao : MainDataBaseClass() {
    companion object {

        const val TELECOLLECTE_TABLE_NAME = "telecollecte"
        const val TELECOLLECTE_ID = "id"
        const val TELECOLLECTE_REFERENCE = "reference"
        const val TELECOLLECTE_DATE = "dateTelecollecte"
        const val TELECOLLECTE_NBR_TRANSACTIONS = "numberOfTransactions"
        const val TELECOLLECTE_NBR_ANN_TRANSACTIONS = "numberOfAnnulationsTransactions"
        const val TELECOLLECTE_NBR_RECHARGES = "numberOfRecharges"
        const val TELECOLLECTE_NBR_ANN_RECHARGES = "numberOfAnnulationsRecharges"
        const val TELECOLLECTE_TOTAL_TRANSACTIONS = "totalTransactions"
        const val TELECOLLECTE_TOTAL_ANN_TRANSACTIONS = "totalAnnulationsTransactions"
        const val TELECOLLECTE_TOTAL_RECHARGES = "totalRecharges"
        const val TELECOLLECTE_TOTAL_ANN_RECHARGES = "totalAnnulationsRecharges"

        val TELECOLLECTE_TABLE_CREATE =
            "CREATE TABLE " +
                    TELECOLLECTE_TABLE_NAME + " (" +
                    TELECOLLECTE_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                    TELECOLLECTE_REFERENCE + " TEXT, " +
                    TELECOLLECTE_DATE + " TEXT, " +
                    TELECOLLECTE_NBR_TRANSACTIONS + " INTEGER, " +
                    TELECOLLECTE_NBR_ANN_TRANSACTIONS + " INTEGER, " +
                    TELECOLLECTE_NBR_RECHARGES + " INTEGER, " +
                    TELECOLLECTE_NBR_ANN_RECHARGES + " INTEGER, " +
                    TELECOLLECTE_TOTAL_TRANSACTIONS + " REAL, " +
                    TELECOLLECTE_TOTAL_ANN_TRANSACTIONS + " REAL, " +
                    TELECOLLECTE_TOTAL_RECHARGES + " REAL, " +
                    TELECOLLECTE_TOTAL_ANN_RECHARGES + " REAL);"
    }

    fun insertTeleCollectDao(mTelecollecte: TelecollectDataModel): Int {

        val values = ContentValues()
        values.put(TELECOLLECTE_REFERENCE, mTelecollecte.reference)
        if (mTelecollecte.dateTelecollecte != null) {
            values.put(TELECOLLECTE_DATE, mTelecollecte.dateTelecollecte)
        }
        values.put(TELECOLLECTE_NBR_TRANSACTIONS, mTelecollecte.nombreTransactions)
        values.put(TELECOLLECTE_NBR_ANN_TRANSACTIONS, mTelecollecte.nombreAnnulationsTransactions)
        values.put(TELECOLLECTE_NBR_RECHARGES, mTelecollecte.nombreRecharges)
        values.put(TELECOLLECTE_NBR_ANN_RECHARGES, mTelecollecte.nombreAnnulationsRecharges)
        values.put(TELECOLLECTE_TOTAL_TRANSACTIONS, mTelecollecte.totalTransactionsTaxis)
        values.put(TELECOLLECTE_TOTAL_ANN_TRANSACTIONS, mTelecollecte.totalAnnulationsTransactions)
        values.put(TELECOLLECTE_TOTAL_RECHARGES, mTelecollecte.totalRecharges)
        values.put(TELECOLLECTE_TOTAL_ANN_RECHARGES, mTelecollecte.totalAnnulationsRecharges)
        val newRowId = mDb!!.insert(TELECOLLECTE_TABLE_NAME, null, values).toInt()
        /*Thread{
            val backupRowId = backupDb!!.insert(TELECOLLECTE_TABLE_NAME, null, values).toInt()
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.insert(TELECOLLECTE_TABLE_NAME, null, values).toInt()
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.insert(TELECOLLECTE_TABLE_NAME, null, values).toInt()
        }
        return newRowId
    }

    fun count(): Long {
        return DatabaseUtils.queryNumEntries(mDb, TELECOLLECTE_TABLE_NAME)
    }


}
