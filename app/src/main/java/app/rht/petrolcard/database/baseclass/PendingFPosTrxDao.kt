package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.fuelpos.models.TransactionData
import app.rht.petrolcard.utils.helpers.BaseUtils
import net.sqlcipher.Cursor
import kotlin.collections.ArrayList

class PendingFPosTrxDao : MainDataBaseClass() {

    private var trxItems: ArrayList<TransactionData> = ArrayList()

    fun insert(item: TransactionData): Int {
        val values = ContentValues()

        values.put(TRX_TOKEN,item.trxToken)
        values.put(PUMP,item.pump)
        values.put(COMPLETION_CODE,item.completionCode)
        values.put(CURRENCY,item.currency)
        values.put(CHECKSUM,item.checksum)
        values.put(DATE_TIME,item.dateTime)
        values.put(PRODUCT_NAME,item.productCode)
        values.put(PRODUCT_CODE,item.productName)
        values.put(QTY,item.quantity)
        values.put(UNIT_PRICE,item.unitPrice)
        values.put(VAT,item.vatPercentage)
        values.put(VAT_AMOUNT,item.vatAmount)
        values.put(TOTAL_AMOUNT,item.totalAmount)
        values.put(TELECOLLECT_STATUS,0)

        val newRowId = mDb!!.insert(PENDING_TRX_TABLE_NAME,null,values).toInt()
        /*Thread{
            val backupRowId = backupDb!!.insert(PENDING_TRX_TABLE_NAME,null,values).toInt()
        }.run()*/
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.insert(PENDING_TRX_TABLE_NAME,null,values).toInt() // returns the id of the created record
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.insert(PENDING_TRX_TABLE_NAME,null,values).toInt() // returns the id of the created record
        }
        return newRowId
    }
    fun update(item: TransactionData) {
        val values = ContentValues()

        values.put(TRX_TOKEN,item.trxToken)
        values.put(PUMP,item.pump)
        values.put(COMPLETION_CODE,item.completionCode)
        values.put(CURRENCY,item.currency)
        values.put(CHECKSUM,item.checksum)
        values.put(DATE_TIME,item.dateTime)
        values.put(PRODUCT_NAME,item.productCode)
        values.put(PRODUCT_CODE,item.productName)
        values.put(QTY,item.quantity)
        values.put(UNIT_PRICE,item.unitPrice)
        values.put(VAT,item.vatPercentage)
        values.put(VAT_AMOUNT,item.vatAmount)
        values.put(TOTAL_AMOUNT,item.totalAmount)
        values.put(TELECOLLECT_STATUS,item.teleCollectStatus)

        val newRowId = mDb!!.update(PENDING_TRX_TABLE_NAME,values, "$ID = ?",arrayOf("${item.id}"))
        /*Thread{
            val backupRowId = backupDb!!.update(PENDING_TRX_TABLE_NAME,values, "$ID = ?",arrayOf("${item.id}"))
        }.run()*/
        if ( BaseUtils.isSdCARD() && mDb2 != null) {
            val newRowId2 = mDb2!!.update(PENDING_TRX_TABLE_NAME,values, "$ID = ?",arrayOf("${item.id}"))
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE && mDb3 != null) {
            val newRowId3 = mDb3!!.update(PENDING_TRX_TABLE_NAME,values, "$ID = ?",arrayOf("${item.id}"))
        }
    }
    fun get(status: Int = 0): ArrayList<TransactionData> {
        trxItems.clear()

        val sortOrder = "$ID DESC"
        val cursor = mDb!!.query(
            PENDING_TRX_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            "$TELECOLLECT_STATUS = $status",  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            val item = getItem(cursor)
            trxItems.add(item)
        }
        cursor.close()
        return trxItems
    }

   companion object {
       const val PENDING_TRX_TABLE_NAME = "tblPendingFposTrx"

        private const val ID = "id"
        private const val TRX_TOKEN = "trxToken"
        private const val PUMP = "pump"
        private const val COMPLETION_CODE = "code"
        private const val CURRENCY = "currency"
        private const val CHECKSUM = "checksum"
        private const val DATE_TIME = "date"
        private const val PRODUCT_NAME = "product"
        private const val PRODUCT_CODE = "productCode"
        private const val QTY = "qty"
        private const val UNIT_PRICE = "unitPrice"
        private const val VAT = "vat"
        private const val VAT_AMOUNT = "vatAmount"
        private const val TOTAL_AMOUNT = "total"
        private const val TELECOLLECT_STATUS = "status"

       const val PENDING_FPOS_TRX_TABLE_CREATE = "CREATE TABLE " + PENDING_TRX_TABLE_NAME + " (" +
               ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
               TRX_TOKEN + " TEXT, " +
               PUMP + " TEXT, " +
               COMPLETION_CODE + " TEXT, " +
               CURRENCY + " TEXT, " +
               CHECKSUM + " TEXT, " +
               DATE_TIME + " TEXT, " +
               PRODUCT_NAME + " TEXT, " +
               PRODUCT_CODE + " TEXT, " +
               QTY + " TEXT, " +
               UNIT_PRICE + " TEXT, " +
               VAT + " TEXT, " +
               VAT_AMOUNT + " TEXT, " +
               TOTAL_AMOUNT + " TEXT, " +
               TELECOLLECT_STATUS + " INTEGER); "

       private val projection = arrayOf(
           ID,                                      //0     //int
           TRX_TOKEN,                               //1     //string
           PUMP,                                    //2     //string
           COMPLETION_CODE,                         //3     //string
           CURRENCY,                                //4     //string
           CHECKSUM,                                //5     //string
           DATE_TIME,                               //6     //string
           PRODUCT_NAME,                            //7     //string
           PRODUCT_CODE,                            //8     //string
           QTY,                                     //9     //string
           UNIT_PRICE,                              //10     //string
           VAT,                                     //11     //string
           VAT_AMOUNT,                              //12     //string
           TOTAL_AMOUNT,                            //13     //string
           TELECOLLECT_STATUS,                      //14     //string
       )
    }
   private fun getItem(cursor: Cursor) : TransactionData {
        val item = TransactionData()
        item.id = cursor.getInt(projection.indexOf(ID))
        item.trxToken = cursor.getString(projection.indexOf(TRX_TOKEN))
        item.pump = cursor.getString(projection.indexOf(PUMP))
        item.completionCode = cursor.getString(projection.indexOf(COMPLETION_CODE))
        item.currency = cursor.getString(projection.indexOf(CURRENCY))
        item.checksum = cursor.getString(projection.indexOf(CHECKSUM))
        item.dateTime = cursor.getString(projection.indexOf(DATE_TIME))
        item.productName = cursor.getString(projection.indexOf(PRODUCT_NAME))
        item.productCode = cursor.getString(projection.indexOf(PRODUCT_CODE))
        item.quantity = cursor.getString(projection.indexOf(QTY))
        item.unitPrice = cursor.getString(projection.indexOf(UNIT_PRICE))
        item.vatPercentage = cursor.getString(projection.indexOf(VAT))
        item.vatAmount = cursor.getString(projection.indexOf(VAT_AMOUNT))
        item.totalAmount = cursor.getString(projection.indexOf(TOTAL_AMOUNT))
        item.teleCollectStatus = cursor.getInt(projection.indexOf(TELECOLLECT_STATUS))

        return item
    }

}