package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.reference.model.GreyListModel
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.database.SQLiteException

class GreyListDao : MainDataBaseClass()
{


    companion object {
         val GREYLIST_TABLE_NAME = "GREYLIST"
        private val GREYLIST_ID = "id"
        private val GREYLIST_PAN = "pan"
        private val GREYLIST_STATE = "etat"

        val GREYLIST_TABLE_CREATE = "CREATE TABLE " + GREYLIST_TABLE_NAME + " (" +
                GREYLIST_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +   // 0
                GREYLIST_PAN + " TEXT, "  +                         // 1
                GREYLIST_STATE + " TEXT); "                      // 1
    }

    fun GreyListDao(pContext: Context?) {
        super.getContext()
    }
    fun clearGreyListData(): Int {
        val newRowId = mDb!!.delete(GREYLIST_TABLE_NAME, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(GREYLIST_TABLE_NAME, null, null)
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.delete(GREYLIST_TABLE_NAME, null, null)
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.delete(GREYLIST_TABLE_NAME, null, null)
        }
        return newRowId
    }
    fun insertGreyListData(model: GreyListModel): Int {
        try {
            val values = ContentValues()
            values.put(GREYLIST_STATE, model.etat)
            values.put(GREYLIST_PAN, model.pan)

            val newRowId = mDb!!.insert(GREYLIST_TABLE_NAME, null, values).toInt()
            /*Thread{
                val backupRowId = backupDb!!.insert(GREYLIST_TABLE_NAME, null, values).toInt()
            }.run()*/
            if ( isSdCARD()) {
                mDb2!!.insert(GREYLIST_TABLE_NAME, null, values).toInt()
            }
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.insert(GREYLIST_TABLE_NAME, null, values).toInt()
            }
            return newRowId
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return -1
    }
    fun deletePAN(pan: String): Boolean {
      
        return mDb!!.delete(
            GREYLIST_TABLE_NAME,
            GREYLIST_PAN + "=?",
            arrayOf(pan)
        ) > 0
    }
    fun insertGreyListArrayData(mesItems: List<GreyListModel?>) {
        if (mesItems.isNotEmpty()) {
            for (list in mesItems) {
                this.insertGreyListData(list!!)
            }
        }
    }
    fun getGreyListByPan(mPan: String): GreyListModel? {
        val mListeGrise: GreyListModel?
        val id: Int // id dans ma base SQLite
        val pan: String // String
        val projection = arrayOf<String>(
            GREYLIST_ID,
            GREYLIST_PAN,
            GREYLIST_STATE
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder = "$GREYLIST_ID DESC"
        val selection = "$GREYLIST_PAN = ?"
        val selectionArgs = arrayOf(mPan)
        val cursor = mDb!!.query(
            GREYLIST_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        if (cursor.moveToFirst()) {
            id = cursor.getInt(0)
            pan = cursor.getString(1)
            mListeGrise = GreyListModel(id, pan)
        } else {
            mListeGrise = null
        }
        cursor.close()
        return mListeGrise
    }


}