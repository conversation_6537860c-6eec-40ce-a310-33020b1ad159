package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import app.rht.petrolcard.ui.common.model.LogFileModel
import app.rht.petrolcard.utils.constant.AppConstant

class LogFilesDao : MainDataBaseClass()
{

    companion object {

        const val LOG_TABLE_NAME = "filePOS"
        const val LOG_ID = "id"
        const val LOG_FILENAME = "fileName"
        const val LOG_TRANSACTIONID = "referenceTransactionId"
        const val LOG_FILEPATH= "filePath"
        const val LOG_FLAG = "telecollectFlag"

        const val POS_FILE_TABLE_CREATE = "CREATE TABLE " + LOG_TABLE_NAME + " (" +
                LOG_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +  //int
                LOG_FILENAME + " TEXT, " +  //String
                LOG_TRANSACTIONID + " TEXT, " +  //String
                LOG_FILEPATH + " TEXT, " +  //String
                LOG_FLAG + " INTEGER);" // int
    }

    fun insert(file: LogFileModel): Int {
        // Create a new map of values, where column names are the keys
        val values = ContentValues()
        values.put(LOG_FILENAME, file.fileName)
        values.put(LOG_TRANSACTIONID, file.referenceTransactionId)
        values.put(LOG_FILEPATH, file.filePath)
        values.put(LOG_FLAG, 0)
        val newRowId = mDb!!.insert(LOG_TABLE_NAME, null, values).toInt()
        /*Thread{
            val backupRowId = backupDb!!.insert(LOG_TABLE_NAME, null, values).toInt()
        }.run()*/
        if ( isSdCARD()) {
            mDb2!!.insert(LOG_TABLE_NAME, null, values).toInt()
        }
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            mDb3!!.insert(LOG_TABLE_NAME, null, values).toInt()
        }
        return newRowId
    }
    fun selectByName(fileName: String): List<LogFileModel>? {
        val mesItems: MutableList<LogFileModel> = ArrayList<LogFileModel>()
        var id: Int // id dans ma base SQLite
        var nomFichier: String
        var referenceTransaction: String
        var pathFile: String
        var fileFlagSend: Int

        val projection = arrayOf<String>(
            LOG_ID,
            LOG_FILENAME,
            LOG_TRANSACTIONID,
            LOG_FILEPATH,
            LOG_FLAG
        )
        val sortOrder: String = "$LOG_ID ASC"
        val selection: String =
            "$LOG_FILENAME = ?"
        val selectionArgs = arrayOf(fileName)
        val cursor = mDb!!.query(
          LOG_TABLE_NAME,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            null,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            id = cursor.getInt(0) //id dans ma base SQLite
            nomFichier = cursor.getString(1)
            referenceTransaction = cursor.getString(2)
            pathFile = cursor.getString(3)
            fileFlagSend = cursor.getInt(4)
            mesItems.add(LogFileModel(id, nomFichier, referenceTransaction, pathFile, fileFlagSend))
        }
        cursor.close()
        return mesItems
    }
}