package app.rht.petrolcard.database.baseclass

import android.content.ContentValues
import android.content.Context
import android.util.Log
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.ui.esdsign.model.EsdSignModel
import app.rht.petrolcard.utils.constant.AppConstant
import com.google.gson.Gson
import net.sqlcipher.DatabaseUtils
import java.lang.Exception
import java.util.*

class ESDSignatureDao : MainDataBaseClass() {
    private val TAG = ESDSignatureDao::class.simpleName

    companion object{
         val ESD_SIGN_TABLE = "fuel_esd_signs"
        private val TRX_ID = "id"
        private val SEQ_NO = "seq"
        private val PUMP_NO = "pump"
        private val PRODUCT_NO = "product"
        private val AMOUNT = "amount"
        private val VOLUME = "volume"
        private val UNIT_PRICE = "unitPrice"
        private val TRX_SIGN = "sign"
        private val TRX_TIME = "time"
        private val FLAG_TELECOLLECT = "flag"

        val ESD_SIGN_TABLE_CREATE =
            "CREATE TABLE " + ESD_SIGN_TABLE + " (" +
                    TRX_ID + " INTEGER PRIMARY KEY AUTOINCREMENT," +
                    SEQ_NO + " TEXT," +
                    PUMP_NO + " TEXT," +
                    PRODUCT_NO + " TEXT," +
                    AMOUNT + " TEXT," +
                    VOLUME + " TEXT," +
                    UNIT_PRICE + " TEXT," +
                    TRX_SIGN + " TEXT," +
                    TRX_TIME + " TEXT," +
                    FLAG_TELECOLLECT + " INTEGER);"

        val ESD_SIGN_TABLE_DROP = "DROP TABLE IF EXISTS $ESD_SIGN_TABLE;"


        private var id = 0
        private var flag:Int = 0
        private var seq: String? = null
        private var pump:String? = null
        private var product:String? = null
        private var amount:String? = null
        private var volume:String? = null
        private var unitPrice:String? = null
        private var sign:String? = null
        private var time:String? = null
    }

    private fun get1hrOldTimeStamp(): String {
        val cal = Calendar.getInstance()
        cal.add(Calendar.HOUR_OF_DAY, -1)
        return cal.time.time.toString() + ""
    }

    fun insert(item: EsdSignModel): Int {
        log(
            TAG,
            "INSERT ESD MODEL:: " + Gson().toJson(item)
        )
        val values = ContentValues()
        values.put(SEQ_NO, item.sequenceNumber)
        values.put(PUMP_NO, item.pumpNumber)
        values.put(PRODUCT_NO, item.productNo)
        values.put(AMOUNT, item.amount)
        values.put(VOLUME, item.volume)
        values.put(UNIT_PRICE, item.unitPrice)
        values.put(TRX_SIGN, item.signature)
        log(TAG, "SYSTEM TIME:: " + System.currentTimeMillis())
        if (BuildConfig.DEBUG) values.put(
            TRX_TIME,
            get1hrOldTimeStamp()
        ) else values.put(
            TRX_TIME,
            System.currentTimeMillis().toString() + ""
        ) //Commented for testing purpose
        values.put(FLAG_TELECOLLECT, 0)
        val newRowId =
            mDb!!.insert(ESD_SIGN_TABLE, null, values)
                .toInt()

        /*Thread{
            val backupRowId = backupDb!!.insert(ESD_SIGN_TABLE, null, values).toInt()
        }.run()*/
        /* if(super.getMcontexte() != null && Support.isSdCARD(super.getMcontexte())) {
            int newRowId2 = (int) mDb2.insert(ESD_SIGN_TABLE, null, values);
        }*/if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            val newRowId3 =
                mDb3!!.insert(
                    ESD_SIGN_TABLE,
                    null,
                    values
                )
                    .toInt()
        }
        return newRowId
    }
    fun delete(): Int {
        val newRowId =
            mDb!!.delete(ESD_SIGN_TABLE, null, null)
        /*Thread{
            val backupRowId = backupDb!!.delete(ESD_SIGN_TABLE, null, null)
        }.run()*/
        if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
            val newRowId3 = mDb3!!.delete(ESD_SIGN_TABLE, null, null)
        }
        return newRowId
    }
    fun count(): Long {
        return DatabaseUtils.queryNumEntries(mDb,ESD_SIGN_TABLE)
    }
    fun getAllTransactions(): List<EsdSignModel> {
        val transactions: MutableList<EsdSignModel> = ArrayList()
        val projection = arrayOf(
            TRX_ID,
            SEQ_NO,
            PUMP_NO,
            PRODUCT_NO,
            AMOUNT,
            VOLUME,
            UNIT_PRICE,
            TRX_SIGN,
            TRX_TIME,
            FLAG_TELECOLLECT
        )

        // How you want the results sorted in the resulting Cursor
        val sortOrder = "$TRX_ID DESC"
        val groupBy =
            "$SEQ_NO, $PUMP_NO"
        val cursor = mDb!!.query(
            ESD_SIGN_TABLE,  // The table to query
            projection,  // The columns to return
            null,  // The columns for the WHERE clause
            null,  // The values for the WHERE clause
            groupBy,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        while (cursor.moveToNext()) {
            id = cursor.getInt(0)
            seq = cursor.getString(1)
            pump = cursor.getString(2)
            product = cursor.getString(3)
            amount = cursor.getString(4)
            volume = cursor.getString(5)
            unitPrice = cursor.getString(6)
            sign = cursor.getString(7)
            time = cursor.getString(8)
            flag = cursor.getInt(9)
            transactions.add(
                EsdSignModel(
                    id = id,
                    sequenceNumber = seq,
                    pumpNumber = pump,
                    productNo = product,
                    amount = amount,
                    volume = volume,
                    unitPrice = unitPrice,
                    signature = sign,
                    time = time,
                    flag = flag
                )
            )
        }
        cursor.close()
        return transactions
    }
    fun getSignedTransaction(trxId: Int): EsdSignModel? {
        val item: EsdSignModel?
        val projection = arrayOf(
            TRX_ID,
            SEQ_NO,
            PUMP_NO,
            PRODUCT_NO,
            AMOUNT,
            VOLUME,
            UNIT_PRICE,
            TRX_SIGN,
            TRX_TIME,
            FLAG_TELECOLLECT
        )
        
        val selection = "$TRX_ID = ?"
        val selectionArgs = arrayOf(trxId.toString())
        val sortOrder = "$TRX_ID DESC"
        val groupBy = "$SEQ_NO, $PUMP_NO"
        val cursor = mDb!!.query(
            ESD_SIGN_TABLE,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            groupBy,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        if (cursor.moveToFirst()) {
            id = cursor.getInt(0)
            seq = cursor.getString(1)
            pump = cursor.getString(2)
            product = cursor.getString(3)
            amount = cursor.getString(4)
            volume = cursor.getString(5)
            unitPrice = cursor.getString(6)
            sign = cursor.getString(7)
            time = cursor.getString(8)
            flag = cursor.getInt(9)
            item = EsdSignModel(
                id = id,
                sequenceNumber = seq,
                pumpNumber = pump,
                productNo = product,
                amount = amount,
                volume = volume,
                unitPrice = unitPrice,
                signature = sign,
                time = time,
                flag = flag
            )
        } else {
            item = null
        }
        cursor.close()
        return item
    }

    fun getSignedTransaction(seqNumber:String,pumpNumber:String): EsdSignModel? {
        val item: EsdSignModel?
        val projection = arrayOf(
            TRX_ID,
            SEQ_NO,
            PUMP_NO,
            PRODUCT_NO,
            AMOUNT,
            VOLUME,
            UNIT_PRICE,
            TRX_SIGN,
            TRX_TIME,
            FLAG_TELECOLLECT
        )

        val selection = "$SEQ_NO = ? AND $PUMP_NO = ?"
        val selectionArgs = arrayOf(seqNumber, pumpNumber)
        val sortOrder = "$TRX_ID DESC"
        val groupBy = "$SEQ_NO, $PUMP_NO"
        val cursor = mDb!!.query(
            ESD_SIGN_TABLE,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            groupBy,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        if (cursor.moveToFirst()) {
            id = cursor.getInt(0)
            seq = cursor.getString(1)
            pump = cursor.getString(2)
            product = cursor.getString(3)
            amount = cursor.getString(4)
            volume = cursor.getString(5)
            unitPrice = cursor.getString(6)
            sign = cursor.getString(7)
            time = cursor.getString(8)
            flag = cursor.getInt(9)
            item = EsdSignModel(
                id = id,
                sequenceNumber = seq,
                pumpNumber = pump,
                productNo = product,
                amount = amount,
                volume = volume,
                unitPrice = unitPrice,
                signature = sign,
                time = time,
                flag = flag
            )
        } else {
            item = null
        }
        cursor.close()
        return item
    }
    fun updateESDSignature(esdSignModel: EsdSignModel) {
        val values = ContentValues()
        values.put( TRX_SIGN, esdSignModel.signature)
        try {
            mDb!!.update(
                 ESD_SIGN_TABLE,
                values,
                "$SEQ_NO = ? AND $PUMP_NO = ?",
                arrayOf(esdSignModel.sequenceNumber, esdSignModel.pumpNumber)
            )

            /*Thread{
                val backupRowId = backupDb!!.update(
                    ESD_SIGN_TABLE,
                    values,
                    "$SEQ_NO = ? AND $PUMP_NO = ?",
                    arrayOf(esdSignModel.sequenceNumber, esdSignModel.pumpNumber)
                )
            }.run()*/
            /*if(super.getMcontexte() != null && Support.isSdCARD(super.getMcontexte())) {
                mDb2.update(ESD_SIGN_TABLE, values, SEQ_NO + " = ? AND "+PUMP_NO +" = ?",  new String[] {esdSignModel.getSequenceNumber(),esdSignModel.getPumpNumber()});
            }*/
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.update(ESD_SIGN_TABLE, values, "$SEQ_NO = ? AND $PUMP_NO = ?", arrayOf(esdSignModel.sequenceNumber, esdSignModel.sequenceNumber))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun updateESDSignFlag(rowId: Int) {
        val values = ContentValues()
        values.put( TRX_ID, rowId)
        values.put( FLAG_TELECOLLECT, 1)
        try {
            mDb!!.update(
                ESD_SIGN_TABLE,
                values,
                "$TRX_ID = ?",
                arrayOf(rowId.toString())
            )
            /*Thread{
                val backupRowId = backupDb!!.update(
                    ESD_SIGN_TABLE,
                    values,
                    "$TRX_ID = ?",
                    arrayOf(rowId.toString())
                )
            }.run()*/
            /*if(super.getMcontexte() != null && Support.isSdCARD(super.getMcontexte())) {
                mDb2.update(ESD_SIGN_TABLE, values, TRX_ID + " = ?", new String[] {String.valueOf(rowId)});
            }*/
            if (AppConstant.USE_SECURE_INTERNAL_STORAGE) {
                mDb3!!.update(ESD_SIGN_TABLE, values, "$TRX_ID = ?", arrayOf(rowId.toString()))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun get30MinsBeforeSignedTransactions(): List<EsdSignModel>? {
        val currentTimeStamp = System.currentTimeMillis()
        val cal = Calendar.getInstance()
        cal.add(Calendar.MINUTE, -30)
        if (BuildConfig.DEBUG) cal.add(Calendar.MINUTE, -60)
        val before30MinutesTimeStamp = cal.time
        Log.i(TAG, "currentTimeStamp :: $currentTimeStamp")
        Log.i(TAG, "before30MinutesTimeStamp :: " + before30MinutesTimeStamp.time)
        val projection = arrayOf<String>(
             TRX_ID,
             SEQ_NO,
             PUMP_NO,
             PRODUCT_NO,
             AMOUNT,
             VOLUME,
             UNIT_PRICE,
             TRX_SIGN,
             TRX_TIME,
             FLAG_TELECOLLECT
        )
        val selection  = "$TRX_TIME <= ? AND $TRX_SIGN != ? AND $FLAG_TELECOLLECT = ?"
        val selectionArgs = arrayOf(before30MinutesTimeStamp.toString(), "", "0")
        val sortOrder  = "$TRX_ID DESC"
        val groupBy  = "$SEQ_NO, $PUMP_NO"
        val cursor = mDb!!.query(
             ESD_SIGN_TABLE,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            groupBy,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        val transactions: MutableList<EsdSignModel> = ArrayList()
        while (cursor.moveToNext()) {
            id = cursor.getInt(0)
            seq = cursor.getString(1)
            pump = cursor.getString(2)
            product = cursor.getString(3)
            amount = cursor.getString(4)
            volume = cursor.getString(5)
            unitPrice = cursor.getString(6)
            sign = cursor.getString(7)
            time = cursor.getString(8)
            flag = cursor.getInt(9)
            transactions.add(
                EsdSignModel(
                    id = id,
                    sequenceNumber = seq,
                    pumpNumber = pump,
                    productNo = product,
                    amount = amount,
                    volume = volume,
                    unitPrice = unitPrice,
                    signature = sign,
                    time = time,
                    flag = flag
                )
            )
        }
        cursor.close()
        return transactions
    }
    
    fun get30MinsBeforeUnSignedTransactions(): List<EsdSignModel>? {
        val currentTimeStamp = System.currentTimeMillis()
        val cal = Calendar.getInstance()
        cal.add(Calendar.MINUTE, -30)
        val before30MinutesTimeStamp = cal.time
        Log.i(TAG, "currentTimeStamp :: $currentTimeStamp")
        Log.i(TAG, "before30MinutesTimeStamp :: " + before30MinutesTimeStamp.time)
        val projection = arrayOf<String>(
             TRX_ID,
             SEQ_NO,
             PUMP_NO,
             PRODUCT_NO,
             AMOUNT,
             VOLUME,
             UNIT_PRICE,
             TRX_SIGN,
             TRX_TIME,
             FLAG_TELECOLLECT
        )
        val selection  = "$TRX_TIME <= ? AND $TRX_SIGN = ? AND $FLAG_TELECOLLECT = ?"
        val selectionArgs = arrayOf(before30MinutesTimeStamp.toString(), "", "0")
        val sortOrder  = "$TRX_ID DESC"
        val groupBy  = "$SEQ_NO, $PUMP_NO"
        val cursor = mDb!!.query(
             ESD_SIGN_TABLE,  // The table to query
            projection,  // The columns to return
            selection,  // The columns for the WHERE clause
            selectionArgs,  // The values for the WHERE clause
            groupBy,  // don't group the rows
            null,  // don't filter by row groups
            sortOrder // The sort order
        )
        val transactions: MutableList<EsdSignModel> = ArrayList()
        while (cursor.moveToNext()) {
            id = cursor.getInt(0)
            seq = cursor.getString(1)
            pump = cursor.getString(2)
            product = cursor.getString(3)
            amount = cursor.getString(4)
            volume = cursor.getString(5)
            unitPrice = cursor.getString(6)
            sign = cursor.getString(7)
            time = cursor.getString(8)
            flag = cursor.getInt(9)
            transactions.add(
                EsdSignModel(
                    id = id,
                    sequenceNumber = seq,
                    pumpNumber = pump,
                    productNo = product,
                    amount = amount,
                    volume = volume,
                    unitPrice = unitPrice,
                    signature = sign,
                    time = time,
                    flag = flag
                )
            )
        }
        cursor.close()
        return transactions
    }
}