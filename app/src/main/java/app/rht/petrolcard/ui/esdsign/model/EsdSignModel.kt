package app.rht.petrolcard.ui.esdsign.model

import androidx.annotation.Keep

@Keep
class EsdSignModel {
    var id: Int = 0
    var flag: Int = 0
    var sequenceNumber: String? = null
    var pumpNumber: String? = null
    var productNo: String? = null
    var amount: String? = null
    var volume: String? = null
    var unitPrice: String? = null
    var signature: String? = ""
    var time: String? = null

    constructor(sequenceNumber: String? = null,
                pumpNumber: String? = null,
                productNo: String? = null,
                amount: String? = null,
                volume: String? = null,
                unitPrice: String? = null,
                signature: String? = "",
                time: String? = null){
        this.sequenceNumber = sequenceNumber
        this.pumpNumber = pumpNumber
        this.productNo = productNo
        this.amount = amount
        this.volume = volume
        this.unitPrice = unitPrice
        this.signature = signature
        this.time = time
    }

    constructor(id: Int = 0, flag: Int = 0,
                sequenceNumber: String? = null,
                pumpNumber: String? = null,
                productNo: String? = null,
                amount: String? = null,
                volume: String? = null,
                unitPrice: String? = null,
                signature: String? = "",
                time: String? = null){
        this.id = id
        this.flag = flag
        this.sequenceNumber = sequenceNumber
        this.pumpNumber = pumpNumber
        this.productNo = productNo
        this.amount = amount
        this.volume = volume
        this.unitPrice = unitPrice
        this.signature = signature
        this.time = time
    }
}