package app.rht.petrolcard.ui.loyalty.activity

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.Window
import android.widget.Button
import android.widget.EditText
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityCardChangeBinding
import app.rht.petrolcard.ui.loyalty.model.LoyaltyCardDetailsModel
import app.rht.petrolcard.ui.loyalty.viewmodel.CardChangeViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import com.bumptech.glide.Glide
import com.kaopiz.kprogresshud.KProgressHUD
import kotlinx.android.synthetic.main.toolbar.view.*
import java.io.File
import java.lang.Exception

class CardChangeActivity  : BaseActivity<CardChangeViewModel>(CardChangeViewModel::class) {

    private lateinit var mBinding: ActivityCardChangeBinding
    private val TAG = LoyaltyBalanceActivity::class.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_card_change)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        initViews()
        authorizeNFC()
    }


    private lateinit var progressView : KProgressHUD
    private fun initViews() {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.change_card)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            finish()
        }

        progressView = KProgressHUD.create(this)
            .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE)
            .setLabel(getString(R.string.please_wait))
            .setDetailsLabel(getString(R.string.verifying_your_card_details))
            .setCancellable(false)
            .setAnimationSpeed(2)
            .setDimAmount(0.5f)


    }

    //region Card scanning
    private var cardNumber = ""
    private var cardHolder = ""
    private var nfcTag = ""
    private fun scanLoyaltyCard() {
        val intent = Intent(this, ICCLoyaltyActivity::class.java)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,true)
        loyaltyCardResultLauncher.launch(intent)
    }
    private var loyaltyCardResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                log(TAG,"data received ++++ ${data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)}")
                cardNumber = data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)!!
                cardHolder = data.getStringExtra(AppConstant.LOYALTY_CARD_HOLDER)!!
                nfcTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!

                mBinding.tvNewCardNumber.text = cardNumber.chunked(4).joinToString(separator = " ")
                mBinding.tvNewCardHolder.text = cardHolder.uppercase()
                mBinding.newLoyaltyCard.visibility = View.VISIBLE
            }
            else{
                gotoAbortMessageActivity(getString(R.string.try_again),getString(R.string.card_reading_failed))
            }
        }
    }
    //endregion

    //region Authorize NFC
    private var attendantTag = ""
    private fun authorizeNFC(){
        val intent = Intent(this, AuthoriseNfcActivity::class.java)
        tagAuthorizationReceiver.launch(intent)
    }
    private var tagAuthorizationReceiver = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                val authorized = data.getBooleanExtra(AppConstant.TAG_AUTHORISED, false)
                attendantTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                //log(TAG,"nfc data received ++++ $tag")
                log(TAG,"nfc authorized: $authorized")
                if(authorized){
                    log(TAG,"Attendant authorization success")
                    showDriverDialog()
                }
                else
                {
                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag),LoyaltyDashboardActivity::class.java)
                }
            }

        }
    }
    //endregion

    //private var oldCards = ArrayList<LoyaltyCardDetailsModel>()
    private lateinit var oldCardDetails : LoyaltyCardDetailsModel
    override fun setObserver() {
        mViewModel.loyaltyCardResponse.observe(this){
            val response = it
            progressView.dismiss()
            if(response!=null){
                if(response.reponse == "0") {
                    gotoAbortMessageActivity(getString(R.string.error),response.error!!,LoyaltyDashboardActivity::class.java)
                }
                else {
                    if(response.contenu!=null) {
                        //oldCards.add(response.contenu)
                        oldCardDetails = response.contenu
                        updateView(oldCardDetails)
                    } else {
                        gotoAbortMessageActivity(getString(R.string.error),getString(R.string.card_not_found),LoyaltyDashboardActivity::class.java)
                    }
                }
            } else {
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.connection_problem),LoyaltyDashboardActivity::class.java)
            }
        }
        mViewModel.replaceCardResponse.observe(this){
            val response = it
            progressView.dismiss()
            if(response!=null){
                if(response.reponse == "0") {
                    gotoAbortMessageActivity(getString(R.string.error),response.error!!,LoyaltyDashboardActivity::class.java)
                }
                else {
                    log(TAG,response.reponse!!)
                    gotoSuccessMessageActivity(getString(R.string.success),getString(R.string.card_replacement_successfully),LoyaltyDashboardActivity::class.java)
                }
            } else {
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.connection_problem),LoyaltyDashboardActivity::class.java)
            }
        }
    }

    private fun showDriverDialog(){
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_loyalty_driver)
        dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        val etDriverId = dialog.findViewById<EditText>(R.id.etDriverId)
        val dialogButton: Button = dialog.findViewById(R.id.btnVerify) as Button
        dialogButton.setOnClickListener {
            setBeep()
            if(etDriverId.length()<1) {
                etDriverId.error = getString(R.string.driver_id_required)
            } else {
                val driverId = etDriverId.text.toString()
                dialog.dismiss()
                progressView.show()
                mViewModel.getLoyaltyCardDetails(driverId)
            }
        }
        dialog.show()
    }

    private fun updateView(data:LoyaltyCardDetailsModel) {

        mBinding.tvCardNumber.text = data.pan!!.chunked(4).joinToString(separator = " ")
        mBinding.tvFirstName.text = data.nom
        mBinding.tvLastName.text = data.prenom
        mBinding.tvTel.text = data.tel
    }

    private val CAMERA_REQUEST_DRIVER_ID = 1888
    private val CAMERA_REQUEST_CARTE_GRISE = 2888

    private var driverIdFile : File? = null
    private var vehicleRegFile : File? = null
    override fun onImagePickSuccess(file: File, imagePickRequest: Int) {
        super.onImagePickSuccess(file, imagePickRequest)

        log(TAG, "IMAGE FILE RESULT: $file $imagePickRequest")

        try {
            when(imagePickRequest){
                CAMERA_REQUEST_DRIVER_ID -> {
                    driverIdFile = file
                    mBinding.ivDriverID.visibility = View.VISIBLE
                    val imageUri = Uri.fromFile(file)
                    Glide.with(this@CardChangeActivity)
                        .load(imageUri)
                        .thumbnail(Glide.with(this@CardChangeActivity).load(R.drawable.ic_file))
                        .into(mBinding.ivDriverID)
                }
                CAMERA_REQUEST_CARTE_GRISE -> {
                    vehicleRegFile = file
                    mBinding.ivVehicleReg.visibility = View.VISIBLE
                    val imageUri = Uri.fromFile(file)
                    Glide.with(this@CardChangeActivity)
                        .load(imageUri)
                        .thumbnail(Glide.with(this@CardChangeActivity).load(R.drawable.ic_file))
                        .into(mBinding.ivVehicleReg)
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun onClick(view: View) {
        when(view){
            mBinding.btnDriverId -> actionCameraToClickImage(CAMERA_REQUEST_DRIVER_ID)
            mBinding.btnVehicleReg -> actionCameraToClickImage(CAMERA_REQUEST_CARTE_GRISE)
            mBinding.btnAddNewCard -> {
                setBeep()
                scanLoyaltyCard()
            }
            mBinding.btnActivate -> {
                setBeep()
                when {
                    cardNumber.isEmpty() -> {
                        showToast(getString(R.string.please_add_new_loyalty_card))
                    }
                    driverIdFile==null -> {
                        showToast(getString(R.string.please_attach_driver_id_document))
                    }
                    vehicleRegFile == null -> {
                        showToast(getString(R.string.please_attach_vehicle_registration_document))
                    }
                    else -> {
                        progressView.setDetailsLabel(getString(R.string.loyalty_card_replacement_is_in_progress))
                        progressView.show()
                        mViewModel.replaceLoyaltyCard(driverIdFile!!,vehicleRegFile!!,oldCardDetails.pan!!,cardNumber)
                    }
                }
            }
        }
    }
}