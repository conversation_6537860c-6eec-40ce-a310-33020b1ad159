package app.rht.petrolcard.ui.reference.activity

import android.Manifest
import android.app.job.JobInfo
import android.app.job.JobScheduler
import android.content.*
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.os.*
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Button
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityReferenceBinding
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.viewmodel.ReferenceViewModel
import app.rht.petrolcard.ui.settings.appupdate.dialog.AppUpdate
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.helpers.appupdate.ApkVersion
import app.rht.petrolcard.utils.job.QuickPeriodicJobScheduler
import com.afollestad.materialdialogs.MaterialDialog
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import com.nabinbhandari.android.permissions.PermissionHandler
import com.nabinbhandari.android.permissions.Permissions
import com.testfairy.TestFairy
import com.vforl.utils.extensions.hideKeyboard
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.FileInputStream
import java.lang.ref.WeakReference
import java.util.*
import java.util.concurrent.TimeUnit


@Suppress("DEPRECATION")
class ReferenceActivity : BaseActivity<ReferenceViewModel>(ReferenceViewModel::class) {

    private lateinit var mBinding: ActivityReferenceBinding
    private val TAG = ReferenceActivity::class.simpleName
    var isButtonClicked = false
    private var scheduleService: ScheduledTeleCollectService? = null
    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_reference)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        prefs.navigationStatus = false
        startTelecollectService()
       // askForPermissions()
        when {
            LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this) -> {
                mBinding.rbEnglish.isChecked = false
                mBinding.rbarabic.isChecked = true
                mBinding.rbFrench.isChecked = false
            }
            LocaleManager.LANGUAGE_FRENCH == LocaleManager.getLanguage(this) -> {
                mBinding.rbEnglish.isChecked = false
                mBinding.rbarabic.isChecked = false
                mBinding.rbFrench.isChecked = true
            }
            else -> {
                mBinding.rbEnglish.isChecked = true
                mBinding.rbarabic.isChecked = false
                mBinding.rbFrench.isChecked = false
            }
        }
        mBinding.rbEnglish.setOnClickListener{
            LocaleManager.setNewLocale(this, LocaleManager.LANGUAGE_ENGLISH)
            val refresh = Intent(this, ReferenceActivity::class.java)
            startActivity(refresh)
            finish()
        }
        mBinding.rbFrench.setOnClickListener{
            LocaleManager.setNewLocale(this, LocaleManager.LANGUAGE_FRENCH)
            val refresh = Intent(this, ReferenceActivity::class.java)
            startActivity(refresh)
            finish()
        }
        mBinding.rbarabic.setOnClickListener{
            LocaleManager.setNewLocale(this, LocaleManager.LANGUAGE_ARABIC)
            val refresh = Intent(this, ReferenceActivity::class.java)
            startActivity(refresh)
            finish()
        }
        mBinding.buttonReference.setOnClickListener {
            setBeep()
           if(MainApp.sn == null)
                {
                    showDialog(getString(R.string.invalid_pos),getString(R.string.no_access_for_this_terminal))
                }
                else
               {
                   isButtonClicked =true
                   askForPermissions()
               }

        }

        getKeyContext()
    }
    fun askForPermissions() {
        val permissions = arrayOf(
            Manifest.permission.CAMERA,
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            Manifest.permission.READ_EXTERNAL_STORAGE,
            Manifest.permission.ACCESS_COARSE_LOCATION,
            Manifest.permission.ACCESS_FINE_LOCATION
        )
        val rationale = getString(R.string.please_provide_required_permissions_needed_by_app)
        val options = Permissions.Options()
            .setRationaleDialogTitle(getString(R.string.permission_required))
            .setSettingsDialogTitle(getString(R.string.warning))
        Permissions.check(this, permissions, rationale, options, object : PermissionHandler() {
            override fun onGranted() {
                if(isButtonClicked)
                {
                    startReferencing()
                }
                checkMemoryCardPermission()
            }

            override fun onDenied(context: Context, deniedPermissions: ArrayList<String>) {
                showToast(getString(R.string.permission_denied))
                askForPermissions()
                checkMemoryCardPermission()
            }
        })
    }
    
    private fun startReferencing() {
        hideKeyboard()
        showLoading(true)
        try {
            prefs.saveStringSharedPreferences(
                AppConstant.SERIAL_NUMBER_TPE,
                 MainApp.sn!!
            )
            mViewModel.getBaseURL(this)

        } catch (e: Exception) {
            e.printStackTrace()
            log(TAG, e.message + " " + e.cause)
            log(TAG, e.message + ExceptionUtils.getStackTrace(e))
            //  mViewModel.generateLogs(e.message!!,0)
        }
    }


    override fun onStop() {
        super.onStop()
        showLoading(false)
    }

    private fun showLoading(visible:Boolean){
        if(visible) {
            mBinding.loadingLayout.visibility = View.VISIBLE
            mBinding.animationView.playAnimation()
        } else {
            mBinding.loadingLayout.visibility = View.GONE
            mBinding.animationView.pauseAnimation()
        }
    }
    override fun onBackPressed() {

    }
    override fun setObserver() {
        val ctx = WeakReference(this).get()!!
        mViewModel.downloadLogoObserver.observe(ctx) {
            if (it != null && it.toString().isNotEmpty()) {
                val writtenToDisk: Boolean = FileUtil.writeResponseBodyToDisk(this, it, AppConstant.LOGO_NAME)
                log(TAG,"$$$$$ LOGO ::: $it")
                Log.w(TAG, "logo file download was a success: $writtenToDisk")
                    prefs.isReferenceLoad = false
                    printTicket()
            } else {
                val writtenToDisk: Boolean = FileUtil.saveDefaultLogo(this, AppConstant.LOGO_NAME)
                log(TAG,"$$$$$ LOGO ::: $it")
                Log.w(TAG, "default logo file download was a success: $writtenToDisk")
                prefs.isReferenceLoad = false
                showSnakeBarColor(getString(R.string.failed_to_download_logo))
                prefs.isReferenceLoad = false
                printTicket()
                log(TAG, getString(R.string.failed_to_download_logo))
            }
        }
        mViewModel.appUpdateObserver.observe(ctx) {
            if (it != null && it.reponse != "0") {
                val version = it.contenu!!.latestVersion
                val url =  it.contenu.latestVersionUrl
                val releaseNotes =  it.contenu.releaseNotes
                val isForceUpdate =  it.contenu.isForceUpdate
                val isUpdate = ApkVersion(version).compareTo(ApkVersion(BuildConfig.VERSION_CODE.toString()))
                if(isUpdate == 1)
                {
                  AppUpdate(ctx,isForceUpdate!!,false).checkAppUpdate(version,url,releaseNotes,isUpdate)
                }
            }
        }
        mViewModel.urlObserver.observe(ctx) {

            if (it.reponse == "1" && it.contenu != null) {
                prefs.baseUrl = it.contenu.base_url!!
                if(BuildConfig.DEBUG)
                {
//                    prefs.baseUrl = "http://rhtlab-tpe.fuelbs.com/"
                    prefs.baseUrl = "http://rhtstaging-tpe.fuelbs.com/"
//                    prefs.baseUrl = "https://rhttest-tpe.fuelbs.com/"
//                    prefs.baseUrl = "https://Fuelup-tpe.fuelbs.com/"
//                    prefs.baseUrl = "https://Fuelup-tpe.fuelbs.com/v2/"
//                    prefs.baseUrl = "https://sol-test-tpe.fuelbs.com/"
//                    prefs.baseUrl = "https://sol-test-tpe.fuelbs.com/"
//                    prefs.baseUrl = "https://solcaribbean-tpe.fuelbs.com/"
//                    prefs.baseUrl = "http://meru-tpe.fuelbusinesssuite.com/"
//                    prefs.baseUrl = "https://test-meruzm-tpe.fuelbs.com/"
//                    prefs.baseUrl = "http://lexo-tpe.fuelbs.com/"
//                    prefs.baseUrl = "http://lexo-tpe.fuelbs.com/"
//                    prefs.baseUrl = "https://dover-tpe.fuelbs.com/"
                }

                mViewModel.importInjectKey()

                //  mViewModel.getMasterFusionIP()
            }
            else
            {
                showLoading(false)
                val errorMessage = if(it.error!!.contains("SORRY WE DONT KNOW YOU")) {
                    getString(R.string.terminal_not_recognized)
                } else {
                    it.error!!
                }
                showDialog(getString(R.string.referencing_error), errorMessage)
                generateLogs(it.error!!,1)
            }
        }

        mViewModel.injectkeyObserver.observe(ctx) {
            log(TAG, "Inject Key response :: $it")
            importInjectKey(it!!)
            ScheduledTeleCollectService.isReferencingRequest = true
            scheduleService!!.ScheduledTeleCollectTask("").getTelecollectData()
        }
        mViewModel.getDiscountDetailsObserver.observe(ctx) {
            if(it.success == "1")
            {
                prefs.isReferenceLoad = false
                prefs.saveDiscountDetails(it.discount_details!!)
                mViewModel.downLoadLogo(prefs.getReferenceModel()!!.COMPANY.logo)
            }
            else
            {
                    showLoading(false)
                    showDialog(getString(R.string.failed_to_get_discount_details), it.error!!)

            }

        }
        mViewModel.noInternetResponse.observe(ctx, androidx.lifecycle.Observer {

          //  showNetworkDialog()
            Handler(Looper.getMainLooper()).postDelayed( {
                val refresh = Intent(this, ReferenceActivity::class.java)
                startActivity(refresh)
                finish()
            },2000)
        })
    }

    private fun showPaperDialog(){
        runOnUiThread{
            MyMaterialDialog(this,
                getString(R.string.error),
                printerStatusMessage,
                positiveBtnText = getString(R.string.print_again),
                negativeBtnText = getString(R.string.cancel),
                object : MyMaterialDialogListener{
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        printTicket()
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        startActivity(Intent(applicationContext, MenuActivity::class.java))
                        finish()
                    }
                })
        }
    }
    private lateinit var ticketThread : Thread
    private fun printTicket() {
        ticketThread = Thread{
            if(!ticketThread.isInterrupted){
                try {
                    if(isPrinterPaperAvailable()) {

                        var bitmap : Bitmap? = null
                        try {
                            bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                            bitmap = Support.getResizedBitmap(bitmap,400,400)
                            receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK).addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)

                        } catch (e:java.lang.Exception) { log(TAG,"LOGO Not printed on receipt  ${e.message}") }

                        receipt = ReceiptBuilder(1200)
                        receipt.setTextSize(75f).setAlign(Paint.Align.CENTER).addText(Support.getDateTicket(Date())).addText("")
                        receipt.setTextSize(75f).setAlign(Paint.Align.CENTER).addText(getString(R.string.label_referencement)).addText("")
                        setAlignment(Paint.Align.LEFT,65f).addText(prefs.getStationModel()!!.name).addText(prefs.getReferenceModel()!!.terminal!!.address).addText(prefs.getReferenceModel()!!.terminal!!.city)
                        receipt.addText("${getString(R.string.fiscal_label)} : ${prefs.getReferenceModel()!!.terminal!!.fiscalId}")
                        receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
                        receipt.addText("")
                        receipt.addText("")
                        val reciept = receipt.build()

                        Handler(Looper.getMainLooper()).post {
                            TicketPrinter(this).printReceipt(reciept)
                        }

                        try {
                            if(bitmap!=null){
                                bitmap.recycle()
                            }
                        } catch (e:Exception){ }

                        startActivity(Intent(this, MenuActivity::class.java))
                        finish()
                    }
                    else
                    {
                        showPaperDialog()
                    }
                }
                catch (e: Exception)
                {
                    e.printStackTrace()
                    log(TAG,e.message+" "+e.cause)
                    log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                    //mViewModel.generateLogs(e.message!!,0)
                }
            }
        }
        ticketThread.start()
    }

    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        generateLogs("Error - "+errorData.statusCode+" - "+errorData.message!!,1)
        gotoAbortMessageActivity("Error - "+errorData.statusCode,errorData.message,ReferenceActivity::class.java)
    }
    private fun dialogShowAppExpired() {
        val dialogExpired = AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogExpired!!.setContentView(R.layout.dialog_expired_app)
        val button = dialogExpired.findViewById<Button>(R.id.button)!!
        button.setOnClickListener {
            val language = LocaleManager.getLanguage(this)
            mViewModel.getAllReferenceData(language)
        }
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = dialogExpired.window
        lp.copyFrom(window!!.attributes)
        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogExpired.setCancelable(false)
        dialogExpired.setCanceledOnTouchOutside(false)
    }

    override fun onDestroy() {
        try {
            if(::ticketThread.isInitialized)
            {
                ticketThread.interrupt()
            }
            Log.i(TAG,"onDestroy")
            stopScheduleTelecollectService()
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

        super.onDestroy()
    }
    fun stopScheduleTelecollectService(){
        try {
            //Unbind from the service
            if (mBound) {
                unbindService(mConnection)
                mBound = false
            }
        } catch (e: Exception) {
            log(TAG, "stopScheduleTelecollectService: " + e.message)
        }
    }
    fun startTelecollectService()
    {
        val intent = Intent(this, ScheduledTeleCollectService::class.java)
        bindService(intent, mConnection, BIND_AUTO_CREATE)
    }
    var mBound = false
    private val mConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            Log.d("ServiceConnected", "Connect")
            val binder: ScheduledTeleCollectService.LocalBinder = service as ScheduledTeleCollectService.LocalBinder
            scheduleService = binder.service
            scheduleService!!.setTelecollectMessageListner(object : ScheduledTeleCollectService.TelecollectListner {
                override fun onTelecollectSuccess(title: String, message: String) {
                    log(TAG, "onTelecollectSuccess :: Receieved $message $title")
                    ScheduledTeleCollectService.isReferencingRequest = false
                    scheduleLogDeleteJob()
                    mViewModel.checkAppUpdateAvailable()
                    if (prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!=null && prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!!) {
                        mViewModel.getAllDiscountDetails()
                    } else {
                        mViewModel.downLoadLogo(prefs.getReferenceModel()!!.COMPANY.logo)
                    }
                }

                override fun onTelecollectFailed(title: String, message: String) {
                    log(TAG, "onTelecollectFailed :: Receieved $message $title")
                    ScheduledTeleCollectService.isReferencingRequest = false
                    showErrorOnProgress(title, message)
                    showLoading(false)
                }
            })
            mBound = true
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            log(TAG, "onServiceDisconnected")
            mBound = false
        }
    }
    fun scheduleLogDeleteJob() {
        try {
            val reference = prefs.getReferenceModel()
            if(reference!!.is_delete_log_on_reference != null && reference.is_delete_log_on_reference!!)
            {
             generateLogs("Reference",0)
            }
            var hours= reference.log_delete_schedule_hour
            if(hours == null)
            {
                hours = 76
            }
//        val REFRESH_INTERVAL  = hours * 60 * 1000
            val REFRESH_INTERVAL=  TimeUnit.HOURS.toMillis(hours)
            Log.i(TAG,"REFRESH_INTERVAL $REFRESH_INTERVAL")
            val jobScheduler = QuickPeriodicJobScheduler(MainApp.appContext)
            jobScheduler.start(1, REFRESH_INTERVAL) // Run job with jobId=1 every 60 seconds
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

    }
}
