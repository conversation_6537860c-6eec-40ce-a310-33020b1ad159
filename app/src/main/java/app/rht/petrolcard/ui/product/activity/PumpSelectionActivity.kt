package app.rht.petrolcard.ui.product.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityPumpListBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.DebitCardLimitsActivity
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.BankPaymentProgressActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.reference.model.PumpsModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.startup.activity.SplashScreenActivity
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.ui.transactionlist.model.*
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.ui.transactionlist.model.DSPConfiguration
import app.rht.petrolcard.ui.transactionlist.model.ProductDSP
import app.rht.petrolcard.utils.Connectivity
import app.rht.petrolcard.utils.MyMaterialDialog
import app.rht.petrolcard.utils.MyMaterialDialogListener
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.VISA_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.VOID_REQUEST
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.afollestad.materialdialogs.MaterialDialog
import com.altafrazzaque.ifsfcomm.*
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import java.lang.ref.WeakReference
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.collections.ArrayList

@Suppress("DEPRECATION")
class PumpSelectionActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<PumpsModel> {
    private var TAG = PumpSelectionActivity::class.java.simpleName
    private lateinit var mBinding: ActivityPumpListBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    var newPumpList:ArrayList<PumpsModel> = ArrayList<PumpsModel>()
    var mesPompes: ArrayList<PumpsModel>? = ArrayList<PumpsModel>()
    private lateinit var adapter : RecyclerViewArrayAdapter<PumpsModel>
    var pumpNumber = 0
    var isButtonClicked=false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_pump_list)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        log(TAG,"isPaymentDone : ${prefs.isPaymentDone} isPaymentMethodClicked : ${prefs.isPaymentMethodClicked} isTransactionCreated : ${prefs.isTransactionCreated}")
        if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.ATTENDANT_MODE || (prefs.isPaymentDone && prefs.isPaymentMethodClicked && prefs.isTransactionCreated))
        {
            setupToolbar()
            initData()
            ClickListners()
        }
        else
        {
            log(TAG,"PAYMENT NOT STARTED")
            showSnakeBarColor("Payment Not Started",true)
            clearTrxHistoryInSp()
            val mIntent: Intent = if (referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                Intent(this, UnattendantModePayActivity::class.java)
            } else {
                Intent(this, MenuActivity::class.java)
            }
            startActivity(mIntent)
            finish()
        }



    }

    override fun onStart() {
        super.onStart()
    }

    private fun ClickListners() {
        mBinding.btnRetryFcc.setOnClickListener {
            if (cancelScreenTimer != null) {
                cancelScreenTimer!!.cancel()
            }
            retryFccConnection()
            isButtonClicked = true
        }
        mBinding.btnVoid.setOnClickListener {
            isButtonClicked = true
            clearTrxHistoryInSp()
            if (intentExtrasModel!!.typePay == VISA_VALUE || intentExtrasModel!!.typePay == AppConstant.CARD_VALUE) {
                cancelAlert(this)
            } else {
                gotoAbortMessageActivity(
                    getString(R.string.transaction_cancelled),
                    getString(R.string.customer_cancel_transaction_)
                )
            }

        }

        
    }

    private fun setupToolbar() {
        mBinding.toolbarPumpList.toolbar.tvTitle.text = getString(R.string.pumps)
        mBinding.toolbarPumpList.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPumpList.toolbar.isEnabled = false
            if(intentExtrasModel!!.typePay == VISA_VALUE || intentExtrasModel!!.typePay == AppConstant.CARD_VALUE)
            {

                cancelAlert(this)
            }
            else
            {
                gotoAbortMessageActivity(
                    getString(R.string.transaction_cancelled),
                    getString(R.string.customer_cancel_transaction_)
                )
            }
        }
    }
    private fun clearTrxHistoryInSp() {
        resetPaymentValues(TAG)
        log(TAG,"TRX HISTORY CLEARED")
    }
    fun abortWithRefund()
    {
        stopCancelScreenTimer()
        clearTrxHistoryInSp()
        if(intentExtrasModel!!.typePay == VISA_VALUE ){
            intentExtrasModel!!.bankRequestType = VOID_REQUEST
            gotoBankActivity()
        }
        else if(intentExtrasModel!!.typePay == AppConstant.CARD_VALUE){
            intentExtrasModel!!.fleetCardRequestType = VOID_REQUEST
            gotoFleetCardActivity()
        }
    }
    private fun cancelAlert(context: Context) {
        try {
            MyMaterialDialog(
                WeakReference(context).get()!!,
                getString(R.string.confirm),
                getString(R.string.are_you_sure_you_want_to_cancel_transaction),
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()

                        abortWithRefund()

                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                    }
                }
            )
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    private fun gotoBankActivity() {
        intentExtrasModel!!.bankRequestType = VOID_REQUEST
        val intent = Intent(this, BankPaymentProgressActivity::class.java)
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
        finish()
    }

    fun gotoFleetCardActivity() {
        if(referenceModel!!.IMPLEMENT_DISCOUNT!! && intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.REBATE_DISCOUNT && intentExtrasModel!!.mTransaction!!.isDiscountTransaction == 1)
        {
            gotoAbortMessageActivity(getString(R.string.transaction),getString(R.string.transaction_cancelled),intentExtrasModel!!.mTransaction)
        }
        else {
            val intent = Intent(this, DebitCardLimitsActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
            finish()
        }
    }
    var referenceModel: ReferenceModel? = null
    private fun initData()
    {
        referenceModel = prefs.getReferenceModel()

        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }

        mesPompes = referenceModel!!.pompes!!
        if(!mesPompes.isNullOrEmpty()) {
            if (referenceModel!!.RFID_TERMINALS != null) {
                newPumpList.clear()
                val pumpsModels: List<RFIDPumpsModel> = referenceModel!!.RFID_TERMINALS!!.pumps
                if (pumpsModels != null) {
                    for (pumpsModel in pumpsModels) {
                        for (pump in mesPompes!!) {
                            if (pump.FP_PUMP.toInt() == pumpsModel.pump_number) {
                                newPumpList.add(pump)
                            }
                        }
                    }
                }
            }
            val gson = Gson()
            val pumpListJson = gson.toJson(newPumpList)
            log(TAG, "PUMP LIST: $pumpListJson")
            setupRecyclerview()
            if (newPumpList.size == 1) {
                saveTrxDetails(newPumpList[0], intentExtrasModel)
                gotoProductSelectionPage(newPumpList[0])
            } else {
                startIfsfReceiver()
            }
        }
        else
        {
            if(intentExtrasModel!!.typePay == VISA_VALUE || intentExtrasModel!!.typePay == AppConstant.CARD_VALUE)
            {
                setupRecyclerview()

            }
            else {
                gotoAbortMessageActivity(getString(R.string.pumps_not_available), getString(R.string.please_assign_fcc_on_the_station))
            }
        }

    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        adapter = RecyclerViewArrayAdapter(newPumpList,this)
        mBinding.mListView.layoutManager = GridLayoutManager(this, 2)
        mBinding.mListView.adapter = adapter
        if(referenceModel!!.RFID_TERMINALS != null && newPumpList.isNotEmpty())
        {
            mBinding.emptyList.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
            mBinding.prompt.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyList.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
            mBinding.prompt.visibility = View.GONE
        }
    }
    private fun startIfsfReceiver() {
        // commented because getting issue on product table if pump is disconnected after debiting amount from fleet card or bank card   //https://app.clickup.com/t/2zbxdmz
        /*val filter = IntentFilter()
        filter.addAction(ACTION_IFSF_READ_DATA)
        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
        registerReceiver(ifsfRecever, filter)
        FusionService.getProductTable()*/
    }

    private fun stopIfsfReceiver() {
        /*try {
            unregisterReceiver(ifsfRecever)
        } catch (e: Exception) {
            e.printStackTrace()
        }*/
    }
    override fun onBackPressed() {

    }


    var serverConnectCount = 0

    var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            //System.out.println("Action: "+action);
            if (action == ACTION_IFSF_READ_DATA) {
                val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
                intent.getByteArrayExtra(IFSF_BYTE_MESSAGE)
                 StringUtils.substringBetween(msg, "<ErrorCode>", "</ErrorCode>")
                log(TAG, "Ifsf Message: $msg")
                performNextStepProductTask(msg!!)
            }
            else if (action == ACTION_IFSF_CONNECTION_STATE) {
                val msg = intent.getStringExtra(IFSF_CONNECTION_STATE)
                log(TAG, "Ifsf Connection State: $msg")
                if (msg!!.contains("Connected")) {
                    Handler().postDelayed({ showLoading(false) }, 2000)
                }
                if (msg.toLowerCase(Locale.ROOT).contains("timeout") || msg.toLowerCase(Locale.ROOT)
                        .contains("disconnected")) {
                    showFccConnectionLayout(true)
                }
            } else {
                log(TAG, "Ifsf Service: $action")
            }
        }
    }
    private var productColors: ArrayList<ProductDSP> = ArrayList()
    private fun performNextStepProductTask(message: String) {
        var message = message
        try{
            val errorCode: String = StringUtils.substringBetween(message, "<ErrorCode>", "</ErrorCode>")
            if (errorCode.contains("ERRCD_OK")) {
                if (message.contains("ServiceResponse")) {
                    message = formatMessage(message)
                    val overallResult: String =
                        StringUtils.substringBetween(message, "OverallResult=\"", "\"")
                    val requestType: String =
                        StringUtils.substringBetween(message, "RequestType=\"", "\"")
                    val gson = Gson()
                    if (overallResult == "Success" && errorCode == "ERRCD_OK") {
                        val jsonString: String = Support.xmlToJsonString(message)!!
                        when (requestType) {
                            "GetDSPConfiguration" -> {
                                try {
                                    log(TAG,"DSP CONFIG::: $jsonString")
                                    val dspConfiguration = gson.fromJson(jsonString, DSPConfiguration::class.java)
                                    val devices = dspConfiguration.serviceResponse.fDCdata.deviceClasses
                                    for(device in devices)
                                    {
                                        val products = device.products
                                        for(product in products)
                                        {
                                            if(!productColors.contains(product))
                                            {
                                                productColors.add(product)
                                                log(TAG,"##### Product color added in list (Product: ${product.productName} ---- #${product.productColour})")
                                            }
                                        }
                                        prefs.saveProductColors(productColors)
                                    }
                                } catch (e: Exception) {
                                    log(TAG, "Error occurred while fetching products from fusion: " + e.message)
                                    e.printStackTrace()
                                }
                            }
                        }
                    } else {
                        showToast(overallResult)
                        showFccConnectionLayout(true)
                        //  finish()
                    }
                }
            }
        } catch (e: NullPointerException){
            log(TAG,"EXCEPTION ${e.message} -- ${e.cause}")
            showToast("Invalid message received, ${e.message}")
        }
    }

    override fun setObserver() {

    }
    private fun showLoading(isLoading: Boolean) {
        if (isLoading) {
            mBinding.loading.visibility = View.VISIBLE
        } else {
            mBinding.loading.visibility = View.GONE
        }
    }
    override fun onItemClick(view: View, model: PumpsModel) {
        setBeep()
        MultiClickPreventer.preventMultiClick(view)
        val thisItem: PumpsModel = model
        saveTrxDetails(model,intentExtrasModel)
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Pump - ${model.FP_PUMP}"))
        stopCancelScreenTimer()
        gotoProductSelectionPage(thisItem)
    }

    private fun gotoProductSelectionPage(model: PumpsModel){
        pumpNumber = model.FP_PUMP.toInt()
        val pumpName: String = model.Name
        log(TAG, "Selected Pump: $pumpNumber")
        val intent = Intent(this, ProductSelectionActivity::class.java)
        intentExtrasModel!!.listNozzles=model.Pistolers
        intentExtrasModel!!.mPumpNumber=pumpNumber.toString()
        intentExtrasModel!!.pumpName=pumpName
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
    }

    private fun retryFccConnection() {
        val connectivity =  Connectivity.isNetworkAvailable(this)
        if(connectivity){
            if(!FusionService.isRunning(this))
                FusionService.start(this)

            val fccConnected =  FusionService.fccConnected()
            if(!fccConnected) {
                FusionService.connectFcc(this)
            }
            showFccConnectionLayout(false)
        } else {
            showToast(getString(R.string.please_check_your_internet_connection_try_again))
        }
    }

    private fun showFccConnectionLayout(isShow: Boolean) {
        if(isShow)
        {
            startCancelScreenTimer()
            mBinding.fccConnectionLayout.visibility = View.VISIBLE
            mBinding.productLayout.visibility = View.GONE
        }
        else
        {
            mBinding.fccConnectionLayout.visibility = View.GONE
            mBinding.productLayout.visibility = View.VISIBLE

        }
        Support.setNavigationStatus(this, false)
    }
    private var cancelScreenTimer: CountDownTimer? = null
    private fun startCancelScreenTimer() {
        try {
            stopCancelScreenTimer()

            var voidTime = referenceModel!!.void_transaction_seconds

            if(voidTime==null)
                voidTime = 30L

            val startTime = TimeUnit.SECONDS.toMillis(voidTime)

            log(TAG,"SECONDsSS ::: $startTime")

            if(referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE){
                cancelScreenTimer = object : CountDownTimer(startTime, 1000) {
                    override fun onTick(millisUntilFinished: Long) {
                        minutes = millisUntilFinished / 1000 / 60
                        seconds = (millisUntilFinished / 1000 % 60)
                        log(TAG,"Pump selection time Remaining: $minutes:$seconds")
                    }
                    override fun onFinish() {
                        //if(mBinding.fccConnectionLayout.visibility == View.VISIBLE)
                        //{
                        if(intentExtrasModel!!.typePay == VISA_VALUE ){
                            intentExtrasModel!!.bankRequestType = VOID_REQUEST
                            gotoBankActivity()
                        }
                        else if(intentExtrasModel!!.typePay == AppConstant.CARD_VALUE){
                            intentExtrasModel!!.fleetCardRequestType = VOID_REQUEST
                            gotoFleetCardActivity()
                        }
                        //}
                        cancelScreenTimer!!.cancel()
                    }
                }.start()
            }
        }
        catch (e:Exception)
        {
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
           // mViewModel.generateLogs(e.message!!,0)
            e.printStackTrace()
        }
    }
    private fun stopCancelScreenTimer(){
        if(cancelScreenTimer!=null) {
            cancelScreenTimer!!.cancel()
            cancelScreenTimer = null
        }
    }
    override fun onTouchEvent(event: MotionEvent): Boolean {
        //val unAttendantMode = MainApp.getPrefs().getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
        val unAttendantMode = referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
        if(unAttendantMode){
            startCancelScreenTimer()
        }
        return super.onTouchEvent(event)
    }

    override fun onPause() {
        super.onPause()
        stopIfsfReceiver()
        stopCancelScreenTimer()
    }
    override fun onStop() {
        super.onStop()
        stopCancelScreenTimer() //starting local screen timer
    }

    override fun onResume() {
        super.onResume()
        startIfsfReceiver()
        // //stopping common timer here
        startCancelScreenTimer() //starting local screen timer
    }

    override fun onDestroy() {
        super.onDestroy()
        stopCancelScreenTimer()
    }

    private fun saveTrxDetails(pump:PumpsModel,intentExtraModel:IntentExtrasModel?){
        prefs.savePumpModel(pump)
        prefs.saveIntentModel(intentExtraModel)
    }

}
