package app.rht.petrolcard.ui.menu.model

import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import androidx.annotation.Keep
@Keep
data class MenuCategoryModel(
    val id: Int,
    val name: String,
    val imageUrl: String,
    val cardColor: String,
    val menuProductModels: List<MenuProductModel>? = null
) : BaseModel()
{
    fun getCardBgColor(): Int {
        val color = cardColor
        return Color.parseColor(color)
    }
}