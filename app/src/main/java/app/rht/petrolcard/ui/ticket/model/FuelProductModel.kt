package app.rht.petrolcard.ui.ticket.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
@Keep
data class FuelProductModel(
    var nozzle:String?,
    var pumpId:String?,
    var seqNumber: String? = ""
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(nozzle)
        parcel.writeString(pumpId)
        parcel.writeString(seqNumber)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<FuelProductModel> {
        override fun createFromParcel(parcel: Parcel): FuelProductModel {
            return FuelProductModel(parcel)
        }

        override fun newArray(size: Int): Array<FuelProductModel?> {
            return arrayOfNulls(size)
        }
    }
}