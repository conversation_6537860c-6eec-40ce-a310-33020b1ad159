package app.rht.petrolcard.ui.product.model
import androidx.annotation.Keep

import com.google.gson.annotations.SerializedName


class PumpStateMessage(
     val NO_PUMP:String="",
     val IDLE:String="",
     val CONFIG_ERROR:String="",
     val REQUESTING:String="",
     val RESERVED:String="",
     val RESERVED_REQ:String="",
     val RELEASED:String="",
     val AUTO_RELEASE:String="",
     val FILLING:String="",
     val BLOCKED:String="",
     val STOPPED:String="",
     val OFFLINE:String="",
     val MINOR_ERROR:String="",
     val MAJOR_ERROR:String="",
     val SAFETY_IDLE:String="",
     val SAFETY_REQ:String=""
)


@Keep
class FuelPosPumpResponse(
    @SerializedName("AMOUNT_TWO_HIGH")
    var AMOUNT_TWO_HIGH: String = "",
    @SerializedName("AMOUNT_TWO_LOW")
    var AMOUNT_TWO_LOW: String = "",
    @SerializedName("CANCELED_TRX")
    var CANCELED_TRX: String = "",
    @SerializedName("CURRENCY_MISMATCH")
    var CURRENCY_MISMATCH: String = "",
    @SerializedName("INVALID_CURRENCY_CODE")
    var INVALID_CURRENCY_CODE: String = "",
    @SerializedName("INVALID_ITEM_CODE")
    var INVALID_ITEM_CODE: String = "",
    @SerializedName("INVALID_MAX_AMOUNT")
    var INVALID_MAX_AMOUNT: String = "",
    @SerializedName("INVALID_MESSAGE")
    var INVALID_MESSAGE: String = "",
    @SerializedName("ITEM_INFO_NOT_AVAILABLE")
    var ITEM_INFO_NOT_AVAILABLE: String = "",
    @SerializedName("NOTHING_STOP")
    var NOTHING_STOP: String = "",
    @SerializedName("NOZZLE_LIFT_TIMEOUT")
    var NOZZLE_LIFT_TIMEOUT: String = "",
    @SerializedName("OK")
    var OK: String = "",
    @SerializedName("ONLINE_AUTH_FAILED")
    var ONLINE_AUTH_FAILED: String = "",
    @SerializedName("ONLINE_AUTH_REFUSED")
    var ONLINE_AUTH_REFUSED: String = "",
    @SerializedName("POWER_DOWN")
    var POWER_DOWN: String = "",
    @SerializedName("PRODUCT_NOT_ALLOWED_DISPENSING")
    var PRODUCT_NOT_ALLOWED_DISPENSING: String = "",
    @SerializedName("PUMP_BUSY")
    var PUMP_BUSY: String = "",
    @SerializedName("PUMP_INACTIVE")
    var PUMP_INACTIVE: String = "",
    @SerializedName("PUMP_NOT_AVAILABLE")
    var PUMP_NOT_AVAILABLE: String = "",
    @SerializedName("PUMP_NOT_CONFIGURED")
    var PUMP_NOT_CONFIGURED: String = "",
    @SerializedName("PUMP_NOT_EPR")
    var PUMP_NOT_EPR: String = "",
    @SerializedName("PUMP_RESERVE_TIMEOUT")
    var PUMP_RESERVE_TIMEOUT: String = "",
    @SerializedName("PUMP_STOPPED")
    var PUMP_STOPPED: String = "",
    @SerializedName("SYSTEM_FAILURE")
    var SYSTEM_FAILURE: String = ""
)