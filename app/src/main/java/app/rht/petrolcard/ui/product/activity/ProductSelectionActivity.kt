package app.rht.petrolcard.ui.product.activity

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.os.*
import android.text.Html
import android.text.InputFilter
import android.text.InputType
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.baseClasses.model.FuelPump
import app.rht.petrolcard.database.baseclass.FuelTrxCountDao
import app.rht.petrolcard.database.baseclass.PriceDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityProductListBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.DebitCardLimitsActivity
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.BankPaymentProgressActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.product.model.*
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.ui.ticket.model.FuelProductModel
import app.rht.petrolcard.ui.transactionlist.activity.OfflineTransactionListActivity
import app.rht.petrolcard.ui.transactionlist.model.DeviceClassGAFT
import app.rht.petrolcard.ui.transactionlist.model.ServiceResponseGAFT
import app.rht.petrolcard.ui.transactionlist.model.ServiceResponseGAFT2
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.CARD_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.CASH_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.CUSTOMER
import app.rht.petrolcard.utils.constant.AppConstant.DISCOUNT_ALL_DAY
import app.rht.petrolcard.utils.constant.AppConstant.DISCOUNT_SPECIFIC_DAY
import app.rht.petrolcard.utils.constant.AppConstant.FIXED
import app.rht.petrolcard.utils.constant.AppConstant.INTENT_EXTRAS_MODEL
import app.rht.petrolcard.utils.constant.AppConstant.PERCENTAGE
import app.rht.petrolcard.utils.constant.AppConstant.REFUND_REQUEST
import app.rht.petrolcard.utils.constant.AppConstant.VISA_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.VOID_REQUEST
import app.rht.petrolcard.utils.constant.CancelPrintReceipt
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.TaxType
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.fuelpos.*
import app.rht.petrolcard.utils.fuelpos.models.PumpReleaseRequest
import app.rht.petrolcard.utils.fuelpos.models.TransactionData
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.tax.TaxUtils
import app.rht.petrolcard.utils.uncaughtExceptionHandler.EmailConfig
import app.rht.petrolcard.utils.uncaughtExceptionHandler.EmailSender
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import com.altafrazzaque.ifsfcomm.*
import com.altafrazzaque.ifsfcomm.ifsf.models.ClearFuelSaleTrxPrams
import com.altafrazzaque.ifsfcomm.ifsf.models.FuelSaleTrxPrams
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.android.synthetic.main.toolbar.view.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.File
import java.io.FileInputStream
import java.io.PrintWriter
import java.io.StringWriter
import java.lang.ref.WeakReference
import java.math.BigDecimal
import java.sql.SQLException
import java.text.DateFormat
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import kotlin.concurrent.schedule


@Suppress("DEPRECATION")
class ProductSelectionActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<NozzelsModel> {
    private var TAG = ProductSelectionActivity::class.java.simpleName
    private lateinit var mBinding: ActivityProductListBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    private lateinit var adapter : RecyclerViewArrayAdapter<NozzelsModel>
    var recievedData = ""
    var executionMode = ""
    var fccProductId = ""
    var trxCountPOSTrx: FuelTrxCountDao? = null
    var mProduitToCheckout: ProductModel? = null
    var mTerminal: TerminalModel? = null
    var logmessage: StringBuilder? = null
    lateinit var fuelTrxDetails: Array<String>
    var isButtonClicked=false
    var fusionRequestStatus = ""
    lateinit var fuelpos: FuelPOSModel
    var fusionExist = false
    var pumpNumber = ""
    var decimal = 2
    var listNozzles: ArrayList<NozzelsModel> = ArrayList()
    var fuelTrxCountDao: FuelTrxCountDao? = null
    var selectedNozzleModel: NozzelsModel? = null
    var fuelTrxCount: Int = 0
    var isQtySelected = false
    var pendingTrx = false

    var isUnattendantMode = false
    var isFuelPosLogOn = false
    var referenceModel : ReferenceModel? = null
    var preferenceModel : PreferenceModel? = null
     var transactionDataTimeout = 60
    var isReleaseTokenGenerated =false
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_product_list)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        log(TAG,"isPaymentDone : ${prefs.isPaymentDone} isPaymentMethodClicked : ${prefs.isPaymentMethodClicked} isTransactionCreated : ${prefs.isTransactionCreated}")
        referenceModel = prefs.getReferenceModel()
        preferenceModel = prefs.getPreferenceModel()
        transactionDataTimeout = if(referenceModel!!.FUELPOS.transactionDataTimeout != null) referenceModel!!.FUELPOS.transactionDataTimeout!!.toInt() else transactionDataTimeout
        val terminalType = prefs.getReferenceModel()!!.TERMINAL_TYPE
        if(terminalType ==  AppConstant.ATTENDANT_MODE) {
            mBinding.authTimerLayout.visibility = View.GONE
        }

        if(terminalType == AppConstant.ATTENDANT_MODE || prefs.isPaymentDone && prefs.isPaymentMethodClicked && prefs.isTransactionCreated)
        {
            intentExtrasModel = intent.getParcelableExtra(INTENT_EXTRAS_MODEL) as IntentExtrasModel?
            fuelVat= referenceModel!!.fuelVat!!
            mTerminal = referenceModel!!.terminal
            fuelpos =  referenceModel!!.FUELPOS
            fusionExist = prefs.getFusionModel()!!.EXIST
            decimal = fuelpos.totalAmountDecimal ?: fuelpos.decimal
            isUnattendantMode = referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE

            setupToolbar()
            initData()

        }
        else
        {
            log(TAG,"PAYMENT NOT STARTED")
            log(TAG,"isPaymentDone : ${prefs.isPaymentDone} isPaymentMethodClicked : ${prefs.isPaymentMethodClicked} isTransactionCreated : ${prefs.isTransactionCreated}")
            showSnakeBarColor("Payment Not Started",true)
            clearTrxHistoryInSp()
            val mIntent: Intent = if (referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                Intent(this, UnattendantModePayActivity::class.java)
            } else {
                Intent(this, MenuActivity::class.java)
            }
            startActivity(mIntent)
            finish()
        }

    }
    var fusionPriceConfiguration = ArrayList<FuelPump>()
    var tokenTRX = ""
    private fun gotoPumpSelectionActivity() {
        stopAllReciversAndTimers()
        val i = Intent(this, PumpSelectionActivity::class.java)
        i.putExtra(INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
        finish()
    }
    private fun cancelAlert(context: Context) {
        val ctx = WeakReference(context).get()!!
        try {
            MyMaterialDialog(
                ctx,
                getString(R.string.confirm),
                getString(R.string.are_you_sure_you_want_to_cancel_transaction),
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        stopConnectivityChecker()

                        isButtonClicked = true

                        if(intentExtrasModel!!.typePay == VISA_VALUE){
                            intentExtrasModel!!.bankRequestType = VOID_REQUEST
                            gotoBankActivity()
                        }
                        else
                        {
                            intentExtrasModel!!.fleetCardRequestType = VOID_REQUEST
                            //todo goto DebitCardLimitsActivity to refund refund full amount
                            log(TAG,"GOTO FLEET CARD ACTIVITY 1")
                            gotoFleetCardActivity()
                        }
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                    }
                }
            )
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    private fun retryFccConnection() {
        stopConnectivityChecker()
        val connectivity =  Connectivity.isNetworkAvailable(this)
        if(connectivity){
            if(fusionExist){
                if(!FusionService.isRunning(this@ProductSelectionActivity))
                    FusionService.start(this@ProductSelectionActivity)

                val fccConnected =  FusionService.fccConnected()
                if(!fccConnected) {
                    FusionService.connectFcc(this@ProductSelectionActivity)
                }
            }
            else if(fuelpos.isExist){
                if(!FuelPosService.isRunning(this@ProductSelectionActivity))
                    FuelPosService.start(this@ProductSelectionActivity)

                FuelPosService.restartFuelPosEpr()
                /*val fccConnected =  FuelPosService.isConnected()
                if(!fccConnected) {
                    FuelPosService.restartFuelPosEpr()
                }
                else {
                    showFccConnectionLayout(false)
                }*/
            }

            if(!isNozzleClicked) {
                startConnectivityChecker()
            }
        } else {
            showToast(getString(R.string.please_check_your_network_connection_and_try_again))
        }
    }


    var isBackButtonPressed = false
    private fun setupToolbar() {
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)  // keep screen on this page
        mBinding.toolbarPumpList.toolbar.tvTitle.text = getString(R.string.products)
        mBinding.toolbarPumpList.toolbar.setNavigationOnClickListener {

            if(!isBackButtonPressed){
                isBackButtonPressed = true
                mBinding.toolbarPumpList.toolbar.isEnabled = false
                log(TAG,"FUELING STATES: ${fuelingStates[0]} && ${fuelingStates[1]} && ${fuelingStates[2]} && ${fuelingStates[3]} ")

                if(!isNozzleClicked && !fuelingStates[0] && !fuelingStates[1] && !fuelingStates[2] && !fuelingStates[3] )
                {
                    setBeep()
                    /*clearTrxHistoryInSp()
                    stopConnectivityChecker()
                    finish()*/
                    goBackOrCancelTrx()
                } else if(!isNozzleClicked && fuelpos!=null && fuelpos.isExist && !fuelingStates[1] && !fuelingStates[2] && !fuelingStates[3] ){
                    setBeep()
                    goBackOrCancelTrx()
                }
            Timer().schedule(5000) {
                isBackButtonPressed = false
            }
        }
        }

    }

    private fun initData() {
        startFccReceiver()
        setupRecyclerview()
        mBinding.btnRetryFcc.setOnClickListener {
            setBeep()
            retryFccConnection()
            isButtonClicked = true
        }
        mBinding.btnVoid.setOnClickListener {
            setBeep()
            isButtonClicked = true
            clearTrxHistoryInSp()
            if(intentExtrasModel!!.typePay == VISA_VALUE || intentExtrasModel!!.typePay == CARD_VALUE)
            {
                cancelAlert(this)
            }
            else
            {
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.customer_cancel_transaction_))
            }
        }

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        if (intentExtrasModel!!.pumpName != null) {
            mBinding.tvPump.text = intentExtrasModel!!.pumpName
        } else {
            mBinding.tvPump.text = intentExtrasModel!!.mPumpNumber
        }
        mBinding.tvPump2.text = mBinding.tvPump.text

        val fuelPrices = prefs.getFuelProductPrices()
        if(fuelPrices!=null){
            fusionPriceConfiguration.clear()
            fusionPriceConfiguration.addAll(fuelPrices)
        }

        if(intentExtrasModel!!.mPumpNumber!=null){
            pumpNumber = intentExtrasModel!!.mPumpNumber!!
        }
        else {
            showToast(getString(R.string.null_value_received_in_pump_number))
            gotoPumpSelectionActivity()
        }

        isQtySelected = intentExtrasModel!!.isQtySelected

        val lastTrxNozzleModel = prefs.getNozzleModel() // checking last transaction before power cut
        val lastTrxNozzleClicked = prefs.getNozzleClicked()
        val lastTrxFuellingStates = prefs.getFuellingStates()

        isNozzleClicked = lastTrxNozzleClicked

        startConnectivityChecker()
        val connectivity =  Connectivity.isNetworkAvailable(this@ProductSelectionActivity)
        if(connectivity) {
            if(fusionExist){
                if(!FusionService.isRunning(this@ProductSelectionActivity))
                    FusionService.start(this@ProductSelectionActivity)

                val fccConnected =  FusionService.fccConnected()
                if(!fccConnected) {
                    //showPumpDisconnected(true)
                    //showFccConnectionLayout(true)                               // added to show disconnection layout on create of acitivity https://app.clickup.com/t/2zbxdmz

                    if(lastTrxFuellingStates[0] || lastTrxNozzleClicked) {       //if pump was authorized before
                        showPumpDisconnected(true)                      //show fcc disconnect without action buttons no need to run cancel timer here
                    } else {
                        showFccConnectionLayout(true)                    //show fcc disconnect with action buttons, need to run cancel timer here
                    }

                    FusionService.connectFcc(this@ProductSelectionActivity)
                }
            }
            else if(fuelpos.isExist){
                if(!FuelPosService.isRunning(this@ProductSelectionActivity))
                    FuelPosService.start(this@ProductSelectionActivity)

                FuelPosService.startFuelPosEpr()
                FuelPosService.startFuelPosEprServer()
            }
        }
        else {
            showFccConnectionLayout(true)
        }
        

        if(lastTrxNozzleModel!=null && isUnattendantMode){
            fuelingStates = lastTrxFuellingStates
            isNozzleClicked = lastTrxNozzleClicked
            selectedNozzleModel = lastTrxNozzleModel
            intentExtrasModel!!.articleID = selectedNozzleModel!!.Article.toString()
            fccProductId = selectedNozzleModel!!.fcc_prod_id.toString()
            showLoading(true)
            prefs.saveNozzleModel(selectedNozzleModel)
            getProductModel(intentExtrasModel!!.articleID!!.toInt())

            if(fuelingStates.isNotEmpty() && (fuelingStates[0] || lastTrxNozzleClicked)) { // fueling was started before
                mBinding.productLayout.visibility = View.VISIBLE
                showLoading(true)
                mBinding.progresMsg.text = getString(R.string.please_wait_gettinh_transaction_details)
                //check previous transaction details
                FusionService.getPumpStatus(intentExtrasModel!!.mPumpNumber!!) //check pump status
                tokenTRX = intentExtrasModel!!.tokenTRX!!
                pendingTrx = true
            }
        }
    }

    var productList : ArrayList<ProductModel> = ArrayList()
    private fun setupRecyclerview() {
        listNozzles.clear()
        productList = getAllProductsList()
        mBinding.mListView.setHasFixedSize(true)
//        if(referenceModel!!.RFID_TERMINALS != null)
//        {
//            mBinding.emptyList.visibility = View.GONE
//            mBinding.mListView.visibility = View.VISIBLE
//        }
//        else {
//            mBinding.emptyList.visibility = View.VISIBLE
//            mBinding.mListView.visibility = View.GONE
//        }

        adapter = RecyclerViewArrayAdapter(listNozzles,this)
        mBinding.mListView.layoutManager = GridLayoutManager(this, 2)
        mBinding.mListView.adapter = adapter

        if (intentExtrasModel!!.verificationType != 2 && intentExtrasModel!!.typePay != AppConstant.RFID_VALUE && intentExtrasModel!!.listNozzles!=null)
        {
            for (nozzle in intentExtrasModel!!.listNozzles!!) {
                if(isProductAvailable(nozzle.prdt_SKU))
                    listNozzles.add(NozzelsModel(nozzle.Article,nozzle.CardId,nozzle.FP_NOOZLE,nozzle.Name,nozzle.id,getFusionProductColor(nozzle.fcc_prod_id!!),nozzle.prdt_SKU,nozzle.fcc_prod_id))
            }
            adapter.notifyDataSetChanged()
        } else {
            showLoading(true)
        }
        if(adapter.itemCount > 0)
        {
            mBinding.prompt.visibility = View.VISIBLE
            mBinding.emptyList.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        else {
            mBinding.prompt.visibility = View.GONE
            mBinding.emptyList.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
    }
    var isNozzleClicked = false
//    var fuelingStates = arrayOf("Authorized", "Started", "Fueling", false, false)
    var fuelingStates = arrayOf(false, false, false, false, false)
    private fun showLoading(isLoading: Boolean) {
        if (isLoading) {
            mBinding.progressBarLayout.visibility = View.VISIBLE
            mBinding.prompt.visibility = View.GONE
            mBinding.mListView.visibility = View.GONE
            isNozzleClicked = true
        } else {
            mBinding.progressBarLayout.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
            mBinding.prompt.visibility = View.VISIBLE
            for (i in fuelingStates.indices) {
                fuelingStates[i] = false
            }
            isNozzleClicked = false
        }
        //prefs.saveNozzleModel(null)                           // commented because after second power off product selection screen was after opening product selection plese check https://app.clickup.com/t/2uhcvx7
        prefs.saveFuellingStates(fuelingStates)
        prefs.saveNozzleClicked(isNozzleClicked)
    }
    private fun getFusionProductColor(productNo: Int): String {
        try {
            if(prefs.getProductColors() != null) {
                for (product in prefs.getProductColors()!!) {
                    if (productNo == product.productNo) {
                        return "#${product.productColour}"
                    }
                }
            }
            else
            {
                return "#FF8212"
            }
        }
        catch (e:Exception) {
            //e.printStackTrace()
            return "#FF8212"
        }

        return "#FF8212"
    }

    private fun startFccReceiver(){
        if(fusionExist)
            startIfsfReceiver()
        else if(fuelpos.isExist){
            startFuelPosReceiver()
        }

    }
    private fun stopFccReceiver(){
        if(fusionExist)
            stopIfsfReceiver()
        else if(fuelpos.isExist){
            eprPumpStateChecker.cancel()
            stopFuelPosReceiver()
            stopTransactionDataMessageChecker()
        }
    }
    override fun setObserver() {

    }


    var dialog: AlertDialog? = null
    var enteredQty = 0.0
    fun showEnterQtyDialog() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_qty, null)
        builder.setView(layout)
        val inputBox: AppCompatEditText = layout.findViewById(R.id.inputBox)
        inputBox.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(3))
        val subTitle: AppCompatTextView = layout.findViewById(R.id.tvQty)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val fuelQtyUnit = referenceModel!!.FUEL_QTY_UNIT?: "L"
        subTitle.text = fuelQtyUnit
        inputBox.inputType = InputType.TYPE_CLASS_NUMBER
        cancelButton.setOnClickListener { v: View? ->
            dialog!!.dismiss()
            enteredQty = 0.0
            cancelTransaction("","",1)
        }
        submitButton.setOnClickListener { v: View? ->
            if (!inputBox.text.toString().isEmpty()) {
                dialog!!.dismiss()
                enteredQty = inputBox.text.toString().toDouble()
                gotoNextStep()
            } else {
                inputBox.error = getString(R.string.enter_last_3_digits)
            }
        }
        dialog = builder.create()
        dialog!!.show()
    }

    private var lastTrxSender = "0"
    private var isPowerCutGetFuelSaleTrxMsgSent = false
    private fun performNextStep(msg: String) {
        var message = msg
        if(!msg.isNullOrEmpty())
        {
            val pump =   StringUtils.substringBetween(message,"DeviceID=\"","\"")
            if(pump == pumpNumber)
            {
                val errorCode = StringUtils.substringBetween(message, "<ErrorCode>", "</ErrorCode>")
                var errorMessage = ""
                log(TAG, "Error Code: $errorCode")
                log(TAG, "Received: $errorCode")
                if(errorCode != null)
                {
                    errorMessage = FusionService.getFusionErrorMessage(errorCode)
                }
                if(!errorCode.isNullOrEmpty() && !errorCode.contains("ERRCD_OK")) {
                    val errMsg = FusionService.getErrorMessage(errorCode)
                    showSnakeBarColor("$errMsg ")
                }
                if (!errorCode.isNullOrEmpty() && errorCode.contains("ERRCD_OK")) {
                    if (message.contains("ServiceResponse")) {
                        val overallResult = StringUtils.substringBetween(message, "OverallResult=\"", "\"")
                        val requestType = StringUtils.substringBetween(message, "RequestType=\"", "\"")

                        if (overallResult == "Success") {
                            if (requestType == "GetFPState") {
                                //stopFpStateChecker()
                                val deviceState = StringUtils.substringBetween(message,  " <DeviceState>","</DeviceState>")
                                log(TAG,"FP state:  $deviceState")
                                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Fuel State  - $deviceState"))
                                showPumpDisconnected(false)
                                if(deviceState.contains("FUELLING"))
                                {
                                    showLoading(true)
                                    mBinding.toolbarPumpList.toolbar.isEnabled = false
                                    fuelingStates[2] = true
                                    mBinding.progresMsg.text = getString(R.string.fuelling)
                                    log(TAG, "Fuelling...")

                                    prefs.saveFuellingStates(fuelingStates)

                                    stopConnectivityChecker()
                                }
                                else
                                {
                                    showLoading(true)
                                    if(deviceState.contains("FDC_AUTHORISED") && clearOldAuthorizedTrx){ //pump was authorized
                                        fuelingStates[0] = true                           // added to fix this issue https://app.clickup.com/t/31gq8kp
                                        //fuelingStates[1] = true
                                        stopConnectivityChecker()
                                        startConnectivityChecker()
                                        prefs.saveFuellingStates(fuelingStates)

                                        mBinding.progresMsg.text = getString(R.string.please_wait_we_are_cancelling_your_transaction)
                                    }
                                    else if(deviceState.contains("FDC_AUTHORISED") /*&& clearOldAuthorizedTrx*/) { //pump was authorized
                                        fuelingStates[0] = true                         // added to fix this issue https://app.clickup.com/t/31gq8kp
                                        //fuelingStates[1] = true
                                        //stopFuelPoint() //stopping the pump
                                        showLoading(true)
                                        stopConnectivityChecker()
                                        startConnectivityChecker()

                                        prefs.saveFuellingStates(fuelingStates)

                                        mBinding.progresMsg.text = getString(R.string.pump_authorized)

                                        intentExtrasModel!!.mTransaction!!.isPumpError = 1  // added this flag to make sure that tranaction will not refund in case in delaied transaction in case of pump error or power cut
                                                                                            // explanation why we added it here https://app.clickup.com/t/32c9mx7

                                    }
                                    else if(deviceState.contains("FDC_FUELLING") && !fuelingStates[2]){
                                        fuelingStates[2] = true
                                        showLoading(true)

                                        prefs.saveFuellingStates(fuelingStates)

                                        mBinding.progresMsg.text = getString(R.string.fueling_in_progress)
                                        stopConnectivityChecker()
                                    }
                                    else if(deviceState.contains("FDC_STARTED") && !fuelingStates[2]){
                                        fuelingStates[1] = true
                                        showLoading(true)

                                        prefs.saveFuellingStates(fuelingStates)

                                        mBinding.progresMsg.text = getString(R.string.pump_started)
                                        stopConnectivityChecker()
                                        startConnectivityChecker()
                                    }
                                    //else if(deviceState.contains("FDC_READY") && fuelingStates[0] ) { // fueling was started before
                                    else if(deviceState.contains("FDC_READY") && (fuelingStates[0] || fuelingStates[1])) { // fueling was started before   // added to fix this issue https://app.clickup.com/t/31gq8kp
                                        showPumpDisconnected(false)
                                        log(TAG, "deviceState.contains(\"FDC_READY\") && (fuelingStates[0] || fuelingStates[1])")
                                        log(TAG, "$deviceState.contains(\"FDC_READY\") && (${fuelingStates[0]} || ${fuelingStates[1]})")
                                        log(TAG,"FDC_READY ::: getAvailableTransactionList")
                                        showLoading(true)
                                        mBinding.progresMsg.text = getString(R.string.please_wait_gettinh_transaction_details)

                                        //startTransactionDataMessageChecker()
                                        //check previous transaction details
                                        FusionService.getAvailableTransactionList(intentExtrasModel!!.mPumpNumber.toString())

                                    }
                                    else if(deviceState.contains("FDC_ERRORSTATE") && fuelingStates[0]){
                                        log(TAG, "deviceState.contains(\"FDC_ERRORSTATE\") && fuelingStates[0]")
                                        log(TAG, "$deviceState: $message")
                                        showToast(getString(R.string.error_code_1))
                                        showPumpDisconnected(true)
                                        stopConnectivityChecker()
                                        //startFpStateChecker()

                                        prefs.isPumpError = true   /// added to resolve if terminal power-off twice during the transaction - it will go back to product selection screen
                                    }
                                    else if(fuelingStates[0] && deviceState.contains("FDC_INVALIDSTATE") || deviceState.contains("FDC_OFFLINE") || deviceState.contains("FDC_OUTOFORDER") || deviceState.contains("FDC_CLOSED")){
                                        log(TAG,"fuelingStates[0] && deviceState.contains(\"FDC_INVALIDSTATE\") || deviceState.contains(\"FDC_OFFLINE\") || deviceState.contains(\"FDC_OUTOFORDER\") || deviceState.contains(\"FDC_CLOSED\")")
                                        log(TAG,"${fuelingStates[0]} && $deviceState.contains(\"FDC_INVALIDSTATE\") || $deviceState.contains(\"FDC_OFFLINE\") || $deviceState.contains(\"FDC_OUTOFORDER\") || $deviceState.contains(\"FDC_CLOSED\")")
                                        showPumpDisconnected(true)
                                        "${getString(R.string.pump_connection_error)} - $deviceState".also { mBinding.tvPumpMessage.text = it }
                                        stopConnectivityChecker()
                                        //startFpStateChecker()
                                    }
                                    /*else if(fuelingStates[0] && message.contains("DeviceAlarm")){
                                        log(TAG, "DeviceAlarm: $message")
                                        showSnakeBar(getString(R.string.device_alaram))
                                    }
                                    else if(deviceState.contains("FDC_CALLING")){  // added to resolve https://app.clickup.com/t/2zbxp7z issue
                                        // don't do anything here
                                    }*/
                                    else {  // user did not perform any step then start connectivity transaction
                                        //abortTransaction()
                                        log(TAG,"****** all above conditions are false and checking if fuelingStates[0] || fuelingStates[1]) { ${fuelingStates[0]} || ${fuelingStates[1]}} last else condition")

                                        //if(fuelingStates[0] || fuelingStates[1]) {                      // added this condition https://app.clickup.com/t/31pbhgc
                                        if(fuelingStates[0] || fuelingStates[1] || isNozzleClicked) {                      // added this condition https://app.clickup.com/t/31pbhgc   //isNozzleClicked added to resolved https://app.clickup.com/t/31w1yk8
                                            showPumpDisconnected(false)
                                            showLoading(true)
                                            mBinding.progresMsg.text = getString(R.string.please_wait_gettinh_transaction_details)
                                            FusionService.getAvailableTransactionList(intentExtrasModel!!.mPumpNumber.toString())
                                        } else {
                                            log(TAG,"****** all above conditions are false and came in else block")
                                            stopConnectivityChecker()
                                            startConnectivityChecker()
                                            showLoading(false)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    else if (!isNozzleClicked && message.contains("FDCMessage")) { //newly added this condition
                        if (message.contains("DeviceState")) {
                            val deviceState = StringUtils.substringBetween(message,  " <DeviceState>","</DeviceState>")
                            log(TAG,"Pump: $pump FP state:  $deviceState")
                            if(pump == pumpNumber){
                                showPumpDisconnected(false)
                                if(deviceState.contains("FDC_ERRORSTATE") && fuelingStates[0]){ // pump was authorized before and pump was disconnected
                                    showPumpDisconnected(true)
                                    stopConnectivityChecker()

                                    prefs.isPumpError = true   /// added to resolve if terminal power-off twice during the transaction - it will go back to product selection screen
                                }
                            } else {
                                log(TAG,"Different Pump: $pump FP state:  $deviceState")
                            }
                        }
                    }
                }
                else if (errorCode != null && errorCode != "ERRCD_OK" && message.contains("ServiceResponse")) {
                    val requestType = StringUtils.substringBetween(message, "RequestType=\"", "\"")
                    if (requestType == "GetAvailableFuelSaleTrxs" && message.contains("ERRCD_NOTRANS") && !fuelingStates[0] /*&& !fuelingStates[1]*/) {
                        log(TAG,"OCCURRENCE AT 1")
                        cancelTransaction("","",4)
                    }
                }
                if (isNozzleClicked) {
                    val requestType = StringUtils.substringBetween(message, "RequestType=\"", "\"")
                    if( message.contains("ServiceResponse") && requestType.contains("AuthoriseFuelPoint")) {                    // newly added parent condition
                        if (message.contains("ERRCD_NOTPOSSIBLE") && !(executionMode == "SuspendFuelling" || executionMode == "ResumeFuelling")) {
                            gotoErrorFlow("ERRCD_NOTPOSSIBLE")
                        }
                        else if (message.contains("ValidationError")) {
                            gotoErrorFlow(errorMessage)
                        }
                        else if (message.contains("ERRCD_MAXSTACKLIMIT")) {              // this will come when pump max stack limit reached
                            gotoErrorFlow(errorMessage)
                        }
                        else if (message.contains("ERRCD_BADDEVID")) {                   // this will come when terminal sent wrong PUMP Number
                            gotoErrorFlow("ERRCD_BADDEVID")
                        }
                        else if (message.contains("ERRCD_FPLOCK")) {
                            gotoErrorFlow("ERRCD_FPLOCK")
                        }
                        else if (message.contains("ERRCD_INVTRANS")) {   //this will come only when clearing transaction with wrong sequence number, which will never happen in our application because application is only clearing transaction based on Seq No sent by FCC
                            //gotoErrorFlow("ERRCD_INVTRANS")
                        }
                        else if (message.contains("ERRCD_NOMONEYPRES")) {
                            gotoErrorFlow("ERRCD_NOMONEYPRES")
                        }
                        else if (message.contains("ERRCD_GENAUTHLIMIT")) {
                            gotoErrorFlow("ERRCD_GENAUTHLIMIT")
                        }
                        else if (message.contains("ERRCD_POSAUTHLIMIT ")) {
                            gotoErrorFlow("ERRCD_POSAUTHLIMIT ")
                        }
                        else if (message.contains("ERRCD_GENAUTHLIMIT")) {
                            gotoErrorFlow("ERRCD_GENAUTHLIMIT")
                        }

                        else if (message.contains("ERRCD_BADTYPE")) {
                            gotoErrorFlow("ERRCD_BADTYPE")
                        }
                        else if (message.contains("ERRCD_DEVICEUNAVAILABLE")) {
                            gotoErrorFlow("ERRCD_DEVICEUNAVAILABLE")
                        }
                        else if (message.contains("ERRCD_CTRLERR")) {
                            gotoErrorFlow("ERRCD_CTRLERR")
                        }
                        else if (message.contains("ERRCD_WRONGDEVICENO")) {
                            gotoErrorFlow("ERRCD_WRONGDEVICENO")
                        }
                        else if (message.contains("ERRCD_DEVICEDISABLED")) {
                            gotoErrorFlow("ERRCD_DEVICEDISABLED")
                        }
                        else if (message.contains("ERRCD_INVTRANS")) {
                            gotoErrorFlow("ERRCD_INVTRANS")
                        }
                        else if (message.contains("ERRCD_COMMERR")) {
                            gotoErrorFlow("ERRCD_COMMERR")
                        }
                        else if (message.contains("ERRCD_INOP")) {
                            gotoErrorFlow("ERRCD_INOP")
                        }
                        else if (message.contains("ERRCD_DEVLOCK")) {
                            gotoErrorFlow("ERRCD_DEVLOCK")
                        }
                        else if (message.contains("ERRCD_DEVOFFL")) {
                            gotoErrorFlow("ERRCD_DEVOFFL")
                        }
                        else if (message.contains("ERRCD_BADVAL")) {
                            gotoErrorFlow("ERRCD_BADVAL")
                        }
                        else if (message.contains("ERRCD_NOPERM")) {
                            gotoErrorFlow("ERRCD_NOPERM")
                        }
                        else if (message.contains("ERRCD_LIMITERR")) {
                            gotoErrorFlow("ERRCD_LIMITERR")
                        }
                        else if (message.contains("ERRCD_NOZZLELOCK")) {
                            gotoErrorFlow("ERRCD_NOZZLELOCK")
                        }
                        else if (message.contains("ERRCD_TANKLOCK")) {
                            gotoErrorFlow("ERRCD_TANKLOCK")
                        }
                        else if (message.contains("ERRCD_PREPAYERR")) {
                            gotoErrorFlow("ERRCD_PREPAYERR")
                        }
                        else if (message.contains("ERRCD_BADCONF")) {
                            gotoErrorFlow("ERRCD_BADCONF")
                        }
                        else if (message.contains("ERRCD_READERR")) {
                            gotoErrorFlow("ERRCD_READERR")
                        }
                        else if (message.contains("ERRCD_NORESTRANS")) {
                            gotoErrorFlow("ERRCD_NORESTRANS")
                        }
                        else if (message.contains("ERRCD_TRANSLOCKED")) {
                            gotoErrorFlow("ERRCD_TRANSLOCKED")
                        }
                        else if (message.contains("ERRCD_WRITEERR")) {
                            gotoErrorFlow("ERRCD_WRITEERR")
                        }
                        else if (message.contains("ERRCD_NOTALLOWED")) {
                            gotoErrorFlow("ERRCD_NOTALLOWED")
                        }
                        else if (message.contains("ERRCD_OTHER")) {
                            gotoErrorFlow("ERRCD_OTHER")
                        }
                        else if (message.contains("ERRCD_BADVAL")) {
                            gotoErrorFlow("ERRCD_BADVAL")
                        }
                        else if (message.contains("FDC_CLOSED")) {
                            gotoErrorFlow("FDC_CLOSED")
                        }
                    }
                    else if(message.contains("DeviceAlarm")){
                        log(TAG,"Device alarm received.......")
                        log(TAG, message)
                    }
                    else if (errorCode != null && errorCode.contains("ERRCD_OK")) {
                        if (message.contains("ServiceResponse")) {
                            message = formatMessage(message)
                            val overallResult = StringUtils.substringBetween(message, "OverallResult=\"", "\"")
                            val requestType = StringUtils.substringBetween(message, "RequestType=\"", "\"")
                            if (overallResult == "Success" && errorCode == "ERRCD_OK") {
                                val jsonString: String = Support.xmlToJsonString(message)!!
                                log(TAG, "JSON STRING:::: $jsonString")
                                if (requestType == "AuthoriseFuelPoint") {
                                    log(TAG, "Authorize fuel point response received")
                                    /*try {
                                        if ( *//*deviceState.contains("FDC_AUTHORISED") && *//* !fuelingStates[0]) {
                                            fuelingStates[0] = true
                                            log(TAG, "FDC_AUTHORISED")
                                            prefs.saveFuellingStates(fuelingStates)
                                            mBinding.progresMsg.text = getString(R.string.pump_authorized)
                                            log(TAG, "Pump Authorised...")
                                            stopConnectivityChecker()
                                            startConnectivityChecker()
                                        }
                                    } catch (e: java.lang.Exception) {
                                        e.printStackTrace()
                                    }*/
                                }
                                else if (requestType == "GetFPState") {
                                    val deviceState = StringUtils.substringBetween(message,  " <DeviceState>","</DeviceState>")
                                    log(TAG,"FP state:  $deviceState")
                                    if(deviceState.contains("FDC_AUTHORISED")){
                                        showLoading(true)
                                        stopConnectivityChecker()
                                        startConnectivityChecker()
                                        intentExtrasModel!!.mTransaction!!.isPumpError = 1  // added this flag to make sure that tranaction will not refund in case in delaied transaction in case of pump error or power cut
                                        // explanation why we added it here https://app.clickup.com/t/32c9mx7
                                    }
                                    else if(deviceState.contains("FDC_ERRORSTATE")) //fusion goes offline and came back then check this condition
                                    {
                                        showPumpDisconnected(true)
                                        //stopConnectivityChecker()
                                        //startFpStateChecker()

                                        prefs.isPumpError = true   /// added to resolve if terminal power-off twice during the transaction - it will go back to product selection screen
                                        intentExtrasModel!!.mTransaction!!.isPumpError = 1  // added this flag to make sure that tranaction will not refund in case in delaied transaction in case of pump error or power cut
                                        // explanation why we added it here https://app.clickup.com/t/32c9mx7
                                    }
                                    else if(!deviceState.contains("FUELLING") && !isPowerCutGetFuelSaleTrxMsgSent) //fusion goes offline and came back then check this condition
                                    {
                                        isPowerCutGetFuelSaleTrxMsgSent = true
                                        //startTransactionDataMessageChecker()
                                        FusionService.getAvailableTransactionList(intentExtrasModel!!.mPumpNumber.toString())
                                        showLoading(true)
                                        mBinding.progresMsg.text = getString(R.string.please_wait_we_are_fetching_your_transaction_details)

                                    }
                                    /*else
                                    {
                                        showLoading(true)
                                    }*/
                                }
                                else if (requestType == "GetAvailableFuelSaleTrxs") {

                                    mBinding.progresMsg.text = getString(R.string.please_wait)
                                    mBinding.productLayout.visibility = View.VISIBLE

                                    fuelingStates[4] = true
                                    prefs.saveFuellingStates(fuelingStates)

                                    checkTransactionSequenceNumbers(message)

                                }
                                else if (requestType == "GetFuelSaleTrxDetails") {
                                    log(TAG, "$requestType == \"GetFuelSaleTrxDetails\"")
                                    if (fuelingStates[4]) {
                                        senderId = Support.getSN()!!
                                        if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)
                                        val releaseToken = StringUtils.substringBetween(message, "<ReleaseToken>", "</ReleaseToken>")

                                        if(trxSequenceList.isNotEmpty()) {
                                            trxSequenceList.removeLast()
                                        }

                                        //if(releaseToken == tokenTRX){     //commented because getting empty tokenTrx if terminal power of twice
                                        if(releaseToken == intentExtrasModel!!.tokenTRX){       //added to resolve this issue https://app.clickup.com/t/2zbwta3
                                            verifyReceivedFuelSaleTransaction(message)
                                        } else {
                                            checkNextTransaction()
                                        }

                                    } else {
                                        log(TAG, "GOT OLD FUEL TRX")
                                    }
                                }
                                else if (requestType == "SuspendFuelling") {
                                    try {

                                        log(TAG, "SuspendFuelling")
                                        mBinding.progresMsg.text = getString(R.string.fueling_paused)
                                        log(TAG, "Fuelling Paused...")
                                    } catch (e: java.lang.Exception) {
                                        e.printStackTrace()
                                        log(TAG, e.message + " " + e.cause)
                                    }
                                }
                                else if (requestType == "ResumeFuelling") {
                                    try {
                                        mBinding.progresMsg.text = getString(R.string.fuelling)
                                        log(TAG, "Fuelling Resumed...")
                                    } catch (e: java.lang.Exception) {
                                        e.printStackTrace()
                                        log(TAG, e.message + " " + e.cause)
                                    }
                                }
                            } else {
                                showToast(overallResult)
                                if (intentExtrasModel!!.verificationType == 2) {
                                    //unregisterReceiver(ifsfRecever)
                                    val intent = Intent()
                                    setResult(AppConstant.RFID_RESULT_CODE, intent)
                                } else {
                                    showLoading(false)
                                }
                                super.finish()
                            }
                        }
                        else if (message.contains("FDCMessage")) {
                            val jsonString: String = Support.xmlToJsonString(formatFDCMessage(message))!!
                            log(TAG, "JSON STRING:::: $jsonString")
                            val messageType = StringUtils.substringBetween(message, "MessageType=\"", "\"")
                            val gson = Gson()
                            if (messageType == "FPStateChange") {
                                try {
                                    val stateMessage: FdcMessageFPState = gson.fromJson(jsonString, FdcMessageFPState::class.java)
                                    val device: DeviceClassFPS = stateMessage.fDCMessage.fDCdata.deviceClass
                                    val pumpNo: Int = stateMessage.fDCMessage.fDCdata.deviceClass.pumpNo
                                    val nozzles: List<NozzleFPS> = device.nozzles
                                    for (i in nozzles.indices) {
                                        if (pumpNo == intentExtrasModel!!.mPumpNumber!!.toInt()) {
                                            fpStateChanged(device.deviceState)
                                            break
                                        }
                                    }
                                } catch (e: JsonSyntaxException) {
                                    //e.printStackTrace()
                                    try {
                                        val stateMessage: FdcMessageFPState2 = gson.fromJson(jsonString, FdcMessageFPState2::class.java)
                                        val device: DeviceClassFPS2 = stateMessage.fDCMessage.fDCdata.deviceClass
                                        if (device.pumpNo.toInt() == intentExtrasModel!!.mPumpNumber!!.toInt()) {
                                            fpStateChanged(device.deviceState)
                                        }
                                    } catch (ex: JsonSyntaxException) {
                                        ex.printStackTrace()
                                        log(TAG, ex.message + " " + ex.cause)

                                        if(message.contains("DeviceClass Type") && message.contains("DeviceState Stopped")){
                                            val pumpNo = StringUtils.substringBetween(message, "PumpNo=\"", "\"")
                                            val isStopped = StringUtils.substringBetween(message, "Stopped=\"", "\"")
                                            val deviceState = StringUtils.substringBetween(message, "Stopped=\"$isStopped\">", "</DeviceState>")
                                            log(TAG,"pumpNo:$pumpNo -- stopped:$isStopped -- state:$deviceState")
                                            if (pumpNo.toInt() == intentExtrasModel!!.mPumpNumber!!.toInt()) {
                                                fpStateChanged(pumpNo)
                                            }
                                        }
                                        else {
                                            //unable to handle message
                                            log(TAG,"Unable to handle FDC message")
                                        }
                                    }
                                }
                                /* }*/
                            }
                            //else if(messageType == "FuelSaleTrx" && prefs.isPumpError){   // added to resolve if pump error was there and after that transaction data received from pump after reconnection
                            else if(messageType == "FuelSaleTrx" /*&& prefs.isPumpError*/){   // pump error commented because we are checking fdc message directily send from fcc in case of normal trnsaction
                                log(TAG, "IN FDC  MESSAGE ----- $messageType == \"FuelSaleTrx\"")
                                //if (fuelingStates[4]) {
                                //if (fuelingStates[0] || fuelingStates[1] || fuelingStates[2] ||fuelingStates[3] || fuelingStates[4]) {
                                    senderId = Support.getSN()!!
                                    if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)
                                    val releaseToken = StringUtils.substringBetween(message, "<ReleaseToken>", "</ReleaseToken>")

                                    if(releaseToken == tokenTRX){
                                        verifyReceivedFuelSaleTransaction(message)
                                    } else {
                                       // storeDifferentFuelTransaction(message)
                                        log(TAG, "GOT DIFFERENT FUEL TRX")
                                      //  showDisputedLayout(true) Commented this because recieving zero or cleared transactions https://app.clickup.com/t/36w7773
                                    }
                                /*} else {
                                    log(TAG, "GOT OLD FUEL TRX")
                                }*/
                            }
                        }
                    }
                }
                else {
                    log(TAG, "Invalid Message")
                }
            }
        }
    }
    fun storeDifferentFuelTransaction(message: String)
    {
        try {
            val mTransaction = if(isNozzleClicked) {
                TransactionModel()
            } else {
                intentExtrasModel!!.mTransaction!!
            }
            val fusionSaleId = StringUtils.substringBetween(message, "FusionSaleId=\"", "\"")
            val seqNo = StringUtils.substringBetween(message, "TransactionSeqNo=\"", "\"")
            val fpPump = StringUtils.substringBetween(message, "DeviceID=\"", "\"")
            val fpProduct = StringUtils.substringBetween(message, "<ProductNo>", "</ProductNo>")
            val fpAmount = StringUtils.substringBetween(message, "<Amount>", "</Amount>")
            val unitPrice = StringUtils.substringBetween(message, "<UnitPrice>", "</UnitPrice>")
            val productUM = StringUtils.substringBetween(message, "<ProductUM>", "</ProductUM>")
            val productName = StringUtils.substringBetween(message, "<ProductName>", "</ProductName>")
            val endTimeStamp = StringUtils.substringBetween(message, "<EndTimeStamp>", "</EndTimeStamp>")
            val releaseToken = StringUtils.substringBetween(message, "<ReleaseToken>", "</ReleaseToken>")
            val fpVolume = StringUtils.substringBetween(message, "<Volume>", "</Volume>")
            if (mTransaction != null) {
                mTransaction.pumpId = fpPump
                mTransaction.sequenceController = seqNo
                mTransaction.idProduit = intentExtrasModel!!.articleID!!.toInt()
                mTransaction.amount = fpAmount.toDouble()
                mTransaction.quantite = fpVolume.toDouble()
                mTransaction.unitPrice = unitPrice.toDouble()
                mTransaction.fccProductId =  fccProductId
                mTransaction.fccSaleId = fusionSaleId
                mTransaction.productName = productName
                mTransaction.timsSignDetails!!.fuelQtyUnit = productUM
                mTransaction.dateTransaction = endTimeStamp
                mTransaction.isDisputedTrx = 1
                mTransaction.transactionStatus = 0
                mTransaction.fccReleaseToken = releaseToken
                if(isNozzleClicked)
                {
                    mTransaction.reference = Support.generateNewReferenceNumber(this)
                    insertTransactionData(mTransaction)
                }
                else
                {
                    updateTransactionByReferenceId(mTransaction) // Zero Transaction
                }

            }
            log(TAG, "GOT DIFFERENT TRANSACTION Details = Pump: $fpPump Product: $fpProduct Amount:$fpAmount Volume: $fpVolume SeqNo: $seqNo")
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

    }

    private fun gotoErrorFlow(errorMessage: String) {
        log(TAG, "gotoErrorFlow:: $errorMessage")
        log(TAG, "fuelingStates[0] ${fuelingStates[0]}")
        //if(fuelingStates[0])
        if(fuelingStates[0] || fuelingStates[1])    //added to resolved this case https://app.clickup.com/t/31w12hk
        {
            showLoading(false)
            log(TAG, "PUMP OUT OF SERVICE")
            showPumpDisconnected(true)
            stopConnectivityChecker()
            "${getString(R.string.pump_out_of_service)} - $errorMessage".also { mBinding.tvPumpMessage.text = it }
        }
        else
        {
            log(TAG, "CANCELING TRANSACTION")
            goBackOrCancelTrx("${getString(R.string.error)} - $errorMessage",getString(R.string.transaction_cancelled))
        }
    }

    private fun verifyReceivedFuelSaleTransaction(message:String){
        val completionReason = StringUtils.substringBetween(message, "<CompletionReason>","</CompletionReason>")
        val authorizationSenderID = StringUtils.substringBetween(message, "<AuthorisationApplicationSender>","</AuthorisationApplicationSender>")
        log(TAG, "AuthorisationApplicationSender:: $authorizationSenderID")
        log(TAG, "senderId:: $senderId")

        val trxPaymentStatus = StringUtils.substringBetween(message, "<State>","</State>")
        val trxSequence = StringUtils.substringBetween(message, "TransactionSeqNo=\"","\"")
        val fpPump = StringUtils.substringBetween(message, "DeviceID=\"", "\"")
        val fpProduct = StringUtils.substringBetween(message, "<ProductNo>", "</ProductNo>")

        val trxVolume = StringUtils.substringBetween(message, "<Volume>", "</Volume>")  // created this variable to check 0 trx volume
        val trxAmt = StringUtils.substringBetween(message, "<Amount>", "</Amount>") // created this variable to check 0 trx amount

        val transactionDao = TransactionDao()
        transactionDao.open()
        val trxBySeq = transactionDao.getTransaction(fpPump,trxSequence)
        //val transactionModel = transactionDao.getTransactionByReleaseToken(tokenTRX)

        transactionDao.close()

        val values = getLatestAmountOfTransaction(intentExtrasModel!!.mPumpNumber.toString(), fccProductId, message)
        log(TAG, "Final FuelTrx amount Received: " + values[0])

        if(senderId == authorizationSenderID) // works in normal flow of transaction
        {
            log(TAG, "Success authorizationSenderID:: $authorizationSenderID")
            log(TAG, "Success senderId:: $senderId")

            if(trxPaymentStatus  != "Payable" && trxPaymentStatus  != "Locked" && trxBySeq!=null) {
                log(TAG,"OCCURRENCE AT 2")
                cancelTransaction("","",2)
            }
            else if(completionReason == "PumpDisconnected") {
                if(values[0].toDouble()>0)
                {
                    //sendClearTransaction(pumpNumber,trxSequence)
                    performFinalOperation(values, intentExtrasModel!!.mPumpNumber!!.toInt())
                }
                else
                {
                    cancelTransaction("","",5)
                }
            }
            //else if((trxPaymentStatus == "Payable" || trxPaymentStatus == "Locked") && trxBySeq == null && trxAmt == "0" && trxVolume == "0") { // ******* 0 tranasction found ******
            else if((trxPaymentStatus == "Payable" || trxPaymentStatus == "Locked") && trxAmt == "0" && trxVolume == "0") { // ******* 0 tranasction found ******
                cancelTransaction("","",3)
            }
            else {
                if(values[0].toDouble()>0 /*&& trxPaymentStatus == "Payable"*/){   // checking if amount is greater than zero
                    //sendClearTransaction(pumpNumber,trxSequence)
                    performFinalOperation(values, intentExtrasModel!!.mPumpNumber!!.toInt())
                } else {
                    val params = FuelSaleTrxPrams(intentExtrasModel!!.mPumpNumber.toString() + "", trxSequence) //sending request for one more time to get transaction with greater than 0 value
                    FusionService.sendFuelTrxDetailMsg(params, applicationContext)
                }
            }
            //endregion
        }
        else if(senderId == lastTrxSender) { // if fusion goes offline and came back then this will work
            log(TAG, "Success authorizationSenderID:: $lastTrxSender")
            log(TAG, "Success senderId:: $senderId")

            //newly added
            if(trxPaymentStatus != "Payable" && trxPaymentStatus  != "Locked" && trxBySeq!=null) {
                log(TAG,"OCCURRENCE AT 3")
                cancelTransaction("","",6)
            }
            else {

                if(values[0].toDouble()>0 && trxPaymentStatus == "Payable"){   // checking if amount is greater than zero
                    //sendClearTransaction(pumpNumber,trxSequence)
                    performFinalOperation(values, intentExtrasModel!!.mPumpNumber!!.toInt())
                } else {
                    val params = FuelSaleTrxPrams(intentExtrasModel!!.mPumpNumber.toString() + "", trxSequence) //sending request for one more time to get transaction with greater than 0 value
                    FusionService.sendFuelTrxDetailMsg(params, applicationContext)
                }
            }
            //newly added end

            //performFinalOperation(values, intentExtrasModel!!.mPumpNumber!!.toInt())
        }
    }

    private fun sendClearTransaction(pumpNo: String,trxSequence:String){
        val paymentType = Support.getFusionPaymentType(intentExtrasModel!!.mTransaction!!.modepay!!)
        val clearTrxDetails = ClearFuelSaleTrxPrams(deviceId = pumpNo, trxSequenceNo = trxSequence, paymentType = paymentType, attributeName = "referenceNo", attributeValue = intentExtrasModel!!.mTransaction!!.reference!!)
        //FusionService.clearTrxDetails(clearTrxDetails) //removed because clearing transactions on menu screen
        FusionService.clearTrxDetailsAfterDelay(clearTrxDetails) //Added to resolve Terminal Power off during saving of transaction
    }

    private var trxSequenceList = ArrayList<DeviceClassGAFT>()
    var getTrxCount = 0
    private fun checkTransactionSequenceNumbers(message: String){
        val jsonString: String = Support.xmlToJsonString(message)!!
        trxSequenceList.clear()
        try {
            val serviceResponse: ServiceResponseGAFT = gson.fromJson(jsonString,ServiceResponseGAFT::class.java)
            val trxList: List<DeviceClassGAFT> = serviceResponse.serviceResponse.fDCdata.deviceClasses
            if (referenceModel!!.RFID_TERMINALS != null) {
                val pumpsModels: List<RFIDPumpsModel> =referenceModel!!.RFID_TERMINALS!!.pumps
                for (pumpsModel in pumpsModels) {
                    for (deviceClassGAFT in trxList) {
                        if (deviceClassGAFT.pumpNo == pumpsModel.pump_number) {
                            trxSequenceList.add(deviceClassGAFT)
                            log(TAG, "Pump Available:: " + deviceClassGAFT.pumpNo)
                        }
                    }
                }
                log(TAG, "pumpsModels from API :: ${Gson().toJson(pumpsModels)}")
            } else {
                log(TAG, "Pump List Not Available")
            }
        }
        catch (ex:Exception) {
            val serviceResponse = gson.fromJson(jsonString, ServiceResponseGAFT2::class.java)
            if (serviceResponse != null) {
                val deviceClassGAFT /*: List<DeviceClassGAFT>*/ = serviceResponse.serviceResponse.fDCdata.deviceClass
                if (referenceModel!!.RFID_TERMINALS != null) {
                    val pumpsModels: List<RFIDPumpsModel> = referenceModel!!.RFID_TERMINALS!!.pumps
                    for (pumpsModel in pumpsModels) {
                        //for (deviceClassGAFT in trxList) {
                        if (deviceClassGAFT.pumpNo == pumpsModel.pump_number) {
                            trxSequenceList.add(deviceClassGAFT)
                            log(TAG, "Pump Available:: " + deviceClassGAFT.pumpNo)
                        }
                        //}
                    }
                    log(TAG, "pumpsModels from API :: $pumpsModels")
                } else {
                    log(TAG, "Pump List Not Available")
                }
            }
        }

        if(trxSequenceList.isNotEmpty()){
            checkNextTransaction()
        } else {
            log(TAG,"****** trx sequence numbers not received ******")
            if(getTrxCount <= 3){
                getTrxCount += 1
                log(TAG,"getTrxCount:: $getTrxCount")
                FusionService.getAvailableTransactionList(intentExtrasModel!!.mPumpNumber.toString())
                showLoading(true)
                mBinding.progresMsg.text = getString(R.string.please_wait_we_are_fetching_your_transaction_details)
            }
            else {
                showDisputedLayout(true)
            }
        }
    }
    private fun sendTransactionOnEmail(trx: TransactionModel) {
        CoroutineScope(Dispatchers.IO).launch {   //Background Thread
            var trxLog = ""

            var trxString = ""
            var intentExtrasModelString = ""
            var errorString = ""

            try{
                trxString = Gson().toJson(trx)
                intentExtrasModelString = Gson().toJson(intentExtrasModel!!)
            } catch (e:Exception){
                val sw = StringWriter()
                val pw = PrintWriter(sw)
                e.printStackTrace(pw)
                errorString = sw.toString()
            }

            val lineSeparator = "\n"
            val builder = StringBuilder()
            builder.append("\n***** APP STUCK LOG \n")
            builder.append("\n***** DEVICE INFO \n")
            builder.append("Brand: ${Build.BRAND}")
            builder.append(lineSeparator)
            builder.append("Device: ${Build.DEVICE}")
            builder.append(lineSeparator)
            builder.append("Model: ${Build.MODEL}")
            builder.append(lineSeparator)
            builder.append("Serial No: ${Build.SERIAL}")
            builder.append(lineSeparator)
            builder.append("Manufacturer: ${Build.MANUFACTURER}")
            builder.append(lineSeparator)
            builder.append("Product: ${Build.PRODUCT}")
            builder.append(lineSeparator)
            builder.append("SDK: ${Build.VERSION.SDK}")
            builder.append(lineSeparator)
            builder.append("Release: ${Build.VERSION.RELEASE}")
            builder.append(lineSeparator)
            builder.append("\n***** APP INFO \n")
            val versionName = app.rht.petrolcard.BuildConfig.VERSION_NAME
            builder.append("Version: $versionName")
            builder.append(lineSeparator)
            val versionCode = app.rht.petrolcard.BuildConfig.VERSION_CODE
            builder.append("Version Code: $versionCode")
            builder.append(lineSeparator)
            try {
                builder.append("Endpoint: ${MainApp.getPrefs().baseUrl}")
                builder.append(lineSeparator)
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val currentDate = Date()
            val dateFormat: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
            builder.append("Current Date: ${dateFormat.format(currentDate)}")

            builder.append(lineSeparator)
            builder.append("\n***** LAST TRANSACTION IN DB\n")
            builder.append(trxString)
            builder.append(lineSeparator)
            builder.append("\n***** APP INTENT LOG\n")
            builder.append(trxString)
            builder.append(intentExtrasModelString)
            if(errorString.isNotEmpty()){
                builder.append("Error caught in Gson().toJson()")
                builder.append(errorString)
                builder.append(lineSeparator)
            }
            builder.append("\n***** END OF LOG *****\n")
            trxLog = builder.toString()

            val referenceModel = MainApp.getPrefs().getReferenceModel()
            val emails = if (app.rht.petrolcard.BuildConfig.DEBUG) {
                "<EMAIL>"
            } else "<EMAIL>;<EMAIL>;<EMAIL>"

            val emailConfig = EmailConfig(
                "smtp.gmail.com",
                "465",
                "<EMAIL>",
                "sfcbjhzhvihyuiym",
                emails
            )

            if(referenceModel!=null){
                val config = referenceModel.crashLogEmailConfig
                if(config!=null){
                    if(!config.host.isNullOrEmpty() && !config.port.isNullOrEmpty() && !config.username.isNullOrEmpty() && !config.password.isNullOrEmpty() && !config.emailsWithSemicolon.isNullOrEmpty())
                        emailConfig.serverHost = config.host!!
                    emailConfig.serverPort = config.port!!
                    emailConfig.username = config.username!!
                    emailConfig.password = config.password!!
                    emailConfig.semicolonSeparatedEmailAddresses = config.emailsWithSemicolon!!
                }
            }

            val username = emailConfig.username
            val pass = emailConfig.password
            val subject = getString(R.string.app_name) + " Stuck Log"
            val texts =
                "Dear Dev Team,\n\nTerminal is just stuck on fueling page, please check following log for more details.\n\n\n$trxLog"
            val toAddress = emailConfig.semicolonSeparatedEmailAddresses

            if (username.isEmpty() || pass.isEmpty() || subject.isEmpty() || texts.isEmpty() || toAddress.isEmpty()) {
                Toast.makeText(MainApp.appContext, "ERROR: Fill all the fields", Toast.LENGTH_LONG).show()
            }

            val tos = StringUtility.extractEmails(toAddress)
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
            val dir = File(path + File.separator + AppConstant.LOG_FOLDER_NAME)
            val trxFileName = "${intentExtrasModel!!.mTransaction!!.reference}.log"
            val tFile = File(dir, trxFileName)
            val serviceFileName = "TRX${AppConstant.FUEL_SERVICE_LOG_NAME}_${prefs.logReferenceNo}.log"
            val sFile = File(dir, serviceFileName)
            val m = EmailSender(username, pass)
            m._from = username
            m._to = tos
            m._subject = subject
            m.body = texts

//            if (tFile.exists()) {
//                val filePath = FileProvider.getUriForFile(this@ProductSelectionActivity,
//                    <EMAIL> + ".provider",
//                    tFile)
//                m.addAttachment(filePath.path)
//            }
            if (m.send()) {
                Log.e(TAG, "App stuck email sent")
            } else {
                Log.e(TAG, "App stuck email failed to send.")
            }
        }
    }

    private fun checkNextTransaction() {
        if(trxSequenceList.size > 0){
            val pumpNumber = "${trxSequenceList[trxSequenceList.size-1].pumpNo}"
            val trxSeq = "${trxSequenceList[trxSequenceList.size-1].transactionSeqNo}"
            //val clearTrxDetails = FuelSaleTrxPrams(pumpNumber,trxSeq)
            log(TAG, "Sending Detail Trx Cmd :: Pump: $pumpNumber -- SeqNo: $trxSeq")
            val params = FuelSaleTrxPrams(pumpNumber + "", trxSeq)
            FusionService.sendFuelTrxDetailMsg(params, applicationContext)
        } else {
            log(TAG, "required transaction not found in list $pumpNumber trying to search again")
            if(getTrxCount <= 3){
              getTrxCount += 1
                log(TAG,"getTrxCount:: $getTrxCount")
                FusionService.getAvailableTransactionList(intentExtrasModel!!.mPumpNumber.toString())
                showLoading(true)
                mBinding.progresMsg.text = getString(R.string.please_wait_we_are_fetching_your_transaction_details)
            }
            else
            {
                showDisputedLayout(true)
            }
        }
    }

    var fuelTrxCommonModel: FuelTrxCommonModel? = null
    var fpVolume = ""
    var seqNumber:String? = ""
    private fun getLatestAmountOfTransaction(pumpNo: String, productId: String, message: String): Array<String> {

        val fusionSaleId = StringUtils.substringBetween(message, "FusionSaleId=\"", "\"")
        val seqNo = StringUtils.substringBetween(message, "TransactionSeqNo=\"", "\"")
        val fpPump = StringUtils.substringBetween(message, "DeviceID=\"", "\"")
        val fpProduct = StringUtils.substringBetween(message, "<ProductNo>", "</ProductNo>")
        val fpAmount = StringUtils.substringBetween(message, "<Amount>", "</Amount>")
        val unitPrice = StringUtils.substringBetween(message, "<UnitPrice>", "</UnitPrice>")
        val productUM = StringUtils.substringBetween(message, "<ProductUM>", "</ProductUM>")
        val productName = StringUtils.substringBetween(message, "<ProductName>", "</ProductName>")
        val endTimeStamp = StringUtils.substringBetween(message, "<EndTimeStamp>", "</EndTimeStamp>")
        val releaseToken = StringUtils.substringBetween(message, "<ReleaseToken>", "</ReleaseToken>")
        fpVolume = StringUtils.substringBetween(message, "<Volume>", "</Volume>")
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Fuel Unit Price - $unitPrice"))

        val values = arrayOf(fpAmount,fpPump,seqNo)

        if (fuelTrxCommonModel == null) fuelTrxCommonModel = FuelTrxCommonModel(fpPump, fpProduct, fpAmount, fpVolume, unitPrice)
        intentExtrasModel!!.fuelTrxCommonModel = fuelTrxCommonModel!!
        if (intentExtrasModel!!.mTransaction != null) {
            intentExtrasModel!!.mTransaction!!.pumpId = fpPump
            intentExtrasModel!!.mTransaction!!.sequenceController = seqNo
            intentExtrasModel!!.mTransaction!!.idProduit = intentExtrasModel!!.articleID!!.toInt()
            intentExtrasModel!!.mTransaction!!.amount = fpAmount.toDouble()
            intentExtrasModel!!.mTransaction!!.quantite = fpVolume.toDouble()
            intentExtrasModel!!.mTransaction!!.unitPrice = unitPrice.toDouble()
            intentExtrasModel!!.mTransaction!!.fccProductId =  fccProductId
            intentExtrasModel!!.mTransaction!!.fccSaleId = fusionSaleId
            intentExtrasModel!!.mTransaction!!.productName = productName
            intentExtrasModel!!.mTransaction!!.timsSignDetails!!.fuelQtyUnit = productUM
            intentExtrasModel!!.mTransaction!!.dateTransaction = endTimeStamp
            intentExtrasModel!!.mTransaction!!.fccReleaseToken = releaseToken

            intentExtrasModel!!.mTransaction!!.flagTelecollecte = 0         // added this flag to make sure that tranaction will not refund in case in delaied transaction in case of pump error or power cut
            intentExtrasModel!!.mTransaction!!.isPumpError = 0              // explanation why we added it here https://app.clickup.com/t/32c9mx7


            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
        }
        log(TAG, "GOT TRANSACTION Details = Pump: $fpPump Product: $fpProduct Amount:$fpAmount Volume: $fpVolume SeqNo: $seqNo")
        // return fpAmount;
        return values
    }
    var isClicked = false
    override fun onItemClick(view: View, model: NozzelsModel) {
        MultiClickPreventer.preventMultiClick(view)
        if(!isClicked)
        {
            isClicked = true
            setBeep()
            log(TAG,"onItemClick")
            showLoading(true)
            ///startFusionTrxCancelTimer()
            selectedNozzleModel = model
            if(intentExtrasModel!!.mTransaction != null)
            {
                intentExtrasModel!!.mTransaction!!.idProduit = model.Article
                intentExtrasModel!!.mTransaction!!.pumpId = intentExtrasModel!!.mPumpNumber
                intentExtrasModel!!.mTransaction!!.productName = model.Name
                val mTransactionDAO = TransactionDao()
                mTransactionDAO.open()
                mTransactionDAO.checkNUpdateByReference( intentExtrasModel!!.mTransaction!!)
                mTransactionDAO.close()
                log(TAG, "Updated Transaction Data:: ${gson.toJson( intentExtrasModel!!.mTransaction!!)}")
            }

            if (intentExtrasModel!!.isQtySelected) { //full tank or qty selected
                showEnterQtyDialog()
            }else {
                gotoNextStep()
            }
        }
        Handler(Looper.getMainLooper()).postDelayed({
            log(TAG,"onItemClick :: set to false")
            isClicked = false
        },5000)
    }

    private fun validateTransaction() {

        val messageErr: String
        val mProduitDAO = ProductsDao()
        mProduitDAO.open()
        var productCode: String
        if (intentExtrasModel!!.verificationType == 2) {
            for (p in referenceModel!!.pompes!!) {
                if (p.id.toString() == intentExtrasModel!!.mPumpNumber) {
                    for (n in p.Pistolers) {
                        if (n.Article == intentExtrasModel!!.articleID!!.toInt()) {
                            productCode = n.prdt_SKU!!
                            mProduitToCheckout = mProduitDAO.getProductsByCategoryId(productCode)
                            break
                        }
                    }
                }
            }
        } else if (mProduitToCheckout == null || intentExtrasModel!!.verificationType != 2) {
            mProduitToCheckout = mProduitDAO.getProductById(intentExtrasModel!!.articleID!!.toInt()) //selectionnerByCode("SSP");//selectionnerByIdProduit(13 ); //articleID);
        }
        mProduitDAO.close()
        var isArticleRestricted = false
        var isStationRestricted = false

        val mRestStationList= referenceModel!!.restrictions_stations
        val mRestArticleList= referenceModel!!.restrictions_articles
        if (intentExtrasModel!!.mRestArticleCard != 1 && mProduitToCheckout!!.productID != 0 && mRestArticleList.isNotEmpty()) {
            isArticleRestricted = UtilsCardInfo.isArticleRestriction(intentExtrasModel!!.mRestArticleCard!!,mProduitToCheckout!!.productID, mRestArticleList)
        } else {
            isArticleRestricted = false
        }
        isStationRestricted = if (intentExtrasModel!!.mRestStationCard != 1 && mRestStationList.isNotEmpty()) {
            UtilsCardInfo.isStationRestriction(intentExtrasModel!!.mRestStationCard!!, preferenceModel!!.stationID!!, mRestStationList)
        } else {
            false
        }
        when {
            isArticleRestricted -> {
                showLoading(false)
                showDialog(getString(R.string.error),resources.getString(R.string.block_rest_produit))
            }
            isStationRestricted -> {
                showLoading(false)
                showDialog(getString(R.string.error),resources.getString(R.string.block_rest_station))
            }
            else -> {
                executeFccCommand()
            }
        }

    }
    private fun validateOnlineTransaction() {
        // OnlinePaymentValidationTask().execute()
    }


    private fun performFinalOperation(fuelDetails: Array<String>, pumpId: Int) {
        //if(isfinalOperationDone) {
        log(TAG, "performing final operation on transaction")
        fuelTrxDetails = fuelDetails
        mBinding.progresMsg.text = getString(R.string.please_wait)
        stopFccReceiver()
        try {
            trxCountPOSTrx = FuelTrxCountDao()
            trxCountPOSTrx!!.open()
            trxCountPOSTrx!!.insert(fuelDetails[0])
            trxCountPOSTrx!!.close()
            val mProduitDAO = ProductsDao()
            mProduitDAO.open()
            // hardcode
            mProduitToCheckout = mProduitDAO.getProductById(intentExtrasModel!!.articleID!!.toInt()) //selectionnerByCode("SSP");//selectionnerByIdProduit(13 ); //articleID);
            mProduitDAO.close()
        } catch (Ex: java.lang.Exception) {
            log(TAG, "Error caught in perform final operation: " + Ex.message)
        }
        intentExtrasModel!!.selectedProduct = mProduitToCheckout
        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.hsCode = mProduitToCheckout!!.hs_code
        Log.i(TAG,"fuelVat.enabled:: "+fuelVat.enabled)
        if(fuelVat.enabled) { // vat calculation
            val isInclusive = fuelVat.type == 0
            val taxModel = TaxUtils.calculate(fuelTrxDetails[0].toDouble(),fuelVat.percentage!!.toDouble(),isInclusive)
            fuelTrxDetails[0] = taxModel.totalAmount.toString()
            intentExtrasModel!!.taxModel = taxModel
            intentExtrasModel!!.amount = fuelTrxDetails[0]
            intentExtrasModel!!.mTransaction!!.vatPercentage =  fuelVat.percentage
            intentExtrasModel!!.mTransaction!!.vatType =  fuelVat.type
            intentExtrasModel!!.mTransaction!!.vatAmount = taxModel.taxAmount.toString()
            intentExtrasModel!!.mTransaction!!.netAmount = taxModel.netAmount.toString()
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
        }

        val trxAmount = fuelTrxDetails[0]
        val preauthAmount = intentExtrasModel!!.preAuthAmount!!

        log(TAG,"PERFORM FINAL OPERATION, TRX Amount : $trxAmount --- PreAuth : $preauthAmount")
        log(TAG,"PERFORM FINAL OPERATION, INTENT EXTRA MODEL: ${Gson().toJson(intentExtrasModel)}")

        if(fusionExist){
            sendClearTransaction(pumpNumber,""+intentExtrasModel!!.mTransaction!!.sequenceController)                   //sending clear trx message to fusion
        }
        clearTrxHistoryInSp()

        if (intentExtrasModel!!.typePay == VISA_VALUE && fuelTrxDetails[0].toDouble() < intentExtrasModel!!.preAuthAmount!!.toDouble()) {
            mBinding.progresMsg.text = getString(R.string.refund_request_under_process)
            val refundAmount = intentExtrasModel!!.preAuthAmount!!.toDouble() - fuelTrxDetails[0].toDouble()
            val finalAmount = refundAmount.toLong() * 100
            intentExtrasModel!!.bankRequestType = REFUND_REQUEST
            intentExtrasModel!!.refundAmount = finalAmount.toString()
            intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - go to Bank Screen - $finalAmount"))
            //intentExtrasModel!!.refundAmount = refundAmount.toString()
            gotoBankActivity()
        }
        else if (intentExtrasModel!!.typePay == CARD_VALUE && fuelTrxDetails[0].toDouble() <= intentExtrasModel!!.preAuthAmount!!.toDouble()){
            mBinding.progresMsg.text = getString(R.string.please_wait)

            var refundAmount = intentExtrasModel!!.preAuthAmount!!.toDouble() - fuelTrxDetails[0].toDouble()
            //val finalAmount = refundAmount//.toLong() * 100
            if(referenceModel!!.IMPLEMENT_DISCOUNT!! && intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.REBATE_DISCOUNT && intentExtrasModel!!.mTransaction!!.isDiscountTransaction == 1)
            {
                intentExtrasModel!!.mTransaction!!.discountAmount = fuelTrxDetails[0]
                intentExtrasModel!!.discountAmount = fuelTrxDetails[0]
                intentExtrasModel!!.mTransaction!!.discountAmount = intentExtrasModel!!.discountAmount
                refundAmount = 0.0
            }
            else if(referenceModel!!.IMPLEMENT_DISCOUNT!! /*&& intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.INSTANT_DISCOUNT*/)
            {
                getDiscountAmount()
                if(intentExtrasModel!!.isDiscountTransaction!!)
                {
                    refundAmount += intentExtrasModel!!.discountAmount!!.toDouble()
                }
            }
            if (intentExtrasModel!!.mTransaction != null) {
                intentExtrasModel!!.mTransaction!!.discountAmount = intentExtrasModel!!.discountAmount
                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            }

            if(refundAmount > 0) {
                mBinding.progresMsg.text = getString(R.string.refund_request_under_process)

                intentExtrasModel!!.fleetCardRequestType = REFUND_REQUEST
                intentExtrasModel!!.refundAmount = refundAmount.toString()
                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - go to Debit amount Screen - $refundAmount"))
                log(TAG,"GOTO FLEET CARD ACTIVITY 2")
                gotoFleetCardActivity()
            }
            else
            {
                gotoNextActivity()
            }
        }
        else {
            gotoNextActivity()
        }
        //}
    }
    fun getDiscountAmount():Double
    {
        var discountAmount = 0.0
        val trxAmount = fuelTrxDetails[0].toDouble()
        val qty = fpVolume.toDouble()
        val cardType = intentExtrasModel!!.cardType

        if(!prefs.getDiscountDetails().isNullOrEmpty())
        {
            for(discount in prefs.getDiscountDetails()!!) {
                if (((discount.discount_for == null || discount.discount_for == 0 || discount.discount_for == AppConstant.ALL_CARDS)
                            || (discount.discount_for == AppConstant.ALL_PREPAID_CARDS && cardType == AppConstant.PREPAID_CARD)
                            || (discount.discount_for == AppConstant.ALL_POSTPAID_CARDS && cardType == AppConstant.POSTPAID_CARD) //Validate card type,if any one condition true go to next validation
                            )
                    && (discount.discount_id == intentExtrasModel!!.cardDiscountId)   // Check is Discount applicable for this card
                    && (isDateWithinRange(discount.date_from!!, discount.date_to!!))    // Check Current date is within the discount applicable date range
                    && (isTimeWithinRange(discount.time_from!!, discount.time_to!!))     // Check Current time is within the discount applicable time
                    && ((discount.date_time_range_type == DISCOUNT_ALL_DAY)    // Check is Discount available for All Day  or
                            || (discount.date_time_range_type == DISCOUNT_SPECIFIC_DAY
                            && isCurrentDayAvailable(discount.specific_day!!))) //If Discount applicable for specific day check specific day value is 1
                ) {
                    for (scheme in discount.scheme) {
                        //log(TAG,"Scheme :: "+gson.toJson(scheme))
                        if ((scheme.fbs_prod_id == intentExtrasModel!!.articleID!!.toInt()) // Check selected product available on discount scheme
                            && (qty >= scheme.qty_range_from!!)
                            && ((scheme.is_unlimited_range!!) || (qty <= scheme.qty_range_to!!)))  // Qty should be within the range or unlimited range
                        {

                            if (scheme.discount_type == PERCENTAGE) {
                                log(TAG,"Discount Type: "+scheme.discount_type)
                                log(TAG,"Discount Value: "+scheme.discount_value)
                                log(TAG, "trxAmount : $trxAmount")
                                discountAmount = (scheme.discount_value!!.toDouble() / 100) * trxAmount
                                intentExtrasModel!!.isDiscountTransaction = true
                                if(intentExtrasModel!!.mTransaction != null)
                                {
                                   intentExtrasModel!!.mTransaction!!.discountPercentage = scheme.discount_value
                                }
                                log(TAG, "Discount Amount: $discountAmount")
                                break
                            } else if (scheme.discount_type == FIXED) {
                                log(TAG,"Discount Type: "+scheme.discount_type)
                                log(TAG,"Discount Value: "+scheme.discount_value)
                                log(TAG, "trxAmount : $trxAmount")
                                discountAmount =  scheme.discount_value!!.toDouble() * qty
                                intentExtrasModel!!.isDiscountTransaction =true
                                if(intentExtrasModel!!.mTransaction != null)
                                {
                                    val discountPercentage=(scheme.discount_value.toDouble() / trxAmount) * 100
                                    intentExtrasModel!!.mTransaction!!.discountPercentage = discountPercentage.toString()
                                }
                                log(TAG, "Discount Amount: $discountAmount")
                                break
                            }
                            if(discountAmount > trxAmount)
                            {
                                discountAmount = trxAmount
                                log(TAG, "Discount Amount greater than trx amount: $discountAmount")
                            }
                        }
                    }
                }
            }
        }
        intentExtrasModel!!.discountAmount  = stringFormatDouble(discountAmount)
        return discountAmount
    }
    private fun gotoNextActivity() {
        //intent = if(intentExtrasModel!!.typePay == AppConstant.CARD_VALUE ) {
        //    Intent(this, DebitCardLimitsActivity::class.java)
        //} else {
        //}

        if(::fuelTrxDetails.isInitialized && fuelTrxDetails != null)
        {
            intentExtrasModel!!.amount = fuelTrxDetails[0]
            intentExtrasModel!!.fuelProductModel = FuelProductModel("", fuelTrxDetails[1], fuelTrxDetails[2])
            //intentExtrasModel!!.mVolumeFUSION = fpVolume.toDouble()
            intentExtrasModel!!.mVolumeFUSION = intentExtrasModel!!.mTransaction!!.quantite
            intentExtrasModel!!.selectedProduct = mProduitToCheckout
        }

        stopAllReciversAndTimers()

        log(TAG, "Fuel Transaction create successfully! Printing the receipt.")
        log(TAG,"GOING TO TicketActivity")
        val intent = Intent(this, TicketActivity::class.java)
        intent.putExtra(INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
       // finish()
    }

    private fun isProductAvailable(selectedCode: String?): Boolean {
        var isAvailable = "false"
        for (mProduct in productList) {
            //log(TAG, "mProduct:::: " + Gson().toJson(mProduct))
            val productCode: String = mProduct.code!!
            val isProductAvailable_: String = mProduct.isAvailable!!
            if (selectedCode != null && productCode != null && selectedCode.contains(productCode)) {
                isAvailable = isProductAvailable_
            }
        }
        return isAvailable.equals("true", ignoreCase = true)
    }

    private fun gotoNextStep() {
        startConnectivityChecker()
        intentExtrasModel!!.articleID = selectedNozzleModel!!.Article.toString()
        fccProductId = selectedNozzleModel!!.fcc_prod_id.toString()

        getProductModel(intentExtrasModel!!.articleID!!.toInt())

        prefs.saveNozzleModel(selectedNozzleModel)
        prefs.saveIntentModel(intentExtrasModel)
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Nozzle FCC product ID- "+selectedNozzleModel!!.fcc_prod_id))


        if ((fusionExist || fuelpos.isExist) && fccProductId != null) {
            log(TAG,"TYPE PAY : ${intentExtrasModel!!.typePay!!}")
            if (intentExtrasModel!!.typePay!! == AppConstant.RFID_VALUE) {
                validateOnlineTransaction()
            }
            else if (intentExtrasModel!!.typePay!! == CARD_VALUE || intentExtrasModel!!.typePay!! == AppConstant.RFID_VALUE)
            {
                if (intentExtrasModel!!.verificationType == 2) {
                    if (intentExtrasModel!!.isOnlinePayment!!) validateOnlineTransaction() else validateTransaction()
                } else if (isProductAvailable(selectedNozzleModel!!.prdt_SKU)) {
                    if (intentExtrasModel!!.isOnlinePayment!!) validateOnlineTransaction() else validateTransaction()
                } else {
                    if (intentExtrasModel!!.verificationType == 2) {
                        val intent = Intent()
                        setResult(AppConstant.RFID_RESULT_CODE, intent)
                        //finish()
                    } else {
                        showLoading(false)
                    }
                    showDialog( selectedNozzleModel!!.Name.toString() + " " + getString(R.string.restricted), getString(R.string.product_not_available))
                    showLoading(false)
                }
            } else {
                if (isProductAvailable(selectedNozzleModel!!.prdt_SKU)) {
                    executeFccCommand()
                } else {
                    showDialog( selectedNozzleModel!!.Name.toString() + " " + getString(R.string.restricted),getString(R.string.product_not_available))
                    showLoading(false)
                }
            }
        }
        else {
            stopAllReciversAndTimers()
            log(TAG,"GOING TO OfflineTransactionListActivity")
            val i3 = Intent(this, OfflineTransactionListActivity::class.java)
            i3.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel!!)
            startActivity(i3)
            finish()
        }
    }

    var fccRequest: String = ""

    private fun executeFccCommand() {
        try {
            val taxModel = intentExtrasModel!!.taxModel
            if((fusionExist || fuelpos.isExist) && fccProductId != null){
                val dec = DecimalFormat("###.#",localeFormat)
                var authAmount = intentExtrasModel!!.amount
                val maxRefillAmount = referenceModel!!.terminal!!.maxRefillAmount!!.toString() + ""
                if (intentExtrasModel!!.amount == null || intentExtrasModel!!.amount == "null" || intentExtrasModel!!.amount!!.isEmpty()) { //full tank
                    try {
                        /*if ( intentExtrasModel!!.typePay!!.contains(AppConstant.CARD_VALUE)) { // fleet card
                            authAmount =  intentExtrasModel!!.dailyAmount!!
                        }
                        else */if(intentExtrasModel!!.typePay!! == CASH_VALUE) { //cash full tank is max station limit
                            authAmount =  maxRefillAmount
                        }
                    } catch (e: java.lang.Exception) {
                        e.printStackTrace()
                        log(TAG, e.message + " " + e.stackTrace.toString())
                    }
                }

                Log.e(TAG,"######## ${intentExtrasModel!!.preset_amount!!}")

                var paymentTypeId = getFuelPosModePaymentId(intentExtrasModel!!.typePay)+""
                var paymentType = getFuelPosModePayment(intentExtrasModel!!.typePay)+""


                if(!intentExtrasModel!!.preset_amount.isNullOrEmpty() && authAmount!!.toDouble() > intentExtrasModel!!.preset_amount!!.toDouble() ){ //if auth amount is greater than MOP setting mop preset amount
                    authAmount = intentExtrasModel!!.preset_amount
                }

                if(paymentTypeId == CASH_VALUE){ /// if mode pay is cash not max preset amount will not be there (added for fuelup)
                    authAmount = intentExtrasModel!!.amount
                }

                if(fuelVat.enabled && taxModel!= null && fuelVat.type == TaxType.EXCLUSIVE /*exclusive*/){
                    authAmount = taxModel.netAmount.toString()
                }

                if(maxRefillAmount.toDouble() < authAmount!!.toDouble())
                    authAmount = maxRefillAmount

                val amount = BigDecimal(authAmount)
                authAmount = dec.format(amount)

                tokenTRX = getUniqueToken()
                log(TAG,"tokenTRX :: $tokenTRX")
                intentExtrasModel!!.tokenTRX = tokenTRX
                intentExtrasModel!!.mTransaction!!.fccReleaseToken = tokenTRX
                prefs.saveIntentModel(intentExtrasModel)
                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)

                //region checking sub payments as per SOL requirements
                if(intentExtrasModel!!.cardType == AppConstant.POSTPAID_CARD || intentExtrasModel!!.cardType == AppConstant.PREPAID_CARD) {
                    Log.e(TAG,"intentExtrasModel!!.modePaymentModel = ${intentExtrasModel!!.modePaymentModel} intentExtrasModel!!.modePaymentModel!!.sub_payments = ${Gson().toJson(intentExtrasModel!!.modePaymentModel!!.sub_payments)}")
                    if(intentExtrasModel != null && intentExtrasModel!!.modePaymentModel!=null && !intentExtrasModel!!.modePaymentModel!!.sub_payments.isNullOrEmpty()){
                        val subPayments = intentExtrasModel!!.modePaymentModel!!.sub_payments!!
                        if(intentExtrasModel!!.cardType == AppConstant.POSTPAID_CARD){
                            for(subPayment in subPayments){
                                if(subPayment.card_type == AppConstant.POSTPAID_CARD){
                                    paymentTypeId = ""+subPayment.fcc_payment_id
                                    paymentType = ""+subPayment.payment_name
                                }
                                break
                            }
                        }
                        else if(intentExtrasModel!!.cardType == AppConstant.PREPAID_CARD){
                            for(subPayment in subPayments){
                                if(subPayment.card_type == AppConstant.PREPAID_CARD){
                                    paymentTypeId = ""+subPayment.fcc_payment_id
                                    paymentType = ""+subPayment.payment_name
                                }
                                break
                            }
                        }
                    }
                }
                //endregion
                if(fusionExist){
                    val priceDao = PriceDao()
                    priceDao.open()
                    var productPrice = priceDao.getProductPriceById(fccProductId)
                    priceDao.close()

                    if(isQtySelected){

                        if(fusionPriceConfiguration.isNotEmpty()){   ///getting fuel price from fusion configuration
                            for(fuelPump in fusionPriceConfiguration){
                                if(fuelPump.pumpId == intentExtrasModel!!.mPumpNumber!!.toString() && fuelPump.product!=null){

                                    for(product in fuelPump.product!!){
                                        if(product.productNumber == fccProductId){
                                            if(productPrice!=null){
                                                productPrice.unitPrice = product.fuelPrice!![0].price!!.toDouble()
                                            } else  {
                                                productPrice = PriceModel(
                                                    datedebut = "",
                                                    datefin = "",
                                                    id = -1,
                                                    idproduit = intentExtrasModel!!.articleID!!.toInt(),
                                                    fcc_prod_id = fccProductId,
                                                    unitPrice = product.fuelPrice!![0].price!!.toDouble())
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        try {
                            //val qty = intentExtrasModel!!.amount!!.toDouble() / productPrice!!.unitPrice
                            if(enteredQty!=0.0) {

                                //val qtyAmount = intentExtrasModel!!.amount!!.toDouble() / productPrice!!.unitPrice
                                val qtyAmount = enteredQty * productPrice!!.unitPrice

                                if(!intentExtrasModel!!.preset_amount.isNullOrEmpty() &&  (qtyAmount > intentExtrasModel!!.preset_amount!!.toDouble() || qtyAmount > maxRefillAmount.toDouble()) &&  paymentTypeId != AppConstant.CASH_VALUE)
                                {
                                    showToast(getString(R.string.amount_is_greater_than_max_preset))
                                    showEnterQtyDialog()
                                }
                                else{
                                    authAmount = qtyAmount.toInt().toString()//String.format(Locale.US,"%." + decimal + "f", qtyAmount).replace(".", "")
                                    intentExtrasModel!!.amount = String.format(Locale.US,"%." + decimal + "f", qtyAmount).replace(".", "")                              //commented because getting non 0 value in amount of trx instead of preAuthAmount
                                    //intentExtrasModel!!.preAuthAmount = String.format(Locale.US,"%." + decimal + "f", qtyAmount).replace(".", "")                                              //suggestion to resolve
                                    fccRequest = FusionService.getAuthRequest(intentExtrasModel!!.mPumpNumber!!.toString(), fccProductId + "", authAmount+"",tokenTRX+"")

                                }
                            }
                            else {
                                showEnterQtyDialog()
                            }
                        } catch (e: Exception) {
                            e.printStackTrace()
                            showToast(getString(R.string.product_price_error)+" : ${e.message}")
                        }
                    }
                    else {
//                      if(app.rht.petrolcard.BuildConfig.DEBUG) //Test Looping Issue
//                        {
//                            tokenTRX = "061668674229"
//                        }
                        log(TAG,"tokenTRX on auth request :: $tokenTRX")
                        fccRequest = FusionService.getAuthRequest(intentExtrasModel!!.mPumpNumber!!.toString(), fccProductId + "", authAmount,tokenTRX+"")
                    }
                    sendIfsfMessage(fccRequest)

                }
                else if(fuelpos.isExist){
                    try {
                        fuelTrxCountDao = FuelTrxCountDao()
                        fuelTrxCountDao!!.open()
                        fuelTrxCount = fuelTrxCountDao!!.get() + 1
                        fuelTrxCountDao!!.close()
                    } catch (e:Exception) {
                        e.message?.let { log(TAG, it) }
                    }

                    //tokenTRX = Utils.padZeroRight("${mTerminal!!.terminalId }", 4) + Utils.padZeroLEFT("$fuelTrxCount", 8)


                    /* if(!FuelPosService.isConnected()){
                        FuelPosService.restartFuelPosEpr()
                    }*/

                    val fuelPosCardId = selectedNozzleModel!!.CardId



                    if(isQtySelected) {
                        val priceDao = PriceDao()
                        priceDao.open()
                        val productPrice = priceDao.getProductPriceById(fccProductId)
                        fuelTrxCountDao!!.close()

                        try {
                            //val qtyAmount = intentExtrasModel!!.amount!!.toDouble()/productPrice!!.unitPrice
                            if(enteredQty !=0.0){
                                val qtyAmount = enteredQty * productPrice!!.unitPrice
                                if(intentExtrasModel!!.preset_amount!=null &&  (qtyAmount > intentExtrasModel!!.preset_amount!!.toDouble() || qtyAmount > maxRefillAmount.toDouble()) &&  paymentTypeId != CASH_VALUE)
                                {
                                    showToast(getString(R.string.amount_is_greater_than_max_preset))
                                    showEnterQtyDialog()
                                }
                                else {
                                    authAmount = String.format(Locale.US,"%." + decimal + "f", qtyAmount).replace(".", "").replace(",", "")
                                    intentExtrasModel!!.amount = String.format(Locale.US,"%." + decimal + "f", qtyAmount).replace(".", "").replace(",", "")                           //commented because getting non 0 value in amount of trx instead of preAuthAmount
                                    //intentExtrasModel!!.preAuthAmount = String.format(Locale.US,"%." + decimal + "f", qtyAmount).replace(".", "").replace(",", "")                    //suggestion to resolve

                            val fccMopId = if(intentExtrasModel!!.modePaymentModel!=null){
                                ""+intentExtrasModel!!.modePaymentModel!!.fcc_payment_id
                            } else {
                                ""+getFuelPosModePaymentId(intentExtrasModel!!.typePay)
                            }
                            val mopName = if(intentExtrasModel!!.modePaymentModel!=null){
                                ""+intentExtrasModel!!.modePaymentModel!!.payment_name
                            } else {
                                ""+getFuelPosModePayment(intentExtrasModel!!.typePay)
                            }

                            pumpReleaseRequest = PumpReleaseRequest(
                                tokenTRX + "",
                                intentExtrasModel!!.mPumpNumber.toString() + "",
                                "$fuelPosCardId",
                                authAmount,
                                prefs.currency.replace(" ",""),
                                getIpAddress() + "",
                                fuelpos.tcpListener.toString() + "",
                                intentExtrasModel!!.mPinNumberAttendant + "",
                                fccMopId+"",
                                mopName+"",
                                "")

                                    FuelPosService.sendRequest(FuelPosCommands.logOn(fuelpos.tcpUser!!, fuelpos.tcpPass!!))
                                }


                            }
                            else {
                                showEnterQtyDialog()
                            }
                        } catch (e:Exception) {
                            e.printStackTrace()
                            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                            //  mViewModel.generateLogs(e.message!!,0)
                            showToast(getString(R.string.product_price_error)+" : ${e.message}")
                        }
                        showToast(getString(R.string.fuel_dispense_by_qty_not_enabled))
                        showLoading(false)
                    }
                    else {
                        authAmount = String.format(Locale.US,"%." + decimal + "f", intentExtrasModel!!.amount!!.toDouble()).replace(".", "").replace(",", "")

                        val fccMopId = if(intentExtrasModel!!.modePaymentModel!=null){
                            ""+intentExtrasModel!!.modePaymentModel!!.fcc_payment_id
                        } else {
                            ""+getFuelPosModePaymentId(intentExtrasModel!!.typePay)
                        }
                        val mopName = if(intentExtrasModel!!.modePaymentModel!=null){
                            ""+intentExtrasModel!!.modePaymentModel!!.payment_name
                        } else {
                            ""+getFuelPosModePayment(intentExtrasModel!!.typePay)
                        }

                        pumpReleaseRequest = PumpReleaseRequest(
                            tokenTRX + "",
                            intentExtrasModel!!.mPumpNumber.toString() + "",
                            "$fuelPosCardId",
                            authAmount,
                            prefs.currency.replace(" ",""),
                            getIpAddress() + "",
                            fuelpos.tcpListener.toString() + "",
                            intentExtrasModel!!.mPinNumberAttendant + "",
                            fccMopId+"",
                            mopName+"",
                            "")
                        FuelPosService.sendRequest(FuelPosCommands.logOn(fuelpos.tcpUser!!, fuelpos.tcpPass!!)
                        )
                    }

                    Log.i(TAG, "RELEASE REQUEST: ${Gson().toJson(pumpReleaseRequest)}")
                    Log.i(TAG,"mAmount ::: $authAmount")
                }
            }
            else {
                log(TAG,"GOING TO OfflineTransactionListActivity")
                val i3 = Intent(this, OfflineTransactionListActivity::class.java)
                i3.putExtra(INTENT_EXTRAS_MODEL, intentExtrasModel)
                stopAllReciversAndTimers()
                startActivity(i3)
                finish()
            }

        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            //showFccConnectionLayout(true)
        }
    }

    //region fuelPOS
    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action
            log(TAG, "FUELPOS ACTION $action")
            var message: String? = ""
            when (action) {
                ACTION_FUEL_POS_REPLY -> {
                    message = intent.getStringExtra(FUEL_POS_REPLY)
                    parseFuelPosMessage(message)
                }
                ACTION_POS_MESSAGE -> {
                    message = intent.getStringExtra(POS_MESSAGE)
                    //showPumpStatusMessage(message)
                }
                ACTION_FUEL_POS_MESSAGE -> {
                    message = intent.getStringExtra(FUEL_POS_MESSAGE)
                    parseFuelPosMessage(message)
                }
                ACTION_FUEL_POS_CLIENT_STATE ->
                    if (intent.hasExtra(FUEL_POS_CLIENT_STATE)) {
                        val connectionState = intent.getStringExtra(FUEL_POS_CLIENT_STATE)
                        when (connectionState) {
                            FuelPosClientState.CONNECTED -> {
                                log(TAG, "Fuel POS connected")
                                showToast(getString(R.string.fuelpos_connected))
                                showFccConnectionLayout(false)
                                showPumpDisconnected(false)
                                eprPumpStateChecker.start()
                                stopfuelPosConnectionChecker() //newly added to stop attempting connection with FuelPos Tue 23 Aug 2022
                            }
                            FuelPosClientState.CONNECTING -> {
                                log(TAG, "Fuel POS connecting")
                            }
                            FuelPosClientState.DISCONNECTED -> {
                                log(TAG, "Fuel POS disconnected")
                                showToast(getString(R.string.fuelpos_disconnected))
                                eprPumpStateChecker.cancel()
                                stopConnectivityChecker()
                                showFccConnectionLayout(true)
                                startFuelPosConnectionChecker()     //newly added to attempt connection with FuelPos Tue 23 Aug 2022
                                isFuelPosLogOn = false
                            }
                            else ->{
                                showFccConnectionLayout(true)
                            }
                        }
                    }
                    else if (intent.hasExtra(FUEL_POS_SERVER_STATE)) {
                        val connectionState = intent.getStringExtra(FUEL_POS_SERVER_STATE)
                        when (connectionState) {
                            FuelPosServerState.STARTED -> {
                                log(TAG, "Fuel POS listener started")
                            }
                            FuelPosServerState.STOPPED -> {
                                log(TAG, "Fuel POS listener stopped")
                            }
                            FuelPosServerState.CLIENT_CONNECTED -> {
                                log(TAG, "Fuel POS connected to listener")
                            }
                            FuelPosServerState.CLIENT_DISCONNECTED -> {
                                log(TAG, "Fuel POS disconnected to listener")
                            }
                        }
                    }
            }
        }
    }
    private fun startFuelPosReceiver() {
        if (!FuelPosService.isRunning(this)) FuelPosService.start(this)
        val filter = IntentFilter()
        filter.addAction(ACTION_FUEL_POS_REPLY)
        filter.addAction(ACTION_FUEL_POS_MESSAGE)
        filter.addAction(ACTION_FUEL_POS_CLIENT_STATE)
        filter.addAction(ACTION_POS_MESSAGE)
        registerReceiver(fuelPosReceiver, filter)
        log(TAG, "FuelPOS receiver started")
        //FuelPosService.startFuelPosStateChecker()
    }
    private fun stopFuelPosReceiver() {
        try {
            unregisterReceiver(fuelPosReceiver)
            log(TAG, "FuelPos receiver stopped")
            //FuelPosService.stopFuelPosStateChecker()
        } catch (e: java.lang.Exception) {

        }
    }

    private var isFuelPosReleased = false

    //private val fuelPosTrxState = arrayOf(false,false)

    private val eprPumpStateChecker = object : CountDownTimer(8000,1000){
        override fun onTick(p0: Long) {
        }

        override fun onFinish() {
            FuelPosService.sendRequest(FuelPosCommands.forecourtStatusRequest()) //sending pump request
            log(TAG,"Sending pump state request")
            this.start()
        }
    }

    private var pumpReleaseRequest: PumpReleaseRequest? = null
    private var transactionDataReceived = false
    private var pumpNotReleased = false
    private var pumpNotReleaseMessage = ""

    private fun parseFuelPosMessage(message: String?) {
        log(TAG, "FUEL POS Message:: $message")
        if (!fuelingStates[0] && pumpReleaseRequest != null) {
            if(pumpReleaseRequest!=null){
                fuelingStates[0] = true
                prefs.saveFuellingStates(fuelingStates)
                val command = FuelPosCommands.releasePump(pumpReleaseRequest!!)
                log(TAG, command)
                FuelPosService.sendRequest(command)
                //FuelPosService.sendRequest("Test message to check unstable network connectivity");
            } else {
                showToast(getString(R.string.pump_release_request_null))
            }
        }
        else if (message!!.contains(LogonResponse.ACCESS_DENIED + " ")) {
            showToast(getString(R.string.invalid_fuelpos_credentials))
            //cancelTransaction() // as per johann's suggestion added cancel transaction here

            pumpNotReleaseMessage = getString(R.string.invalid_fuelpos_credentials)
            val trxData  = TransactionData() // creating sample trx data to show in receipt becuase in this case we are not receiving transaction data
            trxData.pump = pumpNumber
            trxData.unitPrice = "0"
            trxData.totalAmount = "0"
            trxData.quantity = "0"
            showCustomerPrintDialog(trxData)
        }
        else if (message.contains(FuelPosReply.PUMP_RELEASED)) {
            val item = FuelPosReply.getPumpReleaseMessage(message)
            if (item!!.trxToken == tokenTRX && item.completionCode == "0") {
                fuelingStates[1] = true
                mBinding.progresMsg.text = getString(R.string.pump_successfully_released_start_pumping_gas).trimIndent()
                isFuelPosReleased = true
                //FuelPosService.startPumpStateChecker();
                eprPumpStateChecker.start()
            }
            else {
                showToast(CompletionCode.getMessage(item.completionCode))
                pumpNotReleased = true  // added this flag to check in transaction data received
                pumpNotReleaseMessage = getFuelPosPumpResponse(item.completionCode)
                val code = item.completionCode.toInt()
                log(TAG, "FUELPOS PUMP RESPONSE ${getFuelPosPumpResponse(item.completionCode)}")
                //if(code == CompletionCode.PUMP_BUSY || code == CompletionCode.PUMP_NOT_CONFIGURED || code == CompletionCode.INVALID_MESSAGE){
                if(code != CompletionCode.OK){            //added to fix pump release error https://app.clickup.com/t/302tkyu
                    if(item.completionCode != "${CompletionCode.OK}" || pumpNotReleased) {
                        //cancelTransaction() // commented and added in dialog and print task
                        val trxData  = TransactionData() // creating sample trx data to show in receipt becase in this case we are not receiving transaction data
                        trxData.pump = pumpNumber
                        trxData.unitPrice = "0"
                        trxData.totalAmount = "0"
                        trxData.quantity = "0"
                        showCustomerPrintDialog(trxData)
                    }
                    else {
                        showToast(CompletionCode.getMessage(item.completionCode))
                    }
                }
                /*else {
                    showLoading(false)
                    finish()
                }*/

            }
        }
        else if (message.contains(FuelPosReply.DELETED)) {
            log(TAG, getString(R.string.transaction_deleted))
        }
        else if (message.contains(FuelPosReply.FORECOURT_STATUS_RESPONSE) ) {
            log(TAG, "EPR Pump state: $message")
            val item = FuelPosReply.getForecourtStatus(message)
            if(item.completionCode == "${CompletionCode.OK}"){
                for(state in item.pumpsStatus){
                    if (state.pump == pumpNumber) {
                        var stateMessage = EprPumpState.getEprPumpState(state.state)
                        log(TAG,"$$$$ State Message $stateMessage")
                        if(state.state == EprPumpState.RESERVED && !fuelingStates[1] ){
                            mBinding.progresMsg.text = getString(R.string.pump_successfully_released_start_pumping_gas).trimIndent()
                            fuelingStates[1] = true
                            prefs.saveFuellingStates(fuelingStates)
                        }
                        else if((state.state == EprPumpState.SAFETY_IDLE /*&& !fuelingStates[2]*/) || (state.state == EprPumpState.AUTO_RELEASE /*&& !fuelingStates[2]*/ )){
                            log(TAG,"GOING TO (state.state == EprPumpState.SAFETY_IDLE && !fuelingStates[2]) || state.state == EprPumpState.AUTO_RELEASE && !fuelingStates[2] ")
                          //  eprPumpStateChecker.cancel() //Need to Check why we need to cancel
                            stateMessage = getPumpStatusMessage(state.state)
                        }
                        else if(state.state == EprPumpState.FILLING){
                            fuelingStates[2] = true
                            stateMessage = getPumpStatusMessage(state.state)
                            prefs.saveFuellingStates(fuelingStates)
                        }
                        else if((state.state == EprPumpState.IDLE || state.state == EprPumpState.SAFETY_IDLE)
                            && fuelingStates[1] && fuelingStates[2]){
                            stateMessage = getString(R.string.please_wait)
                            eprPumpStateChecker.cancel()
                            startTransactionDataMessageChecker()   // newly added to check wait for transaction data because in meru some of terminal was not received transaction data from fuelPOS
                            // this is just added to store disuputed transaction in case if transaction data not received
                        }
                        else if(state.state == EprPumpState.AUTO_RELEASE
                            && fuelingStates[1] && fuelingStates[2]){
                            stateMessage = getString(R.string.please_wait)
                            eprPumpStateChecker.cancel()
                            startTransactionDataMessageChecker()   // newly added to check wait for transaction data because in meru some of terminal was not received transaction data from fuelPOS
                            // this is just added to store disuputed transaction in case if transaction data not received
                        }
                        else if(state.state == EprPumpState.RELEASED){
                            stateMessage = getPumpStatusMessage(EprPumpState.RELEASED)
                          //  eprPumpStateChecker.cancel() //Need to Check why we need to cancel
                        }
                        else
                        {
                            stateMessage = getPumpStatusMessage(state.state)
                        }

                        mBinding.progresMsg.text = stateMessage
                    }
                }
            }
        }
        else if (message.contains(FuelPosReply.TRANSACTION_DATA)) {
            val item = FuelPosReply.getTransactionDataMessage(message)

            stopTransactionDataMessageChecker() // newly added to check wait for transaction data because in meru some of terminal was not received transaction data from fuelPOS
            // this is just added to store disuputed transaction in case if transaction data not received
            if (tokenTRX == item!!.trxToken && !transactionDataReceived && isNozzleClicked) { //&& !transactionDataReceived added to enter only once
                transactionDataReceived = true // added to enter only once
                eprPumpStateChecker.cancel()

                val totalAmount = toDecimalValue(item.totalAmount, decimal)
                //FuelPosService.stopPumpStateChecker();
                if (item.completionCode == "0" && item.trxToken == tokenTRX && !pumpNotReleased) {
                    performFuelPosNextStep(item)
                }
                if (item.trxToken == tokenTRX && (item.totalAmount.toDouble() == 0.0 )){ // added after bunsengs suggestion to cancel transaction on 0 amount
                    showCustomerPrintDialog(item) // should update 0 amount on local db
                }
                else {
                    //val command = FuelPosCommands.deleteTransaction(item.trxToken) // new addition as per bunseng's request
                    //FuelPosService.sendReplyToClient(command, fuelpos.ipAddress!!) // new addition as per bunseng's request
                    //if(item.completionCode == "${CompletionCode.NOZZLE_LIFT_TIMEOUT}"){ // commented as per bunseng's request to cancel transaction in case on any error or nozzle lift timeout

                    val mProduitDAO = ProductsDao()
                    mProduitDAO.open()
                    productModel = mProduitDAO.getProductById(intentExtrasModel!!.articleID!!.toInt())!!
                    mProduitDAO.close()

                    if(item.completionCode != "${CompletionCode.OK}" || pumpNotReleased) {
                        //cancelTransaction() // commented and added in dialog and print task
                        showCustomerPrintDialog(item)
                    }
                    else {
                        showToast(CompletionCode.getMessage(item.completionCode))
                    }
                    //showLoading(false);
                }
            }
            else {
                log(TAG,"Sending delete transaction form Product selection page")
                val command = FuelPosCommands.deleteTransaction(item.trxToken) // new addition as per bunseng's request
                FuelPosService.sendReplyToClient(command, fuelpos.ipAddress!!) // new addition as per bunseng's request
                //storeFuelPosPendingTrxRecord(item)
            }
        }
        else if(message.contains("230 ")){
            log(TAG,"Logon success message")
            isFuelPosLogOn = true
        }
        else {
          //  showToast(getString(R.string.invalid_message_received_from_fuelpos)+" $message") // canceling transaction if we got invalid messages from fuelpos

            if(!isFuelPosLogOn)
                showToast(getString(R.string.invalid_fuelpos_credentials))
            else {
                showToast(getString(R.string.invalid_message_received_from_fuelpos) + " $message") // canceling transaction if we got invalid messages from fuelpos
                try{
                    val msgToSend = "Request sent: $fccRequest\nFuelPOS message: $message"
                    emailInvalidMessage(msgToSend)
                } catch (e:Exception){}
            }
            pumpNotReleaseMessage = getString(R.string.configuration_error)
            val trxData  = TransactionData() // creating sample trx data to show in receipt becuase in this case we are not receiving transaction data
            trxData.pump = pumpNumber
            trxData.unitPrice = "0"
            trxData.totalAmount = "0"
            trxData.quantity = "0"
            showCustomerPrintDialog(trxData)

        }
    }

    private fun checkOldFuelPosTrx(message : String){
        log(TAG,"OLD TRX DATA RECEIVED")
        val item = FuelPosReply.getTransactionDataMessage(message)

        val command = FuelPosCommands.deleteTransaction(item!!.trxToken) // new addition as per bunseng's request
        FuelPosService.sendReplyToClient(command, fuelpos.ipAddress!!) // new addition as per bunseng's request

        val totalAmount = toDecimalValue(item.totalAmount, decimal)
        //FuelPosService.stopPumpStateChecker();




    }

    private fun emailInvalidMessage(message:String){
        val emailAddressArray = extractEmails(MainApp.emailConfig.semicolonSeparatedEmailAddresses)
        val emailIntent = Intent(Intent.ACTION_SEND)
        emailIntent.type = "plain/text"
        emailIntent.putExtra(Intent.EXTRA_EMAIL, emailAddressArray)
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, getString(R.string.app_name)+ " received invalid message")
        emailIntent.putExtra(Intent.EXTRA_TEXT, getString(R.string.email_welcome_note) + message)
        emailIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        startActivity(Intent.createChooser(emailIntent, "Email Error Log"))
    }

    //region fuelpos transaction data message timer
    private var transactionDataMessageTimerRunning = false
    private var transactionDataMessageTimer : CountDownTimer? = null
    private fun startTransactionDataMessageChecker() {
         transactionDataMessageTimer = object : CountDownTimer(
            transactionDataTimeout*1000L,
            1000
        ) { //checking fuelpos transaction data message for two minutes
            override fun onTick(p0: Long) {
                transactionDataMessageTimerRunning = true
                val minutes = p0 / 1000 / 60
                val seconds = (p0 / 1000 % 60)
                //if (BuildConfig.DEBUG) {
                    log(TAG, "Time Remaining: $minutes:$seconds waiting for transaction data")
                //}
            }

            override fun onFinish() {
                log(TAG, "Transaction Data Checker timeout ($transactionDataTimeout sec) finished. storing disputed trx")
                transactionDataMessageTimerRunning = false
                storeDisputedTransaction()
            }

        }
        if(transactionDataMessageTimer != null)
        {
            if(!transactionDataMessageTimerRunning)
                transactionDataMessageTimer!!.start()
        }
    }
    private fun stopTransactionDataMessageChecker() {
        try{
            transactionDataMessageTimer!!.cancel()
        } catch (e:Exception){ }
    }
    private fun storeDisputedTransaction(){
        try {
            log(TAG,"DISPUTED TRX : ${Gson().toJson(intentExtrasModel!!.mTransaction)}")
            intentExtrasModel!!.mTransaction!!.isDisputedTrx = 1
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            intentExtrasModel!!.selectedProduct = mProduitToCheckout
            gotoNextActivity()
        } catch (e: SQLException) {
            e.printStackTrace()
            log(TAG,e.message +" "+e.cause)
        }
    }
    //endregion

    var productModel : ProductModel? = null
    private fun performFuelPosNextStep(item: TransactionData?) {
        if (fuelingStates[1] /*&& fuelingStates[2] && item!!.trxToken == tokenTRX*/ && item!!.totalAmount != "0") {
            mBinding.progresMsg.text = getString(R.string.trx_details_received)
            log(TAG, "TRX Details: $item")

            val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
            val qtyDecimal = receiptSetting.quantityDecimal ?: 2
            val uniteDecimal = receiptSetting.unitPriceDecimal ?: 2
            val totalDecimal = receiptSetting.totalAmountDecimal ?: 2

            /*val totalDecimal = fuelpos.totalAmountDecimal ?: 2
            val qtyDecimal = fuelpos.quantityDecimal ?: 2
            val uniteDecimal = fuelpos.unitPriceDecimal ?: 2*/

            val values = arrayOf(toDecimalValue(item.totalAmount, totalDecimal).toString() + "", item.pump, item.trxToken) //getLatestAmountOfTransaction(String.valueOf(mPumpNumber), fccProductId, message);
            //fpVolume = item.getQuantity();
            fpVolume = toDecimalValue(item.quantity, qtyDecimal).toString() + "" //*********
            if (fuelTrxCommonModel == null)
                fuelTrxCommonModel = FuelTrxCommonModel(item.pump, item.productCode,
                    toDecimalValue(item.totalAmount, totalDecimal).toString() + "",
                    toDecimalValue(item.quantity, qtyDecimal).toString() + "",
                    toDecimalValue(item.unitPrice, uniteDecimal).toString() + ""
                )
            log(TAG, "Final FuelTrx amount Received: " + values[0])
            fuelTrxDetails = values
            intentExtrasModel!!.amount = fuelTrxDetails[0]
            intentExtrasModel!!.fuelTrxCommonModel = fuelTrxCommonModel
            intentExtrasModel!!.fuelProductModel = FuelProductModel("", fuelTrxDetails[1], fuelTrxDetails[2])

            //val command = FuelPosCommands.deleteTransaction(item.trxToken) // new addition as per bunseng's request
            //FuelPosService.sendReplyToClient(command, fuelpos.ipAddress!!) // new addition as per bunseng's request
            if (intentExtrasModel!!.mTransaction != null) {
                intentExtrasModel!!.mTransaction!!.pumpId = item.pump
                intentExtrasModel!!.mTransaction!!.sequenceController = item.trxToken
                intentExtrasModel!!.mTransaction!!.idProduit = intentExtrasModel!!.articleID!!.toInt() //item.productCode.toInt()
                intentExtrasModel!!.mTransaction!!.amount = fuelTrxCommonModel!!.amount!!.toDouble()
                intentExtrasModel!!.mTransaction!!.quantite = fuelTrxCommonModel!!.quantity!!.toDouble() //item.quantity.toDouble()
                intentExtrasModel!!.mTransaction!!.unitPrice =  fuelTrxCommonModel!!.unityPrice!!.toDouble()
                intentExtrasModel!!.mTransaction!!.fccProductId =  fccProductId
                intentExtrasModel!!.mTransaction!!.fposCardId = item.productCode
                intentExtrasModel!!.mTransaction!!.timsSignDetails!!.fuelQtyUnit =  referenceModel!!.FUEL_QTY_UNIT?: "L"
                intentExtrasModel!!.mTransaction!!.fccSaleId = item.trxToken
                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            }
            performFinalOperation(values, pumpNumber.toInt())
        } else {
            log(TAG, "INVALID AMOUNT OR COMPLETION CODE : " + item.toString())
        }
    }
    //endregion

    private fun startIfsfReceiver() {
        val filter = IntentFilter()
        filter.addAction(ACTION_IFSF_READ_DATA)
        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
        registerReceiver(ifsfRecever, filter)
        log(TAG, "IFSF RECEIVER STARTED")
    }
    private fun stopIfsfReceiver() {
        try {
            unregisterReceiver(ifsfRecever)
        } catch (e: Exception) {
            log(TAG, "IFSF Receiver: " + e.message)
        }
        log(TAG, "IFSF RECEIVER STOPPED")
    }
    private var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            //System.out.println("Action: "+action);
            try {
                if (action == ACTION_IFSF_READ_DATA) {
                    val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
                    val bytes = intent.getByteArrayExtra(IFSF_BYTE_MESSAGE)
                  log(TAG, "Message: $msg")
                    /* when (fusionRequestStatus) {
                       STOP_FUEL_POINT -> {
                            startFuelPoint()
                        }
                        START_FUEL_POINT -> {
                            if(cancelFusionTrxTimeout) {
                                cancelTransaction()
                            }
                            else {
                                // start command manually
                            }
                        }
                        else -> {
                            performNextStep(msg!!)
                        }
                    }*/
                    performNextStep(msg!!)
                }
                if (action == ACTION_IFSF_CONNECTION_STATE) {
                    val msg = intent.getStringExtra(IFSF_CONNECTION_STATE)
                    log(TAG, "FUSION CONNECTION State: $msg")
                    log(TAG, "Received: $msg")
                    if(msg!!.contains("Disconnect")) {
                        stopConnectivityChecker()
                        showFccConnectionLayout(true)
                    }
                    else if(msg.contains("Connected")) {
                        log(TAG,"Getting fusion pump states")
                        FusionService.getPumpStatus(intentExtrasModel!!.mPumpNumber!!)
                        showFccConnectionLayout(false)
                        showPumpDisconnected(false)
                    }
                    else if(msg.contains("TIMEOUT")){
                        if(fuelingStates[0] || fuelingStates[1] || fuelingStates[2]) // fuelling started before
                        {
                            showFccConnectionLayout(true)
                        }
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                log(TAG, "Exception: " + e.message)
            }
        }
    }

    private fun formatFDCMessage(message: String): String {
        var message = message
        message = if (message.contains("</FDCMessage>")) {
            //split = response.split("</ServiceResponse>");
            "<FDCMessage " + StringUtils.substringBetween(
                message,
                "<FDCMessage",
                "</FDCMessage>"
            ) + "</FDCMessage>"
        } else {
            val msgArr =
                message.split("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />".toRegex())
                    .toTypedArray()
            if (msgArr.size > 2) println(msgArr[0] + ", " + msgArr[1])
            if (!msgArr[0].contains("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />")) "<FDCMessage>" + StringUtils.substringBetween(
                msgArr[0], "<FDCMessage>", "</FDCMessage>"
            ) + "</FDCMessage>" else msgArr[0] + "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />"
        }
        return message
    }
    private fun fpStateChanged(deviceState: String) {
        if (deviceState.contains("FDC_CALLING") && !fuelingStates[0]) {
            log(TAG, "FDC_CALLING")
            //fuelingStates[0] = true                         // added to fix this issue https://app.clickup.com/t/31gq8kp
            //fuelingStates[1] = true
            prefs.saveFuellingStates(fuelingStates)
            mBinding.progresMsg.text = getString(R.string.pump_started)
            log(TAG, "FDC_CALLING....")
            //stopConnectivityChecker()
            //startConnectivityChecker()
        }
        //if (deviceState.contains("FDC_AUTHORIZED") && !fuelingStates[1]) {
        if (deviceState.contains("FDC_AUTHORISED") && !fuelingStates[0]) {
            fuelingStates[0] = true                                     // added to fix this issue https://app.clickup.com/t/31gq8kp
           /* if(app.rht.petrolcard.BuildConfig.DEBUG)
                fuelingStates[0] = false            // added just for testing*/
            log(TAG, "FDC_AUTHORISED")
            prefs.saveFuellingStates(fuelingStates)
            mBinding.progresMsg.text = getString(R.string.pump_authorized)
            log(TAG, "Pump Authorised...")
            stopConnectivityChecker()
            startConnectivityChecker()

            intentExtrasModel!!.mTransaction!!.isPumpError = 1  // added this flag to make sure that tranaction will not refund in case in delaied transaction in case of pump error or power cut
                                                                // explanation why we added it here https://app.clickup.com/t/32c9mx7

           /* fuelingStates[0] = true                                     // added to fix this issue https://app.clickup.com/t/31gq8kp
            //fuelingStates[1] = true
            prefs.saveFuellingStates(fuelingStates)
            mBinding.progresMsg.text = getString(R.string.pump_authorized)
            log(TAG, "Pump Authorized...")
            mBinding.toolbarPumpList.toolbar.isEnabled = false
            startConnectivityChecker()*/
        }
        if (deviceState.contains("FDC_STARTED") && (!fuelingStates[0] || !fuelingStates[1])) {
            fuelingStates[0] = true
            fuelingStates[1] = true
            prefs.saveFuellingStates(fuelingStates)
            mBinding.progresMsg.text = getString(R.string.pump_started)
            log(TAG, "Pump Started...")
            mBinding.toolbarPumpList.toolbar.isEnabled = false
            stopConnectivityChecker()
            startConnectivityChecker()
        }
        if (deviceState.contains("FDC_FUELLING") && (!fuelingStates[0] || !fuelingStates[1] || !fuelingStates[2])) {
            mBinding.toolbarPumpList.toolbar.isEnabled = false
            fuelingStates[0] = true
            fuelingStates[1] = true
            fuelingStates[2] = true
            prefs.saveFuellingStates(fuelingStates)
            mBinding.progresMsg.text = getString(R.string.fuelling)
            log(TAG, "Fuelling...")
            stopConnectivityChecker()
        }
        if (deviceState.contains("FDC_READY") && !fuelingStates[2] && !fuelingStates[3]) { // after authorization nozzle put back on the pump
            fuelingStates[0] = false
            fuelingStates[1] = false
            fuelingStates[2] = false
            fuelingStates[3] = false
            prefs.saveFuellingStates(fuelingStates)
            log(TAG, "FDC_READY")
            mBinding.toolbarPumpList.toolbar.isEnabled = false
            mBinding.progresMsg.text =getString(R.string.please_wait_we_are_cancelling_your_transaction)
            log(TAG, "Nozzle put back on the pump...\nSending: ")
            log(TAG,"OCCURRENCE AT 4")
            cancelTransaction("","",7)
        }
        if (deviceState.contains("FDC_READY") &&  fuelingStates[2] && !fuelingStates[3]) {
            fuelingStates[3] = true
            showPumpDisconnected(false)
            prefs.saveFuellingStates(fuelingStates)
            log(TAG, "FDC_READY")
            mBinding.toolbarPumpList.toolbar.isEnabled = false
            mBinding.progressBarLayout.visibility = View.VISIBLE
            mBinding.progresMsg.text = getString(R.string.fueling_done)
//            startTransactionDataMessageChecker()
//            if(!BuildConfig.DEBUG)
//           {
                // FusionService.getAvailableTransactionList(intentExtrasModel!!.mPumpNumber.toString())   // COMMENTED FOR TESTING PUPOSE
//            }
            log(TAG, "Fueling done...\n")
        }
        if (deviceState.contains("FDC_ERRORSTATE") &&  fuelingStates[2] && !fuelingStates[3]) {  /// added to resolve When Pump Disconnect pop-up during fuelling when, show pump is disconnected
            showPumpDisconnected(true)
            prefs.isPumpError = true   /// added to resolve When Pump Disconnect pop-up during fuelling when, show pump is disconnected
        }
    }

    private inner class SendIfsfTimerTask(val message: String) : TimerTask() {
        override fun run() {
            IfsfService.sendIfsfMessage(message)
            log(TAG, message)
        }
    }
    private fun sendIfsfMessage(message: String) {
        Timer().schedule(SendIfsfTimerTask(message), 1000)
    }
    private fun stopFuelPoint() {
        log(TAG,"Sending stop fuel point command...")
        Handler(Looper.getMainLooper()).postDelayed({
            fusionRequestStatus = AppConstant.STOP_FUEL_POINT
            log(TAG,"mPumpNumber::"+intentExtrasModel!!.mPumpNumber!!)
            FusionService.stopFuelPoint(intentExtrasModel!!.mPumpNumber!!.toString())
            log(TAG,"stop fuel point command sent")
            startFuelPoint()
        },1000)

    }
    private fun startFuelPoint() {
        log(TAG,"Sending start fuel point command...")
        Handler(Looper.getMainLooper()).postDelayed({
            fusionRequestStatus = AppConstant.START_FUEL_POINT
            log(TAG,"mPumpNumber::"+intentExtrasModel!!.mPumpNumber!!)
            FusionService.startFuelPoint(intentExtrasModel!!.mPumpNumber!!.toString())
            log(TAG,"start fuel point command sent")
            log(TAG,"OCCURRENCE AT 5")
            cancelTransaction("","",8)
        },2000)
    }

    fun gotoBankActivity() {
        if(::fuelTrxDetails.isInitialized)
        {
            intentExtrasModel!!.amount = fuelTrxDetails[0]
            intentExtrasModel!!.fuelProductModel = FuelProductModel("", fuelTrxDetails[1], fuelTrxDetails[2])
            intentExtrasModel!!.mVolumeFUSION = fpVolume.toDouble()
            intentExtrasModel!!.selectedProduct = mProduitToCheckout
        }
        //stopIfsfReceiver()
        stopAllReciversAndTimers()
        log(TAG,"GOING TO BankPaymentProgressActivity")
        val intent = Intent(this, BankPaymentProgressActivity::class.java)
        intent.putExtra(INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
        finish()
    }
    fun gotoFleetCardActivity() {
        if(::fuelTrxDetails.isInitialized)
        {
            intentExtrasModel!!.amount = fuelTrxDetails[0]
            intentExtrasModel!!.fuelProductModel = FuelProductModel("", fuelTrxDetails[1], fuelTrxDetails[2])
            intentExtrasModel!!.mVolumeFUSION = fpVolume.toDouble()
            intentExtrasModel!!.selectedProduct = mProduitToCheckout
        }
        stopAllReciversAndTimers()
        if(referenceModel!!.IMPLEMENT_DISCOUNT!! && intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.REBATE_DISCOUNT && intentExtrasModel!!.mTransaction!!.isDiscountTransaction == 1)
        {
            log(TAG,"GOING TO Abort")
            gotoAbortMessageActivity(getString(R.string.transaction),getString(R.string.transaction_cancelled),intentExtrasModel!!.mTransaction)
        }
        else {
            log(TAG, "GOING TO DebitCardLimitsActivity")
            val intent = Intent(this, DebitCardLimitsActivity::class.java)
            intent.putExtra(INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
            finish()
        }
    }


    override fun onInternetEnableDisable(status: Boolean) {
        log(TAG, "Network Status:: $status")
        if (!status) {
            showFccConnectionLayout(true)
        }
    }
    override fun onBackPressed() {}

    private var clearOldAuthorizedTrx = false
    private var connectivityTimerRunning= false
    private var connectivityCheckTimer = object : CountDownTimer(33000,1000) {
        override fun onTick(millis: Long) {
            connectivityTimerRunning = true
            minutes = millis / 1000 / 60
            seconds = (millis / 1000 % 60)
            log(TAG,"Fusion Cancel Trx time remaining: $minutes:$seconds")

            if(!isNozzleClicked){                               // added to fix CU-8676zcbv6 - Added fix - Fuelup cancel trx at 18, and user immidiately clicked on fuel item after 30 sec timeout
                if(millis<=3000) {
                    log(TAG,"User did not clicked on auth button in 30 sec, buttons disabled for 5 sec to cancel trx safely")
                    mBinding.progresMsg.text = getString(R.string.please_wait_we_are_cancelling_your_transaction)
                    mBinding.progressBarLayout.visibility = View.VISIBLE
                    mBinding.prompt.visibility = View.GONE
                    mBinding.mListView.visibility = View.GONE
                } else {
                    mBinding.authTimerLayout.visibility = View.VISIBLE
                    mBinding.tvAuthTimeout.text = "$minutes:${seconds-3}"
                }
            }
        }

        override fun onFinish() {
            val connectivity =  Connectivity.isNetworkAvailable(this@ProductSelectionActivity)
            if(fuelingStates[0] && !fuelingStates[1] && !fuelingStates[2]) { //pump is authorized
                if(connectivity) // comes online the check pump states
                {
                    if(!FusionService.isRunning(this@ProductSelectionActivity))
                        FusionService.start(this@ProductSelectionActivity)

                    val fccConnected =  FusionService.fccConnected()
                    if(!fccConnected) {
                        FusionService.connectFcc(this@ProductSelectionActivity)
                    }
                    else {
                        clearTrxHistoryInSp()
                        mBinding.progresMsg.text = getString(R.string.please_wait_we_are_cancelling_your_transaction)
                        stopFuelPoint() //stop the pump
                    }
                }
                else { // there is no connectivity till now then set flag to cancel transaction later
                    clearOldAuthorizedTrx = true
                }
            }
            else if(fuelingStates[0] && fuelingStates[1] && !fuelingStates[2]){ // pump is in start state
                if(connectivity) // comes online the check pump states
                {
                    if(!FusionService.isRunning(this@ProductSelectionActivity))
                        FusionService.start(this@ProductSelectionActivity)

                    val fccConnected =  FusionService.fccConnected()
                    if(!fccConnected) {
                        FusionService.connectFcc(this@ProductSelectionActivity)
                    }
                    else {
                        //FusionService.sendResumeFuellingMsg(intentExtrasModel!!.mPumpNumber.toString() + "")
                        mBinding.progresMsg.text = getString(R.string.please_wait_we_are_cancelling_your_transaction)
                        stopFuelPoint() //stop the pump
                    }
                }
                else { // there is no connectivity till now then set flag to cancel transaction later
                    clearOldAuthorizedTrx = true
                }
            }
            else if(fuelingStates[1] || fuelingStates[2]) { // fueling was started before
                if(connectivity) // comes online the check pump states
                {
                    if(!FusionService.isRunning(this@ProductSelectionActivity))
                        FusionService.start(this@ProductSelectionActivity)

                    val fccConnected =  FusionService.fccConnected()
                    if(!fccConnected) {
                        FusionService.connectFcc(this@ProductSelectionActivity)
                    }
                }
            }
            else {  // user did not perform any step then abort transaction
                log(TAG,"ABORT TRX 1")
                //cancelTransaction("","",18)
                if(!isNozzleClicked) {                                      // added to resolve issue Added fix - Fuelup cancel trx at 18, and user immidiately clicked on fuel item after 30 sec timeout
                    cancelTransaction("","",18)     // for more please check https://app.clickup.com/t/8676zcbv6    Task id: CU-8676zcbv6
                } else {
                    startConnectivityChecker()
                }
            }
        }
    }
    private fun startConnectivityChecker(){
        if(!connectivityTimerRunning && isUnattendantMode){
            stopConnectivityChecker()
            connectivityCheckTimer.start()
//            if(app.rht.petrolcard.BuildConfig.DEBUG)
//                connectivityCheckTimer.cancel()
        }
    }
    private fun stopConnectivityChecker(){
       try {
           connectivityTimerRunning = false
           connectivityCheckTimer.cancel()
       } catch (e:Exception){
           e.printStackTrace()
       }
    }



    private var isCancelTrxTriggered = false
    private fun goBackOrCancelTrx(title:String = "", message: String = ""){
        val mesPompes = referenceModel!!.pompes!!
        val newPumpList = ArrayList<PumpsModel>()
        if (referenceModel!!.RFID_TERMINALS != null) {
            newPumpList.clear()
            val pumpsModels: List<RFIDPumpsModel> = referenceModel!!.RFID_TERMINALS!!.pumps
            if (pumpsModels != null) {
                for (pumpsModel in pumpsModels) {
                    for (pump in mesPompes) {
                        if (pump.FP_PUMP.toInt() == pumpsModel.pump_number) {
                            newPumpList.add(pump)
                        }
                    }
                }
            }
        }
        if(newPumpList.size==1){
            mBinding.progresMsg.text = getString(R.string.fdc_error_not_possible) + "\n" + getString(R.string.please_wait_we_are_cancelling_your_transaction)
            log(TAG,"OCCURRENCE AT 6")
            cancelTransaction("","",9)
        }
        else {
            stopFccReceiver()
            if(intentExtrasModel!!.isTerminalPowerOff!!){
                clearTrxHistoryInSp()
                cancelTransaction(title,message,10)
            }
            else
            {
                if(title.isNotEmpty())
                {
                    showSnakeBarColor(title,true)
                }
                Handler(Looper.getMainLooper()).postDelayed({
                    resetPumpHistory(TAG)
                    gotoPumpSelectionActivity()
                },1000)
            }

        }
    }
    private fun cancelTransaction(title:String = "", message: String = "",occurrence: Int) {
        log(TAG, "CANCEL TRX occurrence at: $occurrence, $isCancelTrxTriggered")
        if(!isCancelTrxTriggered){
            isCancelTrxTriggered = true
            //fusionCancelTrxTimer!!.cancel()
            if(fusionExist)
                FusionService.checkTransactionsToClear()
            stopAllReciversAndTimers()
            clearTrxHistoryInSp()

            if(intentExtrasModel!!.mTransaction!=null) {                    // added this flag to make sure that tranaction will not refund in case in delaied transaction in case of pump error or power cut
               intentExtrasModel!!.mTransaction!!.isPumpError = 0           // explanation why we added it here https://app.clickup.com/t/32c9mx7
            }

            if(intentExtrasModel!!.typePay == VISA_VALUE)
            {
                intentExtrasModel!!.bankRequestType = VOID_REQUEST
                gotoBankActivity()
            }
            else if(intentExtrasModel!!.typePay == CARD_VALUE) {
                intentExtrasModel!!.fleetCardRequestType = VOID_REQUEST
                //gotoBankActivity()
                // todo goto DebitCardLimitsActivity for to credit to complete amount
                log(TAG,"GOTO FLEET CARD ACTIVITY 3")
                gotoFleetCardActivity()
            }
            else {
                val dialogTitle = title.ifEmpty { getString(R.string.transaction_cancelled) }
                val dialogMessage = message.ifEmpty { getString(R.string.transaction_cancelled) }
                gotoAbortMessageActivity(dialogTitle,dialogMessage)
            }
        }
    }
    fun abortTransaction(){
        if(fusionExist)
            FusionService.checkTransactionsToClear()

        clearTrxHistoryInSp()
        if(intentExtrasModel!!.typePay == VISA_VALUE) {
            intentExtrasModel!!.bankRequestType = VOID_REQUEST
            gotoBankActivity()
        }
        else if(intentExtrasModel!!.typePay == CARD_VALUE) {
            intentExtrasModel!!.fleetCardRequestType = VOID_REQUEST
            //todo goto DebitCardLimitsActivity for complete refund
            log(TAG,"GOTO FLEET CARD ACTIVITY 4")
            gotoFleetCardActivity()
        }
        else
        {
            log(TAG,"Timer status:: "+3)
            stopIfsfReceiver()
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancelled))
        }
    }

    private fun showFccConnectionLayout(isShow: Boolean) {
        if(isShow)
        {
            startConnectivityChecker()
            mBinding.fccConnectionLayout.visibility = View.VISIBLE

            if(fuelingStates[0] || fuelingStates[1] || fuelingStates[2])
            {
                mBinding.btnVoid.visibility =View.GONE
                stopConnectivityChecker()

                startRetryFccConnectivity()
            }
            else
            {
                mBinding.btnVoid.visibility =View.VISIBLE
            }
        }
        else
        {
            stopRetryFccConnectivity()
            /*if(readSaleTrx[1] && readSaleTrx[2])
            {
                FusionService.getPumpStatus(intentExtrasModel!!.mPumpNumber.toString())
            }
            else
            {
                showLoading(false)
            }*/
            mBinding.fccConnectionLayout.visibility = View.GONE
        }
        Support.setNavigationStatus(this, false)
    }
    private fun showDisputedLayout(isShow: Boolean) {
        if(isShow)
        {
            mBinding.disputedTransactionLayout.visibility = View.VISIBLE
        }
        else
        {
            mBinding.disputedTransactionLayout.visibility = View.GONE
        }
        Support.setNavigationStatus(this, false)
        mBinding.markDisputed.setOnClickListener {
            try{
                val transactionDao = TransactionDao()
                transactionDao.open()
                val lastSavedTrx = transactionDao.getLastSavedTransaction()
                transactionDao.close()

                if(lastSavedTrx!=null)
                    sendTransactionOnEmail(lastSavedTrx)

            } catch (e:Exception) {
                e.printStackTrace()
            }
            storeDisputedTransaction()
        }
    }

    //region fusion got shutdown
    private fun startRetryFccConnectivity(){
        if(!retryFccConnectivityTimerRunning && isUnattendantMode){
            retryFccConnectivityTimer.start()
        }
    }
    private fun stopRetryFccConnectivity(){
        connectivityTimerRunning = false
        retryFccConnectivityTimer.cancel()
    }
    private var retryFccConnectivityTimerRunning = false
    private var retryFccConnectivityTimer = object : CountDownTimer(30000,1000) {
        override fun onTick(millis: Long) {
            retryFccConnectivityTimerRunning = true
            minutes = millis / 1000 / 60
            seconds = (millis / 1000 % 60)
            log(TAG,"Fusion Connectivity Check time remaining: $minutes:$seconds")
        }

        override fun onFinish() {
            val connectivity =  Connectivity.isNetworkAvailable(this@ProductSelectionActivity)
            if(fuelingStates[0] || fuelingStates[1] || fuelingStates[2]) { // fueling was started before
                retryFccConnection()
                isButtonClicked = true
            }
            retryFccConnectivityTimerRunning = false
        }
    }
    //endregion

    private fun clearTrxHistoryInSp() {
        prefs.isPowerCutGetFuelSaleTrxMsgSent = isPowerCutGetFuelSaleTrxMsgSent
      //  prefs.isPumpError = false   /// added to resolve if terminal power-off twice during the transaction - it will go back to product selection screen
        for (i in fuelingStates.indices) {
            fuelingStates[i] = false
        }
        prefs.saveFuellingStates(fuelingStates)
        prefs.mCurrentActivity = ""
        resetPaymentValues(TAG)
        log(TAG,"TRX HISTORY CLEARED")
    }

    //region fusion pump state checker
    private var fpStateCheckerRunning = false
    private var fpStateCheckTimer = object : CountDownTimer(5000,1000) {
        override fun onTick(millis: Long) {
            fpStateCheckerRunning = true
            minutes = millis / 1000 / 60
            seconds = (millis / 1000 % 60)
            log(TAG,"Fusion FP State Check time remaining: $minutes:$seconds")
        }

        override fun onFinish() {
            FusionService.getPumpStatus(intentExtrasModel!!.mPumpNumber!!)
            fpStateCheckerRunning = false
        }

    }
    private fun startFpStateChecker(){
        if(!fpStateCheckerRunning && isUnattendantMode){
            fpStateCheckTimer.start()
        }
    }
    private fun stopFpStateChecker(){
        fpStateCheckerRunning = false
        fpStateCheckTimer.cancel()
    }
    private fun showPumpDisconnected(isVisible: Boolean) {
        if (isVisible) {
            mBinding.pumpDisconnectedLayout.visibility = View.VISIBLE
        } else {
            mBinding.pumpDisconnectedLayout.visibility = View.GONE
        }
    }
    //endregion

    private fun getPumpStatusMessage(state:String):String
    {
        if(referenceModel!!.fuelPosMessage == null)
        {
            when (state) {
                PumpState.NO_PUMP -> {
                    return getString(R.string.no_pump_mode)
                }
                PumpState.IDLE -> {
                    return getString(R.string.pump_idle)
                }
                PumpState.CONFIG_ERROR -> {
                    return getString(R.string.configuration_error)
                }
                PumpState.REQUESTING -> {
                    return getString(R.string.requesting)
                }
                PumpState.RESERVED -> {
                    return getString(R.string.reserved)
                }
                PumpState.RESERVED_REQ -> {
                    return getString(R.string.reserved_requesting)
                }
                PumpState.RELEASED -> {
                    return getString(R.string.released)
                }
                PumpState.AUTO_RELEASE -> {
                    return getString(R.string.automatic_released)
                }
                PumpState.FILLING -> {
                    return getString(R.string.filling)
                }
                PumpState.BLOCKED -> {
                    return getString(R.string.blocked)
                }
                PumpState.STOPPED -> {
                    return getString(R.string.stopped)
                }
                PumpState.OFFLINE -> {
                    return getString(R.string.offline)
                }
                PumpState.SAFETY_REQ -> {
                    return getString(R.string.safety_requesting)
                }
                PumpState.SAFETY_IDLE -> {
                    return getString(R.string.safety_idle)
                }
                PumpState.MAJOR_ERROR -> {
                    return getString(R.string.major_error)
                }
                PumpState.MINOR_ERROR -> {
                    return getString(R.string.minor_error)
                }
                else -> {
                    return state
                }
            }
        }
        else
        {
            when (state) {
                PumpState.NO_PUMP -> {
                    return referenceModel!!.fuelPosMessage!!.NO_PUMP
                }
                PumpState.IDLE -> {
                    return referenceModel!!.fuelPosMessage!!.IDLE
                }
                PumpState.CONFIG_ERROR -> {
                    return referenceModel!!.fuelPosMessage!!.CONFIG_ERROR
                }
                PumpState.REQUESTING -> {
                    return referenceModel!!.fuelPosMessage!!.REQUESTING
                }
                PumpState.RESERVED -> {
                    return referenceModel!!.fuelPosMessage!!.RESERVED
                }
                PumpState.RESERVED_REQ -> {
                    return referenceModel!!.fuelPosMessage!!.RESERVED_REQ
                }
                PumpState.RELEASED -> {
                    return referenceModel!!.fuelPosMessage!!.RELEASED
                }
                PumpState.AUTO_RELEASE -> {
                    return referenceModel!!.fuelPosMessage!!.AUTO_RELEASE
                }
                PumpState.FILLING -> {
                    return referenceModel!!.fuelPosMessage!!.FILLING
                }
                PumpState.BLOCKED -> {
                    return referenceModel!!.fuelPosMessage!!.BLOCKED
                }
                PumpState.STOPPED -> {
                    return referenceModel!!.fuelPosMessage!!.STOPPED
                }
                PumpState.OFFLINE -> {
                    return referenceModel!!.fuelPosMessage!!.OFFLINE
                }
                PumpState.SAFETY_REQ -> {
                    return referenceModel!!.fuelPosMessage!!.SAFETY_REQ
                }
                PumpState.SAFETY_IDLE -> {
                    return referenceModel!!.fuelPosMessage!!.SAFETY_IDLE
                }
                PumpState.MAJOR_ERROR -> {
                    return referenceModel!!.fuelPosMessage!!.MAJOR_ERROR
                }
                PumpState.MINOR_ERROR -> {
                    return referenceModel!!.fuelPosMessage!!.MINOR_ERROR
                }
                else -> {
                    return state
                }
            }
        }
    }

    private fun getFuelPosPumpResponse(statusCode:String):String
    {
        if(referenceModel!!.fuelPosPumpResponse == null)
        {
            when (statusCode.toInt()) {
                CompletionCode.OK -> { return getString(R.string.ok) }
                CompletionCode.SYSTEM_FAILURE -> { return getString(R.string.system_failure) }
                CompletionCode.POWER_DOWN -> { return getString(R.string.power_down) }
                CompletionCode.PUMP_NOT_CONFIGURED -> { return getString(R.string.pump_not_configured) }
                CompletionCode.PUMP_BUSY -> { return getString(R.string.pump_is_busy) }
                CompletionCode.PUMP_NOT_AVAILABLE -> { return getString(R.string.pump_not_available) }
                CompletionCode.PUMP_NOT_EPR -> { return getString(R.string.pump_not_epr) }
                CompletionCode.PUMP_STOPPED -> { return getString(R.string.pump_stopped) }
                CompletionCode.PUMP_INACTIVE -> { return getString(R.string.pump_inactive) }
                CompletionCode.PUMP_RESERVE_TIMEOUT -> { return getString(R.string.pump_reserve_timeout) }
                CompletionCode.NOTHING_STOP -> { return getString(R.string.nothing_to_stop) }
                CompletionCode.NOZZLE_LIFT_TIMEOUT -> { return getString(R.string.nozzle_lift_timeout) }
                CompletionCode.INVALID_MESSAGE -> { return getString(R.string.invalid_message) }
                CompletionCode.PRODUCT_NOT_ALLOWED_DISPENSING -> { return getString(R.string.product_not_allowed) }
                CompletionCode.INVALID_MAX_AMOUNT -> { return getString(R.string.invalid_amount) }
                CompletionCode.INVALID_CURRENCY_CODE -> { return getString(R.string.invalid_currency_code) }
                CompletionCode.INVALID_ITEM_CODE -> { return getString(R.string.invalid_item_code) }
                CompletionCode.ITEM_INFO_NOT_AVAILABLE -> { return getString(R.string.invalid_info_not_available) }
                CompletionCode.ONLINE_AUTH_FAILED -> { return getString(R.string.online_auth_failed) }
                CompletionCode.ONLINE_AUTH_REFUSED -> { return getString(R.string.online_auth_refused) }
                CompletionCode.CANCELED_TRX -> { return getString(R.string.cancled_trx) }
                CompletionCode.CURRENCY_MISMATCH -> { return getString(R.string.currency_mismatch) }
                CompletionCode.AMOUNT_TOO_LOW -> { return getString(R.string.amount_two_low) }
                CompletionCode.AMOUNT_TOO_HIGH -> { return getString(R.string.amount_too_high) }
                else -> {
                    return CompletionCode.getMessage(statusCode)
                }
            }
        }
        else
        {
            when (statusCode.toInt()) {
                CompletionCode.OK -> { return referenceModel!!.fuelPosPumpResponse!!.OK }
                CompletionCode.SYSTEM_FAILURE -> { return referenceModel!!.fuelPosPumpResponse!!.SYSTEM_FAILURE  }
                CompletionCode.POWER_DOWN -> { return referenceModel!!.fuelPosPumpResponse!!.POWER_DOWN  }
                CompletionCode.PUMP_NOT_CONFIGURED -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_NOT_CONFIGURED  }
                CompletionCode.PUMP_BUSY -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_BUSY  }
                CompletionCode.PUMP_NOT_AVAILABLE -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_NOT_AVAILABLE  }
                CompletionCode.PUMP_NOT_EPR -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_NOT_EPR  }
                CompletionCode.PUMP_STOPPED -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_STOPPED  }
                CompletionCode.PUMP_INACTIVE -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_INACTIVE  }
                CompletionCode.PUMP_RESERVE_TIMEOUT -> { return referenceModel!!.fuelPosPumpResponse!!.PUMP_RESERVE_TIMEOUT  }
                CompletionCode.NOTHING_STOP -> { return referenceModel!!.fuelPosPumpResponse!!.NOTHING_STOP  }
                CompletionCode.NOZZLE_LIFT_TIMEOUT -> { return referenceModel!!.fuelPosPumpResponse!!.NOZZLE_LIFT_TIMEOUT  }
                CompletionCode.INVALID_MESSAGE -> { return referenceModel!!.fuelPosPumpResponse!!.INVALID_MESSAGE  }
                CompletionCode.PRODUCT_NOT_ALLOWED_DISPENSING -> { return referenceModel!!.fuelPosPumpResponse!!.PRODUCT_NOT_ALLOWED_DISPENSING  }
                CompletionCode.INVALID_MAX_AMOUNT -> { return referenceModel!!.fuelPosPumpResponse!!.INVALID_MAX_AMOUNT  }
                CompletionCode.INVALID_CURRENCY_CODE -> { return referenceModel!!.fuelPosPumpResponse!!.INVALID_CURRENCY_CODE  }
                CompletionCode.INVALID_ITEM_CODE -> { return referenceModel!!.fuelPosPumpResponse!!.INVALID_ITEM_CODE  }
                CompletionCode.ITEM_INFO_NOT_AVAILABLE -> { return referenceModel!!.fuelPosPumpResponse!!.ITEM_INFO_NOT_AVAILABLE  }
                CompletionCode.ONLINE_AUTH_FAILED -> { return referenceModel!!.fuelPosPumpResponse!!.ONLINE_AUTH_FAILED  }
                CompletionCode.ONLINE_AUTH_REFUSED -> { return referenceModel!!.fuelPosPumpResponse!!.ONLINE_AUTH_REFUSED  }
                CompletionCode.CANCELED_TRX -> { return referenceModel!!.fuelPosPumpResponse!!.CANCELED_TRX  }
                CompletionCode.CURRENCY_MISMATCH -> { return referenceModel!!.fuelPosPumpResponse!!.CURRENCY_MISMATCH  }
                CompletionCode.AMOUNT_TOO_LOW -> { return referenceModel!!.fuelPosPumpResponse!!.AMOUNT_TWO_LOW  }
                CompletionCode.AMOUNT_TOO_HIGH -> { return referenceModel!!.fuelPosPumpResponse!!.AMOUNT_TWO_HIGH  }
                else -> {
                    return CompletionCode.getMessage(statusCode)
                }
            }
        }
    }

    //region cancel ticket printing
    private var receiptDate = ""
    private var customerMessage = ""
    private var customerCopy = ""
    private var merchantCopy = ""
    @SuppressLint("SimpleDateFormat")
    private fun ticketConfig(){
        val terminalConfig = referenceModel!!.terminalConfig
        if(terminalConfig!=null){
            val receiptSetting = terminalConfig.receiptSetting
            if(receiptSetting!=null){
                if(!receiptSetting.dateFormat.isNullOrEmpty()){
                    val sdf = SimpleDateFormat(receiptSetting.dateFormat!!)
                    val currentDate = sdf.format(Date())
                    receiptDate =  currentDate
                }

                if(!receiptSetting.customerMessage.isNullOrEmpty()){
                    customerMessage = receiptSetting.customerMessage!!
                }

                if(!receiptSetting.customerCopy.isNullOrEmpty()){
                    customerCopy = receiptSetting.customerCopy!!
                }

                if(!receiptSetting.merchantCopy.isNullOrEmpty()){
                    merchantCopy = receiptSetting.merchantCopy!!
                }
            }
        }
    }

    private var ticketType = 1

    private fun getPrintBitmap(transactionData:TransactionData): Bitmap {
        ticketConfig()
        val currency = prefs.currency
        val fuelQtyUnit = referenceModel!!.FUEL_QTY_UNIT ?: "L"

        val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
        val qtyDecimal = receiptSetting.quantityDecimal ?: 2
        val uniteDecimal = receiptSetting.unitPriceDecimal ?: 2
        val vatDecimal = receiptSetting.vatDecimal ?: 2
        val netDecimal = receiptSetting.netDecimal ?: 2
        val discountDecimal = receiptSetting.discountDecimal ?: 2
        val totalDecimal = receiptSetting.totalAmountDecimal ?: 2

        val isLitre = intentExtrasModel!!.isLitreUnit ?: false

        var dateFormated: String? = transactionData.dateTime
        if (dateFormated == null || !dateFormated.contains(" ")) {
            dateFormated = Support.dateToString(Date())
        }

        val dateArray = dateFormated!!.split(" ").toTypedArray()
        val date = dateArray[0]
        val time = dateArray[1]

        //mTerminal = referenceModel!!.terminal  //getting only once on create activity

        var bitmap : Bitmap? = null
        try {
            bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap,400,400)
        } catch (e:Exception) {
            e.printStackTrace()
        }

        receipt = ReceiptBuilder(1200)
        if(bitmap!=null)  receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK).addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)
        receipt.setTextSize(65f).setTypeface(this@ProductSelectionActivity, "fonts/Roboto-Bold.ttf").setAlign(Paint.Align.CENTER).addText("").
        addText(mTerminal!!.stationName).
        addText(mTerminal!!.address+", "+mTerminal!!.city)

        receipt.addText("")
        receipt.addLine()
        setAlignment(Paint.Align.LEFT,65f).setAlign(Paint.Align.CENTER).addText("${if(receiptDate.isNotEmpty())receiptDate else date} $time")
        receipt.addParagraph()

        setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.pump_no_p),false)
        setAlignment(Paint.Align.RIGHT,65f).addText(transactionData.pump)

        //selectedNozzleModel!!.CardId

        selectedProductName = transactionData.productName
        setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.product_p),false)
        setAlignment(Paint.Align.RIGHT,65f).addText(selectedProductName)

        receipt.addText("")

        val qty = toDecimalValue(transactionData.quantity, qtyDecimal).decimal(qtyDecimal)
        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.qty_p) , false)
        setAlignment(Paint.Align.RIGHT,80f).addText("${Support.getFormattedValue(this@ProductSelectionActivity,qty)} $fuelQtyUnit")

        val unitPrice = toDecimalValue(transactionData.unitPrice, uniteDecimal).decimal(uniteDecimal)
        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.price_p) , false)
        setAlignment(Paint.Align.RIGHT,80f).addText("${Support.getFormattedValue(this@ProductSelectionActivity,unitPrice)} $currency")

        val totalAmt = toDecimalValue(transactionData.totalAmount, totalDecimal).decimal(totalDecimal)
        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.total_p) , false)
        setAlignment(Paint.Align.RIGHT,80f).addText("${Support.getFormattedValue(this@ProductSelectionActivity,totalAmt)} $currency")

        receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")

        if(transactionData.trxToken.isNotEmpty()){
            setAlignment(Paint.Align.LEFT,80f).addText(getString(R.string.transaction_token_p) , false)
            setAlignment(Paint.Align.RIGHT,80f).addText(transactionData.trxToken)
        }

        setAlignment(Paint.Align.LEFT,80f).addText(getString(R.string.error_code_p) , false)
        setAlignment(Paint.Align.RIGHT,80f).addText(transactionData.completionCode)


        if(pumpNotReleaseMessage.isNotEmpty()){
            if(pumpNotReleaseMessage.length>16)
                setAlignment(Paint.Align.LEFT,80f).addText("${getString(R.string.status)} : " , true)
            else {
                setAlignment(Paint.Align.LEFT,80f).addText("${getString(R.string.status)} : " , false)
            }
            setAlignment(Paint.Align.RIGHT,80f).addText(pumpNotReleaseMessage)
        }
        else {
            setAlignment(Paint.Align.LEFT,80f).addText("${getString(R.string.status)} : " , false)
            setAlignment(Paint.Align.RIGHT,80f).addText(getString(R.string.transaction_cancelled))
        }

        receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")

        if (!intentExtrasModel!!.tagsNFC.isNullOrEmpty()) {
            setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.tag_no_p)+" "+intentExtrasModel!!.tagsNFC)
        }

        if (!mTerminal!!.fiscalId.isNullOrEmpty() && !mTerminal!!.fiscalId.equals("0")) {
            setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.fiscal_p)+" "+mTerminal!!.fiscalId)
        }

        receipt.addText("")
        if (ticketType == CUSTOMER) {
            setAlignment(Paint.Align.LEFT,65f)
            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")
            log(TAG, "Customer Message:: $customerMessage")

            if(customerMessage.isNotEmpty())
            {
                if(customerMessage.contains("|"))
                {
                    val msgArray = customerMessage.split("|")
                    for(msg in msgArray)
                    {
                        receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(msg)
                    }
                }
                else
                {
                    receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(customerMessage)
                }
            }
            else
            {
                customerMessage = getString(R.string.thanks_for_fueling)
                receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(customerMessage)
            }

            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")
            receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(customerCopy.ifEmpty { getString(R.string.customer_copy_p) })
        } else {
            receipt.setAlign(Paint.Align.CENTER).setTextSize(60f).addText(merchantCopy.ifEmpty { getString(R.string.operator_copy_p) } )
        }

        receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(" ")
        receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(" ")

        val bitmapReceipt = receipt.build()
        if(bitmap!=null){ bitmap.recycle() }

        return bitmapReceipt
    }

    var cancelledTicketPrint : CancelledTicketPrint? = null
    inner class CancelledTicketPrint(val transactionData: TransactionData) : CoroutineAsyncTask<String, String, Void?>() {
        override fun doInBackground(vararg params: String): Void? {
            try {

                val bitmap = getPrintBitmap(transactionData)
                TicketPrinter(this@ProductSelectionActivity).printReceipt(bitmap)
            }
            catch (e:Exception)
            {
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                //   mViewModel.generateLogs(e.message!!,0)
                e.printStackTrace()
            }
            return null
        }

        override fun onPostExecute(result: Void?) {
            if (ticketType == AppConstant.ATTENDANT) {
                log(TAG,"OCCURRENCE AT 7")
                cancelTransaction("","",11)
            } else {
                ticketType = AppConstant.ATTENDANT
                showAttendantPrintDialog(transactionData)
            }
        }
    }
    private fun showCustomerPrintDialog(transactionData: TransactionData){
        var timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {}

            override fun onFinish() {
                val unAttendantMode = referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    log(TAG,"OCCURRENCE AT 8")
                    cancelTransaction("","",12)
                }
            }
        }
        if(referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()
        var title = getString(R.string.transaction_cancelled)

        val cancelReceiptPrint = referenceModel!!.terminalConfig!!.receiptSetting!!.cancelTrxPrintReceipt
        if(cancelReceiptPrint == CancelPrintReceipt.ENABLED || cancelReceiptPrint == CancelPrintReceipt.OPTIONAL) {
            try {
                val ctx = WeakReference(this).get()!!
                MaterialDialog(ctx)
                    .title(text = title)
                    .message(text = Html.fromHtml("<font color='#000000'>"+getString(R.string.do_you_want_print_customer_receipt)+"</font>"))
                    .cancelable(false)
                    .show {
                        cornerRadius(res = R.dimen.my_corner_radius)
                        positiveButton(text = getString(R.string.print),click = object : DialogCallback{
                            override fun invoke(dialog: MaterialDialog) {
                                setBeep()
                                timer.cancel()
                                ticketType = CUSTOMER
                                if(isPrinterPaperAvailable())
                                {
                                    cancelledTicketPrint = CancelledTicketPrint(transactionData)
                                    cancelledTicketPrint!!.execute()
                                }
                                else
                                {
                                    showPaperDialog(transactionData)
                                }

                            }
                        })
                        negativeButton(text = getString(R.string.cancel), click = object : DialogCallback{
                            override fun invoke(dialog: MaterialDialog) {
                                setBeep()
                                timer.cancel()
                                
                                if(referenceModel!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                                    log(TAG,"OCCURRENCE AT 9")
                                    cancelTransaction("","",13)
                                }
                                else
                                    showAttendantPrintDialog(transactionData)
                            }
                        })
                    }
            } catch (e:Exception) {
                e.printStackTrace()

            }
        }
        else {
            log(TAG,"Print receipt not required")
            log(TAG,"OCCURRENCE AT 10")
            cancelTransaction("","",14)
        }
    }
    fun showAttendantPrintDialog(transactionData: TransactionData){
        val cancelReceiptPrint = referenceModel!!.terminalConfig!!.receiptSetting!!.cancelTrxPrintReceipt
        if(cancelReceiptPrint == CancelPrintReceipt.ENABLED || cancelReceiptPrint == CancelPrintReceipt.OPTIONAL) {
            if (referenceModel!!.TERMINAL_TYPE != AppConstant.UN_ATTENDANT_MODE) {
                MaterialDialog(this)
                    .title(text = getString(R.string.confirm))
                    .message(text = Html.fromHtml("<font color='#000000'>" + getString(R.string.do_you_want_to_print_merchant_receipt) + "</font>"))
                    .cancelable(false)
                    .show {
                        cornerRadius(res = R.dimen.my_corner_radius)
                        positiveButton(
                            text = getString(R.string.print),
                            click = object : DialogCallback {
                                override fun invoke(dialog: MaterialDialog) {
                                    setBeep()
                                    ticketType = AppConstant.ATTENDANT
                                    if(isPrinterPaperAvailable())
                                    {
                                        cancelledTicketPrint = CancelledTicketPrint(transactionData)
                                        cancelledTicketPrint!!.execute()
                                    }
                                    else
                                    {
                                        showPaperDialog(transactionData)
                                    }
                                }
                            })
                        negativeButton(
                            text = getString(R.string.cancel),
                            click = object : DialogCallback {
                                override fun invoke(dialog: MaterialDialog) {
                                    setBeep()
                                    log(TAG,"OCCURRENCE AT 11")
                                    cancelTransaction("","",15)
                                }
                            })
                    }
            }
        } else {
            log(TAG,"Print receipt not required")
            log(TAG,"OCCURRENCE AT 12")
            cancelTransaction("","",16)
        }
    }
    private fun showPaperDialog(transactionData: TransactionData) {
        MyMaterialDialog(this,
            getString(R.string.error),
            printerStatusMessage,
            positiveBtnText = "Print again",
            negativeBtnText = "Cancel",
            object : MyMaterialDialogListener{
                override fun onPositiveClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    cancelledTicketPrint = CancelledTicketPrint(transactionData)
                    cancelledTicketPrint!!.execute()
                }

                override fun onNegativeClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    if (ticketType == AppConstant.ATTENDANT) {
                        log(TAG,"OCCURRENCE AT 7")
                        cancelTransaction("","",17)
                    } else {
                        ticketType = AppConstant.ATTENDANT
                        showAttendantPrintDialog(transactionData)
                    }
                    return
                }
            })
    }
    var selectedProductName = ""
    private fun getProductModel(productId: Int){
        try {
            val productsDAO = ProductsDao()
            productsDAO.open()
            val product = productsDAO.getProductById(productId)
            selectedProductName = if(product!=null) "${product.libelle}" else ""
            productsDAO.close()
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }
    //endregion

    private fun getUniqueToken() : String{
        /*val terminalDao = TerminalDao()
        terminalDao.open()
        mTerminal = terminalDao.getCurrent()
        terminalDao.close()*/
        if(!isReleaseTokenGenerated && tokenTRX.isNullOrEmpty())
        {
        isReleaseTokenGenerated = true
        var terminalId = mTerminal!!.fccTrxToken ?: "${mTerminal!!.terminalId}"  //getting unique terminal id for trx token
        if(terminalId.length<2)
            terminalId = Utils.padZeroRight(terminalId, 2)
        if(terminalId.length>2)
            terminalId = terminalId.substring(0,2)
            return "$terminalId${getTimestampSeconds()}"
        }
        else
        {
            return tokenTRX
        }
    }

    private fun storeFuelPosPendingTrxRecord(transactionData: TransactionData) {
        /* try {
             val pendingTrxDao = PendingFPosTrxDao()
             pendingTrxDao.open()
             pendingTrxDao.insert(transactionData)
         } catch (e:Exception) {
             e.printStackTrace()
         }*/
    }

    //region fuelpos connection checker
    private fun startFuelPosConnectionChecker() {
        try { fuelPosConnectionChecker.start() } catch (e:Exception) {}
    }
    private fun stopfuelPosConnectionChecker() {
        try{ fuelPosConnectionChecker.cancel() } catch (e:Exception) {}
    }
    private var fuelPosConnectionChecker = object : CountDownTimer(3000, 1000) {
        override fun onTick(p0: Long) {}
        override fun onFinish() {
            try{
                FuelPosService.restartFuelPosEpr()
                this.start()
            } catch (e:Exception){}
        }
    }

    private fun stopAllReciversAndTimers(){
        stopTransactionDataMessageChecker()
        stopFccReceiver()
        stopConnectivityChecker()
        if(fuelpos.isExist){
            FuelPosService.stopFuelPosEpr()
            FuelPosService.stopFuelPosEprServer()
            stopfuelPosConnectionChecker()
        }
        try{ trxCountPOSTrx = null } catch (e:Exception) { }
    }
    //endregion
}
