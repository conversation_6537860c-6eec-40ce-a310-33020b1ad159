package app.rht.petrolcard.ui.settings.card.common.model

import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import androidx.annotation.Keep
@Keep
data class CardItemModel(
    var iconResource: Int,
    var title: String,
    var id: String,
    var iconColor: String = "#2e3236",
)  : BaseModel() {

    fun getColor(): Int {
        return Color.parseColor(iconColor)
    }

    fun getIcon(): Int {
        return iconResource
    }
}