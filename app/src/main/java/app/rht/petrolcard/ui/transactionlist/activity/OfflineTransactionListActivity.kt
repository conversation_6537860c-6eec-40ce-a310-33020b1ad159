package app.rht.petrolcard.ui.transactionlist.activity

import android.app.AlertDialog
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.PriceDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.databinding.ActivityOfflineTransactionListBinding
import app.rht.petrolcard.ui.amountselection.activity.EnterAmountActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.model.PriceModel

import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.transactionlist.model.OfflineProductsModel
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.util.ArrayList

@Suppress("DEPRECATION")
class OfflineTransactionListActivity : BaseActivity<CommonViewModel>(CommonViewModel::class), RecyclerViewArrayAdapter.OnItemClickListener<OfflineProductsModel> {
    private var TAG= OfflineTransactionListActivity::class.java.simpleName
    private lateinit var mBinding: ActivityOfflineTransactionListBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    private var productList : ArrayList<ProductModel> = ArrayList()
    private var offlineProductsModel : ArrayList<OfflineProductsModel> = ArrayList()
    private var priceList : PriceModel? = null
    var mSelectedProduct: ProductModel? = null
    var color = "#FF8212"
    var icon = ""
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_offline_transaction_list)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getInitIntentExtras()
        getAllProductDetails()
        setupRecyclerView()
        setupToolbar()
    }
    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

           if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
    }
    private fun setupToolbar()
    {
        mBinding.toolbarofflineList.toolbar.tvTitle.text = getString(R.string.product_list)
        mBinding.toolbarofflineList.toolbar.setNavigationOnClickListener {
            mBinding.toolbarofflineList.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
        }
    }
    private fun setupRecyclerView(){
        mBinding.mListView.removeAllViews()
        val listAdapter = RecyclerViewArrayAdapter(offlineProductsModel,this)
        mBinding.mListView.adapter = listAdapter
        listAdapter.notifyDataSetChanged()

    }
    override fun setObserver() {

    }
    fun getAllProductDetails() {
        offlineProductsModel.clear()
        try {
            val mProductsDao = ProductsDao()
            mProductsDao.open()
            productList = mProductsDao.getFuelProducts()
            mProductsDao.close()
            val mPriceslistDao = PriceDao()
            mPriceslistDao.open()

            if (productList != null && productList.isNotEmpty()) {
                for (prod in productList) {
                    run {
                        getSubProductList(prod.productID)
                        priceList = mPriceslistDao.selectionnerLastPriceByIdProduit(prod.productID)
                        offlineProductsModel.add(
                            OfflineProductsModel(
                                productsModel = prod,
                                priceModel = priceList!!,
                                priceFullValue = priceList!!.unitPrice.toString() + prefs.currency + "/ L",
                                colorCode = color,
                                icon = icon
                            )
                        )
                    }
                    }
            }
            mPriceslistDao.close()
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
            log(TAG, Ex.message+ ExceptionUtils.getStackTrace(Ex))
          //  mViewModel.generateLogs(Ex.message!!,0)
        }
    }
    fun getSubProductList(productNo: Int){
        if(prefs.getFuelProductList() != null) {
            for (product in prefs.getFuelProductList()!!) {
                if (productNo == product.id) {
                    color = product.color_code
                    icon = product.icon
                }
            }
        }
    }
    override fun onItemClick(view: View, model: OfflineProductsModel) {
        MultiClickPreventer.preventMultiClick(view)
        if(view.id == R.id.offlineListLayout)
        if (model.priceModel != null &&model.priceModel.unitPrice > 0) {
            mSelectedProduct = model.productsModel
            intentExtrasModel!!.selectedPrice =model.priceModel
            intentExtrasModel!!.selectedProduct =model.productsModel
            intentExtrasModel!!.articleID = model.productsModel!!.productID.toString()

            if (mSelectedProduct != null) {
                val builder = AlertDialog.Builder(this@OfflineTransactionListActivity, R.style.MyStyleDialog)
                builder.setMessage(Html.fromHtml("<font color='#000000'>" + resources.getString(R.string.validate_product) + "</font>" + "<br/><br/>" + resources.getString(R.string.label_carburant) + " : <strong>" + model.productsModel.libelle.toString() + "</strong>" + "<br/>"))
                builder.setCancelable(false)
                builder.setNegativeButton(resources.getString(R.string.no)) { dialog, which ->
                    UtilsCardInfo.beep(mCore, 10)
                    dialog.dismiss()
                }
                builder.setPositiveButton(resources.getString(R.string.yes)) { dialog, which ->
                    UtilsCardInfo.beep(mCore, 10)
                    dialog.dismiss()
                    if (model.productsModel.isAvailable.equals("true")) {
                        validateTransaction()
                    } else {
                        showDialog(model.productsModel.libelle.toString() + " " + getString(R.string.restricted), getString(R.string.product_not_available))
                    }
                }
                val alert = builder.create()
                alert.show()
                val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
                //Set negative button background
                // nbutton.setBackgroundColor(Color.MAGENTA);
                //Set negative button text color
                nbutton.setTextColor(
                    ContextCompat.getColor(
                        this@OfflineTransactionListActivity,
                        R.color.redLight
                    )
                )
                nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                nbutton.textSize = 20f
                val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
                //Set positive button background
                // pbutton.setBackgroundColor(Color.YELLOW);
                //Set positive button text color
                pbutton.setTextColor(
                    ContextCompat.getColor(
                        this@OfflineTransactionListActivity,
                        R.color.greenLight
                    )
                )
                pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
                pbutton.textSize = 20f
            } else {
                showSnakeBar( resources.getString(R.string.no_product_selected))
            }
        } else {
            gotoAbortMessageActivity(resources.getString(R.string.error),resources.getString(R.string.no_ppu))
        }
    }

    fun validateTransaction() {
        var intent: Intent?=null

        intent = if (prefs.getBooleanSharedPreferences(AppConstant.wITHPICTURE))
            Intent(this@OfflineTransactionListActivity, TPhotoActivity::class.java)
        else
            Intent(this@OfflineTransactionListActivity, EnterAmountActivity::class.java)

        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)

    }


    /*private fun validateTransaction() {
        val messageErr: String
        val mProduitDAO = ProductsDao()
        mProduitDAO.open()
        var productCode: String

        var isArticleRestricted = false;
        var isStationRestricted = false;

        val mRestStationList= prefs.getReferenceModel()!!.restrictions_stations
        val mRestArticleList= prefs.getReferenceModel()!!.restrictions_articles
        if (intentExtrasModel!!.mRestArticleCard != 1 && mSelectedProduct!!.fcc_prod_id != 0 && mRestArticleList.isNotEmpty()) {
            isArticleRestricted = UtilsCardInfo.isArticleRestriction(intentExtrasModel!!.mRestArticleCard!!,mSelectedProduct!!.fcc_prod_id, mRestArticleList)
        } else {
            isArticleRestricted = false
        }
        isStationRestricted = if (intentExtrasModel!!.mRestStationCard != 1 && mRestStationList.isNotEmpty()) {
            UtilsCardInfo.isStationRestriction(intentExtrasModel!!.mRestStationCard!!, prefs.getPreferenceModel()!!.stationID!!, mRestStationList)
        } else {
            false
        }
        when {
            isArticleRestricted -> {
                showDialog(getString(R.string.error),resources.getString(R.string.block_rest_produit))
            }
            isStationRestricted -> {
                showDialog(getString(R.string.error),resources.getString(R.string.block_rest_station))
            }
            else -> {
                var intent: Intent?=null
                if (prefs.getBooleanSharedPreferences(AppConstant.wITHPICTURE))
                    intent = Intent(this@OfflineTransactionListActivity, TPhotoActivity::class.java)
                else
                    intent = Intent(this@OfflineTransactionListActivity, EnterAmountActivity::class.java)

                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)
            }
        }

    }*/

    override fun onBackPressed() {

    }
}
