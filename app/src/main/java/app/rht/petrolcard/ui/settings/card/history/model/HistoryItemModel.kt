package app.rht.petrolcard.ui.settings.card.history.model

import android.graphics.Color
import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.baseClasses.model.BaseModel

class HistoryItemModel (
     var type: String? = null,
     var montant: String? = null,
     var dateTrx: String? = null,
     var produit: String? = null,
     var productName: String? = null
):BaseModel()
{
   fun getTypeName():String
   {
        return when (type) {
            "00" -> {
                 "Debit"
            }
            "01" -> {
                 "Credit"
            }
            else -> {
                 ""
            }
        }
   }

    fun getTypeColor():Int
    {
        return when (type) {
            "00" -> {
                Color.parseColor("#e74c3c")
            }
            "01" -> {
                Color.parseColor("#2ecc71")
            }
            else -> {
                Color.parseColor("#000000")
            }
        }
    }
}