package app.rht.petrolcard.ui.transactionlist.model
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class ServiceResponseGPT(
    @SerializedName("ServiceResponse")
    val serviceResponse: ServiceResponsePT
)
@Keep
data class ServiceResponsePT(
//    @SerializedName("ApplicationSender")
//    val applicationSender: Int,
    @SerializedName("FDCdata")
    val fDCdata: FDCdataPT,
//    @SerializedName("OverallResult")
//    val overallResult: String,
//    @SerializedName("RequestID")
//    val requestID: Int,
//    @SerializedName("RequestType")
//    val requestType: String,
//    @SerializedName("WorkstationID")
//    val workstationID: String,
//    @SerializedName("xmlns:xsd")
//    val xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    val xmlnsXsi: String
)
@Keep
data class FDCdataPT(
//    @SerializedName("ErrorCode")
//    val errorCode: String,
//    @SerializedName("FDCTimeStamp")
//    val fDCTimeStamp: String,
    @SerializedName("FuelProducts")
    val fuelProducts: FuelProducts
)
@Keep
data class FuelProducts(
    @SerializedName("Product")
    val product: ArrayList<ProductPT>
)
@Keep
data class ProductPT(
    @SerializedName("ProductName")
    val productName: String,
    @SerializedName("ProductNo")
    val productNo: Int
)