package app.rht.petrolcard.ui.reference.model

import android.os.Parcel
import android.os.Parcelable

class AuditModel() : Parcelable {
    var id: Int? = null
    var activity: String? = null
    var action: String? = null
    var performedBy: String? = null
    var timestamp: String? = null
    var teleCollectStatus : Int? = null

    constructor(parcel: Parcel) : this() {
        id = parcel.readValue(Int::class.java.classLoader) as? Int
        activity = parcel.readString()
        action = parcel.readString()
        performedBy = parcel.readString()
        timestamp = parcel.readString()
        teleCollectStatus = parcel.readValue(Int::class.java.classLoader) as? Int
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeValue(id)
        parcel.writeString(activity)
        parcel.writeString(action)
        parcel.writeString(performedBy)
        parcel.writeString(timestamp)
        parcel.writeValue(teleCollectStatus)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<AuditModel> {
        override fun createFromParcel(parcel: Parcel): AuditModel {
            return AuditModel(parcel)
        }

        override fun newArray(size: Int): Array<AuditModel?> {
            return arrayOfNulls(size)
        }
    }
}