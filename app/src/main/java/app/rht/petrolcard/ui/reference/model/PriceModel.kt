package app.rht.petrolcard.ui.reference.model

import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.baseClasses.model.BaseModel
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

import java.util.*

import androidx.annotation.Keep
@Keep
data class PriceModel(
    @SerializedName("datedebut") @Expose
    val datedebut: String?,
    @SerializedName("datefin") @Expose
    val datefin: String?,
    @SerializedName("id") @Expose
    val id: Int,
    @SerializedName("idproduit") @Expose
    val idproduit: Int,
    @SerializedName("fcc_prod_id") @Expose
    val fcc_prod_id: String,
    @SerializedName("prix") @Expose
    var unitPrice: Double
):BaseModel(),Parcelable {

    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString()!!,
        parcel.readDouble()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(datedebut)
        parcel.writeString(datefin)
        parcel.writeInt(id)
        parcel.writeInt(idproduit)
        parcel.writeString(fcc_prod_id)
        parcel.writeDouble(unitPrice)

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<PriceModel> {
        override fun createFromParcel(parcel: Parcel): PriceModel {
            return PriceModel(parcel)
        }

        override fun newArray(size: Int): Array<PriceModel?> {
            return arrayOfNulls(size)
        }
        fun Parcel.writeDate(date: Date?) {
            writeLong(date?.time ?: -1)
        }

        fun Parcel.readDate(): Date? {
            val long = readLong()
            return if (long != -1L) Date(long) else null
        }
    }

}