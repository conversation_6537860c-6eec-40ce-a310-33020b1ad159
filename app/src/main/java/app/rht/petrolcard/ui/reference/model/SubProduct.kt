package app.rht.petrolcard.ui.reference.model

import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import androidx.annotation.Keep
@Keep
data class SubProduct(
    val code: String,
    val color_code: String,
    val icon: String,
    val id: Int,
    val isAvailable: Boolean,
    val label: String,
    val fcc_prod_id: String,
    val hs_code: String?
):BaseModel()
{
    fun getCardBgColor(): Int {
        var color="#FFFFFF"
        if(!color_code.isNullOrEmpty())
        {
            color = color_code
        }
        return Color.parseColor(color)
    }
}