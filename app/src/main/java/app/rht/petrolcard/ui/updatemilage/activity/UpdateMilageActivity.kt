package app.rht.petrolcard.ui.updatemilage.activity


import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.nfc.model.NFCEnum
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.updatecard.model.VehicleModel
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import java.sql.SQLException
import java.util.*


@Suppress("DEPRECATION")
class UpdateMilageActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private val TAG = UpdateMilageActivity::class.java.simpleName
    private lateinit var mBinding: app.rht.petrolcard.databinding.ActivityUpdateMileageBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    var returnValue=0
    private var mBankCard: BankCard? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String = ""
    var authKey = ""
    var nomPorteur = ""
    var statusCard = "1"
    var vehicleModel: VehicleModel? = VehicleModel()
    private var infoCarte: String? = null

    var listnfcrecord: List<NfcTagModel> = ArrayList<NfcTagModel>()
    lateinit var readCardTask : ReadCardAsyncTask

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_update_mileage)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        getInitIntentExtras()
        setupToolbar()
        if(intentExtrasModel!!.listnfcrecord != null) {
            listnfcrecord = intentExtrasModel!!.listnfcrecord!!
            mBinding.enteredMileage.requestFocus()
            val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
            imm.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0)
            mBinding.submitButton.setOnClickListener {
                val imm: InputMethodManager = getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                imm.hideSoftInputFromWindow(mBinding.enteredMileage.windowToken, 0)
                if(!mBinding.enteredMileage.text.toString().isNullOrEmpty())
                {
                    readCardTask = ReadCardAsyncTask()
                    readCardTask.execute()
                }
                else
                {
                    mBinding.enteredMileage.error = getString(R.string.kilo_plz)
                }

            }
            mBinding.cancelButton.setOnClickListener {
                setBeep()
                gotoAbortMessageActivity(
                    getString(R.string.transaction_cancelled),
                    getString(R.string.transaction_cancel)
                )
            }
        }
        else
        {

            gotoAbortMessageActivity(
                getString(R.string.transaction_cancelled),
                getString(R.string.nfc_tag_not_available_on_this_card)
            )
        }

    }
    private fun setupToolbar()
    {
        mBinding.toolbarMileage.toolbar.tvTitle.text = getString(R.string.update_mileage)
        mBinding.toolbarMileage.toolbar.setNavigationOnClickListener {
            setBeep()
            mBinding.toolbarMileage.toolbar.isEnabled = false
            gotoAbortMessageActivity(
                getString(R.string.transaction_cancelled),
                getString(R.string.transaction_cancel)
            )
        }
    }

    override fun onStop() {
        super.onStop()
        if(::readCardTask.isInitialized){
            readCardTask.cancel(true)
        }
    }

    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }
    override fun setObserver() {

    }
    override fun onInternetEnableDisable(status: Boolean) {

    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@UpdateMilageActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                     infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@UpdateMilageActivity) //icCpuReader Not Required for BTPE
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19) else return -1 //Abort Transaction

                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@UpdateMilageActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@UpdateMilageActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {

                            if (panNumber != null) {

                                if (UtilsCardInfo.verifyPIN(mBankCard, icCpuReader, intentExtrasModel!!.mPinNumberCard, this@UpdateMilageActivity)) {
                                    try {
                                        if (intentExtrasModel!!.tagsNFC != null) {
                                            val resultNFC = UtilsCardInfo.readBinaryFile(mBankCard, icCpuReader, "2F20", "5F", this@UpdateMilageActivity)
                                            listnfcrecord = UtilsCardInfo.getNFCList(resultNFC!!)

                                            val itemNFC: NfcTagModel? = UtilsCardInfo.searchforNFCTag(listnfcrecord, NFCEnum.NFC, intentExtrasModel!!.tagsNFC)
                                            return if (itemNFC != null) {
                                                itemNFC.kmValeur = mBinding.enteredMileage.text.toString()
                                                val dataNFCKM = UtilsCardInfo.updateKM(itemNFC, resultNFC!!, itemNFC.position)
                                                val resultado = UtilsCardInfo.updateBinaryFile(mBankCard, icCpuReader, "2F20", "5F", dataNFCKM.substring(0, 190).uppercase(), this@UpdateMilageActivity)
                                                1
                                            } else {
                                                -2
                                            }
                                        }
                                    }
                                    catch (e:Exception)
                                    {
                                        e.printStackTrace()
                                        return -2
                                    }
                                }

                            } else {
                                returnValue = -1
                            }
                        }
                    else
                    {
                        return -1
                    }
                }
                else
                {
                    returnValue = -1
                }
            }
            catch (e: Exception)
            {
                e.printStackTrace()
                log(TAG,e.message+" "+e.cause)
            }
            return returnValue
        }
        override fun onPreExecute() {
            mBinding.progressBarLayout.visibility = View.VISIBLE
            mBinding.mileageLayout.visibility = View.GONE
        }
        override fun onPostExecute(result: Int?) {
            when (result) {
                1 -> {
                    saveTransaction()
                    gotoSuccessMessageActivity(getString(R.string.card_update_success),getString(R.string.mileage_updated_successfully), MenuActivity::class.java)
                }
                -1 -> {
                    gotoAbortMessageActivity(resources.getString(R.string.error), resources.getString(R.string.read_card_failed))
                }
                -2 -> {
                    gotoAbortMessageActivity(resources.getString(R.string.error), getString(R.string.failed_to_update_mileage))
                }

            }
        }
        override fun onProgressUpdate(vararg values: IntArray) {

        }
    }
    fun saveTransaction()
    {
        intentExtrasModel!!.mTransaction!!.idTerminal = if (prefs.getReferenceModel()!!.terminal != null) prefs.getReferenceModel()!!.terminal!!.terminalId else 0
        intentExtrasModel!!.mTransaction!!.idTypeTransaction = 5 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge ; 5 = Update Mileage
        intentExtrasModel!!.mTransaction!!.dateTransaction = Support.dateToString(Date())
        intentExtrasModel!!.mTransaction!!.flagController = 0
        intentExtrasModel!!.mTransaction!!.kilometrage = mBinding.enteredMileage.text.toString()
        intentExtrasModel!!.mTransaction!!.tagNFC = intentExtrasModel!!.tagsNFC
        intentExtrasModel!!.mTransaction!!.pan = panNumber
        intentExtrasModel!!.mTransaction!!.flagTelecollecte = 0
        intentExtrasModel!!.mTransaction!!.reference = "TRX" + Support.generateReference(this,true)
        intentExtrasModel!!.mTransaction!!.transactionStatus = 1
        try {
            insertTransactionData(intentExtrasModel!!.mTransaction!!)
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }

    override fun onBackPressed() {

    }

}
