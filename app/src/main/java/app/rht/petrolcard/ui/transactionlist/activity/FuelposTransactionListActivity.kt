package app.rht.petrolcard.ui.transactionlist.activity

import android.app.AlertDialog
import android.content.*
import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.databinding.ActivityTransactionListBinding
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.ModePayActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.transactionlist.model.*
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.OFFLINE_TRX_MODE
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.fuelpos.*
import app.rht.petrolcard.utils.fuelpos.models.PosTransaction
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.tax.TaxModel
import app.rht.petrolcard.utils.tax.TaxUtils
import com.altafrazzaque.ifsfcomm.*
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.net.Socket
import java.util.*


class FuelposTransactionListActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<TransactionFromFcc>,android.view.View.OnClickListener  {
    private lateinit var mBinding: ActivityTransactionListBinding
    private var TAG = FuelposTransactionListActivity::class.simpleName
    private var stationMode=0
    private var intentExtrasModel: IntentExtrasModel? = null
    private var transactionToPay: TransactionFromFcc? = null
    private var carburant: String? = null
    private var volume: String? = null
    private var montant: String? = null
    private val mReponseFusion: ResponseFusion? = null
    private var mTerminalDAO: TerminalDao? = null
    private var mTerminal: TerminalModel? = null
    private var transactionList: ArrayList<TransactionFromFcc> = java.util.ArrayList()
    private var fuelTransactionStatusDAO: FCCTransactionsDao? = null
    private var mTransaction: TransactionModel? = null
    private var mProduitDAO: ProductsDao? = null
    var selectedProduct: ProductModel? = null
    private var mTransactionDAO: TransactionDao? = null
    var daoError = false
    var socket: Socket? = null
    var command: String? = null
    var response: String? = null
    var produit: String? = null
    var pump = 0
    var amount = 0.0
    var quantite = 0.0
    var idterminal = 0
    var result = false
    var fusionFuelTrxList: MutableList<TransactionFromFcc> = ArrayList()
    lateinit var fuelpos : FuelPOSModel
    var decimal  = 2
    lateinit var mOperationsAdapter : RecyclerViewArrayAdapter<TransactionFromFcc>
    var fuelQtyUnit = "L"
    var rfidPumpsModel = ArrayList<RFIDPumpsModel>()
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_transaction_list)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getIntentData()
        setupToolbar()
        if (stationMode == 3 && fuelpos.isExist) {
            showProgress(true)
            startFuelPosReceiver()
        }
        showTransactionsInRecyclerView()
    }
    private fun getIntentData() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        fuelpos = prefs.getFuelPosModel()!!
        val referenceModel = prefs.getReferenceModel()
        mTerminal = referenceModel!!.terminal
        fuelQtyUnit = referenceModel.FUEL_QTY_UNIT!!
        if (referenceModel.RFID_TERMINALS != null) {
            rfidPumpsModel.clear()
            rfidPumpsModel.addAll(referenceModel.RFID_TERMINALS.pumps)
        }
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if (intentExtrasModel!!.loyaltyTrx) {
                stationMode = 1
            }
        }
        transactionToPay = null
    }
    private fun showTransactionsInRecyclerView() {
        showProgress(true)
        Collections.sort(transactionList, TransactionFromFccComparator())
        mOperationsAdapter = RecyclerViewArrayAdapter(transactionList,this)
        mBinding.mListView.adapter = mOperationsAdapter
        mOperationsAdapter.notifyDataSetChanged()

    }
    private fun setupToolbar()
    {
        mBinding.toolbarTransactionList.toolbar.tvTitle.text = resources.getString(R.string.transaction_list_title)
        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.setOnClickListener(this)
        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.setImageResource(R.drawable.ic_baseline_refresh_24)
        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.visibility = View.VISIBLE
        mBinding.toolbarTransactionList.toolbar.setNavigationOnClickListener {
            mBinding.toolbarTransactionList.toolbar.isEnabled = false
            setBeep()
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel)) }
    }

    private fun gotoOfflineModePage() {
        if (stationMode != 3) {
            if (mReponseFusion?.error != null) showToast( mReponseFusion.error
            ) else showToast(resources.getString(R.string.offline))
        } else {
            showToast( resources.getString(R.string.no_transaction_found))
        }
        // go directly to offline mode, no need to put a popup
        val intent = Intent(applicationContext, OfflineTransactionListActivity::class.java)
        intentExtrasModel!!.stationMode = OFFLINE_TRX_MODE
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        applicationContext.startActivity(intent)
        //  finish()
    }
    private fun showProgress(isVisible: Boolean) {
        if (isVisible) {
            mBinding.progressBarLayout.visibility = View.VISIBLE
            mBinding.listViewLayout.visibility = View.GONE
        } else {
            mBinding.progressBarLayout.visibility = View.GONE
            mBinding.listViewLayout.visibility = View.VISIBLE
        }
    }

    private fun clearFccFuelTrxList() {
        fusionFuelTrxList.clear()
        mBinding.progressMessage.visibility = View.GONE
    }

    override fun setObserver() {

    }

    override fun onItemClick(view: View, model: TransactionFromFcc) {
        MultiClickPreventer.preventMultiClick(view)
        prefs.isSignInBackground =false
        if(view.id == R.id.transactionListLayout) {
            UtilsCardInfo.beep(mCore, 10)
            transactionToPay = model
            prefs.saveTransactionModel(model)
            log("" + " : ", prefs.getTransactionModel()!!.ref_transaction!!)

            if (transactionToPay != null) {
                carburant = transactionToPay!!.produit
                volume = transactionToPay!!.quantite.toString()
                montant = montant+ " " + prefs.getStringSharedPreferences(AppConstant.MODE_CURRENCY)

                if(fuelVat.enabled){
                    showTaxAmountDialog(carburant!!,volume!!, transactionToPay!!.amount.toString())
                }
                else {
                    showPaymentDialog(carburant!!,volume!!, transactionToPay!!.amount.toString())
                }

            } else {
                showToast(resources.getString(R.string.no_trx_selected))
            }
        }
    }

    private fun showPaymentDialog(productName:String, qty: String, amount:String){
        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)
        builder.setMessage(
            Html.fromHtml(
                "<font color='#000000'>" + resources.getString(R.string.validate_transaction) + "</font>" +
                        "<br/><br/>"
                        + resources.getString(R.string.label_carburant) + " : <strong>" + productName + "</strong>" +
                        "<br/>" +
                        "Volume : <strong>" + qty + "</strong>" +
                        "<br/>" +
                        resources.getString(R.string.amount_pay) + " : <strong>" + amount + "</strong>"
            )
        )
        builder.setCancelable(false)
        builder.setNegativeButton(
            resources.getString(R.string.no)
        ) { dialog, which ->
            prefs.isSignInBackground =true
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
        }
        builder.setPositiveButton(
            resources.getString(R.string.yes)
        ) { dialog, which ->
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
            getProductDetails(transactionToPay!!.productId!!)
        }
        val alert = builder.create()
        alert.show()
        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        nbutton.setTextColor(
            ContextCompat.getColor(this, R.color.redLight)
        )
        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        nbutton.textSize = 20f
        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        pbutton.setTextColor(
            ContextCompat.getColor(this, R.color.greenLight)
        )
        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        pbutton.textSize = 20f
    }
    var taxModel: TaxModel? = null
    var vatAmount: Double? = 0.0
    private fun showTaxAmountDialog(productName:String, qty: String, amount:String) {
        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)

        var isInclusive = true
        var type = "Incl."
        taxModel = if(fuelVat.enabled) {
            isInclusive = fuelVat.type == 0
            type = if(isInclusive) "Incl." else  "Excl."
            TaxUtils.calculate(amount.toDouble(),fuelVat.percentage!!.toDouble(),isInclusive)
        } else /*if(shopVat.enabled)*/ {
            isInclusive = shopVat.type == 0
            type = if(isInclusive) "Incl." else  "Excl."
            TaxUtils.calculate(amount.toDouble(),shopVat.percentage!!.toDouble(),isInclusive)
        }

        builder.setMessage(Html.fromHtml(("<font color='#000000'>" + resources.getString(R.string.validate_transaction) + "</font><br/><br/>" +
                getString(R.string.label_carburant) + " : <strong>" + productName + "</strong><br/>" +
                getString(R.string.qty) + " : <strong>" + "$qty $fuelQtyUnit" + "</strong><br/><br/>" +
                getString(R.string.net_amount) + " : <strong>" + Support.formatString(taxModel!!.netAmount).toString() + " " + prefs.currency + "</strong>" + "<br/>" +
                getString(R.string.tax_amount) + " (${taxModel!!.taxPercentile}% $type)" + " : <strong>" + Support.formatString(taxModel!!.taxAmount).toString() + " " + prefs.currency + "</strong>" + "<br/><br/>" +
                getString(R.string.total_amount) + " : <strong>" + taxModel!!.totalAmount.toString() + " " + prefs.currency + "</strong>"
                )))
        builder.setCancelable(false)
        builder.setNegativeButton(resources.getString(R.string.no)) { dialog, which ->
            prefs.isSignInBackground =true
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
        }
        builder.setPositiveButton(resources.getString(R.string.yes)) { dialog, which ->
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
            intentExtrasModel!!.taxModel = taxModel
            getProductDetails(transactionToPay!!.productId!!)
        }
        val alert = builder.create()
        alert.show()
        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        nbutton.setTextColor(ContextCompat.getColor(this, R.color.redLight))
        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        nbutton.textSize = 20f
        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        pbutton.setTextColor(ContextCompat.getColor(this, R.color.greenLight))
        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        pbutton.textSize = 20f
    }

    //endregion
    override fun onClick(view: View) {
        MultiClickPreventer.preventMultiClick(view)
        UtilsCardInfo.beep(mCore, 10)
        when (view.id) {
            R.id.toolbar_right_image -> {
                refreshTransactions()
            }
        }
    }

    private fun refreshTransactions() {
        transactionList.clear()
        showProgress(true)
        mBinding.progressMessage.text = getString(R.string.fetching_latest_transactions)
        if (intentExtrasModel!!.stationMode == AppConstant.AFTER_TRX_MODE && fuelpos.isExist) {
            startFuelPosReceiver()
        }
    }

    fun goToPayment(selectedProduct: ProductModel?) {
        val intent1 = Intent(this, ModePayActivity::class.java) // commented
        intentExtrasModel!!.mTransaction=mTransaction
        intentExtrasModel!!.selectedProduct=selectedProduct
        log(TAG,"mTransaction:: "+gson.toJson(mTransaction))
        intent1.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        log(TAG,"isLoyalty:: "+intentExtrasModel!!.loyaltyTrx)
        startActivity(intent1)
    }

    private fun getProductDetails(productChosen:Int) {
        try {
            mProduitDAO = ProductsDao()
            mProduitDAO!!.open()
            selectedProduct = mProduitDAO!!.getProductById(productChosen)
            log(TAG, "productChosen::: $productChosen")
            log(TAG,"getAllProducts ::: "+mProduitDAO!!.getAllProducts())
            log(TAG, "selectedProduct ::: $selectedProduct")
            mProduitDAO!!.close()
            getTransactionDetails()
        } catch (ex: SQLiteException) {
            daoError = true
            performNextStepProductTask(false)
            ex.printStackTrace()
            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
            //  mViewModel.generateLogs(ex.message!!,0)
        }
    }
    private fun getTransactionDetails() {
        if (selectedProduct != null) {
            saveTransactionDetails()

        } else {
            showDialog(getString(R.string.product_mismatch),getString(R.string.this_product_not_available_on_server))
            log(TAG, "### ### ### product => null")
        }
    }

    private fun saveTransactionDetails() {
        try {
            mTransactionDAO = TransactionDao()
            mTransactionDAO!!.open()
            mTransaction = mTransactionDAO!!.checkAvailableTransactions(transactionToPay!!.pump.toString(),transactionToPay!!.ref_transaction!!)!!

            if(mTransaction != null && mTransaction!!.reference != null)
            {
                if(taxModel != null)
                {
                    mTransaction!!.vatAmount = Support.formatString(taxModel!!.taxAmount)
                    mTransaction!!.netAmount = Support.formatString(taxModel!!.netAmount)
                    mTransaction!!.amount =  taxModel!!.totalAmount
                    if(fuelVat.enabled || shopVat.enabled)
                    {
                        mTransaction!!.vatPercentage =prefs.getReferenceModel()!!.fuelVat!!.percentage
                        mTransaction!!.vatType =prefs.getReferenceModel()!!.fuelVat!!.type
                    }
                }
                mTransaction!!.fccProductId = selectedProduct!!.fcc_prod_id.toString()
                mTransaction!!.categoryId =intentExtrasModel!!.categoryId
                mTransaction!!.idTerminal = mTerminal!!.terminalId
                mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
                mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
                mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
                mTransaction!!.fccSaleId = transactionToPay!!.fusionSaleId
                mTransactionDAO!!.updateTransactionsByReferenceID(mTransaction!!)
            }
            else
            {
                mTransaction = TransactionModel()
                if(taxModel != null)
                {
                    mTransaction!!.vatAmount =Support.formatString(taxModel!!.taxAmount)
                    mTransaction!!.netAmount = Support.formatString(taxModel!!.netAmount)
                    mTransaction!!.amount =  taxModel!!.totalAmount
                    if(fuelVat.enabled || shopVat.enabled)
                    {
                        mTransaction!!.vatPercentage =prefs.getReferenceModel()!!.fuelVat!!.percentage
                        mTransaction!!.vatType =prefs.getReferenceModel()!!.fuelVat!!.type
                    }
                }
                mTransaction!!.categoryId =intentExtrasModel!!.categoryId
                mTransaction!!.idTerminal = mTerminal!!.terminalId
                mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
                mTransaction!!.idProduit = selectedProduct!!.productID
                mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
                mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
                mTransaction!!.dateTransaction = transactionToPay!!.dh_transaction
                mTransaction!!.amount =  transactionToPay!!.amount
                mTransaction!!.quantite = transactionToPay!!.quantite
                mTransaction!!.unitPrice = transactionToPay!!.pu
                mTransaction!!.flagController = 1
                mTransaction!!.sequenceController = transactionToPay!!.ref_transaction
                mTransaction!!.kilometrage = ""
                mTransaction!!.pan = intentExtrasModel!!.panNumber
                mTransaction!!.flagTelecollecte = 0
                mTransaction!!.pumpId = transactionToPay!!.pump.toString()
                mTransaction!!.fccSaleId = transactionToPay!!.fusionSaleId
                mTransaction!!.transactionStatus = 0
                mTransaction!!.productName = selectedProduct!!.libelle
                mTransaction!!.reference = "TRX" + Support.generateReference(this)/*Support.generateReference(mTransaction!!.dateTransaction, "", "", this@TransactionListActivity)*/
                mTransaction!!.fccProductId = selectedProduct!!.fcc_prod_id.toString()
                mTransaction!!.timsSignDetails!!.fuelQtyUnit = transactionToPay!!.fuelQtyUnit
                mTransaction!!.timsSignDetails!!.hsCode = transactionToPay!!.hsCode
                intentExtrasModel!!.mTransaction = mTransaction
                insertTransactionData(mTransaction!!)
            }
            log(TAG, "mTransaction !!! --> " + gson.toJson(mTransaction))
            mTransactionDAO!!.close()
            performNextStepProductTask(true)
        } catch (ex: SQLiteException) {
            ex.printStackTrace()
            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
            // mViewModel.generateLogs(ex.message!!,0)
        }
    }
    private fun performNextStepProductTask(isTrue:Boolean) {
        when {
            isTrue -> {
                goToPayment(selectedProduct)
            }
            daoError -> {
                UtilsCardInfo.beep(mCore, 10)
                showDialog(resources.getString(R.string.error),getString(R.string.data_error))
            }
            else -> {
                showDialog(resources.getString(R.string.error), resources.getString(R.string.no_product))
            }
        }
        showProgress(false)
    }

    private fun startFuelPosReceiver() {
        if (!FuelPosService.isRunning(this)) FuelPosService.start(this)
        val filter = IntentFilter()
        filter.addAction(ACTION_FUEL_POS_REPLY)
        filter.addAction(ACTION_FUEL_POS_MESSAGE)
        filter.addAction(ACTION_FUEL_POS_CLIENT_STATE)
        filter.addAction(ACTION_POS_MESSAGE)
        registerReceiver(fuelPosReceiver, filter)
        log(TAG, "FuelPOS receiver started")

        FuelPosService.startExtPosClient()
    }
    private fun stopFuelPosReceiver() {
        try {
            unregisterReceiver(fuelPosReceiver)
            log(TAG, "FuelPos receiver stopped")
            FuelPosService.stopExtPosClient()
        } catch (e: java.lang.Exception) {
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            //  mViewModel.generateLogs(e.message!!,0)
        }
    }
    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action
            log(TAG, "FUELPOS ACTION $action")
            var message: String? = ""
            when (action) {
                ACTION_FUEL_POS_REPLY -> {
                    message = intent.getStringExtra(FUEL_POS_REPLY)
                    log(TAG, "POS MESSAGE: $message")
                }
                ACTION_FUEL_POS_CLIENT_STATE ->{
                    message = intent.getStringExtra(FUEL_POS_CLIENT_STATE)
                    if(message == "TIMEOUT" || (message!=null && message.contains("TIMEOUT"))){
                        showToast(getString(R.string.unable_to_connect_fcc)+"CONNECTION TIMEOUT")
                        showNoTrxLayout(true)
                        mBinding.tvFccConnectionMessage.text = getString(R.string.unable_to_connect_fcc)+" CONNECTION TIMEOUT"
                    }
                }
                ACTION_POS_MESSAGE -> {
                    message = intent.getStringExtra(POS_MESSAGE)
                    log(TAG, "POS MESSAGE: $message")
                    if (message!!.contains(FuelPosReply.FILLING_STATES)) {
//                        stopFuelPos()
                        stopFuelPosReceiver()
                        val transactions: java.util.ArrayList<PosTransaction> = FuelPosReply.getPosTransactions(message)
                        //log(TAG, "POS TRX: " + Gson().toJson(transactions))
                        UtilsCardInfo.Log(TAG,  "POS TRX: " + Gson().toJson(transactions))
                        if (transactions.isEmpty()) {
                            showNoTrxLayout(true)
                            mBinding.progressMessage.text =  getString(R.string.fetching_latest_transactions)
                        } else {
                            val transactionDao = TransactionDao()
                            transactionDao.open()
                            decimal = fuelpos.totalAmountDecimal ?: fuelpos.decimal
                            if (BuildConfig.DEBUG) decimal = 2
                            val pumpsModels: List<RFIDPumpsModel> = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
                            if (pumpsModels != null && pumpsModels.isNotEmpty()) {
                                for (transaction in transactions) {
                                    if (transaction.fillingState == PosFillingState.PAYABLE || transaction.fillingState == PosFillingState.PAYABLE_AGAIN){
                                        for (pump in pumpsModels) {
                                            if (transaction.pumpNumber == pump.pump_number.toString()) {
                                                for(nozzle in pump.nozzles)
                                                {
                                                    if(nozzle.nozzle_number.toString() == transaction.nozzleNumber)
                                                    {
                                                        if(transaction.fillingVolume.toDouble() != 0.0)
                                                        {
                                                           // val referenceTransaction: String =  transaction.pumpNumber + ":" +transaction.fillingId + ":"+ transaction.trxChecksum + mTerminal!!.terminalId + mTerminal!!.stationId
                                                            if (transactionDao.checkTransactionCount(transaction.trxChecksum) == 0) {
                                                                addFCCTransactionList(transaction)
                                                            }
                                                        }
                                                    }
                                                }

                                            }
                                        }
                                    }
                                }
                                getFccTransactions()
                            } else {
                                gotoAbortMessageActivity(resources.getString(R.string.error),getString(R.string.pump_not_assigned_terminal))
                            }
                            transactionDao.close()
                        }
                    }
                    else if (message.contains(FuelPosReply.LOGIN_RESULT)){
                        val result = FuelPosReply.getLoginResult(message)
                        if(result!!.completionCode != PosCompletionCode.OK){
                            val loginStatus = PosCompletionCode.getMessage(result.completionCode)
                            showToast("${result.completionCode} : $loginStatus")
                            showNoTrxLayout(true)
                            mBinding.tvFccConnectionMessage.text = "${result.completionCode} : $loginStatus"
                        }
                    }
                }
                ACTION_FUEL_POS_MESSAGE -> {
                    message = intent.getStringExtra(FUEL_POS_MESSAGE)
                    log(TAG, "FUEL_POS_MESSAGE: $message")
                }

            }
        }
    }
    private fun addTransactionsInDB(fusionTransaction:TransactionFromFcc) {
        mBinding.progressMessage.text = ""
        mBinding.progressMessage.visibility = View.VISIBLE
        fuelTransactionStatusDAO = FCCTransactionsDao()
        fuelTransactionStatusDAO!!.open()
        fuelTransactionStatusDAO!!.insert(fusionTransaction)
        fuelTransactionStatusDAO!!.close()
    }
    fun getFccTransactions()
    {
        fuelTransactionStatusDAO = FCCTransactionsDao()
        fuelTransactionStatusDAO!!.open()
        fusionFuelTrxList = fuelTransactionStatusDAO!!.getAllTransactionList()
        fuelTransactionStatusDAO!!.close()
        if (fusionFuelTrxList.isNotEmpty()) {
            var size = fusionFuelTrxList.size
            if (fusionFuelTrxList.size > 20) size = 20 //Commented this for missing trx
            for (i in 0 until size) {
                transactionList.add(fusionFuelTrxList[i])
            }
        }
        updateRecyclerView()
    }
    private fun addFCCTransactionList(transaction: PosTransaction) {
        try {
            val totalDecimal = fuelpos.totalAmountDecimal ?: 2
            val qtyDecimal = fuelpos.quantityDecimal ?: 2
            val uniteDecimal = fuelpos.unitPriceDecimal ?: 2
            mProduitDAO = ProductsDao()
            mProduitDAO!!.open()
            val product: ProductModel? = mProduitDAO!!.getProductById(transaction.fillingFuelNumber.toInt()) //product id and filling fuel number should be same in both fuel pos and fbs for product
            mProduitDAO!!.close()
            if (product != null) {
                val fuelPosTransaction = TransactionFromFcc()
                fuelPosTransaction.pump = transaction.pumpNumber.toInt()
                var nozzle = 0
                try {
                    nozzle = transaction.nozzleNumber.toInt()
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                val referenceTransaction: String =  transaction.pumpNumber + ":" +transaction.fillingId + ":"+ transaction.trxChecksum.toString() + /*":" +*/ + mTerminal!!.terminalId + mTerminal!!.stationId /*+ ":" + transaction.getMessageSequence()*/
                fuelPosTransaction.hose = nozzle
                fuelPosTransaction.fusionSaleId = transaction.trxChecksum
                fuelPosTransaction.ref_transaction = referenceTransaction
                fuelPosTransaction.releaseToken = transaction.trxChecksum
                fuelPosTransaction.fccProductId = product.fcc_prod_id
                fuelPosTransaction.amount = toDecimalValue(transaction.fillingAmount, totalDecimal)
                fuelPosTransaction.quantite = toDecimalValue(transaction.fillingVolume, qtyDecimal)
                fuelPosTransaction.pu = toDecimalValue(transaction.unitPrice, uniteDecimal)
                fuelPosTransaction.produit = product.libelle
                fuelPosTransaction.fuelQtyUnit = fuelQtyUnit
                fuelPosTransaction.dh_transaction = Support.getFormatTrxDate(Date())
                fuelPosTransaction.currency = prefs.currency
                fuelPosTransaction.productId = product.productID
                fuelPosTransaction.hsCode = product.hs_code
                if (fuelVat.enabled) {
                  val isInclusive = fuelVat.type == 0
                   taxModel = TaxUtils.calculate(
                        transaction.fillingAmount.toDouble(),
                       fuelVat.percentage!!.toDouble(),
                       isInclusive
                    )
                   vatAmount = Support.formatString(taxModel!!.taxAmount)!!.toDouble()
                }
                fuelPosTransaction.vatAmount = vatAmount.toString()
                addTransactionsInDB(fuelPosTransaction)
            } else {
                log(TAG, "Product not found in product table ")
            }
            mBinding.progressBarLayout.visibility = View.VISIBLE
        } catch (e: Exception) {
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
        }
    }
    //endregion

    override fun onBackPressed() {

    }

    private fun showNoTrxLayout(isVisible: Boolean, title:String ="") {
        mBinding.tvFccConnectionMessage.text = getString(R.string.no_transaction_found)
        if (isVisible) {
            mBinding.noTransactionLayout.visibility = View.VISIBLE
        } else {
            //fccConnectionProgressBar(false);
            mBinding.noTransactionLayout.visibility = View.GONE
            if(title.isNotEmpty() ){
                mBinding.tvFccConnectionMessage.text = title.replace(":","")
            }
        }
    }
    fun updateRecyclerView() {
        showProgress(false)
        try {
            if (transactionList.isNotEmpty()) {
                Collections.sort(transactionList, TransactionFromFccComparator())
                mOperationsAdapter = RecyclerViewArrayAdapter( transactionList, this)
                mBinding.mListView.adapter = mOperationsAdapter
                mOperationsAdapter.notifyDataSetChanged()
                clearFccFuelTrxList()
            } else {
                showNoTrxLayout(true)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    fun noFccConnLayoutButtonClick(view: View) {
        setBeep()
        when(view){
            mBinding.btnRefreshTrx -> {
                showNoTrxLayout(false)
                showProgress(true)

                refreshTransactions()
            }
            mBinding.btnOfflinePage -> {
                gotoOfflineModePage()
            }
            mBinding.btnHome -> {
                val mIntent: Intent =
                    if (prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                        Intent(this, UnattendantModePayActivity::class.java)
                    } else {
                        Intent(this, MenuActivity::class.java)
                    }

                startActivity(mIntent)
                // finish()
            }
        }
    }

}
