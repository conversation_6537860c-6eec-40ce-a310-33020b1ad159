package app.rht.petrolcard.ui.menu.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.reference.model.BaseUrlModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import java.io.File


class MenuViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {

    var urlObserver = MutableLiveData<BaseResponse<BaseUrlModel>>()
    var teleCollectObserver = MutableLiveData<BaseResponse<String>>()
    var lastTransactionObserver = MutableLiveData<BaseResponse<String>>()


//     fun sendTeleCollectFile(key: String,hash:String,file: File) {
//         //log("sendTeleCollectFile",preferencesHelper.baseUrl+ NetworkRequestEndPoints.SEND_TELECOLLECT_FILE)
//        requestData(mNetworkService.sendTeleCollectFile(url = preferencesHelper.baseUrl+ NetworkRequestEndPoints.SEND_TELECOLLECT_FILE,
//            file = createFormData(file, "file", "*/*"),sn = createFormData(MainApp.sn!!),key = createFormData(key), hash = createFormData(hash)),
//            {
//                teleCollectObserver.postValue(it)
//            })
//    }
//
//    fun sendLastTransaction(date:String) {
//        requestData(mNetworkService.sendLastTrx(url =preferencesHelper.baseUrl+ NetworkRequestEndPoints.SEND_LAST_TRANSACTION,MainApp.sn!!,date,BuildConfig.VERSION_NAME),
//            {
//                lastTransactionObserver.postValue(it)
//            })
//    }

}
