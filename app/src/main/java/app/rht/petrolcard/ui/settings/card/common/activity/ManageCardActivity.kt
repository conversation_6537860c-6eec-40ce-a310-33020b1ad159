package app.rht.petrolcard.ui.settings.card.common.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityManageCardBinding
import app.rht.petrolcard.ui.badge.activity.BadgeActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.card.common.model.CardItemModel
import app.rht.petrolcard.utils.constant.AppConstant
import kotlinx.android.synthetic.main.toolbar.view.*
import java.util.ArrayList

import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.settings.card.pendingtrx.activity.PendingRefundsActivity
import app.rht.petrolcard.ui.settings.card.unblockcard.activity.UnblockActivity
import app.rht.petrolcard.ui.settings.card.unlockpin.activity.EnterNewPinActivity
import app.rht.petrolcard.ui.updatecard.activity.UpdateCardUtility
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.constant.Workflow.SETTINGS_UPDATE_MILEAGE
import app.rht.petrolcard.utils.helpers.MultiClickPreventer


class ManageCardActivity: BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<CardItemModel> {

    private lateinit var mBinding: ActivityManageCardBinding

    private val TAG = SettingsActivity::class.simpleName

    private val RECHARGE_ITEM = "recharge_card"
    private val TRANSACTION_ITEM = "card_transaction"
    private val UPDATE_CARD_ITEM = "update_card"
    private val UNBLOCK_CARD_ITEM =  "umclock_card"
    private val CHANGE_PIN_ITEM =  "change_pin"
    private val UNBLOCK_CARD_PIN_ITEM =  "unblock_pin"
    private val CARD_SYNC_ITEM =  "card_sync"
    private val UPDATE_CARD_TYPE =  "update_card_type"
    private val PENDING_REFUND =  "peding_refund"
    private val IMPORT_INJECT_KEY =  "import_inject_key"
    private val UPDATE_MILEAGE =  "update_mileage"
    private var intentExtrasModel: IntentExtrasModel? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_manage_card)

        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_manage_card)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        intentExtrasModel = IntentExtrasModel()
        setupItemsInList()

        getKeyContext()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.card_management)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish()
        }
    }

    override fun setObserver() {
        mViewModel.injectkeyObserver.observe(this) {
            log(TAG, "Inject Key response :: $it")
            importInjectKey(it!!)
            if(prefs.isKeyImported)
            {
                gotoSuccessMessageActivity(getString(R.string.success),getString(R.string.inject_key_import_success),ManageCardActivity::class.java)
            }
            else
            {
                gotoAbortMessageActivity(getString(R.string.failed_to_import_inject_key_title),getString(R.string.contact_rising_hightech),ManageCardActivity::class.java)
            }
        }
    }
    var activityResultLaunch = registerForActivityResult(
        StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppConstant.CARD_NFC_BADGE) {
            if(result.data != null)
            {
                intentExtrasModel!!.tagsNFC = result.data!!.getStringExtra("tagsNFC")
            }
            if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD) {
                gotoCheckRestrictions()
            }
            else if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_UPDATE_MILEAGE)
            {
                val intent = if (prefs.getStationModel()!!.mode_pompiste == "CODE") {
                    Intent(this, AttendantCodeActivity::class.java)
                } else {
                    Intent(this, AttendantTagActivity::class.java)
                }
                intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
                intentExtrasModel!!.mTransaction = TransactionModel()
                 val authorizedManagerID = result.data!!.getIntExtra("authorizedManagerID",0)
                intentExtrasModel!!.mTransaction!!.authorizedManagerID = authorizedManagerID
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(intent)
            }


        }
    }
    fun gotoCheckRestrictions()
    {
        val i = Intent(this, CheckCardRestrictionsActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }

    fun gotoBadgeActivity()
    {
        val intent= Intent(this, BadgeActivity::class.java)
        activityResultLaunch.launch(intent)
    }
    fun gotoUnblockActivity()
    {
        val intent= Intent(this, UnblockActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
    }
    fun gotoUnlockActivity()
    {
        val intent= Intent(this, EnterNewPinActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
    }

    private fun isRechargeAvailable(): Boolean {
        var result = false
        try {
            val mProduitDAO = ProductsDao()
            mProduitDAO.open()
            val mProduct = mProduitDAO.getProductById(PRODUCT.RECHARGE)
            mProduitDAO.close()
            result = mProduct != null
        } catch (e:Exception){
           setBeep()
            gotoAbortMessageActivity(getString(R.string.error),getString(R.string.recharge_option_is_not_available_on_this_station))
        }
        return result
    }

    override fun onItemClick(view: View, item: CardItemModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        when(item.id){
            RECHARGE_ITEM -> {
                intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS_RECHARGE_CARD
                gotoBadgeActivity()
            }
            UPDATE_MILEAGE -> {
                intentExtrasModel!!.workFlowTransaction = SETTINGS_UPDATE_MILEAGE
                gotoBadgeActivity()
            }
            TRANSACTION_ITEM -> {
                intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS_CARD_HISTORY
                gotoCheckRestrictions()
            }
            UPDATE_CARD_ITEM -> {
                intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS_CARD_UPDATE
                intentExtrasModel!!.isCardUpdate = true
                gotoCheckRestrictions()
            }
            UNBLOCK_CARD_ITEM -> {gotoUnblockActivity()}
            CHANGE_PIN_ITEM -> {
                intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS_CARD_CHANGE_PIN
                gotoCheckRestrictions()
            }
            UNBLOCK_CARD_PIN_ITEM -> {gotoUnlockActivity()}
            CARD_SYNC_ITEM -> {}
            IMPORT_INJECT_KEY -> {
                mViewModel.importInjectKey()
            }
            UPDATE_CARD_TYPE -> {
                val intent= Intent(this, UpdateCardUtility::class.java)
                intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(intent)
            }
            PENDING_REFUND -> {
                val intent= Intent(this, PendingRefundsActivity::class.java)
                startActivity(intent)
            }
        }
    }

    private var settingsItems : ArrayList<CardItemModel> = ArrayList()
    private fun setupItemsInList(){
        val terminalConfig = prefs.getReferenceModel()!!.terminalConfig
        if(terminalConfig?.cardSettings != null){
            val cardSettings = terminalConfig.cardSettings
            if(cardSettings!!.recharge!! && isRechargeAvailable())
                settingsItems.add(CardItemModel(R.drawable.ic_recharge,getString(R.string.recharge),RECHARGE_ITEM,"#F97F51"))
            if(cardSettings.transactionHistory!!)
                settingsItems.add(CardItemModel(R.drawable.ic_transaction_history,getString(R.string.transaction_history),TRANSACTION_ITEM,"#1B9CFC"))
            if(cardSettings.updateCard!!)
                settingsItems.add(CardItemModel(R.drawable.ic_update_card, getString(R.string.update_card),UPDATE_CARD_ITEM,"#3B3B98"))
            if(cardSettings.unblockCard!!)
                settingsItems.add(CardItemModel(R.drawable.ic_unblock_card,getString(R.string.unblock_card),UNBLOCK_CARD_ITEM,"#FC427B"))
            if(cardSettings.changePin!!)
                settingsItems.add(CardItemModel(R.drawable.ic_change_pin, getString(R.string.change_pin),CHANGE_PIN_ITEM,"#ff3f34"))
            if(cardSettings.cardUnlock!!)
                settingsItems.add(CardItemModel(R.drawable.ic_unblock_card_pin, getString(R.string.unblock_pin),UNBLOCK_CARD_PIN_ITEM,"#B33771"))
            if(cardSettings.importInjectKey!!)
                settingsItems.add(CardItemModel(R.drawable.ic_key_card, getString(R.string.import_inject_key),IMPORT_INJECT_KEY,"#F97F51"))
            if(cardSettings.pendingRefund!!)
                settingsItems.add(CardItemModel(R.drawable.ic_refund, getString(R.string.pending_refund) ,PENDING_REFUND,"#e74c3c"))
            try {
                if(cardSettings.updateMileage != null && cardSettings.updateMileage!!)
                    settingsItems.add(CardItemModel(R.drawable.mileage,getString(R.string.update_mileage),UPDATE_MILEAGE,"#1B9CFC"))
            }
            catch (e:Exception) { e.printStackTrace()}

        }
        else {
            settingsItems.add(CardItemModel(R.drawable.ic_recharge,getString(R.string.recharge),RECHARGE_ITEM,"#F97F51"))
            settingsItems.add(CardItemModel(R.drawable.ic_transaction_history,getString(R.string.transaction_history),TRANSACTION_ITEM,"#1B9CFC"))
            settingsItems.add(CardItemModel(R.drawable.ic_update_card, getString(R.string.update_card),UPDATE_CARD_ITEM,"#3B3B98"))
            settingsItems.add(CardItemModel(R.drawable.ic_unblock_card,getString(R.string.unblock_card),UNBLOCK_CARD_ITEM,"#FC427B"))
            settingsItems.add(CardItemModel(R.drawable.ic_change_pin, getString(R.string.change_pin),CHANGE_PIN_ITEM,"#ff3f34"))
            settingsItems.add(CardItemModel(R.drawable.ic_unblock_card_pin, getString(R.string.unblock_pin),UNBLOCK_CARD_PIN_ITEM,"#B33771"))
            settingsItems.add(CardItemModel(R.drawable.ic_key_card, getString(R.string.import_inject_key),IMPORT_INJECT_KEY,"#F97F51"))
            settingsItems.add(CardItemModel(R.drawable.ic_refund, getString(R.string.pending_refund) ,PENDING_REFUND,"#e74c3c"))


            //settingsItems.add(CardItemModel(R.drawable.ic_card_sync, "Card Sync",CARD_SYNC_ITEM,"#2C3A47"))
        }


        mBinding.rvSettings.removeAllViews()
        val mSettingsAdapter = RecyclerViewArrayAdapter(settingsItems,this)
        mBinding.rvSettings.adapter = mSettingsAdapter
        mSettingsAdapter.notifyDataSetChanged()

    }


}