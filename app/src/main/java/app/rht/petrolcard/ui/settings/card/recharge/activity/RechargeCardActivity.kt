package app.rht.petrolcard.ui.settings.card.recharge.activity

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Paint
import android.os.*
import android.text.Html
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.databinding.ActivityRechargeBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.settings.card.recharge.viewmodel.RechargeViewModel
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.Dw14Printer
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.constant.*
import com.pax.dal.IDAL
import wangpos.sdk4.libbasebinder.BankCard
import java.lang.Exception
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.fuelpos.decimal
import app.rht.petrolcard.utils.tax.TaxModel
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import com.pax.gl.page.IPage
import com.pax.gl.page.PaxGLPage
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.FileInputStream
import java.lang.ref.WeakReference
import java.sql.SQLException
import java.text.SimpleDateFormat
import java.util.*


class RechargeCardActivity : BaseActivity<RechargeViewModel>(RechargeViewModel::class) {
    private var TAG= "RechargeCardActivity"
    var dal: IDAL? = null
    var ret = -1
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""
    var authKey:String? = ""
    var cardType:Int? = 0
    var cardModel: CardStaticStructureModel?=null
    private var isFirstUse = false
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode = 0
    private var mToday: Date? = null
    private var terminalDateErr = false
    private lateinit var mBinding: ActivityRechargeBinding
    private var errorMessage = ""
    var returnValue = 0
    private var infoCreditPostPayee: String? = null
    private var mProduct: ProductModel? = null //mProduitToCheckout2
    private var cardHolderName = ""
    private var mTransaction: TransactionModel? = null
    private var compteurTrxCredit: Long? = null
    private var preCardCeiling: String? = null //plafondCartePre
    private var PLF_MIN_PREPAYE = 0.0
    private var PLF_MAX_PREPAYE = 0.0
    var ticketType = 1
    private var receiptDate = ""
    private var customerMessage = ""
    private var customerCopy = ""
    private var merchantCopy = ""
    var receiptBitmap:Bitmap?=null
    var preferenceModel:PreferenceModel?=null
    var islimitPrint = false

    var referenceModel : ReferenceModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_recharge)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        getInitIntentExtras()
        mToday = Support.getDateComparison(Date())

        setupToolbar()

        preferenceModel = prefs.getPreferenceModel()
        referenceModel = prefs.getReferenceModel()

        val readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
        mBinding.message.text =getString(R.string.recharge_process)


    }
    private fun setupToolbar()
    {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.recharge_verification)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
            finish()
        }
    }
    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

           if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        log(TAG,"WorkFLOW:: "+intentExtrasModel!!.workFlowTransaction!!)
    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
               mBankCard = BankCard(this@RechargeCardActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                   mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@RechargeCardActivity) //icCpuReader Not Required for BTPE
                    log(TAG, "infoCarte::: $infoCarte")
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19) else return -1 //Abort Transaction

                    getCardStaticStructure()
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@RechargeCardActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@RechargeCardActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        return if(intentExtrasModel!!.panNumber != panNumber) {
                            -3
                        } else {
                            return checkCardValidation()
                        }

                    }
                    else
                    {
                        showSnakeBar(getString(R.string.card_reading_failed))
                    }
                }
            }
            catch (e:Exception)
            {
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
               // mViewModel.generateLogs(e.message!!,0)
                e.printStackTrace()
                return -1
            }
            return returnValue
        }
        override fun onPreExecute() {
        }
        override fun onPostExecute(result: Int?) {
            when (result) {
                -1 -> {
                    UtilsCardInfo.beep(mCore, 10)
                    var title = resources.getString(R.string.error)
                    var msg = getString(R.string.transaction_cancelled)
                    if (terminalDateErr) {
                        title = resources.getString(R.string.TPE_DATE_ERROR_SYNCH)
                        msg = resources.getString(R.string.TPE_DATE_ERROR)
                    } else {
                        if (panNumber != null)
                            msg = resources.getString(R.string.card_changed)
                        else resources.getString(R.string.read_card_failed)
                    }
                    gotoAbortMessageActivity(title, msg)
                }
                -2 ->{
                    val title = resources.getString(R.string.error)
                    gotoAbortMessageActivity(title, errorMessage)
                }
                -3 ->{
                    gotoAbortMessageActivity(getString(R.string.transaction_cancelled), getString(R.string.card_is_changed))
                }
                0 -> {
                    val title = resources.getString(R.string.error)
                    val msg = getString(R.string.failed_update)
                    gotoAbortMessageActivity(title, msg)
                }
                1 -> {
                    mViewModel.rechargeCard(intentExtrasModel!!.panNumber!!,intentExtrasModel!!.amount!!)
                }
            }


        }
        override fun onProgressUpdate(vararg values: IntArray) {

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }

    fun getCardStaticStructure()
    {
        if(infoCarte != null && panNumber!!.isNotEmpty())
        {
            cardModel = UtilsCardInfo.readCardStaticStructureInfo(mBankCard,icCpuReader,this)
            val jsonData= (gson.toJson(cardModel))
            "Card Static Structure : $jsonData".also { log( TAG,it) }
            cardType = cardModel!!.cardType.toInt()
            intentExtrasModel!!.verificationType=cardModel!!.verificationType.toInt()
            if(cardModel!!.cardStatus == "F")
            {
                isFirstUse = true
            }
        }
    }
    fun checkCardValidation():Int
    {
        cardHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this)!!
        if (UtilsCardInfo.verifyPIN(mBankCard, icCpuReader, intentExtrasModel!!.mPinNumberCard , this)) {
            preCardCeiling = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "05", "28", this).replace("F", "0")
            PLF_MIN_PREPAYE = UtilsCardInfo.getPreCardCeiling(preCardCeiling!!,
                0,
                12
            ) //PLF MIN PREPAYE of the card ---->
            log(TAG,"MIN MAX CARD LIMIT $PLF_MIN_PREPAYE ===== $PLF_MAX_PREPAYE")
            PLF_MAX_PREPAYE = UtilsCardInfo.getCardCeilings(preCardCeiling!!, 12, 24) //PLF_MAX_PREPAYE of the card
            if (intentExtrasModel!!.amount!!.toDouble() in PLF_MIN_PREPAYE..PLF_MAX_PREPAYE) { // hardcode
                returnValue = 1
            } else {
                errorMessage = resources.getString(R.string.amount_pay) + " " + intentExtrasModel!!.amount + " " + prefs.currency + " " + resources.getString(
                        R.string.amount_hors_plafond
                    )
                returnValue = -2
                return -2
            }
        }
        else
        {
            return -1
        }
        return returnValue
    }
    override fun setObserver() {
        mViewModel.rechargeDataObserver.observe(this) {
            if (it.reponse != null && it.reponse == "1") {
                successRecharge()
            } else {
                gotoAbortMessageActivity(getString(R.string.recharge_failed), it.error!!)
            }

        }
    }
    private fun getProductDetailsByCategory() {
        try {
            val mProduitDAO = ProductsDao()
            mProduitDAO.open()

            //mProduct = mProduitDAO.getProductDetailsByCode("RHG")
            mProduct = mProduitDAO.getProductById(PRODUCT.RECHARGE)

            mProduitDAO.close()
            if (mProduct == null) {
                UtilsCardInfo.beep(mCore, 40)
                gotoAbortMessageActivity(getString(R.string.error),resources.getString(R.string.transaction_failure))
            }
        } catch (ex: Exception) {
            ex.stackTrace
            UtilsCardInfo.beep(mCore, 40)
        }
    }
    private fun successRecharge()
    {

        val ammtHex = Utils.amountToHex(intentExtrasModel!!.amount!!)
        try {
            assignKeyForCard(panNumber!!)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        val nowDate = Support.dateToStringX(Date())

        var rechargeID = "17"
        if(mProduct!=null)
        {
            rechargeID = mProduct!!.productID.toString()

        }


        /*if()*/

        val transData = Utils.padZero(nowDate, 14).toString() + "01" + Utils.padZero(preferenceModel!!.stationID.toString() + "", 4) +"00${rechargeID}FFFFFFFFFFFFFFFFFFFFFFFF" //"0017FFFFFFFFFFFFFFFFFFFFFFFF" //"0010FFFFFFFFFFFFFFFFFFFFFFFF"

        if (UtilsCardInfo.verifyPIN(mBankCard, icCpuReader,  intentExtrasModel!!.mPinNumberCard, this)) {
            getProductDetailsByCategory()
            try {
                if (UtilsCardInfo.credit(mBankCard, icCpuReader, ammtHex, authKey, transData, this)){
                    infoCreditPostPayee = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "06", "32", this).replace("F", "0")
                    compteurTrxCredit = UtilsCardInfo.getCompteurTransactionPanCredit(infoCreditPostPayee!!)
                    UtilsCardInfo.updateCreditPostPayee(mBankCard, icCpuReader, infoCreditPostPayee, Support.dateToStringPlafondCompt(Date()),
                        intentExtrasModel!!.amount!!,
                        compteurTrxCredit!! + 1,
                        this
                    )
                    saveTransactionOfflineModel()
                    printTicket()
                } else {
                    UtilsCardInfo.beep(mCore, 40)
                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.error_while_refilling_card))
                }
            } catch (ex: Exception) {
                ex.printStackTrace()
                UtilsCardInfo.beep(mCore, 40)
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.transaction_failed))
            }
        }

    }

    private fun printTicket()
    {
        ticketConfig()
        mBinding.paymentSuccess.visibility =View.GONE
        mBinding.previewLayout.visibility =View.VISIBLE
        mBinding.successLayout.visibility =View.GONE
        receiptBitmap=getPrintBitmapLayout2()
        when (prefs.getReferenceModel()!!.PRINT_RECEIPT) {
            AppConstant.PRINT_RECEIPT_MANDATORY -> {
                printCustomerTicket()
            }
            AppConstant.PRINT_RECEIPT_REQUIRED -> {
                showCustomerPrintDialog()
            }
            else -> {
                Handler(Looper.getMainLooper()).postDelayed({
                    gotoMenuActivity()
                }, 2000)
            }
        }

    }
    private fun ticketConfig(){
        val terminalConfig = prefs.getReferenceModel()!!.terminalConfig
        if(terminalConfig!=null){
            val receiptSetting = terminalConfig.receiptSetting
            if(receiptSetting!=null){
                if(!receiptSetting.dateFormat.isNullOrEmpty()){
                    val sdf = SimpleDateFormat(receiptSetting.dateFormat!!)
                    val currentDate = sdf.format(Date())
                    receiptDate =  currentDate
                }

                if(!receiptSetting.customerMessage.isNullOrEmpty()){
                    customerMessage = receiptSetting.customerMessage!!
                }

                if(!receiptSetting.customerCopy.isNullOrEmpty()){
                    customerCopy = receiptSetting.customerCopy!!
                }

                if(!receiptSetting.merchantCopy.isNullOrEmpty()){
                    merchantCopy = receiptSetting.merchantCopy!!
                }
            }
        }
        receiptDate =  if(!receiptDate.isNullOrEmpty()) receiptDate else mTransaction!!.dateTransaction!!
        log(TAG, "receiptDate:: $receiptDate")
    }

    //region iPaxGL Page moved here from base activity
    private lateinit var iPaxGLPage: PaxGLPage
    private lateinit var receiptLayout2: IPage
    private fun initPrinterLayout2()
    {
        iPaxGLPage = PaxGLPage.getInstance(applicationContext)
        receiptLayout2 = iPaxGLPage.createPage()
    }
    private fun addLine(text1:String,text2:String,size: Int,style: Int): IPage.ILine {
        var actualSize=size
        if(size != 0) {
            when (size) {
                1 -> {
                    actualSize = FONT_SMALL
                }
                2 -> {
                    actualSize = FONT_NORMAL
                }
                3 -> {
                    actualSize= FONT_BIG
                }
                4 -> {
                    actualSize = FONT_BIGEST
                }
                5 -> {
                    actualSize = FONT_XTRA_SMALL
                }
            }
        }
        val line= receiptLayout2.addLine()
        if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this)) {
            val unit1 = receiptLayout2.createUnit()
            unit1.textStyle=style
            unit1.fontSize=actualSize
//            unit1.align= IPage.EAlign.RIGHT
            unit1.text=text2
            line.addUnit(unit1)
            val unit2 = receiptLayout2.createUnit()
            unit2.textStyle=style
            unit2.fontSize=actualSize
//            unit2.align= IPage.EAlign.RIGHT
            unit2.text=text1
            line.addUnit(unit2)
        }
        else
        {
            val unit1 = receiptLayout2.createUnit()
            unit1.textStyle=style
            unit1.fontSize=actualSize
            unit1.align= IPage.EAlign.LEFT
            unit1.text=text1
            line.addUnit(unit1)
            val unit2 = receiptLayout2.createUnit()
            unit2.textStyle=style
            unit2.fontSize=actualSize
            unit2.align= IPage.EAlign.RIGHT
            unit2.text=text2
            line.addUnit(unit2)
        }
        return line
    }
    private fun addReceiptUnit(text:String,align: IPage.EAlign?,size: Int,style: Int): IPage.ILine.IUnit {
        val unit = receiptLayout2.createUnit()
        if(align != null)
        {
            unit.align = align
        }
        if(text.isNotEmpty())
        {
            unit.text = text
        }
        if(style != 0)
        {
            unit.textStyle = style
        }
        if(size != 0) {
            when (size) {
                1 -> {
                    unit.fontSize = FONT_SMALL
                }
                2 -> {
                    unit.fontSize = FONT_NORMAL
                }
                3 -> {
                    unit.fontSize = FONT_BIG
                }
                4 -> {
                    unit.fontSize = FONT_BIGEST
                }
                5 -> {
                    unit.fontSize = FONT_XTRA_SMALL
                }
            }
        }
        return unit
    }
    //endregion

    fun getPrintBitmapLayout2(): Bitmap? {
        initPrinterLayout2()
        val TEXT_STYLE_NORMAL = 0
        val TEXT_STYLE_BOLD = 1
        val receiptSetting = prefs.getReferenceModel()!!.terminalConfig!!.receiptSetting!!
        val totalDecimal = receiptSetting.totalAmountDecimal ?: 2
        var mTerminal = prefs.getReferenceModel()!!.terminal
        val limitDecimal = receiptSetting.limitDecimal ?: 2
        val fiscalId =mTerminal!!.fiscalId
        var dateFormated: String? = mTransaction!!.dateTransaction
        if (dateFormated == null || !dateFormated.contains(" ")) {
            dateFormated = Support.dateToString(Date())
        }

        val dateArray = dateFormated!!.split(" ").toTypedArray()
        val date = dateArray[0]
        val time = dateArray[1]
        if(prefs.getReferenceModel()!!.receiptFormatFields != null)
        {
            val fields = prefs.getReferenceModel()!!.receiptFormatFields

            //Header Format
            for(fd in fields!!.receipt_header)
            {
                if(fd.is_available == 1)
                {
                    if(fd.label_type ==  ReceiptFields.LOGO_R)
                    {
                        var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                        bitmap = Support.getResizedBitmap(bitmap,200,200)
                        receiptLayout2.addLine().addUnit(bitmap, IPage.EAlign.CENTER)
                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_STATION_NAME && mTerminal != null)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(mTerminal.stationName!!, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_ADDRESS)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(mTerminal.address+", "+ mTerminal.city, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.DATE)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit("$receiptDate $time", IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(fd.label, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                }
            }
            receiptLayout2.addLine().addUnit(addReceiptUnit("", IPage.EAlign.CENTER, 0,0))
            for(rm in fields.receipt_middle)
            {
                if(rm.is_available == 1)
                {
                    when  {
                        rm.label_type ==  ReceiptFields.TRX_ID && !mTransaction!!.reference.isNullOrEmpty()   -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label+" : "+mTransaction!!.reference,
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_NORMAL))
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_NAME && ! mTransaction!!.productName.isNullOrEmpty()  -> {
                            addLine(rm.label,"${ mTransaction!!.productName}",rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.TRANSACTION_BY && !intentExtrasModel!!.attendantName.isNullOrEmpty()  -> {
                            addLine(rm.label,intentExtrasModel!!.attendantName!!,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.PAN && !mTransaction!!.pan.isNullOrEmpty()  -> {
                            addLine(rm.label,Support.hashPan(mTransaction!!.pan),rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.TAG_NFC && !mTransaction!!.tagNFC.isNullOrEmpty()-> {
                            addLine(rm.label,"${mTransaction!!.tagNFC}",rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.PAYMENT_TYPE && !mTransaction!!.modepay.isNullOrEmpty()  -> {
                            addLine(rm.label,"${getModePayment(mTransaction!!.modepay)}",rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type == ReceiptFields.DATE  && !receiptDate.isNullOrEmpty()  -> {
                            addLine(rm.label,receiptDate,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type == ReceiptFields.TIME  && !time.isNullOrEmpty()  -> {
                            addLine(rm.label,time,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && mTransaction!!.productName != null && mTransaction!!.amount != null -> {
                            receiptLayout2.addLine()
                                .addUnit(addReceiptUnit(mTransaction!!.productName!!,
                                    IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(Support.getFormattedValue(this,mTransaction!!.amount!!.decimal(totalDecimal)),
                                    IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.AMOUNT && mTransaction!!.amount != null  -> {
                            addLine(rm.label,Support.getFormattedValue(this,mTransaction!!.amount!!.decimal(totalDecimal)),rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.PAN_HOLDER_NAME && !mTransaction!!.nomPorteur.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("---------------------------------------------------",IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                            addLine(rm.label,"${mTransaction!!.nomPorteur}",rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.HEADER_2 -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("",
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.TOTAL && mTransaction!!.amount != null  -> {
                            addLine(rm.label,mTransaction!!.amount!!.toString(),rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.LINE -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label, IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.TERMINAL_SN && !MainApp.sn.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${MainApp.sn}",
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_NORMAL))
                        }
                        rm.label_type ==  ReceiptFields.PAN_LIMIT_RECHARGE && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                            islimitPrint = true
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            receiptLayout2.addLine().addUnit()
                        }
                        rm.label_type ==  ReceiptFields.DAILY_LIMIT && islimitPrint && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {

                            val dayLmt = Support.getFormattedValue(this,mTransaction!!.dailyCeiling!!.decimal(limitDecimal))
                            addLine(rm.label,dayLmt,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.WEEKLY_LIMIT && islimitPrint && !mTransaction!!.weeklyCeiling.isNullOrEmpty()  -> {

                            val weekLmt = Support.getFormattedValue(this,mTransaction!!.weeklyCeiling!!.decimal(limitDecimal))
                            addLine(rm.label,weekLmt,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.MONTHLY_LIMIT && islimitPrint && !mTransaction!!.monthlyCeiling.isNullOrEmpty()  -> {
                            val monthLmt = Support.getFormattedValue(this,mTransaction!!.monthlyCeiling!!.decimal(limitDecimal))
                            addLine(rm.label,monthLmt,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.CARD_BALANCE && !mTransaction!!.soldeCard.isNullOrEmpty()  -> {
                            val cardBal = Support.getFormattedValue(this,Support.formatDoubleAffichage(mTransaction!!.soldeCard!!.toDouble())?:"0".decimal(limitDecimal))
                            addLine(rm.label,cardBal,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                        rm.label_type ==  ReceiptFields.FISCAL_ID && !mTerminal.fiscalId.isNullOrEmpty() && !mTerminal.fiscalId.equals("0")   -> {
                            addLine(rm.label,fiscalId!!,rm.font_size,TEXT_STYLE_NORMAL)
                        }
                    }
                }

            }
            receiptLayout2.addLine().addUnit()
            for(rm in fields.receipt_footer)
            {
                if(rm.label_type ==  ReceiptFields.CUSTOMER_MESSAGE )
                {
                    receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                        IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))

                    if(rm.label.isNotEmpty())
                    {
                        if(rm.label.contains("|"))
                        {
                            val msgArray = rm.label.split("|")
                            for(msg in msgArray)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit(msg,
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_NORMAL))
                            }
                        }
                        else
                        {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_NORMAL))
                        }
                    }
                    else
                    {
                        receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(getString(R.string.thanks_for_fueling))
                    }
                    receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                        IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                }
                else if(rm.label_type ==  ReceiptFields.CUSTOMER_RECEIPT_COPY && ticketType == AppConstant.CUSTOMER)
                {
                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,
                        IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_NORMAL))
                }
                else if(rm.label_type ==  ReceiptFields.ATTENDANT_RECEIPT_COPY && ticketType == AppConstant.ATTENDANT)
                {
                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,
                        IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_NORMAL))
                }

            }
            val width = 384
            val receiptBitmap = iPaxGLPage.pageToBitmap(receiptLayout2, width)
            runOnUiThread {  mBinding.ivTicketPreview.setImageBitmap(receiptBitmap)  }

//             TicketPrinter(this).printReceipt(receiptBitmap,2)
            return  receiptBitmap
        }
        else
        {
            return null
        }

    }

    fun getDw14PrintBitmapLayout2():  ArrayList<PrintCmd> {
        val printCommands = ArrayList<PrintCmd>()
        initPrinterLayout2()
        val TEXT_STYLE_NORMAL = 0
        val receiptSetting = prefs.getReferenceModel()!!.terminalConfig!!.receiptSetting!!
        val totalDecimal = receiptSetting.totalAmountDecimal ?: 2
        var mTerminal = prefs.getReferenceModel()!!.terminal
        val limitDecimal = receiptSetting.limitDecimal ?: 2
        val fiscalId =mTerminal!!.fiscalId
        var dateFormated: String? = mTransaction!!.dateTransaction
        if (dateFormated == null || !dateFormated.contains(" ")) {
            dateFormated = Support.dateToString(Date())
        }

        val dateArray = dateFormated!!.split(" ").toTypedArray()
        val date = dateArray[0]
        val time = dateArray[1]
        if(prefs.getReferenceModel()!!.receiptFormatFields != null)
        {
            val fields = prefs.getReferenceModel()!!.receiptFormatFields

            //Header Format
            for(fd in fields!!.receipt_header)
            {
                if(fd.is_available == 1)
                {
                    if(fd.label_type ==  ReceiptFields.LOGO_R) {
                        var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                        bitmap = Support.getResizedBitmap(bitmap,200,200)
                        printCommands.add(PrintCmd(bitmap, PrintContentType.IMAGE))
                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_STATION_NAME && mTerminal != null)
                    {
                        printCommands.add(PrintCmd(referenceModel!!.COMPANY.name,AlignmentType.CENTER,true))
                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_ADDRESS)
                    {
                        printCommands.add(PrintCmd(mTerminal.address+", "+ mTerminal.city,AlignmentType.CENTER,true))

                    }
                    else if(fd.label_type ==  ReceiptFields.DATE)
                    {
                        printCommands.add(PrintCmd("$receiptDate $time",AlignmentType.CENTER,true))
                    }
                    else {
                        printCommands.add(PrintCmd(fd.label,AlignmentType.CENTER,true))
                    }
                }
            }
            receiptLayout2.addLine().addUnit(addReceiptUnit("", IPage.EAlign.CENTER, 0,0))
            for(rm in fields.receipt_middle)
            {
                if(rm.is_available == 1)
                {
                    when  {
                        rm.label_type ==  ReceiptFields.TRX_ID && !mTransaction!!.reference.isNullOrEmpty()   -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]"+mTransaction!!.reference,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_NAME && ! mTransaction!!.productName.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]${ mTransaction!!.productName}",AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.TRANSACTION_BY && !intentExtrasModel!!.attendantName.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]${intentExtrasModel!!.attendantName!!}",AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.PAN && !mTransaction!!.pan.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]${Support.hashPan(mTransaction!!.pan)}",AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.TAG_NFC && !mTransaction!!.tagNFC.isNullOrEmpty()-> {
                            printCommands.add(PrintCmd(rm.label+" :[L]${mTransaction!!.tagNFC}",AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.PAYMENT_TYPE && !mTransaction!!.modepay.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]${getModePayment(mTransaction!!.modepay)}",AlignmentType.LEFT,true))
                        }
                        rm.label_type == ReceiptFields.DATE  && !receiptDate.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]${receiptDate}",AlignmentType.LEFT,true))
                        }
                        rm.label_type == ReceiptFields.TIME  && !time.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && mTransaction!!.productName != null && mTransaction!!.amount != null -> {
                            printCommands.add(PrintCmd(mTransaction!!.productName!!+" :[L]"+Support.getFormattedValue(mTransaction!!.amount!!.decimal(totalDecimal)),AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.AMOUNT && mTransaction!!.amount != null  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]"+Support.getFormattedValue(mTransaction!!.amount!!.decimal(totalDecimal)),AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.PAN_HOLDER_NAME && !mTransaction!!.nomPorteur.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd("-----------------------------",AlignmentType.CENTER,true))
                            printCommands.add(PrintCmd(rm.label+" [L]"+mTransaction!!.nomPorteur,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.HEADER_2 -> {
                            printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.TOTAL && mTransaction!!.amount != null  -> {
                            printCommands.add(PrintCmd(rm.label+" [L]"+mTransaction!!.amount!!.toString(),AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.LINE -> {
                            printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                        }
                        rm.label_type ==  ReceiptFields.TERMINAL_SN && !MainApp.sn.isNullOrEmpty()  -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]"+MainApp.sn,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.PAN_LIMIT_RECHARGE && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                            islimitPrint = true
                            printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.DAILY_LIMIT && islimitPrint && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                            val dayLmt = Support.getFormattedValue(this,mTransaction!!.dailyCeiling!!.decimal(limitDecimal))
                            printCommands.add(PrintCmd(rm.label+" :[L]"+dayLmt,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.WEEKLY_LIMIT && islimitPrint && !mTransaction!!.weeklyCeiling.isNullOrEmpty()  -> {
                            val weekLmt = Support.getFormattedValue(this,mTransaction!!.weeklyCeiling!!.decimal(limitDecimal))
                            printCommands.add(PrintCmd(rm.label+" :[L]"+weekLmt,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.MONTHLY_LIMIT && islimitPrint && !mTransaction!!.monthlyCeiling.isNullOrEmpty()  -> {
                            val monthLmt = Support.getFormattedValue(this,mTransaction!!.monthlyCeiling!!.decimal(limitDecimal))
                            printCommands.add(PrintCmd(rm.label+" :[L]"+monthLmt,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.CARD_BALANCE && !mTransaction!!.soldeCard.isNullOrEmpty()  -> {
                            val cardBal = Support.getFormattedValue(this,Support.formatDoubleAffichage(mTransaction!!.soldeCard!!.toDouble())?:"0".decimal(limitDecimal))
                            printCommands.add(PrintCmd(rm.label+" :[L]"+cardBal,AlignmentType.LEFT,true))
                        }
                        rm.label_type ==  ReceiptFields.FISCAL_ID && !mTerminal.fiscalId.isNullOrEmpty() && !mTerminal.fiscalId.equals("0")   -> {
                            printCommands.add(PrintCmd(rm.label+" :[L]"+fiscalId,AlignmentType.LEFT,true))
                        }
                    }
                }

            }
            receiptLayout2.addLine().addUnit()
            for(rm in fields.receipt_footer)
            {
                if(rm.label_type ==  ReceiptFields.CUSTOMER_MESSAGE )
                {
                    printCommands.add(PrintCmd("*****************************",AlignmentType.CENTER,true))
                    if(rm.label.isNotEmpty())
                    {
                        if(rm.label.contains("|"))
                        {
                            val msgArray = rm.label.split("|")
                            for(msg in msgArray)
                            {
                                printCommands.add(PrintCmd(msg,AlignmentType.CENTER,true))
                            }
                        }
                        else
                        {
                            printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                        }
                    }
                    else
                    {
                        printCommands.add(PrintCmd(getString(R.string.thank_you_for_recharge),AlignmentType.CENTER,true))
                    }
                    printCommands.add(PrintCmd("*****************************",AlignmentType.CENTER,true))
                }
                else if(rm.label_type ==  ReceiptFields.CUSTOMER_RECEIPT_COPY && ticketType == AppConstant.CUSTOMER)
                {
                    printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                }
                else if(rm.label_type ==  ReceiptFields.ATTENDANT_RECEIPT_COPY && ticketType == AppConstant.ATTENDANT)
                {
                    printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                }

            }
            val width = 384
            val receiptBitmap = iPaxGLPage.pageToBitmap(receiptLayout2, width)
            runOnUiThread {  mBinding.ivTicketPreview.setImageBitmap(receiptBitmap)  }
//             TicketPrinter(this).printReceipt(receiptBitmap,2)
        }
        return printCommands
    }

    private fun saveTransactionOfflineModel() {

        mTransaction = TransactionModel()
        mTransaction!!.idTerminal = if (prefs.getReferenceModel()!!.terminal != null) prefs.getReferenceModel()!!.terminal!!.terminalId else 0
        mTransaction!!.idTypeTransaction = 3 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
        mTransaction!!.idProduit = PRODUCT.RECHARGE
        mTransaction!!.categoryId = PRODUCT.RECHARGE
        mTransaction!!.codePompiste = if(intentExtrasModel!!.mPinNumberAttendant!=null) intentExtrasModel!!.mPinNumberAttendant+"" else ""
        mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
        mTransaction!!.dateTransaction = Support.dateToString(Date())
        mTransaction!!.amount = intentExtrasModel!!.amount!!.toDouble()
        mTransaction!!.quantite = 0.0
        mTransaction!!.unitPrice = 0.0
        mTransaction!!.reference = ""
        mTransaction!!.flagController = 0
        mTransaction!!.sequenceController = ""
        mTransaction!!.tagNFC = intentExtrasModel!!.tagsNFC ?: ""//prefs.badge
        mTransaction!!.kilometrage = ""
        mTransaction!!.pan = panNumber
        mTransaction!!.nomPorteur = cardHolderName
        mTransaction!!.dateExp = cardModel!!.expiredDate
        mTransaction!!.flagTelecollecte = 0
        mTransaction!!.modepay = intentExtrasModel!!.typePay
        mTransaction!!.panLoyalty = ""
        mTransaction!!.soldeCard = UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString() + ""
        mTransaction!!.pumpId = ""
        mTransaction!!.reference = "RCH" + Support.generateReference(this,true)/*Support.generateReference(mTransaction!!.dateTransaction, "", "", this)*/
        mTransaction!!.transactionStatus = 1
        if(mProduct != null)
        {
            mTransaction!!.productName= mProduct!!.libelle
        }
        else{
            mTransaction!!.productName= getString(R.string.recharge)
        }

        try {
           insertTransactionData(mTransaction!!)
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun showPaperDialog(){
        MyMaterialDialog(this,
            getString(R.string.error),
            printerStatusMessage,
            positiveBtnText = "Print again",
            negativeBtnText = "Cancel",
            object : MyMaterialDialogListener{
                override fun onPositiveClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    PrintNewFormattedTicket2().execute()
                }

                override fun onNegativeClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    gotoNextActivity()
                    return
                }
            })
    }
    private fun showCustomerPrintDialog(){

        val timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
            }
            override fun onFinish() {
                val unAttendantMode = prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    val mIntent: Intent =
                        if (prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                            Intent(this@RechargeCardActivity, UnattendantModePayActivity::class.java)
                        } else {
                            Intent(this@RechargeCardActivity, MenuActivity::class.java)
                        }

                    startActivity(mIntent)
                    finish()
                }
            }
        }
        if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()
        
        if(prefs.getReferenceModel()!!.PRINT_RECEIPT == AppConstant.PRINT_RECEIPT_REQUIRED)
        {
            MaterialDialog(this)
                .title(text = getString(R.string.recharge_success))
                .message(text = Html.fromHtml("<font color='#000000'>"+getString(R.string.do_you_want_print_customer_receipt)+"</font>"))
                .cancelable(false)
                .show {
                    cornerRadius(res = R.dimen.my_corner_radius)
                    positiveButton(text = getString(R.string.print),click = object : DialogCallback{
                        override fun invoke(dialog: MaterialDialog) {
                            setBeep()
                            timer.cancel()
                            printCustomerTicket()
                        }
                    })
                    negativeButton(text = getString(R.string.cancel), click = object : DialogCallback{
                        override fun invoke(dialog: MaterialDialog) {
                            setBeep()
                            timer.cancel()
                            
                            if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                                gotoMenuActivity()
                            }
                            else
                                showAttendantPrintDialog()
                        }
                    })
                }
        }
        else {
            log(TAG,"Print receipt not required")
            Handler(Looper.getMainLooper()).postDelayed({
                gotoMenuActivity()
            }, 2000)
        }
    }

    private fun printCustomerTicket() {
        ticketType = AppConstant.CUSTOMER
        if (isPrinterPaperAvailable()) {
            PrintNewFormattedTicket2().execute()
        } else {
            showPaperDialog()
        }
    }

    fun showAttendantPrintDialog(){
        //if(prefs.getReferenceModel()!!.PRINT_RECEIPT == AppConstant.PRINT_RECEIPT_REQUIRED) {
            if (prefs.getReferenceModel()!!.TERMINAL_TYPE != AppConstant.UN_ATTENDANT_MODE) {
                MaterialDialog(this)
                    .title(text = getString(R.string.confirm))
                    .message(text = Html.fromHtml("<font color='#000000'>" + getString(R.string.do_you_want_to_print_merchant_receipt) + "</font>"))
                    .cancelable(false)
                    .show {
                        cornerRadius(res = R.dimen.my_corner_radius)
                        positiveButton(
                            text = getString(R.string.print),
                            click = object : DialogCallback {
                                override fun invoke(dialog: MaterialDialog) {
                                    setBeep()
                                    ticketType = AppConstant.ATTENDANT

                                    if(isPrinterPaperAvailable())
                                    {
                                        PrintNewFormattedTicket2().execute()
                                    }
                                    else
                                    {
                                        showPaperDialog()
                                    }
                                }
                            })
                        negativeButton(
                            text = getString(R.string.cancel),
                            click = object : DialogCallback {
                                override fun invoke(dialog: MaterialDialog) {
                                    setBeep()
                                    gotoMenuActivity()
                                }
                            })
                    }
            }
//        } else {
//            log(TAG,"Print receipt not required")
//            mBinding.message.text = getString(R.string.please_wait_saving_transaction_details)
//        }
    }
    private fun gotoMenuActivity() {
        val i = Intent(this, MenuActivity::class.java) //
        startActivity(i)
        finish()
    }

    inner class PrintNewFormattedTicket2 : CoroutineAsyncTask<String, String, Void?>() {
        override fun doInBackground(vararg params: String): Void? {
            try {
                var printerType = referenceModel!!.PRINTER_TYPE
                if(BuildConfig.DEBUG){
                    printerType = AppConstant.DW14_PRINTER
                }
                if(ticketType == AppConstant.CUSTOMER)
                {
                    if(printerType == AppConstant.DW14_PRINTER) {
                        val command = getDw14PrintBitmapLayout2()
                        TicketPrinter(this@RechargeCardActivity).printTicket(command)
                    } else {
                        TicketPrinter(this@RechargeCardActivity).printReceipt(receiptBitmap!!, prefs.getReferenceModel()!!.terminalConfig!!.receiptSetting!!.receipt_layout!!)
                    }
                }
                else
                {
                    if(printerType == AppConstant.DW14_PRINTER) {
                        val command = getDw14PrintBitmapLayout2()
                        TicketPrinter(this@RechargeCardActivity).printTicket(command)
                    }
                    else {
                        receiptLayout2 = iPaxGLPage.createPage()
                        receiptBitmap = getPrintBitmapLayout2()
                        TicketPrinter(this@RechargeCardActivity).printReceipt(receiptBitmap!!, prefs.getReferenceModel()!!.terminalConfig!!.receiptSetting!!.receipt_layout!!)
                    }
                }

            }
            catch (e:Exception)
            {
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                e.printStackTrace()
            }
            return null
        }

        override fun onPostExecute(result: Void?) {
            gotoNextActivity()
        }
    }
    override fun onBackPressed() {

    }

    private fun gotoNextActivity(){
        if (ticketType == AppConstant.ATTENDANT) {
            gotoMenuActivity()
        } else {
            if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            {
                gotoMenuActivity()
            }
            else
            {
                ticketType = AppConstant.ATTENDANT
                showAttendantPrintDialog()
            }
        }
    }
}