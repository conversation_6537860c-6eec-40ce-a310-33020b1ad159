package app.rht.petrolcard.ui.iccpayment.activity


import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.os.RemoteException
import android.view.View
import android.view.Window
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.observe
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityCardCeilinglimitsBinding
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.cardpincode.activity.VerifyPinActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel
import app.rht.petrolcard.ui.iccpayment.model.DiscountResponse
import app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.ModePayActivity
import app.rht.petrolcard.ui.modepay.activity.SplitPaymentActivity
import app.rht.petrolcard.ui.nfc.activity.NfcActivity
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.AFTER_TRX_MODE
import app.rht.petrolcard.utils.constant.AppConstant.ALL_CARDS
import app.rht.petrolcard.utils.constant.AppConstant.ALL_POSTPAID_CARDS
import app.rht.petrolcard.utils.constant.AppConstant.ALL_PREPAID_CARDS
import app.rht.petrolcard.utils.constant.AppConstant.BEFORE_TRX_MODE
import app.rht.petrolcard.utils.constant.AppConstant.OFFLINE_TRX_MODE
import app.rht.petrolcard.utils.constant.AppConstant.POSTPAID_CARD
import app.rht.petrolcard.utils.constant.AppConstant.PREPAID_CARD
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.constant.Workflow.OTHER_PRODUCTS
import app.rht.petrolcard.utils.constant.Workflow.SHOP_PRODUCTS
import app.rht.petrolcard.utils.constant.Workflow.TAXI_FUEL
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.tax.TaxModel
import app.rht.petrolcard.utils.tax.TaxUtils
import com.google.gson.Gson
import com.pax.dal.IDAL
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.activity_card_ceilinglimits.*
import kotlinx.android.synthetic.main.item_disputed_trx.*
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.BankCard
import java.math.BigDecimal
import java.util.*
import kotlin.collections.ArrayList


class CheckCardCeilingsLimitsActivity : BaseActivity<PaymentViewModel>(PaymentViewModel::class) {
    private var TAG= CheckCardCeilingsLimitsActivity::class.simpleName
    var dal: IDAL? = null
    var ret = -1
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""
    var authKey:String? = ""
    var cardType:Int? = 0
    var cardModel: CardStaticStructureModel?=null
    private var isFirstUse = false
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode = 0
    var carHolderName = ""
    private var mToday: Date? = null
    var dateLastPLF:String?=null
    private var terminalDateErr = false
    private lateinit var mBinding: ActivityCardCeilinglimitsBinding
    private var errorMessage = ""
    var returnValue = 0
    private var totalMonthlyLimit = 0.0 // plafondMensuel
    private var totalWeeklyLimit = 0.0 // plafondHebdo
    private var totalDailyLimit = 0.0 //plafondJournalier
    private var NBR_TRS_MONTHLY = 0 //NBR_TRS_MENSUEL
    private var NBR_TRS_WEEKLY = 0 //NBR_TRS_HEBDO
    private var NBR_TRS_DAILY = 0 //NBR_TRS_JOURNALIER
    private var preCardCeiling: String? = null //plafondCartePre
    private var PLF_MIN_PREPAYE = 0.0
    private var PLF_MAX_PREPAYE = 0.0
    private var NBR_TRS_OFF = 0
    private var CARD_NBR_TRS_OFF = 0
    var isLitreUnit= false
    var remainingCardCeilings:String?= ""
    private var remainingMonthlyAmount = 0.0 //plafondMensuelCompt
    private var remainingWeeklyAmount = 0.0 //plafondHebdoCompt
    private var remainingDailyAmount = 0.0 //plafondJournaliercompt
    private var REMAINING_NBR_TRS_MONTHLY = 0 //COMPTEUR_NBR_TRS_MENSUEL
    private var REMAINING_NBR_TRS_WEEKLY = 0 //COMPTEUR_NBR_TRS_HEBDO
    private var REMAINING_NBR_TRS_DAILY = 0 //COMPTEUR_NBR_TRS_JOURNALIER
    private var monthlyAmount = ""
    private var weeklyAmount = ""
    private var dailyAmount =""
    var cardBalance = 0.0
    var cardBalanceTxt = ""
    var allowedBalance = ""
    var unitTxt = ""
    var amount = ""
    private var isSameMonth = false
    var readPlf: String? = null
    private var isNextDayPLF = false
    private var mProduct: ProductModel? = null //mProduitToCheckout2
    private var cardHolderName = ""
    private var resultNFC: String? = null
    var listnfcrecord: ArrayList<NfcTagModel> = ArrayList<NfcTagModel>()
    private var NumberofnfcTag: String? = null
    private var discountAmount: String? = "0"
    var isNetworkAvailable = true
    private var discountAuthResponse:BaseResponse<DiscountResponse>?= null
    var productID = 0
    private var errorTitle = "Error"
    private var cardRestrictions: String? = null//restrictionCarte
    private var mRestScheduleCard = 0 // mRestHoraireCard
    private var mRestArticleCard = 0 //
    private var mRestStationCard = 0
    private var mRestSectorCard = 0 //mRestSecteurCard
    private var mRestHolidaysCard = 0
    private var isHourRestricted = false // ismRestHoraireCard
    private var isArticleRestricted = false //ismRestArticleCard
    private var isStationRestricted = false //ismRestStationCard
    private var isSectorRestricted = false //ismRestSecteurCard
    private var isHolidayRestricted = false //ismRestHolidaysCard
    private var isCardExpired = false
    private var isCardBlocked = false
    lateinit var readCardTask :ReadCardAsyncTask
    var loyatyDetailsResult = true
    private lateinit var referenceModel : ReferenceModel
    private var preferenceModel : PreferenceModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_card_ceilinglimits)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getInitIntentExtras()
        isNetworkAvailable = Connectivity.isNetworkAvailable(this)
        mToday = Support.getDateComparison(Date())
        preferenceModel = prefs.getPreferenceModel()
        readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
        setupToolbar()
        switchListeners()
    }
    override fun onInternetEnableDisable(status: Boolean) {
        super.onInternetEnableDisable(status)
        log(TAG, "onInternetEnableDisable :: $status")
        isNetworkAvailable = status
    }

    fun switchListeners()
    {
        mBinding.cardSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                setAllowedBalance()
                if (stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL) {
                    mBinding.discountSwitch.isChecked = false
                    mBinding.discountBalanceLayout.visibility = View.GONE
                } else if((stationMode == AFTER_TRX_MODE || stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS) && intentExtrasModel!!.mTransaction!=null && intentExtrasModel!!.mTransaction!!.amount!!.toDouble() < discountAmount!!.toDouble()){
                    mBinding.discountSwitch.isChecked = false
                }
            }
            else
            {
                if((stationMode == AFTER_TRX_MODE || stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS) && intentExtrasModel!!.mTransaction!=null && intentExtrasModel!!.mTransaction!!.amount!!.toDouble() > discountAmount!!.toDouble()){
                    mBinding.cardSwitch.isChecked = true
                }
                (!mBinding.cardSwitch.isChecked && !mBinding.discountSwitch.isChecked).also { mBinding.discountSwitch.isChecked = true }
            }
          validateDiscount()
        }
        mBinding.discountSwitch.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                log(TAG,"intentExtrasModel!!.mTransaction :: ${intentExtrasModel!!.mTransaction}")
                log(TAG,"intentExtrasModel!!.mTransaction.amount :: ${intentExtrasModel!!.mTransaction!!.amount}")
                log(TAG,"discountAmountt :: $discountAmount")
                log(TAG,"stationMode $stationMode")
                if(mBinding.discountBalanceLayout.visibility == View.VISIBLE && intentExtrasModel!!.amount!!.toDouble() > discountAmount!!.toDouble() &&  intentExtrasModel!!.workFlowMode == Workflow.FUEL_ENTER_AMOUNT)
                {
                    mBinding.discountSwitch.isChecked = false
                    mBinding.cardSwitch.isChecked = true
                    showToast(getString(R.string.discount_balance_lessthan_trx_amount))
                }
                else if (stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL) {
                    mBinding.cardSwitch.isChecked = false
                } else if((stationMode == AFTER_TRX_MODE || stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS) && intentExtrasModel!!.mTransaction!=null && intentExtrasModel!!.mTransaction!!.amount!!.toDouble() < discountAmount!!.toDouble()){
                    mBinding.cardSwitch.isChecked = false
                }
            }
            else
            {
                (!mBinding.cardSwitch.isChecked).also { mBinding.cardSwitch.isChecked = true }
            }
            validateDiscount()
        }
    }
    private fun setupToolbar()
    {
        mBinding.toolbarCeilingLimit.toolbar.tvTitle.text = getString(R.string.payment_confirmation)
        mBinding.toolbarCeilingLimit.toolbar.setNavigationOnClickListener {
            mBinding.toolbarCeilingLimit.toolbar.isEnabled = false
            if(::readCardTask.isInitialized)
                readCardTask.cancel(true)
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel),intentExtrasModel!!.mTransaction)
        }
    }
    private fun showLoading(boolean: Boolean) {
        if(boolean)
        {
            mBinding.progressBar.visibility=View.VISIBLE
            mBinding.CeilingLayout.visibility=View.GONE
        }
        else
        {
            mBinding.progressBar.visibility=View.GONE
            mBinding.CeilingLayout.visibility=View.VISIBLE
        }
    }
    fun getInitIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if (intentExtrasModel!!.loyaltyTrx) {
                stationMode = 1
            }
        }
        referenceModel = prefs.getReferenceModel()!!
    }
    var enableValidateButton = false
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {

            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@CheckCardCeilingsLimitsActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@CheckCardCeilingsLimitsActivity) //icCpuReader Not Required for BTPE
                    log(TAG, "infoCarte::: $infoCarte")
                    if (infoCarte != null && infoCarte!!.isNotEmpty()) {
                        panNumber = infoCarte!!.substring(0, 19)
                        if( intentExtrasModel!!.mTransaction != null)
                        {
                            intentExtrasModel!!.mTransaction!!.pan = panNumber
                        }
                    } else {
                        errorMessage = getString(R.string.card_error_9)
                        log(TAG,"errorMessage:: info carte null")
                        return -1
                    } //Abort Transaction

                    getCardStaticStructure()
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@CheckCardCeilingsLimitsActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@CheckCardCeilingsLimitsActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        getCardRestrictionInfo()
                        return if(intentExtrasModel!!.panNumber != panNumber) {
                            errorTitle = getString(R.string.error)
                            errorMessage = getString(R.string.card_changed)
                            log(TAG,"errorMessage:: pan number changed")
                            -1
                        } else {
                            val cardFBSRestriction = checkCardFBSRestriction(this@CheckCardCeilingsLimitsActivity)
                            if (cardFBSRestriction) {
                                checkCardLimits()
                            } else {
                                -1
                            }
                        }

                    }
                    else
                    {
                        errorTitle = getString(R.string.invalid_card)
                        errorMessage = getString(R.string.card_error_10)
                    }
                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
                errorMessage = getString(R.string.card_error_11)
                log(TAG,"errorMessage:: exception")
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                // mViewModel.generateLogs(e.message!!,0)
                return -1
            }
            return returnValue
        }
        override fun onPreExecute() {
        }

        override fun onPostExecute(result: Int?) {

            when (result) {
                -1 -> {
                    UtilsCardInfo.beep(mCore, 10)
                    if (terminalDateErr) {
                        errorTitle = resources.getString(R.string.TPE_DATE_ERROR_SYNCH)
                        errorMessage = resources.getString(R.string.TPE_DATE_ERROR)
                    }
                    gotoAbortMessageActivity(errorTitle, errorMessage,intentExtrasModel!!.mTransaction)
                }
                0 -> {
                    errorTitle = resources.getString(R.string.error)
                    errorMessage = getString(R.string.failed_update)
                    gotoAbortMessageActivity(errorTitle, errorMessage,intentExtrasModel!!.mTransaction)
                }
                1 -> {
                    gotoVerifyPinActivity()
                }
                2 ->
                {
                    proceedToNextStep()
                }
            }
        }
        override fun onProgressUpdate(vararg values: IntArray) {}
        override fun onCancelled(result: Int?) {}
        override fun onCancelled() {
            log(TAG,"Check Card Ceiling Limit Activity Task Canceled")
        }
    }
    fun getCardStaticStructure() {
        if(infoCarte != null && panNumber!!.isNotEmpty())
        {
            cardModel = UtilsCardInfo.readCardStaticStructureInfo(mBankCard,icCpuReader,this)
            val jsonData= (gson.toJson(cardModel))
            "Card Static Structure : $jsonData".also { log( TAG,it) }
            cardType = cardModel!!.cardType.toInt()
            intentExtrasModel!!.verificationType=cardModel!!.verificationType.toInt()
            if(cardModel!!.cardStatus == "F")
            {
                isFirstUse = true
            }
            carHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this)
            setCardTextValues()
        }
    }

    fun setCardTextValues() {
        runOnUiThread {
            mBinding.cardNumber.text = panNumber
            mBinding.cardHolderName.text = carHolderName
            val exp = cardModel!!.expiredDate.split("-")
            (exp[0] + "-" + exp[1]).also { mBinding.expiryDate.text = it }
            if (cardType == POSTPAID_CARD) {
                mBinding.cardType.text = getString(R.string.post_paid)
            } else if (cardType == AppConstant.PREPAID_CARD) {
                mBinding.cardType.text =  getString(R.string.pre_paid)
            } else if (cardType == AppConstant.LOYALTY_CARD) {
                mBinding.cardType.text =   getString(R.string.loyalty)
            }
        }
    }
    fun isCardDetected(): Boolean {
        var whileCondition = false
        try {
            whileCondition = BankCard.CARD_DETECT_EXIST == mBankCard!!.iccDetect()
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
        return whileCondition
    } // eof detect
    override fun setObserver() {
        mViewModel.discountObservable.observe(this) {
            discountAuthResponse = it
            intentExtrasModel!!.mTransaction!!.preAuthId = it.contenu!!.preAuthId
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)

            if(validateDiscount())
            {
                proccedForPayment()
            }
            else
            {
                showWarning(getString(R.string.card_not_authorized_for_rebate_discount))
            }
        }
        mViewModel.discountBalanceObservable.observe(this) {
            discountAuthResponse = it
            showLoading(false)
            if(it.reponse != null && it.reponse == "1" && it.contenu!!.discountBalance!! >= 1)
            {
                discountAuthResponse!!.contenu!!.isAuthorized =true
                discountAmount = discountAuthResponse!!.contenu!!.discountBalance.toString()
                validateDiscount()
            }
        }
        mViewModel.activationObservable.observe(this) {
            if (it.reponse != null && it.contenu != null) {
                loyatyDetailsResult = false
                if (it.reponse != "1") {
                    gotoAbortMessageActivity(getString(R.string.error), it.error,intentExtrasModel!!.mTransaction)
                }
            }

        }
    }
    fun showDiscountLayout()
    {
        mBinding.discountBalance.text = Support.formatString(discountAuthResponse!!.contenu!!.discountBalance!!)
        mBinding.discountTab.visibility = View.VISIBLE
        mBinding.progressDiscount.visibility = View.GONE
        mBinding.discountBalanceLayout.visibility = View.VISIBLE

        /* if(stationMode == AFTER_TRX_MODE && intentExtrasModel!!.mTransaction!=null && intentExtrasModel!!.mTransaction!!.amount!!.toDouble() < discountAmount!!.toDouble()){
             mBinding.cardSwitch.isChecked = false
         }*/

    }
    private fun validateDiscount():Boolean {
        try {
        hideWarning()
        if(discountAuthResponse!!.reponse != null && discountAuthResponse!!.reponse == "1" && discountAuthResponse!!.contenu != null && discountAuthResponse!!.contenu!!.discountBalance != null &&  discountAuthResponse!!.contenu!!.isAuthorized != null && discountAuthResponse!!.contenu!!.isAuthorized!!) {
            discountAmount = discountAuthResponse!!.contenu!!.discountBalance.toString()
            if(discountAmount!! < 1.toString()){
                showWarning(getString(R.string.discount_balance_should_be_greater_than))
                return false
            }
            else if ((stationMode == AFTER_TRX_MODE || stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS) && mBinding.discountSwitch.isChecked) {
                val finalAvailableAmount = discountAuthResponse!!.contenu!!.discountBalance!! + allowedBalance.replace(" ","").toDouble()

                   if (mBinding.cardSwitch.isChecked && intentExtrasModel!!.mTransaction!!.amount!! <= finalAvailableAmount) { // trx amount is lesser than available balance
                        mBinding.allowedBalnceTxt.text = Support.formatString(finalAvailableAmount)
                    }
                 if (mBinding.cardSwitch.isChecked && intentExtrasModel!!.mTransaction!!.amount!! <= finalAvailableAmount) { // trx amount is lesser than available balance
                     mBinding.allowedBalnceTxt.text = Support.formatString(finalAvailableAmount)
                 }
                 else if (!mBinding.cardSwitch.isChecked && intentExtrasModel!!.mTransaction!!.amount!! <= discountAmount!!.toDouble()) {
                     mBinding.allowedBalnceTxt.text = Support.formatString(discountAmount.toString())
                 }
                else
                {
                    showWarning(resources.getString(R.string.plafond_depase))
                    return false
                }
            }
            else if ((stationMode == AFTER_TRX_MODE || stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS) && !mBinding.discountSwitch.isChecked) {
            setAllowedBalance()
            }
            else if (stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL) {
                if(mBinding.discountSwitch.isChecked && !mBinding.cardSwitch.isChecked)
                {
                if (intentExtrasModel!!.workFlowMode ==  Workflow.FUEL_ENTER_AMOUNT && discountAmount!!.toDouble() < intentExtrasModel!!.amount!!.toDouble()) {
                    showWarning(resources.getString(R.string.plafond_depase))
                    return false
                }
                else if(intentExtrasModel!!.workFlowMode ==  Workflow.FUEL_FULL_TANK && intentExtrasModel!!.amount!!.toDouble() > discountAmount!!.toDouble()) {
                    //amount = ""+discountAmount
                    //check minimum amount among card limits, card balance, discount amount as per divya's suggestion 7 OCT 2022
                    val list: ArrayList<Double> = ArrayList()
                    list.add(Math.max(0.0, amount.toDouble()))
                    list.add(Math.max(0.0, discountAmount!!.toDouble()))
                    allowedBalance = ""+Support.formatDoubleAffichage(Collections.min(list))
                    amount = ""+Support.formatString(Collections.min(list))
                    log(TAG,"Discount amount assigned to amount because full tank selected and\n" +
                            "full tank amount ($amount) is greater than discount amount ($discountAmount) in such case we are sending lesser amount which is discount amount($discountAmount)")
                    log(TAG, "amount = ${amount}")
                    log(TAG, "intentExtrasModel!!.preAuthAmount = ${intentExtrasModel!!.preAuthAmount}")
                    log(TAG, "intentExtrasModel!!.mTransaction!!.preAuthAmount = ${intentExtrasModel!!.mTransaction!!.preAuthAmount}")
                    mBinding.allowedBalnceTxt.text = allowedBalance
                }
                else
                {
                    mBinding.allowedBalnceTxt.text = Support.formatString(discountAmount!!.toDouble())
                    if(intentExtrasModel!!.amount != null && intentExtrasModel!!.amount!!.toDouble() <=  discountAmount!!.toDouble())
                    {
                        discountAmount = intentExtrasModel!!.amount.toString()
                    }
                    intentExtrasModel!!.discountAmount = Support.formatString(discountAmount!!.toDouble())
                }
            }
            else if (mBinding.cardSwitch.isChecked && !mBinding.discountSwitch.isChecked ) {
                    mBinding.allowedBalnceTxt.text = Support.formatString(intentExtrasModel!!.amount!!)
                    amount = intentExtrasModel!!.amount!!
                    setAllowedBalance()
                }
            }


        }
         else
        {
            setAllowedBalance()
            return false
        }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            return false
        }
        mBinding.transactionAmount.text = amount
        showDiscountLayout()
        if(mBinding.cardSwitch.isChecked)
        {
            validateFleetcard()
        }
        return true
    }

    private fun authorizeDiscount() {
        intentExtrasModel!!.amount = amount
        intentExtrasModel!!.preAuthAmount = amount
        intentExtrasModel!!.mTransaction!!.amount = amount.toDouble()
        intentExtrasModel!!.mTransaction!!.preAuthAmount = amount
        log(TAG, "discountSwitch:: "+ mBinding.discountSwitch.isChecked)
        log(TAG, "cardSwitch:: "+ mBinding.cardSwitch.isChecked)
        log(TAG, "discountAmount:: $discountAmount")
        log(TAG, "amount:: $amount")
        if((stationMode == AFTER_TRX_MODE || stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS) && mBinding.discountSwitch.isChecked && !mBinding.cardSwitch.isChecked)
        {
            if(discountAmount!!.toDouble() > amount.toDouble())
            {
                discountAmount = amount
                log(TAG,"if(discountAmount!! > amount) true")
            }
            else
            {
                log(TAG,"if(discountAmount!! > amount) false")
            }
        }
        intentExtrasModel!!.mTransaction!!.discountAmount = Support.formatString(discountAmount!!.toDouble())
        intentExtrasModel!!.discountAmount = Support.formatString(discountAmount!!.toDouble())
        log(TAG,"discountAmount $discountAmount")
        if(stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL) {
            mViewModel.validateDiscountBalance(panNumber!!, intentExtrasModel!!.mTransaction!!.amount!!.toString())
        }
        else {
            mViewModel.validateDiscountBalance(panNumber!!,  intentExtrasModel!!.mTransaction!!.discountAmount!!)
        }
    }

    private fun showWarning(msg:String) {
        if(!msg.isNullOrEmpty())
        {
            mBinding.discountWarningMsg.text = msg
        }
        mBinding.discountWarningMsg.visibility = View.VISIBLE
        mBinding.submitBtn.visibility = View.GONE
        mBinding.modePayBtn.visibility = View.GONE
    }
    private fun hideWarning() {
        if(mBinding.discountSwitch.isChecked){
            mBinding.amountWarningMsg.visibility =View.GONE
        }
        mBinding.discountWarningMsg.visibility = View.GONE
        mBinding.submitBtn.visibility = View.VISIBLE
        mBinding.modePayBtn.visibility = View.GONE
    }
    fun checkCardFBSRestriction(context: Context): Boolean {
        cardRestrictions = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "03", "28", this)
        mRestStationCard = UtilsCardInfo.getRestStationCard(cardRestrictions!!.replace("F", "0"))
        mRestScheduleCard = UtilsCardInfo.getHourRestriction(cardRestrictions!!.replace("F", "0"))
        mRestHolidaysCard = UtilsCardInfo.getHolidayRestriction(cardRestrictions!!.replace("F", "0"))
        mRestSectorCard = UtilsCardInfo.getSectorRestriction(cardRestrictions!!.replace("F", "0"))
        mRestArticleCard = UtilsCardInfo.getArticalRestriction(cardRestrictions!!.replace("F", "0"))
        isCardExpired = UtilsDate.isCardExpired(mToday, cardModel!!.expiredDate)
        isCardBlocked = UtilsCardInfo.getStatusCard(infoCarte!!).equals("0") || UtilsCardInfo.getStatusCard(infoCarte!!).equals("F") && !intentExtrasModel!!.isCardUpdate!!
        intentExtrasModel!!.mRestArticleCard = mRestArticleCard
        intentExtrasModel!!.mRestStationCard = mRestStationCard
        intentExtrasModel!!.cardType = cardType
        intentExtrasModel!!.cardDiscountId = UtilsCardInfo.getDiscountID(mBankCard,icCpuReader,this)
        log(TAG, "DiscountID :: "+ intentExtrasModel!!.cardDiscountId)
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.discountId =  intentExtrasModel!!.cardDiscountId!!.toInt()
        }
        if (intentExtrasModel!!.loyaltyTrx) {
            mViewModel.cardActivationDetails(panNumber!!)
        }
        val mRestStationList=referenceModel.restrictions_stations
        val mRestHourList=referenceModel.restrictions_horaire
        val mRestSectorList=referenceModel.restrictions_secteurs
        val mRestArticleList=referenceModel.restrictions_articles
        val mRestHolidayList=referenceModel.restrictions_jrs_free


        if (cardType != null && cardType != AppConstant.PREPAID_CARD && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD) {
            errorMessage =  resources.getString(R.string.invalid_card)
            return false
        }
        if (cardType != null && cardType == AppConstant.LOYALTY_CARD && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_HISTORY) {
            errorMessage =  resources.getString(R.string.card_loyalty)
            return false
        }
        isStationRestricted = if (mRestStationCard != 1 && mRestStationList.isNotEmpty()) {
            UtilsCardInfo.isStationRestriction(mRestStationCard,preferenceModel!!.stationID!!, mRestStationList)
        } else {
            false
        }
        isHourRestricted = if (mRestScheduleCard != 1 && mRestHourList.isNotEmpty()) {
            UtilsCardInfo.isHourRestricted(mRestScheduleCard, mRestHourList)
        } else {
            false
        }

        isArticleRestricted = if (intentExtrasModel!!.selectedProduct != null && mRestArticleCard != 1 && intentExtrasModel!!.selectedProduct!!.productID != 0 && mRestArticleList.isNotEmpty()) {
            UtilsCardInfo.isArticleRestriction(mRestArticleCard, intentExtrasModel!!.selectedProduct!!.productID, mRestArticleList)
        } else {
            false
        }
        isSectorRestricted = if (mRestSectorCard != 1 && mRestSectorList.isNotEmpty()) {
            UtilsCardInfo.isSecteurRestriction(mRestSectorCard,  preferenceModel!!.sectorId!!, mRestSectorList)
        } else {
            false
        }
        isHolidayRestricted = if (mRestHolidaysCard != 1 && mRestHolidayList.isNotEmpty()) {
            UtilsCardInfo.isHolidaysRestriction(mRestHolidaysCard, mRestHolidayList, Support.dateToString(Date()))
        } else {
            false
        }
        return if (!loyatyDetailsResult) {
            false
        } else if (isCardExpired) {
            errorMessage = resources.getString(R.string.card_expired)+"\n"+resources.getString(R.string.contact_agent)
            false
        } else if (isCardBlocked) {
            errorMessage = resources.getString(R.string.card_block)
            false
        }  else if (isStationRestricted) {
            errorMessage = resources.getString(R.string.block_rest_station)
            false
        } else if (isHourRestricted) {
            errorMessage = resources.getString(R.string.block_rest_horaire)
            false
        }
        else if (isArticleRestricted) {
            errorMessage = resources.getString(R.string.block_rest_produit)
            false
        }
        else if (isSectorRestricted) {
            errorMessage = resources.getString(R.string.block_rest_secteur)
            false
        } else if (isHolidayRestricted) {
            errorMessage = resources.getString(R.string.block_rest_holiday)
            false
        } else {
            true
        }

    }
    fun getCardRestrictionInfo() {


        if(stationMode == OFFLINE_TRX_MODE)
        {
            mProduct = intentExtrasModel!!.selectedProduct
        }
        else if (intentExtrasModel!!.productCode != null) {
            mProduct = getProductDetailsByCode(intentExtrasModel!!.productCode!!)
        }
        //TotalCard Ceiling Limits
        val cardCeilings = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "02", "32", this).replace("F", "0")
        totalMonthlyLimit = UtilsCardInfo.getCardCeilings( cardCeilings, 0, 12) //Monthly card limit
        totalWeeklyLimit = UtilsCardInfo.getCardCeilings( cardCeilings, 12, 24) //weekly ceiling cap
        totalDailyLimit = UtilsCardInfo.getCardCeilings( cardCeilings, 24, 36) //ceiling day of the card
        NBR_TRS_MONTHLY = UtilsCardInfo.getCardCeilingCount( cardCeilings, 36, 44) // get No. of Trx //NBR MONTHLY TRS of the card
        NBR_TRS_WEEKLY = UtilsCardInfo.getCardCeilingCount( cardCeilings, 44, 52) //NBR TRS WEEKLY from the card
        NBR_TRS_DAILY = UtilsCardInfo.getCardCeilingCount( cardCeilings, 52, 60) // NBR TRS DAILY of the card

        // Total PrePaid Card ceilings
        preCardCeiling = UtilsCardInfo.readRecordLinear( mBankCard,icCpuReader, "2F09", "05", "28", this).replace("F", "0")
        PLF_MIN_PREPAYE = UtilsCardInfo.getPreCardCeiling( preCardCeiling!!, 0, 12) //PLF MIN PREPAYE of the card ---->
        PLF_MAX_PREPAYE = UtilsCardInfo.getCardCeilings( preCardCeiling!!, 12, 24) //PLF_MAX_PREPAYE of the card
        NBR_TRS_OFF = UtilsCardInfo.getCardCeilingCount( preCardCeiling!!, 24, 28) //BR_TRS_OFF from the card
        CARD_NBR_TRS_OFF = UtilsCardInfo.getCardCeilingCount( preCardCeiling!!, 31, 35) //card_NBR_TRS_OFF counter
        cardHolderName = UtilsCardInfo.readCardHolderName( mBankCard,icCpuReader, this)!!
        intentExtrasModel!!.cardDiscountId = UtilsCardInfo.getDiscountID(mBankCard,icCpuReader,this)
        log(TAG,"DiscountId :: "+intentExtrasModel!!.cardDiscountId)
        //Remaining Card Limits
        if(cardModel!!.cardCeilingUnit == "1")
        {
            isLitreUnit = true
        }
        // counter ceiling
        remainingCardCeilings = if (isLitreUnit) {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", this).replace("F", "0")
        } else {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", this).replace("F", "0")
        }
        remainingMonthlyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  0, 12)
        remainingWeeklyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  12, 24)
        remainingDailyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  24, 36)

        dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
        REMAINING_NBR_TRS_MONTHLY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  36, 44).toInt()
        REMAINING_NBR_TRS_WEEKLY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!, 44, 52).toInt()
        REMAINING_NBR_TRS_DAILY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  52, 60).toInt()

        prefs.remCardMonthlyLimit = remainingMonthlyAmount
        prefs.remCardWeeklyLimit = remainingWeeklyAmount
        prefs.remCardDailyLimit = remainingDailyAmount
        prefs.remCardMonthlyCount = REMAINING_NBR_TRS_MONTHLY
        prefs.remCardMonthlyCount = REMAINING_NBR_TRS_WEEKLY
        prefs.remCardMonthlyCount = REMAINING_NBR_TRS_DAILY
        prefs.remCardCeiling = remainingCardCeilings!!

        /*-------------Set Card Balance Text---------------------*/
        cardBalance = if(cardType == AppConstant.PREPAID_CARD) {
            UtilsCardInfo.getAmount(mBankCard, icCpuReader, 4, 12, this)
        } else {
            Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount)
        }
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.soldeCard = cardBalance.toString()
            intentExtrasModel!!.mTransaction!!.discountType = cardModel!!.discountType.toInt()
            log(TAG,"discountType ${intentExtrasModel!!.mTransaction!!.discountType}")
        }
        cardBalanceTxt = ""+(Support.formatDoubleAffichage(cardBalance).toString())
        /*-------------Set Card Balance Text---------------------*/
        monthlyAmount = "" + (Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount))
        weeklyAmount = "" + (Math.max(0.0, totalWeeklyLimit - remainingWeeklyAmount))
        dailyAmount = "" + (Math.max(0.0, totalDailyLimit - remainingDailyAmount))
        setAllowedBalance()

        if (isLitreUnit) {
            unitTxt = getString(R.string.liters)
        } else {
            unitTxt =prefs.currency
            dailyAmount = dailyAmount.toDouble().toString()
            weeklyAmount = weeklyAmount.toDouble().toString()
            monthlyAmount = monthlyAmount.toDouble().toString()
        }
        runOnUiThread {
            if (cardType!! == AppConstant.PREPAID_CARD)
                mBinding.layoutCardBalance.visibility = View.VISIBLE
            else
                mBinding.layoutCardBalance.visibility = View.GONE
        }
        if(stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL)
        {
            if (isLitreUnit) {
                if (intentExtrasModel!!.selectedPrice != null && intentExtrasModel!!.selectedPrice!!.unitPrice > 0)
                    amount = (amount.toDouble() / intentExtrasModel!!.selectedPrice!!.unitPrice).toString() + ""
            } else amount = intentExtrasModel!!.amount.toString()
        }
        if(intentExtrasModel!!.mTransaction != null) {
            amount = if (isLitreUnit && intentExtrasModel!!.mTransaction != null) {
                intentExtrasModel!!.mTransaction!!.quantite.toString() + ""
            }
            else {
                java.lang.String.valueOf(intentExtrasModel!!.mTransaction!!.amount)
            }
        }
    }

    private fun setAllowedBalance() {
        val list: ArrayList<Double> = ArrayList<Double>()
        /*-------------Set Allowed Balance Text---------------------*/
        if (cardType == PREPAID_CARD) {
            list.add(Math.max(0.0, cardBalance))
        }
        list.add(monthlyAmount.toDouble())
        list.add(weeklyAmount.toDouble())
        list.add(dailyAmount.toDouble())
        allowedBalance = "" + Support.formatDoubleAffichage(Collections.min(list))
        mBinding.allowedBalnceTxt.text = allowedBalance
        /*-------------Set Allowed Balance Text---------------------*/
    }

    fun setTextValues() {

        val dTxt = BigDecimal.valueOf(dailyAmount.replace(" ","").toDouble())
        val wTxt = BigDecimal.valueOf(weeklyAmount.replace(" ","").toDouble())
        val mTxt = BigDecimal.valueOf(monthlyAmount.replace(" ","").toDouble())
        val aTxt = BigDecimal.valueOf(allowedBalance.replace(" ","").toDouble())
        val cbTxt = BigDecimal.valueOf(cardBalanceTxt.replace(" ","").toDouble())

        val df=  decimalFormat.format(dTxt)
        val wf=  decimalFormat.format(wTxt)
        val mf=  decimalFormat.format(mTxt)
        val cbf=  decimalFormat.format(cbTxt)
        val aTxtf=  decimalFormat.format(aTxt)
        ("$df $unitTxt").also { mBinding.dailyCeilingTxt.text = it }
        ("$wf $unitTxt").also { mBinding.weeklyCeilingTxt.text = it }
        ("$mf $unitTxt").also { mBinding.monthlyCeilingTxt.text = it }
        ("$cbf $unitTxt").also { mBinding.tvCardBalance.text = it }
        ("$aTxtf $unitTxt").also { mBinding.allowedBalnceTxt.text = it }
        if (cardBalance == 0.0) {
            mBinding.amountWarningMsg.visibility = View.VISIBLE
            mBinding.amountWarningMsg.text = resources.getString(R.string.soldeCardErr)
            mBinding.submitBtn.visibility = View.GONE
        }
        checkCeilingLimitLayoutConfig()
        if(stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowMode ==  Workflow.FUEL_FULL_TANK) {
            getFullTankAmount()
        }
        else
        {
            getEnteredAmount()
        }
    }
    fun checkValidationForEnterAmount(amount: String)
    {
        val list: ArrayList<Double> = ArrayList<Double>()
        if(cardType == PREPAID_CARD)
        {
            list.add(Math.max(0.0, cardBalance))
        }
        list.add(Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount))
        list.add(Math.max(0.0, totalWeeklyLimit - remainingWeeklyAmount))
        list.add(Math.max(0.0, totalDailyLimit - remainingDailyAmount))

        if (amount.toDouble() <= monthlyAmount.toDouble() &&
            amount.toDouble() <= weeklyAmount.toDouble() &&
            amount.toDouble() <= dailyAmount.toDouble())
        {
            if (cardBalance < amount.toDouble() || cardBalance == 0.0 || list.isEmpty() && Collections.min(list) <= 0) {
                mBinding.amountWarningMsg.visibility = View.VISIBLE
                mBinding.submitBtn.visibility = View.GONE

            }
        }
        //added this to set validation for enter amount
        else if ((amount.toDouble() > monthlyAmount.toDouble() || amount.toDouble() > weeklyAmount.toDouble() || amount.toDouble() > dailyAmount.toDouble()))
        {
            showCeilingExceededMessage()
        }
        else if(cardType == PREPAID_CARD && amount.toDouble() > cardBalance)
        {
            mBinding.amountWarningMsg.visibility = View.VISIBLE
            mBinding.submitBtn.visibility = View.GONE

        }
    }
    fun checkValidationForFullTank(amountValue: String)
    {
        //newly created function to handle SOL ceiling exceeded issue
        if( (amountValue.toDouble()>monthlyAmount.toDouble() || monthlyAmount.toDouble() < 1) ||
            (amountValue.toDouble()>weeklyAmount.toDouble() || weeklyAmount.toDouble() < 1) ||
            (amountValue.toDouble()>dailyAmount.toDouble() || dailyAmount.toDouble() < 1) ||
            (cardType == PREPAID_CARD && amountValue.toDouble() > cardBalance)){
            log(TAG,"Full Tank Amount :: $amountValue")
            showCeilingExceededMessage()
        }
    }
    fun getFullTankAmount()
    {
        val list: ArrayList<Double> = ArrayList<Double>()
        if(intentExtrasModel!!.amount != null)
        {
            amount =intentExtrasModel!!.amount!!
        }
        else if(intentExtrasModel!!.mTransaction != null && intentExtrasModel!!.mTransaction!!.amount != null)
        {
            amount =intentExtrasModel!!.mTransaction!!.amount.toString()
        }
        if(fuelVat.enabled || shopVat.enabled) {
            val taxModel =  calculateTax(amount)
            amount = taxModel.totalAmount.toString()
            intentExtrasModel!!.preAuthAmount = taxModel.totalAmount.toString()
            intentExtrasModel!!.taxModel = taxModel
        } else {
            intentExtrasModel!!.preAuthAmount = amount
        }
        var presetAmount = amount

        log(TAG,"Full Tank presetAmount = $amount")
        if(intentExtrasModel!!.modePaymentModel != null)
        {
            presetAmount=intentExtrasModel!!.modePaymentModel!!.preset_amount.toString()
            list.add(Math.max(0.0, presetAmount.toDouble()))
        }
        if(cardType == PREPAID_CARD)
        {
            list.add(Math.max(0.0, cardBalance))
        }
        list.add(Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount))
        list.add(Math.max(0.0, totalWeeklyLimit - remainingWeeklyAmount))
        list.add(Math.max(0.0, totalDailyLimit - remainingDailyAmount))
        amount = Collections.min(list).toString()
        log(TAG,"Full Tank minimum = $amount")
        intentExtrasModel!!.amount = amount
        if(intentExtrasModel!!.mTransaction != null) {
            intentExtrasModel!!.mTransaction!!.amount = amount.toDouble()
            intentExtrasModel!!.mTransaction!!.preAuthAmount = amount
        }
        intentExtrasModel!!.preAuthAmount = amount
        if (isLitreUnit) {
            if (intentExtrasModel!!.selectedPrice != null && intentExtrasModel!!.selectedPrice!!.unitPrice > 0)
                amount = (amount.toDouble() / intentExtrasModel!!.selectedPrice!!.unitPrice).toString() + ""
        }
        checkValidationForFullTank(amount)
    }
    fun getEnteredAmount()
    {
        if(stationMode == BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL)
        {
            if (isLitreUnit) {
                if (intentExtrasModel!!.selectedPrice != null && intentExtrasModel!!.selectedPrice!!.unitPrice > 0)
                    amount = (amount.toDouble() / intentExtrasModel!!.selectedPrice!!.unitPrice).toString() + ""
            } else amount = intentExtrasModel!!.amount.toString()
        }
        if(intentExtrasModel!!.mTransaction != null) {
            amount = if (isLitreUnit && intentExtrasModel!!.mTransaction != null) {
                intentExtrasModel!!.mTransaction!!.quantite.toString() + ""
            }
            else {
                java.lang.String.valueOf(intentExtrasModel!!.mTransaction!!.amount)
            }
        }
        intentExtrasModel!!.amount = amount
        if(intentExtrasModel!!.mTransaction != null) {
            intentExtrasModel!!.mTransaction!!.amount = amount.toDouble()
            intentExtrasModel!!.mTransaction!!.preAuthAmount = amount
        }
        intentExtrasModel!!.preAuthAmount = amount
        checkValidationForEnterAmount(amount)
    }

    private fun showCeilingExceededMessage() {
        if(cardType == PREPAID_CARD && amount.toDouble() > cardBalance)
        {
            mBinding.amountWarningMsg.visibility = View.VISIBLE

            mBinding.submitBtn.visibility = View.GONE
        }
        else {
            mBinding.amountWarningMsg.visibility = View.VISIBLE

            mBinding.amountWarningMsg.text = resources.getString(R.string.plafond_depase)
            mBinding.submitBtn.visibility = View.GONE
        }
        if (referenceModel.TERMINAL_TYPE != AppConstant.UN_ATTENDANT_MODE)
            mBinding.modePayBtn.visibility = View.VISIBLE
    }

    private fun calculateTax(authAmount:String) : TaxModel{
        val taxModel = if(amount == "null" || amount.isNullOrEmpty()) { // full tank tax calculation
            when {
                fuelVat.enabled -> TaxUtils.calculate(authAmount.toDouble(),fuelVat.percentage!!.toDouble(),true)
                shopVat.enabled -> TaxUtils.calculate(authAmount.toDouble(),shopVat.percentage!!.toDouble(),true)
                else -> TaxModel(true,0.0,0.0,authAmount.toDouble(),authAmount.toDouble())
            }
        } else { //entered amount
            when {
                fuelVat.enabled -> TaxUtils.calculate(authAmount.toDouble(),fuelVat.percentage!!.toDouble(),fuelVat.type == 0)
                shopVat.enabled -> TaxUtils.calculate(authAmount.toDouble(),shopVat.percentage!!.toDouble(),fuelVat.type == 0)
                else -> TaxModel(true,0.0,0.0,authAmount.toDouble(),authAmount.toDouble())
            }
        }
        return taxModel
    }

    private fun checkCeilingLimitLayoutConfig() {
        val terminalConfig =referenceModel.terminalConfig
        if(terminalConfig?.cardSettings != null){
            val ceilingLimitConfig = terminalConfig.cardCeilingLimits
            if(ceilingLimitConfig!!.monthlyLimit!!)
                mBinding.monthlyLayout.visibility = View.VISIBLE
            else
                mBinding.monthlyLayout.visibility = View.GONE

            if(ceilingLimitConfig.weeklyLimit!!)
                mBinding.weeklyLayout.visibility = View.VISIBLE
            else
                mBinding.weeklyLayout.visibility = View.GONE

            if(ceilingLimitConfig.dailyLimit!!)
                mBinding.dailyLayout.visibility = View.VISIBLE
            else
                mBinding.dailyLayout.visibility = View.GONE

            if(ceilingLimitConfig.allowedBalance!!)
                mBinding.layoutAllowedBalance.visibility = View.VISIBLE
            else
                mBinding.layoutAllowedBalance.visibility = View.GONE

            if(ceilingLimitConfig.cardBalance!! && cardType == PREPAID_CARD)
                mBinding.layoutCardBalance.visibility = View.VISIBLE
            else
                mBinding.layoutCardBalance.visibility = View.GONE

            if(!ceilingLimitConfig.monthlyLimit!! && !ceilingLimitConfig.weeklyLimit!! && !ceilingLimitConfig.dailyLimit!!){
                mBinding.ceilingLimitLayout.visibility = View.GONE
            }
        }

    }

    fun checkCardLimits():Int {

        if (mProduct != null && mProduct!!.isAvailable == null && mProduct!!.isAvailable == "false" && isLitreUnit && mProduct!!.productID == PRODUCT.FUEL_CATEGORY_ID) {
            errorMessage = resources.getString(R.string.block_rest_produit)
            log(TAG,"errorMessage:: Fuel restriction")
            return -1
        } else if (intentExtrasModel!!.mPinNumberCard == null) {
            returnValue = 1
        }
        else if (cardType != null && cardType == AppConstant.PREPAID_CARD || cardType == POSTPAID_CARD) {
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                if (isLitreUnit) dateLastPLF =
                    UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
                else
                    dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                isSameMonth = UtilsDate.isSameMonth(mToday, dateLastPLF)
            }
            if (!isSameMonth) {
                if (true) {
                    val plafondCarte: String = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "05", "28", this)
                    UtilsCardInfo.updateNbreTrxOFFCompteur(mBankCard, icCpuReader, plafondCarte, 0.toString() + "", this)
                    val plafondCarteNew: String = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "05", "28", this)
                    CARD_NBR_TRS_OFF = UtilsCardInfo.getCardCeilingCount( plafondCarteNew, 31, 35)
                }
                readPlf = if (isLitreUnit) UtilsCardInfo.readCardCeilingLimits( remainingCardCeilings!!)
                else
                    UtilsCardInfo.readCardCeilingLimits( remainingCardCeilings!!)
                var updatedRecord1: String? = UtilsCardInfo.prepareRecordLinear(readPlf!!, 0, 12, Utils.padZero(0.toString() + "", 12))
                updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1!!, 36, 44, Utils.padZero(0.toString() + "", 8))
                val updatedRecord3: String = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 72, 80, Utils.padZero(Support.dateToStringPlafondCompt(mToday)!!, 8))
                if (isLitreUnit) {
                    UtilsCardInfo.updateRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", updatedRecord3, this)
                    remainingCardCeilings = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", this).replace("F", "0")
                    remainingMonthlyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  0, 12)
                    REMAINING_NBR_TRS_MONTHLY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  36, 44).toInt()
                } else {
                    UtilsCardInfo.updateRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", updatedRecord3, this)
                    remainingCardCeilings = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", this).replace("F", "0")
                    remainingMonthlyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!,  0, 12)
                    REMAINING_NBR_TRS_MONTHLY = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!,  36, 44).toInt()
                }
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                isNextDayPLF = UtilsDate.isNextDayPLF(mToday, dateLastPLF)
            }
            if (isNextDayPLF) {
                readPlf = if (isLitreUnit) UtilsCardInfo.readCardCeilingLimits( remainingCardCeilings!!) else UtilsCardInfo.readCardCeilingLimits( remainingCardCeilings!!)
                var updatedRecord1: String? = UtilsCardInfo.prepareRecordLinear(readPlf!!, 24, 36, Utils.padZero(0.toString() + "", 12))
                updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1!!, 52, 60, Utils.padZero(0.toString() + "", 8))
                val updatedRecord3: String = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 72, 80, Utils.padZero(Support.dateToStringPlafondCompt(mToday)!!, 8))
                if (isLitreUnit) {
                    updateRecords("03", updatedRecord3)
                } else {
                    updateRecords("01", updatedRecord3)
                }
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                if (!UtilsDate.isSameWeek(Date(), dateLastPLF)) {

                    readPlf = if (isLitreUnit) UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings!!
                    ) else UtilsCardInfo.readCardCeilingLimits( remainingCardCeilings!!)
                    var updatedRecord1: String? = UtilsCardInfo.prepareRecordLinear(readPlf!!, 12, 24, Utils.padZero(0.toString() + "", 12))
                    updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1!!, 44, 52, Utils.padZero(0.toString() + "", 8))
                    val updatedRecord3: String = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 72, 80, Utils.padZero(Support.dateToStringPlafondCompt(mToday)!!, 8))
                    if (isLitreUnit) {
                        updateRecords("03", updatedRecord3)
                    } else {
                        updateRecords("01", updatedRecord3)
                    }
                } else
                    log(TAG, " isNextWeekPLF ----> false ")
            }

            getCardRestrictionInfo() //newly added by update to update text values
        }

        returnValue = 2

        return 2
    }
    fun updateRecords(recordNumber:String,updatedRecord3:String) {
        UtilsCardInfo.updateRecordLinear(mBankCard, icCpuReader, "2F07", recordNumber, "32", updatedRecord3, this)
        remainingCardCeilings = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", recordNumber, "32", this).replace("F", "0")
        remainingDailyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  24, 36)
        REMAINING_NBR_TRS_DAILY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  52, 60).toInt()
    }
    fun gotoVerifyPinActivity() {
        val i = Intent(this, VerifyPinActivity::class.java)
        intentExtrasModel!!.isFirstUse = isFirstUse
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(i)
        finish()

    }

    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        showLoading(false)
    }
    fun proceedToNextStep() {
        try {
            if(BuildConfig.REBATE_DISCOUNT_REQUIRED && referenceModel.IMPLEMENT_DISCOUNT!! && isNetworkAvailable && cardType == PREPAID_CARD)
            {
                mViewModel.getDiscountBalance(panNumber!!)

            }
            else
            {
                mBinding.progressDiscount.visibility = View.GONE
                mBinding.discountBalanceLayout.visibility = View.GONE
                mBinding.discountSwitch.isChecked = false
                showLoading(false)
            }
            if (cardModel!!.verificationType.toInt() == 1) {
                resultNFC = UtilsCardInfo.readBinaryFile(mBankCard, icCpuReader, "2F20", "5F", this)
                if(resultNFC != null)
                {
                    NumberofnfcTag = resultNFC!!.substring(resultNFC!!.length - 2, resultNFC!!.length)
                    listnfcrecord = UtilsCardInfo.getNFCList(resultNFC!!)
                    log(TAG,"listnfcrecord ::: "+ Gson().toJson(listnfcrecord))
                }
                else
                {
                    NumberofnfcTag = "0"
                }

        }
        if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
            if (cardModel!!.verificationType == "1") {
                gotoNfcActivity()
            }
            else
            {
                intent = if (prefs.getStationModel()!!.mode_pompiste == AppConstant.CODE) {
                    Intent(this, AttendantCodeActivity::class.java)
                } else {
                    Intent(this, AttendantTagActivity::class.java)
                }
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)
                finish()
            }
        } else {

                printLog()
//            isOffTrxLimite = !isOnlineOffline && NBR_TRS_OFF != 0 && NBR_TRS_OFF <= CARD_NBR_TRS_OFF // offline limit
                setTextValues()
//            if (isOffTrxLimite) {
//                mBinding.amountWarningMsg.visibility = View.VISIBLE
//                mBinding.amountWarningMsg.text = resources.getString(R.string.nbr_trx_off_error)
//                mBinding.submitBtn.visibility = View.GONE
//            }
               validateFleetcard()
                mBinding.submitBtn.setOnClickListener { view: View? ->
                    MultiClickPreventer.preventMultiClick(view)
                    if(BuildConfig.REBATE_DISCOUNT_REQUIRED && referenceModel.IMPLEMENT_DISCOUNT!! && mBinding.discountSwitch.isChecked)
                    {
                        authorizeDiscount()
                    }
                    else
                    {
                        proccedForPayment()
                    }
                }
                mBinding.modePayBtn.setOnClickListener { view: View? ->
                    if(intentExtrasModel!!.mTransaction != null)
                    {
                        intentExtrasModel!!.mTransaction!!.pan = ""
                        intentExtrasModel!!.mTransaction!!.dailyCeiling = ""
                        intentExtrasModel!!.mTransaction!!.weeklyCeiling = ""
                        intentExtrasModel!!.mTransaction!!.monthlyCeiling = ""
                        intentExtrasModel!!.mTransaction!!.soldeCard = ""

                }
                val mIntent = Intent(this,ModePayActivity::class.java)
                mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(mIntent)
                finish()
            }
            mBinding.cancelBtn.setOnClickListener { view: View? ->
                UtilsCardInfo.beep(mCore, 10)
                gotoAbortMessageActivity(getString(R.string.transaction_error),resources.getString(R.string.transaction_cancel),intentExtrasModel!!.mTransaction)
            }
        }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    private fun getAllowedBalance(amount: String): String {
        log(TAG, "GETTING ALLOWED BALANCE")
        val amt: String
        val dailyAMount: Double = totalDailyLimit - remainingDailyAmount
        amt = try {
            if (!amount.isEmpty()) {
                val cardAmount = amount.replace("[^0-9]".toRegex(), "").toDouble()
                if (cardAmount <= dailyAMount) {
                    log(TAG, "CARD AMOUNT")
                    cardAmount.toString()
                } else if (dailyAMount < cardAmount) {
                    log(TAG, "DAILY AMOUNT")
                    dailyAMount.toString()
                } else {
                    log(
                        TAG,
                        "INNER ELSE DAILY AMOUNT"
                    )
                    dailyAMount.toString()
                }
            } else {
                log(TAG, "ELSE DAILY AMOUNT")
                dailyAMount.toString()
            }
        } catch (e: Exception) {
            log(
                TAG,
                "ERROR WHILE getting the daily limint and card values"
            )
            dailyAMount.toString()
        }
        return amt
    }
    private fun gotoDebitCardLimitsActivity() {
        val mIntent = Intent(this, DebitCardLimitsActivity::class.java)
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(mIntent)
         finish()
    }
    private fun gotoPumpSelectiontionActivity() {
        val mIntent = Intent(this, PumpSelectionActivity::class.java)
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(mIntent)
        finish()
    }
    private fun proccedForPayment() {
        enableValidateButton = false
        UtilsCardInfo.beep(mCore, 10)

        if(BuildConfig.REBATE_DISCOUNT_REQUIRED && referenceModel.IMPLEMENT_DISCOUNT!! && mBinding.discountSwitch.isChecked)
        {
            intentExtrasModel!!.mTransaction!!.discountType = DISCOUNT_TYPE.REBATE_DISCOUNT
            intentExtrasModel!!.mTransaction!!.isDiscountTransaction = 1
        }
        else if(referenceModel.IMPLEMENT_DISCOUNT!! && intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL && (intentExtrasModel!!.stationMode == OFFLINE_TRX_MODE || intentExtrasModel!!.stationMode == AFTER_TRX_MODE))
        {
            getDiscountAmount()
        }
        if(intentExtrasModel!!.stationMode!! == AppConstant.BEFORE_TRX_MODE)  //added this because app is crashing while debiting the amount from the card (in after trx mode amount is already coming from enter amount activity so no need to assign amount here)
        {
            intentExtrasModel!!.amount = amount
            intentExtrasModel!!.preAuthAmount = amount
        }
        else if(intentExtrasModel!!.stationMode!! == AppConstant.OFFLINE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL)  //added this because app is crashing while debiting the amount from the card (in after trx mode amount is already coming from enter amount activity so no need to assign amount here)
        {
            amount = intentExtrasModel!!.amount!!
            intentExtrasModel!!.preAuthAmount = amount
        }

        if(intentExtrasModel!!.mTransaction!=null){ //newly added to resolve SOL ceiling exceeded issue
            intentExtrasModel!!.mTransaction!!.amount = amount.toDouble()
        }

        log(TAG, "Amount Value:: $amount")
        intentExtrasModel!!.cardHolderName = cardHolderName
        intentExtrasModel!!.dateExp = cardModel!!.expiredDate
        intentExtrasModel!!.panNumber = panNumber
        if(intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL)
        {
            intentExtrasModel!!.selectedProduct = mProduct
        }
        intentExtrasModel!!.dailyAmount = getAllowedBalance(mBinding.allowedBalnceTxt.text.toString())
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.nomPorteur = carHolderName
            intentExtrasModel!!.mTransaction!!.dateExp = cardModel!!.expiredDate
            intentExtrasModel!!.mTransaction!!.pan = panNumber
        }

        val unAttendantMode =referenceModel.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE //added by altaf to skip vehicle nfc tag verification

        if (!unAttendantMode && cardModel!!.verificationType.toInt() == 1 && intentExtrasModel!!.workFlowTransaction != Workflow.SHOP_PRODUCTS) {
            gotoNfcActivity()
        }
        else if(BuildConfig.REBATE_DISCOUNT_REQUIRED /*&& intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL*/ && referenceModel.IMPLEMENT_DISCOUNT!! && mBinding.discountSwitch.isChecked)
        {
            if(intentExtrasModel!!.stationMode!! == AppConstant.BEFORE_TRX_MODE && intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL)
            {
                gotoPumpSelectiontionActivity()
            }
            else if(intentExtrasModel!!.stationMode!! == AppConstant.AFTER_TRX_MODE || intentExtrasModel!!.stationMode!! == AppConstant.OFFLINE_TRX_MODE || intentExtrasModel!!.workFlowTransaction == SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == OTHER_PRODUCTS)
            {
                if(mBinding.cardSwitch.isChecked)
                {
                    gotoDebitCardLimitsActivity()
                }
                else if(mBinding.discountSwitch.isChecked && !mBinding.cardSwitch.isChecked)
                {
                    gotoTicketActivity()
                }
            }
        }
        else {
            gotoDebitCardLimitsActivity()
        }
    }
    private fun gotoTicketActivity() {
        val intent = Intent(this,TicketActivity::class.java)
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
        finish()
    }
    private fun gotoNfcActivity() {
        val intent = Intent(this,NfcActivity::class.java)
        intentExtrasModel!!.listnfcrecord = listnfcrecord
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
       finish()
    }
    private fun resetPayment() {
        if(!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            intentExtrasModel!!.splitPaymentModel!!.firstModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.firstPaymentName = ""

        }
        else
        {
            intentExtrasModel!!.splitPaymentModel!!.secondModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.secondPaymentName = ""
        }
        intentExtrasModel!!.splitPaymentModel!!.isError = true
    }
    fun showErrorDialog(title:String,msg: String?) {

        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_failed_message)
        dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        val tvTitle = dialog.findViewById<TextView>(R.id.title)
        val tvMessage = dialog.findViewById<TextView>(R.id.message)
        val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

        tvTitle.text = title
        tvMessage.text = msg

        var timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                //here you can have your logic to set text to edittext
                println("Popup Time Remaining: $minutes:$seconds")
            }

            override fun onFinish() {
                val unAttendantMode =referenceModel.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    if(intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!)
                    {
                        val mIntent = Intent(this@CheckCardCeilingsLimitsActivity, SplitPaymentActivity::class.java)
                        resetPayment()
                        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                        startActivity(mIntent)
                       finish()
                    }
                    else
                    {
                        val mIntent = Intent(this@CheckCardCeilingsLimitsActivity, MenuActivity::class.java)
                        startActivity(mIntent)
                       finish()
                    }
                }
            }
        }
        if(referenceModel.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()

        dialogButton.setOnClickListener {
            if(dialog.isShowing)
            {
                dialog.dismiss()
            }
            setBeep()
            timer.cancel()
            if(intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!)
            {
                val mIntent = Intent(this, SplitPaymentActivity::class.java)
                resetPayment()
                mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(mIntent)
                finish()
            }
            else
            {
                val mIntent = Intent(this, MenuActivity::class.java)
                startActivity(mIntent)
                finish()
            }
        }
        dialog.show()

    }
    override fun onBackPressed() {

    }
    //just for testing purpose
    // eof statut card

    private fun dummyCardLimits():Int {
        var isUpdated = false

        try {
            val readPlfPost = UtilsCardInfo.readInfoPlafondCardPost(mBankCard, icCpuReader, this@CheckCardCeilingsLimitsActivity)
            val readPlfPrep = UtilsCardInfo.readInfoPlafondCardPre(mBankCard, icCpuReader, this@CheckCardCeilingsLimitsActivity)

            if (UtilsCardInfo.verifyPIN(mBankCard, icCpuReader, intentExtrasModel!!.mPinNumberCard, this@CheckCardCeilingsLimitsActivity)) {

                val result =  UtilsCardInfo.updateInfoPlafondCardPost(readPlfPost,
                    ""+(totalMonthlyLimit * 100.toLong()),
                    ""+(totalWeeklyLimit * 100.toLong()),
                    ""+(totalDailyLimit * 100.toLong()),
                    255.toString() + "",
                    255.toString() + "",
                    255.toString() + "",
                    mBankCard,icCpuReader,this@CheckCardCeilingsLimitsActivity)

                val result2 = updateCardRemainingLimits(
                    readPlfPost,
                    ""+(0 * 100).toLong(),
                    ""+(0 * 100).toLong(),
                    ""+(0 * 100).toLong(),
                    "0",
                    "0",
                    "0",
                    mBankCard,
                    icCpuReader,
                    this@CheckCardCeilingsLimitsActivity
                )


                /*val status = UtilsCardInfo.setStatusCardF(mBankCard,icCpuReader,infoCarte,"F",this@CheckCardCeilingsLimitsActivity)  //reset card for activation
                val bal = UtilsCardInfo.getAmount(mBankCard, icCpuReader, 4, 12, this)

                val prd = "0000"
                val nowDate = Support.dateToStringX(Date())
                val transData = Utils.padZero(nowDate, 14).toString() + "00" + Utils.padZero(prefs.getPreferenceModel()!!.stationID.toString() + "", 4) + prd + (if (intentExtrasModel!!.mTransaction  != null) Support.dateToStringX(intentExtrasModel!!.mTransaction!!.dateTransaction!!) else "FFFFFFFFFFFFFF") + "FFFFFFFFFF"
                val ammtHex = Utils.amountToHex(bal.toString())
                UtilsCardInfo.debit(mBankCard, icCpuReader, ammtHex, authKey, transData, this)*/

                isUpdated = if (cardType == AppConstant.PREPAID_CARD) {
                    UtilsCardInfo.updateInfoPlafondCardPre(
                        readPlfPrep,
                        ""+(1 * 100).toLong(),
                        ""+totalMonthlyLimit * 100.toLong(),
                        "255",
                        "1",
                        "1",
                        "0",
                        intentExtrasModel!!.cardDiscountId.toString(),
                        mBankCard,icCpuReader,this@CheckCardCeilingsLimitsActivity)
                } else {
                    true
                }
            }


        }
        catch (e: java.lang.Exception)
        {
            e.printStackTrace()
            isUpdated = false
        }

        return if(isUpdated) 1 else -2
    }
    private fun updateCardRemainingLimits(
        readCard: String?,
        monthlyLimit: String?,
        weeklyLimit: String?,
        dailyLimit: String?,
        monthlyTrxLimit: String?,
        weeklyTrxLimit: String?,
        dailyTrxLimit: String?,
        mBankCard: BankCard?,
        icCpuReader: UICCpuReader?,
        context: Context?
    ): Boolean {
        var updatedRecord1 = ""
        val sm = sdf
        val date = Date()
        val dateplfrecord2 = sm.format(date).replace("-".toRegex(), "")

        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(readCard, 0, 12, Utils.padZero(monthlyLimit, 12))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 12, 24, Utils.padZero(weeklyLimit, 12))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 24, 36, Utils.padZero(dailyLimit, 12))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 36, 44, Utils.padZero(monthlyTrxLimit, 8))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 44, 52, Utils.padZero(weeklyTrxLimit, 8))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(updatedRecord1, 52, 60, Utils.padZero(dailyTrxLimit, 8))

        val result = if (isLitreUnit) {
            UtilsCardInfo.updateRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", updatedRecord1, context)
        } else {
            UtilsCardInfo.updateRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", updatedRecord1, context)
        }
        return result.contains("9000")
    }

    fun sampleCode(view: View) {
        if(BuildConfig.DEBUG){
            dummyCardLimits()
            ReadCardAsyncTask().execute()
        }
    }

    override fun onResume() {
        super.onResume()

    }
    override fun onPause() {
        super.onPause()

    }
    private fun printLog() {
        log(TAG, "totalMonthlyLimit ::: $totalMonthlyLimit")
        log(TAG, "totalWeeklyLimit ::: $totalWeeklyLimit")
        log(TAG, "totalDailyLimit ::: $totalDailyLimit")
        log(TAG, "remainingMonthlyAmount ::: $remainingMonthlyAmount")
        log(TAG, "remainingWeeklyAmount ::: $remainingWeeklyAmount")
        log(TAG, "remainingDailyAmount ::: $remainingDailyAmount")
        log(TAG, "REMAINING_NBR_TRS_MONTHLY ::: $REMAINING_NBR_TRS_MONTHLY")
        log(TAG, "REMAINING_NBR_TRS_WEEKLY ::: $REMAINING_NBR_TRS_WEEKLY")
        log(TAG, "REMAINING_NBR_TRS_DAILY ::: $REMAINING_NBR_TRS_DAILY")
        log(TAG, "cardBalance ::: $cardBalance")
        log(TAG, "cardBalance Actual ::: "+ UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this))
        log(TAG, "CARD_NBR_TRS_OFF ::: $CARD_NBR_TRS_OFF")
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - cardBalance - "+UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this)))
    }

    fun getDiscountAmount():Double
    {
        var discountAmount = 0.0
        if(!prefs.getDiscountDetails().isNullOrEmpty())
        {
            log(TAG,"Discount Details:: "+prefs.getDiscountDetails())

            var trxAmount = 0.0
            var qty  = 0.0
            if(intentExtrasModel!!.mTransaction != null) {
                qty = intentExtrasModel!!.mTransaction!!.quantite!!
                trxAmount = intentExtrasModel!!.mTransaction!!.amount!!
                productID = intentExtrasModel!!.mTransaction!!.idProduit!!.toInt()
            } else {
                qty = intentExtrasModel!!.amount!!.toDouble() / intentExtrasModel!!.selectedPrice!!.unitPrice
                trxAmount = intentExtrasModel!!.amount!!.toDouble()
                productID = intentExtrasModel!!.articleID!!.toInt()
            }


            for(discount in prefs.getDiscountDetails()!!) {
                if (((discount.discount_for == null || discount.discount_for == 0  || discount.discount_for == ALL_CARDS)
                            || (discount.discount_for == ALL_PREPAID_CARDS && cardType == PREPAID_CARD)
                            || (discount.discount_for == ALL_POSTPAID_CARDS && cardType == POSTPAID_CARD) //Validate card type,if any one condition true go to next validation
                            )
                    && (discount.discount_id == intentExtrasModel!!.cardDiscountId)   // Check is Discount applicable for this card
                    && (isDateWithinRange(discount.date_from!!, discount.date_to!!))    // Check Current date is within the discount applicable date range
                    && (isTimeWithinRange(discount.time_from!!, discount.time_to!!))     // Check Current time is within the discount applicable time
                    && ((discount.date_time_range_type == AppConstant.DISCOUNT_ALL_DAY)    // Check is Discount available for All Day  or
                            || (discount.date_time_range_type == AppConstant.DISCOUNT_SPECIFIC_DAY
                            && isCurrentDayAvailable(discount.specific_day!!))) //If Discount applicable for specific day check specific day value is 1
                ) {
                    for (scheme in discount.scheme) {
                        if ((scheme.fbs_prod_id == productID)// Check selected product available on discount scheme
                            && (qty >= scheme.qty_range_from!!)
                            && ((scheme.is_unlimited_range!!) || (qty <= scheme.qty_range_to!!))  // Qty should be within the range or unlimited range
                        )
                        {

                            if (scheme.discount_type == AppConstant.PERCENTAGE) {
                                discountAmount = (scheme.discount_value!!.toDouble() / 100) * trxAmount
                                intentExtrasModel!!.isDiscountTransaction =true
                                intentExtrasModel!!.cardDiscountId = discount.discount_id
                                if(intentExtrasModel!!.mTransaction != null)
                                {
                                    intentExtrasModel!!.mTransaction!!.discountPercentage = scheme.discount_value
                                }
                            } else if (scheme.discount_type == AppConstant.FIXED) {
                                discountAmount = scheme.discount_value!!.toDouble() * qty
                                intentExtrasModel!!.isDiscountTransaction =true
                                intentExtrasModel!!.cardDiscountId = discount.discount_id
                                if(intentExtrasModel!!.mTransaction != null)
                                {
                                    val discountPercentage=(scheme.discount_value.toDouble() / trxAmount) * 100
                                    intentExtrasModel!!.mTransaction!!.discountPercentage = discountPercentage.toString()
                                }
                            }
                            if(discountAmount > trxAmount)
                            {
                                discountAmount = trxAmount
                                log(TAG, "Discount Amount greater than trx amount: $discountAmount")
                            }
                            break
                        }
                    }
                }
            }
        }
        intentExtrasModel!!.discountAmount  = stringFormatDouble(discountAmount)
        return discountAmount
    }
fun validateFleetcard()
{
    if (REMAINING_NBR_TRS_DAILY >= NBR_TRS_DAILY && NBR_TRS_DAILY != 0 ||
        REMAINING_NBR_TRS_WEEKLY >= NBR_TRS_WEEKLY && NBR_TRS_WEEKLY != 0 ||
        REMAINING_NBR_TRS_MONTHLY >= NBR_TRS_MONTHLY && NBR_TRS_MONTHLY != 0 ||
        (CARD_NBR_TRS_OFF >= NBR_TRS_OFF && stationMode == OFFLINE_TRX_MODE)
    ) {
        UtilsCardInfo.beep(mCore, 10)
        var msg =   resources.getString(R.string.nbr_trx_error)
        if(stationMode == OFFLINE_TRX_MODE)
        {
            msg =  resources.getString(R.string.nbr_trx_off_error)
        }

        mBinding.amountWarningMsg.visibility = View.VISIBLE
        mBinding.amountWarningMsg.text = msg
        mBinding.submitBtn.visibility = View.GONE
        if(referenceModel.TERMINAL_TYPE != AppConstant.UN_ATTENDANT_MODE)
            mBinding.modePayBtn.visibility = View.VISIBLE

    }
    checkValidationForEnterAmount(amount)
    checkValidationForFullTank(amount)
    if (cardBalance == 0.0) {
        mBinding.amountWarningMsg.visibility = View.VISIBLE
        mBinding.amountWarningMsg.text = resources.getString(R.string.soldeCardErr)
        mBinding.submitBtn.visibility = View.GONE

    }
}
}