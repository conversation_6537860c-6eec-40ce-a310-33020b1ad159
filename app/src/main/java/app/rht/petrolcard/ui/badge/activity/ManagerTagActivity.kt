package app.rht.petrolcard.ui.badge.activity

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.*
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.databinding.ActivityBadgeBinding
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.model.GasStationAttendantModel

import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.fuelprice.activity.FuelPriceActivity
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.paxutils.modules.picc.PiccTester
import com.afollestad.materialdialogs.MaterialDialog
import com.pax.dal.entity.EPiccType
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.HEX
import java.lang.ref.WeakReference
import kotlin.collections.ArrayList

@Suppress("DEPRECATION")
class ManagerTagActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private val TAG = ManagerTagActivity::class.simpleName
    private lateinit var mBinding: ActivityBadgeBinding
    private var mBankCard: BankCard? = null
    var tagNFC: String? = null
    private var tagUID: String? = null
    private var piccType: EPiccType? = null
    private var alert: AlertDialog? = null
    var stationMode = 0
     var managerBadges : ArrayList<GasStationAttendantModel>? = null
    private var intentExtrasModel: IntentExtrasModel? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_badge)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
            val mUsersDao = UsersDao()
            mUsersDao.open()
            managerBadges = mUsersDao.getTagsByRole(AppConstant.MANAGER_ROLE_ID)
            mUsersDao.close()
            log(TAG, "badges: $managerBadges")
        setupToolbar()

        readCardNfc()
    }

    lateinit var readCardTask : ReadCardAsyncTask
    private fun readCardNfc(){
        cancelPreviousNfcScanIfRunning()
        readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
    }
    private fun cancelPreviousNfcScanIfRunning(){
        try {
            if(::readCardTask.isInitialized) {
                readCardTask.cancel(true)
            }
        } catch (e:Exception){e.printStackTrace()}
    }
    override fun onStop() {
        cancelPreviousNfcScanIfRunning()
        super.onStop()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarBadge.toolbar.tvTitle.text = getString(R.string.nfc_verification)
        mBinding.toolbarBadge.toolbar.setNavigationOnClickListener {
            try {
                mBinding.toolbarBadge.toolbar.isEnabled = false
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    if (mBankCard != null && mCore != null) {
                        mBankCard!!.breakOffCommand()
                        val ret = mBankCard!!.openCloseCardReader(BankCard.CARD_MODE_PICC, 0x02)
                    }
                }
                if(BuildConfig.POS_TYPE == "PAX")
                {
                    PiccTester.getInstance(piccType!!).close()
                }
                showSnakeBarColor(getString(R.string.transaction_cancelled),true)
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
            } catch (e: RemoteException) {
                e.printStackTrace()
            }}
    }


    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        var resultat = 0
        lateinit var respdata: ByteArray
        lateinit var resplen: IntArray
        var retvalue = 0
        lateinit var sn: ByteArray
        lateinit var pes: IntArray
        var resSN = 0
        override fun doInBackground(vararg params: String): Int {
            resultat = 0
            respdata = ByteArray(28)
            resplen = IntArray(1)
            retvalue = -1
            sn = ByteArray(16)
            pes = IntArray(1)
            resSN = 0
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(applicationContext)
                    tagNFC = "NO BADGE"
                    if (mBankCard != null) retvalue = mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL,
                        BankCard.CARD_MODE_PICC,
                        60,
                        respdata,
                        resplen,
                        AppConstant.TPE_APP
                    )

                    if (mBankCard != null) resSN = mBankCard!!.getCardSNFunction(sn, pes)
                    tagUID = HEX.bytesToHex(sn)
                    if (tagUID != null) {
                        log("tagUID =>", "tagUID--->>>$tagUID")
                    } else {
                        log("tagUID =>", "tagUID--->>>" + null)
                        return 0
                    }
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    var i = 0
                    piccType = EPiccType.INTERNAL
                    PiccTester.getInstance(piccType!!).open()
                    var tag: String = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    while (tag.equals("", ignoreCase = true) && i < 20) {
                        i++
                        Thread.sleep(500)
                        tag = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    }
                    if (tag.isNotEmpty()) tagUID = tag
                }
            } catch (e: RemoteException) {
                e.printStackTrace()
                return 0
            } catch (e: InterruptedException) {
                e.printStackTrace()
                return 0
            } catch (e: Exception) {
                e.printStackTrace()

            }
            var nfcLength = 0
            var minLength = 5
            var maxLength = 14

            if(BuildConfig.POS_TYPE == "PAX" && tagUID != null)
            {
                nfcLength = tagUID!!.length
            }
            else if(BuildConfig.POS_TYPE == "B_TPE")
            {
                nfcLength =  pes[0]
            }
            if(prefs.getReferenceModel()!!.station!!.nfc_read_min_length != null) {
                minLength = prefs.getReferenceModel()!!.station!!.nfc_read_min_length!!
                maxLength = prefs.getReferenceModel()!!.station!!.nfc_read_max_length!!
            }
            if (nfcLength in minLength..maxLength) {
                setBeep()
                tagNFC = if (tagUID!!.length >= 14) {
                    tagUID!!.substring(0, 14)
                } else {
                    tagUID
                }
                log("tagNFC =>", "tagNFC--->>>$tagNFC")

                try{


                        if (!managerBadges.isNullOrEmpty()) {
                            for (badge in managerBadges!!) {
                                resultat = 1
                                if (tagNFC == badge.tag) {
                                    intentExtrasModel!!.badge = tagNFC
                                    intentExtrasModel!!.tagsNFC = tagNFC
                                    resultat = 2
                                    intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Manager verification Tag success - "+tagNFC))

                                    break
                                }
                            }
                        } else {
                            resultat = 1
                        }

                } catch (e:java.lang.Exception){
                    e.printStackTrace()
                }
                /*resultat = if (tagNFC == prefs.badge) {
                    2
                } else {
                    1
                }*/
            } else {
                resultat = 0
            }
            return resultat
        }
        override fun onPreExecute() {

        }
        override fun onPostExecute(message: Int?) {

            if (message == 1) {
                showRetryDialog(resources.getString(R.string.scan_other_tag))
            }
            else if(message == 0)
            {
                //showRetryDialog(resources.getString(R.string.tag_nfc_ko))
                if (!isFinishing && !isDestroyed) {
                    showRetryDialog(resources.getString(R.string.tag_nfc_ko))
                }
            }
            else if (message == 2) {
                gotoNextScreen()
            }

        }
        override fun onProgressUpdate(vararg values: IntArray) {

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }

    var isDialogShown = false
    private fun showRetryDialog(message :String){
        var timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                //here you can have your logic to set text to edittext
             //   println("Popup Time Remaining: $minutes:$seconds")
            }

            override fun onFinish() {
                val unAttendantMode = prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    gotoAbortMessageActivity(getString(R.string.error), getString(R.string.transaction_cancelled))
                }
            }
        }
        if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()

        if (!(this as Activity).isFinishing && !isDialogShown) {
            isDialogShown = true
            try{
                MyMaterialDialog(
                    this,
                    getString(R.string.error),
                    "" + message,
                    getString(R.string.yes),
                    getString(R.string.no),
                    object : MyMaterialDialogListener {
                        override fun onPositiveClick(dialog: MaterialDialog) {
                            setBeep()
                            timer.cancel()
                            dialog.dismiss()
                            isDialogShown = false
                            readCardNfc()
                        }

                        override fun onNegativeClick(dialog: MaterialDialog) {
                            setBeep()
                            timer.cancel()
                            dialog.dismiss()
                            isDialogShown = false
                            gotoAbortMessageActivity(  getString(R.string.error), getString(R.string.transaction_cancelled))
                        }
                    })
            } catch (e:java.lang.Exception){
                e.printStackTrace()
            }
        }
    }

    override fun setObserver() {

    }

    override fun onDestroy() {
        if(BuildConfig.POS_TYPE == "PAX")
        {
            PiccTester.getInstance(piccType!!).close()
        }
        super.onDestroy()
    }
    override fun onBackPressed() {

    }
    private fun gotoNextScreen() {

        if (intentExtrasModel!!.workFlowTransaction == Workflow.PRICE_CHANGE) {
            intentExtrasModel!!.priceChangeVerified = true
            val intent = Intent()
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            setResult(AppConstant.CARD_NFC_BADGE, intent)
            finish()
        }
        else {
            val intent = Intent(this, SettingsActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
            finish()
        }
    }
}
