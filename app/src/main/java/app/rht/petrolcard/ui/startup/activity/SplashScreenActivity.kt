package app.rht.petrolcard.ui.startup.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.*
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivitySplashBinding
import app.rht.petrolcard.ui.Dw14PrinterActivity
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.activity.ReferenceActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.startup.viewmodel.StartupViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.DEVICE_NAVIGATION_BAR
import app.rht.petrolcard.utils.constant.AppConstant.DEVICE_STATUS_BAR
import app.rht.petrolcard.utils.helpers.KeyControlUtil
import app.rht.petrolcard.utils.paxutils.system.SysTester
import com.pax.dal.entity.ENavigationKey
import java.lang.Exception

@SuppressLint("CustomSplashScreen")
class SplashScreenActivity : BaseActivity<StartupViewModel>(StartupViewModel::class) {
    private var TAG = SplashScreenActivity::class.java.simpleName
    private lateinit var mBinding: ActivitySplashBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_splash)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        log(TAG,"CurrentActivity")
        mBinding.executePendingBindings()
        if (prefs.isRestartApplication == "false") {
            mBinding.message.visibility = View.GONE
        }

    }

    override fun onStart() {
        super.onStart()
        mBinding.animationView.playAnimation()
        if(BuildConfig.POS_TYPE == "PAX")
        {
            SysTester.getInstance().enableNavigationKey(ENavigationKey.HOME,true)
        }
        getFirebaseRegId()
        val serialNumber: String? = getSN()
        if(prefs.isFirstTimeLoadingApp)
        {
            Runtime.getRuntime().gc()
            prefs.isFirstTimeLoadingApp=false
            prefs.savePreferenceModel(
                PreferenceModel(blockListVersionNo = 1,greyListVersionNo = 1,firstTimeLoadingApp = false,aReferencer = true,blockage = false,suggestedPrice = false,
                    tlcTimeStamp = System.currentTimeMillis(),transactionLimit =  AppConstant.CEILING_TRANSACTION,
                    stationTitle = AppConstant.VERSION + " | " + AppConstant.STATION_NAME + " (" + AppConstant.ID_STATION + ")",
                    //badgeGrants = AppConstant.BADGE_GRANTS,
                    badgeGrants = ArrayList(),
                    occuranceNetwork = true,BATTERY_ALERT = 10,RFID_VERIFICATION_TYPE = 1,TELECOLLECT_TIME = 15,MAX_REFILL_AMNT = AppConstant.MAX_REFILL_AMNT)
            )
            prefs.saveBooleanSharedPreferences(DEVICE_STATUS_BAR,true)
            prefs.saveBooleanSharedPreferences(DEVICE_NAVIGATION_BAR,true)
        }
                log(TAG,"Is Restart :: "+prefs.isRestartApplication)
                if (prefs.isRestartApplication == "true") {
                    mBinding.message.visibility = View.VISIBLE
                    mBinding.message.text = getString(R.string.please_wait_st)
                }
                else {
                    mBinding.message.text = getString(R.string.processing)
                }
               // prefs.transactionCount = 0
                goToNextScreen()
    }

    private fun goToNextScreen() {
        Handler(Looper.getMainLooper()).postDelayed({
            if (prefs.isReferenceLoad) {
                val i = Intent(this, ReferenceActivity::class.java)
                startActivity(i)
                //finish()
            } else {
                var i = Intent(this, MenuActivity::class.java) //
               /* if(BuildConfig.DEBUG){
                    i = Intent(this, Dw14PrinterActivity::class.java)
                }*/
                startActivity(i)
             //   finish()
            }
        }, SPLASH_DISPLAY_LENGTH.toLong())
    }

    companion object {
        private const val SPLASH_DISPLAY_LENGTH = 3000
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        val menuInflater = menuInflater
        menuInflater.inflate(R.menu.menu, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            R.id.about -> {
//                val i1 = Intent(this, AboutActivity::class.java)
//                startActivity(i1)
                true
            }
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun kioskModeON() {
       try{
           if (BuildConfig.POS_TYPE == "B_TPE") {
               // mode kiosk ON
               KeyControlUtil.setStatusbarMode(1)
               KeyControlUtil.setPropForControlBackKey(false)
               KeyControlUtil.setPropForControlHomeKey(true)
               KeyControlUtil.setPropForControlMenuKey(true)
           }
           // end of mode kiosk on
       } catch (e:Exception) {
           e.printStackTrace()
       }
    }
    private fun kioskModeOFF() {
       try {
           // mode kiosk off
           if (BuildConfig.POS_TYPE == "B_TPE") {
               KeyControlUtil.setStatusbarMode(0)
               KeyControlUtil.setPropForControlBackKey(false)
               KeyControlUtil.setPropForControlHomeKey(false)
               KeyControlUtil.setPropForControlMenuKey(false)
           }
           // end of mode kiosk off
       } catch (e: Exception){
           e.printStackTrace()
       }
    }

    override fun onResume() {
        super.onResume()
        if (BuildConfig.KIOSK) {
            kioskModeON()
        } else {
            kioskModeOFF()
        }
        // end mode kiosk
        prefs.navigationStatus = true
    }

    override fun setObserver() {

    }
    override fun finish() {

    }

}
