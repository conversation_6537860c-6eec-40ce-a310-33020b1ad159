package app.rht.petrolcard.ui.startup.model

import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.ui.reference.model.ManagerBadge

class PreferenceModel(
    var blockListVersionNo: Int? = 1,
    var greyListVersionNo: Int? = 1,
    var firstTimeLoadingApp: Boolean? = false,
    var aReferencer: Boolean? = false,
    var blockage: Boolean? = false,
    var suggestedPrice: Boolean? = false, //prixConseille in old app variable name
    var tlcTimeStamp: Long? = 0 ,
    var transactionLimit: String? = "" , // plafondTransactionTPE in old app variable name
    var rechargeLimit: String? = "" ,
  /*  var serialNumber: String? = "",*/
    var stationTitle: String? = "",
    //var badgeGrants: String? = "",
    var badgeGrants: ArrayList<ManagerBadge> = ArrayList(),
    var terminalID: Int? = 0,
    var stationID: Int? = 0,
    var sectorId: Int? = 0,
    var occuranceNetwork: Boolean? = false,
    val BATTERY_ALERT: Int?,
    val RFID_VERIFICATION_TYPE: Int?,
    val TELECOLLECT_TIME: Long?,
    val MAX_REFILL_AMNT: String?
) : BaseModel()