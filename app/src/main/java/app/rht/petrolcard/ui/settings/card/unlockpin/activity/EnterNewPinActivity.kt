package app.rht.petrolcard.ui.settings.card.unlockpin.activity

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import android.widget.TextView
import app.rht.petrolcard.databinding.ActivityNewPinBinding
import app.rht.petrolcard.ui.settings.card.changepin.activity.ChangePinActivity
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.passwordview.ActionListener
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import kotlinx.android.synthetic.main.toolbar.view.*

@Suppress("DEPRECATION")
class EnterNewPinActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private var TAG= "EnterNewPinActivity"
    private lateinit var mBinding: ActivityNewPinBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    private var pinCard = ""
    var authKey = ""
    var pinPrevious = ""
    var pinConfirm = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_new_pin)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupNumberKeys()
        setupToolbar()
        getIntentExtras()
    }

    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        mBinding.pinMessage.text= resources.getString(R.string.enter_new_pin)
    }
    private fun setupToolbar()
    {
        mBinding.toolbarChangePin.toolbar.tvTitle.text = getString(R.string.unblock_pin)
        mBinding.toolbarChangePin.toolbar.setNavigationOnClickListener {
            mBinding.toolbarChangePin.toolbar.isEnabled = false
            showSnakeBarColor(getString(R.string.transaction_cancelled), true)
            Handler(Looper.getMainLooper()).postDelayed({
                finish()
            }, 2000) }
    }
    private fun setupNumberKeys()
    {
      mBinding.text0.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text1.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text2.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text3.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text4.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text5.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text6.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text7.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text8.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.text9.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
         mBinding.textD.setOnClickListener { setBeep()
            mBinding.passwordView.removeInputText()
             mBinding.passwordView.reset()
             pinCard = ""
         }
         mBinding.textSubmit.setOnClickListener {
           validatePin()
        }
        mBinding.passwordView.setListener(object : ActionListener {
            override fun onCompleteInput(inputText: String) {
                pinCard = inputText
            }

            override fun onEndJudgeAnimation() {
                mBinding.passwordView.reset()
            }
        })

    }
    fun validatePin()
    {
       setBeep()
        if(pinPrevious.isNotEmpty() && mBinding.passwordView.passwordCount == 4)
        {
            pinConfirm = pinCard
        }
        if(pinCard.length < 4)
        {
            showSnakeBar(getString(R.string.enter_4_digit_pin))

        }
         else if(pinPrevious.isEmpty() && pinCard.length == 4)
        {
            pinPrevious = pinCard
            mBinding.pinMessage.text = getString(R.string.confirm_new_pin)
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
        }
        else if(pinPrevious != pinConfirm)
        {
            showSnakeBarColor(getString(R.string.same_pin_no),true)
        }
        else if(pinPrevious.isNotEmpty() && pinConfirm.isNotEmpty() && pinConfirm == pinPrevious)
        {
            if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_CHANGE_PIN){
                val i = Intent(this, ChangePinActivity::class.java)
                intentExtrasModel!!.mPinNew = pinConfirm
                i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(i)
            }
            else {
                val i = Intent(this, UnlockPinActivity::class.java)
                intentExtrasModel!!.mPinNew = pinConfirm
                i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(i)
            }

        }
        else
        {
            showSnakeBarColor(getString(R.string.same_pin_no),true)
        }

    }
    override fun setObserver() {

    }
    override fun onBackPressed() {

    }
}
