package app.rht.petrolcard.ui.transactionlist.model

import android.graphics.Color
import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.ui.reference.model.PriceModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import java.lang.Exception

class OfflineProductsModel (
    val priceModel:PriceModel?,
    val productsModel:ProductModel?,
    val priceFullValue:String?,
    val colorCode:String?,
    val icon:String?
):BaseModel() {
    fun getColorValue(): Int {
        return try {
            if (colorCode != null)
                Color.parseColor(colorCode)
            else
                Color.parseColor("#ffffff")
        } catch (e: Exception) {
            Color.parseColor("#177AF9")
        }

    }

}