package app.rht.petrolcard.ui.settings.operations.activity.fpos

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.DividerItemDecoration
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.PendingFPosTrxDao
import app.rht.petrolcard.databinding.ActivityPendingFuelPosTrxBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.fuelpos.models.TransactionData
import kotlinx.android.synthetic.main.toolbar.view.*


class PendingFuelPosTrxActivity : BaseActivity<CommonViewModel>(CommonViewModel::class)  {

    private var TAG = PendingFuelPosTrxActivity::class.java.simpleName
    private lateinit var mBinding: ActivityPendingFuelPosTrxBinding


    var transactionList: ArrayList<TransactionData> = ArrayList()
    var intentExtrasModel: IntentExtrasModel? = null
    lateinit var transactionAdapter: FuelPosTrxAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_pending_fuel_pos_trx)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        initViews()
        prepareTransactionData()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.fuelpos_pending_transaction)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish()
        }
    }

    override fun setObserver() {

    }

    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }

/*    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menuInflater.inflate(R.menu.menu_main, menu)

        // Associate searchable configuration with the SearchView
        val searchManager = getSystemService<Any>(Context.SEARCH_SERVICE) as SearchManager
        searchView = menu.findItem(R.id.action_search).getActionView() as SearchView
        searchView.setSearchableInfo(searchManager.getSearchableInfo(componentName))
        searchView.setMaxWidth(Int.MAX_VALUE)

        // listening to search query text change
        searchView.setOnQueryTextListener(object : OnQueryTextListener() {
            fun onQueryTextSubmit(query: String?): Boolean {
                // filter recycler view when query submitted
                clubAdapter.getFilter().filter(query)
                return false
            }

            fun onQueryTextChange(query: String?): Boolean {
                // filter recycler view when text is changed
                clubAdapter.getFilter().filter(query)
                return false
            }
        })
        return true
    }*/

    private fun initViews() {
        setUpRecyclerView()
    }


    private fun setUpRecyclerView() {
        transactionAdapter = FuelPosTrxAdapter(this, transactionList)
        val manager = FixedGridLayoutManager()
        manager.setTotalColumnCount(1)
        mBinding.rvTransactions.layoutManager = manager
        mBinding.rvTransactions.adapter = transactionAdapter
        mBinding.rvTransactions.addItemDecoration(DividerItemDecoration(this, DividerItemDecoration.VERTICAL))
    }

    private fun prepareTransactionData() {
        val item = TransactionData()
       /* item.id = 0
        item.trxToken = "1234567890"
        item.pump = "1"
        item.completionCode = "06"
        item.currency = "AED"
        item.checksum = "AS312K3213"
        item.dateTime = "202211110405"
        item.productName = "Gasoil 10"
        item.productCode = "125"
        item.quantity = "10"
        item.unitPrice = "1000"
        item.vatPercentage = "18"
        item.vatAmount = "18"
        item.totalAmount = "11800"
        item.teleCollectStatus = 1*/

        transactionList.add(item)

        val pendingFPosTrxDao = PendingFPosTrxDao()
        pendingFPosTrxDao.open()
        transactionList.addAll(pendingFPosTrxDao.get())
        pendingFPosTrxDao.close()

    }
}