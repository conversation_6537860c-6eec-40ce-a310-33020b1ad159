package app.rht.petrolcard.ui.loyalty.activity

import android.content.Intent
import android.os.*
import android.util.Log
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityIccLoyaltyBinding
import app.rht.petrolcard.ui.iccpayment.model.ChipResponse

import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.CARD_NFC_TAG
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_ACTIVATION_REQUEST
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_CARD_HOLDER
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_CARD_NUMBER
import com.daimajia.androidanimations.library.YoYo
import com.usdk.apiservice.aidl.emv.*
import com.usdk.apiservice.aidl.icreader.DriverID
import com.usdk.apiservice.aidl.icreader.OnInsertListener
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.Core

@Suppress("DEPRECATION")
class ICCLoyaltyActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {


    private lateinit var mBinding: ActivityIccLoyaltyBinding
    private lateinit var scanIccTask : ScanningICCTask
    companion object{
        private val TAG = ICCLoyaltyActivity::class.simpleName
        private var activationRequest = false

    }

    var requestType = 0

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_icc_loyalty)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        setupInitialData()
    }

    override fun onDestroy() {

        scanIccTask.cancel(true)

        if (BuildConfig.POS_TYPE == "PAX") {
            UtilsCardInfo.disconnectPAX()
        }

        Thread.currentThread().interrupt()

        super.onDestroy()
    }

    override fun onBackPressed() {
        // super.onBackPressed()
        UtilsCardInfo.beep(mCore, 10)

        try {
            if (BuildConfig.POS_TYPE == "B_TPE") {
                mBankCard!!.breakOffCommand()
                mBankCard!!.openCloseCardReader(BankCard.CARD_MODE_ICC, 0x02)
            }
        } catch (ex: RemoteException) {
            ex.stackTrace
            super.finish()
        } catch (ex: UninitializedPropertyAccessException) {
            ex.stackTrace
            super.finish()
        }
        super.finish()
    }

    private fun setupInitialData(){

        setupToolbar()

         if(intent.hasExtra(LOYALTY_ACTIVATION_REQUEST))
        {
              activationRequest = intent.getBooleanExtra(LOYALTY_ACTIVATION_REQUEST,false)
         }

        //startCardAnim()


        if (BuildConfig.POS_TYPE.equals("B_TPE",true)) {
            object : Thread() {
                override fun run() {
                    mCore = Core(applicationContext)
                }
            }.start()
        }

        if (BuildConfig.POS_TYPE != "LANDI") {
            scanIccTask = ScanningICCTask()
            scanIccTask.execute()
        } else {
            try {
                searchCard()
            } catch (ex: RemoteException) {
                ex.printStackTrace()
            }
        }

    }

    private fun setupToolbar()
    {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.card_reading)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            onBackPressed()
        }
    }

    private var cardAnimation : YoYo.YoYoString? = null
    private fun startCardAnim(){
       /* cardAnimation = YoYo.with(Techniques.Wave)
            .duration(1500)
            .repeat(YoYo.INFINITE)
            .playOn(mBinding.ivCardImage)*/
    }
    private fun stopCardAnim(){
        if(cardAnimation!!.isRunning)
            cardAnimation!!.stop()
    }

    private fun showLoading(isVisible:Boolean){
        if(isVisible)
            mBinding.spinKit.visibility = View.VISIBLE
        else
            mBinding.spinKit.visibility = View.INVISIBLE
    }
    private fun setMessage(message:String){
        mBinding.tvMessage.text  = message
    }

    override fun setObserver() {

    }



    //WPOS card reader object
    private var mBankCard: BankCard? = null

    //LANDI
    private lateinit var emv: UEMV
    private var icCpuReader: UICCpuReader? = null

    private var cardTagNFC = ""
    private var cardHolderName = ""
    private var pan = ""

    inner class ScanningICCTask : CoroutineAsyncTask<Void?, Void?, Boolean>() {
        var resultat = 0
        var respdata: ByteArray = ByteArray(40)
        var resplen: IntArray = IntArray(1)
        var retvalue: Int
        var sn: ByteArray
        var pes: IntArray
        var resSN: Int
        var bb = false
        var panSequence: String? = null
        var myResponse: ChipResponse?

        init {
            retvalue = -1
            sn = ByteArray(16)
            pes = IntArray(1)
            resSN = 0
            panSequence = null
            myResponse = null
        }


        override fun doInBackground(vararg params: Void?): Boolean {
            resultat = 1
            panSequence = null
            var migrateKeysBoolean = false
            var flagTemp: String? = null
            Log.v(TAG, "Scan carte ICC")
            try {

                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@ICCLoyaltyActivity)
                    retvalue = mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL,
                        BankCard.CARD_MODE_ICC,
                        60,
                        respdata,
                        resplen,
                        AppConstant.TPE_APP
                    )
                    log(TAG, "retvalue => $retvalue")
                    log(TAG, "respdata => $respdata")
                    log(TAG, "respdata Hex => " + Utils.byteArrayToHex(respdata))
                    log(TAG, "resplen => $resplen")
                    log(TAG, "resplen[0] => " + resplen[0])
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }

                if (Utils.byteArrayToHex(respdata).substring(0, 2) == "05" ||
                    Utils.byteArrayToHex(respdata).substring(0, 2) == "07" ||
                    BuildConfig.POS_TYPE == "LANDI" ||
                    BuildConfig.POS_TYPE == "PAX"
                ) {
                    var resultatToPrint: String? = null
                    val monFichierPan = UtilsCardInfo.getCardInfo(mBankCard, icCpuReader, this@ICCLoyaltyActivity)
                    cardHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this@ICCLoyaltyActivity)
                    if (monFichierPan != null && monFichierPan.isNotEmpty() && !monFichierPan.substring(68, 69).equals("3", ignoreCase = true)) {
                        resultat = 0
                        return false
                    }
                    if (monFichierPan != null) {
                        val panTemp = monFichierPan.substring(0, 19) // pan
                        flagTemp = monFichierPan.substring(79, 80) // flag
                        log(TAG, "La carte $panTemp a le flag $flagTemp")
                        resultatToPrint = panTemp

                        // si la carte n'a pas été traitée (flag 6) ; si traitée (flag 7)
                        if (flagTemp == "6") {
                            migrateKeysBoolean = migrateKey(monFichierPan)
                        }
                    }
                    if (resultatToPrint != null) {
                        panSequence = resultatToPrint
                        log("PAN Attarik Pro => ", panSequence!!)
                    }
                    var key1: String? = null
                    val infoCarte: String = UtilsCardInfo.getCardInfo(mBankCard, icCpuReader, this@ICCLoyaltyActivity)
                    if (infoCarte.isNotEmpty())
                        pan = infoCarte.substring(0, 19)
                    if (pan.isNotEmpty()) {
                        log(TAG, "PAN ---> $pan")
                        if (BuildConfig.POS_TYPE == "B_TPE") key1 = UtilsCardInfo.genKey(
                            mCore!!,
                            pan
                        ) else if (BuildConfig.POS_TYPE == "PAX") key1 =
                            UtilsCardInfo.genKeyPAX(pan) else {
                            if (pan.endsWith("00148"))
                                key1 = "6BB75C0B6EF18F44"
                            else if (pan.endsWith("00028"))
                                key1 = "11D85F74A9F8811E"
                        } // "F17FACC7383CD1BC"  PAN : 000324 || "358BDADAB21E9041"  PAN : 000258  ||| PAN : 00148 / key1 = "6BB75C0B6EF18F44" ||| PAN : 00028 / key1 = "11D85F74A9F8811E"
                    }
                    if (key1 != null && UtilsCardInfo.externalAuth1(mBankCard, icCpuReader, key1, this@ICCLoyaltyActivity) && UtilsCardInfo.externalAuth2(mBankCard, icCpuReader, key1, this@ICCLoyaltyActivity)) {
                        UtilsCardInfo.beep(mCore, 20)
                        if (activationRequest) {
                            val result: String = UtilsCardInfo.readBinaryFile(
                                mBankCard,
                                icCpuReader,
                                "2F20",
                                "5F",
                                this@ICCLoyaltyActivity
                            )
                            if (result.isEmpty()) {
                                resultat = 9
                                return false
                            }
                            Log.i(TAG, " result readBinaryFile --->$result")
                            var nfcCardCount = result.substring(result.length - 2, result.length)
                            Log.i(TAG, " Number of nfc Tag --->$nfcCardCount")
                            if (nfcCardCount == "00" || nfcCardCount == "FF") Log.i(
                                TAG, " Tag NFC not activated")
                            if (nfcCardCount == "FF") nfcCardCount = "00"
                            if (result.isNotEmpty() && result.length > 14)
                                cardTagNFC =result.substring(0, 14)
                        }
                    } else {
                        resultat = 0
                        bb = false
                    }
                } else {
                    bb = false
                    resultat = -2
                }

            } catch (e: RemoteException) {
                e.printStackTrace()
                log(TAG,e.message +" "+e.cause)
            }
            finally {
                log(TAG, "finally")
                if (pan.isEmpty()) {
                    resultat = 0
                    bb = false
                }
            }
            if (panSequence != null) {
                log(TAG, panSequence!!)
                bb = true
                if (flagTemp != null) {
                    log(TAG, "*** Attarik Pro Card***")
                    log(TAG, "The $panSequence card has the flag $flagTemp")
                    if (flagTemp == "6") {
                        if (!migrateKeysBoolean) {
                            bb = false
                            log(TAG, "The key change failed!")
                        } else {
                            bb = true
                            log(TAG, "The key change failed!")
                        }
                    }
                }
            } else {
                if (resultat != -2) resultat = 0
            }
            return bb
        }

        override fun onPostExecute(result: Boolean?) {
            super.onPostExecute(result)
            showLoading(false)

            try {
                if (BuildConfig.POS_TYPE.equals("LANDI",true))
                    UtilsCardInfo.powerDownLANDI(icCpuReader!!)
            }
            catch (ex: RemoteException) {
                ex.printStackTrace()
            }

            setBeep()
            when {
                result!! -> {
                    //return card holder name and card number in result
                    val resultIntent = Intent()
                    resultIntent.putExtra(LOYALTY_CARD_NUMBER,panSequence)
                    resultIntent.putExtra(LOYALTY_CARD_HOLDER,cardHolderName)
                    resultIntent.putExtra(CARD_NFC_TAG,cardTagNFC)
                    setResult(RESULT_OK, resultIntent)
                    finish()
                }
                resultat == 0 -> {
                    gotoAbortMessageActivity(  getString(R.string.invalid_card),getString(R.string.card_reading_failed))
                }
                resultat == 9 -> {
                    gotoAbortMessageActivity(  getString(R.string.unknown_card),getString(R.string.card_reading_failed))
                }
                else -> {
                    gotoAbortMessageActivity(  getString(R.string.error),getString(R.string.card_not_activated))
                }
            }
            /*else {
                setBeep()
                finish()
            }*/
        }

        private fun migrateKey(monFichierPan: String?): Boolean {
            return true
        }

        private fun pan(): String? {
            val rc = "00A40000022F09"
            val record = "00B2020428"
            val record1: ByteArray = Utils.hexStringToByteArray(rc)
            if (selectApp()) {
                var myResponse = executeApdu(record1, record1.size)
                log("Resp File => ", myResponse.toString())
                if (myResponse.status!!.substring(0, 4) == "9000") {
                    val data1: ByteArray = Utils.hexStringToByteArray(record)
                    myResponse = executeApdu(data1, data1.size)
                    log("Resp Record => ", myResponse.toString())
                    if (myResponse.status!!.substring(80, 84) == "9000") {
                        return myResponse.status!!.substring(0, 19)
                    }
                }
            }
            return null
        }

        private fun selectApp(): Boolean {
            val creditData = "00A4040008D1D2D3D4D5D6D7D800"
            val bytes: ByteArray = Utils.hexStringToByteArray(creditData)
            var myResponse  = executeApdu(bytes, bytes.size)
            log("Resp selectApp => ", myResponse.toString())
            return myResponse.status!!.substring(0, 4) == "9000"
        }
        override fun onCancelled() {
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") mBankCard!!.breakOffCommand()
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }

        private fun executeApdu(data: ByteArray?, length: Int): ChipResponse {
            val myResponse = ChipResponse()
            val respdata = ByteArray(100)
            val resplen = IntArray(1)
            var St = ""
            var apdufilepinret = -1
            try {
                apdufilepinret = mBankCard!!.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen)
                St = Utils.byteArrayToHex(respdata)
                myResponse.status = St
                myResponse.data = St
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
            return myResponse
        }


    }

    //region LANDI CARD
    @Throws(RemoteException::class)
    private fun getEMV(): UEMV? {
        return UEMV.Stub.asInterface(MainApp.getDeviceService()!!.emv)
    }

    @Throws(RemoteException::class)
    private fun getICCpuReader(): UICCpuReader {
            val iBinder = MainApp.getDeviceService()!!.getICReader(DriverID.ICCPU, null)
            return UICCpuReader.Stub.asInterface(iBinder)
    }

    @Throws(RemoteException::class)
    fun startEMV() {
        val param = Bundle()
        emv = getEMV()!!
        emv.startEMV(param, object : EMVEventHandler.Stub(){
            override fun onInitEMV() {
                TODO("Not yet implemented")
            }

            override fun onWaitCard(p0: Int) {
                TODO("Not yet implemented")
            }

            override fun onCardChecked(p0: Int) {
                TODO("Not yet implemented")
            }

            override fun onAppSelect(p0: Boolean, p1: MutableList<CandidateAID>?) {
                TODO("Not yet implemented")
            }

            override fun onFinalSelect(p0: FinalData?) {
                TODO("Not yet implemented")
            }

            override fun onReadRecord(p0: CardRecord?) {
                TODO("Not yet implemented")
                // Received this event on behalf of the card record has been completed, according to the main account number, public key index and other information to do the relevant operations.
                emv.respondEvent(null)
            }

            override fun onCardHolderVerify(p0: CVMMethod?) {
                TODO("Not yet implemented")
            }

            override fun onOnlineProcess(p0: TransData?) {
                TODO("Not yet implemented")
            }

            override fun onEndProcess(p0: Int, p1: TransData?) {
                TODO("Not yet implemented")
            }

            override fun onVerifyOfflinePin(
                p0: Int,
                p1: ByteArray?,
                p2: CAPublicKey?,
                p3: OfflinePinVerifyResult?
            ) {
                TODO("Not yet implemented")
            }

            override fun onObtainData(p0: Int, p1: ByteArray?) {
                TODO("Not yet implemented")
            }

            override fun onSendOut(p0: Int, p1: ByteArray?) {
                TODO("Not yet implemented")
            }

        })
    }


    @Throws(RemoteException::class)
    private fun searchCard() {
        icCpuReader = getICCpuReader()
        icCpuReader!!.searchCard(object : OnInsertListener.Stub() {
            @Throws(RemoteException::class)
            override fun onCardInsert() {

                // TODO Search card success event handling, such as: power up.
                startEMV()
                emv.respondCard()
                UtilsCardInfo.powerUpLANDI(icCpuReader!!)
                Log.i(TAG, "CARD INSERTED !")
                UtilsCardInfo.powerONLANDI(icCpuReader!!)
                if (UtilsCardInfo.isCardInLANDI(icCpuReader!!)) {
                    scanIccTask = ScanningICCTask()
                    //scanIccTask.executeOnExecutor(AsyncTask.THREAD_POOL_EXECUTOR)
                    scanIccTask.execute()
                } else {
                    log(TAG, "CARD NOT INSERTED")
                }
            }

            @Throws(RemoteException::class)
            override fun onFail(error: Int) {
                // TODO Error handling, error see ICError.
                log(TAG, "CARD INSERT ERROR !")
            }
        })
    }
    //endregion
}
