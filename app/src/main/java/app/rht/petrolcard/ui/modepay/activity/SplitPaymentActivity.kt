package app.rht.petrolcard.ui.modepay.activity

import android.app.AlertDialog
import android.app.ProgressDialog
import android.content.*
import android.os.Bundle
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.InputType
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivitySplitPayBinding
import app.rht.petrolcard.networkRequest.ApiClient
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.NotificationModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.loyalty.activity.ICCLoyaltyActivity
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.utils.NotificationUtils
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.mpesaservices.AccessToken
import app.rht.petrolcard.utils.mpesaservices.STKPush
import com.google.firebase.messaging.FirebaseMessaging
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.database.SQLiteException
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import ticker.views.com.ticker.widgets.circular.timer.callbacks.CircularViewCallback
import ticker.views.com.ticker.widgets.circular.timer.view.CircularView
import timber.log.Timber
import java.lang.Exception
import java.util.*
import kotlin.math.roundToInt

@Suppress("DEPRECATION")
class SplitPaymentActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<ModePaymentModel> {
    private var TAG = SplitPaymentActivity::class.simpleName
    private lateinit var mBinding: ActivitySplitPayBinding
    private var mApiClient: ApiClient? = null
    var inputManager: InputMethodManager? = null
    private var mRegistrationBroadcastReceiver: BroadcastReceiver? = null
    private var stationMode = 0
    // M-PESA
    private val mProgressDialog: ProgressDialog? = null
    private var intentExtrasModel: IntentExtrasModel? = null
    var typePay: String? = null
    var dialog: AlertDialog? = null
    var amountDialog: AlertDialog? = null
    var numberDialog: AlertDialog? = null
    var isRegistered = false
    var circular_view: CircularView? = null
    var dialogMessage: TextView? = null
    private lateinit var adapter : RecyclerViewArrayAdapter<ModePaymentModel>
    private var mList: ArrayList<ModePaymentModel> = ArrayList<ModePaymentModel>()
    var phoneNumber = ""
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_split_pay)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
                if(intentExtrasModel!!.loyaltyTrx)
                {
                    stationMode = 1
                }
        }
        if(intentExtrasModel!!.mTransaction == null)
        {
            createTransactionDetails()
        }
        setupRecyclerview()
        initListener()
        setupToolbar()
        setAmounts()
    }
    fun createTransactionDetails()
    {
        val mTransaction = TransactionModel()
        val mProduit = intentExtrasModel!!.selectedProduct
      
        if (intentExtrasModel!!.amount!=null && intentExtrasModel!!.amount!!.isNotEmpty())
          mTransaction.amount = intentExtrasModel!!.amount!!.toDouble()

        mTransaction.codePompiste = intentExtrasModel!!.mPinNumberAttendant
       mTransaction.idTerminal = prefs.getReferenceModel()!!.terminal!!.terminalId
       mTransaction.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
       mTransaction.idProduit = mProduit!!.productID
       mTransaction.dateTransaction = Support.dateToString(Date())
        mTransaction.flagController = 0
       mTransaction.sequenceController = ""
       mTransaction.kilometrage = ""
       mTransaction.pan = ""
       mTransaction.nomPorteur = ""
       mTransaction.detailArticle = ""
       mTransaction.flagTelecollecte = 0
       mTransaction.modepay = ""
       mTransaction.panLoyalty = ""
       mTransaction.pumpId =""
       mTransaction.soldeCard = ""
       mTransaction.panLoyalty = ""
       mTransaction.tagNFC = ""
       mTransaction.reference =  "TRX" + Support.generateReference(this)/*Support.generateReference(mTransaction!!.dateTransaction, intentExtrasModel!!.compteurTrxDebit, "", this)*/
        intentExtrasModel!!.mTransaction =mTransaction
    }
    private fun setupToolbar()
    {
        mBinding.toolbarModepay.toolbar.tvTitle.text = getString(R.string.split_payment)
        mBinding.toolbarModepay.toolbar.setNavigationOnClickListener {
            mBinding.toolbarModepay.toolbar.isEnabled = false
            finish() }
    }
    private fun setupRecyclerview() {
        mList.clear()
        if(prefs.getReferenceModel()!!.mode_of_payment_list != null)
        {
            mBinding.emptyList.visibility =View.GONE
            mBinding.mListView.visibility =View.VISIBLE
            val modeList= prefs.getReferenceModel()!!.mode_of_payment_list!!
            for(list in modeList)
            {
                var icon = 0
                var color = ""
                when (list.payment_id) {
                    1 -> {
                        icon = R.drawable.ic_card
                        color = "#feebea"
                    }
                    2 -> {
                        icon = R.drawable.ic_cash
                        color = "#dff5f8"
                    }
                    3 -> {
                        icon = R.drawable.ic_mobile
                        color = "#f3eefd"
                    }
                    4 -> {
                        icon = R.drawable.ic_qr_code
                        color = "#fff2de"
                    }
                    5 -> {
                        icon = R.drawable.ic_rfid
                        color = "#edf8e2"
                    }
                    7 -> {
                        icon = R.drawable.ic_visa
                        color = "#CACFEC"
                    }

                }
//                if(!(intentExtrasModel!!.isFirstPaymentDone!! && intentExtrasModel!!.mTransaction!!.modepay!!.toInt() != list.payment_id) && list.payment_id == 8)
//                {
//                    mList.add(ModePaymentModel(list.payment_id, list.payment_name, color, icon))
//                }
               log(TAG,"list.payment_id:: "+list.payment_id)
               log(TAG,"intentExtrasModel!!.isFirstPaymentDone!! "+intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
               log(TAG,"intentExtrasModel!!.mTransaction!!.modepay "+intentExtrasModel!!.mTransaction!!.modepay)
                if(list.payment_id != 8 && intentExtrasModel!!.splitPaymentModel!!.firstModeOfPayment != list.payment_id) // previously paid mode of payment not add into list
                {
                    val mItem = list
                    mItem.payment_id = list.payment_id
                    mItem.payment_name = list.payment_name
                    mItem.color = color
                    mItem.order = list.order
                    mItem.image_url = ""

                    mList.add(mItem)
                }


                continue
            }
        }
        else {
            mBinding.emptyList.visibility =View.VISIBLE
            mBinding.mListView.visibility =View.GONE
        }

        adapter = RecyclerViewArrayAdapter(mList,this)
       mBinding.mListView.adapter = adapter
        adapter.setContext(this)
     adapter.notifyDataSetChanged()
    }
    fun getAccessToken() {
        var url: String = AppConstant.BASE_URL_MPESA_TEST + AppConstant.MPESA_AUTHENTICATION_URL
        if (prefs.getReferenceModel()!!.mpesa_credentials!!.payment_mode != "test") {
            url = AppConstant.BASE_URL_MPESA_PRODUCTION + AppConstant.MPESA_AUTHENTICATION_URL
        }
        mApiClient!!.setGetAccessToken(true)
        mApiClient!!.mpesaService(this).getAccessToken(url)
            .enqueue(object : Callback<AccessToken> {
                override fun onResponse(call: Call<AccessToken>, response: Response<AccessToken>) {
                    if (response != null && response.isSuccessful) {
                        mApiClient!!.setAuthToken(response.body()!!.accessToken)
                        typePay = AppConstant.MOBILE_VALUE
                        intentExtrasModel!!.typePay = typePay
                        if (prefs.getReferenceModel()!!.mpesa_credentials == null) {
                            showDialog(
                                getString(R.string.mobile_payment_not_allowed),
                                getString(R.string.setup_mpesa_credentials)
                            )
                        } else {
                            showCheckoutDialog()
                        }
                    }
                    else
                    {
                        showDialog(getString(R.string.invalid_consumer_details),response.message())
                    }
                }

                override fun onFailure(call: Call<AccessToken>, t: Throwable) {
                    showDialog(getString(R.string.invalid_consumer_details),t.message)
                }
            })
    }


    private fun initListener() {
        inputManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        mRegistrationBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                log("TAG", "##### onReceive push ##### ")
                if (intent.action == AppConstant.REGISTRATION_COMPLETE) {
                    FirebaseMessaging.getInstance().subscribeToTopic(AppConstant.TOPIC_GLOBAL)
                    getFirebaseRegId()
                    log(TAG, "### REGISTRATION_COMPLETE #### ")
                } else if (intent.action == AppConstant.PUSH_NOTIFICATION) {
                    val message: NotificationModel = intent.getParcelableExtra("message")!!
                    try {
                        NotificationUtils.createNotification(
                            applicationContext,
                            message.title,
                            message.message
                        )
                        if (circular_view != null) circular_view!!.stopTimer()
                        if (dialog!!.isShowing) dialog!!.dismiss()
                        showResultDialog(message)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1) {
            if (resultCode == RESULT_OK) {
                intentExtrasModel = intent.getSerializableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
            }
        }
    }
    fun showResultDialog(result: NotificationModel) {

        val layout:Int = if (result.code != null && result.code == "1") {
            R.layout.dialog_success_message
        } else {
            R.layout.dialog_failed_message
        }
        val dialogR = androidx.appcompat.app.AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogR.setContentView(layout)
        dialogR.findViewById<TextView>(R.id.title)!!.text = result.title
        dialogR.findViewById<TextView>(R.id.message)!!.text = result.message
        dialogR.findViewById<TextView>(R.id.action_done)!!.setOnClickListener {
            dialogR.dismiss()
            setBeep()
            if (result.code != null && result.code == "1") {//changed to 1 for testing purpose
                intentExtrasModel!!.panNumber = phoneNumber
                intentExtrasModel!!.mTransaction!!.pan = phoneNumber
                dialogR!!.dismiss()
                resetView()
            }
        }
        val lp = WindowManager.LayoutParams()
        val window = dialogR.window
        lp.copyFrom(window!!.attributes)
        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogR.setCancelable(false)
        dialogR.setCanceledOnTouchOutside(false)
    }
    fun gotoTicketActivity()
    {
        val i = Intent(this, TicketActivity::class.java)
        intentExtrasModel!!.typePay = typePay
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(i)
        finish()
    }


    fun showCheckoutDialog() {
        var dialog: AlertDialog? = null
        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_mobilenumber, null)
        builder.setView(layout)
        val enterNumber: AppCompatEditText = layout.findViewById(R.id.enterNumber)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val msgTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)
        if (BuildConfig.DEBUG) {
            enterNumber.setText("254708374149")
        }
        cancelButton.setOnClickListener { v: View? ->  setBeep()
            dialog!!.dismiss() }
        submitButton.setOnClickListener { v: View? ->
            when {
                enterNumber.text.toString().isEmpty() -> {
                    enterNumber.error = getString(R.string.enter_mobile_number)
                }
                else -> {
                    dialog!!.dismiss()
                    performSTKPush(enterNumber.text.toString())
                    phoneNumber = enterNumber.text.toString()
                }

            }
        }
        dialog = builder.create()
        dialog!!.show()
    }
    fun performSTKPush(phone_number: String?) {
        showProgressBar()
        var roundOffAmount = "0"
        if (intentExtrasModel!!.mTransaction!!.amount != null) {
            roundOffAmount = intentExtrasModel!!.mTransaction!!.amount!!.toDouble().roundToInt().toString()
        }
        var url: String = AppConstant.BASE_URL_MPESA_TEST + AppConstant.MPESA_PROCESSREQUEST_URL
        if (prefs.getReferenceModel()!!.mpesa_credentials!!.payment_mode != "test") {
            url = AppConstant.BASE_URL_MPESA_PRODUCTION + AppConstant.MPESA_PROCESSREQUEST_URL
        }
        val timestamp: String = Utils.getTimestamp()
        val stkPush = STKPush(
            prefs.getReferenceModel()!!.mpesa_credentials!!.business_short_code,
            Utils.getPassword(
                prefs.getReferenceModel()!!.mpesa_credentials!!.business_short_code,
                prefs.getReferenceModel()!!.mpesa_credentials!!.passkey,
                timestamp
            ),
            timestamp,
            prefs.getReferenceModel()!!.mpesa_credentials!!.transaction_type,
            roundOffAmount,
            Utils.sanitizePhoneNumber(phone_number).toString() + "",
            prefs.getReferenceModel()!!.mpesa_credentials!!.partyb.toString() + "",
            Utils.sanitizePhoneNumber(phone_number).toString() + "",
            prefs.getReferenceModel()!!.mpesa_credentials!!.call_back_url.toString() + prefs.fireBaseToken,
            prefs.getReferenceModel()!!.mpesa_credentials!!.payment_mode,  //The account reference
            getString(R.string.payment_request_from) + prefs.getReferenceModel()!!.terminal!!.stationName
        )
        mApiClient!!.setGetAccessToken(false)
        mApiClient!!.mpesaService(this).sendPush(url, stkPush)
            .enqueue(object : Callback<STKPush?> {
                override fun onResponse(call: Call<STKPush?>, response: Response<STKPush?>) {
                    try {
                        if (response != null && response.isSuccessful) {
                            Timber.d("post submitted to API. %s", response.body())
                            if (dialogMessage != null) dialogMessage!!.text = getString(R.string.please_wait_request_processing)
                        } else {
                            Timber.e("Response %s", response.errorBody()!!.string())
                            circular_view!!.stopTimer()
                            dialog!!.dismiss()
                            showDialog(getString(R.string.invalid_request),response.message())
                        }

                    } catch (e: Exception) {
                        e.printStackTrace()
                        circular_view!!.stopTimer()
                        dialog!!.dismiss()
                        showDialog(getString(R.string.invalid_request),e.message)
                    }
                }

                override fun onFailure(call: Call<STKPush?>, t: Throwable) {
                    circular_view!!.stopTimer()
                    dialog!!.dismiss()
                    Timber.e(t)
                    showDialog(getString(R.string.invalid_request),t.message)
                }
            })
    }

    fun showProgressBar() {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("")
        builder.setCancelable(false)
        val customLayout: View = layoutInflater.inflate(R.layout.dialog_progress_bar, null)
        builder.setView(customLayout)
        circular_view = customLayout.findViewById(R.id.circular_view)
        dialogMessage = customLayout.findViewById<TextView>(R.id.dialogMessage)
        circular_view!!.startTimer()
        val builderWithTimer: CircularView.OptionsBuilder = CircularView.OptionsBuilder()
            .shouldDisplayText(true)
            .setCounterInSeconds(119)
            .setCircularViewCallback(object : CircularViewCallback {
                override fun onTimerFinish() {
                    Toast.makeText(this@SplitPaymentActivity, getString(R.string.request_not_accepted), Toast.LENGTH_SHORT)
                        .show()
                    circular_view!!.stopTimer()
                    dialog!!.dismiss()
                }

                override fun onTimerCancelled() {}
            })
        circular_view!!.setOptions(builderWithTimer)
        dialog = builder.create()
        dialog!!.show()
    }

    override fun onPause() {
        if (isRegistered) {
            LocalBroadcastManager.getInstance(this)
                .unregisterReceiver(mRegistrationBroadcastReceiver!!)
            //isRegistered = false;
        }
        super.onPause()
    }
    override fun onResume() {
        super.onResume()

        // register GCM registration complete receiver
        LocalBroadcastManager.getInstance(this).registerReceiver(
            mRegistrationBroadcastReceiver!!,
            IntentFilter(AppConstant.REGISTRATION_COMPLETE)
        )
        LocalBroadcastManager.getInstance(this).registerReceiver(
            mRegistrationBroadcastReceiver!!,
            IntentFilter(AppConstant.PUSH_NOTIFICATION)
        )
        NotificationUtils.clearNotifications(applicationContext)
        isRegistered = true
    }
    override fun onStop() {
        if (isRegistered) {
            try {
                unregisterReceiver(mRegistrationBroadcastReceiver)
            } catch (e: Exception) {
                log(TAG, "mRegistrationBroadcastReceiver already unregistered")
            }
        }
        super.onStop()
    }
    override fun setObserver() {

    }
    fun gotoCardPaymentFlow()
    {
        typePay = AppConstant.CARD_VALUE
        intentExtrasModel!!.typePay = typePay
        val i = Intent(this, CheckCardRestrictionsActivity::class.java)
            i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
          startActivity(i)
    }
    fun gotoCashPaymentFlow()
    {
       // saveTransactionDetails()
        gotoTicketActivity()
    }
    fun gotoMobilePaymentFlow()
    {
        mApiClient = ApiClient()
        mApiClient!!.setConsumerCredentials(prefs.getReferenceModel()!!.mpesa_credentials!!.consumer_key, prefs.getReferenceModel()!!.mpesa_credentials!!.consumer_secret_key)
        mApiClient!!.setIsDebug(true) //Set True to enable logging, false to disable.
        getAccessToken()
    }
    fun gotoQRPaymentFlow()
    {
        val i: Intent
        typePay = AppConstant.QRCODE_VALUE
        if (intentExtrasModel!!.mTransaction != null) {
        i = Intent(this, ICCLoyaltyActivity::class.java)
        intentExtrasModel!!.typePay = typePay
         startActivityForResult(i, 1)
         }


    }
    fun gotoRFIDPaymentFlow()
    {

    }

    override fun onItemClick(view: View, model: ModePaymentModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
       if(view.id == R.id.modePayLayout)
       {
           intentExtrasModel!!.typePay = model.payment_id.toString()
           intentExtrasModel!!.mTransaction!!.modepay = model.payment_id.toString()
           if(!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
           {
               intentExtrasModel!!.splitPaymentModel!!.firstModeOfPayment = model.payment_id
               intentExtrasModel!!.splitPaymentModel!!.firstPaymentName = model.payment_name
               enterAmountDialog(this,model.payment_id)
           }
           else
           {
               intentExtrasModel!!.splitPaymentModel!!.secondModeOfPayment = model.payment_id
               intentExtrasModel!!.splitPaymentModel!!.secondPaymentName = model.payment_name
               paymentClickListeners(model.payment_id)
           }

       }
    }
    fun setAmounts()
    {
        if(!intentExtrasModel!!.splitPaymentModel!!.isError!!) {
            if (intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!! && !intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone!!) //First payment done and second payment not done
            {
                mBinding.msgText.text = getString(R.string.please_select_your_second_payment_method)
                saveTransactionDetails() //Update first payment details
            } else if (!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!! && !intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone!!) //Both payment not done
            {
                mBinding.msgText.text = getString(R.string.please_select_your_first_payment_method)
                intentExtrasModel!!.splitPaymentModel!!.totalAmount =
                    intentExtrasModel!!.mTransaction!!.amount.toString() // Transaction List Amount
            } else if (intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!! && intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone!!) {
                saveTransactionDetails() //Insert Second payment details
                gotoTicketActivity()
            }
        }
        val paidAmount = intentExtrasModel!!.splitPaymentModel!!.firstPaymentAmount!! + intentExtrasModel!!.splitPaymentModel!!.secondPaymentAmount!!
        val payable =  intentExtrasModel!!.splitPaymentModel!!.totalAmount!!.toDouble() - paidAmount
        mBinding.totalAmount.text =  intentExtrasModel!!.splitPaymentModel!!.totalAmount + " "+prefs.currency
        mBinding.paidAmount.text = paidAmount.toString() + " "+prefs.currency
        mBinding.payableAmount.text = payable.toString() + " "+prefs.currency
    }
    fun saveTransactionDetails()
    {
        try {
           val  mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            intentExtrasModel!!.mTransaction!!.transactionStatus = 1
            intentExtrasModel!!.mTransaction!!.modepay = intentExtrasModel!!.typePay
            intentExtrasModel!!.mTransaction!!.isSplitPayment = true
             if(intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone!!)
            {
                insertTransactionData(intentExtrasModel!!.mTransaction!!)
            }
            else
             {
               mTransactionTaxiDAO.updateTransactionsByReferenceID(intentExtrasModel!!.mTransaction!!)
             }
            mTransactionTaxiDAO.close()

        } catch (ex: SQLiteException) {
            ex.printStackTrace()
        }
    }
    fun showCardNumber(context: Context?) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_single_input, null)
        builder.setView(layout)
        val inputBox: AppCompatEditText = layout.findViewById(R.id.inputBox)
        inputBox.filters = arrayOf<InputFilter>(LengthFilter(3))
        val mainTitle: AppCompatTextView = layout.findViewById(R.id.title)
        val subTitle: AppCompatTextView = layout.findViewById(R.id.subTitle)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        mainTitle.text = getString(R.string.visa_payment)
        subTitle.text = getString(R.string.enter_last_3_digit)
        inputBox.inputType = InputType.TYPE_CLASS_NUMBER
        cancelButton.setOnClickListener { v: View? -> numberDialog!!.dismiss() }
        submitButton.setOnClickListener { v: View? ->
            if (inputBox.text.toString().isNotEmpty() && inputBox.length() == 3) {
                intentExtrasModel!!.panNumber = inputBox.text.toString()
                intentExtrasModel!!.mTransaction!!.pan = inputBox.text.toString()
                numberDialog!!.dismiss()
                resetView()
            } else {
                inputBox.error =  getString(R.string.enter_last_3_digit)
            }
        }
        numberDialog = builder.create()
        numberDialog!!.show()
    }
    fun enterAmountDialog(context: Context?,paymentId: Int) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_amount, null)
        builder.setView(layout)
        val enterAmount: AppCompatEditText = layout.findViewById(R.id.enterAmount)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val msgTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)
        if(intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            msgTxt.text = getString(R.string.enter_second_payment_amount)
        }
        else
        {
            msgTxt.text = getString(R.string.enter_first_payment_amount)
        }
        cancelButton.setOnClickListener { v: View? -> amountDialog!!.dismiss() }
        submitButton.setOnClickListener { v: View? ->
            when {
                enterAmount.text.toString().isEmpty() -> {
                    enterAmount.error = getString(R.string.enter_first_payment_amount)
                }
                enterAmount.text.toString().toDouble() >= intentExtrasModel!!.splitPaymentModel!!.totalAmount!!.toDouble() -> {
                    enterAmount.error = getString(R.string.enter_amount_cannot_be_grater)
                }
                else -> {
                    amountDialog!!.dismiss()
                    intentExtrasModel!!.mTransaction!!.isSplitPayment = true
                    intentExtrasModel!!.mTransaction!!.amount = enterAmount.text.toString().toDouble()
                    paymentClickListeners(paymentId)

                }

            }
        }
        amountDialog = builder.create()
        amountDialog!!.show()
    }
    fun paymentClickListeners(paymentId:Int) {
        if(intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            val paidAmount = intentExtrasModel!!.splitPaymentModel!!.firstPaymentAmount!! + intentExtrasModel!!.splitPaymentModel!!.secondPaymentAmount!!
            val payable =  intentExtrasModel!!.splitPaymentModel!!.totalAmount!!.toDouble() - paidAmount
            intentExtrasModel!!.mTransaction!!.amount = payable
        }
        intentExtrasModel!!.mTransaction!!.modepay = paymentId.toString()
        intentExtrasModel!!.typePay = paymentId.toString()
        typePay = paymentId.toString()
        when (paymentId) {

            1 -> {
                gotoCardPaymentFlow()
            }
            2 -> {
               resetView()
                if(intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!! && intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone!!)
                {
                    gotoCashPaymentFlow()
                }
            }
            3 -> {

                gotoMobilePaymentFlow()
            }
            4 -> {
                gotoQRPaymentFlow()
            }
            5 -> {
                gotoRFIDPaymentFlow()
            }
            7 -> {
              showCardNumber(this)
            }
            else -> {
                showDialog(getString(R.string.payment_mode), getString(R.string.no_function))
            }

        }
    }
    fun resetView()
    {
        if(!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone = true
            intentExtrasModel!!.splitPaymentModel!!.firstPaymentAmount = intentExtrasModel!!.mTransaction!!.amount
        }
        else if(intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone = true
            intentExtrasModel!!.splitPaymentModel!!.secondPaymentAmount = intentExtrasModel!!.mTransaction!!.amount
        }
        setupRecyclerview() //Refresh recyclerview to remove first payment list
        setAmounts() // to refresh total,paid and payable amount value
    }

    override fun onBackPressed() {

    }
}
