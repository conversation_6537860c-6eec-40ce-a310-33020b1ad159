package app.rht.petrolcard.ui.settings.bankcard.activity

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.Toast
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityManageCardBinding
import app.rht.petrolcard.ui.badge.activity.BadgeActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.card.common.model.CardItemModel
import app.rht.petrolcard.utils.constant.AppConstant
import kotlinx.android.synthetic.main.toolbar.view.*
import java.util.ArrayList

import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.settings.card.unblockcard.activity.UnblockActivity
import app.rht.petrolcard.ui.settings.card.unlockpin.activity.EnterNewPinActivity
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showFailedDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.paxutils.modules.printer.PrinterTester
import com.ebe.ebeunifiedlibrary.factory.ITransAPI
import com.ebe.ebeunifiedlibrary.factory.TransAPIFactory
import com.ebe.ebeunifiedlibrary.message.*
import com.ebe.ebeunifiedlibrary.sdkconstants.SdkConstants
import com.google.android.material.textfield.TextInputLayout


class ManageBankCardActivity: BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<CardItemModel> {

    private lateinit var mBinding: ActivityManageCardBinding

    private val TAG = SettingsActivity::class.simpleName
    private var intentExtrasModel: IntentExtrasModel? = null
    var valueDialog: AlertDialog? = null
    var enteredValue: String? = null
    var transAPI: ITransAPI? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_manage_card)

        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_manage_card)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        transAPI = TransAPIFactory.createTransAPI()
        setupToolbar()
        intentExtrasModel = IntentExtrasModel()
        setupItemsInList()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.card_management)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish()
        }
    }

    override fun setObserver() {

    }
    var activityResultLaunch = registerForActivityResult(
        StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppConstant.CARD_NFC_BADGE) {
            intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS_RECHARGE_CARD
            gotoCheckRestrictions()
        }
    }
    fun gotoCheckRestrictions()
    {
        val i = Intent(this, CheckCardRestrictionsActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }

    override fun onItemClick(view: View, item: CardItemModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        when (item.id) {
            "1","8"-> {
                enterValueDialog(this,item,getString(R.string.enter_voucher_no))
            }
            "2","6","7" -> {
                enterValueDialog(this,item,getString(R.string.enter_amount))
            }
           "3" -> {
                if(PrinterTester.getInstance().status == 0)
                {
                    enterValueDialog(this,item,getString(R.string.enter_voucher_no_last))
                }
                else
                {
                    val status = PrinterTester.getInstance().status
                    Log.i(TAG, "Printer Status: $status")
                    showFailedDialog(item.title,getPrinterStatusMsg(status))
                }
            }
            "4" ->
            {
                if(PrinterTester.getInstance().status == 0)
                {
                    enterValueDialog(this,item,getString(R.string.enter_type))
                }
                else
                {
                    val status = PrinterTester.getInstance().status
                    Log.i(TAG, "Printer Status: $status")
                    showFailedDialog(item.title,getPrinterStatusMsg(status))
                }
            }
            "5" -> {
                startSettlementRequest()
            }
            else -> Toast.makeText(
                applicationContext,
                resources.getString(R.string.no_function),
                Toast.LENGTH_SHORT
            ).show()
        }
    }

    private var settingsItems : ArrayList<CardItemModel> = ArrayList()
    private fun setupItemsInList(){
        settingsItems.add(CardItemModel(R.drawable.s_void_icon, "Void","1","#F97F51"))
        settingsItems.add(CardItemModel(R.drawable.s_refund_icon, "Refund","2","#1B9CFC"))
        settingsItems.add(CardItemModel(R.drawable.s_printer_icon, "Print Trx","3","#FC427B"))
        settingsItems.add(CardItemModel(R.drawable.s_printer_total, "Print Total","4","#ff3f34"))
        settingsItems.add(CardItemModel(R.drawable.s_settlement, "Settle","5","#B33771"))
        settingsItems.add(CardItemModel(R.drawable.s_scanning, "Scan Code","6","#3B3B98"))
        settingsItems.add(CardItemModel(R.drawable.s_barcode, "Scan Barcode","7","#F97F51"))
        settingsItems.add(CardItemModel(R.drawable.s_online_query, "Online Query","8","#1B9CFC"))
//        settingsItems.add(CardItemModel(R.drawable.ic_card_sync, "Card Sync",CARD_SYNC_ITEM,"#2C3A47"))
        mBinding.rvSettings.removeAllViews()
        val mSettingsAdapter = RecyclerViewArrayAdapter(settingsItems,this)
        mBinding.rvSettings.adapter = mSettingsAdapter
        mSettingsAdapter.notifyDataSetChanged()

    }

    fun enterValueDialog(context: Context?, model:CardItemModel, msg: String) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_value, null)
        builder.setView(layout)
        val enterValue: AppCompatEditText = layout.findViewById(R.id.enterValue)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val msgTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)
        val inputlayout: TextInputLayout = layout.findViewById(R.id.inputlayout)
        val icon: ImageView = layout.findViewById(R.id.icon)

        msgTxt.text = model.title
        enterValue.hint = msg
        inputlayout.hint = msg
//        icon.setImageResource(model.icon)
        cancelButton.setOnClickListener { v: View? -> valueDialog!!.dismiss() }
        submitButton.setOnClickListener { v: View? ->
            when {
                enterValue.text.toString().isEmpty() -> {
                    enterValue.error = title
                }
                else -> {
                    valueDialog!!.dismiss()
                    enteredValue = enterValue.text.toString()
                    gotoNext(model.id.toInt())
                }

            }
        }
        valueDialog = builder.create()
        valueDialog!!.show()
    }
    fun gotoNext(id:Int)
    {
        when (id) {
            1 -> {
                startVoid()
            }
            2 -> {
                startRefund()
            }
            3 -> {
                startPrintTrans()
            }
            4 ->
            {
                startPrintTransTotal()
            }
            6 -> {
                startScanCode()
            }
            7-> {
                startScanBarCode()
            }
            8-> {
                startOnlineQuery()
            }
            else -> Toast.makeText(
                applicationContext,
                resources.getString(R.string.no_function),
                Toast.LENGTH_SHORT
            ).show()
        }
    }
    private fun startVoid() {
        val request = VoidMsg.Request()
        request.traceNo = enteredValue!!.toLong()
        request.category = SdkConstants.CATEGORY_VOID
        transAPI!!.startTrans(this, request)
    }
    private fun startRefund() {
        val request = RefundMsg.Request()
        request.amount = enteredValue!!.toLong()
        request.category = SdkConstants.CATEGORY_REFUND
        transAPI!!.startTrans(this, request)
    }
    private fun startPrintTrans() {
        val request = ReprintTransMsg.Request()
        request.voucherNo = enteredValue!!.toInt()
        request.category = SdkConstants.CATEGORY_PRINT_TRANS
        transAPI!!.startTrans(this, request)
    }
    private fun startPrintTransTotal() {
        val request = ReprintTotalMsg.Request()
        request.reprintType = enteredValue!!.toInt()
        request.category = SdkConstants.CATEGORY_PRINT_TRANS_TOTAL
        transAPI!!.startTrans(this, request)
    }
    private fun startScanCode() {
        val request = ScanQRCodeMsg.Request()
        request.amount = enteredValue!!.toLong()
        request.category = SdkConstants.CATEGORY_SCAN_CODE
        transAPI!!.startTrans(this, request)
    }
    private fun startScanBarCode() {
        val request = BarCodeMsg.Request()
        request.amount = enteredValue!!.toLong()
        request.category = SdkConstants.CATEGORY_SCAN_BAR_CODE
        transAPI!!.startTrans(this, request)
    }
    private fun startOnlineQuery() {
        val request = OnlineQueryMsg.Request()
        request.voucherNo = enteredValue!!.toInt()
        request.category = SdkConstants.CATEGORY_ONLINE_QUERY
        transAPI!!.startTrans(this, request)
    }
    private fun startSettlementRequest() {
        val request = SettleMsg.Request()
        request.category = SdkConstants.CATEGORY_SETTLE
        transAPI!!.startTrans(this, request)
    }
}