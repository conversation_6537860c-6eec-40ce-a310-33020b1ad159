package app.rht.petrolcard.ui.common.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.dialog.BaseDialog
import app.rht.petrolcard.databinding.DialogFailedMessageBinding
import app.rht.petrolcard.databinding.DialogSuccessMessageBinding
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel

class FailedDialog : BaseDialog<CommonViewModel>(CommonViewModel::class), View.OnClickListener {

    var listener: OnClickListener? = null
    private lateinit var mBinding: DialogFailedMessageBinding
    var title="Error"
    var msg="Exception"

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        disableFullScreenDialog()
        seTCancelable(false)
        mBinding =
            DialogFailedMessageBinding.inflate(getThemeLayoutInflater(inflater), container, false)
        return mBinding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mBinding.actionDone.setOnClickListener(this)
        mBinding.title.text=title
        mBinding.message.text=msg
    }

    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.action_done -> listener?.onClick()
        }
    }

    interface OnClickListener {
        fun onClick()
    }

    override fun setObserver() {

    }
}