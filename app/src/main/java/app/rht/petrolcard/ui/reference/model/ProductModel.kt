package app.rht.petrolcard.ui.reference.model

import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.baseClasses.model.BaseModel
import androidx.annotation.Keep
@Keep
data class ProductModel(
    val productID: Int,
    val fcc_prod_id: Int,
    var libelle: String?,
    val code: String?,
    val bit: Int?,
    val categorie: String?,
    val isAvailable: String?,
    val color_code: String?,
    val icon: String?,
    val categoryId: Int,
    val hs_code: String?
):BaseModel(),Parcelable
{
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(productID)
        parcel.writeInt(fcc_prod_id)
        parcel.writeString(libelle)
        parcel.writeString(code)
        parcel.writeValue(bit)
        parcel.writeString(categorie)
        parcel.writeString(isAvailable)
        parcel.writeString(color_code)
        parcel.writeString(icon)
        parcel.writeInt(categoryId)
        parcel.writeString(hs_code)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ProductModel> {
        override fun createFromParcel(parcel: Parcel): ProductModel {
            return ProductModel(parcel)
        }

        override fun newArray(size: Int): Array<ProductModel?> {
            return arrayOfNulls(size)
        }
    }

}