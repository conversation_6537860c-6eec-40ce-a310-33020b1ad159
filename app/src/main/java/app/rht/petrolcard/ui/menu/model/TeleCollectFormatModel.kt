package app.rht.petrolcard.ui.menu.model

import app.rht.petrolcard.ui.reference.model.TransactionModel
import androidx.annotation.Keep
import app.rht.petrolcard.ui.reference.model.AuditModel

@Keep
data class TeleCollectFormatModel(
    val mTelecollecte: TelecollectDataModel,
    val mTransactions: List<TransactionModel>?,
    val mTaxis :List<TransactionModel>,
    val auditLogs :List<AuditModel>
) {
    companion object
}