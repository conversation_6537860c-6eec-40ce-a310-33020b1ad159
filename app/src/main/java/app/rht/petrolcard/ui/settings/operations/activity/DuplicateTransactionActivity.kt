package app.rht.petrolcard.ui.settings.operations.activity

import android.content.DialogInterface
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.view.View
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.databinding.ActivityDuplicateTransactionsBinding
import app.rht.petrolcard.ui.loyalty.utils.QRCode
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.operations.model.DuplicateTransactionModel
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.LocaleManager
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.ReceiptFields
import app.rht.petrolcard.utils.fuelpos.decimal
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import com.google.gson.Gson
import com.google.zxing.WriterException
import com.pax.gl.page.IPage
import com.pax.gl.page.PaxGLPage
import kotlinx.android.synthetic.main.activity_card_ceilinglimits.*
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException
import java.io.FileInputStream
import java.lang.Exception
import java.lang.ref.WeakReference
import java.util.*
import kotlin.collections.ArrayList


class DuplicateTransactionActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<DuplicateTransactionModel> {
    private var TAG= "DuplicateTransactionActivity"
    private lateinit var mBinding: ActivityDuplicateTransactionsBinding
    private var transactionList: ArrayList<DuplicateTransactionModel> = ArrayList<DuplicateTransactionModel>()
    private lateinit var adapter : RecyclerViewArrayAdapter<DuplicateTransactionModel>
    var attendantName:String?=""

    var currency = ""

    var referenceModel : ReferenceModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_duplicate_transactions)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        currency =prefs.currency
        setupToolbar()
        mBinding.prompt.text =getString(R.string.select_transactions)

        referenceModel = prefs.getReferenceModel()

        getTransactionDetails()
        setupRecyclerview()
        searchListner()
    }
    fun searchListner()
    {
        mBinding.searchBtn.setOnClickListener {
            searchTransactions()
        }
    }
    fun searchTransactions()
    {
        val mTransactionTaxiDAO = TransactionDao()
        mTransactionTaxiDAO.open()
        val mesTransactionsByPAN = mTransactionTaxiDAO.getDuplicateByPAN(10,   mBinding.searchTxt.text.toString())
        if(mesTransactionsByPAN == null || mesTransactionsByPAN.isEmpty())
        {
            resetView()
        }
        else
        {
            for(trans in mesTransactionsByPAN)
            {
                transactionList.add(DuplicateTransactionModel(trans,getProduct(trans)!!))
                adapter.notifyDataSetChanged()
                resetView()
            }
        }
        mTransactionTaxiDAO.close()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.duplicate_transactions)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish() }
    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        resetView()
        log(TAG,"transactionList:::"+gson.toJson(transactionList))
        adapter = RecyclerViewArrayAdapter(transactionList,this)
        mBinding.mListView.adapter = adapter
    }
    fun resetView()
    {
        if(!transactionList.isNullOrEmpty())
        {
            mBinding.emptyTXT.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
            mBinding.prompt.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyTXT.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
            mBinding.prompt.visibility = View.GONE

        }
    }
    override fun setObserver() {
    }
    private lateinit var printDuplicateTransactionLayout2: PrintDuplicateTransactionLayout2
    private lateinit var printDuplicateTransactionLayout: PrintDuplicateTransaction
    override fun onItemClick(view: View, model: DuplicateTransactionModel) {
        MultiClickPreventer.preventMultiClick(view)
        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)
        builder.setMessage(
            Html.fromHtml(
                //"<font color='#000000'>" + resources.getString(R.string.please_attach_driver_id_document) + "</font>" +
                "<font color='#000000'>" + getString(R.string.do_you_want_to_print_duplicate_receipt) + "</font>" +
                        "<br/><br/>" +
                        resources.getString(R.string.product) + " : <strong>" + model.productModel!!.libelle + "</strong>" +
                        "<br/>" +
                        resources.getString(R.string.amount_pay) + " : <strong>" + model.transactionModel!!.amount + " $currency</strong>"
            )
        )
        builder.setCancelable(false)
        builder.setNegativeButton(resources.getString(R.string.no)) { dialog, _ ->
            setBeep()
            dialog.dismiss()
        }
        builder.setPositiveButton(resources.getString(R.string.yes)) { dialog, _ ->
            setBeep()
            dialog.dismiss()
            getAttendantName(model.transactionModel.idPompiste!!)
            if(referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout != null && referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout == 2 && model.transactionModel.idProduit != PRODUCT.RECHARGE)
            {
                printDuplicateTransactionLayout2 = PrintDuplicateTransactionLayout2(model)
                printDuplicateTransactionLayout2.execute()
            }
            else {
                printDuplicateTransactionLayout = PrintDuplicateTransaction(model)
                printDuplicateTransactionLayout.execute()
            }
        }

        if (!isFinishing && !isDestroyed) {
            val alert = builder.create()
            alert.show()
            val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
            nbutton.setTextColor(ContextCompat.getColor(this, R.color.redLight))
            nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
            nbutton.textSize = 20f
            val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
            pbutton.setTextColor(ContextCompat.getColor(this, R.color.greenLight))
            pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
            pbutton.textSize = 20f
        }
    }
    fun getTransactionDetails() {
        try {
            val mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            val transactions = mTransactionTaxiDAO.getDuplicateTransaction(10)
            mTransactionTaxiDAO.close()
            for(trans in transactions)
            {
                val prod = getProduct(trans)
                if(prod != null)
                {
                    transactionList.add(DuplicateTransactionModel(trans,getProduct(trans)!!))

                }
            }
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun getProduct(transaction:TransactionModel) : ProductModel?{
        var product : ProductModel? = null
        runOnUiThread {
            val productsDAO = ProductsDao()
            productsDAO.open()
            product = productsDAO.getProductById(transaction.idProduit!!)
            productsDAO.close()
            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product!!.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product!!.libelle = fusionProductName
                }
            }

        }
        return product
    }

    var printDialog : AlertDialog? = null
    var fuelQtyUnit = "L"


    inner class PrintDuplicateTransaction(val duplicateTrx: DuplicateTransactionModel) : CoroutineAsyncTask<String, String, Void?>() {

        override fun onPreExecute() {
            super.onPreExecute()
            if(printDialog==null)
                printDialog = getMyPrintDialog()

        }

        override fun doInBackground(vararg params: String): Void? {
            val mTransaction = duplicateTrx.transactionModel
            val mProduit = duplicateTrx.productModel
            fuelQtyUnit = referenceModel!!.FUEL_QTY_UNIT!!
            val currency = prefs.currency

            try {
                var dateFormated: String? = mTransaction!!.dateTransaction
                if (dateFormated == null || !dateFormated.contains(" ")) {
                    dateFormated = Support.dateToStringH(Date())
                }
                val dateArray = dateFormated!!.split(" ").toTypedArray()
                val date = dateArray[0]
                val time = dateArray[1]


                val mTerminal = referenceModel!!.terminal
                var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                bitmap = Support.getResizedBitmap(bitmap,400,400)

                if(mProduit!!.libelle=="CARD RECHARGE")
                    mProduit.libelle = "RECHARGE"

                receipt = ReceiptBuilder(1200)
                receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK)
                    .addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)
                receipt.setTextSize(65f).setTypeface(this@DuplicateTransactionActivity, "fonts/Roboto-Bold.ttf").setAlign(Paint.Align.CENTER).addText("").
                addText(mTerminal!!.stationName).
                addText(mTerminal.address+", "+ mTerminal.city)
                receipt.addText("${resources.getString(R.string.duplicate_transactions)}")

                if (mTransaction.detailArticle != null && mTransaction.detailArticle == "loy") {
                    receipt.addText("")
                    receipt.addText("""* * * ${resources.getString(R.string.loyalty_label)} * * *""")
                }

                receipt.addText("")
                receipt.addLine()
                setAlignment(Paint.Align.LEFT,65f).setAlign(Paint.Align.CENTER).addText("$date $time")
                receipt.addParagraph()

                if (mTransaction.pumpId != null && mTransaction.pumpId!!.isNotEmpty()) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.pump_no_p),false)
                    setAlignment(Paint.Align.RIGHT,65f).addText(mTransaction.pumpId)
                }

                if (mProduit != null && mProduit.fcc_prod_id != 0) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.product_id_p),false)
                    setAlignment(Paint.Align.RIGHT,65f).addText(" ${mProduit.fcc_prod_id}")
                }

                if(!mProduit.libelle.isNullOrEmpty()){
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.product_p),false)
                    setAlignment(Paint.Align.RIGHT,65f).addText(" ${mProduit.libelle}")
                }

                setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.txn_type_p), false)

                var mop = getModePayment(mTransaction.modepay)


                setAlignment(Paint.Align.RIGHT,65f).addText(" $mop")
                receipt.addText("")
                receipt.addLine().setAlign(Paint.Align.CENTER)
                receipt.addText("")
                receipt.setAlign(Paint.Align.CENTER).setTypeface(this@DuplicateTransactionActivity, "fonts/Roboto-Bold.ttf").addText(resources.getString(R.string.txn_id_p)+" ${mTransaction.reference}")
                receipt.addText("")
                receipt.addLine().setAlign(Paint.Align.CENTER)


                if (mTransaction.amount != null) {
                 if (mTransaction.quantite != null && mTransaction.quantite != 0.0) {
                            setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.qty_p) , false)
                            setAlignment(Paint.Align.RIGHT,80f).addText("${Support.formatDouble(mTransaction.quantite!!)} $fuelQtyUnit")
                  }
                  if (mTransaction.unitPrice != null && mTransaction.unitPrice != 0.0) {
                            setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.price_p) , false)
                            setAlignment(Paint.Align.RIGHT,80f).addText("${mTransaction.unitPrice.toString().format("#.##")} $currency")
                  }
                    if (!mTransaction.netAmount.isNullOrEmpty()) {
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.net_amount_p), false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("${mTransaction.netAmount } $currency")
                    }
                    if (!mTransaction.vatAmount.isNullOrEmpty()) {
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.vat_p) , false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("${mTransaction.vatAmount} $currency")
                    }
                    if(mTransaction.isDiscountTransaction == 1)
                    {
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.fuel_total_p) , false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("${mTransaction.amount} $currency")
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.discount) , false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("-${mTransaction.discountAmount!!} $currency")
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.total_p) , false)
                        val discountAmount = mTransaction.discountAmount!!.toDouble()
                        val total = stringFormatDouble(mTransaction.amount!!.toDouble() - discountAmount)
                        setAlignment(Paint.Align.RIGHT,80f).addText("$total $currency")
                    }
                    else
                    {
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.total_p) , false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("${mTransaction.amount} $currency")
                    }
                }
                receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("___________________________________")

                if (!mTransaction.pan.isNullOrEmpty()) {
                    receipt.addText("")
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.pan_p)+" "+mTransaction.pan)
                }
                if (!mTransaction.panLoyalty.isNullOrEmpty()) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.loyalty_p)+" "+mTransaction.panLoyalty)
                }
                if (!mTransaction.nomPorteur.isNullOrEmpty()) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.name_p) +" "+ mTransaction.nomPorteur)
                }
                if (!mTransaction.dateExp.isNullOrEmpty()) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.date_exp_p)+" "+mTransaction.dateExp)
                }
                if (!mTransaction.tagNFC.isNullOrEmpty()) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.tag_no_p)+" "+mTransaction.tagNFC)
                }
                if(!mTransaction.kilometrage.isNullOrEmpty()){
                    setAlignment(Paint.Align.LEFT, 65f).addText(getString(R.string.mileage) + " " + mTransaction.kilometrage)
                }

                if (!mTerminal.fiscalId.isNullOrEmpty() && !mTerminal.fiscalId.equals("0")) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.fiscal_p)+" "+ mTerminal.fiscalId)
                }

                if (mTransaction.transactionSignature != null && mTransaction.transactionSignature!!.isNotEmpty()) {
                    setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.signature_p)+" "+mTransaction.transactionSignature)
                }

                if (mTransaction.modepay == AppConstant.VISA_VALUE) {
                    setAlignment(Paint.Align.LEFT,65f).addText(getString(R.string.auth_id_p)+" "+prefs.getStringSharedPreferences(
                        AppConstant.AUTH_CODE
                    ))
                    if(!prefs.getStringSharedPreferences(AppConstant.VOUCHER_NO).isNullOrEmpty() && prefs.getStringSharedPreferences(
                            AppConstant.VOUCHER_NO
                        )!=" "){
                        setAlignment(Paint.Align.LEFT,65f).addText(getString(R.string.voucher_p)+" "+prefs.getStringSharedPreferences(
                            AppConstant.VOUCHER_NO
                        ))
                    }
                }
                receipt.addText("")

                    setAlignment(Paint.Align.LEFT,65f)
                    receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")
                    receipt.setTextSize(65f).addText(getString(R.string.duplicate_copy_p))
                    receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")

                val reciept = receipt.build()

                TicketPrinter(this@DuplicateTransactionActivity).printReceipt(reciept)
                bitmap.recycle()
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }
            return null
        }

        override fun onPostExecute(result: Void?) {
            printDialog!!.dismiss()

        }
    }

    //region iPaxGL Page moved here from base activity
    private lateinit var iPaxGLPage: PaxGLPage
    private lateinit var receiptLayout2: IPage
    private fun initPrinterLayout2()
    {
        iPaxGLPage = PaxGLPage.getInstance(applicationContext)
        receiptLayout2 = iPaxGLPage.createPage()
    }
    private fun addLine(text1:String,text2:String,size: Int,style: Int): IPage.ILine {
        val line= receiptLayout2.addLine()
        if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this)) {
            val unit1 = receiptLayout2.createUnit()
            unit1.textStyle=style
            unit1.fontSize=size
//            unit1.align= IPage.EAlign.RIGHT
            unit1.text=text2
            line.addUnit(unit1)
            val unit2 = receiptLayout2.createUnit()
            unit2.textStyle=style
            unit2.fontSize=size
//            unit2.align= IPage.EAlign.RIGHT
            unit2.text=text1
            line.addUnit(unit2)
        }
        else
        {
            val unit1 = receiptLayout2.createUnit()
            unit1.textStyle=style
            unit1.fontSize=size
            unit1.align= IPage.EAlign.LEFT
            unit1.text=text1
            line.addUnit(unit1)
            val unit2 = receiptLayout2.createUnit()
            unit2.textStyle=style
            unit2.fontSize=size
            unit2.align= IPage.EAlign.RIGHT
            unit2.text=text2
            line.addUnit(unit2)
        }
        return line
    }
    private fun addReceiptUnit(text:String,align: IPage.EAlign?,size: Int,style: Int): IPage.ILine.IUnit {
        val unit = receiptLayout2.createUnit()
        if(align != null)
        {
            unit.align = align
        }
        if(text.isNotEmpty())
        {
            unit.text = text
        }
        if(style != 0)
        {
            unit.textStyle = style
        }
        unit.fontSize = size
        return unit
    }
    //endregion

    inner class PrintDuplicateTransactionLayout2(val duplicateTrx: DuplicateTransactionModel) : CoroutineAsyncTask<String, String, Void?>() {
        val TEXT_STYLE_NORMAL = 0
        val TEXT_STYLE_BOLD = 1
        val TEXT_STYLE_UNDERLINE = 2
        val TEXT_STYLE_ITALIC = 4

        var receiptSetting : ReceiptSetting? = null
        var qtyDecimal:Int = 2
        var uniteDecimal :Int = 2
        var vatDecimal :Int = 2
        var netDecimal :Int = 2
        var discountDecimal :Int = 2
        var totalDecimal :Int = 2
        var limitDecimal :Int = 2

        var totalAmount = ""
        var discountAmount =""
        var fuel_vat_percentage =""
        var shop_vat_percentage =""
        var fuel_vatable_amount=""
        var fuel_vat_amount=""
        var shop_vat_amount =""
        var shop_vatable_amount =""
        var vat =""
        var vatName =""
        var net =""
        var qty = ""

        lateinit var mTransaction :TransactionModel
        lateinit var mTerminal :TerminalModel
        lateinit var mProduit :ProductModel
        var fiscalId :String? = ""
        var currency :String = ""
        var dateFormated: String? = ""
        var date: String = ""
        var time: String = ""

        private fun initVariables() {

            receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
            qtyDecimal = receiptSetting!!.quantityDecimal ?: 2
            uniteDecimal = receiptSetting!!.unitPriceDecimal ?: 2
            vatDecimal = receiptSetting!!.vatDecimal ?: 2
            netDecimal = receiptSetting!!.netDecimal ?: 2
            discountDecimal = receiptSetting!!.discountDecimal ?: 2
            totalDecimal = receiptSetting!!.totalAmountDecimal ?: 2
            limitDecimal = receiptSetting!!.limitDecimal ?: 2

            mTransaction = duplicateTrx.transactionModel!!
            mTerminal = referenceModel!!.terminal!!
            mProduit = duplicateTrx.productModel!!
            fiscalId = mTerminal.fiscalId
            currency = prefs.currency
            dateFormated = mTransaction.dateTransaction
            if (dateFormated == null || !dateFormated!!.contains(" ")) {
                dateFormated = Support.dateToStringH(Date())
            }
            val dateArray = dateFormated!!.split(" ").toTypedArray()
            date = dateArray[0]
            time = dateArray[1]
            if(mTransaction.quantite != null) {
                qty = Support.getFormattedValue(this@DuplicateTransactionActivity, mTransaction.quantite!!.decimal(qtyDecimal!!))
            }

            if(fuelVat.enabled || shopVat.enabled) {
                net = Support.getFormattedValue(this@DuplicateTransactionActivity, mTransaction.netAmount!!.decimal(netDecimal!!))
                vat = Support.getFormattedValue(this@DuplicateTransactionActivity, mTransaction.vatAmount!!.decimal(vatDecimal!!))

                if(mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID)
                {
                    fuel_vat_amount=vat
                    fuel_vat_percentage ="${referenceModel!!.fuelVat!!.percentage} %"
                    fuel_vatable_amount="${mTransaction.amount!!}"
                    shop_vat_amount = "0"
                    shop_vatable_amount ="0"
                    shop_vat_percentage ="0"
                    vatName ="${referenceModel!!.fuelVat!!.vat_name}"
                }
                else if(mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID)
                {
                    fuel_vat_amount="0"
                    fuel_vat_percentage ="0"
                    fuel_vatable_amount="0"
                    shop_vat_percentage ="${referenceModel!!.shopVat!!.percentage} %"
                    shop_vat_amount = vat
                    shop_vatable_amount ="${mTransaction.amount!!}"
                    vatName ="${referenceModel!!.fuelVat!!.vat_name}"
                }

            }
            if(mTransaction.discountAmount != null && mTransaction.isDiscountTransaction!= null && mTransaction.isDiscountTransaction == 1)
            {
                discountAmount = Support.getFormattedValue(this@DuplicateTransactionActivity,
                    mTransaction.discountAmount!!.toDouble().decimal(discountDecimal!!))
                val discountAmt = mTransaction.discountAmount!!.toDouble()
                totalAmount = Support.getFormattedValue(this@DuplicateTransactionActivity,(mTransaction.amount!!.toDouble() - discountAmt).decimal(totalDecimal!!))
            }
            else
            {
                totalAmount = Support.getFormattedValue(this@DuplicateTransactionActivity,
                    mTransaction.amount!!.decimal(totalDecimal!!))
            }
        }
        private fun commonPrintLayout(): Bitmap?{
            var receiptBitmap: Bitmap? = null
            initVariables()
            try
            {
                if(referenceModel!!.receiptFormatFields != null)
                {
                    val fields = referenceModel!!.receiptFormatFields

                    //Header Format
                    for(fd in fields!!.receipt_header)
                    {
                        if(fd.is_available == 1)
                        {
                            if(fd.label_type ==  ReceiptFields.LOGO_R)
                            {
                                var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                                bitmap = Support.getResizedBitmap(bitmap,200,200)
                                receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
                            }
                            else if(fd.label_type ==  ReceiptFields.TELECOLLECT_COMPANY_NAME && mTerminal != null)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit(referenceModel!!.COMPANY.name, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                            }
                            else if(fd.label_type ==  ReceiptFields.TELECOLLECT_STATION_NAME && mTerminal != null)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit(mTerminal.stationName!!, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                            }
                            else if(fd.label_type ==  ReceiptFields.TELECOLLECT_ADDRESS)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit(mTerminal.address+", "+ mTerminal.city, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                            }
                            else if(fd.label_type ==  ReceiptFields.DATE)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit("$date $time", IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                            }
                            else if(fd.label_type ==  ReceiptFields.NEW_LINE)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,fd.font_size,TEXT_STYLE_BOLD))
                            }
                            else {
                                receiptLayout2.addLine().addUnit(addReceiptUnit(fd.label, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                            }
                        }
                    }
                    receiptLayout2.addLine().addUnit(addReceiptUnit("", IPage.EAlign.CENTER, 0,0))
                    for(rm in fields.receipt_middle)
                    {
                        if(rm.is_available == 1)
                        {
                            when  {

                                rm.label_type ==  ReceiptFields.NEW_LINE -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    receiptLayout2.addLine().addUnit()
                                }
                                rm.label_type ==  ReceiptFields.HEADER_1 && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1)  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    receiptLayout2.addLine().addUnit()
                                }
                                rm.label_type ==  ReceiptFields.TRX_ID && !mTransaction.reference.isNullOrEmpty()   -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label+" : "+ mTransaction.reference,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.FPOS_SEQUENCE_NO && !mTransaction.sequenceController.isNullOrEmpty()   -> {
                                    addLine(rm.label,"${mTransaction.sequenceController}",rm.font_size,TEXT_STYLE_BOLD)

                                }
                                rm.label_type ==  ReceiptFields.SALE_ID && !mTransaction.fccSaleId.isNullOrEmpty()   -> {
                                    addLine(rm.label,"${mTransaction.fccSaleId}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.TRANSACTION_BY && !attendantName.isNullOrEmpty()  -> {
                                    val name= attendantName!!.split(" ")
                                    addLine(rm.label,name[name.size - 1],rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.CUSTOMER_PIN && !mTransaction.timsSignDetails!!.customer_details!!.customerPin.isNullOrEmpty() && mTransaction.timsSignDetails!!.customer_details!!.customerPin != "0000"-> {
                                    addLine(rm.label,
                                        mTransaction.timsSignDetails!!.customer_details!!.customerPin!!,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.PAN && !mTransaction.pan.isNullOrEmpty()  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit("---------------------------------------------------",IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                    if(mTransaction.modepay == AppConstant.CARD_VALUE || mTransaction.modepay == AppConstant.VISA_VALUE)
                                    {
                                        addLine(rm.label,Support.hashPan(mTransaction.pan),rm.font_size,TEXT_STYLE_BOLD)
                                    }
                                    else {
                                        addLine(
                                            getModePayment(mTransaction.modepay)!!,
                                            mTransaction.pan!!,
                                            rm.font_size,
                                            TEXT_STYLE_BOLD
                                        )
                                    }
                                }
                                rm.label_type ==  ReceiptFields.PAYMENT_TYPE && !mTransaction.modepay.isNullOrEmpty()  -> {
                                    if(mTransaction.modepay != null)
                                    {
                                        addLine(rm.label,"${getModePayment(mTransaction.modepay)}",rm.font_size,TEXT_STYLE_BOLD)
                                    }
                                }
                                rm.label_type ==  ReceiptFields.UNIT_PRICE && mTransaction.unitPrice != null && mTransaction.unitPrice != 0.0  -> {
                                    val unit = Support.getFormattedValue(this@DuplicateTransactionActivity,
                                        mTransaction.unitPrice!!.decimal(uniteDecimal))
                                    addLine(rm.label,unit,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.REFERENCE -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.CARD_EXPIRY && !mTransaction.dateExp.isNullOrEmpty()-> {
                                    addLine(rm.label,"${mTransaction.dateExp}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.TAG_NFC && !mTransaction.tagNFC.isNullOrEmpty()-> {
                                    addLine(rm.label,"${mTransaction.tagNFC}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mProduit != null && mProduit.libelle != null && mTransaction.quantite != null && mTransaction.amount != null)-> {
                                    receiptLayout2.addLine()
                                        .addUnit(addReceiptUnit(mProduit.libelle!!,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit("$qty $fuelQtyUnit",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(Support.getFormattedValue(this@DuplicateTransactionActivity,
                                            mTransaction.amount!!.decimal(totalDecimal)),IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && mTransaction.categoryId != PRODUCT.FUEL_CATEGORY_ID && mTransaction.productName != null && mTransaction.amount != null -> {
                                    receiptLayout2.addLine()
                                        .addUnit(addReceiptUnit(mTransaction.productName!!,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(Support.getFormattedValue(this@DuplicateTransactionActivity,
                                            mTransaction.amount!!.decimal(totalDecimal)),IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.AMOUNT && mTransaction.amount != null  -> {
                                    addLine(rm.label,"${Support.getFormattedValue(this@DuplicateTransactionActivity,
                                        mTransaction.amount!!.decimal(totalDecimal))}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.PRODUCT_NAME && !mTransaction.productName.isNullOrEmpty()  -> {
                                    addLine(rm.label,"${mTransaction.productName}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.PAN_LOYALTY && !mTransaction.panLoyalty.isNullOrEmpty()  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(Support.hashPan(mTransaction.panLoyalty),IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.PAN_HOLDER_NAME && !mTransaction.nomPorteur.isNullOrEmpty()  -> {
                                    addLine(rm.label,"${mTransaction.nomPorteur}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.PUMP_NO && !mTransaction.pumpId.isNullOrEmpty()  -> {
                                    addLine(rm.label,"${mTransaction.pumpId}",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.QTY && mTransaction.quantite != null  -> {
                                    addLine(rm.label,"$qty $fuelQtyUnit",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.HEADER_2  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type == ReceiptFields.DATE  && !date.isNullOrEmpty()  -> {
                                    addLine(rm.label, date,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type == ReceiptFields.TIME  && !time.isNullOrEmpty()  -> {
                                    addLine(rm.label,time,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.VAT &&  (fuelVat.enabled || shopVat.enabled) && vat != null && vat != "0.0" -> {
                                    val isInclusive = fuelVat.type == 0
                                    val type = if(isInclusive) "Incl." else  "Excl."
                                    addLine("${rm.label} (${mTransaction.vatPercentage}% $type)",vat,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.VAT_NAME && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) && !vatName.isNullOrEmpty()-> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(vatName,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit("${mTransaction.vatPercentage}%",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(vat,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.ROUNDING_ADJUSTMENT && !mTransaction.roundingAdjustment.isNullOrEmpty() -> {
                                    addLine(rm.label,
                                        mTransaction.roundingAdjustment!!,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.NET_AMOUNT && (fuelVat.enabled || shopVat.enabled) && net != null && net != "0.0" -> {
                                    addLine(rm.label,net,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.SUB_INC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && net != null && net != "0.0" -> {
                                    addLine(rm.label,
                                        mTransaction.amount!!.toString(),rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.SUB_EXC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && net != null && net != "0.0" -> {
                                    addLine(rm.label,net,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.DISCOUNT && mTransaction.discountAmount != null && mTransaction.isDiscountTransaction == 1 -> {
                                    addLine(rm.label,"-$discountAmount",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.REFUND_AMOUNT && mTransaction.refundAmount != null && mTransaction.refundStatus == "1" -> {
                                    addLine(rm.label,
                                        mTransaction.refundAmount!!,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.TOTAL && totalAmount != null  -> {
                                    addLine(rm.label,"$totalAmount $currency",rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.LINE -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.TERMINAL_SN  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && !MainApp.sn.isNullOrEmpty()  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${MainApp.sn}",
                                        IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.RECEIPT_NO && !mTransaction.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty()  -> {
                                    if(!mTransaction.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty())
                                    {
                                        receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction.timsSignDetails!!.invoice_details!!.receiptNo}",
                                            IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    }
                                    else
                                    {
                                        receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction.timsSignDetails!!.invoice_details!!.receiptNo}",
                                            IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    }
                                }
                                rm.label_type ==  ReceiptFields.CU_SN && !mTransaction.timsSignDetails!!.controlUnitSerialNumber.isNullOrEmpty()  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction.timsSignDetails!!.controlUnitSerialNumber!!}", IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.VAT_DETAILS  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && rm.label.contains("|") && (fuelVat.enabled || shopVat.enabled)  -> {
                                    val vatArray = rm.label.split("|")
                                    if(vatArray.count() == 3)
                                    {
                                        receiptLayout2.addLine().addUnit(addReceiptUnit(vatArray[0],IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                            .addUnit(addReceiptUnit(vatArray[1] ,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                            .addUnit(addReceiptUnit(vatArray[2] ,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                    }
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_1   && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) &&  (fuelVat.enabled || shopVat.enabled)  -> {

                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+fuel_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(fuel_vatable_amount,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(fuel_vat_amount,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_2   && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+shop_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(shop_vatable_amount,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit(shop_vat_amount,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_3   && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+shop_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit("0",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit("0",IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_4  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+shop_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit("0",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        .addUnit(addReceiptUnit("0",IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.CU_INVOICE_NO  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && !mTransaction.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty()  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber!!}", IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.HEADER_3 && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && !mTransaction.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty() -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.HEADER_4  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) -> {
                                    receiptLayout2.addLine().addUnit()
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                }
                                rm.label_type ==  ReceiptFields.BANK_AUTH_CODE  && mTransaction.modepay == AppConstant.VISA_VALUE -> {
                                    val authCode = prefs.getStringSharedPreferences(AppConstant.AUTH_CODE)
                                    if(!authCode.isNullOrEmpty())
                                        addLine(rm.label,authCode,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.BANK_VOUCHER_NUMBER  && mTransaction.modepay == AppConstant.VISA_VALUE -> {
                                    val voucherNo = prefs.getStringSharedPreferences(AppConstant.VOUCHER_NO)
                                    if(!voucherNo.isNullOrEmpty() && voucherNo!=" ")
                                        addLine(rm.label,voucherNo,rm.font_size,TEXT_STYLE_BOLD)
                                }

                                rm.label_type ==  ReceiptFields.PAYMENT_REFERENCE_NUMBER  && (mTransaction.modepay == AppConstant.VISA_VALUE || mTransaction.modepay == "18") -> {
                                    val paymentRefNo = mTransaction.bank_reference_num
                                    if(!paymentRefNo.isNullOrEmpty())
                                        addLine(rm.label,paymentRefNo,rm.font_size,TEXT_STYLE_BOLD)

                                }

                                rm.label_type ==  ReceiptFields.PAN_LIMITS && !mTransaction.dailyCeiling.isNullOrEmpty()  -> {
                                    receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    receiptLayout2.addLine().addUnit()
                                }
                                rm.label_type ==  ReceiptFields.DAILY_LIMIT && !mTransaction.dailyCeiling.isNullOrEmpty()  -> {
                                    val dayLmt = Support.getFormattedValue(this@DuplicateTransactionActivity,
                                        mTransaction.dailyCeiling!!.decimal(limitDecimal))
                                    addLine(rm.label,dayLmt,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.WEEKLY_LIMIT && !mTransaction.weeklyCeiling.isNullOrEmpty()  -> {
                                    val weekLmt = Support.getFormattedValue(this@DuplicateTransactionActivity,
                                        mTransaction.weeklyCeiling!!.decimal(limitDecimal))
                                    addLine(rm.label,weekLmt,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.MONTHLY_LIMIT && !mTransaction.monthlyCeiling.isNullOrEmpty()  -> {
                                    val monthLmt = Support.getFormattedValue(this@DuplicateTransactionActivity,
                                        mTransaction.monthlyCeiling!!.decimal(limitDecimal))
                                    addLine(rm.label,monthLmt,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.CARD_BALANCE && !mTransaction.soldeCard.isNullOrEmpty() -> {
                                    val cardBal = Support.getFormattedValue(this@DuplicateTransactionActivity,Support.formatDoubleAffichage(
                                        mTransaction.soldeCard!!.toDouble())?:"0".decimal(limitDecimal))
                                    addLine(rm.label,cardBal,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.FISCAL_ID && !mTerminal.fiscalId.isNullOrEmpty() && !mTerminal.fiscalId.equals("0")   -> {
                                    addLine(rm.label,fiscalId!!,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.MILEAGE && !mTransaction.kilometrage.isNullOrEmpty() -> {
                                    addLine(rm.label,
                                        mTransaction.kilometrage!!,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.VEHICLE_NUMBER && !mTransaction.vehicleNumber.isNullOrEmpty() -> {
                                    addLine(rm.label,
                                        mTransaction.vehicleNumber!!,rm.font_size,TEXT_STYLE_BOLD)
                                }
                                rm.label_type ==  ReceiptFields.QR_CODE  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) -> {
                                    generateQRCODE(mTransaction)
                                }
                                rm.label_type ==  ReceiptFields.QR_CODE && mTransaction.qrCodeTicket!=null -> {
                                    generateQrCodeTicket(mTransaction)
                                }
                            }
                        }

                    }
                    receiptLayout2.addLine().addUnit()
                    for(rm in fields.receipt_footer)
                    {
                        when (rm.label_type) {
                            ReceiptFields.DUPLICATE_CUSTOMER_MESSAGE -> {
                                receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))

                                if(rm.label.isNotEmpty()) {
                                    if(rm.label.contains("|")) {
                                        val msgArray = rm.label.split("|")
                                        for(msg in msgArray) {
                                            receiptLayout2.addLine().addUnit(addReceiptUnit(msg,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        }
                                    } else {
                                        receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    }
                                } else {
                                    receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(getString(R.string.thanks_for_fueling))
                                }
                                receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            }
                            ReceiptFields.DUPLICATE_RECEIPT_COPY -> {
                                receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))

                                receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))

                                receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            }
                        }
                    }
                    receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,2,TEXT_STYLE_BOLD))

                    val width = 384
                    receiptBitmap = iPaxGLPage.pageToBitmap(receiptLayout2, width)
                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }
            return receiptBitmap
        }
        private fun dw14PrintLayout(): ArrayList<PrintCmd> {
            val printCommands = ArrayList<PrintCmd>()
            initVariables()
            try
            {
                if(referenceModel!!.receiptFormatFields != null)
                {
                    val fields = referenceModel!!.receiptFormatFields

                    //Header Format
                    for(fd in fields!!.receipt_header) {
                        if(fd.is_available == 1)
                        {
                            if(fd.label_type ==  ReceiptFields.LOGO_R)
                            {
                                var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                                bitmap = Support.getResizedBitmap(bitmap,200,200)
                                printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
                            }
                            else if(fd.label_type ==  ReceiptFields.TELECOLLECT_COMPANY_NAME && mTerminal != null)
                            {
                                printCommands.add(PrintCmd(referenceModel!!.COMPANY.name,AlignmentType.CENTER,true))
                            }
                            else if(fd.label_type ==  ReceiptFields.TELECOLLECT_STATION_NAME && mTerminal != null)
                            {
                                printCommands.add(PrintCmd(mTerminal.stationName!!,AlignmentType.CENTER,true))
                            }
                            else if(fd.label_type ==  ReceiptFields.TELECOLLECT_ADDRESS)
                            {
                                printCommands.add(PrintCmd(mTerminal.address+", "+ mTerminal.city,AlignmentType.CENTER,true))
                            }
                            else if(fd.label_type ==  ReceiptFields.DATE)
                            {
                                printCommands.add(PrintCmd("$date $time",AlignmentType.CENTER,true))
                            }
                            else if(fd.label_type ==  ReceiptFields.NEW_LINE)
                            {
                                printCommands.add(PrintCmd("",true))
                            }
                            else {
                                printCommands.add(PrintCmd(fd.label,true))
                            }
                        }
                    }
                    receiptLayout2.addLine().addUnit(addReceiptUnit("", IPage.EAlign.CENTER, 0,0))
                    for(rm in fields.receipt_middle) {
                        if(rm.is_available == 1)
                        {
                            when  {

                                rm.label_type ==  ReceiptFields.NEW_LINE -> {
                                    printCommands.add(PrintCmd("",true))
                                }
                                rm.label_type ==  ReceiptFields.HEADER_1 && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1)  -> {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                                }
                                rm.label_type ==  ReceiptFields.TRX_ID && !mTransaction.reference.isNullOrEmpty()   -> {
                                    printCommands.add(PrintCmd(rm.label+" : "+mTransaction!!.reference,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.FPOS_SEQUENCE_NO && !mTransaction.sequenceController.isNullOrEmpty()   -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.sequenceController}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.SALE_ID && !mTransaction.fccSaleId.isNullOrEmpty()   -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.fccSaleId}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.TRANSACTION_BY && !attendantName.isNullOrEmpty()  -> {
                                    val name= attendantName!!.split(" ")
                                    printCommands.add(PrintCmd(rm.label+"[L]"+name[name.size - 1],AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.CUSTOMER_PIN && !mTransaction.timsSignDetails!!.customer_details!!.customerPin.isNullOrEmpty() && mTransaction.timsSignDetails!!.customer_details!!.customerPin != "0000"-> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.timsSignDetails!!.customer_details!!.customerPin!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PAN && !mTransaction.pan.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd("-------------------------",AlignmentType.CENTER,true))
                                    if(mTransaction.modepay == AppConstant.CARD_VALUE || mTransaction.modepay == AppConstant.VISA_VALUE)
                                    {
                                        printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.pan),AlignmentType.LEFT,true))
                                    }
                                    else {
                                        printCommands.add(PrintCmd(getModePayment(mTransaction!!.modepay)!!+"[L]"+mTransaction!!.pan!!,AlignmentType.LEFT,true))
                                    }
                                }
                                rm.label_type ==  ReceiptFields.PAYMENT_TYPE && !mTransaction.modepay.isNullOrEmpty()  -> {
                                    if(mTransaction!!.modepay != null)
                                    {
                                        printCommands.add(PrintCmd(rm.label+"[L]"+"${getModePayment(mTransaction!!.modepay)}",AlignmentType.LEFT,true))
                                    }
                                }
                                rm.label_type ==  ReceiptFields.UNIT_PRICE && mTransaction.unitPrice != null && mTransaction.unitPrice != 0.0  -> {
                                    val unit = Support.getFormattedValue(this@DuplicateTransactionActivity,mTransaction!!.unitPrice!!.decimal(uniteDecimal))
                                    printCommands.add(PrintCmd(rm.label+"[L]"+unit,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.REFERENCE -> {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.CARD_EXPIRY && !mTransaction.dateExp.isNullOrEmpty()-> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction.dateExp}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.TAG_NFC && !mTransaction.tagNFC.isNullOrEmpty()-> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.tagNFC}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mProduit != null && mProduit.libelle != null && mTransaction.quantite != null && mTransaction.amount != null)-> {
                                    var qtyValue=qty
                                    if(mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) {
                                        qtyValue = "$qty $fuelQtyUnit"
                                    }
                                    printCommands.add(PrintCmd(mProduit!!.libelle!!+"[L]"+qtyValue+"[L]"+Support.getFormattedValue(mTransaction!!.amount!!.decimal(totalDecimal)),AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && mTransaction.categoryId != PRODUCT.FUEL_CATEGORY_ID && mTransaction.productName != null && mTransaction.amount != null -> {
                                    printCommands.add(PrintCmd(mTransaction!!.productName!!+"[L]"+Support.getFormattedValue(mTransaction!!.amount!!.decimal(totalDecimal)),AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.AMOUNT && mTransaction.amount != null  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${Support.getFormattedValue(mTransaction!!.amount!!.decimal(totalDecimal))}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PRODUCT_NAME && !mTransaction.productName.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.productName}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PAN_LOYALTY && !mTransaction.panLoyalty.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.panLoyalty),AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PAN_HOLDER_NAME && !mTransaction.nomPorteur.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.nomPorteur),AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.PUMP_NO && !mTransaction.pumpId.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.pumpId),AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.QTY && mTransaction.quantite != null  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"$qty $fuelQtyUnit",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.HEADER_2  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) -> {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }
                                rm.label_type == ReceiptFields.DATE  && date.isNotEmpty() -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+date,AlignmentType.LEFT,true))
                                }
                                rm.label_type == ReceiptFields.TIME  && time.isNotEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+time,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.VAT &&  (fuelVat.enabled || shopVat.enabled) && vat != null && vat != "0.0" -> {
                                    val isInclusive = fuelVat.type == 0
                                    val type = if(isInclusive) "Incl." else  "Excl."
                                    printCommands.add(PrintCmd("${rm.label} (${mTransaction!!.vatPercentage}% $type)\n",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.VAT_NAME && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) && !vatName.isNullOrEmpty()-> {
                                    printCommands.add(PrintCmd(vatName+"[C]"+"${mTransaction!!.vatPercentage}%"+"[L]"+time,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.ROUNDING_ADJUSTMENT && !mTransaction.roundingAdjustment.isNullOrEmpty() -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.roundingAdjustment!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.NET_AMOUNT && (fuelVat.enabled || shopVat.enabled) && net != null && net != "0.0" -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+net,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.SUB_INC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && net != null && net != "0.0" -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.amount!!.toString(),AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.SUB_EXC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && net != null && net != "0.0" -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+net,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.DISCOUNT && mTransaction.discountAmount != null && mTransaction.isDiscountTransaction == 1 -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"-$discountAmount",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.REFUND_AMOUNT && mTransaction.refundAmount != null && mTransaction.refundStatus == "1" -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.refundAmount!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.TOTAL && totalAmount != null  -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"$totalAmount $currency",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.LINE -> {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                                }
                                rm.label_type ==  ReceiptFields.TERMINAL_SN  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && !MainApp.sn.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd("${rm.label} : ${MainApp.sn}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.RECEIPT_NO && !mTransaction.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty()  -> {
                                    if(!mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty())
                                    {
                                        printCommands.add(PrintCmd("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo}",AlignmentType.LEFT,true))
                                    }
                                    else
                                    {
                                        printCommands.add(PrintCmd("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo}",AlignmentType.LEFT,true))
                                    }
                                }
                                rm.label_type ==  ReceiptFields.CU_SN && !mTransaction.timsSignDetails!!.controlUnitSerialNumber.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd("${rm.label} : ${mTransaction!!.timsSignDetails!!.controlUnitSerialNumber!!}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.VAT_DETAILS  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && rm.label.contains("|") && (fuelVat.enabled || shopVat.enabled)  -> {
                                    val vatArray = rm.label.split("|")
                                    if(vatArray.count() == 3)
                                    {
                                        printCommands.add(PrintCmd(vatArray[0]+"[L]"+vatArray[1]+"[L]"+vatArray[2],AlignmentType.LEFT,true))
                                    }
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_1   && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) &&  (fuelVat.enabled || shopVat.enabled)  -> {
                                    printCommands.add(PrintCmd(rm.label+" = [L]"+fuel_vat_percentage,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd(fuel_vatable_amount+" = [L]"+fuel_vat_percentage,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("[L][L]"+fuel_vat_amount,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_2   && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {
                                    printCommands.add(PrintCmd(rm.label+" = [L]"+shop_vat_percentage,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd(shop_vatable_amount+" = [L]"+shop_vat_percentage,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("[L][L]"+shop_vat_amount,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_3   && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {
                                    printCommands.add(PrintCmd(rm.label+" = [L]"+shop_vat_percentage,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("[L][L]0",AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("[L][L]0",AlignmentType.RIGHT,true))
                                }
                                rm.label_type ==  ReceiptFields.VAT_GROUP_4  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && (mTransaction.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {
                                    printCommands.add(PrintCmd(rm.label+" = "+shop_vat_percentage,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("[L][L]0",AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("[L][L]0",AlignmentType.LEFT,true))

                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("${rm.label} : [L]${mTransaction!!.timsSignDetails!!.controlUnitSerialNumber!!}",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.CU_INVOICE_NO  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && !mTransaction.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label+" : [L]"+mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.HEADER_3 && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) && !mTransaction.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty() -> {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.HEADER_4  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) -> {
                                    printCommands.add(PrintCmd("",AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.BANK_AUTH_CODE  && mTransaction.modepay == AppConstant.VISA_VALUE -> {
                                    val authCode = prefs.getStringSharedPreferences(AppConstant.AUTH_CODE)
                                    if(!authCode.isNullOrEmpty())
                                        printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.BANK_VOUCHER_NUMBER  && mTransaction.modepay == AppConstant.VISA_VALUE -> {
                                    val voucherNo = prefs.getStringSharedPreferences(AppConstant.VOUCHER_NO)
                                    if(!voucherNo.isNullOrEmpty() && voucherNo!=" ")
                                        printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }

                                rm.label_type ==  ReceiptFields.PAYMENT_REFERENCE_NUMBER  && (mTransaction.modepay == AppConstant.VISA_VALUE || mTransaction.modepay == "18") -> {
                                    val paymentRefNo = mTransaction!!.bank_reference_num
                                    if(!paymentRefNo.isNullOrEmpty())
                                        printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                }

                                rm.label_type ==  ReceiptFields.PAN_LIMITS && !mTransaction.dailyCeiling.isNullOrEmpty()  -> {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                    printCommands.add(PrintCmd("",AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.DAILY_LIMIT && !mTransaction.dailyCeiling.isNullOrEmpty()  -> {
                                    val dayLmt = Support.getFormattedValue(this@DuplicateTransactionActivity,mTransaction!!.dailyCeiling!!.decimal(limitDecimal))
                                    printCommands.add(PrintCmd(rm.label+"[L]"+dayLmt,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.WEEKLY_LIMIT && !mTransaction.weeklyCeiling.isNullOrEmpty()  -> {
                                    val weekLmt = Support.getFormattedValue(this@DuplicateTransactionActivity,mTransaction!!.weeklyCeiling!!.decimal(limitDecimal))
                                    printCommands.add(PrintCmd(rm.label+"[L]"+weekLmt,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.MONTHLY_LIMIT && !mTransaction.monthlyCeiling.isNullOrEmpty()  -> {
                                    val monthLmt = Support.getFormattedValue(this@DuplicateTransactionActivity,mTransaction!!.monthlyCeiling!!.decimal(limitDecimal))
                                    printCommands.add(PrintCmd(rm.label+"[L]"+monthLmt,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.CARD_BALANCE && !mTransaction.soldeCard.isNullOrEmpty() -> {
                                    val cardBal = Support.getFormattedValue(this@DuplicateTransactionActivity,Support.formatDoubleAffichage(mTransaction!!.soldeCard!!.toDouble())?:"0".decimal(limitDecimal))
                                    printCommands.add(PrintCmd(rm.label+"[L]"+cardBal,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.FISCAL_ID && !mTerminal.fiscalId.isNullOrEmpty() && !mTerminal.fiscalId.equals("0")   -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+fiscalId!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.MILEAGE && !mTransaction.kilometrage.isNullOrEmpty() -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.kilometrage!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.VEHICLE_NUMBER && !mTransaction.vehicleNumber.isNullOrEmpty() -> {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.vehicleNumber!!,AlignmentType.LEFT,true))
                                }
                                rm.label_type ==  ReceiptFields.QR_CODE  && (mTransaction.timsInvoiceStatus != null && mTransaction.timsInvoiceStatus == 1) -> {
                                    try {
                                        var qrCode = ""
                                        if(!mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode.isNullOrEmpty()) {
                                            qrCode =mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode!!
                                        }
                                        if(qrCode.isNotEmpty()) {
                                            val content =mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode
                                            var bitmap =  QRCode.generateBitmap(content)
                                            bitmap = Support.getResizedBitmap(bitmap,300,300)
                                            receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
                                            printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
                                        }
                                    } catch (e: WriterException) {
                                        Log.e("Tag", e.toString())
                                    }
                                }
                                rm.label_type ==  ReceiptFields.QR_CODE && mTransaction.qrCodeTicket!=null -> {
                                    try {
                                        val qrCodeResponse = mTransaction!!.qrCodeTicket
                                        if(qrCodeResponse!=null) {
                                            val jsonString = Gson().toJson(qrCodeResponse)
                                            if(jsonString.isNotEmpty()){
                                                var bitmap =  QRCode.generateBitmap(jsonString)
                                                bitmap = Support.getResizedBitmap(bitmap,300,300)
                                                printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
                                            }
                                        }
                                    } catch (e: WriterException) {
                                        Log.e("Tag", e.toString())
                                    }
                                }
                            }
                        }

                    }
                    receiptLayout2.addLine().addUnit()
                    for(rm in fields.receipt_footer)
                    {
                        when (rm.label_type) {
                            ReceiptFields.DUPLICATE_CUSTOMER_MESSAGE -> {
                                printCommands.add(PrintCmd("*****************************",AlignmentType.CENTER,true))

                                if(rm.label.isNotEmpty()) {
                                    if(rm.label.contains("|")) {
                                        val msgArray = rm.label.split("|")
                                        for(msg in msgArray) {
                                            receiptLayout2.addLine().addUnit(addReceiptUnit(msg,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                        }
                                    } else {
                                        receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    }
                                } else {
                                    receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(getString(R.string.thanks_for_fueling))
                                }
                                receiptLayout2.addLine().addUnit(addReceiptUnit("***********************************",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            }
                            ReceiptFields.DUPLICATE_RECEIPT_COPY -> {
                                printCommands.add(PrintCmd("*****************************",AlignmentType.CENTER,true))
                                receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                printCommands.add(PrintCmd("*****************************",AlignmentType.CENTER,true))
                            }
                        }
                    }
                    printCommands.add(PrintCmd("",AlignmentType.CENTER,true))
                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }
            return printCommands
        }

        override fun onPreExecute() {
            super.onPreExecute()
            initPrinterLayout2()
            if(printDialog==null)
                printDialog = getMyPrintDialog()
        }

        override fun doInBackground(vararg params: String): Void? {
            val ctx = WeakReference(this@DuplicateTransactionActivity).get()!!

            var printerType = referenceModel!!.PRINTER_TYPE
            if(BuildConfig.DEBUG){
                printerType = AppConstant.DW14_PRINTER
            }

            if(printerType == AppConstant.DW14_PRINTER){
                val commands = dw14PrintLayout()
                TicketPrinter(ctx).printTicket(commands)
            } else {
                val receiptBitmap = commonPrintLayout()
                if(receiptBitmap!=null) {
                    TicketPrinter(ctx).printReceipt(receiptBitmap, 2)
                    receiptBitmap.recycle()
                }
            }
            return null
        }

        override fun onPostExecute(result: Void?) {
            printDialog!!.dismiss()
        }
    }

    private fun getAttendantName(id:String):String?
    {
        log(TAG, "Attendant ID:: $id")
        val mUsersDao = UsersDao()
        mUsersDao.open()
        val attendantModel  = mUsersDao.getAttendantListByID(id)
        mUsersDao.close()
        if(attendantModel != null)
        {
            attendantName = attendantModel.getFullName()
        }
        return attendantName
    }


    fun generateQRCODE(mTransaction:TransactionModel)
    {
        try {
            var qrCode = ""

             if(!mTransaction.timsSignDetails!!.invoice_details!!.invoiceQrCode.isNullOrEmpty())
            {
                qrCode =mTransaction.timsSignDetails!!.invoice_details!!.invoiceQrCode!!
            }
            if(qrCode.isNotEmpty())
            {
                val content =mTransaction.timsSignDetails!!.invoice_details!!.invoiceQrCode
                var bitmap =  QRCode.generateControlKey(content)
                bitmap = Support.getResizedBitmap(bitmap,300,300)
                receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
            }
        } catch (e: WriterException) {
            Log.e("Tag", e.toString())
        }

    }

    private fun generateQrCodeTicket(mTransaction:TransactionModel) {
        try {
            val qrCodeResponse = mTransaction.qrCodeTicket
            if(qrCodeResponse!=null) {
                val jsonString = Gson().toJson(qrCodeResponse)
                if(jsonString.isNotEmpty()){
                    var bitmap =  QRCode.generateBitmap(jsonString)
                    bitmap = Support.getResizedBitmap(bitmap,300,300)
                    receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
                }
            }
        } catch (e: WriterException) {
            Log.e("Tag", e.toString())
        }
    }



    override fun onStop() {
        if(::printDuplicateTransactionLayout2.isInitialized){
            printDuplicateTransactionLayout2.cancel(true)
        }
        if(::printDuplicateTransactionLayout.isInitialized){
            printDuplicateTransactionLayout.cancel(true)
        }
        super.onStop()
    }
}

