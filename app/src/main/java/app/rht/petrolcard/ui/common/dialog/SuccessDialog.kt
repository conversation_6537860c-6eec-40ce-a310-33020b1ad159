package app.rht.petrolcard.ui.common.dialog

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.dialog.BaseDialog
import app.rht.petrolcard.databinding.DialogSuccessMessageBinding
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel

class SuccessDialog : BaseDialog<CommonViewModel>(CommonViewModel::class), View.OnClickListener {

    var listener: OnClickListener? = null
    private lateinit var mBinding: DialogSuccessMessageBinding
    var title="Success"
    var msg="Success"

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
//        matchParentWidth()
        seTCancelable(false)
        mBinding =
            DialogSuccessMessageBinding.inflate(getThemeLayoutInflater(inflater), container, false)
        return mBinding.root
    }
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        mBinding.actionDone.setOnClickListener(this)
        mBinding.close.setOnClickListener(this)
        mBinding.title.text= getString(R.string.success)
        mBinding.message.text=getString(R.string.success)
    }

    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.action_done -> listener?.onClick()
        }
    }

    interface OnClickListener {
        fun onClick()
        fun onCancelClick()
    }

    override fun setObserver() {

    }
}