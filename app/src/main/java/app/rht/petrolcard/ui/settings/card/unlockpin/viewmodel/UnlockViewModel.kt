package app.rht.petrolcard.ui.settings.card.unlockpin.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.settings.card.unblockcard.model.UnblockResponseModel
import app.rht.petrolcard.utils.AppPreferencesHelper


open class UnlockViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : BaseViewModel() {

    var unlockCardObserver = MutableLiveData<BaseResponse<String>>()
    var unCardNotificationObserver = MutableLiveData<BaseResponse<String>>()


    fun unlockCard(pan:String) {
        requestData(mNetworkService.unlockCard(preferencesHelper.baseUrl+NetworkRequestEndPoints.UNLOCK_CARD_PIN,MainApp.sn,pan),
            {
                unlockCardObserver.postValue(it)
            })
    }
    fun unlockCardNotification(pan:String,date:String) {
        requestData(mNetworkService.unlockCardNotification(preferencesHelper.baseUrl+NetworkRequestEndPoints.UNLOCK_CARD_NOTIFICATION,MainApp.sn,pan,date),
            {
                unCardNotificationObserver.postValue(it)
            })
    }
}
