package app.rht.petrolcard.ui.reference.model

import android.graphics.Color
import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.baseClasses.model.BaseModel
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep
@Keep
data class NozzelsModel(
    val Article: Int,
    val CardId: Int,
    val FP_NOOZLE: String?,
    val Name: String?,
    val id: Int,
    val productColor: String?,
    var prdt_SKU: String? = null,
    var fcc_prod_id: Int? = null,
    var fcc_prod_name: String? = null
) :BaseModel(),Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString()
    )


    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(Article)
        parcel.writeInt(CardId)
        parcel.writeString(FP_NOOZLE)
        parcel.writeString(Name)
        parcel.writeInt(id)
        parcel.writeString(productColor)
        parcel.writeString(prdt_SKU)
        parcel.writeInt(fcc_prod_id!!)
        parcel.writeString(fcc_prod_name)

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<NozzelsModel> {
        override fun createFromParcel(parcel: Parcel): NozzelsModel {
            return NozzelsModel(parcel)
        }

        override fun newArray(size: Int): Array<NozzelsModel?> {
            return arrayOfNulls(size)
        }
    }
    fun getColor(): Int {
        return Color.parseColor(productColor)
    }
}
