package app.rht.petrolcard.ui.updatecard.activity

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import androidx.annotation.RequiresApi
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.updatecard.model.*
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.POSTPAID_CARD
import app.rht.petrolcard.utils.constant.AppConstant.RECHARGE_TRANSACTION
import app.rht.petrolcard.utils.constant.AppConstant.REFUND_TRANSACTION
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.paxutils.icc.IccTester
import com.afollestad.materialdialogs.MaterialDialog
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.BankCard
import java.math.BigDecimal
import java.sql.SQLException
import java.text.SimpleDateFormat
import java.util.*


@Suppress("DEPRECATION")
class UpdateCardActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private val TAG = UpdateCardActivity::class.java.simpleName
    private lateinit var mBinding: app.rht.petrolcard.databinding.ActivityCardUpdateBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    private var mProduitDAO: ProductsDao? = null
    var stationMode = 0
    var returnValue = 0
    private var mBankCard: BankCard? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String = ""
    var authKey = ""
    var nomPorteur = ""
    private var dateExpirationCard: String? = null
    private var cardExpired = false
    var statusCard = "1"
    private var cardModel: CardResponseModel? = null
    private var rechargeModel: RechargeModel? = null
    private var refundModel: RefundModel? = null
    var vehicules: ArrayList<VehicleModel>? = ArrayList<VehicleModel>()
    private var infoCarte: String? = null
    private var isRechargeMass = false
    private var isRefund = false
    private var amount: String? = null
    private var fbsAmount: String? = null
    private var monthlyLimit = 0.0
    private var infoCard: String? = null
    var readPlfPost: String? = null
    var readPlfPrep: String? = null
    private var mProduit: ProductModel? = null
    private var mTransactionOffline: TransactionModel? = null
    private var alert: AlertDialog? = null
    var isSameMonthCredit = true
    var isFirstUse = false
    var dateLastPLF: String? = null
    var readPlf: String? = null
    var isNextDayPLF = false
    var preferenceModel: PreferenceModel? = null
    var isCreditValidationDone=false
    var creditStatus = 0
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_card_update)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        getInitIntentExtras()

        setupToolbar()

        preferenceModel = prefs.getPreferenceModel()

        val readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
    }

    private fun setupToolbar() {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.update_card)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
           // finish()
            if(isCreditValidationDone)
            {
                log(TAG,"SaveCreditStatus 3")
                SaveCreditStatus()
            }
            gotoAbortMessageActivity(getString(R.string.error),getString(R.string.transaction_cancelled))
        }
    }

    fun getInitIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL)
                as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if (intentExtrasModel!!.loyaltyTrx) {
                stationMode = 1
            }
        }

    }

    var errorMessage = ""

    @RequiresApi(Build.VERSION_CODES.O)
    override fun setObserver() {
        mViewModel.cardDetailsObserver.observe(this) {
            val dt: String = Support.dateToStringPlafond(Date())
            val cardUpdated = updateCardDetails(it)
            if (cardUpdated) {
                deletePanFromGreyList(panNumber)
                mViewModel.updateCardNotification(panNumber, dt)
            } else {
                if (errorMessage.isNotEmpty())
                    gotoAbortMessageActivity(resources.getString(R.string.error), errorMessage)
                else
                    gotoAbortMessageActivity(
                        resources.getString(R.string.error),
                        resources.getString(R.string.failed_update)
                    )
            }
        }
        mViewModel.updateCardNotificationObserver.observe(this) {
            if (it.reponse == "1") {
                proceedNextStep()
            } else {
                setBeep()
                gotoAbortMessageActivity(
                    resources.getString(R.string.error),
                    resources.getString(R.string.prblm_connexion)
                )
            }
        }
        mViewModel.updateCardRechargeObserver.observe(this) {
            if (it.reponse == "1") {
                isCreditValidationDone = true
                updateCreditAmount()
            } else {
                setBeep()
                gotoAbortMessageActivity(resources.getString(R.string.error), it.error!!)
            }
        }
        mViewModel.updateCreditStatusObserver.observe(this) {
            if (it.reponse == "1") {
                if (isRechargeMass || isRefund ) {
                    if (UtilsCardInfo.verifyPIN(
                            mBankCard,
                            icCpuReader,
                            intentExtrasModel!!.mPinNumberCard,
                            this@UpdateCardActivity
                        )
                    ) {
                        statusCard = UtilsCardInfo.getStatusCard(infoCarte)
                        readCardLimitsAndCounter()
                    }
                      resetAllValues()
                        mViewModel.getCardDetails(panNumber, statusCard)
                }
            } else {
                setBeep()
                log(TAG,"SaveCreditStatus 1")
                SaveCreditStatus()
                gotoAbortMessageActivity(resources.getString(R.string.error), it.error!!)
            }
        }
    }
    fun resetAllValues()
    {
        creditStatus = 0
        isRechargeMass = false
        isRefund = false
        isCreditValidationDone = false
    }
    fun updateCreditAmount()
    {
        if (rechargeModel != null && rechargeModel!!.mnt!!.toDouble() > 0)
            isRechargeMass = changeSoldeCredit(rechargeModel!!.mnt.toString() + "", true)
        else if (refundModel != null && refundModel!!.mnt!!.toDouble() > 0)
            isRefund = processCardRefund(refundModel!!.mnt.toString() + "")

        if(isRechargeMass)
        {
            mViewModel.updateCreditStatus(
                panNumber,
                rechargeModel!!.id!!,
                rechargeModel!!.checksum!!,
                creditStatus
            )
        }
        else if (rechargeModel != null && rechargeModel!!.mnt!!.toDouble() > 0 && !isRechargeMass)
        {
            mViewModel.updateCreditStatus(
                panNumber,
                rechargeModel!!.id!!,
                rechargeModel!!.checksum!!,
                creditStatus
            )
        }

        if(isRefund)
        {
            mViewModel.updateCreditStatus(
                panNumber,
                refundModel!!.id!!,
                refundModel!!.checksum!!,
                creditStatus
            )
        }
        else if (refundModel != null && refundModel!!.mnt!!.toDouble() > 0 && !isRefund)
        {
                mViewModel.updateCreditStatus(
                    panNumber,
                    refundModel!!.id!!,
                    refundModel!!.checksum!!,
                    creditStatus
                )
        }

    }

    override fun onInternetEnableDisable(status: Boolean) {
        log(TAG,"Network Status :: $status")
        if (!status) {
            if(isCreditValidationDone)
            {
                log(TAG,"SaveCreditStatus 2")
                SaveCreditStatus()
            }
            else
            {
                showRetryDialog()
            }
        }
    }

    private fun SaveCreditStatus() {
        isCreditValidationDone = false
        if (isRechargeMass) {
            saveTransaction(
                transactionType = RECHARGE_TRANSACTION,
                montant = rechargeModel!!.mnt.toString()
            )
        } else if (isRefund) {
            saveTransaction(
                transactionType = REFUND_TRANSACTION,
                montant = refundModel!!.mnt.toString()
            )
        }
        gotoAbortMessageActivity(getString(R.string.failed_update),getString(R.string.no_internet_connection))
    }

    private fun showRetryDialog() {
        if (!(this as Activity).isFinishing) {
            val dialog = MyMaterialDialog(
                this,
                getString(R.string.error),
                getString(R.string.try_update_card),
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        setBeep()
                        val readCardTask = ReadCardAsyncTask()
                        readCardTask.execute()
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        setBeep()
                        gotoAbortMessageActivity(
                            getString(R.string.error),
                            getString(R.string.transaction_cancelled)
                        )
                    }

                })
        }
    }

    private fun proceedNextStep() {
            if (isRechargeMass) {
                mBinding.progressMessage.text =
                    "${getString(R.string.tota_recharge_amount)} ${rechargeModel!!.mnt} \n\n ${
                        getString(R.string.rechargr_proccessing)
                    }"
                mViewModel.updateMassRechargeValidate(
                    panNumber,
                    rechargeModel!!.id!!,
                    rechargeModel!!.checksum!!
                )

            } else if (isRefund) {
                mBinding.progressMessage.text =
                    "${getString(R.string.refund_amount)} ${refundModel!!.mnt} \n\n ${getString(R.string.refund_proccessing)}"
                mViewModel.updateMassRechargeValidate(
                    panNumber,
                    refundModel!!.id!!,
                    refundModel!!.checksum!!
                )
                //  showRefundDialog()
            } else {
                setBeep()
                if (intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_UPDATE) {
                    gotoSuccessMessageActivity(
                        getString(R.string.card_update_success),
                        getString(R.string.your_card_update_success),
                        MenuActivity::class.java
                    )
                } else {
                    showSnakeBar(getString(R.string.card_updated_successfully), true)
                    Handler().postDelayed({
                        val intent = Intent()
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                        setResult(AppConstant.CARD_UPDATE_CODE_RESULT, intent)
                        finish()
                    }, 2000)
                }
            }

    }

    private fun updateCardDetails(it: BaseResponse<CardDetailsResponse>): Boolean {
        var isUpdated = false
        try {
            if (it.reponse == "1" && it.contenu != null) {
                val updateContent = it.contenu
                cardModel = updateContent.carte
                if (updateContent.Recharges!!.isNotEmpty())
                    rechargeModel = updateContent.Recharges!![0]
                else {
                    rechargeModel = null
                    isRechargeMass = false
                }

                if (updateContent.Refunds!!.isNotEmpty())
                    refundModel = updateContent.Refunds!![0]
                else {
                    refundModel = null
                    isRefund = false
                }

                if (cardModel!!.NFC == 1 || cardModel!!.NFC == 2) {
                    var tagList: List<VehicleModel> = ArrayList()
                    tagList = updateContent.Vehicules!!
                    for (itemTag in tagList) {
                        if (itemTag.tagnfc != null && itemTag.tagnfc!!.length >= 5) {
                            vehicules!!.add(itemTag)
                        }
                    }
                }
                if (cardModel != null && infoCarte != null &&
                    infoCarte!!.isNotEmpty() && infoCarte!!.length > 21) {
                    if (cardModel!!.type_id == 1 && rechargeModel != null &&
                        rechargeModel!!.mnt!! > 0) {
                        isRechargeMass = true
                    } else if ((cardModel!!.type_id == 1 || cardModel!!.type_id == 2)
                        && refundModel != null && refundModel!!.mnt!! > 0) {
                        isRefund = true
                    }

                    val type: String = infoCarte!!.substring(19, 20)

                    if (type == "F" && (cardModel!!.type_id == 1 || cardModel!!.type_id == 2 ||
                                cardModel!!.type_id == 3))
                    // update card type //1 prepaid //2 postpaid //3 loyalty
                    {
                        infoCarte = UtilsCardInfo.updateCarteType(
                            this@UpdateCardActivity,
                            icCpuReader, mBankCard, infoCarte, cardModel!!.type_id!!)
                        isFirstUse = true
                    }

                    if (type == "F" && cardModel!!.type_id == 2)  //for postpaid
                    {
                        //amount = ""+((cardModel!!.Plafond_Mensuel!! * 100).toLong()) // not using
                        amount = "" + cardModel!!.Plafond_Mensuel!!
                        //newly added for postpaid first card update balance issue
                        fbsAmount = "" + cardModel!!.Plafond_Mensuel!!
                        val chengecredit: Boolean = changeSoldeCredit(amount!!, false)
                        isFirstUse = true
                    } else if (cardModel!!.type_id == AppConstant.POSTPAID_CARD
                        || cardModel!!.type_id == AppConstant.PREPAID_CARD) {
                        updateLimitsNbalance()
                    }

                } else {
                  gotoAbortMessageActivity(resources.getString(R.string.error), resources.getString(R.string.prblm_connexion))
                }
                if (cardModel != null && cardModel!!.NFC == 1 || cardModel!!.NFC == 2) {
                    var result: String = UtilsCardInfo.readBinaryFile(
                        mBankCard,
                        icCpuReader,
                        "2F20",
                        "5F",
                        this@UpdateCardActivity
                    )
                    val oldResult = result

                    var numberofnfcTag = result.substring(result.length - 2, result.length)
                    if (numberofnfcTag == "00" || numberofnfcTag == "FF")
                        if (numberofnfcTag == "FF") numberofnfcTag = "00"
                    for (i in 0 until Integer.valueOf(numberofnfcTag)) {
                        result = UtilsCardInfo.removenfctorecord(result, i + 1)
                    }
                    var datacompined1 = ""
                    var datacompined2 = ""
                    var datacompined3 = ""
                    var nfcItem: NfcTagModel

                    var i = 0
                    while (vehicules != null && vehicules!!.isNotEmpty() &&
                        i < vehicules!!.size && result.isNotEmpty()) {
                        if (i == 0 && vehicules!![i].tagnfc != null && !vehicules!![i].tagnfc!!.isEmpty()
                        ) {
                            var previousMileage = "0"
                            previousMileage = if (vehicules!![i].vehiclemileage == null) {
                                if (oldResult.substring(48, 54)
                                        .contains("FF")
                                ) "000000" else oldResult.substring(48, 54)
                            } else {
                                vehicules!![i].vehiclemileage!!
                            }
                            nfcItem =
                                UtilsCardInfo.initNfcTAG(vehicules!![i], i + 1, previousMileage)
                            datacompined1 = UtilsCardInfo.addnfctorecord(
                                nfcItem,
                                result,
                                i + 1,
                                numberofnfcTag.toInt()
                            )
                            UtilsCardInfo.updateBinaryFile(
                                mBankCard,
                                icCpuReader,
                                "2F20",
                                "5F",
                                datacompined1.toUpperCase(),
                                this@UpdateCardActivity
                            )
                        }
                        else if (i == 1 && vehicules!![i].tagnfc != null
                            && vehicules!![i].tagnfc!!.isNotEmpty()) {
                            var previousMileage = "0"
                            previousMileage = if (vehicules!![i].vehiclemileage == null) {
                                if (oldResult.substring(102, 108)
                                        .contains("FF")
                                ) "000000" else oldResult.substring(102, 108)
                            } else {
                                vehicules!![i].vehiclemileage!!
                            }
                            nfcItem =
                                UtilsCardInfo.initNfcTAG(vehicules!![i], i + 1, previousMileage)
                            if (datacompined1.isEmpty()) datacompined1 = result
                            datacompined2 = UtilsCardInfo.addnfctorecord(
                                nfcItem,
                                datacompined1,
                                i + 1,
                                numberofnfcTag.toInt()
                            )
                            UtilsCardInfo.updateBinaryFile(
                                mBankCard,
                                icCpuReader,
                                "2F20",
                                "5F",
                                datacompined2.toUpperCase(),
                                this@UpdateCardActivity
                            )
                        }
                        else if (i == 2 && vehicules!![i].tagnfc != null
                            && vehicules!![i].tagnfc!!.isNotEmpty()) {
                            var previousMileage = "0"
                            previousMileage = if (vehicules!![i].vehiclemileage == null) {
                                if (oldResult.substring(156, 162)
                                        .contains("FF")
                                ) "000000" else oldResult.substring(156, 162)
                            } else {
                                vehicules!![i].vehiclemileage!!
                            }
                            nfcItem =
                                UtilsCardInfo.initNfcTAG(vehicules!![i], i + 1, previousMileage)
                            log(TAG, " Tag NFC nfcItem ---> $nfcItem")
                            if (datacompined2.isEmpty()) {
                                datacompined2 =
                                    if (datacompined1.isEmpty()) result else datacompined1
                            }
                            datacompined3 = UtilsCardInfo.addnfctorecord(
                                nfcItem,
                                datacompined2,
                                i + 1,
                                numberofnfcTag.toInt()
                            )
                            log(TAG, " Tag NFC datacompined 3---> $datacompined3")
                            UtilsCardInfo.updateBinaryFile(
                                mBankCard,
                                icCpuReader,
                                "2F20",
                                "5F",
                                datacompined3.toUpperCase(),
                                this@UpdateCardActivity
                            )
                        }
                        i++
                    }
                }

                readPlfPost = UtilsCardInfo.readInfoPlafondCardPost(
                    mBankCard,
                    icCpuReader,
                    this@UpdateCardActivity
                )
                readPlfPrep = UtilsCardInfo.readInfoPlafondCardPre(
                    mBankCard,
                    icCpuReader,
                    this@UpdateCardActivity
                )

                if (UtilsCardInfo.verifyPIN(
                        mBankCard,
                        icCpuReader,
                        intentExtrasModel!!.mPinNumberCard,
                        this@UpdateCardActivity
                    )
                    && UtilsCardInfo.updateCarteNomPorteur(
                        cardModel!!.nom_sur_carte,
                        mBankCard,
                        icCpuReader,
                        this@UpdateCardActivity
                    )
                    && UtilsCardInfo.updateCarteTotal(
                        this@UpdateCardActivity,
                        icCpuReader,
                        mBankCard,
                        infoCarte,
                        cardModel!!.status!!,
                        cardModel!!.customers_id,
                        cardModel!!.type_id!!,
                        cardModel!!.Unite!!,
                        cardModel!!.status!!,
                        cardModel!!.NFC!!,
                        cardModel!!.discount_type!!,
                        cardModel!!.deleted!!,
                        cardModel!!.online!!
                    )
                    && UtilsCardInfo.updateRestrictionsCarte(
                        cardModel!!.profil_STATION.toString() + "",
                        cardModel!!.profil_HORAIRE.toString() + "",
                        cardModel!!.profil_JRS_FREE.toString() + "",
                        cardModel!!.profil_SECTEUR.toString() + "",
                        cardModel!!.profil_ARTICLE.toString() + "",
                        mBankCard,
                        icCpuReader,
                        this@UpdateCardActivity
                    )
                ) {

                    if (cardModel!!.type_id == AppConstant.PREPAID_CARD || isFirstUse) {
                        isUpdated = updateCardLimits() && updateMinMaxLimits()
                    } else if (cardModel!!.type_id == AppConstant.POSTPAID_CARD
                        && !isSameMonthCredit) {

                        isUpdated = updateCardLimits() && updateMinMaxLimits()
                    } else if (cardModel!!.type_id == AppConstant.POSTPAID_CARD) {

                        isUpdated = updateCardCounts() && updateMinMaxLimits()
                    } else {
                        isUpdated = true
                    }
                } else {
                    isUpdated = false
                }

            } else {
                errorMessage = it.error!!
                isUpdated = false
            }
        } catch (e: Exception) {
            log(TAG, e.message + ExceptionUtils.getStackTrace(e))
            e.printStackTrace()
            isUpdated = false
        }
        return isUpdated
    }

    fun updateCardLimits(): Boolean {
        return UtilsCardInfo.updateInfoPlafondCardPost(
            readPlfPost,
            "" + (cardModel!!.Plafond_Mensuel!! * 100).toLong(),
            "" + (cardModel!!.Plafond_Hebdomadaire!! * 100).toLong(),
            "" + (cardModel!!.Plafond_Journalier!! * 100).toLong(),
            cardModel!!.NBR_Trs_Mensuel.toString() + "",
            cardModel!!.NBR_Trs_Hebdomadaire.toString() + "",
            cardModel!!.NBR_Trs_Journalier.toString() + "",
            mBankCard,
            icCpuReader,
            this@UpdateCardActivity
        )
    }

    fun updateRecords(recordNumber: String, updatedRecord3: String): Boolean {
        var result = UtilsCardInfo.updateRecordLinear(
            mBankCard, icCpuReader, "2F07", recordNumber, "32",
            updatedRecord3, this)
        remainingCardCeilings =
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
         "2F07", recordNumber, "32", this).replace("F", "0")
        remainingDailyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)
        REMAINING_NBR_TRS_DAILY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()
        return result == "9000"
    }

    fun updateCardCounts(): Boolean {
        return UtilsCardInfo.updateCardCounts(
            readPlfPost,
            cardModel!!.NBR_Trs_Mensuel.toString() + "",
            cardModel!!.NBR_Trs_Hebdomadaire.toString() + "",
            cardModel!!.NBR_Trs_Journalier.toString() + "",
            mBankCard,
            icCpuReader,
            this@UpdateCardActivity
        )
    }

    fun updateMinMaxLimits(): Boolean {
        return UtilsCardInfo.updateInfoPlafondCardPre(
            readPlfPrep,
            "" + (cardModel!!.Plafond_Min_Prepayee!! * 100).toLong(),
            "" + (cardModel!!.Plafond_Max_Prepayee!! * 100).toLong(),
            cardModel!!.Nbr_transaction_offline.toString(),
            "1",
            "1",
            cardModel!!.Solde_Cumulable!!.toString() + "",
            cardModel!!.discount_id.toString(),
            mBankCard,
            icCpuReader,
            this@UpdateCardActivity
        )
    }

    inner class ReadCardAsyncTask : CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@UpdateCardActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC,
                        60, responseData, responseLength, AppConstant.TPE_APP
                    )
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05" ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07" ||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(
                        mBankCard, icCpuReader, this@UpdateCardActivity
                    )
                    //icCpuReader Not Required for BTPE
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19) else return -1 //Abort Transaction

                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(
                        mBankCard, icCpuReader, authKey, this@UpdateCardActivity
                    )
                    val externalAuth2 = UtilsCardInfo.externalAuth2(
                        mBankCard, icCpuReader, authKey, this@UpdateCardActivity
                    )
                    if (authKey != null && externalAuth1 && externalAuth2) {
                        cardRemoveChecker.start()
                        if (intentExtrasModel!!.panNumber != panNumber) {
                            return -1
                        } else {
                            statusCard = UtilsCardInfo.getStatusCard(infoCarte)
                            dateExpirationCard = UtilsCardInfo.getDateExpCard(mBankCard, infoCarte)
                            val mToday: Date = Support.getDateComparison(Date())!!
                            cardExpired = UtilsCardInfo.isCardExpired(mToday, dateExpirationCard)
                            if (panNumber != null) {
                                if (checkBlackList(this@UpdateCardActivity, panNumber)
                                ) {
                                    returnValue = 5
                                } else {
                                    if (cardExpired) {
                                        returnValue = 3
                                    } else {
                                        if (UtilsCardInfo.verifyPIN(
                                                mBankCard, icCpuReader,
                                                intentExtrasModel!!.mPinNumberCard,
                                                this@UpdateCardActivity
                                            )
                                        ) {
                                            readCardLimitsAndCounter()
                                            returnValue = 1
                                        }
                                    }
                                }
                            } else {
                                returnValue = 4
                            }
                        }
                    } else {
                        return -1
                        showSnakeBar(getString(R.string.card_reading_failed))
                    }
                } else {
                    returnValue = -2
                }
            } catch (e: Exception) {
                e.printStackTrace()
                log(TAG, e.message + " " + e.cause)
            }
            return returnValue
        }

        override fun onPreExecute() {

        }

        override fun onPostExecute(result: Int?) {
            if (result == 1) {
                resetAllValues()
              mViewModel.getCardDetails(panNumber, statusCard)
            } else if (result == 0) {
                showSnakeBar(resources.getString(R.string.pin_errone_again))
                finish()
            } else if (result == 2) {
                finish()
            } else if (result == 3) {
                gotoAbortMessageActivity(
                    resources.getString(R.string.error),
                    resources.getString(R.string.card_expired)
                )
            } else if (result == 4 || result == -1) {
                gotoAbortMessageActivity(
                    resources.getString(R.string.error),
                    resources.getString(R.string.read_card_failed)
                )
            } else if (result == 5) {
                gotoAbortMessageActivity(
                    resources.getString(R.string.card_opposee),
                    resources.getString(R.string.contact_agent)
                )
            }
        }

        override fun onProgressUpdate(vararg values: IntArray) {
        }
    }

    // (old param name isRecharge: Boolean) // new creditType: Int
    fun changeSoldeCredit(montant: String, isRecharge: Boolean): Boolean {
        return try {
            val ammtHex: String = Utils.amountToHex(montant)
            if (panNumber != null) {
                authKey = assignKeyForCard(panNumber)
            }
            val transDate = Support.dateToStringX(Date())
            val creditDate = Support.dateToStringPlafondCompt(Date())
//            if(BuildConfig.DEBUG)
//            {
//                transDate = "20220326145054"
//                creditDate = "20220326"
//            }

            log(TAG, "transDate :: $transDate")
            log(TAG, "creditDate :: $creditDate")
            log(TAG, "creditDate old:: " + Support.dateToStringPlafondCompt(Date()))
            var rechargeID = "17"

            var transType = "01"
            if (!isRecharge) {
                transType = "04"
            }

            val transData = Utils.padZero(transDate, 14).toString() + transType + Utils.padZero(
                preferenceModel!!.stationID.toString() + "",
                4
            ) + "00${rechargeID}FFFFFFFFFFFFFFFFFFFFFFFF"

            if (UtilsCardInfo.verifyPIN(
                    mBankCard,
                    icCpuReader,
                    intentExtrasModel!!.mPinNumberCard,
                    this@UpdateCardActivity
                )
            ) {
                try {
                    mProduitDAO = ProductsDao()
                    mProduitDAO!!.open()
                    mProduit = mProduitDAO!!.getProductById(PRODUCT.RECHARGE)
                    mProduitDAO!!.close()
                } catch (ex: SQLiteException) {
                    ex.stackTrace
                }
                return try {

                    val lastCardBalance =  UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()
                    if (UtilsCardInfo.credit(
                            mBankCard,
                            icCpuReader,
                            ammtHex,
                            authKey,
                            transData,
                            this@UpdateCardActivity
                        )
                    ) {
                        val currentCardBalance = UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()
                        if(currentCardBalance != lastCardBalance)
                        {
                            creditStatus = 1
                        }
                        val infoCreditPostPayee: String = UtilsCardInfo.readRecordLinear(
                            mBankCard,
                            icCpuReader,
                            "2F07",
                            "06",
                            "32",
                            this@UpdateCardActivity
                        ).replace("F", "0")
                        val compteurCredit: Long = UtilsCardInfo.getCompteurTransactionPanCredit(infoCreditPostPayee)
                        UtilsCardInfo.updateCreditPostPayee(
                            mBankCard, icCpuReader, infoCreditPostPayee,
                            creditDate, montant, compteurCredit + 1, this@UpdateCardActivity
                        )
                        mTransactionOffline = TransactionModel()
                        mTransactionOffline!!.soldeCard = currentCardBalance
                        if(currentCardBalance == lastCardBalance)
                        {
                            creditStatus = 0
                            return false
                        }
                        else
                        {
                         if(isRecharge)
                        {
                            creditStatus = 1
                         saveTransaction(transactionType = RECHARGE_TRANSACTION,montant=montant)
                         }
                            val isActivated: Boolean = UtilsCardInfo.setStatusCard(
                                mBankCard,
                                icCpuReader,
                                infoCarte,
                                1,
                                this@UpdateCardActivity
                            )
                            printLog()
                            true
                        }

                    } else {
                        UtilsCardInfo.beep(mCore, 40)
                        gotoAbortMessageActivity(
                            getString(R.string.error),
                            getString(R.string.error_while_refilling_card_try_later)
                        )
                        false
                    }
                } catch (ex: Exception) {
                    ex.printStackTrace()
                    UtilsCardInfo.beep(mCore, 40)
                    gotoAbortMessageActivity(
                        getString(R.string.error),
                        getString(R.string.failed_update)
                    )
                    false
                }
            }
            false
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    private fun saveTransaction(transactionType:Int, montant: String) {
        if(mTransactionOffline == null)
        {
            mTransactionOffline = TransactionModel()
        }
        if (transactionType == RECHARGE_TRANSACTION) {
            mTransactionOffline!!.idTypeTransaction = 3 // =1 trx ; =2 ann trx ; =3 recharge or refund; =4 Postpaid Balance refill;
            mTransactionOffline!!.idProduit = 17    // recharge or monthly refill or postpaid first time
            mTransactionOffline!!.modepay = AppConstant.CARD_VALUE
            mTransactionOffline!!.transactionChecksum = rechargeModel!!.checksum
        }
        else if (transactionType == REFUND_TRANSACTION) {
            mTransactionOffline!!.idProduit = 99
            mTransactionOffline!!.modepay = AppConstant.REFUND_VALUE
            mTransactionOffline!!.transactionChecksum = refundModel!!.checksum
        }
        mTransactionOffline!!.categoryId = PRODUCT.RECHARGE
        mTransactionOffline!!.creditStatus = creditStatus
        mTransactionOffline!!.transactionStatus = creditStatus
        mTransactionOffline!!.idTypeTransaction = 3 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
        mTransactionOffline!!.idTerminal = preferenceModel?.terminalID ?: 0
        mTransactionOffline!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
        mTransactionOffline!!.idPompiste = prefs.getPompisteId(mTransactionOffline!!.codePompiste.toString())
        mTransactionOffline!!.dateTransaction = Support.dateToString(Date())
        mTransactionOffline!!.amount = montant.toDouble()//fbsAmount!!.toDouble()
        mTransactionOffline!!.quantite = 0.0
        mTransactionOffline!!.unitPrice = 0.0
        mTransactionOffline!!.flagController = 0 // flag controller
        mTransactionOffline!!.sequenceController =
            ""  // reference transaction controller
        mTransactionOffline!!.tagNFC = intentExtrasModel!!.badge
        mTransactionOffline!!.kilometrage = intentExtrasModel!!.km
        mTransactionOffline!!.pan = intentExtrasModel!!.panNumber
        mTransactionOffline!!.detailArticle = "" // qr code du produit
        mTransactionOffline!!.flagTelecollecte = 0  // flag telecollecte,
        mTransactionOffline!!.nomPorteur = nomPorteur
        mTransactionOffline!!.dateExp = dateExpirationCard
        mTransactionOffline!!.panLoyalty = ""

        mTransactionOffline!!.pumpId = ""
        mTransactionOffline!!.reference = "RCH" + Support.generateReference(
            this@UpdateCardActivity,
            true
        )
        try {
            val mTransactionDAO = TransactionDao()
            mTransactionDAO.open()
            mTransactionDAO.checkNUpdateByCheckSum(mTransactionOffline!!)
            mTransactionDAO.close()
            log(TAG, "Inserted Transaction Data:: ${gson.toJson(mTransactionOffline)}")
        }
        catch (e: java.lang.Exception)
        {
            e.printStackTrace()
            showDialog(getString(R.string.error), getString(R.string.failed_to_update_transaction_data))
        }
    }

    override fun onBackPressed() {

    }

    private fun printLog() {
        log(TAG, "totalMonthlyLimit ::: $totalMonthlyLimit")
        log(TAG, "totalWeeklyLimit ::: $totalWeeklyLimit")
        log(TAG, "totalDailyLimit ::: $totalDailyLimit")
        log(TAG, "remainingMonthlyAmount ::: $remainingMonthlyAmount")
        log(TAG, "remainingWeeklyAmount ::: $remainingWeeklyAmount")
        log(TAG, "remainingDailyAmount ::: $remainingDailyAmount")
        log(TAG, "REMAINING_NBR_TRS_MONTHLY ::: $REMAINING_NBR_TRS_MONTHLY")
        log(TAG, "REMAINING_NBR_TRS_WEEKLY ::: $REMAINING_NBR_TRS_WEEKLY")
        log(TAG, "REMAINING_NBR_TRS_DAILY ::: $REMAINING_NBR_TRS_DAILY")
        log(
            TAG,
            "cardBalance Actual ::: " + UtilsCardInfo.getAmountCardString(
                mBankCard,
                icCpuReader,
                4,
                12,
                this
            ).toString() + ""
        )
        log(TAG, "CARD_NBR_TRS_OFF ::: $CARD_NBR_TRS_OFF")
    }

    private fun processCardRefund(amount: String): Boolean {
        var result = false
        if(refundModel!!.trx_date != null && cardModel!!.updated_at != null)
        {
            val serverDate =
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(cardModel!!.updated_at)  //2022-02-25 15:09:21"
            val refundDate = SimpleDateFormat("yyyy-MM-dd").parse(refundModel!!.trx_date)  //2022-02-25
            val sameMonth =
                (serverDate.month == refundDate.month) && (serverDate.year == refundDate.year)
            if (sameMonth) {
                val updated = creditCardLimits(amount, refundModel!!.isFullRefund)

                result = updated == 1
            }
        }

        return result
    }

    private var totalMonthlyLimit = 0.0 // plafondMensuel
    private var totalWeeklyLimit = 0.0 // plafondHebdo
    private var totalDailyLimit = 0.0 //plafondJournalier
    private var CARD_NBR_TRS_OFF = 0

    private var remainingMonthlyAmount = 0.0 //plafondMensuelCompt
    private var remainingWeeklyAmount = 0.0 //plafondHebdoCompt
    private var remainingDailyAmount = 0.0 //plafondJournaliercompt
    private var REMAINING_NBR_TRS_MONTHLY = 0 //COMPTEUR_NBR_TRS_MENSUEL
    private var REMAINING_NBR_TRS_WEEKLY = 0 //COMPTEUR_NBR_TRS_HEBDO
    private var REMAINING_NBR_TRS_DAILY = 0 //COMPTEUR_NBR_TRS_JOURNALIER

    private lateinit var cardStaticStructure: CardStaticStructureModel

    var cardType: Int? = 0
    var cardHolderName = ""
    var isLitreUnit = false
    var remainingCardCeilings: String? = ""
    private fun getCardStaticStructure() {
        if (infoCarte != null && panNumber.isNotEmpty()) {
            cardStaticStructure =
                UtilsCardInfo.readCardStaticStructureInfo(mBankCard, icCpuReader, this)
            val jsonData = (gson.toJson(cardStaticStructure))
            "Card Static Structure : $jsonData".also { log(TAG, it) }
            cardType = cardStaticStructure.cardType.toInt()
            intentExtrasModel!!.verificationType = cardStaticStructure.verificationType.toInt()
        }
    }

    private fun readCardLimitsAndCounter() {
        getCardStaticStructure()

        cardHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this)!!
        //Remaining Card Limits
        if (cardStaticStructure.cardCeilingUnit == "1") {
            isLitreUnit = true
        }
        //TotalCard Ceiling Limits
        val cardCeilings = UtilsCardInfo.readRecordLinear(
            mBankCard, icCpuReader,
            "2F07", "02", "32", this
        ).replace("F", "0")
        totalMonthlyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 0, 12) //Monthly card limit
        totalWeeklyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 12, 24) //weekly ceiling cap
        totalDailyLimit =
            UtilsCardInfo.getCardCeilings(cardCeilings, 24, 36) //ceiling day of the card

        // counter ceiling
        remainingCardCeilings = if (isLitreUnit) {
            UtilsCardInfo.readRecordLinear(
                mBankCard, icCpuReader, "2F07",
                "03", "32", this
            ).replace("F", "0")
        } else {
            UtilsCardInfo.readRecordLinear(
                mBankCard, icCpuReader, "2F07",
                "01", "32", this
            ).replace("F", "0")
        }
        remainingMonthlyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
        remainingWeeklyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 12, 24)
        remainingDailyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)

        REMAINING_NBR_TRS_MONTHLY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
        REMAINING_NBR_TRS_WEEKLY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 44, 52).toInt()
        REMAINING_NBR_TRS_DAILY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()

        log(
            TAG, """ cardHolderName: $cardHolderName cardCeilings: $cardCeilings
           remainingCardCeilings: $remainingCardCeilings """.trimIndent()
        )
    }

    private fun creditCardLimits(amount: String, updateTrxCount: Boolean): Int {
        try {
            var prd =
                /*if (mProduct != null) Utils.padZero(mProduct!!.productID.toString() + "", 4) else*/
                "0000"
            prd = "0099"

            log(TAG, "Product id in Credit Card: $prd")

            val nowDate = Support.dateToStringX(Date())
            val transData = Utils.padZero(nowDate, 14).toString() + "01" + Utils.padZero(
                preferenceModel!!.stationID.toString() + "",
                4
            ) + prd + (if (intentExtrasModel!!.mTransaction != null) Support.dateToStringX(
                intentExtrasModel!!.mTransaction!!.dateTransaction!!
            ) else "FFFFFFFFFFFFFF") + "FFFFFFFFFF"

            val ammtHex = Utils.amountToHex(amount)

            if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
                returnValue = 1
            }
            if (intentExtrasModel!!.mPinNumberCard != null && UtilsCardInfo.verifyPIN(
                    mBankCard,
                    icCpuReader,
                    intentExtrasModel!!.mPinNumberCard,
                    this
                )
            ) {
                if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
                    if (intentExtrasModel!!.loyaltyTrx) {
                        if (intentExtrasModel!!.mTransaction != null) intentExtrasModel!!.mTransaction!!.detailArticle =
                            AppConstant.DETAIL_ARTICLE
                        returnValue = 1
                    }
                } else if (cardType != null && cardType == AppConstant.POSTPAID_CARD || cardType == AppConstant.PREPAID_CARD) {

                    remainingCardCeilings = if (isLitreUnit) {
                        UtilsCardInfo.readRecordLinear(
                            mBankCard,
                            icCpuReader,
                            "2F07",
                            "03",
                            "32",
                            this
                        ).replace("F", "0")
                    } else {
                        UtilsCardInfo.readRecordLinear(
                            mBankCard,
                            icCpuReader,
                            "2F07",
                            "01",
                            "32",
                            this
                        ).replace("F", "0")
                    }

                    remainingMonthlyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
                    remainingWeeklyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 12, 24)
                    remainingDailyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)

                    val dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
                    REMAINING_NBR_TRS_MONTHLY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
                    REMAINING_NBR_TRS_WEEKLY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 44, 52).toInt()
                    REMAINING_NBR_TRS_DAILY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()

                    // To fix outofbound exception because of minus value
                    if (remainingMonthlyAmount <= 0) {
                        remainingMonthlyAmount = amount.toDouble()
                    } else {
                        remainingMonthlyAmount -= amount.toDouble()
                    }
                    if (remainingWeeklyAmount <= 0) {
                        remainingWeeklyAmount = amount.toDouble()
                    } else {
                        remainingWeeklyAmount -= amount.toDouble()
                    }
                    if (remainingDailyAmount <= 0) {
                        remainingDailyAmount = amount.toDouble()
                    } else {
                        remainingDailyAmount -= amount.toDouble()
                    }
                    if (updateTrxCount) {
                        if (REMAINING_NBR_TRS_MONTHLY <= 0) {
                            REMAINING_NBR_TRS_MONTHLY = 1
                        } else {
                            REMAINING_NBR_TRS_MONTHLY -= 1
                        }
                        if (REMAINING_NBR_TRS_WEEKLY <= 0) {
                            REMAINING_NBR_TRS_WEEKLY = 1
                        } else {
                            REMAINING_NBR_TRS_WEEKLY -= 1
                        }
                        if (REMAINING_NBR_TRS_DAILY <= 0) {
                            REMAINING_NBR_TRS_DAILY = 1
                        } else {
                            REMAINING_NBR_TRS_DAILY -= 1
                        }
                    }
                    // To fix outofbound exception because of minus value

                    intentExtrasModel!!.typeCard = cardType!!.toString()

                    val readPlf = UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings)
                    val plfMonthNew = ((remainingMonthlyAmount) * 100).toLong()
                    val plfWeekNew = ((remainingWeeklyAmount) * 100).toLong()
                    val plfDailyNew = ((remainingDailyAmount) * 100).toLong()

                    val nbreTrxMonthNewCompt = ((REMAINING_NBR_TRS_MONTHLY) * 100).toLong()
                    val nbreTrxWeekNewCompt = ((REMAINING_NBR_TRS_WEEKLY) * 100).toLong()
                    val nbreTrxDayNewCompt = ((REMAINING_NBR_TRS_DAILY) * 100).toLong()

                    /* val transDebitData = Utils.padZero(nowDate, 14).toString() + "00" + Utils.padZero(prefs.getPreferenceModel()!!.stationID.toString() + "", 4) + prd + (if (intentExtrasModel!!.mTransaction  != null) Support.dateToStringX(intentExtrasModel!!.mTransaction!!.dateTransaction!!) else "FFFFFFFFFFFFFF") + "FFFFFFFFFF"
                     val transDebitAmountHex = intentExtrasModel!!.amount
                     val amountDebitRecord = UtilsCardInfo.debit(mBankCard, icCpuReader, ammtHex, authKey, transDebitData, this@DebitCardLimitsActivity)*/
                    val lastCardBalance =  UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()
                    val cardBalanceUpdated = UtilsCardInfo.credit(
                        mBankCard,
                        icCpuReader,
                        ammtHex,
                        authKey,
                        transData,
                        this@UpdateCardActivity
                    )
                    if (cardBalanceUpdated &&
                        if (isLitreUnit)
                            UtilsCardInfo.updateInfoPlafondCardCompt(
                                mBankCard,
                                icCpuReader,
                                readPlf,
                                "03",
                                plfMonthNew.toString() + "",
                                plfWeekNew.toString() + "",
                                plfDailyNew.toString() + "",
                                "" + nbreTrxMonthNewCompt,
                                "" + nbreTrxWeekNewCompt,
                                "" + nbreTrxDayNewCompt,
                                this
                            ) else
                            UtilsCardInfo.updateInfoPlafondCardCompt(
                                mBankCard,
                                icCpuReader,
                                readPlf,
                                "01",
                                plfMonthNew.toString() + "",
                                plfWeekNew.toString() + "",
                                plfDailyNew.toString() + "",
                                "" + nbreTrxMonthNewCompt,
                                "" + nbreTrxWeekNewCompt,
                                "" + nbreTrxDayNewCompt,
                                this
                            )
                    ) {

                        remainingCardCeilings = if (isLitreUnit) {
                            UtilsCardInfo.readRecordLinear(
                                mBankCard,
                                icCpuReader,
                                "2F07",
                                "03",
                                "32",
                                this
                            ).replace("F", "0")
                        } else {
                            UtilsCardInfo.readRecordLinear(
                                mBankCard,
                                icCpuReader,
                                "2F07",
                                "01",
                                "32",
                                this
                            ).replace("F", "0")
                        }
                        prefs.remCardCeiling = remainingCardCeilings

                        val infoCreditPostPayee = UtilsCardInfo.readRecordLinear(
                            mBankCard,
                            icCpuReader,
                            "2F07",
                            "06",
                            "32",
                            this
                        ).replace("F", "0")
                        val compteurTrxCredit = UtilsCardInfo.getCompteurTransactionPanCredit(
                            infoCreditPostPayee
                        )
                        intentExtrasModel!!.compteurTrxCredit = compteurTrxCredit.toString()
                        UtilsCardInfo.updateCompteurTrxCredit(
                            mBankCard,
                            icCpuReader,
                            infoCreditPostPayee,
                            compteurTrxCredit + 1,
                            this
                        )

                        if (stationMode != AppConstant.OFFLINE_TRX_MODE) {
                            val readPlfPrep = UtilsCardInfo.readRecordLinear(
                                mBankCard,
                                icCpuReader,
                                "2F09",
                                "05",
                                "28",
                                this
                            )
                            UtilsCardInfo.updateNbreTrxOFFCompteur(
                                mBankCard,
                                icCpuReader,
                                readPlfPrep,
                                "" + (CARD_NBR_TRS_OFF),
                                this
                            )
                            CARD_NBR_TRS_OFF =
                                UtilsCardInfo.getCardCeilingCount(readPlfPrep, 31, 35)
                        }
                        val currentCardBalance =  UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()
                        mTransactionOffline = TransactionModel()
                        mTransactionOffline!!.soldeCard = currentCardBalance
                        if(lastCardBalance != currentCardBalance)
                        {
                            creditStatus = 1
                            saveTransaction(transactionType = REFUND_TRANSACTION,montant=amount)
                            returnValue = 1
                        }
                        else
                        {
                            creditStatus = 0
                            returnValue = -2
                        }
                    } else
                        returnValue = -2

                } else {
                    returnValue = -2
                    return -2
                }
            }
        } catch (e: Exception) {
            log(TAG, e.message + ExceptionUtils.getStackTrace(e))
            //  mViewModel.generateLogs(e.message!!,0)
            e.printStackTrace()
            return -2
        }
        return returnValue
    }

    fun resetCardBalance() {
        val cardBalanceString = UtilsCardInfo.getAmount(mBankCard, icCpuReader,
            4, 12, this)
        val ammtHex = Utils.amountToHex(cardBalanceString.toString())
        val nowDate = Support.dateToStringX(Date())
        log(TAG, "DATE ::: " + Support.dateToStringX(nowDate))
        val prd = "0000"
        val transData = Utils.padZero(nowDate, 14).toString() + "00" + Utils.padZero(
            preferenceModel!!.stationID.toString() + "",
            4
        ) + prd + (Support.dateToStringX(nowDate)) + "FFFFFFFFFF"
        if (UtilsCardInfo.verifyPIN(
                mBankCard,
                icCpuReader,
                intentExtrasModel!!.mPinNumberCard,
                this
            ) && cardBalanceString > 0
        ) {
            if (UtilsCardInfo.debit(mBankCard, icCpuReader, ammtHex, authKey,
                    transData, this)) {
                log(TAG, "Card Balance Reset Success")
                creditMonthlyLimit()
            } else {
                gotoAbortMessageActivity(
                    resources.getString(R.string.error),
                    resources.getString(R.string.failed_reset_card)
                )
            }
        } else {
            creditMonthlyLimit()
        }

    }

    fun creditMonthlyLimit() {
        if (UtilsCardInfo.verifyPIN(
                mBankCard,
                icCpuReader,
                intentExtrasModel!!.mPinNumberCard,
                this
            )
        ) {
            val cardBalance = UtilsCardInfo.getAmount(mBankCard, icCpuReader,
                4, 12, this)
            val monthlyLimitFBS =
                decimalFormat.format(BigDecimal.valueOf(cardModel!!.Plafond_Mensuel!!.toDouble()))
            amount = monthlyLimitFBS
            val convertedAmount = BigDecimal.valueOf(amount!!.toDouble())
            amount = decimalFormat.format(convertedAmount)
            fbsAmount = amount
            val chengecredit = changeSoldeCredit(amount!!, false)
            log(TAG, "||---Monthly limit fbs converted----> $monthlyLimitFBS")
            log(TAG, "||---Old cardBalanceString----> $cardBalance")
            log(TAG, "||---recharge amount ----> $amount")
            log(TAG, "||--- fbsAmount----> $fbsAmount")
            log(TAG, "||--- monthlyLimit ----> $monthlyLimit")
            log(TAG, "||--- monthlyLimit FBS ----> " + cardModel!!.Plafond_Mensuel!!)
            log(TAG, "SOLDE CREDIT UPDATED: $chengecredit")
            val cardBalanceString =
                UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader,
                    4, 12, this)
            log(TAG, "||---New cardBalanceString----> $cardBalanceString")
        }
    }

    fun updateLimitsNbalance() {
        val mToday = Support.getDateComparison(Date())
        var creditDate = Support.dateToStringPlafondCompt(mToday)!!
        if (cardStaticStructure.cardCeilingUnit == "1") {
            isLitreUnit = true
        }
        // counter ceiling
        remainingCardCeilings = if (isLitreUnit) {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
                "2F07", "03", "32", this)
                .replace("F", "0")
        } else {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
                "2F07", "01", "32", this)
                .replace("F", "0")
        }
        dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
        if (cardType != null && cardType == AppConstant.PREPAID_CARD
            || cardType == AppConstant.POSTPAID_CARD) {
            if (cardType == POSTPAID_CARD) {
                val infoCreditPostPayee =
                    UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
                        "2F07", "06", "32", this)
                        .replace("F", "0")
                dateLastPLF = UtilsCardInfo.getDateLastPLF(infoCreditPostPayee)
                isSameMonthCredit = UtilsDate.isSameMonth(mToday, dateLastPLF)

                if (dateLastPLF != null && dateLastPLF!!.isNotEmpty()
                    && dateLastPLF!!.contains("0000")) {
                    resetCardBalance()
                } else if (dateLastPLF != null && dateLastPLF!!.isNotEmpty()) {
                    printLog()
                    if (!isSameMonthCredit) {
                        resetCardBalance()
                    }
                }
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                if (isLitreUnit) dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
                else
                    dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                isSameMonthCredit = UtilsDate.isSameMonth(mToday, dateLastPLF)
            }

            if (!isSameMonthCredit) {
                if (true) {
                    val plafondCarte: String = UtilsCardInfo.readRecordLinear(
                        mBankCard,
                        icCpuReader,
                        "2F09",
                        "05",
                        "28",
                        this
                    )
                    UtilsCardInfo.updateNbreTrxOFFCompteur(
                        mBankCard,
                        icCpuReader,
                        plafondCarte,
                        0.toString() + "",
                        this
                    )
                    val plafondCarteNew: String = UtilsCardInfo.readRecordLinear(
                        mBankCard,
                        icCpuReader,
                        "2F09",
                        "05",
                        "28",
                        this
                    )
                    CARD_NBR_TRS_OFF = UtilsCardInfo.getCardCeilingCount(plafondCarteNew, 31, 35)
                }
                readPlf =
                    if (isLitreUnit) UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings!!)
                    else
                        UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings!!)
                var updatedRecord1: String? = UtilsCardInfo.prepareRecordLinear(
                    readPlf!!,
                    0,
                    12,
                    Utils.padZero(0.toString() + "", 12)
                )
                updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
                    updatedRecord1!!,
                    36,
                    44,
                    Utils.padZero(0.toString() + "", 8)
                )
                val updatedRecord3: String = UtilsCardInfo.prepareRecordLinear(
                    updatedRecord1,
                    72,
                    80,
                    Utils.padZero(creditDate, 8)
                )
                if (isLitreUnit) {
                    UtilsCardInfo.updateRecordLinear(
                        mBankCard,
                        icCpuReader,
                        "2F07",
                        "03",
                        "32",
                        updatedRecord3,
                        this
                    )
                    remainingCardCeilings = UtilsCardInfo.readRecordLinear(
                        mBankCard,
                        icCpuReader,
                        "2F07",
                        "03",
                        "32",
                        this
                    ).replace("F", "0")
                    remainingMonthlyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
                    REMAINING_NBR_TRS_MONTHLY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
                } else {
                    UtilsCardInfo.updateRecordLinear(
                        mBankCard,
                        icCpuReader,
                        "2F07",
                        "01",
                        "32",
                        updatedRecord3,
                        this
                    )
                    remainingCardCeilings = UtilsCardInfo.readRecordLinear(
                        mBankCard,
                        icCpuReader,
                        "2F07",
                        "01",
                        "32",
                        this
                    ).replace("F", "0")
                    remainingMonthlyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
                    REMAINING_NBR_TRS_MONTHLY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
                }
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                isNextDayPLF = UtilsDate.isNextDayPLF(mToday, dateLastPLF)
            }
            if (isNextDayPLF) {
                readPlf =
                    if (isLitreUnit) UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings!!) else UtilsCardInfo.readCardCeilingLimits(
                        remainingCardCeilings!!
                    )
                var updatedRecord1: String? = UtilsCardInfo.prepareRecordLinear(
                    readPlf!!,
                    24,
                    36,
                    Utils.padZero(0.toString() + "", 12)
                )
                updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
                    updatedRecord1!!,
                    52,
                    60,
                    Utils.padZero(0.toString() + "", 8)
                )
                val updatedRecord3: String = UtilsCardInfo.prepareRecordLinear(
                    updatedRecord1,
                    72,
                    80,
                    Utils.padZero(creditDate, 8)
                )
                if (isLitreUnit) {
                    updateRecords("03", updatedRecord3)
                } else {
                    updateRecords("01", updatedRecord3)
                }
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                if (!UtilsDate.isSameWeek(Date(), dateLastPLF)) {

                    readPlf = if (isLitreUnit) UtilsCardInfo.readCardCeilingLimits(
                        remainingCardCeilings!!
                    ) else UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings!!)
                    var updatedRecord1: String? = UtilsCardInfo.prepareRecordLinear(
                        readPlf!!,
                        12,
                        24,
                        Utils.padZero(0.toString() + "", 12)
                    )
                    updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
                        updatedRecord1!!,
                        44,
                        52,
                        Utils.padZero(0.toString() + "", 8)
                    )
                    val updatedRecord3: String = UtilsCardInfo.prepareRecordLinear(
                        updatedRecord1,
                        72,
                        80,
                        Utils.padZero(creditDate, 8)
                    )
                    if (isLitreUnit) {
                        updateRecords("03", updatedRecord3)
                    } else {
                        updateRecords("01", updatedRecord3)
                    }
                } else log(
                    TAG,
                    " isNextWeekPLF ----> false "
                )
            }

        }
    }
    private var isCardRemovedShown = false
    private val cardRemoveChecker = object : CountDownTimer(10000,1000){
        override fun onTick(p0: Long) {
            if(BuildConfig.POS_TYPE == AppConstant.PAX){
                if(!IccTester.getInstance().detect( 0.toByte()))
                {
                    if(!isCardRemovedShown)
                    {
                        isCardRemovedShown = true
                        if(isCreditValidationDone)
                        {
                            log(TAG,"SaveCreditStatus 3")
                            SaveCreditStatus()
                        }
                        gotoAbortMessageActivity(getString(R.string.error),getString(R.string.card_removed),SettingsActivity::class.java)
                        log(TAG,"CARD REMOVED")
                    }
                }
            }
        }

        override fun onFinish() {
            this.start()
        }

    }

    override fun onDestroy() {
        try {
            cardRemoveChecker.cancel()
        } catch (e:Exception) {
            e.printStackTrace()
        }
        super.onDestroy()
    }

}
