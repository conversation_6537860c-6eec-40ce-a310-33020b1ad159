package app.rht.petrolcard.ui

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.hardware.usb.UsbDevice
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.LocaleManager
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.constant.AppConstant
import com.citizen.sdk.CitizenPrinterInfo
import com.citizen.sdk.ESCPOSConst
import com.citizen.sdk.ESCPOSPrinter
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import kotlinx.android.synthetic.main.activity_dw14_printer.*
import java.io.FileInputStream
import java.util.*

class Dw14PrinterActivity : AppCompatActivity() {
    private val TAG = Dw14PrinterActivity::class.java.simpleName
    lateinit var prefs : AppPreferencesHelper
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_dw14_printer)

        prefs = MainApp.getPrefs()

        btnInit.setOnClickListener {
            initPrinter()
        }

        btnPrint.setOnClickListener {
            printTicket()
        }

        btnClose.setOnClickListener {
            resetPrinter()
        }
    }


    //region citizen printer
    var printerType = 1
    private lateinit var citizenPrinter: ESCPOSPrinter
    private var usbDevice: UsbDevice? = null

    private fun initCitizenPrinter() {
        citizenPrinter = ESCPOSPrinter()
        citizenPrinter.setContext(this)
        usbDevice = null
        val result = citizenPrinter.connect(ESCPOSConst.CMP_PORT_USB, usbDevice)

        if (ESCPOSConst.CMP_SUCCESS == result) {
            // Set encoding
            citizenPrinter.setEncoding("ISO-8859-1")
            // Start Transaction ( Batch )
            citizenPrinter.transactionPrint(ESCPOSConst.CMP_TP_TRANSACTION)
            Support.log("Max. Page area", "( x,y ) : " + citizenPrinter.pageModeArea)

            // Direction set
            citizenPrinter.pageModePrintDirection = ESCPOSConst.CMP_PD_TOP_TO_BOTTOM
            //logWriter.appendLog(TAG, "CitizenPrint KT")

        } else {
            // Connect Error
            Support.log("Citizen_POS_sample1", "Connect or Printer Error : $result")
            //logWriter.appendLog(TAG, "CitizenPrinter Connect error code: " + result + " message: " + showCitizenPrinterMessage(result))

            Thread {
                Handler(Looper.getMainLooper()).post {
                    if (result != ESCPOSConst.CMP_E_CONNECTED)
                        Toast.makeText(MainApp.appContext, showCitizenPrinterMessage(result), Toast.LENGTH_LONG).show()
                }
            }.start()
        }
    }
    private fun resetCitizenPrinter() {
        text(" ", AlignmentType.LEFT)
        text(" ", AlignmentType.LEFT)
        citizenPrinter.cutPaper(ESCPOSConst.CMP_CUT_PARTIAL_PREFEED)
        val result = citizenPrinter.transactionPrint(ESCPOSConst.CMP_TP_NORMAL)
        citizenPrinter.disconnect()
        if (ESCPOSConst.CMP_SUCCESS != result) {
            // Print process Error
            Support.log("Citizen_POS_sample1", "Transaction Error : $result")
            //logWriter.appendLog(TAG, "Transaction Error : $result")
        } else {
            //logWriter.appendLog(TAG, "print success")
        }
    }
    private fun showCitizenPrinterMessage(result: Int): String {
        val word: String = when (result) {
            ESCPOSConst.CMP_SUCCESS -> "The operation is success."
            ESCPOSConst.CMP_E_CONNECTED -> "The printer is already connected."
            ESCPOSConst.CMP_E_DISCONNECT -> "The printer is not connected."
            ESCPOSConst.CMP_E_NOTCONNECT -> "Failed connection to the printer."
            ESCPOSConst.CMP_E_CONNECT_NOTFOUND -> "Failed to check the support model after connecting to the device."
            ESCPOSConst.CMP_E_CONNECT_OFFLINE -> "Failed to check the printer status after connecting to the device."
            ESCPOSConst.CMP_E_ILLEGAL -> "Unsupported operation with the Device, or an invalid parameter value was used."
            ESCPOSConst.CMP_E_OFFLINE -> "The printer is off-line."
            ESCPOSConst.CMP_E_NOEXIST -> "The file name does not exist."
            ESCPOSConst.CMP_E_FAILURE -> "The Service cannot perform the requested procedure."
            ESCPOSConst.CMP_E_TIMEOUT -> "The Service timed out waiting for a response from the printer."
            ESCPOSConst.CMP_E_NO_LIST -> "The printer cannot be found in the printer search."
            ESCPOSConst.CMP_EPTR_COVER_OPEN -> "The cover of the printer opens."
            ESCPOSConst.CMP_EPTR_REC_EMPTY -> "The printer is out of paper."
            ESCPOSConst.CMP_EPTR_BADFORMAT -> "The specified file is in an unsupported format."
            ESCPOSConst.CMP_EPTR_TOOBIG -> "The specified bitmap is either too big."
            else -> "The other error."
        }
        return word
    }
    //endregion


    //region printer methods
    private fun initPrinter(){
        if(BuildConfig.DEBUG){
            printerType = AppConstant.DW14_PRINTER
        }
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {

                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        initCitizenPrinter()
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){

                    }
                    else if(printerType==AppConstant.DW14_PRINTER){
                        initCitizenPrinter()
                    }
                }
            }
            else -> {

            }
        }
    }
    private fun resetPrinter(){
        Log.e(TAG,"Reset Printer")
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {

                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        resetCitizenPrinter()
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){

                    }
                    else if(printerType==AppConstant.DW14_PRINTER){
                        resetCitizenPrinter()
                    }
                }
            }
            else -> {

            }
        }
    }
    private fun getAlignment(type: AlignmentType): Int {
        return when (type) {
            AlignmentType.CENTER -> ESCPOSConst.CMP_ALIGNMENT_CENTER
            AlignmentType.RIGHT -> ESCPOSConst.CMP_ALIGNMENT_RIGHT
            else -> ESCPOSConst.CMP_ALIGNMENT_LEFT
        }
    }
    private fun setPageMargin() {
        //printer.setPageModePrintArea( "40,0,250,0");
    }
    private fun text(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {

                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment), ESCPOSConst.CMP_FNT_DEFAULT,
                            ESCPOSConst.CMP_TXT_1WIDTH or ESCPOSConst.CMP_TXT_1HEIGHT)
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){

                    }
                    else if(printerType==AppConstant.DW14_PRINTER){

                    }
                }
            }
            else -> {

            }
        }

    }
    private fun boldText(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {

                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment), ESCPOSConst.CMP_FNT_BOLD,
                            ESCPOSConst.CMP_TXT_1WIDTH or ESCPOSConst.CMP_TXT_1HEIGHT
                        )
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){

                    }
                    else if(printerType==AppConstant.DW14_PRINTER){

                    }
                }
            }
            else -> {

            }
        }
    }
    private fun text2(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {

                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment),
                            ESCPOSConst.CMP_FNT_DEFAULT,
                            ESCPOSConst.CMP_TXT_2WIDTH or ESCPOSConst.CMP_TXT_2HEIGHT)
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){

                    }
                    else if(printerType==AppConstant.DW14_PRINTER){

                    }
                }
            }
            else -> {

            }
        }
    }
    private fun boldText2(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {

                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment),
                            ESCPOSConst.CMP_FNT_BOLD,
                            ESCPOSConst.CMP_TXT_2WIDTH or ESCPOSConst.CMP_TXT_2HEIGHT
                        )
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){

                    }
                    else if(printerType==AppConstant.DW14_PRINTER){

                    }
                }
            }
            else -> {

            }
        }
    }
    private fun printBitmapReceipt(bmp: Bitmap, layout: Int) {
        setPageMargin()

        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if (MainApp.deviceName.contains("A920")) {

                } else {
                    if (printerType == AppConstant.CITIZEN_PRINTER) {
                        //val bitmap = Bitmap.createScaledBitmap(bmp, width, height, false)
                        val result = citizenPrinter.printBitmap(
                            bmp,
                            bmp.height,
                            ESCPOSConst.CMP_ALIGNMENT_CENTER
                        )
                        Support.log(TAG, "PRINT BITMAP Result: $result")
                        //bmp.recycle()
                    } else if (printerType == AppConstant.PT102_PRINTER) {
                        Support.log(TAG, "IMAGE print not working in PT102")
                    } else if (printerType == AppConstant.DW14_PRINTER) {

                    }
                }
            }
            else -> {

            }
        }
    }
    private fun printBitmap(bmp: Bitmap, width:Int, height:Int, alignment: AlignmentType) {
        setPageMargin()

        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if (MainApp.deviceName.contains("A920")) {

                } else {
                    if (printerType == AppConstant.CITIZEN_PRINTER) {
                        val bitmap = Bitmap.createScaledBitmap(bmp, width, height, false)
                        val result = citizenPrinter.printBitmap(
                            bitmap,
                            bitmap.height,
                            getAlignment(alignment)
                        )
                        Support.log(TAG, "PRINT BITMAP Result: $result")
                    } else if (printerType == AppConstant.PT102_PRINTER) {
                        Support.log(TAG, "IMAGE print not working in PT102")
                    } else if (printerType == AppConstant.DW14_PRINTER) {

                    }
                }
            }
            else -> {

            }
        }
    }
    private fun printBitmap(bmp:ByteArray,  width:Int, height:Int, alignment: AlignmentType) {
        setPageMargin()
        /*val result =   citizenPrinter.printBitmap(bitmap, width, getAlignment(alignment))
        log(TAG, "PRINT BITMAP Result: $result")*/

        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {

            }
            "PAX" -> {
                if (MainApp.deviceName.contains("A920")) {

                } else {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        //var bitmap = BitmapFactory.decodeByteArray(bmp, 0, bmp.size)
                        val result =   citizenPrinter.printBitmap(bmp,width, height,getAlignment(alignment))
                        Support.log(TAG, "PRINT BITMAP Result: $result")
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        Support.log(TAG, "IMAGE print not working in PT102")
                    }
                    else if(printerType==AppConstant.DW14_PRINTER){

                    }
                }
            }
            else -> {

            }
        }
    }
    //endregion

    private fun formatText(inputText: String): String {
        return if (inputText.contains("\n\n")) """
     ${inputText.replace("\n", "")}
     
     
     """.trimIndent() else """
     ${inputText.replace("\n", "")}
     """.trimIndent()
        //else
        //    return inputText;
    }

    var receipt = ReceiptBuilder(800)
    private fun printTicket() {
        val ticketThread = Thread {
            var bitmap : Bitmap? = null
            try {
                bitmap = BitmapFactory.decodeResource(resources, R.drawable.logo)
                //bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                bitmap = Support.getResizedBitmap(bitmap,200,200)
                receipt.setMargin(0, 0).setAlign(Paint.Align.LEFT).setColor(Color.BLACK).addLine(180).setAlign(
                    Paint.Align.CENTER).addParagraph().addImage(bitmap)

            } catch (e:java.lang.Exception) { Log.e(TAG,"LOGO Not printed on receipt  ${e.message}") }

            receipt = ReceiptBuilder(800)
            receipt.setTextSize(35f).setAlign(Paint.Align.CENTER).addText(Support.getDateTicket(
                Date()
            )).addText("")
            receipt.setTextSize(35f).setAlign(Paint.Align.CENTER).addText(getString(R.string.label_referencement)).addText("")
            setAlignment(Paint.Align.LEFT,35f).addText(prefs.getStationModel()!!.name).addText(prefs.getReferenceModel()!!.terminal!!.address).addText(prefs.getReferenceModel()!!.terminal!!.city)
            receipt.addText("${getString(R.string.fiscal_label)} : ${prefs.getReferenceModel()!!.terminal!!.fiscalId}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText(getString(R.string.ceilings)+" : ${prefs.getReferenceModel()!!.terminal!!.maxRefillAmount.toString()} ${prefs.currency}")
            receipt.addText("")
            receipt.addText("")
            val reciept = receipt.build()

            Handler(Looper.getMainLooper()).post {
                TicketPrinter(this).printReceipt(bitmap!!)
            }

            try {
                bitmap?.recycle()
            } catch (ex:Exception){ ex.printStackTrace() }
        }
        ticketThread.start()
    }

    fun setAlignment(align: Paint.Align,size: Float):ReceiptBuilder
    {
        return if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this)) {
            when (align) {
                Paint.Align.RIGHT -> {
                    receipt.setAlign(Paint.Align.LEFT).setTextSize(size).setTypeface(this, "fonts/Roboto-Bold.ttf")
                }
                Paint.Align.LEFT -> {
                    receipt.setAlign(Paint.Align.RIGHT).setTextSize(size).setTypeface(this, "fonts/Roboto-Bold.ttf")
                }
                else -> {
                    receipt.setAlign(align).setTextSize(size).setTypeface(this, "fonts/Roboto-Bold.ttf")
                }
            }
        } else {
            receipt.setAlign(align).setTextSize(size).setTypeface(this, "fonts/Roboto-Regular.ttf")
        }
    }

}