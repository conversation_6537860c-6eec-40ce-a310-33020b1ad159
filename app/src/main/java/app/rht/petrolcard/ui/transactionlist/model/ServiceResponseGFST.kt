package app.rht.petrolcard.ui.transactionlist.model
import com.google.gson.annotations.SerializedName

//GET FUEL TRANSACTION BY SEQUENCE NUMBER
import androidx.annotation.Keep
@Keep
data class ServiceResponseGFST(
    @SerializedName("ServiceResponse")
    val serviceResponse: ServiceResponseFST
)
@Keep
data class ServiceResponseFST(
//    @SerializedName("ApplicationSender")
//    val applicationSender: Long,
    @SerializedName("FDCdata")
    val fDCdata: FDCdataFST,
//    @SerializedName("OverallResult")
//    val overallResult: String,
//    @SerializedName("RequestID")
//    val requestID: Long,
//    @SerializedName("RequestType")
//    val requestType: String,
//    @SerializedName("WorkstationID")
//    val workstationID: String,
//    @SerializedName("xmlns:xsd")
//    val xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    val xmlnsXsi: String
)

@Keep
data class FDCdataFST(
    @SerializedName("DeviceClass")
    val deviceClass: DeviceClassFST,
    @SerializedName("FDCTimeStamp")
    val fDCTimeStamp: String
)

@Keep
data class DeviceClassFST(
    @SerializedName("Amount")
    val amount: Double,
//    @SerializedName("AuthorisationApplicationSender")
//    val authorisationApplicationSender: String,
//    @SerializedName("BlendRatio")
//    val blendRatio: Int,
//    @SerializedName("CRCMode")
//    val cRCMode: String,
//    @SerializedName("CompletionReason")
//    val completionReason: String? = null,
//    @SerializedName("DSPFields")
//    val dSPFields: String,
//    @SerializedName("DeviceID")
//    val deviceID: Int,
//    @SerializedName("ErrorCode")
//    val errorCode: String,
//    @SerializedName("FuelMode")
//    val fuelMode: FuelModeFST,
//    @SerializedName("LockingApplicationSender")
//    val lockingApplicationSender: String,
    @SerializedName("NozzleNo")
    val nozzleNo: Long,
    @SerializedName("ProductNo")
    val productNo: Int,
//    @SerializedName("ProductNo1")
//    val productNo1: Long,
    @SerializedName("PumpNo")
    val pumpNo: Int,
//    @SerializedName("ReleaseToken")
//    val releaseToken: Long,
//    @SerializedName("State")
//    val state: String,
    @SerializedName("TransactionSeqNo")
    val transactionSeqNo: String, //Changed Long to String
//    @SerializedName("Type")
//    val type: String,
    @SerializedName("UnitPrice")
    val unitPrice: Double,
    @SerializedName("Volume")
    val volume: Double,
    @SerializedName("StartTimeStamp")
    val startTimeStamp: String,
    @SerializedName("EndTimeStamp")
    val endTimeStamp: String
//    @SerializedName("VolumeProduct1")
//    val volumeProduct1: Double,
//    @SerializedName("VolumeProduct2")
//    val volumeProduct2: Int
)

//@Keep
//data class FuelModeFST(
//    @SerializedName("ModeNo")
//    val modeNo: Int
//)

@Keep
data class FDCMessageResponse(
    @SerializedName("FDCMessage")
    val fdcMessageResponse: FDCMessageResponseFST
)
@Keep
data class FDCMessageResponseFST(
    @SerializedName("FDCdata")
    val fDCdata: FDCMessageFData,
)
@Keep
data class FDCMessageFData(
    @SerializedName("DeviceClass")
    val deviceClass: FDCMessageDeviceClassFST,
    @SerializedName("FDCTimeStamp")
    val fDCTimeStamp: String
)
@Keep
data class FDCMessageDeviceClassFST(
    @SerializedName("Amount")
    val amount: Double,
    @SerializedName("NozzleNo")
    val nozzleNo: Long,
    @SerializedName("ProductNo")
    val productNo: Int,
    @SerializedName("PumpNo")
    val pumpNo: Int,
    @SerializedName("TransactionSeqNo")
    val transactionSeqNo: String, //Changed Long to String
    @SerializedName("UnitPrice")
    val unitPrice: Double,
    @SerializedName("Volume")
    val volume: Double,
    @SerializedName("StartTimeStamp")
    val startTimeStamp: String?,
    @SerializedName("ReleaseToken")
    val releaseToken: String?,
    @SerializedName("FusionSaleId")
    val fusionSaleId: String?,
    @SerializedName("EndTimeStamp")
    val endTimeStamp: String?,
    @SerializedName("ProductName")
    val ProductName: String?,
    @SerializedName("ProductUM")
    val ProductUM: String?
)


