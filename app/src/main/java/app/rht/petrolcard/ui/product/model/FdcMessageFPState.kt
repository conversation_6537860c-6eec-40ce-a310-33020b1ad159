package app.rht.petrolcard.ui.product.model

import com.google.gson.annotations.SerializedName

import androidx.annotation.Keep

@Keep
data class FdcMessageFPState(
    @SerializedName("FDCMessage")
    val fDCMessage: FDCMessageFPS
)

@Keep
data class FDCMessageFPS(
    @SerializedName("FDCdata")
    val fDCdata: FDCdataFPS,
)

@Keep
data class FDCdataFPS(
    @SerializedName("DeviceClass")
    val deviceClass: DeviceClassFPS
)

@Keep
data class DeviceClassFPS(
    @SerializedName("DeviceState")
    val deviceState: String,
    @SerializedName("ErrorCode")
    val errorCode: String,
    @SerializedName("Nozzle")
    val nozzles: List<NozzleFPS>,
    @SerializedName("PumpNo")
    val pumpNo: Int
)

@Keep
data class NozzleFPS(
    @SerializedName("ErrorCode")
    val errorCode: String,
//    @SerializedName("LogicalNozzle")
//    val logicalNozzle: String,
//    @SerializedName("LogicalState")
//    val logicalState: String,
    @SerializedName("NozzleNo")
    val nozzleNo: String,
//    @SerializedName("TankLogicalState")
//    val tankLogicalState: String
)


//region second object
@Keep
data class FdcMessageFPState2(
    @SerializedName("FDCMessage")
    val fDCMessage: FDCMessageFPS2
)

@Keep
data class FDCMessageFPS2(
    @SerializedName("ApplicationSender")
    val applicationSender: String,
    @SerializedName("FDCdata")
    val fDCdata: FDCdataFPS2
//    @SerializedName("MessageID")
//    val messageID: String,
//    @SerializedName("MessageType")
//    val messageType: String,
//    @SerializedName("WorkstationID")
//    val workstationID: String,
//    @SerializedName("xmlns:xsd")
//    val xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    val xmlnsXsi: String
)

@Keep
data class FDCdataFPS2(
    @SerializedName("DeviceClass")
    val deviceClass: DeviceClassFPS2
//    @SerializedName("FDCTimeStamp")
//    val fDCTimeStamp: String
)

@Keep
data class DeviceClassFPS2(
    @SerializedName("DeviceID")
    val deviceID: String,
    @SerializedName("DeviceState")
    val deviceState: String,
    @SerializedName("ErrorCode")
    val errorCode: String,
    @SerializedName("LockingApplicationSender")
    val lockingApplicationSender: String,
    @SerializedName("Nozzle")
    val nozzle: NozzleFPS,
    @SerializedName("PumpNo")
    val pumpNo: String,
    @SerializedName("Type")
    val type: String
)
//endregion

//region third object
//@Keep
//data class FdcMessageFPState3(
//    @SerializedName("FDCMessage")
//    val fDCMessage: FDCMessageFPS3
//)

//@Keep
//data class FDCMessageFPS3(
//    @SerializedName("ApplicationSender")
//    var applicationSender: String,
////    @SerializedName("FDCdata")
////    var fDCdata: FDCdataFPS3,
//    @SerializedName("MessageID")
//    var messageID: String,
//    @SerializedName("MessageType")
//    var messageType: String,
//    @SerializedName("WorkstationID")
//    var workstationID: String,
//    @SerializedName("xmlns:xsd")
//    var xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    var xmlnsXsi: String
//)

//@Keep
//data class FDCdataFPS3(
//    @SerializedName("DeviceClass")
//    var deviceClass: DeviceClassFPS3,
//    @SerializedName("FDCTimeStamp")
//    var fDCTimeStamp: String?
//)

//@Keep
//data class DeviceClassFPS3(
//    @SerializedName("DeviceID")
//    val deviceID: String,
////    @SerializedName("DeviceState")
////    val deviceState: DeviceStateObj,
//    @SerializedName("ErrorCode")
//    val errorCode: String,
//    @SerializedName("LockingApplicationSender")
//    val lockingApplicationSender: String,
//    @SerializedName("Nozzle")
//    val nozzle: NozzleFPS,
//    @SerializedName("PumpNo")
//    val pumpNo: String,
//    @SerializedName("Type")
//    val type: String
//)

//@Keep
//data class DeviceStateObj(
//    @SerializedName("content")
//    var content: String,
//    @SerializedName("Stopped")
//    var stopped: Boolean
//)
//endregion