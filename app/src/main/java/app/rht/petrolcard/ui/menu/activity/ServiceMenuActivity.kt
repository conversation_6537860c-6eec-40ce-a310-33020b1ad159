package app.rht.petrolcard.ui.menu.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityServiceMenuBinding
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.TransactionStepLog
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.SubProduct
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.LogWriter
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow.OTHER_PRODUCTS
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import java.util.*

@Suppress("DEPRECATION")
class ServiceMenuActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<SubProduct> {
    private var TAG = ServiceMenuActivity::class.simpleName
    private lateinit var mBinding: ActivityServiceMenuBinding
    var logWriter: LogWriter? = null
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    var productModel:ArrayList<SubProduct> = ArrayList<SubProduct>()
    private lateinit var adapter : RecyclerViewArrayAdapter<SubProduct>
    var productList = ArrayList<ProductModel>()
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_service_menu)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.logReferenceNo  = ""
//        prefs.logReferenceNo  = Support.generateReference(this)
        setupToolbar()

    }

    override fun onStart() {
        super.onStart()
        initData()
        setupRecyclerview()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarServices.toolbar.tvTitle.text = getString(R.string.services)
        mBinding.toolbarServices.toolbar.setNavigationOnClickListener {
            mBinding.toolbarServices.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
        }
    }
    fun initData()
    {

        productList = getAllProductsList()

        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }


    }


    private fun isProductAvailable(selectedCode: String?): Boolean {
        var isAvailable = "false"
        for (mProduct in productList) {
            //log(TAG, "mProduct:::: " + Gson().toJson(mProduct))
            val productCode: String = mProduct.code!!
            val isProductAvailable_: String = mProduct.isAvailable!!
            if (selectedCode != null && productCode != null && selectedCode.contains(productCode)) {
                isAvailable = isProductAvailable_
            }
        }
        return isAvailable.equals("true", ignoreCase = true)
    }

    private fun setupRecyclerview() {
        productModel.clear()
        mBinding.mListView.setHasFixedSize(true)

        if(!prefs.getReferenceModel()!!.category_list.isNullOrEmpty())
        {
            for(categoryList in prefs.getReferenceModel()!!.category_list!!)
            {
                if(categoryList.category_id == PRODUCT.SERVICE_CATEGORY_ID)
                {
                    for(item in categoryList.sub_categories!!){
                        if(isProductAvailable(item.code))
                        productModel.add(item)
                    }
                    //productModel.addAll(categoryList.sub_categories!!)
                }
            }
        }
        if(productModel.isNullOrEmpty())
        {
            mBinding.emptyList.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
        else {
            mBinding.emptyList.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        adapter = RecyclerViewArrayAdapter(productModel,this)
        mBinding.mListView.layoutManager = GridLayoutManager(this, 2)
        mBinding.mListView.adapter = adapter
    }
    override fun setObserver() {

    }
    override fun onItemClick(view: View, model: SubProduct) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()

        if(model.isAvailable)
        {
            prefs.logReferenceNo  = ""
            prefs.logReferenceNo  = Support.generateReference(this)
            val trxLog = TransactionStepLog()
            trxLog.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Service - "+ model.id))
            intentExtrasModel!!.transactionStepLog = trxLog
            val intent = goDirection()
            val productModel = ProductModel(productID=model.id,fcc_prod_id=model.id,libelle=model.label,code=model.code,bit=0,categorie ="",isAvailable=model.isAvailable.toString(),color_code = model.color_code,icon = model.icon,PRODUCT.SERVICE_CATEGORY_ID,hs_code = model.hs_code)
            intentExtrasModel!!.selectedProduct=productModel
            intentExtrasModel!!.workFlowTransaction=OTHER_PRODUCTS
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            startActivityForResult(intent, 1)
        }
        else
        {
            showDialog(model.label+" - Restricted",getString(R.string.product_not_available))
        }

    }

    fun goDirection(): Intent {
        val i: Intent
        if(intentExtrasModel!!.loyaltyTrx)
        {
            i = Intent(this, CheckCardRestrictionsActivity::class.java)
            intentExtrasModel!!.typePay=AppConstant.CARD_VALUE
        }
        else
        {
           i = if (prefs.getStationModel()!!.mode_pompiste == "CODE") {
                Intent(this, AttendantCodeActivity::class.java)
            } else {
                Intent(this, AttendantTagActivity::class.java)
            }
        }
        return i
    }
    override fun onBackPressed() {

    }

}
