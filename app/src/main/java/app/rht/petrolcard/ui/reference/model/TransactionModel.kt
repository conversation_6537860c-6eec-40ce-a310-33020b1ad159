package app.rht.petrolcard.ui.reference.model

import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketResponse
import app.rht.petrolcard.ui.timssign.model.TIMSSignModel

class TransactionModel (
    var id: Int? = null,
    var idTerminal: Int? = null,
    var idProduit: Int? = 0,
    var codePompiste: String? = null,
    var dateTransaction: String? = null,
    var idTypeTransaction : Int? = 1, // =1 trx ; =2 ann trx ; =3 recharge ; =4 monthly recharge, 5=Update Mileage
    var amount: Double? = null,
    var quantite: Double? = 0.0,
    var unitPrice: Double? = 0.0,
    var reference: String? = null,
    var flagController: Int? = null,
    var sequenceController: String? = null,
    var tagNFC : String? = "", //vareur du code NFC ou RFID
    var kilometrage: String? = null, //vareur du code NFC ou RFID
    var pan: String? = "",
    var nomPorteur: String? = null,
    var dateExp: String? = null,
    var detailArticle: String? = "",
    var flagTelecollecte: Int? = 0,
    var modepay: String? = null,// Cash / card / mobile / sq code
    var panLoyalty : String? = null,
    var soldeCard: String? = "",
    var transactionStatus : Int? = null,
    var transactionSignature: String? = null,
    var pumpId : String? = null,
    var isSplitPayment : Boolean? = false,
    var discountAmount : String? = "",
    var isDiscountTransaction :Int? = 0,
    var bank_reference_num :String? = null,
    var preAuthAmount :String? = "0",
    var refundStatus :String? = "1",
    var idPompiste :String? = "0",
    var vatAmount :String? = "0.0",
    var netAmount :String? = null,
    var discountId :Int? = 0,
    var categoryId :Int? = 0,
    var transactionRefundExported :Int? = 0,
    var refundAmount :String? = null,
    var dailyCeiling :String? = null,
    var weeklyCeiling :String? = null,
    var monthlyCeiling :String? = null,
    var roundingAdjustment :String? = null,
    var remainingMonthlyCount:String? = null,
    var remainingDailyCount :String? = null,
    var remainingWeeklyCount :String? = null,
    var vatPercentage :Int? = 0,
    var vatType :Int? = 0,
    var fccProductId :String? =null,
    var fposCardId :String? = null,
    var fccSaleId :String? = null,
    var fccReleaseToken :String? = null,
    var discountPercentage :String? = "0",
    var isDisputedTrx :Int? = 0,  //0 = normal transaction //1 = disputedTransaction
    var vehicleNumber :String? = null,
    var productName :String? = null,
    var timsSignDetails: TIMSSignModel? = TIMSSignModel(),
    var authorizedManagerID: Int? = 0,
    var preAuthId: String? = "",
    var discountType: Int? = 0,
    var timsInvoiceStatus: Int? = 0,
    var qrCodeTicket: QRCodeTicketResponse? = null,
    var totalPaidAmount: Double? = 0.0,
    var isPumpError: Int? = 0,
    var transactionChecksum: String? = "",
    var creditStatus: Int? = 1,
    ) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Double::class.java.classLoader) as? Double,
        parcel.readValue(Double::class.java.classLoader) as? Double,
        parcel.readValue(Double::class.java.classLoader) as? Double,
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(TIMSSignModel::class.java.classLoader),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readParcelable(QRCodeTicketResponse::class.java.classLoader),
        parcel.readValue(Double::class.java.classLoader) as? Double,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString()
    ) {



    }

    fun getModePayment():String
    {
        val context = MainApp.appContext
        var modePayName = context.getString(R.string.cash)
        try {
            val referenceModel = MainApp.getPrefs().getReferenceModel()
            if(modepay != null) {
                if (referenceModel != null) {
                    val modePayList = referenceModel.mode_of_payment_list!!
                    for (mode in modePayList) {
                        if (modepay == "${mode.payment_id}") {
                            modePayName = mode.payment_name ?: context.getString(R.string.cash)
                            break
                        }
                    }
                }
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }

        return modePayName
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeValue(id)
        parcel.writeValue(idTerminal)
        parcel.writeValue(idProduit)
        parcel.writeString(codePompiste)
        parcel.writeString(dateTransaction)
        parcel.writeValue(idTypeTransaction)
        parcel.writeValue(amount)
        parcel.writeValue(quantite)
        parcel.writeValue(unitPrice)
        parcel.writeString(reference)
        parcel.writeValue(flagController)
        parcel.writeString(sequenceController)
        parcel.writeString(tagNFC)
        parcel.writeString(kilometrage)
        parcel.writeString(pan)
        parcel.writeString(nomPorteur)
        parcel.writeString(dateExp)
        parcel.writeString(detailArticle)
        parcel.writeValue(flagTelecollecte)
        parcel.writeString(modepay)
        parcel.writeString(panLoyalty)
        parcel.writeString(soldeCard)
        parcel.writeValue(transactionStatus)
        parcel.writeString(transactionSignature)
        parcel.writeString(pumpId)
        parcel.writeValue(isSplitPayment)
        parcel.writeString(discountAmount)
        parcel.writeValue(isDiscountTransaction)
        parcel.writeString(bank_reference_num)
        parcel.writeString(preAuthAmount)
        parcel.writeString(refundStatus)
        parcel.writeString(idPompiste)
        parcel.writeString(vatAmount)
        parcel.writeString(netAmount)
        parcel.writeValue(discountId)
        parcel.writeValue(categoryId)
        parcel.writeValue(transactionRefundExported)
        parcel.writeString(refundAmount)
        parcel.writeString(dailyCeiling)
        parcel.writeString(weeklyCeiling)
        parcel.writeString(monthlyCeiling)
        parcel.writeString(roundingAdjustment)
        parcel.writeString(remainingMonthlyCount)
        parcel.writeString(remainingDailyCount)
        parcel.writeString(remainingWeeklyCount)
        parcel.writeValue(vatPercentage)
        parcel.writeValue(vatType)
        parcel.writeString(fccProductId)
        parcel.writeString(fposCardId)
        parcel.writeString(fccSaleId)
        parcel.writeString(fccReleaseToken)
        parcel.writeString(discountPercentage)
        parcel.writeValue(isDisputedTrx)
        parcel.writeString(vehicleNumber)
        parcel.writeString(productName)
        parcel.writeParcelable(timsSignDetails, flags)
        parcel.writeValue(authorizedManagerID)
        parcel.writeString(preAuthId)
        parcel.writeValue(discountType)
        parcel.writeValue(timsInvoiceStatus)
        parcel.writeParcelable(qrCodeTicket, flags)
        parcel.writeValue(totalPaidAmount)
        parcel.writeValue(isPumpError)
        parcel.writeString(transactionChecksum)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TransactionModel> {
        override fun createFromParcel(parcel: Parcel): TransactionModel {
            return TransactionModel(parcel)
        }

        override fun newArray(size: Int): Array<TransactionModel?> {
            return arrayOfNulls(size)
        }
    }
}