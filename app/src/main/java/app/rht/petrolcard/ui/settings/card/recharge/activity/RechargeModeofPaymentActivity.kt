package app.rht.petrolcard.ui.settings.card.recharge.activity

import android.app.Activity
import android.app.AlertDialog
import android.app.Dialog
import android.content.*
import android.os.Bundle
import android.text.InputFilter
import android.text.InputFilter.LengthFilter
import android.text.InputType
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.TextView
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityModePayBinding
import app.rht.petrolcard.networkRequest.ApiClient
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.NotificationModel
import app.rht.petrolcard.ui.loyalty.activity.ICCLoyaltyActivity
import app.rht.petrolcard.ui.modepay.activity.BankPaymentProgressActivity
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.ui.modepay.model.MtnRequestToPayBody
import app.rht.petrolcard.ui.modepay.viewmodel.ModePayViewmodel
import app.rht.petrolcard.ui.reference.model.MtnPayCredentials
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.NotificationUtils
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.MOBILE_PAYMENT_REFERENCE
import app.rht.petrolcard.utils.constant.AppConstant.MOBILE_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.MTN_PAY_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.OFFLINE_TRX_MODE
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.mpesaservices.AccessToken
import app.rht.petrolcard.utils.mpesaservices.STKPush
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.android.synthetic.main.toolbar.view.*
import retrofit2.Call
import retrofit2.Callback
import retrofit2.Response
import ticker.views.com.ticker.widgets.circular.timer.callbacks.CircularViewCallback
import ticker.views.com.ticker.widgets.circular.timer.view.CircularView
import timber.log.Timber
import java.lang.ref.WeakReference
import java.util.*
import kotlin.math.roundToInt

class RechargeModeofPaymentActivity : BaseActivity<ModePayViewmodel>(ModePayViewmodel::class),RecyclerViewArrayAdapter.OnItemClickListener<ModePaymentModel> {
    private var TAG = RechargeModeofPaymentActivity::class.simpleName
    private lateinit var mBinding: ActivityModePayBinding
    private var mApiClient: ApiClient? = null
    var inputManager: InputMethodManager? = null
    private var mRegistrationBroadcastReceiver: BroadcastReceiver? = null
    private var stationMode = 0
    // M-PESA
    private var intentExtrasModel: IntentExtrasModel? = null
    var typePay: String? = null
    var dialog: AlertDialog? = null
    var isRegistered = false
    var circular_view: CircularView? = null
    var dialogMessage: TextView? = null
    private var adapter : RecyclerViewArrayAdapter<ModePaymentModel>? = null
    private var mList: ArrayList<ModePaymentModel> = ArrayList<ModePaymentModel>()
    var amount = ""
    // M-PESA
    var phoneNumber = ""
    var mtnPayAccessToken = ""
    var mtnReferenceID = ""
    var isResultDialogShowing = false
    val mTransaction = TransactionModel()
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_mode_pay)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        init()
    }

    private fun init() {
        intentExtrasModel =
            intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        mApiClient = ApiClient()
        mApiClient!!.setConsumerCredentials(
            prefs.getReferenceModel()!!.mpesa_credentials!!.consumer_key,
            prefs.getReferenceModel()!!.mpesa_credentials!!.consumer_secret_key
        )
        mApiClient!!.setIsDebug(true) //Set True to enable logging, false to disable.
        setupRecyclerview()
        initListener()
        setupToolbar()
        amount = if (intentExtrasModel!!.mTransaction != null) {
            intentExtrasModel!!.mTransaction!!.amount.toString()
        } else {
            intentExtrasModel!!.amount.toString()
        }
    }

    private fun setupToolbar()
    {
        mBinding.toolbarModepay.toolbar.tvTitle.text = getString(R.string.mode_of_payment)
        mBinding.toolbarModepay.toolbar.setNavigationOnClickListener {
            mBinding.toolbarModepay.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
        }
    }
    private fun setupRecyclerview() {
        mList.clear()
        if(prefs.getReferenceModel()!!.mode_of_payment_list != null)
        {
            mBinding.emptyList.visibility =View.GONE
            mBinding.mListView.visibility =View.VISIBLE
            val modeList= prefs.getReferenceModel()!!.mode_of_payment_list!!
            for(item in modeList)
            {
                var icon = 0
                var color = ""
                when (item.payment_id) {
                 /*   1 -> {
                        icon = R.drawable.ic_card
                        color = "#feebea"
                    }*/
                    2 -> {
                        icon = R.drawable.ic_cash
                        color = "#dff5f8"
                    }
                    3 -> {
                        icon = R.drawable.ic_mobile
                        color = "#f3eefd"
                    }
                    4 -> {
                        icon = R.drawable.ic_qr_code
                        color = "#fff2de"
                    }
                    5 -> {
                        icon = R.drawable.ic_rfid
                        color = "#edf8e2"
                    }
                    7 -> {
                        icon = R.drawable.ic_visa
                        color = "#CACFEC"
                    }
                    8 -> {
                        icon = R.drawable.ic_split_payment
                        color = "#F3D8F8"
                    }
                    13 -> {
                        icon = R.drawable.ic_mobile
                        color = "#f3eefd"
                    }
                    15 -> {
                        icon = R.drawable.oil_tank
                        color = "#33009432"
                    }
                    16 -> {
                        icon = R.drawable.gas_station_new
                        color = "#33F79F1F"
                    }
                    17 -> {
                        icon = R.drawable.test_payment
                        color = "#33ED4C67"
                    }
                    20 -> {
                        icon = R.drawable.ic_mobile
                        color = "#f3eefd"
                    }
                    else ->
                    {
                        icon = R.drawable.ic_cash
                        color = "#dff5f8"
                    }
                }

                try {
                    if(item.isAvailable!=null && item.isAvailable!!){
                        if(item.payment_id != 1 ) {
                            val mItem = item
                            mItem.payment_id = item.payment_id
                            mItem.payment_name = item.payment_name
                            mItem.color = color
                            mItem.icon = icon
                            mItem.order = item.order
                            mItem.image_url = ""
                            mList.add(mItem)
                        }
                    }
                } catch (e:Exception) {
                    e.printStackTrace()
                }

                continue
            }
        }
        else {
            mBinding.emptyList.visibility =View.VISIBLE
            mBinding.mListView.visibility =View.GONE
        }

        try{
            val refundItem: ModePaymentModel = mList.single { s -> s.payment_id == 9 }
            mList.remove(refundItem)
        } catch (e:Exception){
            e.printStackTrace()
        }

        mList = ArrayList(mList.sortedBy { it.order!! })
        adapter = RecyclerViewArrayAdapter(mList,this)
        mBinding.mListView.adapter = adapter
        adapter!!.setContext(this)
    }
    fun getAccessToken() {
        var url: String = AppConstant.BASE_URL_MPESA_TEST + AppConstant.MPESA_AUTHENTICATION_URL
        if (prefs.getReferenceModel()!!.mpesa_credentials!!.payment_mode != "test") {
            url = AppConstant.BASE_URL_MPESA_PRODUCTION + AppConstant.MPESA_AUTHENTICATION_URL
        }
        mApiClient!!.setGetAccessToken(true)
        val ctx = WeakReference(this).get()!!
        mApiClient!!.mpesaService(ctx).getAccessToken(url)
            .enqueue(object : Callback<AccessToken> {
                override fun onResponse(call: Call<AccessToken>, response: Response<AccessToken>) {
                    if (response != null && response.isSuccessful) {
                        mApiClient!!.setAuthToken(response.body()!!.accessToken)
                        typePay = AppConstant.MOBILE_VALUE
                        intentExtrasModel!!.typePay = typePay
                        if (prefs.getReferenceModel()!!.mpesa_credentials == null) {
                            showDialog(
                                getString(R.string.mobile_payment_not_allowed),
                                getString(R.string.setup_mpesa_credentials)
                            )
                        } else {
                            showCheckoutDialog()
                        }
                    }
                    else
                    {
                        showDialog(getString(R.string.invalid_consumer_details),response.message())
                    }
                }

                override fun onFailure(call: Call<AccessToken>, t: Throwable) {
                    showDialog(getString(R.string.invalid_consumer_details),t.message)
                }
            })
    }

    private fun initListener() {
        inputManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)

        mRegistrationBroadcastReceiver = object : BroadcastReceiver() {
            override fun onReceive(context: Context, intent: Intent) {
                log("TAG", "##### onReceive push ##### ")
                if (intent.action == AppConstant.REGISTRATION_COMPLETE) {
                    FirebaseMessaging.getInstance().subscribeToTopic(AppConstant.TOPIC_GLOBAL)
                    getFirebaseRegId()
                    log(TAG, "### REGISTRATION_COMPLETE #### ")
                } else if (intent.action == AppConstant.PUSH_NOTIFICATION) {
                    val message: NotificationModel = intent.getParcelableExtra("message")!!
                    try {
                        NotificationUtils.createNotification(
                            applicationContext,
                            message.title,
                            message.message
                        )
                        if (circular_view != null) circular_view!!.stopTimer()
                        if (dialog!!.isShowing) dialog!!.dismiss()
                        showResultDialog(message,true)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 1) {
            if (resultCode == RESULT_OK) {
                intentExtrasModel = intent.getSerializableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
            }
        }
    }

    fun showResultDialog(result: NotificationModel,isFail:Boolean) {
        if(!isResultDialogShowing) {
            isResultDialogShowing = true
            val layout: Int = if (result.code != null && result.code == "0") {
                R.layout.dialog_success_message
            } else {
                R.layout.dialog_failed_message
            }
            val ctx = WeakReference(this).get()!!
            val dialogR =
                androidx.appcompat.app.AlertDialog.Builder(ctx, R.style.MyStyleDialog).show()
            dialogR.setContentView(layout)
            dialogR.findViewById<TextView>(R.id.title)!!.text = result.title
            dialogR.findViewById<TextView>(R.id.message)!!.text = result.message
            dialogR.findViewById<TextView>(R.id.action_done)!!.setOnClickListener {
                isResultDialogShowing = false
                dialogR.dismiss()
                setBeep()
                if (!isFail) {
                    if (result.code != null && result.code == "0") {
                        intentExtrasModel!!.panNumber = phoneNumber
                        intentExtrasModel!!.mTransaction!!.pan = phoneNumber
                        typePay = MOBILE_VALUE
                        intentExtrasModel!!.typePay = typePay
                        gotoRechargeActivity()
                    }
                }
            }
            val lp = WindowManager.LayoutParams()
            val window = dialogR.window
            lp.copyFrom(window!!.attributes)
            // This makes the dialog take up the full width
            lp.width = WindowManager.LayoutParams.WRAP_CONTENT
            lp.height = WindowManager.LayoutParams.WRAP_CONTENT
            window.setFlags(
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
                WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
            )
            window.attributes = lp
            window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
            dialogR.setCancelable(false)
            dialogR.setCanceledOnTouchOutside(false)
        }
    }
    fun showCheckoutDialog() {
        var dialog: AlertDialog? = null
        val builder = AlertDialog.Builder(this@RechargeModeofPaymentActivity, R.style.MyStyleDialog)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_mobilenumber, null)
        builder.setView(layout)
        val enterNumber: AppCompatEditText = layout.findViewById(R.id.enterNumber)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val msgTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)
        if (BuildConfig.DEBUG) {
            enterNumber.setText("254708374149")
        }
        if(typePay == AppConstant.MTN_PAY_VALUE) {
            if (BuildConfig.DEBUG) {
                enterNumber.setText("250791583185")
            }
        }
        cancelButton.setOnClickListener { v: View? ->  setBeep()
            dialog!!.dismiss() }
        submitButton.setOnClickListener { v: View? ->
            when {
                enterNumber.text.toString().isEmpty() -> {
                    enterNumber.error = getString(R.string.enter_mobile_number)
                }
                else -> {
                    dialog!!.dismiss()
                    phoneNumber = enterNumber.text.toString()
                    if(typePay == AppConstant.MTN_PAY_VALUE)
                    {
                        showProgressBar()
                        mViewModel.getMtnPayAccessToken()
                    }
                    else
                    {
                        performSTKPush(enterNumber.text.toString())
                    }

                }

            }
        }
        dialog = builder.create()
        dialog!!.show()
    }
    fun performSTKPush(phone_number: String?) {
        showProgressBar()
        var roundOffAmount = "0"
        if (amount != null) {
            roundOffAmount = amount.toDouble().roundToInt().toString()
        }
        var url: String = AppConstant.BASE_URL_MPESA_TEST + AppConstant.MPESA_PROCESSREQUEST_URL
        if (prefs.getReferenceModel()!!.mpesa_credentials!!.payment_mode != "test") {
            url = AppConstant.BASE_URL_MPESA_PRODUCTION + AppConstant.MPESA_PROCESSREQUEST_URL
        }
        val timestamp: String = Utils.getTimestamp()
        val stkPush = STKPush(
            prefs.getReferenceModel()!!.mpesa_credentials!!.business_short_code,
            Utils.getPassword(
                prefs.getReferenceModel()!!.mpesa_credentials!!.business_short_code,
                prefs.getReferenceModel()!!.mpesa_credentials!!.passkey,
                timestamp
            ),
            timestamp,
            prefs.getReferenceModel()!!.mpesa_credentials!!.transaction_type,
            roundOffAmount,
            Utils.sanitizePhoneNumber(phone_number).toString() + "",
            prefs.getReferenceModel()!!.mpesa_credentials!!.partyb.toString() + "",
            Utils.sanitizePhoneNumber(phone_number).toString() + "",
            prefs.getReferenceModel()!!.mpesa_credentials!!.call_back_url.toString() + prefs.fireBaseToken,
            prefs.getReferenceModel()!!.mpesa_credentials!!.payment_mode,  //The account reference
            "Payment Request from " + prefs.getPreferenceModel()!!.stationTitle
        )
        mApiClient!!.setGetAccessToken(false)
        val ctx = WeakReference(this).get()!!
        mApiClient!!.mpesaService(ctx).sendPush(url, stkPush)
            .enqueue(object : Callback<STKPush?> {
                override fun onResponse(call: Call<STKPush?>, response: Response<STKPush?>) {
                    try {
                        if (response != null && response.isSuccessful) {
                            Timber.d("post submitted to API. %s", response.body())
                            if (dialogMessage != null) dialogMessage!!.text =
                                getString(R.string.please_wait_request_processing)
                        } else {
                            Timber.e("Response %s", response.errorBody()!!.string())
                            circular_view!!.stopTimer()
                            dialog!!.dismiss()
                            showDialog(getString(R.string.invalid_request),response.message())
                        }

                    } catch (e: Exception) {
                        e.printStackTrace()
                        circular_view!!.stopTimer()
                        dialog!!.dismiss()
                        showDialog(getString(R.string.invalid_request),e.message)
                    }
                }

                override fun onFailure(call: Call<STKPush?>, t: Throwable) {
                    circular_view!!.stopTimer()
                    dialog!!.dismiss()
                    Timber.e(t)
                    showDialog(getString(R.string.invalid_request),t.message)
                }
            })
    }
    private fun showProgressBar() {
        val ctx = WeakReference(this).get()!!
        val builder = AlertDialog.Builder(ctx)
        builder.setTitle("")
        builder.setCancelable(false)
        val customLayout: View = layoutInflater.inflate(R.layout.dialog_progress_bar, null)
        builder.setView(customLayout)
        circular_view = customLayout.findViewById(R.id.circular_view)
        dialogMessage = customLayout.findViewById<TextView>(R.id.dialogMessage)
        circular_view!!.startTimer()
        var timer = 119
        if(typePay == MTN_PAY_VALUE)
        {
            timer = prefs.getMtnPayCredentials()!!.get_api_timeout
        }
        val builderWithTimer: CircularView.OptionsBuilder = CircularView.OptionsBuilder()
            .shouldDisplayText(true)
            .setCounterInSeconds(timer.toLong())
            .setCircularViewCallback(object : CircularViewCallback {
                override fun onTimerFinish() {

                    if(typePay == MTN_PAY_VALUE)
                    {
                        if(!mtnPayAccessToken.isNullOrEmpty())
                        {
                            mViewModel.getMTNPayResponseStatus(mtnReferenceID,mtnPayAccessToken)
                        }
                        else
                        {
                            circular_view!!.stopTimer()
                            dialog!!.dismiss()
                            showDialog(getString(R.string.invalid_consumer_details), getString(R.string.contact_rising_hightech))
                        }
                    }
                    else
                    {
                        circular_view!!.stopTimer()
                        dialog!!.dismiss()
                        showDialog(getString(R.string.not_accepted),getString(R.string.push_not_recvd))
                    }

                }

                override fun onTimerCancelled() {}
            })
        circular_view!!.setOptions(builderWithTimer)
        dialog = builder.create()
        dialog!!.show()
    }

    override fun onPause() {
        if (isRegistered) {
            val ctx = WeakReference(this).get()!!
            LocalBroadcastManager.getInstance(ctx)
                .unregisterReceiver(mRegistrationBroadcastReceiver!!)
            //isRegistered = false;
        }
        super.onPause()
    }
    override fun onResume() {
        super.onResume()

        // register GCM registration complete receiver
        LocalBroadcastManager.getInstance(this).registerReceiver(
            mRegistrationBroadcastReceiver!!,
            IntentFilter(AppConstant.REGISTRATION_COMPLETE)
        )
        LocalBroadcastManager.getInstance(this).registerReceiver(
            mRegistrationBroadcastReceiver!!,
            IntentFilter(AppConstant.PUSH_NOTIFICATION)
        )
        NotificationUtils.clearNotifications(applicationContext)
        isRegistered = true
    }
    override fun onStop() {
        if (isRegistered) {
            try {
                unregisterReceiver(mRegistrationBroadcastReceiver)
            } catch (e: Exception) {
                log(TAG, "mRegistrationBroadcastReceiver already unregistered")
            }
        }
        super.onStop()
    }

    private fun gotoRechargeActivity()
    {
        val i = Intent(this, RechargeCardActivity::class.java)
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(i)
        finish()
    }
    private fun gotoMobilePaymentFlow()
    {
        mApiClient = ApiClient()
        mApiClient!!.setConsumerCredentials(prefs.getReferenceModel()!!.mpesa_credentials!!.consumer_key, prefs.getReferenceModel()!!.mpesa_credentials!!.consumer_secret_key)
        mApiClient!!.setIsDebug(true) //Set True to enable logging, false to disable.
        getAccessToken()
    }
    private fun gotoQRPaymentFlow()
    {
        val i: Intent
        typePay = AppConstant.QRCODE_VALUE
        if (intentExtrasModel!!.mTransaction != null) {
            i = Intent(this, ICCLoyaltyActivity::class.java)
            intentExtrasModel!!.typePay = typePay
            startActivityForResult(i, 1)
        }
    }
    override fun onItemClick(view: View, model: ModePaymentModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Payment - "+model.payment_name))
        if(view.id == R.id.modePayLayout)
        {
            typePay = model.payment_id.toString()
            intentExtrasModel!!.typePay = model.payment_id.toString()
            when (model.payment_id) {
                2,15,16,17 -> {
                    typePay = "${model.payment_id}"
                    intentExtrasModel!!.typePay = typePay
                    gotoRechargeActivity()
                }
                3 -> {
                    gotoMobilePaymentFlow()
                }
                4 -> {
                    gotoQRPaymentFlow()
                }
                7-> {
                    if (model.payment_id.toString() == AppConstant.VISA_VALUE && !isPackageInstalled("com.ebe.edc.nbe")) {
                        showDialog(getString(R.string.bank_payment_application_not_found), getString(R.string.please_install_bank_app))
                    }
                    else
                    {
                        gotoBankActivity()
                    }
                }
                13 -> {
                    gotoMtnPayFlow()
                }
                else -> {
                    if(model.referenceNoSize!! > 0) {
                        showReferenceNumberDialog(model)
                    }
                    else
                    {
                        typePay = "${model.payment_id}"
                        intentExtrasModel!!.typePay = typePay
                        gotoRechargeActivity()
                    }
                }
            }
        }
    }
    private fun gotoMtnPayFlow() {
        if (prefs.getReferenceModel()!!.mtn_pay_credentials == null) {
            showDialog(
                getString(R.string.invalid_consumer_details),
                getString(R.string.contact_rising_hightech)
            )
        } else {
            showCheckoutDialog()
        }
    }

    fun gotoBankActivity()
    {
        intentExtrasModel!!.bankRequestType = AppConstant.SALE_REQUEST
        val intent = Intent(this, BankPaymentProgressActivity::class.java)
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
        finish()
    }

    private fun showReferenceNumberDialog(model: ModePaymentModel) {
        try {
            val ctx = WeakReference(this).get()!!
            val inputLength = model.referenceNoSize?:20
            val builder = AlertDialog.Builder(ctx)
            builder.setTitle("")
            builder.setCancelable(false)
            val layout: View = layoutInflater.inflate(R.layout.dialog_single_input, null)
            builder.setView(layout)
            val inputBox: AppCompatEditText = layout.findViewById(R.id.inputBox)
            inputBox.filters = arrayOf<InputFilter>(LengthFilter(inputLength))
            val mainTitle: AppCompatTextView = layout.findViewById(R.id.title)
            val subTitle: AppCompatTextView = layout.findViewById(R.id.subTitle)
            val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
            val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
            mainTitle.text = model.payment_name //getString(R.string.visa_payment)
            var subTitleTxt =""
            subTitleTxt = if(model.payment_name!!.contains(getString(R.string.number))) {
                "${getString(R.string.enter)} ${model.payment_name} "
            } else {
                "${getString(R.string.enter)} ${model.payment_name} ${getString(R.string.number)}"
            }
            subTitle.text = subTitleTxt
            inputBox.inputType = InputType.TYPE_CLASS_TEXT
            if(model.payment_id.toString() == MOBILE_PAYMENT_REFERENCE)
            {
                inputBox.inputType = InputType.TYPE_CLASS_NUMBER
            }
            dialog = builder.create()
            cancelButton.setOnClickListener {
                setBeep()
                dialog!!.dismiss()
            }
            submitButton.setOnClickListener {
                setBeep()
                dialog!!.dismiss()
                if (inputBox.text.toString().isNotEmpty() && inputBox.length() <= inputLength) {
                    val refNo = inputBox.text.toString()
                    if(intentExtrasModel!!.mTransaction!=null) {
                        intentExtrasModel!!.mTransaction!!.bank_reference_num = refNo

                        if(model.payment_id.toString() == MOBILE_PAYMENT_REFERENCE)
                        {
                            intentExtrasModel!!.mTransaction!!.pan = refNo
                        }
                    }

                    typePay = "${model.payment_id}"
                    intentExtrasModel!!.typePay = "${model.payment_id}"
                    //intentExtrasModel!!.panNumber = refNo
                    gotoRechargeActivity()
                } else {
                    inputBox.error = getString(R.string.reference_number_should_be_less_than)+" $inputLength" //getString(R.string.enter_last_3_digits)
                }
            }
            dialog!!.show()
        } catch (e:Exception) {
            //e.printStackTrace()
            log(TAG,"Window leaked in $TAG showReferenceNumberDialog()")
        }
    }
    override fun onBackPressed() {

    }
    override fun setObserver() {
        val ctx = WeakReference(this).get()!!
        mViewModel.mtnAccessTokenObserver.observe(ctx) {
            if(it?.access_token != null)
            {
                mtnPayAccessToken = it.access_token!!
                mViewModel.getMtnReferenceID()
            }
            else
            {
                showDialog(getString(R.string.error),it.error!!)
            }
        }
        mViewModel.mtnReferenceIdObserver.observe(ctx) {
            if(it != null)
            {
                mtnReferenceID = it
                val mtnRequest = MtnRequestToPayBody()
                var roundOffAmount = "0"
                if (amount != null) {
                    roundOffAmount = amount.toDouble().roundToInt().toString()
                }
                mtnRequest.amount = roundOffAmount
                mtnRequest.currency = prefs.currency.replace(" ","")
                if(intentExtrasModel!!.refrenceID.isNullOrEmpty())
                {
                    intentExtrasModel!!.refrenceID = Support.generateReference(ctx)
                }
                mtnRequest.externalId = intentExtrasModel!!.refrenceID!!
                mtnRequest.payer.partyIdType = prefs.getMtnPayCredentials()!!.party_id_type
                mtnRequest.payer.partyId = phoneNumber
                mtnRequest.payerMessage = prefs.getMtnPayCredentials()!!.payer_message+prefs.getReferenceModel()!!.terminal!!.stationName
                mtnRequest.payeeNote = prefs.getMtnPayCredentials()!!.payer_message+prefs.getReferenceModel()!!.terminal!!.stationName
                mViewModel.mtnRequestToPay(mtnReferenceID,mtnPayAccessToken,mtnRequest)
            }

        }
        mViewModel.mtnRequestToPayObserver.observe(ctx) {
            if (dialogMessage != null) dialogMessage!!.text =
                getString(R.string.please_wait_request_processing)
        }
        mViewModel.mtngetRequestStatusObserver.observe(ctx) {
            if (circular_view != null) circular_view!!.stopTimer()
            if (dialog!!.isShowing) dialog!!.dismiss()
            when {
                it.status.contains("SUCCESSFUL") -> {
                    mTransaction.bank_reference_num=it.financialTransactionId
                    if(stationMode == OFFLINE_TRX_MODE) {
                        mTransaction.transactionStatus=1
                    }
                    updateTransactionByReferenceId(mTransaction)
                    showResultDialog(NotificationModel(code = "0",title = it.status,message = "Transaction ID - "+ it.financialTransactionId),false)
                }
                it.status.contains("PENDING") -> {
                    showErrorDialog(it.status,getString(R.string.transaction_is_pending))
                }
                else -> {
                    showDialog(it.status,it.reason)
                }
            }
        }
    }
    fun saveMtnPayStatic()
    {
        val mtnPayBaseURL = "https://mtndeveloperapi.portal.mtn.co.rw/collection/"
        val subscriptionKey = "acb8df0586cc4b0bacd8a8863d46181b"
        val mtnReferenceIDURL = "https://www.uuidgenerator.net/api/version4"
        val  model= MtnPayCredentials(api_key = "cf262aaf-4857-4876-b368-1bac3274f8d7",
            api_url = mtnPayBaseURL,api_user = "4433d5a19f9444e4882dd502acb2ea09",call_back_url ="",
            party_id_type ="MSISDN",payee_note="",payer_message="Payment Request from ",reference_id_url = mtnReferenceIDURL,
            subscription_key = subscriptionKey,target_environment="mtnrwanda"
        )
        prefs.saveMtnPayCredentials(model)
    }

    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        log(TAG, "ERROR CODE:: $errorData")
        if(errorData.statusCode == 400)
        {
            showResultDialog(NotificationModel(message = getString(R.string.error)+" "+errorData.statusCode.toString(),code = getString(R.string.invalid_consumer_details)),true)
        }
        else
        {
            showResultDialog((NotificationModel(message = errorData.message,code = errorData.statusCode.toString())), true)
        }

        if (circular_view != null) circular_view!!.stopTimer()
        if (dialog!!.isShowing) dialog!!.dismiss()
    }
    fun showErrorDialog(title:String,msg: String?) {
        if(!(this as Activity).isFinishing)
        {
            val ctx = WeakReference(this).get()!!
            val dialog = Dialog(ctx)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialog.setCancelable(false)
            dialog.setContentView(R.layout.dialog_fail_retry)
            dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
            val tvTitle = dialog.findViewById<TextView>(R.id.title)
            val tvMessage = dialog.findViewById<TextView>(R.id.message)
            val dialogButton = dialog.findViewById<TextView>(R.id.action_done)
            val btnRetry = dialog.findViewById<TextView>(R.id.btnRetry)
            tvTitle.text = title
            tvMessage.text = msg

            dialogButton.setOnClickListener {
                dialog.dismiss()
                setBeep()
            }
            btnRetry.setOnClickListener {
                dialog.dismiss()
                setBeep()
                showProgressBar()
            }
            dialog.show()
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        adapter = null
    }
}
