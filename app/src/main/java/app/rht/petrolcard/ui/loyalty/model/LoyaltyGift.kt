package app.rht.petrolcard.ui.loyalty.model

import android.graphics.Color
import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.baseClasses.model.BaseModel
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep
@Keep
data class LoyaltyGift(
    @SerializedName("id") @Expose
    var id: Int? = null,
    @SerializedName("name")
    @Expose
    var name: String? = null,

    @SerializedName("description")
    @Expose
    var description: String? = null,

    @SerializedName("picture")
    @Expose
    var picture: String? = null,

    @SerializedName("point")
    @Expose
    var point: Int? = null,

    @SerializedName("actif")
    @Expose
    var actif: Int? = null,
) : BaseModel() {
    fun getColor(): Int {
        return Color.parseColor("#ffffff")
    }
}