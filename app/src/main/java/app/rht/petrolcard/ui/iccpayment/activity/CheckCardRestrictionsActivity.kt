package app.rht.petrolcard.ui.iccpayment.activity

import android.app.Activity
import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.*
import android.view.View
import android.view.Window
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.observe
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityCardRestrictionsBinding
import app.rht.petrolcard.ui.cardpincode.activity.VerifyPinActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel
import app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.SplitPaymentActivity
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import com.pax.dal.IDAL
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libkeymanagerbinder.Key
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.paxutils.icc.IccTester
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import java.util.*
import kotlin.Exception


class CheckCardRestrictionsActivity : BaseActivity<PaymentViewModel>(PaymentViewModel::class) {
    private var TAG= "CheckCardRestrictionsActivity"
    var dal: IDAL? = null
    var ret = -1
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""
    var authKey:String? = ""
    var cardType:Int? = 0
    var cardModel: CardStaticStructureModel?=null
    private var isFirstUse = false
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode = 0
    var isOnlineOffline = false
    var carHolderName = ""
    private var mToday: Date? = null
    private var cardCeiling: String? = null //plafondCarte
    private var preCardCeiling: String? = null //plafondCarte
    var cardCountDetails:String?=null
    var dateLastPLF:String?=null
    private var terminalDateErr = false
    private lateinit var mBinding: ActivityCardRestrictionsBinding
    private var cardRestrictions: String? = null//restrictionCarte
    private var mRestScheduleCard = 0 // mRestHoraireCard
    private var mRestArticleCard = 0 //
    private var mRestStationCard = 0
    private var mRestSectorCard = 0 //mRestSecteurCard
    private var mRestHolidaysCard = 0
    private var isHourRestricted = false // ismRestHoraireCard
    private var isArticleRestricted = false //ismRestArticleCard
    private var isStationRestricted = false //ismRestStationCard
    private var isSectorRestricted = false //ismRestSecteurCard
    private var isHolidayRestricted = false //ismRestHolidaysCard
    private var isCardExpired = false
    private var isCardBlocked = false
    private var errorMessage = ""
    private var errorTitle = "Error"
    private var isGreyListAvailable = false
    var isLitreUnit= false
    var remainingCardCeilings = ""
    var isBackPressed = false
    private var isSameMonth = false
    private var isNextDayPLF = false
    private lateinit var referenceModel : ReferenceModel
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_card_restrictions)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getInitIntentExtras()
        val readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
        setupToolbar()

    }
    private fun setupToolbar()
    {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.card_verification)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
            isBackPressed =true
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    if (mBankCard != null && mCore != null) {
                        mBankCard!!.breakOffCommand()
                        val ret = mBankCard!!.openCloseCardReader(BankCard.CARD_MODE_PICC, 0x02)
                    }
                }
                if(BuildConfig.POS_TYPE == "PAX")
                {
                    IccTester.getInstance().light(false)
                }
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))

            } catch (e: RemoteException) {
                e.printStackTrace()
               log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
               // mViewModel.generateLogs(e.message!!,0)
            }
        }
    }
    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

           if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        log(TAG,"WorkFLOW:: "+intentExtrasModel!!.workFlowTransaction!!)
        (stationMode == 2 || stationMode == 3).also { isOnlineOffline = it }
        referenceModel = prefs.getReferenceModel()!!
    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@CheckCardRestrictionsActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@CheckCardRestrictionsActivity) //icCpuReader Not Required for BTPE
                    log(TAG, "infoCarte::: $infoCarte")
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19)
                    else {
                        errorMessage = getString(R.string.card_error_9)
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Card Information Null"))
                        return -1
                    } //Abort Transaction

                    getCardStaticStructure()
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    cardRemoveChecker.start()
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@CheckCardRestrictionsActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@CheckCardRestrictionsActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Inject key verification Success - $panNumber"))
                        log(TAG, "inject Key Success::: $authKey")
                        val cardRestriction: Boolean = checkCardRestrictions()
                        if (!cardRestriction) {
                            return if (isFirstUse)
                                -2 //Update Card
                            else -1  //Abort Transaction
                        }
                        if (intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_UPDATE_MILEAGE) {
                            return 1
                        } else {
                            checkCardUpdateAvailable()
                            if (mToday != null && dateLastPLF != null && !dateLastPLF!!.contains("FFFF") && UtilsDate.isAfterToday(
                                    mToday,
                                    dateLastPLF
                                )
                            ) {
                                terminalDateErr = true
                                errorTitle = getString(R.string.unknown_card)
                                errorMessage = getString(R.string.card_error_1)
                                intentExtrasModel!!.transactionStepLog!!.actions!!.add(
                                    Action(
                                        action = "TRX${prefs.logReferenceNo} - ${
                                            getString(
                                                R.string.card_error_1
                                            )
                                        }"
                                    )
                                )
                                return -1
                            }
                            if (intentExtrasModel!!.isCardUpdate!!) {
                                return 1
                            } else {
                                val cardFBSRestriction =
                                    checkCardFBSRestriction(this@CheckCardRestrictionsActivity)
                                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - cardFBSRestriction - $cardFBSRestriction"))
                                return if (!cardFBSRestriction) {
                                    if (isGreyListAvailable) {
                                        -2 //Update card
                                    } else {
                                        -4 //FBS Card Restriction Error Abort

                                    }
                                } else {
                                    1
                                }
                            }
                        }
                    }
                        else
                        {

                            errorTitle = getString(R.string.invalid_card)
                            errorMessage = getString(R.string.card_error_10)
                            return -1
                        }

                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Failed to read inject key"))
                errorMessage = getString(R.string.card_error_11)
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
              //  mViewModel.generateLogs(e.message!!,0)
                return -1
            }
            return 0
        }
        override fun onPreExecute() {

        }
        override fun onPostExecute(result: Int?) {
            
            if(isBackPressed)
            {
                finish()
            }
            else {
                when (result) {

                    -1 -> {
                        UtilsCardInfo.beep(mCore, 10)
                        if(errorMessage.isEmpty())
                        {
                            errorMessage = getString(R.string.transaction_cancelled)
                        }
                        if (terminalDateErr) {
                            errorTitle = resources.getString(R.string.TPE_DATE_ERROR_SYNCH)
                            errorMessage = resources.getString(R.string.TPE_DATE_ERROR)
                        }
                        if (cardType == AppConstant.LOYALTY_CARD && !intentExtrasModel!!.loyaltyTrx) {
                            errorMessage = if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_UPDATE) {
                                    getString(R.string.loyalty_card_is_already_updated)
                                } else {
                                    getString(R.string.card_error_3)
                                }
                            errorTitle = getString(R.string.invalid_card)

                        }
                        showErrorDialog(errorTitle, errorMessage)
                      //  mViewModel.generateLogs(errorMessage,1)
                    }
                    -2 -> {
                        if (isFirstUse) {
                            showSnakeBar(resources.getString(R.string.card_not_updated))
                        }
                        intentExtrasModel!!.isCardUpdate = true
                        gotoVerifyPinActivity()
                    }
                    -4 -> {
                        showErrorDialog(errorTitle, errorMessage)
                       // mViewModel.generateLogs(errorMessage,1)
                    }
                    1 -> {
                        if (checkGreyList(this@CheckCardRestrictionsActivity, panNumber)) {
                            intentExtrasModel!!.isCardUpdate = true
                        }
                        gotoVerifyPinActivity()
                    }
                    else -> {
                        val msg = resources.getString(R.string.read_card_failed)
                        val errorTitle = resources.getString(R.string.error)
                        showErrorDialog(errorTitle, msg)
                       // mViewModel.generateLogs(errorMessage,1)
                    }
                }
            }
        }
        override fun onProgressUpdate(vararg values: IntArray) {
           mBinding.msgText.text = getString(R.string.card_inserted_success)
            mBinding.insertLayout.visibility = View.GONE
            mBinding.progressBar.visibility = View.VISIBLE

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }

    fun checkCardRestrictions():Boolean
    {
        return if(isFirstUse) {
            intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Is First Use - false"))
            false
        }
        else if (cardType != null && cardType == AppConstant.LOYALTY_CARD  && (!intentExtrasModel!!.loyaltyTrx || cardType != AppConstant.LOYALTY_CARD)  && intentExtrasModel!!.loyaltyTrx ) {
        //else if (cardType != null && cardType == AppConstant.LOYALTY_CARD  && !intentExtrasModel!!.loyaltyTrx || cardType != AppConstant.LOYALTY_CARD  && intentExtrasModel!!.loyaltyTrx ) {
          errorTitle = getString(R.string.invalid_card)
            errorMessage = getString(R.string.card_error_3)
            false
        } else if (BuildConfig.POS_TYPE  != "B_TPE" || isCardDetected()) {
            getCardRestrictionsInfo()
        } else {
            intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action =  "TRX${prefs.logReferenceNo} -${getString(R.string.card_error_2)}"))
            errorMessage = getString(R.string.card_error_2)
            false
        }
    }
    fun checkCardUpdateAvailable() {
         if (cardType != null && cardType == AppConstant.PREPAID_CARD || cardType == AppConstant.POSTPAID_CARD) {
            if (cardType == AppConstant.POSTPAID_CARD) {
                val infoCreditPostPayee = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "06", "32", this).replace("F", "0")
                val lastDateCreditPost = UtilsCardInfo.getDateLastPLF(infoCreditPostPayee)
                val isSameMonthCredit = UtilsDate.isSameMonth(mToday, lastDateCreditPost)
                if ((lastDateCreditPost != null && lastDateCreditPost.isNotEmpty() && lastDateCreditPost.contains("0000")) || !isSameMonthCredit) {
                   intentExtrasModel!!.isCardUpdate = true
                }
//                if(BuildConfig.DEBUG)
//                {
//                    intentExtrasModel!!.isCardUpdate = true //Added this line for testing purpose
//                }

            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                if (isLitreUnit) dateLastPLF =
                    UtilsCardInfo.getDateLastPLF(remainingCardCeilings)
                else
                    dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings)
            }
            if (dateLastPLF != null && !dateLastPLF!!.contains("FFFF")) {
                isSameMonth = UtilsDate.isSameMonth(mToday, dateLastPLF)
            }
            if (!isSameMonth) {
                intentExtrasModel!!.isCardUpdate = true
            }
        }
    }
    fun getCardStaticStructure()
    {
            if (infoCarte != null && panNumber!!.isNotEmpty()) {
                cardModel = UtilsCardInfo.readCardStaticStructureInfo(mBankCard, icCpuReader, this)
                val jsonData = (gson.toJson(cardModel))
                "Card Static Structure : $jsonData".also { log(TAG, it) }
                cardType = cardModel!!.cardType.toInt()
                intentExtrasModel!!.verificationType = cardModel!!.verificationType.toInt()
                if (cardModel!!.cardStatus == "F") {
                    isFirstUse = true
                }
        }
    }
    fun isCardDetected(): Boolean {
        var whileCondition = false
        try {
            whileCondition = BankCard.CARD_DETECT_EXIST == mBankCard!!.iccDetect()
        } catch (e: RemoteException) {
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
           // mViewModel.generateLogs(e.message!!,0)
        }
        return whileCondition
    } // eof detect

    fun getCardRestrictionsInfo():Boolean {
        try {
        carHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this)
        mToday = Support.getDateComparison(Date())
        cardRestrictions = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "03", "28", this)
        mRestStationCard = UtilsCardInfo.getRestStationCard(cardRestrictions!!.replace("F", "0"))
        mRestScheduleCard = UtilsCardInfo.getHourRestriction(cardRestrictions!!.replace("F", "0"))
        mRestHolidaysCard = UtilsCardInfo.getHolidayRestriction(cardRestrictions!!.replace("F", "0"))
        mRestSectorCard = UtilsCardInfo.getSectorRestriction(cardRestrictions!!.replace("F", "0"))
        mRestArticleCard = UtilsCardInfo.getArticalRestriction(cardRestrictions!!.replace("F", "0"))
        isCardExpired = UtilsDate.isCardExpired(mToday, cardModel!!.expiredDate)
        isCardBlocked = UtilsCardInfo.getStatusCard(infoCarte!!).equals("0") || UtilsCardInfo.getStatusCard(infoCarte!!).equals("F") && !intentExtrasModel!!.isCardUpdate!!
        intentExtrasModel!!.mRestArticleCard = mRestArticleCard
        intentExtrasModel!!.mRestStationCard = mRestStationCard
        intentExtrasModel!!.cardType = cardType
        intentExtrasModel!!.cardDiscountId = UtilsCardInfo.getDiscountID(mBankCard,icCpuReader,this)
            try {
                val resultNFC = UtilsCardInfo.readBinaryFile(mBankCard, icCpuReader, "2F20", "5F", this)
                if(resultNFC != null)
                {
                    val listnfcrecord = UtilsCardInfo.getNFCList(resultNFC)
                    if(listnfcrecord != null) {
                        log(TAG, "listnfcrecord ::: " + Gson().toJson(listnfcrecord))
                        intentExtrasModel!!.listnfcrecord = listnfcrecord
                    }
                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }

         log(TAG, "DiscountID :: "+ intentExtrasModel!!.cardDiscountId)
         if(intentExtrasModel!!.mTransaction != null)
            {
                intentExtrasModel!!.mTransaction!!.discountId =  intentExtrasModel!!.cardDiscountId!!.toInt()
           }
            if(cardType == AppConstant.PREPAID_CARD)
            {
                preCardCeiling = UtilsCardInfo.readRecordLinear( mBankCard,icCpuReader, "2F09", "05", "28", this).replace("F", "0")
                intentExtrasModel!!.minimumRechargeAmount = UtilsCardInfo.getPreCardCeiling( preCardCeiling!!, 0, 12).toString()//PLF MIN PREPAYE of the card ---->
            }

        if (cardType != null && (cardType == AppConstant.LOYALTY_CARD && !intentExtrasModel!!.loyaltyTrx) || (cardType != AppConstant.LOYALTY_CARD  && intentExtrasModel!!.loyaltyTrx)) {
            errorTitle = getString(R.string.unknown_card)
            errorMessage = getString(R.string.card_error_12) //getString(R.string.card_error_3)
            return false
        }
        cardCeiling=UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,"2F07", "02", "32",this).replace("F", "0")
        preCardCeiling = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "05", "28", this).replace("F", "0")
        if(cardModel!!.cardCeilingUnit == "1")
        {
            isLitreUnit = true
        }
        // counter ceiling
        remainingCardCeilings = if (isLitreUnit) {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", this).replace("F", "0")
        } else {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", this).replace("F", "0")
        }
        intentExtrasModel!!.pinCount = prefs.getStationModel()!!.tryPinCount!! - UtilsCardInfo.getPinTryCounter(mBankCard, icCpuReader, this)
        // counter ceiling
        if (cardModel!!.cardCeilingUnit == "1") {
            cardCountDetails = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", this).replace("F", "0")
            dateLastPLF = UtilsCardInfo.getDateLastPLF(cardCountDetails!!)
        } else {
            cardCountDetails = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", this).replace("F", "0")
            dateLastPLF = UtilsCardInfo.getDateLastPLF(cardCountDetails!!)
        }

        if (cardRestrictions != null && cardRestrictions == "-1") {
            errorMessage = getString(R.string.card_error_4)
            return false
        }
        else if (cardCeiling != null && cardCeiling == "-1")
        {
            errorMessage = getString(R.string.card_error_5)
            return false
        }
        else if (preCardCeiling != null && preCardCeiling == "-1" && cardType == AppConstant.PREPAID_CARD)
        {
            errorMessage = getString(R.string.card_error_6)
            return false
        }
        else if (remainingCardCeilings != null && remainingCardCeilings == "-1")
        {
            errorMessage = getString(R.string.card_error_7)
            return false
        }
        return true
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            log(TAG, e.message+ExceptionUtils.getStackTrace(e))
            errorMessage = getString(R.string.card_error_8)
            return false
        }
    }

    fun gotoVerifyPinActivity() {
        val i = Intent(this, VerifyPinActivity::class.java)
        intentExtrasModel!!.isFirstUse = isFirstUse
        intentExtrasModel!!.panNumber = panNumber
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(i)
        finish()
    }
    fun checkCardFBSRestriction(context: Context): Boolean {
        //taxi_historique_carte //send_info_card //taxi_update_carte RestrictionNot ablicable to this workflow (Reference)
        // taxi_deverouiller_carte(block list validation not required)
        if (intentExtrasModel!!.loyaltyTrx) {
            mViewModel.cardActivationDetails(panNumber!!)
        }
        val mRestStationList= referenceModel.restrictions_stations
        val mRestHourList= referenceModel.restrictions_horaire
        val mRestSectorList= referenceModel.restrictions_secteurs
        val mRestArticleList= referenceModel.restrictions_articles
        val mRestHolidayList= referenceModel.restrictions_jrs_free



        var isCardInBlackList = false

        val blackList  = referenceModel.blacklist
        if(blackList!=null && blackList.isNotEmpty()){
            for(item in blackList){
                if(item.pan!=null && item.pan.contains(panNumber!!)){
                    isCardInBlackList = true
                    break
                }
            }
        }

        val greyList = prefs.getGreyList()
        if(greyList!=null && greyList.isNotEmpty()){
            for(item in greyList){
                if(item.pan!=null && item.pan.contains(panNumber!!)){
                    isGreyListAvailable = true
                    break
                }
            }
        }

        if (cardType != null && cardType != AppConstant.PREPAID_CARD && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD) {
            errorMessage =  resources.getString(R.string.invalid_card)
            return false
        }
        if (cardType != null && cardType == AppConstant.LOYALTY_CARD && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_HISTORY) {
            errorMessage =  resources.getString(R.string.card_loyalty)
            return false
        }
        isStationRestricted = if (mRestStationCard != 1 && mRestStationList.isNotEmpty()) {
            UtilsCardInfo.isStationRestriction(mRestStationCard, prefs.getPreferenceModel()!!.stationID!!, mRestStationList)
        } else {
            false
        }
        isHourRestricted = if (mRestScheduleCard != 1 && mRestHourList.isNotEmpty()) {
            UtilsCardInfo.isHourRestricted(mRestScheduleCard, mRestHourList)
        } else {
            false
        }

        isArticleRestricted = if (intentExtrasModel!!.selectedProduct != null && mRestArticleCard != 1 && intentExtrasModel!!.selectedProduct!!.productID != 0 && mRestArticleList.isNotEmpty()) {
            UtilsCardInfo.isArticleRestriction(mRestArticleCard, intentExtrasModel!!.selectedProduct!!.productID, mRestArticleList)
        } else {
            false
        }
        isSectorRestricted = if (mRestSectorCard != 1 && mRestSectorList.isNotEmpty()) {
            UtilsCardInfo.isSecteurRestriction(mRestSectorCard,  prefs.getPreferenceModel()!!.sectorId!!, mRestSectorList)
        } else {
            false
        }
        isHolidayRestricted = if (mRestHolidaysCard != 1 && mRestHolidayList.isNotEmpty()) {
            UtilsCardInfo.isHolidaysRestriction(mRestHolidaysCard, mRestHolidayList, Support.dateToString(Date()))
        } else {
            false
        }
        return if (!loyatyDetailsResult) {
            false
        } else if (isCardExpired) {
            errorMessage = resources.getString(R.string.card_expired)+"\n"+resources.getString(R.string.contact_agent)
            false
        } else if (isCardBlocked) {
            errorMessage = resources.getString(R.string.card_block)
            false
        } else if (isGreyListAvailable) {
            false
        } else if ( isCardInBlackList /*checkBlackList(this@CheckCardRestrictionsActivity, panNumber)*/ || (intentExtrasModel!!.pinCount != null && intentExtrasModel!!.pinCount!! >= prefs.getStationModel()!!.tryPinCount!!)
        ) { //Check Card Blocked
            isCardBlocked = true
            errorTitle =
                if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_CHANGE_PIN) {
                    resources.getString(R.string.unable_to_unblock)
                } else {
                    resources.getString(R.string.card_blocked)
                }
            errorMessage = resources.getString(R.string.contact_agent)
            false
        } else if (isStationRestricted) {
            errorMessage = resources.getString(R.string.block_rest_station)
            false
        } else if (isHourRestricted) {
            errorMessage = resources.getString(R.string.block_rest_horaire)
            false
        }
        else if (isArticleRestricted) {
            errorMessage = resources.getString(R.string.block_rest_produit)
            false
        }
        else if (isSectorRestricted) {
            errorMessage = resources.getString(R.string.block_rest_secteur)
            false
        } else if (isHolidayRestricted) {
            errorMessage = resources.getString(R.string.block_rest_holiday)
            false
        } else {
            true
        }

    }
    var loyatyDetailsResult = true


    override fun setObserver() {
        mViewModel.activationObservable.observe(this) {
            if (it.reponse != null && it.contenu != null) {
                loyatyDetailsResult = false
                if (it.reponse != "1") {
                    showErrorDialog(getString(R.string.error), it.error!!)
                }
            }

        }
    }
    private lateinit var errorDialog : Dialog
    fun showErrorDialog(title:String,msg: String?) {
         cardRemoveChecker.cancel()
         if(!this.isFinishing)
         {
             errorDialog = Dialog(this)
             errorDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
             errorDialog.setCancelable(false)
             errorDialog.setContentView(R.layout.dialog_failed_message)
             errorDialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
             val tvTitle = errorDialog.findViewById<TextView>(R.id.title)
             val tvMessage = errorDialog.findViewById<TextView>(R.id.message)
             val dialogButton = errorDialog.findViewById<TextView>(R.id.action_done)

             tvTitle.text = title
             tvMessage.text = msg
             val timer = object : CountDownTimer(10000, 1000) {
                 override fun onTick(millisUntilFinished: Long) {
                     //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                     //here you can have your logic to set text to edittext
                   //  println("Popup Time Remaining: $minutes:$seconds")
                 }

                 override fun onFinish() {
                     val unAttendantMode = referenceModel.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                     if (unAttendantMode) {
                         setBeep()
                         if(intentExtrasModel!!.splitPaymentModel != null && intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!)
                         {
                             val mIntent = Intent(this@CheckCardRestrictionsActivity, SplitPaymentActivity::class.java)
                             resetPayment()
                             mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                             startActivity(mIntent)
                        //     finish()
                         }
                         else
                         {
                             val mIntent = Intent(this@CheckCardRestrictionsActivity, MenuActivity::class.java)
                             startActivity(mIntent)
                           //  finish()
                         }
                     }
                 }
             }
             if(referenceModel.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
                 timer.start()

             dialogButton.setOnClickListener {
                 try { errorDialog.dismiss() } catch (e:Exception){}
                 setBeep()
                 timer.cancel()
                 if(intentExtrasModel!!.splitPaymentModel != null && intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!) {
                     val mIntent = Intent(this, SplitPaymentActivity::class.java)
                     resetPayment()
                     mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                     startActivity(mIntent)
                 //    finish()
                 }
                 else {
                     try{
                         val mIntent = Intent(this, MenuActivity::class.java)
                         startActivity(mIntent)
                     } catch (e:Exception){}
                    // finish()
                 }
             }
             errorDialog.show()

             
         }

    }

    override fun onStop() {
        try {
            cardRemoveChecker.cancel()
            if(::errorDialog.isInitialized) {
                errorDialog.dismiss()
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }
        super.onStop()
    }

    private fun resetPayment() {
        if(!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            intentExtrasModel!!.splitPaymentModel!!.firstModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.firstPaymentName = ""
//            intentExtrasModel!!.mTransaction!!.amount = intentExtrasModel!!.splitPaymentModel!!.totalAmount!!.toDouble()
        }
        else
        {
            intentExtrasModel!!.splitPaymentModel!!.secondModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.secondPaymentName = ""

        }
        intentExtrasModel!!.splitPaymentModel!!.isError = true
    }
    override fun onBackPressed() {

    }

    private var isCardRemovedShown = false
    private val cardRemoveChecker = object : CountDownTimer(10000,1000){
        override fun onTick(p0: Long) {
            if(BuildConfig.POS_TYPE == AppConstant.PAX){
                if(!IccTester.getInstance().detect( 0.toByte()))
                {
                    if(!isCardRemovedShown)
                    {
                        isCardRemovedShown = true
                        showErrorDialog(getString(R.string.error),getString(R.string.card_removed))
                        log(TAG,"CARD REMOVED")
                    }
                }
            }
        }

        override fun onFinish() {
            this.start()
        }

    }


    override fun onResume() {
        super.onResume()
        
    }

    override fun onPause() {
        super.onPause()
        
    }
}
