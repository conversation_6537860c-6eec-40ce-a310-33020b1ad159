package app.rht.petrolcard.ui.reference.viewmodel

import android.content.Context
import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.reference.model.BaseUrlModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.constant.AppConstant
import okhttp3.ResponseBody


class ReferenceViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {

    var urlObserver = MutableLiveData<BaseResponse<BaseUrlModel>>()
    var downloadLogoObserver = MutableLiveData<ResponseBody?>()

    fun getBaseURL(context: Context) {
        context.run {
            var serialNumber = MainApp.sn!!
            if (BuildConfig.POS_TYPE == "LANDI") {
                serialNumber = AppConstant.LANDI_SERIAL_NUMBER // Hardcode
            }
            requestData(mNetworkService.getURLInitial(
                NetworkRequestEndPoints.BASE_URL,
                serialNumber
            ),
                {
                    urlObserver.postValue(it)
                    if (it.reponse == "0")
                        showSnakBar.postValue(it.error!!)
                }, priority = ApiService.PRIORITY_HIGH
            )
        }
    }
    fun downLoadLogo1(companyLogo:String) {
        requestData(mNetworkService.downloadLogo(companyLogo), {
                downloadLogoObserver.postValue(it)
        })
    }
    fun downLoadLogo(companyLogo:String) {
        mNetworkService.downloadLogo(
            companyLogo
        ).subscribeOn(rx.schedulers.Schedulers.io())
            .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
            .subscribe({
                downloadLogoObserver.postValue(it)
            },
            {
                downloadLogoObserver.postValue(null)
             })
    }



}
