package app.rht.petrolcard.ui.transactionlist.model

import android.graphics.Color
import androidx.annotation.Keep
import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.utils.Support


@Keep
class TransactionFromFcc (
     var id: Int? = 0,
     var ref_transaction: String? = null,
     var produit: String? = null,
     var amount : Double =  0.0,
     var quantite : Double =  0.0,
     var pu : Double = 0.0,
     var pompiste: String? = null,
     var rfid: String? = null,
     var dh_transaction: String? = null,
     var pump :Int = 0,
     var hose:Int = 0,
     var flag :Int= 0,
     var transactionStatus :Int= 0,
     var currency :String= "",
     var hexColor: String? = "#FF8212",
     var productId: Int? =0,
     var fccProductId: Int? =0,
     var payment_status:Int = 0,
     var hsCode: String? = null,
     var fuelQtyUnit: String? = null,
     var vatAmount: String? = null,
     var fusionSaleId: String? = "",
     var releaseToken: String? = "",
     var transactionTimestamp: String? = "",
): BaseModel()
{

     fun getAmountValue():String
     {
          //return Support.formatDoubleAffichage(amount).toString() + ""
          return amount.toString() + ""
     }
     fun getQuantityValue():String
     {
          //return Support.formatDoubleAffichage(quantite).toString() + ""
          return quantite.toString() + ""
     }

     fun getDate() : String {
          return if(dh_transaction!=null)
               dh_transaction!!.split(" ")[0]
          else
               ""
     }

     fun getTime() : String {
          return if(dh_transaction!=null)
               dh_transaction!!.split(" ")[1]
          else
               ""
     }

     fun getColor(): Int {
          return Color.parseColor(hexColor)
     }
}


class TransactionFromFccComparator : Comparator<TransactionFromFcc> {
     override fun compare(obj1: TransactionFromFcc, obj2: TransactionFromFcc): Int {
          return obj2.ref_transaction!!.compareTo(obj1.ref_transaction!!)
     }
}