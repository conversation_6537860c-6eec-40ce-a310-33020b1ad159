package app.rht.petrolcard.ui.loyalty.activity

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityMiddleMarketBinding
import app.rht.petrolcard.ui.loyalty.model.ChildLoyaltyCardModel
import app.rht.petrolcard.ui.loyalty.viewmodel.MiddleMarketViewModel
import app.rht.petrolcard.utils.MyMaterialDialog
import app.rht.petrolcard.utils.MyMaterialDialogListener
import app.rht.petrolcard.utils.SwipeToDeleteCallback
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.afollestad.materialdialogs.MaterialDialog
import kotlinx.android.synthetic.main.toolbar.view.*
import java.lang.ref.WeakReference
import java.util.ArrayList

class MiddleMarketActivity  : BaseActivity<MiddleMarketViewModel>(MiddleMarketViewModel::class),
    RecyclerViewArrayAdapter.OnItemClickListener<ChildLoyaltyCardModel> {

    private lateinit var mBinding: ActivityMiddleMarketBinding
    private val TAG = LoyaltyBalanceActivity::class.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_middle_market)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        initViews()
        setupRecyclerview()
    }

    //region child recyclerview
    private val childCardList: ArrayList<ChildLoyaltyCardModel> = ArrayList<ChildLoyaltyCardModel>()
    private lateinit var adapter : RecyclerViewArrayAdapter<ChildLoyaltyCardModel>
    private fun setupRecyclerview() {
        mBinding.recyclerView.setHasFixedSize(true)
        adapter = RecyclerViewArrayAdapter(childCardList,this)
        mBinding.recyclerView.adapter = adapter

       /* val swipeHandler = object : SwipeToDeleteCallback(this) {
            override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
                childCardList.remove(childCardList[viewHolder.adapterPosition])
                adapter.notifyDataSetChanged()
            }
        }
        val itemTouchHelper = ItemTouchHelper(swipeHandler)
        itemTouchHelper.attachToRecyclerView(mBinding.recyclerView)*/
    }
    private fun addChildCard(item: ChildLoyaltyCardModel){
        val foundedElement = childCardList.find {
            it.cardNumber == item.cardNumber
        }
        when {
            foundedElement != null -> {
                showToast(getString(R.string.card_already_resent_in_list))
            }
            item.cardNumber == cardNumber -> {
                showToast(getString(R.string.master_loyalty_cannot_be_added_as_child_card))
            }
            else -> {
                setBeep()
                childCardList.add(item)
                adapter.notifyDataSetChanged()
            }
        }
    }
    //endregion

    //region Card scanning
    private var cardNumber = ""
    private var cardHolder = ""
    private var nfcTag = ""
    private fun scanLoyaltyCard() {
        val intent = Intent(this, ICCLoyaltyActivity::class.java)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,true)
        loyaltyCardResultLauncher.launch(intent)
    }
    private var loyaltyCardResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                log(TAG,"data received ++++ ${data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)}")
                cardNumber = data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)!!
                cardHolder = data.getStringExtra(AppConstant.LOYALTY_CARD_HOLDER)!!
                nfcTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!

                mBinding.tvCardHolder.text = cardHolder
                mBinding.tvCardNumber.text = cardNumber.chunked(4).joinToString(separator = " ")
                //scanNfcTag(cardNumber)
                //mBinding.tvPanNumber.text = cardNumber
                //mBinding.tvTagNumber.text = nfcTag

            }
            else{
                gotoAbortMessageActivity(getString(R.string.try_again),getString(R.string.card_reading_failed))
            }
        }
    }
    //endregion

    //region Authorize NFC
    private var attendantTag = ""
    private fun authorizeNFC(){
        val intent = Intent(this, AuthoriseNfcActivity::class.java)
        intent.putExtra(AppConstant.CARD_NUMBER,cardNumber)
        tagAuthorizationReceiver.launch(intent)
    }
    private var tagAuthorizationReceiver = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                val authorized = data.getBooleanExtra(AppConstant.TAG_AUTHORISED, false)
                attendantTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                //log(TAG,"nfc data received ++++ $tag")
                log(TAG,"nfc authorized: $authorized")
                if(authorized){
                    scanLoyaltyCard()
                }
                else
                {
                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag))
                }
            }

        }
    }
    //endregion

    //region Child Card scanning

    private fun scanChildLoyaltyCard() {
        val intent = Intent(this, ICCLoyaltyActivity::class.java)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,true)
        loyaltyChildCardResultLauncher.launch(intent)
    }
    private var loyaltyChildCardResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                val childCardNumber = data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)!!
                val childCardName = data.getStringExtra(AppConstant.LOYALTY_CARD_HOLDER)!!
                val childNfc = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                log(TAG,"child data received ++++ $childCardName $childCardNumber $childNfc")

                val child = ChildLoyaltyCardModel(childCardNumber,childCardName,childNfc)
                addChildCard(child)
            }
            else{
                showToast(getString(R.string.child_card_reading_failed))
            }
        }
    }
    //endregion

    private fun initViews() {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.middle_market)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            onBackPressed()
        }

        authorizeNFC()
    }

    override fun setObserver() {

    }

    override fun onItemClick(view: View, item: ChildLoyaltyCardModel) {
        MultiClickPreventer.preventMultiClick(view)
        when(view.id){
            R.id.btnDelete -> {
                MyMaterialDialog(
                    this,
                    title = getString(R.string.remove),
                    message = getString(R.string.please_confirm_to_remove)+"\n ${item.cardNumber}",
                    positiveBtnText = getString(R.string.confirm),
                    negativeBtnText = getString(R.string.cancel),
                    listener = object : MyMaterialDialogListener{
                        override fun onPositiveClick(dialog: MaterialDialog) {
                            childCardList.remove(item)
                            adapter.notifyDataSetChanged()
                        }

                        override fun onNegativeClick(dialog: MaterialDialog) {
                            dialog.dismiss()
                        }
                    }
                )
            }
        }
    }

    fun addChildLoyalty(view: View) {
        scanChildLoyaltyCard()
    }
}