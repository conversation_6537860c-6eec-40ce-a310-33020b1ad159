package app.rht.petrolcard.ui.ticket.fragment

import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.CountDownTimer
import android.os.RemoteException
import android.util.DisplayMetrics
import android.view.*
import android.widget.Toast
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.dialog.BaseDialog
import app.rht.petrolcard.databinding.DialogLoyaltyFragmentBinding
import app.rht.petrolcard.ui.iccpayment.model.ChipResponse
import app.rht.petrolcard.ui.ticket.viewmodel.LoyaltyDialogViewModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.paxutils.icc.IccTester
import com.usdk.apiservice.aidl.emv.UEMV
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.BankCard
import java.lang.ref.WeakReference

enum class LoyaltyDialogStatus {
    SUCCESS, INVALID_CARD, NOT_FOUND, SERVER_NOT_CONNECTED
}

interface LoyaltyDialogListener {
    fun onCloseDialog()
    fun onLoyaltyDialogResponse(
        cardNumber: String?,
        isLoyaltyCard: Boolean,
        loyaltyDialogStatus: LoyaltyDialogStatus?
    )
}

class DialogLoyaltyFragment(val loyaltyDialogListener: LoyaltyDialogListener) : BaseDialog<LoyaltyDialogViewModel>(LoyaltyDialogViewModel::class){

    private lateinit var mBinding: DialogLoyaltyFragmentBinding

    private var isLoyaltyCard = false
    private var mBankCard: BankCard? = null

    private val TAG = DialogLoyaltyFragment::class.java.simpleName
    //private var mCore: Core? = null
    private val listener = WeakReference(loyaltyDialogListener).get()!!
    private fun closeDialog() {
        this.listener.onCloseDialog()
        this.dismiss()
    }

    private fun onLoyaltyCardResponse(
        panLoyalty: String,
        isLoyaltyCard: Boolean,
        status: LoyaltyDialogStatus
    ) {
        this.dismiss()
        this.listener.onLoyaltyDialogResponse(panLoyalty, isLoyaltyCard, status)
    }


    init {
        val args = Bundle()
        this.arguments = args
        log(TAG, "Loyalty Dialog Initialized")
    }

    private fun readBundle(mBundle: Bundle?) {
        if (mBundle != null) { }
    }


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        readBundle(arguments)
        setStyle(STYLE_NO_TITLE, R.style.MyThemeDialog)
    }

    override fun onResume() {
        super.onResume()
        val window = dialog!!.window
        val displayMetrics = DisplayMetrics()
        requireActivity().windowManager.defaultDisplay.getMetrics(displayMetrics)

        isCancelable = false

        window!!.setLayout(ViewGroup.LayoutParams.MATCH_PARENT , ViewGroup.LayoutParams.MATCH_PARENT)
        window.setGravity(Gravity.CENTER)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        mBinding = DialogLoyaltyFragmentBinding.inflate(getThemeLayoutInflater(inflater), container, false)

        dialog!!.window!!.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        dialog!!.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog!!.window!!.setLayout(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)

        initView()

        return mBinding.root
    }

    override fun onDestroy() {

        mBinding.unbind()

        if(::readLoyaltyCardTask.isInitialized){
            readLoyaltyCardTask.cancel(true)
        }

        if (BuildConfig.POS_TYPE == "PAX") {
            UtilsCardInfo.disconnectPAX()
            Thread.currentThread().interrupt()
        }

        super.onDestroy()

    }

    fun onBackPressed() {
        if (isAdded && activity != null) requireActivity().supportFragmentManager.popBackStack()
    }
    private fun initView() {
        mBinding.btnConfirm.setOnClickListener {
            setBeep()
            mBinding.btnCancel.visibility = View.GONE
            mBinding.btnConfirm.visibility = View.GONE
            mBinding.CardRelativeLayout.visibility = View.VISIBLE
            mBinding.confirmation.text = getString(R.string.insert_loyalty_card)
            log(TAG, "Starting ICC Task")

            readLoyaltyCardTask = ReadLoyaltyCardTask()
            readLoyaltyCardTask.execute()
        }
        mBinding.btnCancel.setOnClickListener {
            setBeep()
            closeDialog()
        }
    }

    lateinit var readLoyaltyCardTask : ReadLoyaltyCardTask

    override fun setObserver() {
        val ctx = WeakReference(this).get()!!
        mViewModel.activationDetailsObservable.observe(ctx) {
            val activationDetails = it
            log(TAG, "Get customer response::: $activationDetails")
            if (activationDetails != null) {
                if (activationDetails.reponse.equals("1")) {
                    if (activationDetails.contenu != null) {
                        onLoyaltyCardResponse(pan, isLoyaltyCard, LoyaltyDialogStatus.SUCCESS)
                    } else {
                        onLoyaltyCardResponse("", isLoyaltyCard, LoyaltyDialogStatus.NOT_FOUND)
                        Toast.makeText(activity, activationDetails.error, Toast.LENGTH_LONG)
                            .show()
                        log(TAG, "Response content null or empty")
                    }
                } else {
                    setBeep()
                    onLoyaltyCardResponse(
                        activationDetails.error!!,
                        isLoyaltyCard,
                        LoyaltyDialogStatus.SERVER_NOT_CONNECTED
                    )
                   // Toast.makeText(activity, activationDetails.error, Toast.LENGTH_LONG).show()
                }
            } else {
                setBeep()
                onLoyaltyCardResponse("", isLoyaltyCard, LoyaltyDialogStatus.SERVER_NOT_CONNECTED)
                Toast.makeText(activity, getString(R.string.not_connected_to_server), Toast.LENGTH_LONG).show()
            }
        }
    }

    private var pan = ""
    inner class ReadLoyaltyCardTask : CoroutineAsyncTask<Void,Void,Boolean>(){

        //landi device
        private val emv: UEMV? = null
        private val icCpuReader: UICCpuReader? = null

        var resultat = 0
        var respdata: ByteArray
        var resplen: IntArray
        var retvalue = 0

        var sn: ByteArray
        var pes: IntArray
        var resSN = 0
        var bb = false

        var panSequence: String? = null
        var myResponse: ChipResponse? = null
        var logWriter: LogWriter? = null
        init {
            logWriter = LogWriter("LoyaltyScreenLogs")
            logWriter!!.appendLog(TAG, "Scan Card Task started")
            resultat = 0
            respdata = ByteArray(40)
            resplen = IntArray(1)
            retvalue = -1
            sn = ByteArray(16)
            pes = IntArray(1)
            resSN = 0
            panSequence = null
            myResponse = null
        }
        override fun doInBackground(vararg params: Void): Boolean {
            try{
               if (BuildConfig.POS_TYPE == "B_TPE") {
                   mBankCard = BankCard(dialog!!.context)
                   retvalue = mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, respdata, resplen, AppConstant.TPE_APP)
               } else if (BuildConfig.POS_TYPE == "PAX") {
                   UtilsCardInfo.connectPAX()
               }
               resultat = 1
               panSequence = null
                addLogs()
               if (Utils.byteArrayToHex(respdata).substring(0, 2) == "05" ||
                   Utils.byteArrayToHex(respdata).substring(0, 2) == "07" ||
                   BuildConfig.POS_TYPE == "LANDI" ||
                   BuildConfig.POS_TYPE == "PAX"
               ) {   // carte insérée
                   setBeep()

                   //readInfoCarte() ;
                   val infoCarte = UtilsCardInfo.getCardInfo(mBankCard, icCpuReader, dialog!!.context)
                   logWriter!!.appendLog(TAG, "Info carte : $infoCarte")
                   if (infoCarte != null && infoCarte.isNotEmpty())
                       pan = infoCarte.substring(0, 19)
                   if (infoCarte != null && infoCarte.isNotEmpty()) {
                       isLoyaltyCard = infoCarte[68] == '3'
                   }
                   logWriter!!.appendLog(TAG, "NEW PAN LOYALTY carte  : $pan")

               } else {

                   bb = false
                   resultat = -2
               }

           }
            catch (e : RemoteException) {
               e.printStackTrace()
            }
            finally {
              log(TAG, "finally")
               if(pan == null){
                   this.resultat = 0
                 bb = false
               }
               else bb = true
           }
            return bb
        }

        override fun onPostExecute(result: Boolean?) {
            super.onPostExecute(result)

            try {
                if (BuildConfig.POS_TYPE.equals("LANDI",true) && icCpuReader != null)
                    UtilsCardInfo.powerDownLANDI(icCpuReader)
            } catch (ex: RemoteException) {
                ex.printStackTrace()
            }

            if (result!!) {
                if (isLoyaltyCard) {
                    mViewModel.getCardActivationDetails(pan)
                } else {
                    onLoyaltyCardResponse("", false, LoyaltyDialogStatus.INVALID_CARD)
                }
            } else {
                mBinding.CardRelativeLayout.visibility = View.VISIBLE
              showSnackBar(requireView(),getString(R.string.please_insert_valid_loyalty_card))
                ReadLoyaltyCardTask().execute()
            }
        }
        fun addLogs()
        {
            log(TAG, "retvalue => $retvalue")
            log(TAG, "respdata => $respdata")
            log(TAG, "respdata Hex => " + Utils.byteArrayToHex(respdata))
            log(TAG, "resplen => $resplen")
            log(TAG, "resplen[0] => " + resplen[0])
            logWriter!!.appendLog(TAG, "retvalue => $retvalue")
            logWriter!!.appendLog(TAG, "respdata => $respdata")
            logWriter!!.appendLog(TAG, "respdata Hex => " + Utils.byteArrayToHex(respdata))
            logWriter!!.appendLog(TAG, "resplen => $resplen")
            logWriter!!.appendLog(TAG, "resplen[0] => " + resplen[0])
        }

    }

    private fun setBeep(){
        UtilsCardInfo.beep(MainApp.mCore, 10)
    }



}