package app.rht.petrolcard.ui.loyalty.activity

import android.content.Intent
import android.os.Bundle
import android.os.RemoteException
import android.util.Log
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.databinding.ActivityAuthoriseNfcBinding
import app.rht.petrolcard.ui.reference.model.GasStationAttendantModel
import app.rht.petrolcard.ui.reference.model.ManagerBadge
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.MyMaterialDialog
import app.rht.petrolcard.utils.MyMaterialDialogListener
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.paxutils.modules.picc.PiccTester
import com.afollestad.materialdialogs.MaterialDialog
import com.pax.dal.entity.EPiccType
import com.usdk.apiservice.aidl.DeviceServiceData
import com.usdk.apiservice.aidl.UDeviceService
import com.usdk.apiservice.aidl.constants.RFDeviceName
import com.usdk.apiservice.aidl.rfreader.OnPassAndActiveListener
import com.usdk.apiservice.aidl.rfreader.OnPassListener
import com.usdk.apiservice.aidl.rfreader.URFReader
import kotlinx.android.synthetic.main.dialog_message.*
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.HEX
import java.lang.ref.WeakReference

class AuthoriseNfcActivity : BaseActivity<CommonViewModel>(CommonViewModel::class)  {

    private lateinit var mBinding: ActivityAuthoriseNfcBinding
    private val TAG: String = AuthoriseNfcActivity::class.java.simpleName

    private var mBankCard: BankCard? = null
    private var tagUID: String = ""

    private var piccType: EPiccType? = null

    private var deviceService: UDeviceService? = null
    private var rfReader: URFReader? = null
    lateinit var allBadges : ArrayList<GasStationAttendantModel>
    var managerBadges : ArrayList<GasStationAttendantModel>? = null

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_authorise_nfc)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()

        if(prefs.getReferenceModel()!!.IS_ATTENDANT_RECHARGE_CARD != null && prefs.getReferenceModel()!!.IS_ATTENDANT_RECHARGE_CARD!!)
        {
            allBadges = prefs.getReferenceModel()!!.pompiste
            log(TAG, "ALl Badge: $allBadges")
        }
        else
        {
            val mUsersDao = UsersDao()
            mUsersDao.open()
            log(TAG, "badges All: "+mUsersDao.getAllList())
            managerBadges = mUsersDao.getTagsByRole(AppConstant.MANAGER_ROLE_ID)
            mUsersDao.close()
            log(TAG, "badges: $managerBadges")
        }

        if (BuildConfig.POS_TYPE != "LANDI") {
           ScanningMagTask().execute()
        } else {
            try {
                rfReader = getRFCardReaderInner()
                searchAndActive()
            } catch (ex: RemoteException) {
                ex.stackTrace
            }
        }
    }

    private fun setupToolbar()
    {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.nfc_verification)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            finish()
        }

    }

    override fun setObserver() {

    }

    private var tagNFC: String = ""
    private var back = false

    inner class ScanningMagTask : CoroutineAsyncTask<Void,Void,Int>() {

        var resultat = 0
        var retvalue = 0

        var sn: ByteArray
        var pes: IntArray
        var resSN = 0

        init {
            retvalue = 0
            sn = ByteArray(16)
            pes = IntArray(1)
            resSN = 0
        }

        override fun doInBackground(vararg params: Void): Int {
            try{
                if(BuildConfig.POS_TYPE == "B_TPE") {
                    val responseLength = IntArray(1)
                    val responseData = ByteArray(80)
                    mBankCard = BankCard(applicationContext)
                    tagUID = ""
                    resultat = 1
                    tagNFC = "xxx TEST ONLY DELETE PLEASE"

                    if (mBankCard != null)
                        retvalue = mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL,
                        BankCard.CARD_MODE_PICC,
                        60,
                            responseData,
                            responseLength,
                        AppConstant.TPE_APP
                    )

                    if (mBankCard != null) resSN = mBankCard!!.getCardSNFunction(sn, pes)
                    tagUID = HEX.bytesToHex(sn)

                    if (tagUID.isNotEmpty()) {
                        log("tagUID =>", "tagUID--->>>$tagUID")
                    } else {
                        log("tagUID =>", "tagUID--->>>" + null)
                        return 2
                    }
                }
                else if (BuildConfig.POS_TYPE == "PAX"){
                    var i = 0
                    piccType = EPiccType.INTERNAL
                    PiccTester.getInstance(piccType!!).open()
                    var tag: String = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    while (tag.equals("", ignoreCase = true) && i < 20) {
                        i++
                        Thread.sleep(500)
                        tag = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    }

                    if (tag.isNotEmpty())
                        tagUID = tag

                    if (tagUID.isNotEmpty()) {
                        log("tagUID =>", "tagUID--->>>$tagUID")
                    } else {
                        log("tagUID =>", "tagUID--->>>" + null)
                        return 2
                    }
                }
            }
            catch (e :RemoteException ) {
                return 1
            }
            catch (e :InterruptedException) {
                return 1
            }
            var nfcLength = 0
            var minLength = 5
            var maxLength = 14

            if(BuildConfig.POS_TYPE == "PAX" && tagUID != null)
            {
                nfcLength = tagUID.length
            }
            else if(BuildConfig.POS_TYPE == "B_TPE")
            {
                nfcLength =  pes[0]
            }
            if(prefs.getReferenceModel()!!.station!!.nfc_read_min_length != null) {
                minLength = prefs.getReferenceModel()!!.station!!.nfc_read_min_length!!
                maxLength = prefs.getReferenceModel()!!.station!!.nfc_read_max_length!!
            }
            if (nfcLength in minLength..maxLength) {
                setBeep()
                log("equals", "pes[0] == 7")
                tagNFC = if (tagUID.length >= 14) {
                    tagUID.substring(0, 14)
                } else {
                    tagUID
                }
                log("tagNFC =>", "tagNFC--->>>$tagNFC")
                if(prefs.getReferenceModel()!!.IS_ATTENDANT_RECHARGE_CARD != null && prefs.getReferenceModel()!!.IS_ATTENDANT_RECHARGE_CARD!!)
                {
                    if(!allBadges.isNullOrEmpty()){
                        for (badge in allBadges){
                            resultat = 0
                            if (tagNFC == badge.tag){
                                resultat = -2
                                break
                            }
                        }
                    }
                    else {
                        resultat = 0
                    }
                }
                else {
                    if (!managerBadges.isNullOrEmpty()) {
                        for (badge in managerBadges!!){
                            resultat = 0
                            if (tagNFC == badge.tag){
                                resultat = -2
                                break
                            }
                        }
                    } else {
                        resultat = 0
                    }
                }


            } else {
                if (!back) resultat = 1
            }
            return resultat
        }

        override fun onPostExecute(result: Int?) {
            super.onPostExecute(result)
            setBeep()
            if(!back){
                when (result) {
                    0 -> {
                        MyMaterialDialog(
                           this@AuthoriseNfcActivity,
                            title =  getString(R.string.confirm),
                            message =getString(R.string.invalid_badge),
                            positiveBtnText = getString(R.string.yes),
                            negativeBtnText = getString(R.string.no),
                            listener = object : MyMaterialDialogListener{
                                override fun onPositiveClick(dialog: MaterialDialog) {
                                    setBeep()
                                    ScanningMagTask().execute()
                                }

                                override fun onNegativeClick(dialog: MaterialDialog) {
                                    gotoAbortMessageActivity(  getString(R.string.error),getString(R.string.transaction_cancelled))
                                }
                            }
                        )
                    }
                    1 -> {
                        MyMaterialDialog(
                            this@AuthoriseNfcActivity,
                            title =  getString(R.string.confirm),
                            message =getString(R.string.do_you_want_to_scan_again),
                            positiveBtnText = getString(R.string.yes),
                            negativeBtnText = getString(R.string.no),
                            listener = object : MyMaterialDialogListener{
                                override fun onPositiveClick(dialog: MaterialDialog) {
                                    setBeep()
                                    ScanningMagTask().execute()
                                }

                                override fun onNegativeClick(dialog: MaterialDialog) {
                                    gotoAbortMessageActivity(  getString(R.string.error),getString(R.string.transaction_cancelled))
                                }
                            }
                        )
                    }
                    -2 -> {
                        val resultIntent = Intent()
                        resultIntent.putExtra(AppConstant.TAG_AUTHORISED,true)
                        resultIntent.putExtra(AppConstant.CARD_NFC_TAG,tagNFC)
                        setResult(RESULT_OK, resultIntent)
                        finish()
                    }
                    else -> {
                        gotoAbortMessageActivity(  getString(R.string.error),getString(R.string.transaction_cancelled))
                    }
                }
            }
        }

        override fun onCancelled() {
            //super.onCancelled()
            log(TAG,   "onCancelled")
            try {
                mBankCard!!.breakOffCommand()
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }

    override fun onBackPressed() {
        back = true
        if (mBankCard != null) {
            try {
                mBankCard!!.breakOffCommand()
                mBankCard!!.openCloseCardReader(BankCard.CARD_MODE_MAG, 0x02)
                setBeep()
            } catch (ex: RemoteException) {
                ex.stackTrace
            }
        }

        super.finish()
    }

    @Throws(RemoteException::class)
    fun searchAndActive() {
        rfReader!!.searchCardAndActivate(object : OnPassAndActiveListener.Stub() {
            @Throws(RemoteException::class)
            override fun onActivate(responseData: ByteArray) {
                // TODO Activate successful event handling.
                log(TAG, "=> onActivate | cardType = ")
                tagUID = HEX.bytesToHex(responseData)

                searchCard()
            }

            @Throws(RemoteException::class)
            override fun onFail(error: Int) {
                // TODO Error handling, error see RFError.
                log(TAG,"=> onFail: Error ==  $error")
                //UtilsCard.beep(mCore,10);
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag))
            }
        })
    }

    @Throws(RemoteException::class)
    fun getRFCardReaderInner(): URFReader? {
        if (rfReader == null) {
            deviceService = MainApp.getDeviceService()
            val param = Bundle()
            param.putString(DeviceServiceData.RF_DEVICE_NAME, RFDeviceName.INNER)
            rfReader = URFReader.Stub.asInterface(deviceService!!.getRFReader(param))
        }
        return rfReader
    }

    @Throws(RemoteException::class)
    fun searchCard() {
        rfReader!!.searchCard(object : OnPassListener.Stub() {
            @Throws(RemoteException::class)
            override fun onCardPass(cardtype: Int) {
                log(TAG, "=> onCardPass | cardType = $cardtype")
            }

            @Throws(RemoteException::class)
            override fun onFail(i: Int) {
                log(TAG, "=> onFail: ")
            }
        })
    }
}