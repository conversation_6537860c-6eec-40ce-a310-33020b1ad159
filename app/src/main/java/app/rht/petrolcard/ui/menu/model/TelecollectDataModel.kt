package app.rht.petrolcard.ui.menu.model
import androidx.annotation.Keep
@Keep
data class TelecollectDataModel(

//    var id : Int = 0, // identifiant dans ma base sqlite
//    var reference: String? = null,
//    var teleCollectDate: String? = null,
//    var transactionsCount: Int = 0,
//    var cancellationsTransactionsCount: Int = 0,
//    var rechargeCount: Int = 0,
//    var cancellationsRechargeCount: Int = 0,
//    var transactionsTaxisCount: Int = 0,
//    var totalTransactions: Double =0.0,
//    var totalCancellationsTransactions: Double =0.0,
//    var totalRecharges: Double =0.0,
//    var totalCancellationsRecharges: Double = 0.0,
//    var totalTransactionsTaxis:Double =0.0
    var id : Int = 0, // identifiant dans ma base sqlite
    var reference: String? = null,
    var dateTelecollecte: String? = null,
    var nombreTransactions: Int = 0,
    var nombreAnnulationsTransactions: Int = 0,
    var nombreRecharges: Int = 0,
    var nombreAnnulationsRecharges: Int = 0,
    var nbTransactionsTaxis: Int = 0,
    var nbUpdateMileage: Int = 0,
    var totalTransactions: Double =0.0,
    var totalAnnulationsTransactions: Double =0.0,
    var totalRecharges: Double =0.0,
    var totalAnnulationsRecharges: Double = 0.0,
    var totalTransactionsTaxis:Double =0.0,
    var nbTransactionsTicket:Int =0,
    var nbRechargesTicket:Int =0,
    var totalRechargesTicket:Double =0.0,
    var totalTransactionsTicket:Double =0.0,


)