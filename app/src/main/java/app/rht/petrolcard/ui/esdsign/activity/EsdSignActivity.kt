package app.rht.petrolcard.ui.esdsign.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Typeface
import android.os.AsyncTask
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Base64
import android.util.Log
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.ESDSignatureDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TerminalDao
import app.rht.petrolcard.databinding.ActivityEsdSignBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.ui.common.model.IntentExtrasModel

import app.rht.petrolcard.ui.esdsign.model.EsdSignModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TerminalModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.ticket.model.FuelProductModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.fuelpos.FuelPosService
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import com.airbnb.lottie.LottieAnimationView
import com.basusingh.beautifulprogressdialog.BeautifulProgressDialog
import com.github.ybq.android.spinkit.sprite.Sprite
import com.github.ybq.android.spinkit.style.Circle
import kotlinx.android.synthetic.main.activity_attendant_code.*
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.lang.Exception
import java.lang.IndexOutOfBoundsException
import java.lang.NullPointerException
import java.net.SocketException
import java.util.*

@Suppress("DEPRECATION")
class EsdSignActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private lateinit var mBinding: ActivityEsdSignBinding

    private var promptTextView: TextView? = null
     val ESD_SIGNATURE_DATA = "esd_sign_data"
     val ESD_ERROR_ACTION = "action.esd_not_sign_received"
     val ESD_ERROR_MESSAGE = "esd_error_message"
     val ESD_SIGN_RECEIVED_ACTION = "action.esd_sign_received"
     val ESD_SIGN_NOT_RECEIVED_ACTION = "action.esd_not_sign_received"
    //region views
    private var mImageView: ImageView? = null
    private var printingView: LottieAnimationView? = null
    private var outCard: LinearLayout? = null
    private var insertCard: LinearLayout? = null
    private var progressBar: ProgressBar? = null
    private var ticketV: LottieAnimationView? = null
    //endregion

    private var mTransaction: TransactionModel? = null
    //private lateinit var mTerminalDAO: TerminalDao
    private var esdSignatureDAO: ESDSignatureDao? = null
    private var mTerminal: TerminalModel? = null
    private var esdSignModel: EsdSignModel? = null

    private var mProduit: ProductModel? = null

    private var workFlowTransaction: String? = null

    var currency = ""

    lateinit var logWriter: LogWriter

    var esdSignature = ""
    var esdRequestMessage = ""
    var esdTrxRowId = 0
    var stationMode = 0

    private var fuelProductModel: FuelProductModel? = null
     val TAG = EsdSignActivity::class.simpleName
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_esd_sign)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        currency = prefs.currency

        logWriter = LogWriter("EsdSignPageLogs")

        SQLiteDatabase.loadLibs(this)
        initViews()
    }

    private fun initViews(){
        promptTextView = findViewById(R.id.prompt)
        ticketV = findViewById(R.id.animation_view_online)
        progressBar = findViewById(R.id.progressBar)
        val wave: Sprite = Circle()
        progressBar!!.indeterminateDrawable = wave

        printingView = findViewById(R.id.printing_view)
        outCard = findViewById(R.id.outCardImageLayout)
        insertCard = findViewById(R.id.insertCardImageLayout)
        mImageView = findViewById(R.id.insertCardImageView)

        printingView = findViewById(R.id.printing_view)
        outCard = findViewById(R.id.outCardImageLayout)
        insertCard = findViewById(R.id.insertCardImageLayout)

        promptTextView!!.text = resources.getString(R.string.ticket_printing)
        promptTextView!!.typeface = Typeface.DEFAULT_BOLD
        promptTextView!!.setTextColor(ContextCompat.getColor(this, R.color.colorAccent))
        promptTextView!!.textSize = 25f
        promptTextView!!.textAlignment = View.TEXT_ALIGNMENT_CENTER

        getIntentData()

    }
    var intentExtrasModel : IntentExtrasModel? = null
    private fun getIntentData() {

        intentExtrasModel  = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        mTransaction = intentExtrasModel!!.mTransaction

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }

        val mProduitDAO = ProductsDao()
        mProduitDAO.open()
        mProduit = mProduitDAO.getProductById(mTransaction!!.idProduit!!)
        mProduitDAO.close()

        if(intentExtrasModel!!.mTransaction!=null)
            mProduit = intentExtrasModel!!.selectedProduct
            workFlowTransaction = intentExtrasModel!!.workFlowTransaction
            fuelProductModel =intentExtrasModel!!.fuelProductModel

        getEsdSignature()
    }

    override fun setObserver() {

    }

    private fun getEsdSignature() {

        if (stationMode != AppConstant.OFFLINE_TRX_MODE) {
            //if (mProduit != null && mProduit!!.categorie!!.contains("FUEL")) //IS Fuel Product
            if (mProduit != null && mProduit!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) //IS Fuel Product
            {
                if (isSignatureAvailable()) // Compare SEQ No and Pump id on DB
                {
                    gotoTicketActivity()
                } else {
                    startEsdSignReceiver()
                }
            } else {
                startEsdSignReceiver()
            }
        } else {
            esdRequestMessage = prepareEsdMessage()
            EsdSignTask(esdRequestMessage).execute()
        }
    }

    private fun isSignatureAvailable(): Boolean {
        return try {
            esdSignatureDAO = ESDSignatureDao()
            esdSignatureDAO!!.open()
            if (mTransaction != null && mTransaction!!.sequenceController != null && mTransaction!!.sequenceController!!.isNotEmpty())
            {
                //esdSignModel = esdSignatureDAO.getSignedTransaction(fuelProductModel.getSeqNumber(), fuelProductModel.getPumpId());
                esdSignModel = esdSignatureDAO!!.getSignedTransaction(
                    mTransaction!!.sequenceController!!,
                    mTransaction!!.pumpId!!
                )
                if (esdSignModel != null) {
                    esdTrxRowId = esdSignModel!!.id
                    if (esdSignModel!!.signature!!.isNotEmpty()) {
                        esdSignature = esdSignModel!!.signature!!
                        esdSignatureDAO!!.close()
                        true
                    } else {
                        esdSignatureDAO!!.close()
                        false
                    }
                } else {
                    false
                }
            }
            else
            {
                false
            }
        } catch (ex: SQLiteException) {
            log(TAG, ex.message!!)
            esdSignatureDAO!!.close()
            false
        }
    }

    private fun gotoTicketActivity() {
        val intent = Intent()
        intent.putExtra("esdSignature", esdSignature)
        intent.putExtra("esdTrxRowId", esdTrxRowId)
        setResult(RESULT_OK, intent)
        finish()
    }



    //region ESD Printer
    private var udpManager: UDPManager? = null
    private val request1 = "\u0002{/0/09\u0003JN"
    private var messageToSend = ""
    private val request3 = "\u0002}/72\u0003NN"
    private val request4 = "\u0002^/41\u0003"
    var fiscalDate = ""

    private var signatureCountDownTimer: CountDownTimer? = object : CountDownTimer( /*120000*/10000, 1000) {
        var count = 0
        override fun onTick(millisUntilFinished: Long) {
            count++
            log(TAG, "Waiting for ESD Signature: $count")
        }

        override fun onFinish() {
            try {
                showEsdProgress(false)
                count = 0
                if (esdSignature.isEmpty()) {
                    MaterialDialog(this@EsdSignActivity)
                        .title(text = getString(R.string.signature_not_received))
                        .message(text = getString(R.string.retry_to_get_signature))
                        .cancelable(false)
                        .show {
                            cornerRadius(res = R.dimen.material_dialog_corner)
                            positiveButton(text = getString(R.string.retry),click = object : DialogCallback{
                                override fun invoke(dialog: MaterialDialog) {
                                    dialog.dismiss()
                                    printingView!!.playAnimation()
                                    tryToGetSignature() // try again to get signature
                                }
                            })
                        }
                } else {
                    log(TAG, "ESD sign timer timeout: Sign already received")
                }
            } catch (e: Exception) {
                log(TAG, e.message!!)
            }
        }
    }

    private fun tryToGetSignature() {
        esdSignature = ""
        if (signatureCountDownTimer != null) signatureCountDownTimer!!.cancel()

        /*startEsdPrinter(oldMessageForEsd);*/showEsdProgress(true)
        sendUdpMessage(request1)
        sendUdpMessage(getBlocToSignContent(oldMessageForEsd))
        sendUdpMessage(request3)
        sendUdpMessage(request4)
        signatureCountDownTimer!!.start()
    }

    private var oldMessageForEsd = ""
    private var isEsdRequire = false

    private var isSignature = false
    private var messageCount = 0

    private fun sendUdpMessage(message: String) {
        try {
            udpManager!!.sendMessage(message)
            logWriter.appendLog(TAG, "SENT to ESD: $message")
            messageCount++
            Thread.sleep(1000)
        } catch (e: InterruptedException) {
            e.printStackTrace()
        } catch (e: NullPointerException) {
            e.printStackTrace()
        }
    }

    private lateinit var progressDialogCurrent: BeautifulProgressDialog



    private fun getBlocToSignContent(text: String): String {

        val data = text.toByteArray() //"UTF-8" removed by altaf
        val base64 = Base64.encodeToString(data, Base64.DEFAULT)
        Log.d("Base 64 ", base64.replace("\\s+".toRegex(), "") + "!")
        var blockToSign = base64.replace("\\s+".toRegex(), "")
        blockToSign = "\u0002&/" + blockToSign + "/" + Utils.CalcChecksum("&/$blockToSign/".toByteArray()) + "\u0003"
        log(TAG, "ESD Command :: $blockToSign")
        return blockToSign
    }
    private fun prepareEsdMessage(): String {

        var mTerminalDAO = TerminalDao()
        mTerminalDAO.open()
        val terminal = mTerminalDAO.getCurrent()
        mTerminalDAO.close()

        var message = ""
        message += Support.dateToStringH(Date())
            .toString() + " (" + (terminal?.terminalId ?: "***") + ")"
        message += "\n${if (mProduit != null) mProduit!!.categorie else "FUEL"}"
        if (terminal != null) {
            message += "\n${terminal.stationName.toString()} (${terminal.stationId})"
            message += "\n${terminal.address}".trimIndent()
        }
        message += "\n${if (terminal != null) terminal.city else "***"}"
        if (terminal != null)
            message += getString(R.string.fiscal_label)+" : ${terminal.fiscalId}"
        message += (
                if (mProduit != null)
                    "\n"+getString(R.string.product_label)+" : ${mProduit!!.libelle}"
                else
                    "\n"+getString(R.string.product_label)+" : ${Support.getDesignationProductFromId(mTransaction!!.idProduit!!)}".trimIndent()
                ) +resources.getString(R.string.amount_pay) + " : " + mTransaction!!.amount.toString() + " " + currency + (if (mTransaction!!.codePompiste != null)  getString(R.string.attendant_p)+mTransaction!!.codePompiste!!.trimIndent() else "").toString() +
                    "\n"+getString(R.string.date_label)+": " + mTransaction!!.dateTransaction
        if (workFlowTransaction == Workflow.TAXI_FUEL) {
            message += "\n${getString(R.string.price_unit_label)} : ${Support.formatDouble(mTransaction!!.unitPrice!!)} $currency\n"+ getString(R.string.qty_esd) +Support.formatDouble(mTransaction!!.quantite!!)+" L'\n"+getString(R.string.pump_p)+mTransaction!!.pumpId
        }
        return message
    }
    //endregion

    private fun startEsdSignReceiver() {
        val filter = IntentFilter()
        filter.addAction(ESD_SIGN_RECEIVED_ACTION)
        filter.addAction(ESD_SIGN_NOT_RECEIVED_ACTION)
        registerReceiver(esdSignReceiver, filter)
        esdRequestMessage = prepareEsdMessage()
        showEsdProgress(true)

        var fusion = prefs.getFusionModel()
        val fuelPos = prefs.getFuelPosModel()

//        if(fusion!!.EXIST){
//            //FusionService.context = this@EsdSignActivity
//            FusionService.sendESDSignRequest(esdRequestMessage, true)
//        }
//        else if(fuelPos!!.isExist){
//            //FuelPosService.context = this@EsdSignActivity
//            FuelPosService.sendESDSignRequest(esdRequestMessage, true)
//        }
    }

    private var esdSignReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            try {
                if (action == ESD_SIGN_RECEIVED_ACTION) {
                    esdSignature = intent.getStringExtra(ESD_SIGNATURE_DATA)!!
                    showEsdProgress(false)
                    gotoTicketActivity()
                }
                if (action == ESD_SIGN_NOT_RECEIVED_ACTION) {
                    if (esdSignature.isEmpty()) {

                        MaterialDialog(context)
                            .title(text = getString(R.string.signature_not_received))
                            .message(text = getString(R.string.retry_to_get_signature))
                            .cancelable(false)
                            .show {
                                cornerRadius(res = R.dimen.material_dialog_corner)
                                positiveButton(text =  getString(R.string.retry),click = object : DialogCallback{
                                    override fun invoke(dialog: MaterialDialog) {
                                        dialog.dismiss()
                                        printingView!!.playAnimation()
                                        //FusionService.retryESDSignRequest()
                                    }
                                })
                            }

                    } else {
                        log(TAG, "ESD sign timer timeout: Sign already received")
                    }
                }
            } catch (e: Exception) {
                log(TAG, "ERROR IN ESD BR RECEIVER")
                e.printStackTrace()
                logWriter.appendLog(TAG, "Exception: " + e.message)
            }
        }
    }


    private fun showEsdProgress(isVisible: Boolean) {
        try {
            runOnUiThread {
                if (isVisible) {
                    progressDialogCurrent = BeautifulProgressDialog(this@EsdSignActivity, BeautifulProgressDialog.withLottie, getString(
                                            R.string.please_wait_to_get_signature))
                    progressDialogCurrent.setLottieLocation("signature.json")
                    progressDialogCurrent.setCancelable(false)
                    progressDialogCurrent.setLayoutColor(resources.getColor(R.color.white))
                    progressDialogCurrent.setLottieLoop(true)
                    progressDialogCurrent.show()
                } else {
                    progressDialogCurrent.dismiss()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    inner class EsdSignTask(var message: String) : AsyncTask<Void?, Void?, Boolean?>() {
        override fun doInBackground(vararg params: Void?): Boolean? {
            //isEsdRequire = Support.isEsdEnable(EsdSignActivity.this);
            isEsdRequire = true
            log(TAG, "ESD Signature Config: $isEsdRequire")
            if (isEsdRequire) {
                if (esdSignature.isEmpty()) {
                    signatureCountDownTimer!!.start()
                    oldMessageForEsd = message
                    showEsdProgress(true)
                    try {
                        messageToSend = getBlocToSignContent(message)
                        val ip: String
                        var port = 5445
                        val prefs = MainApp.getPrefs()
                        if (BuildConfig.DEBUG) ip = "**************" else {
                            ip = prefs.getReferenceModel()!!.fiscal_printer!!.IPESD
                            port = prefs.getReferenceModel()!!.fiscal_printer!!.PORTESD
                        }
                        if (udpManager == null) {
                            udpManager = UDPManager.getUdpManager(ip, port)
                            udpManager!!.startUDPSocket()
                        }
                        logWriter.appendLog(TAG, "ESD Printer connected: $ip:$port")
                        udpManager!!.setUdpReceiveCallback { data ->
                            val strReceive = String(data.data, 0, data.length)
                            logWriter.appendLog(TAG, "RECEIVED From ESD: $strReceive")
                            if (strReceive.length > 15) {
                                esdSignature = strReceive

                                val packet = strReceive.split("/").toTypedArray()
                                log(TAG, "Total Message part: " + packet.size)
                                if (packet.size >= 7) {
                                    for (i in packet.indices) {
                                        log(TAG, "Part:" + i + " Data:" + packet[i])
                                    }
                                    fiscalDate = packet[5].trim { it <= ' ' }
                                    esdSignature = packet[7].trim { it <= ' ' }
                                    isSignature = true
                                    esdSignature = esdSignature + "_" + fiscalDate
                                    signatureCountDownTimer!!.cancel()
                                    udpManager!!.stopUDPSocket()
                                }
                            }
                        }
                        sendUdpMessage(request1)
                        sendUdpMessage(messageToSend)
                        sendUdpMessage(request3)
                        sendUdpMessage(request4)
                        while (!isSignature) {
                        }
                        showEsdProgress(false)
                        gotoTicketActivity()
                    } catch (e: IndexOutOfBoundsException) {
                        logWriter.appendLog(TAG, "SocketException: " + ExceptionUtils.getStackTrace(e))
                        runOnUiThread {
                            showSnakeBarColor(getString(R.string.unable_to_process_esd))
                        }
                    } catch (e: SocketException) {
                        logWriter.appendLog(TAG, "SocketException: " + ExceptionUtils.getStackTrace(e))
                        runOnUiThread {
                            showSnakeBarColor(getString(R.string.unable_to_connect_esd_printer))
                        }
                    }
                    showEsdProgress(false)
                } else {
                    log(TAG, "ESD Signature already exist")
                }
            } else {
                log(TAG, "ESD Signature not required")
            }

            return null
        }
    }
    override fun onBackPressed() {

    }
}
