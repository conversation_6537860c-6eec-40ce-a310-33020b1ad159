package app.rht.petrolcard.ui.modepay.activity

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.app.PendingIntent
import android.content.*
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.net.Uri
import android.os.*
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.RedirectModel
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.databinding.ActivityUnattendantModepayBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.ui.amountselection.activity.AmountFullTankActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.badge.activity.BadgeActivity
import app.rht.petrolcard.ui.badge.activity.ManagerCodeActivity
import app.rht.petrolcard.ui.badge.activity.ManagerTagActivity
import app.rht.petrolcard.ui.common.dialog.DialogProgressBar
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.TransactionStepLog
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.ui.modepay.model.SplitPaymentModel
import app.rht.petrolcard.ui.product.activity.ProductSelectionActivity
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.model.GasStationAttendantModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.connections.ConnectionDetector
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.fuelpos.FuelPosService
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.afollestad.materialdialogs.MaterialDialog
import com.altafrazzaque.ifsfcomm.*
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.dantsu.escposprinter.EscPosPrinter
import com.dantsu.escposprinter.connection.usb.UsbConnection
import com.dantsu.escposprinter.connection.usb.UsbPrintersConnections
import com.google.gson.Gson
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.File
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList


@Suppress("DEPRECATION")
class UnattendantModePayActivity : BaseActivity<MenuViewModel>(MenuViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<ModePaymentModel> {
    private var TAG = UnattendantModePayActivity::class.java.simpleName
    private lateinit var mBinding: ActivityUnattendantModepayBinding
    var inputManager: InputMethodManager? = null
    private var stationMode = 2
    private var intentExtrasModel: IntentExtrasModel? = null
    var typePay: String? = null
    var dialog: AlertDialog? = null
    private lateinit var adapter : RecyclerViewArrayAdapter<ModePaymentModel>
    private var mList: ArrayList<ModePaymentModel> = ArrayList<ModePaymentModel>()
    var amount = ""
    var isSettings = false
    var fusionExist = false
    private var scheduleService: ScheduledTeleCollectService? = null
    var mBound = false
    var referenceModel: ReferenceModel? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_unattendant_modepay)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
       // log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = IntentExtrasModel()
        intentExtrasModel!!.splitPaymentModel = SplitPaymentModel()
        intentExtrasModel!!.splitPaymentModel!!.isSplitPayment = false
        prefs.isCurrentActivityisMenu = true
        setupRecyclerview()
        initListener()
         referenceModel = prefs.getReferenceModel()
        fusionExist = prefs.getFusionModel()!!.EXIST
        checkClearTransctionFirst =referenceModel!!.terminalConfig!!.clearBeforeTrx ?: false
        startFccService()
        amount = if(intentExtrasModel!!.mTransaction != null) {
            intentExtrasModel!!.mTransaction!!.amount.toString()
        } else {
            intentExtrasModel!!.amount.toString()
        }
        setupViews()
        //requestUsbPermission()

        //  prefs.logReferenceNo  = Support.generateReference(this)
//        if(BuildConfig.DEBUG){
          //  loadTrxs()
       // }
        startTelecollectService()
        //
        checkTrxBeforePowerCut()  //check transaction before transaction
    }

    private fun checkAttendants(){
        val mUsersDao = UsersDao()
        mUsersDao.open()
        val attendantModel  = mUsersDao.getAllList()
        mUsersDao.close()

        if(attendantModel.isEmpty()){
            /*try {
                val adminAttendantModel = GasStationAttendantModel(
                    codepompiste =" 0101",
                    firstname = "Admin",
                    id = 9999,
                    lastname = "RHT",
                    role = "Menage",
                    authenticationMethod = null,
                    authenticationKey = null,
                    usersButton = "",
                    tag = "",
                    doCashTrxUnAttendedOPT = "1")
                val result = mUsersDao.insertAttendiesData(adminAttendantModel,null)
                log(TAG, "********** Admin Attendant code insertion result :: $result ")
            } catch (e:Exception){
                log(TAG, "********** Admin Attendant code insertion failed :: ${e.cause} == ${e.message}")
                showToast("Admin Attendant code insertion failed")
            }*/
            teleCollect()
        }
    }

    private fun checkTrxBeforePowerCut(){
        stopIfsfReceiver()
        val oldTrxHistory = prefs.getIntentModel()
        val pump = prefs.getPumpModel()
        val pumpTag= PumpSelectionActivity::class.simpleName
        val productTag = ProductSelectionActivity::class.simpleName
        if(oldTrxHistory!=null && pump!=null && !prefs.mCurrentActivity.isNullOrEmpty() && (prefs.mCurrentActivity == pumpTag || prefs.mCurrentActivity == productTag)){
            log(TAG,"checkTrxBeforePowerCut :: true")
            if(scheduleService != null)
            {
                scheduleService!!.stopCountDownTimer()
                prefs.isCurrentActivityisMenu = false
            }
            val pumpNumber = pump.FP_PUMP.toInt()
            val pumpName: String = pump.Name
            log(TAG, "Selected Pump: $pumpNumber")
            intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "go to product selection page"))
            val intent = Intent(this, ProductSelectionActivity::class.java)
            val oldIntentModel = prefs.getIntentModel()
            oldIntentModel!!.listNozzles = pump.Pistolers
            oldIntentModel.mPumpNumber=pumpNumber.toString()
            oldIntentModel.pumpName=pumpName
            oldIntentModel.isTerminalPowerOff=true
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,oldIntentModel)
            startActivity(intent )
        }
        else{
            resetPaymentValues(TAG)
            log(TAG,"checkTrxBeforePowerCut :: false")
            prefs.logReferenceNo  = ""
            mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
        }
    }

    private fun loadTrxs(){
        try{
            val transactionTaxiDAO = TransactionDao()
            transactionTaxiDAO.open()
            val selectionner: List<TransactionModel> = transactionTaxiDAO.getTransactions()
            transactionTaxiDAO.close()
            val json = Gson().toJson(selectionner)
            UtilsCardInfo.Log(TAG, "Transaction List : $json")
        } catch (e:Exception) {
            e.printStackTrace()
        }
    }
    private fun startFccService() {
        val fuelpos = prefs.getReferenceModel()!!.FUELPOS
        val fusion = prefs.getReferenceModel()!!.FUSION
        if(fusion!!.EXIST) {

            if(!FusionService.isRunning(this))
            {
                FusionService.start(this)
            }
            else
            {
                Log.i(TAG,"FusionService is already running")
            }
            //startIfsfReceiver()
        }
        else if(fuelpos.isExist) {
            if(!FuelPosService.isRunning(this))
                FuelPosService.start(this)
            else
                Log.i(TAG,"FuelPosService is already running")
        }

    }

    private fun setupViews() {
        val station = prefs.getStationModel()
        mBinding.tvStationName.text = station!!.name
        mBinding.tvTransactionMode.text = "v ${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
    }

    //region clock code
    private val clockTimer = object: CountDownTimer(10000,1000){
        override fun onTick(p0: Long) {
            val currentDateAndTime: String = clockSdf.format(Date())
            mBinding.tvTimeStamp.text = currentDateAndTime
        }

        override fun onFinish() {
            this.start()
        }

    }
    private lateinit var clockSdf : SimpleDateFormat
    private fun startClock(){
        mBinding.tvTimeStamp.text = ""
        clockSdf = SimpleDateFormat("MMM dd yyyy, hh:mm:ss aa")
        try {
            clockTimer.cancel()
            clockTimer.start()
        } catch (e:Exception){
            Log.e(TAG,"EXCEPTIONNNNNNN:::: ${e.cause}: ${e.message}")
        }
    }
    private fun stopClock(){
        try {
            clockTimer.cancel()
        } catch (e:Exception){
            Log.e(TAG,"EXCEPTIONNNNNNN:::: ${e.cause}: ${e.message}")
        }
    }
    //endregion
    override fun onResume() {
        super.onResume()
        startClock()
        mBinding.tvTerminalSn.text = Support.maskString(MainApp.sn?:"","*",4)
    }

    override fun onPause() {
        super.onPause()
        stopClock()
    }

    private fun setupRecyclerview() {
        mList.clear()
        if(prefs.getReferenceModel()!!.mode_of_payment_list != null)
        {
            mBinding.noPayments.visibility =View.GONE
            mBinding.rvModePayments.visibility =View.VISIBLE
            val modeList= prefs.getReferenceModel()!!.mode_of_payment_list!!
            for(list in modeList)
            {
                var icon = 0
                var color = ""
                when (list.payment_id) {
                    AppConstant.VISA_VALUE.toInt() -> {
                        icon = R.drawable.bancardlogo
                    }
                    AppConstant.CARD_VALUE.toInt() -> {
                        icon =R.drawable.fueluplogo
                    }
                    AppConstant.CASH_VALUE.toInt() -> {
                        icon =R.drawable.cash_icon
                    }
                }

                if(list.payment_id == AppConstant.VISA_VALUE.toInt() || list.payment_id == AppConstant.CARD_VALUE.toInt() || list.payment_id == AppConstant.CASH_VALUE.toInt())
                {
                    var paymentName=list.payment_name
//                    if(paymentName == "Card")
//                    {
//                        paymentName = "Fleet Card"
//                    }

                    val mItem = list
                    mItem.payment_id = list.payment_id
                    mItem.payment_name = list.payment_name
                    mItem.color = color
                    mItem.icon = icon
                    mItem.order = list.order
                    mItem.image_url = ""

                    mList.add(mItem)
                }

                continue
            }
        }
        else {
            mBinding.noPayments.visibility =View.VISIBLE
            mBinding.rvModePayments.visibility =View.GONE
        }

        //mList.sortWith(Comparator.comparing { .comparing(ModePaymentModel::order).reversed() })
        mList = ArrayList(mList.sortedBy { it.order!! })

//        for(item in mList){
//            log(TAG,"Mode: ${item.payment_name} ${item.order}")
//        }
        //Collections.sort(mList, ModePaySortList())
        adapter = RecyclerViewArrayAdapter(mList,this)
        mBinding.rvModePayments.adapter = adapter

        adapter.setContext(this)
    }
    private fun initListener() {
        inputManager = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        window.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN)
        val imageURL =  prefs.logoPath //File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).toString() + File.separator + AppConstant.LOGO_NAME)
        if(imageURL!=null){
            Glide.with(this)
                .load(Uri.fromFile(File(imageURL)))
                .apply(RequestOptions().override(512, 512))
                .into(mBinding.logo)
        } else {
            Glide.with(this)
                .load(R.drawable.logo)
                .apply(RequestOptions().override(512, 512))
                .into(mBinding.logo)
        }
        mBinding.logo.setOnClickListener {

                MultiClickPreventer.preventMultiClick(it)
                if(BuildConfig.DEBUG) {
                    val trxDao = TransactionDao()
                    trxDao.open()
//                val trxModel: TotalTransactioCountModel = trxDao.getTotalFuelTransaction()
                    UtilsCardInfo.Log(TAG, "trxModel List : ${gson.toJson(trxDao.getTransactions())}")
                    trxDao.close()
                }
                 setupTestFairy("Settings_${MainApp.sn}")
                isSettings = true
                intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS
                gotoManagerActivity()

        }

    }
    private fun gotoManagerActivity()
    {
        intent = if (prefs.getStationModel()!!.mode_pompiste == AppConstant.CODE) {
            Intent(this, ManagerCodeActivity::class.java)
        } else {
            Intent(this, ManagerTagActivity::class.java)
        }
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
    }
    private fun gotoBadgeActivity()
    {
        val intent= Intent(this, BadgeActivity::class.java)
        activityResultLaunch.launch(intent)
    }
    override fun setObserver() {

    }
    var activityResultLaunch = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppConstant.CARD_NFC_BADGE) {
            if(isSettings)
            {
                val intent = Intent(this, SettingsActivity::class.java)
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(intent)
            }
            else
            {
                gotoNextActivity()
            }

        }
    }
    override fun onItemClick(view: View, model: ModePaymentModel) {
            MultiClickPreventer.preventMultiClick(view)
            setBeep()
            prefs.isCurrentActivityisMenu = false
            prefs.isRestartApplication = "false"
            prefs.logReferenceNo = ""
            if (fusionExist) {
                startIfsfReceiver()
            }
            if (view.id == R.id.categoryCard) {

                if (fusionExist) {
                    intentExtrasModel!!.categoryId = PRODUCT.FUEL_CATEGORY_ID
                    intentExtrasModel!!.typePay = model.payment_id.toString()
                    intentExtrasModel!!.preset_amount = "" + model.preset_amount
                    intentExtrasModel!!.modePaymentModel = model
                    intentExtrasModel!!.mTransaction = TransactionModel()
                    intentExtrasModel!!.mTransaction!!.modepay = model.payment_id.toString()
                    prefs.logReferenceNo = Support.generateNewReferenceNumber(this)
                    intentExtrasModel!!.mTransaction!!.reference = "TRX"+prefs.logReferenceNo
                    setupTestFairy()
                    mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
                    val trxLog = TransactionStepLog()
                    trxLog.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Payment - " + model.payment_name))
                    intentExtrasModel!!.transactionStepLog = trxLog
                    if (Connectivity.isNetworkAvailable(this) || model.payment_id == AppConstant.CASH_VALUE.toInt() || model.payment_id == AppConstant.CARD_VALUE.toInt()) {
                        if (FusionService.fccConnected()) {
                            if (model.payment_id.toString() == AppConstant.VISA_VALUE && !isPackageInstalled(
                                    "com.ebe.edc.nbe"
                                )
                            ) {
                                showDialog(
                                    getString(R.string.bank_payment_application_not_found),
                                    getString(
                                        R.string.please_install_bank_app
                                    )
                                )
                            } else {
                                if (model.payment_id.toString() == AppConstant.CARD_VALUE)
                                {
                                    mainApp.setFleetCardLogName(AppConstant.FLEET_CARD_LOG_NAME + "_" + prefs.logReferenceNo)
                                }
                                intentExtrasModel!!.typePay = model.payment_id.toString()
                                if (fusionExist) {
                                    getPumpStatus()
                                } else {
                                    gotoNextStep()
                                }
                            }
                        } else {
                            dialogShowFCCAlert()
                        }
                    } else {
                        showNetworkDialog()
                    }
                } else {
                    showDialog(getString(R.string.fcc_configuration_on_fbs), getString(R.string.please_assign_fcc_on_the_station))
                }
            }

    }
    fun gotoAttendantScreen()
    {
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "goto attendant pin verification"))
        intent = if (prefs.getStationModel()!!.mode_pompiste == AppConstant.CODE) {
            Intent(this, AttendantCodeActivity::class.java)
        } else {
            Intent(this, AttendantTagActivity::class.java)
        }

        intentExtrasModel!!.stationMode = AppConstant.BEFORE_TRX_MODE
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
    }
    override fun onBackPressed() {
        //finish()
    }
    private fun gotoNextActivity()
    {
        intentExtrasModel!!.workFlowTransaction = Workflow.TAXI_FUEL
        intentExtrasModel!!.stationMode = AppConstant.BEFORE_TRX_MODE
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "goto amount full tank page"))
        log(TAG,"Audit Logs:: "+intentExtrasModel!!.transactionStepLog!!)

        val intent = Intent(this, AmountFullTankActivity::class.java)
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
    }



    //region Telecollect Timer Dialog
    private fun teleCollect() {
        startTelecollectTimer()
        val referenceValue = "TLC" + Support.generateReference()
        showTelecollectProgressDialog(referenceValue)
        mViewModel.checkAppUpdateAvailable()
        val scheduleTask = scheduleService!!.ScheduledTeleCollectTask(referenceValue,false)
        scheduleTask.execute()
    }
    lateinit var telecollectProgressDialog : DialogProgressBar
    private fun showTelecollectProgressDialog(referenceValue:String) {
        telecollectProgressDialog = DialogProgressBar()
        telecollectProgressDialog.title = getString(R.string.processing)
        telecollectProgressDialog.message = "REFERENCE BATCH \n $referenceValue"
        telecollectProgressDialog.listener = object : DialogProgressBar.OnClickListener {
            override fun onClick() {
                setBeep()
                telecollectProgressDialog.dismiss()
                mViewModel.redirect.postValue(
                    RedirectModel(
                        mClass = MenuActivity::class.java,
                        isTopFinish = true,
                        flagIsTopClearTask = true
                    )
                )
            }
        }
        telecollectProgressDialog.show(supportFragmentManager, "DialogProgressBar")
    }
    private fun startTelecollectTimer(){
        stopTelecpollectTimer()
        if(telecollectTimer != null) {
            telecollectTimer.start()
        }
    }
    private fun stopTelecpollectTimer(){
        try {
            if(telecollectTimer != null)
            {
                telecollectTimer.cancel()
            }
        } catch (e:Exception) { e.printStackTrace() }
    }
    private val telecollectTimer = object : CountDownTimer(60000,1000) {
        @SuppressLint("SetTextI18n")
        override fun onTick(milliseconds: Long) {
            val minutes = milliseconds / 1000 / 60
            val seconds = milliseconds / 1000 % 60
            log(TAG, "telecollectTimer: $minutes:$seconds")
        }

        override fun onFinish() {
            stopTelecpollectTimer()
            if(::telecollectProgressDialog.isInitialized){
                try {
                    if(telecollectProgressDialog.isVisible)
                    {
                        telecollectProgressDialog.dismiss()

                    }
                } catch (e:Exception) {}
            }
            if(scheduleService!!.isTelecollectSuccess)
            {
                gotoSuccessMessageActivity(resources.getString(R.string.terminal_setting_success), MainApp.appContext.resources.getString(R.string.telecollecte_ok))
            }
            else
            {
                showDialog(getString(R.string.time_out),getString(R.string.failed_to_connect_with_server))
            }
        }

    }
    //endregion

    fun btnClick(view: View){
        when(view.id){
            R.id.btnTeleCollect -> {
                teleCollect()
            }
            R.id.btnConnect -> {
                FusionService.connectFcc(this)
            }
            R.id.btnDisconnect -> {
                FusionService.disconnectFcc(this)
            }
            R.id.btnAuth -> {
                FusionService.getAuthRequest("1","1","50","1001")
            }
            R.id.btnConState -> {
                val connectivity =  ConnectionDetector(this).isConnected
                val state =  FusionService.fccConnected()
                log(TAG,"$connectivity ----- CONN STATE : $state")
            }
        }
    }
    fun getPumpStatus()
    {
        if (prefs.getReferenceModel()!!.RFID_TERMINALS != null) {
            val pumpsModels: List<RFIDPumpsModel> = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
            if (pumpsModels != null && pumpsModels.size == 1) {
                FusionService.getPumpStatus(pumpsModels[0].pump_number.toString()) //check pump status
            }
            else
            {
                gotoNextStep()
            }
        }
        else
        {
            showDialog(getString(R.string.terminal_out_of_service),getString(R.string.pumps_not_mapped_to_this_terminal))
        }

    }
    var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            //System.out.println("Action: "+action);
            try {
                if (action == ACTION_IFSF_READ_DATA) {
                    val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
                    val bytes = intent.getByteArrayExtra(IFSF_BYTE_MESSAGE)
                    log(TAG, "Message: $msg")
                    performNextStep(msg!!)
                }
                if (action == ACTION_IFSF_CONNECTION_STATE) {
                    val msg = intent.getStringExtra(IFSF_CONNECTION_STATE)
                    log(TAG, "CONNECTION State: $msg")
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }


    private fun performNextStep(msg: String) {
        try {
            val errorCode = StringUtils.substringBetween(msg, "<ErrorCode>", "</ErrorCode>")
            log(TAG, "Error Code: $errorCode")
            if (errorCode != null && errorCode.contains("ERRCD_OK")) {
                if (msg.contains("ServiceResponse")) {
                    val overallResult =
                        StringUtils.substringBetween(msg, "OverallResult=\"", "\"")
                    val requestType = StringUtils.substringBetween(msg, "RequestType=\"", "\"")
                    if (overallResult == "Success") {
                        if (requestType == "GetFPState") {
                            var deviceState = StringUtils.substringBetween(msg, "<DeviceState>", "</DeviceState>")
                            if (deviceState == null) {
                                if (!msg.contains("<DeviceState>")) {
                                    val extraData = StringUtils.substringBetween(msg, "<DeviceState", ">")
                                    deviceState = StringUtils.substringBetween(msg, "<DeviceState$extraData", "</DeviceState>")
                                }
                            }
                            log(TAG, "FP state:  $deviceState")

                            if (deviceState.contains("FDC_ERRORSTATE")) {
                                showDialog(getString(R.string.terminal_out_of_service), getString(R.string.pump_connection_error))
                                //generateLogs(getString(R.string.pump_connection_error), 1)
                            } else {
                                /*val modePay = prefs.getStationModel()!!.mode    //commented because clear trx implementation shifted to service
                                if(checkClearTransctionFirst && modePay == 2) {
                                    //getAvailableTransactionsList()
                                    FusionService.getAllAvailableTransactions()
                                }
                                else {
                                    gotoNextStep()
                                }*/
                                val modePay = prefs.getStationModel()!!.mode
                                if(modePay == 2){
                                    gotoNextStep()
                                } else {
                                    MyMaterialDialogSingle(
                                        this,
                                        getString(R.string.error),
                                        getString(R.string.please_change_station_mode_to_before_transcation),
                                        getString(R.string.okay),
                                        listener = object : MyMaterialDialogSingleListener{
                                            override fun onButtonClick(dialog: MaterialDialog) {
                                                dialog.dismiss()
                                            }
                                        })
                                }
                            }
                        }
                        /*else if (requestType == "GetAvailableFuelSaleTrxs") {  //commented because clear trx implementation shifted to service
                            //checkTransactionsToClear(msg)
                            checkTransactionSequenceNumbers(msg)
                        }
                        else if (requestType == "GetFuelSaleTrxDetails") {
                            checkTransactionDetails(msg)
                        }
                        else if (requestType == "ClearFuelSaleTrx") {
                            trxClearLog.appendLog(TAG, "Transaction cleared: $msg")
                            checkNextTransaction()
                        }*/
                    }
                }
            }
            else
            {
                showDialog(getString(R.string.terminal_out_of_service),getString(R.string.pump_connection_error))
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
        }
    }

    private var checkClearTransctionFirst = false
    private fun gotoNextStep()
    {
        log(TAG,"isPaymentMethodClicked :: true")
        prefs.isPaymentMethodClicked = true
        if(checkClearTransctionFirst) {
            FusionService.checkTransactionsToClear() //checking clear trx in service
        }
        hideNavBarAndStatusBarForcefully()
        if(scheduleService != null) { scheduleService!!.stopCountDownTimer() }
        stopIfsfReceiver()

        if (intentExtrasModel!!.typePay == AppConstant.CASH_VALUE) {
            log(TAG,"isPaymentDone :: true")
            prefs.isPaymentDone = true
            isSettings = false
            intentExtrasModel!!.workFlowTransaction = Workflow.TAXI_FUEL
            gotoAttendantScreen()
        } else {
            gotoNextActivity()
        }
    }

    //region USB connection
    /*private val ACTION_USB_PERMISSION: String = "com.android.example.USB_PERMISSION"
    private fun requestUsbPermission(){
        val usbConnection = UsbPrintersConnections.selectFirstConnected(this)
        val usbManager = this.getSystemService(Context.USB_SERVICE) as UsbManager?
        val permissionIntent = PendingIntent.getBroadcast(
            this,
            0,
            Intent(ACTION_USB_PERMISSION),
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_MUTABLE else 0
        )
        val filter = IntentFilter()
        filter.addAction(ACTION_USB_PERMISSION)
        registerReceiver(usbReceiver, filter)
        usbManager!!.requestPermission(usbConnection!!.device, permissionIntent)
    }
    private fun resetUsbReceiver(){
        unregisterReceiver(usbReceiver)
    }
    private val usbReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (ACTION_USB_PERMISSION == action) {
                synchronized(this) {
                    val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager?
                    val usbDevice = intent.getParcelableExtra<Parcelable>(UsbManager.EXTRA_DEVICE) as UsbDevice?
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (usbManager != null && usbDevice != null) {
                            log(TAG,"USB printer connected")
                        }
                        else {
                            log(TAG,"USB printer null")
                        }
                    }
                }
            }
        }
    }*/
    //endregion

    private fun startIfsfReceiver() {
        val filter = IntentFilter()
        filter.addAction(ACTION_IFSF_READ_DATA)
        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
        registerReceiver(ifsfRecever, filter)
    }
    private fun stopIfsfReceiver() {
        try {
            unregisterReceiver(ifsfRecever)
        } catch (e: Exception) {
            Log.i(TAG, "IFSF Receiver: " + e.message)
        }
        Log.i(TAG, "IFSF RECEIVER STOPPED")
    }
    private fun startTelecollectService()
    {
        val intent = Intent(this, ScheduledTeleCollectService::class.java)
        bindService(intent, mConnection, BIND_AUTO_CREATE)
    }
    private val mConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            Log.d("ServiceConnected", "Connect")
            val binder: ScheduledTeleCollectService.LocalBinder = service as ScheduledTeleCollectService.LocalBinder
            scheduleService = binder.service
            scheduleService!!.startCountDownTimer()
            checkAttendants()
            scheduleService!!.setTelecollectMessageListner(object : ScheduledTeleCollectService.TelecollectListner {
                override fun onTelecollectSuccess(title: String, message: String) {
                    log(TAG, "onTelecollectSuccess :: Receieved $message $title")
                    if(::telecollectProgressDialog.isInitialized){
                        try { telecollectProgressDialog.dismiss() } catch (e:Exception) {}
                    }
                    stopTelecpollectTimer()
                    gotoSuccessMessageActivity(title,message)
                    log(TAG, "scheduleService!!.mTeleCollectData ${gson.toJson(scheduleService!!.mTeleCollectData)}")
                    printTicket(scheduleService!!.mTeleCollectData, true)
                }

                override fun onTelecollectFailed(title: String, message: String) {
                    log(TAG, "onTelecollectFailed :: Receieved $message $title")
                    if(::telecollectProgressDialog.isInitialized){
                        try { telecollectProgressDialog.dismiss() } catch (e:Exception) {}
                    }
                    stopTelecpollectTimer()
                    showErrorOnProgress(title, message)
                }
            })
            mBound = true
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            log(TAG, "onServiceDisconnected")
            mBound = false
            scheduleService!!.stopCountDownTimer()
        }
    }
    private fun executeScheduleTelecollect() {
        var trxCount = 5
        if (referenceModel != null && referenceModel!!.telecollect_transaction_count != null) {
            trxCount = referenceModel!!.telecollect_transaction_count!!
        }
        if (prefs.transactionCount >= trxCount) {
            val referenceValue = "TLC" + Support.generateReference()
            val scheduleTask = scheduleService!!.ScheduledTeleCollectTask(
                referenceValue,
                isScheduleTelecollect = true,
                isReferenceRequired = false
            )
            scheduleTask.execute()
        }
    }
    override fun onDestroy() {
        Log.i(TAG,"onDestroy")
        if(fusionExist)
        {
            stopIfsfReceiver()
        }
        stopScheduleTelecollectService()
        if(scheduleService != null) { scheduleService!!.stopCountDownTimer() }
        super.onDestroy()
    }
    fun stopScheduleTelecollectService(){
        try {
            //Unbind from the service
            if (mBound) {
                unbindService(mConnection)
                mBound = false
            }
        } catch (e: Exception) {
            log(TAG, "stopScheduleTelecollectService: " + e.message)
        }
    }

    override fun onStart() {
        if(scheduleService != null) { scheduleService!!.startCountDownTimer() }
        super.onStart()
    }

    override fun onStop() {
        //resetUsbReceiver()
        if(scheduleService != null) { scheduleService!!.stopCountDownTimer() }
        super.onStop()
    }

}
