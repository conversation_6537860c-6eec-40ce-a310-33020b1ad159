package app.rht.petrolcard.ui.settings.terminal_settings.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityTerminalSettingsBinding
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.settings.terminal_settings.viewmodel.TerminalSettingsViewModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.extensions.showDialog
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import org.json.JSONObject
import java.lang.Exception

class TerminalSettingsActivity : BaseActivity<TerminalSettingsViewModel>(TerminalSettingsViewModel::class) {

    private lateinit var mBinding: ActivityTerminalSettingsBinding
    val fusion = prefs.getReferenceModel()!!.FUSION
    val fuelPos = prefs.getReferenceModel()!!.FUELPOS
    private val TAG = TerminalSettingsActivity::class.java.simpleName
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_terminal_settings)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        setupToolbar()
        setupViews()
    }


    @SuppressLint("SetTextI18n")
    private fun setupToolbar() {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.terminal_settings_params)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            val mIntent = Intent(this, MenuActivity::class.java)
            startActivity(mIntent)
            finish()
        }
    }

    private fun setupViews(){
        val referenceModel = prefs.getReferenceModel()
        if(referenceModel!=null){

            val jsonString = JSONObject(Gson().toJson(referenceModel))

            try{ jsonString.remove("mpesa_credentials") } catch (e:Exception) { e.printStackTrace() }
            try{ jsonString.remove("pompiste") } catch (e:Exception) { e.printStackTrace() }
            try{ jsonString.remove("mtn_pay_credentials") } catch (e:Exception) { e.printStackTrace() }
            try{ jsonString.remove("crash_log_email_config") } catch (e:Exception) { e.printStackTrace() }
            try{ jsonString.remove("fiscal_printer") } catch (e:Exception) { e.printStackTrace() }
            try{ jsonString.remove("badge") } catch (e:Exception) { e.printStackTrace() }
            try{ jsonString.remove("test_fairy_token") } catch (e:Exception) { e.printStackTrace() }

            try{
                jsonString.remove("BACKUPUSER")
                jsonString.remove("BACKUPPASS")
            } catch (e:Exception) { e.printStackTrace() }

            try{
                jsonString.remove("ONLINE_PAYMENT_IP")
                jsonString.remove("ONLINE_PAYMENT_PORT")
            } catch (e:Exception) { e.printStackTrace() }

            try{
                jsonString.remove("GreyList")
                jsonString.remove("GreyListVersion")
            } catch (e:Exception) { e.printStackTrace() }

            try{
                jsonString.remove("blacklist")
                jsonString.remove("blackListVersion")
            } catch (e:Exception) { e.printStackTrace() }

            mBinding.jsonView.bindJson(jsonString)
        }
    }

    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        showDialog(getString(R.string.error), "${errorData.message}")
    }

    override fun setObserver() {

    }

    fun btnClick(v: View){
        when(v){

        }
    }

    //region check server
    private inner class CheckServerTask : CoroutineAsyncTask<Void, Void, Boolean>() {
        override fun doInBackground(vararg params: Void): Boolean {
            return Connectivity.isHostReachable(prefs.baseUrl)
        }

        override fun onPostExecute(result: Boolean?) {
            super.onPostExecute(result)
            val rs = result ?: false
            if(rs){
                showDialog(getString(R.string.success), getString(R.string.server_connection_success))
            } else {
                showDialog(getString(R.string.error), getString(R.string.unable_to_connect))
            }
        }
    }
    //endregion

}