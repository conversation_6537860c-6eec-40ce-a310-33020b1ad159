package app.rht.petrolcard.ui.loyalty.activity

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityTicketLoyaltyBinding
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.utils.LogWriter
import kotlinx.android.synthetic.main.toolbar.view.*

class TicketLoyaltyActivity: BaseActivity<CommonViewModel>(CommonViewModel::class)  {

    private lateinit var mBinding: ActivityTicketLoyaltyBinding
    private var TAG = TicketActivity::class.simpleName
    private lateinit var logWriter : LogWriter

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_loyalty_balance)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
    }

    private fun setupToolbar() {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.loyalty_balance)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            onBackPressed()
        }
    }

    override fun setObserver() {
        TODO("Not yet implemented")
    }
}