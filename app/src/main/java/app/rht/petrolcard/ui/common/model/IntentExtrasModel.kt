package app.rht.petrolcard.ui.common.model

import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.utils.tax.TaxModel
import app.rht.petrolcard.ui.modepay.model.SplitPaymentModel


import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.product.model.FuelTrxCommonModel
import app.rht.petrolcard.ui.reference.model.NozzelsModel
import app.rht.petrolcard.ui.reference.model.PriceModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.ticket.model.BarCode
import app.rht.petrolcard.ui.ticket.model.FuelProductModel
import app.rht.petrolcard.ui.transactionlist.model.OfflineProductsModel
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc


enum class WORKFLOW {
    FUEL,
    LUBRICANTS,
    CAR_TYRES,
    WASHING,
    OIL_CHANGE,
    SHOP,
    TELE_COLLECT
}

class IntentExtrasModel (
    var workFlowTransaction: String? = null,//mWorkFlowTransaction
    var vehicleVerificationTagId: String? = null,
    var amount: String? = null,//montant //monMontant
    var km: String? = null,//KM
    var tagsNFC: String? = null,
    var mPinNumberCard: String? = null,
    var productCode: String? = null,
    var selectedProduct: ProductModel? = null,//monProduit
    var dateExp: String? = null,
    var isValidActionDone: Boolean? = false,
    var verifyPin: Boolean? = false,
    var isOnlinePayment: Boolean? = false,
    var mScanContent: String? = null,
    var badge: String? = null,
    var mPinNumberAttendant: String? = null,
    var stationMode: Int? = null,
    var isOffline: Boolean? = false,
    var mPumpNumber: String? = null,
    var articleID: String? = null,
    var mTransaction: TransactionModel? = null,
    var cardBalance: String? = null, //monSolde
    var typePay: String? = null,//type_pay
    var cardPaymentVerify: String? = null,
    var pinCount: Int? = null,
    var loyaltyTrx: Boolean = false,
    var main: Boolean? = false,
    var transactionFromFcc: TransactionFromFcc? = null,
    var previousWorkFlowTransaction: String? = null, //mWorkFlowTransactionPrecedent
    var panNumber: String? = null, //panStored //mScanContent
    var selectedPrice: PriceModel? = null, //monPrix
    var screenFrom: String? = null, //Constantes.SCREEN_FROM
    var mPinNew: String? = null,
    var cardAmount: String? = null,//montant_carb
    var mDetailArticle: String? = null,
    var mPinPrevious: String? = null,
    var mPhoto: String? = null,
    var mCurrentPhotoPath: String? = null,
    var workflowOFF: String? = null,
    var validTagRowid: Int? = null,
    var verificationType: Int? = null,
    var cardHolderName: String? = null,//nomPorteur
    var creditCount: String? = null,//compteurCredit
    var id_customer: String? = null,
    var isLitreUnit:  Boolean? = false,
    var mVolumeFUSION:  Double? = null,
    var pinError:  Int? = null,
    var cardRestrictions:  String? = null,//restrictionCarte
    var mRestStationCard:  Int? = null,//mRestStationCard
    var mRestScheduleCard:  Int? = null,//mRestHoraireCard
    var mRestHolidaysCard:  Int? = null,//mRestHolidaysCard
    var mRestSectorCard:  Int? = null,//mRestSecteurCard
    var mRestArticleCard:  Int? = null,//mRestArticleCard
    var dailyAmount:  String? = null,
    var isFirstUse:  Boolean? = false,
    var typeCard:  String? = null,
    var volume:  String? = null,
    var amountCardPost:  String? = null,
    var monthlyCeiling:  String? = null,
    var weeklyCeiling:  String? = null,
    var dailyCeiling:  String? = null,
    var nozzeleNumber:  String? = null,
    var offlineMsg:  String? = null,
    var listNozzles:  List<NozzelsModel>? = null,
    var isCardUpdate:  Boolean? = false,
    //var modePay:  Int? = -1,
    var serverUpdateStatus:  Boolean? = false,
    var fuelProductModel: FuelProductModel? = null,
    var compteurTrxDebit: String? = null,
    var compteurTrxCredit: String? = null,
    var qrGift: BarCode? = null,
    var listnfcrecord: List<NfcTagModel>? = null,
    var pumpName: String? = null,
    var fuelTrxCommonModel: FuelTrxCommonModel? = null,
    var splitPaymentModel: SplitPaymentModel? = SplitPaymentModel(),
    var isQtySelected: Boolean = false,
    var isDiscountTransaction: Boolean? = false,
    var discountAmount: String? = null,
    var bankRequestType: Int? = 0,
    var refundAmount: String? = "0",
    var preAuthAmount: String? = "",
    var fleetCardRequestType: Int? = 0,
    var refundStatus: Int? = 0,
    var isFullTankSelected :Boolean = false,
    var taxModel: TaxModel? = null,
    var idPompiste :String? = "0",
    var transactionStages : Int = 0,
    var minimumRechargeAmount : String? = "",
    var cardType : Int? = 0,
    var priceChangeVerified : Boolean = false,
    var productForPriceChange: OfflineProductsModel? = null,
    var cardDiscountId : Int? = 0,
    var categoryId : Int? = 0,
    var isBankRefundSentToApp: Int? = 0,
    var transactionStepLog: TransactionStepLog? = TransactionStepLog(),
    var tokenTRX : String? = null,
    var preset_amount :String? = "",
    var refrenceID :String? = "",
    var modePaymentModel: ModePaymentModel? = null,
    var attendantName :String? = "",
    var workFlowMode :String? = "",
    var vehicleNumber :String? = "",
    var isTerminalPowerOff :Boolean? = false,
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(ProductModel::class.java.classLoader),
        parcel.readString(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(TransactionModel::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readByte() != 0.toByte(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readParcelable(TransactionFromFcc::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(PriceModel::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Double::class.java.classLoader) as? Double,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.createTypedArrayList(NozzelsModel),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readParcelable(FuelProductModel::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(BarCode::class.java.classLoader),
        parcel.createTypedArrayList(NfcTagModel),
        parcel.readString(),
        parcel.readParcelable(FuelTrxCommonModel::class.java.classLoader),
        parcel.readParcelable(SplitPaymentModel::class.java.classLoader),
        parcel.readByte() != 0.toByte(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readByte() != 0.toByte(),
        parcel.readParcelable(TaxModel::class.java.classLoader),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readByte() != 0.toByte(),
        parcel.readParcelable(OfflineProductsModel::class.java.classLoader),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readParcelable(TransactionStepLog::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(ModePaymentModel::class.java.classLoader),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(workFlowTransaction)
        parcel.writeString(vehicleVerificationTagId)
        parcel.writeString(amount)
        parcel.writeString(km)
        parcel.writeString(tagsNFC)
        parcel.writeString(mPinNumberCard)
        parcel.writeString(productCode)
        parcel.writeParcelable(selectedProduct, flags)
        parcel.writeString(dateExp)
        parcel.writeValue(isValidActionDone)
        parcel.writeValue(verifyPin)
        parcel.writeValue(isOnlinePayment)
        parcel.writeString(mScanContent)
        parcel.writeString(badge)
        parcel.writeString(mPinNumberAttendant)
        parcel.writeValue(stationMode)
        parcel.writeValue(isOffline)
        parcel.writeString(mPumpNumber)
        parcel.writeString(articleID)
        parcel.writeParcelable(mTransaction, flags)
        parcel.writeString(cardBalance)
        parcel.writeString(typePay)
        parcel.writeString(cardPaymentVerify)
        parcel.writeValue(pinCount)
        parcel.writeByte(if (loyaltyTrx) 1 else 0)
        parcel.writeValue(main)
        parcel.writeParcelable(transactionFromFcc, flags)
        parcel.writeString(previousWorkFlowTransaction)
        parcel.writeString(panNumber)
        parcel.writeParcelable(selectedPrice, flags)
        parcel.writeString(screenFrom)
        parcel.writeString(mPinNew)
        parcel.writeString(cardAmount)
        parcel.writeString(mDetailArticle)
        parcel.writeString(mPinPrevious)
        parcel.writeString(mPhoto)
        parcel.writeString(mCurrentPhotoPath)
        parcel.writeString(workflowOFF)
        parcel.writeValue(validTagRowid)
        parcel.writeValue(verificationType)
        parcel.writeString(cardHolderName)
        parcel.writeString(creditCount)
        parcel.writeString(id_customer)
        parcel.writeValue(isLitreUnit)
        parcel.writeValue(mVolumeFUSION)
        parcel.writeValue(pinError)
        parcel.writeString(cardRestrictions)
        parcel.writeValue(mRestStationCard)
        parcel.writeValue(mRestScheduleCard)
        parcel.writeValue(mRestHolidaysCard)
        parcel.writeValue(mRestSectorCard)
        parcel.writeValue(mRestArticleCard)
        parcel.writeString(dailyAmount)
        parcel.writeValue(isFirstUse)
        parcel.writeString(typeCard)
        parcel.writeString(volume)
        parcel.writeString(amountCardPost)
        parcel.writeString(monthlyCeiling)
        parcel.writeString(weeklyCeiling)
        parcel.writeString(dailyCeiling)
        parcel.writeString(nozzeleNumber)
        parcel.writeString(offlineMsg)
        parcel.writeTypedList(listNozzles)
        parcel.writeValue(isCardUpdate)
        parcel.writeValue(serverUpdateStatus)
        parcel.writeParcelable(fuelProductModel, flags)
        parcel.writeString(compteurTrxDebit)
        parcel.writeString(compteurTrxCredit)
        parcel.writeParcelable(qrGift, flags)
        parcel.writeTypedList(listnfcrecord)
        parcel.writeString(pumpName)
        parcel.writeParcelable(fuelTrxCommonModel, flags)
        parcel.writeParcelable(splitPaymentModel, flags)
        parcel.writeByte(if (isQtySelected) 1 else 0)
        parcel.writeValue(isDiscountTransaction)
        parcel.writeString(discountAmount)
        parcel.writeValue(bankRequestType)
        parcel.writeString(refundAmount)
        parcel.writeString(preAuthAmount)
        parcel.writeValue(fleetCardRequestType)
        parcel.writeValue(refundStatus)
        parcel.writeByte(if (isFullTankSelected) 1 else 0)
        parcel.writeParcelable(taxModel, flags)
        parcel.writeString(idPompiste)
        parcel.writeInt(transactionStages)
        parcel.writeString(minimumRechargeAmount)
        parcel.writeValue(cardType)
        parcel.writeByte(if (priceChangeVerified) 1 else 0)
        parcel.writeParcelable(productForPriceChange, flags)
        parcel.writeValue(cardDiscountId)
        parcel.writeValue(categoryId)
        parcel.writeValue(isBankRefundSentToApp)
        parcel.writeParcelable(transactionStepLog, flags)
        parcel.writeString(tokenTRX)
        parcel.writeString(preset_amount)
        parcel.writeString(refrenceID)
        parcel.writeParcelable(modePaymentModel, flags)
        parcel.writeString(attendantName)
        parcel.writeString(workFlowMode)
        parcel.writeString(vehicleNumber)
        parcel.writeValue(isTerminalPowerOff)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<IntentExtrasModel> {
        override fun createFromParcel(parcel: Parcel): IntentExtrasModel {
            return IntentExtrasModel(parcel)
        }

        override fun newArray(size: Int): Array<IntentExtrasModel?> {
            return arrayOfNulls(size)
        }
    }
}


//region bank trx stages
//1 enter amount full tank activity
//2 bank transaction process to deduct amount
//3 bank result after deduction
//4 pump selection
//5 product selection
//6 authorise product
//7 fuelling
//8 fuelling done
//9 refund if(avaialble)
//10 ticket
//endregion

//store intentExtraModel in sp
//store fuelling states in sp
//store nozzleClicked in sp
