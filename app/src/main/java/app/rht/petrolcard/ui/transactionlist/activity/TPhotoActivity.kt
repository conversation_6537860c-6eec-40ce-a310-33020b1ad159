package app.rht.petrolcard.ui.transactionlist.activity

import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.os.Environment
import android.util.Log
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.PhotoDao
import app.rht.petrolcard.databinding.ActivityPhotoBinding
import app.rht.petrolcard.ui.amountselection.activity.EnterAmountActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.PhotoModel
import app.rht.petrolcard.ui.loyalty.activity.LoyaltyBalanceActivity
import app.rht.petrolcard.ui.reference.model.TransactionModel

import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.IMAGE_DIRECTORY_NAME
import com.bumptech.glide.Glide
import java.io.File
import java.lang.Exception

@Suppress("DEPRECATION")
class TPhotoActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private val TAG = TPhotoActivity::class.simpleName
    private lateinit var mBinding: ActivityPhotoBinding
    private var mFile: File? = null
    private var fileName: String? = null
    private var intentExtrasModel: IntentExtrasModel? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_photo)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if(isDeviceSupportCamera())
        {
            actionCameraToClickImage(CAMERA_IMAGE_REQUEST)
        }
        else
        {
            gotoAbortMessageActivity(getString(R.string.camera_not_available),getString(R.string.device_not_support))
        }

    }

    private fun isDeviceSupportCamera(): Boolean {
        return applicationContext.packageManager.hasSystemFeature(
            PackageManager.FEATURE_CAMERA_ANY
        )
    }

    override fun onImageCaptureFailed() {
        super.onImageCaptureFailed()
        finish()
    }
    override fun onImagePickSuccess(file: File, imagePickRequest: Int) {
        super.onImagePickSuccess(file, imagePickRequest)
        log(TAG, "IMAGE FILE RESULT: $file $imagePickRequest")
        try {
            when(imagePickRequest){
                CAMERA_IMAGE_REQUEST -> {
                    val timeStamp = getCurrentTimestamp()
                    mFile = file
                    fileName = file.name
                    intentExtrasModel!!.mPhoto = fileName
                    
                    val mediaStorageDir = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_PICTURES), IMAGE_DIRECTORY_NAME)
//                    val mediaFile = File(mediaStorageDir.path + File.separator
//                                + "IMG" + timeStamp + ".jpg"
//                    )
                    intentExtrasModel!!.mCurrentPhotoPath = "file:" + mFile!!.absolutePath
                    log(TAG,"fileName ::: "+fileName)
                    log(TAG,"mCurrentPhotoPath ::: "+ intentExtrasModel!!.mCurrentPhotoPath)
                    if(intentExtrasModel!!.refrenceID.isNullOrEmpty())
                    {
                        intentExtrasModel!!.refrenceID = "TRX" + Support.generateReference(this)
                    }
                        if(intentExtrasModel!!.mCurrentPhotoPath!=null){
                        val mPhotoDAO = PhotoDao()
                        mPhotoDAO.open()
                        val row2: Int = mPhotoDAO.insertPhotoDao(
                            PhotoModel(
                                fileName = fileName!!,
                                referenceTransaction =  intentExtrasModel!!.refrenceID,
                                fieldsOptionalString = intentExtrasModel!!.mCurrentPhotoPath
                            )
                        )
                        mPhotoDAO.close()
                    }

                    intent = Intent(this, EnterAmountActivity::class.java)
                    intent!!.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                    startActivity(intent)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun setObserver() {

    }
    override fun onBackPressed() {

    }


}
