package app.rht.petrolcard.ui.transactionlist.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class SPGetFuelSaleTrxDetails(
    @SerializedName("PumpNo")
    var pumpNo: String="",
    @SerializedName("ProductNo")
    var productNo: String="",
    @SerializedName("Amount")
   var amount: String="",
    @SerializedName("Volume")
    var volume: String="",
    @SerializedName("UnitPrice")
    var unitPrice: String="",
    @SerializedName("ProductName")
    var productName: String?="",
    @SerializedName("ProductUM")
    var productUM: String?="",
    @SerializedName("EndTimeStamp")
    var endTimeStamp: String?="",
    @SerializedName("StartTimeStamp")
    var startTimeStamp: String?="",
    @SerializedName("TransactionSeqNo")
    var transactionSeqNo: String="",
    @SerializedName("FusionSaleId")
    var fusionSaleId: String?="",
    @SerializedName("NozzleNo")
    var nozzleNo: String="",
    @SerializedName("ReleaseToken")
    var releaseToken: String=""
)


//data class SPGetFuelSaleTrxDetails(
//    @SerializedName("ServiceResponse")
//    var serviceResponse: FuelSaleTrxDetailsFields
//)
//data class FuelSaleTrxDetailsFields(
//    @SerializedName("ApplicationSender")
//    var applicationSender: String,
//    @SerializedName("FDCdata")
//    var fDCdata: FuelSaleTrxDetailsFDCDataList,
//)
//data class FuelSaleTrxDetailsFDCDataList(
//    @SerializedName("DeviceClass")
//    var deviceClasses: ArrayList<FuelSaleTrxDetailsDeviceClass>,
//)
//data class FuelSaleTrxDetailsDeviceClass(
//    @SerializedName("PumpNo")
//    var pumpNo: Int,
//    @SerializedName("ProductNo")
//    var ProductNo: Int,
//    @SerializedName("Amount")
//    var Amount: String,
//    @SerializedName("Volume")
//    var Volume: String,
//    @SerializedName("UnitPrice")
//    var UnitPrice: String,
//    @SerializedName("ProductName")
//    var ProductName: String?,
//    @SerializedName("ProductUM")
//    var ProductUM: String?,
//    @SerializedName("EndTimeStamp")
//    var EndTimeStamp: String?,
//    @SerializedName("TransactionSeqNo")
//    var transactionSeqNo: String,
//    @SerializedName("FusionSaleId")
//    var FusionSaleId: String?,
//    @SerializedName("NozzleNo")
//    var NozzleNo: Int,
//
//)
//
//data class FuelSaleTrxDetailsFDCDataObject(
//    @SerializedName("DeviceClass")
//    var deviceClasses: FuelSaleTrxDetailsDeviceClass,
//)


