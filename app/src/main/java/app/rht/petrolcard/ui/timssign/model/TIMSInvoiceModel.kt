package app.rht.petrolcard.ui.timssign.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class TIMSInvoiceModel (
    var timsInvoiceStatus :Int? = 0,
    var receiptNo :String? = null,
    var controlUnitInvoiceNumber :String? = null,
    var invoiceQrCode :String? = null,
    var dateTime :String? = null
):Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeValue(timsInvoiceStatus)
        parcel.writeString(receiptNo)
        parcel.writeString(controlUnitInvoiceNumber)
        parcel.writeString(invoiceQrCode)
        parcel.writeString(dateTime)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TIMSInvoiceModel> {
        override fun createFromParcel(parcel: Parcel): TIMSInvoiceModel {
            return TIMSInvoiceModel(parcel)
        }

        override fun newArray(size: Int): Array<TIMSInvoiceModel?> {
            return arrayOfNulls(size)
        }
    }
}