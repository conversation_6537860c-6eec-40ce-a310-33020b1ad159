package app.rht.petrolcard.ui.settings.maintenance.activity

import android.annotation.SuppressLint
import android.app.ProgressDialog
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityMaintananceBinding
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.model.FuelPOSModel
import app.rht.petrolcard.ui.reference.model.FusionModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.settings.maintenance.viewmodel.MaintenanceViewModel
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.timssign.activity.TIMSInvoiceGenerate
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.AppUtils.Companion.createFormData
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.fuelpos.*
import app.rht.petrolcard.utils.tims.CloseReceiptRes
import com.altafrazzaque.ifsfcomm.*
import kotlinx.android.synthetic.main.toolbar.view.*
import java.io.File
import java.io.FileOutputStream
import java.io.InputStream
import java.lang.ref.WeakReference

class MaintenanceActivity : BaseActivity<MaintenanceViewModel>(MaintenanceViewModel::class) {

    private lateinit var mBinding: ActivityMaintananceBinding
     var fusion: FusionModel?=null
     var fuelPos: FuelPOSModel?=null
    var referenceModel: ReferenceModel? = null
    var preferenceModel: PreferenceModel? = null

    private val TAG = MaintenanceActivity::class.java.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)


        val ctx = WeakReference(this).get()!!
        mBinding = DataBindingUtil.setContentView(ctx, R.layout.activity_maintanance)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = ctx
        mBinding.executePendingBindings()
        referenceModel = prefs.getReferenceModel()
        fusion=  referenceModel!!.FUSION!!
        fuelPos = referenceModel!!.FUELPOS
        preferenceModel = prefs.getPreferenceModel()
        setupToolbar()
        setupViews()
    }


    @SuppressLint("SetTextI18n")
    private fun setupToolbar() {
        val ctx = WeakReference(this).get()!!
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.maintenance)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            val mIntent = Intent(this, MenuActivity::class.java)
            startActivity(mIntent)
            finish()
        }
    }

    private fun setupViews(){
        if(fuelPos!!.isExist && (fusion == null || !fusion!!.EXIST))
        {
            mBinding.btnCheckFusion.visibility = View.GONE
            mBinding.fccSeparator.visibility = View.GONE
            mBinding.btnCheckFuelPos.visibility = View.VISIBLE
        }
        else if (!fuelPos!!.isExist && (fusion != null && fusion!!.EXIST)){
            mBinding.btnCheckFusion.visibility = View.VISIBLE
            mBinding.fccSeparator.visibility = View.GONE
            mBinding.btnCheckFuelPos.visibility = View.GONE
        }
        else {
            mBinding.fccPanel.visibility = View.GONE
        }

        val isDiscountAvailable = prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT ?: false
        if(isDiscountAvailable) {
            mBinding.btnCheckDiscount.visibility = View.VISIBLE
            mBinding.discountSeparator.visibility = View.VISIBLE
        }
        else {
            mBinding.btnCheckDiscount.visibility = View.GONE
            mBinding.discountSeparator.visibility = View.GONE
        }
        val fiscalPrinterModel = referenceModel!!.fiscal_printer
        if (fiscalPrinterModel!!.isAvailable && fiscalPrinterModel.isTIMSRequired == AppConstant.TIMS_REQUIRED) {
            mBinding.btnCheckPrinterStatus.visibility = View.VISIBLE
        }
        else
        {
            mBinding.btnCheckPrinterStatus.visibility = View.GONE
        }
    }

    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        hideProgressDialog()
        showDialog(getString(R.string.error), "${errorData.message}")
    }

    override fun setObserver() {
        val ctx = WeakReference(this).get()!!
        mViewModel.uploadFileResponse.observe(ctx){
            val response = it
            hideProgressDialog()
            if(response.reponse == "1"){
                showDialog(getString(R.string.success), "$it")
            }
            else {
                showDialog(getString(R.string.error), "${it.error!!}, ${it.reponse}, ${it.contenu}")
            }
        }

        mViewModel.cardDetailsObserver.observe(ctx) {
            hideProgressDialog()
            val response = it
            //if(response.reponse == "1"){
                showDialog(getString(R.string.success), getString(R.string.response_received_successfully))
            /*}
            else {
                showDialog(getString(R.string.error), "${it.error!!}, ${it.reponse}, ${it.contenu}")
            }*/
        }
    }

    private lateinit var checkServerTask: CheckServerTask
    fun btnClick(v: View){
        when(v){
            mBinding.btnCheckInternet -> {
                val result = Connectivity.isConnected("www.google.com")
               if(result){
                   showDialog(getString(R.string.success), getString(R.string.internet_connection_is_available))
               } else {
                   showDialog(getString(R.string.error), getString(R.string.no_internet_connection))
               }
            }
            mBinding.btnCheckServer -> {
                checkServerTask = CheckServerTask()
                checkServerTask.execute()
            }
            mBinding.btnCheckFusion -> {
                if(prefs.getFusionModel() != null) {
                    fusionConnectionChecker()
                }
                else {
                    showDialog(getString(R.string.error), getString(R.string.fusion_details_not_found))
                }
            }
            mBinding.btnCheckFuelPos -> {
                if(prefs.getFusionModel() != null) {
                    fuelPosConnectionChecker()
                }
                else {
                    showDialog(getString(R.string.error), getString(R.string.fuelpos_details_not_found))
                }
            }
            mBinding.btnCheckParameters -> {
                checkTeleCollectParameters()
            }
           /* mBinding.btnCheckTelecollect -> {

            }*/
            mBinding.btnCheckLogsUpload -> {
                checkUploadLogs()
            }
            mBinding.btnCheckDiscount -> {
                checkDiscountApi()
            }
            mBinding.btnCheckCardUpdate -> {
                checkCardUpdate()
            }
            mBinding.btnCheckPrinterStatus -> {
                if (referenceModel!!.fiscal_printer!!.isAvailable && referenceModel!!.fiscal_printer!!.isTIMSRequired == AppConstant.TIMS_REQUIRED && (!referenceModel!!.timsAppPackageName!!.isNullOrEmpty() && !isPackageInstalled(referenceModel!!.timsAppPackageName!!))){
                    showDialog(getString(R.string.tremol_app_not_found),getString(R.string.please_install_to_continue_the_transaction))
                }
                else {
                    checkTIMSStatus()
                }
            }
        }
    }

    //region check server
    private inner class CheckServerTask : CoroutineAsyncTask<Void, Void, Boolean>() {
        override fun doInBackground(vararg params: Void): Boolean {
            return Connectivity.isHostReachable(prefs.baseUrl)
        }

        override fun onPostExecute(result: Boolean?) {
            super.onPostExecute(result)
            val rs = result ?: false
            if(rs){
                showDialog(getString(R.string.success), getString(R.string.server_connection_success))
            } else {
                showDialog(getString(R.string.error), getString(R.string.unable_to_connect))
            }
        }
    }
    //endregion

    //region fusion
    private fun startIfsfReceiver() {
        val filter = IntentFilter()
        filter.addAction(ACTION_IFSF_READ_DATA)
        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
        registerReceiver(ifsfRecever, filter)
    }
    private fun stopIfsfReceiver() {
        try {
            unregisterReceiver(ifsfRecever)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
    private var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            println("Action: $action")
            try {
                if (action == ACTION_IFSF_READ_DATA) {
                    val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
                    val bytes = intent.getByteArrayExtra(IFSF_BYTE_MESSAGE)
                    fccCommunication += if(!msg.isNullOrEmpty()) " true " else " false "
                }
                if (action == ACTION_IFSF_CONNECTION_STATE) {
                    val msg = intent.getStringExtra(IFSF_CONNECTION_STATE)
                    fccConnectionStatus += " $msg "
                }
            } catch (e: Exception) {
                e.printStackTrace()
                fccError += " "+ e.message +", "
            }
        }
    }
    //endregion

    private fun fusionConnectionChecker() {
        fccServiceStatus = ""
        val ctx = WeakReference(this).get()!!
        if(prefs.getFusionModel() != null) {
            val fusionExist = prefs.getFusionModel()!!.EXIST
            if(fusionExist){
                showFccProgressDialog(getString(R.string.please_wait_checking_connection))
                if(!FusionService.isRunning(ctx))
                {
                    try {
                        FusionService.stop(ctx)
                        FusionService.start(ctx)
                    } catch (e: Exception){
                        e.printStackTrace()
                    }
                }
                else
                {
                    fccServiceStatus = " "+getString(R.string.running) +" "
                }
                startIfsfReceiver()
                FusionService.getAllAvailableTransactions()
            } else {
                showDialog(getString(R.string.error), getString(R.string.terminal_not_connected_with_fusion))
            }
        }
        else {
            showDialog(getString(R.string.error), getString(R.string.fusion_details_not_found))
        }
    }
    private var fccServiceStatus = ""
    private var fccConnectionStatus = ""
    private var fccCommunication = ""
    private var fuelPosServerConnection = ""
    private var fccError = "--"

    private fun clearFccStatuses(){
        fccServiceStatus = ""
        fccConnectionStatus = ""
        fccCommunication = ""
        fuelPosServerConnection = ""
        fccError = "--"
    }

    //region fuelPOS
    private fun fuelPosConnectionChecker() {
        fccServiceStatus = ""
        val ctx = WeakReference(this).get()!!
        if(prefs.getFuelPosModel() != null) {
            val fuelPosExit = prefs.getFuelPosModel()!!.isExist
            if(fuelPosExit){
                showFccProgressDialog(getString(R.string.please_wait_checking_connection))
                if(!FuelPosService.isRunning(ctx))
                    FuelPosService.start(ctx)

                FuelPosService.startFuelPosEpr()
                startFuelPosReceiver()
            } else {
                fccConnectionStatus += " false"
            }
        }
        else {
            showDialog(getString(R.string.error), getString(R.string.fusion_details_not_found))
        }
    }

    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action
            log(TAG, "FUELPOS ACTION $action")
            var message: String? = ""
            when (action) {
                ACTION_FUEL_POS_REPLY -> {
                    message = intent.getStringExtra(FUEL_POS_REPLY)
                    fccCommunication = if(message?.isNotEmpty() == true) " true" else ""
                }
                ACTION_POS_MESSAGE -> {
                    message = intent.getStringExtra(POS_MESSAGE)
                    fccCommunication = if(message?.isNotEmpty() == true) " true" else ""
                }
                ACTION_FUEL_POS_MESSAGE -> {
                    message = intent.getStringExtra(FUEL_POS_MESSAGE)
                    fccCommunication = if(message?.isNotEmpty() == true) " true" else ""
                }
                ACTION_FUEL_POS_CLIENT_STATE ->
                    if (intent.hasExtra(FUEL_POS_CLIENT_STATE)) {
                        val state = intent.getStringExtra(FUEL_POS_CLIENT_STATE)
                        when (state) {
                            FuelPosClientState.CONNECTED -> {
                                log(TAG, "Fuel POS connected")
                                fccConnectionStatus += " ${getString(R.string.fuelpos_connected)}"

                                FuelPosService.sendRequest(FuelPosCommands.forecourtStatusRequest()) //sending pump request
                                log(TAG,"Sending pump state request")
                            }
                            FuelPosClientState.CONNECTING -> {
                                log(TAG, "Fuel POS connecting")
                                fccConnectionStatus += getString(R.string.conecting)
                            }
                            FuelPosClientState.DISCONNECTED -> {
                                log(TAG, "Fuel POS disconnected")
                                fccConnectionStatus += " ${getString(R.string.fuelpos_disconnected)}"
                            }
                            else ->{
                                log(TAG, "Fuel POS disconnected")
                                fccConnectionStatus += if(state!!.contains("TIMEOUT")){
                                    " "+getString(R.string.connection_timeout_please_check_fcc_ip)
                                } else
                                    " ${getString(R.string.fuelpos_disconnected)+ " $state"}"
                            }
                        }
                    }
                    else if (intent.hasExtra(FUEL_POS_SERVER_STATE)) {
                        when (intent.getStringExtra(FUEL_POS_SERVER_STATE)) {
                            FuelPosServerState.STARTED -> {
                                log(TAG, "Fuel POS listener started")
                                fuelPosServerConnection += " ${FuelPosServerState.STARTED}"
                            }
                            FuelPosServerState.STOPPED -> {
                                log(TAG, "Fuel POS listener stopped")
                                fuelPosServerConnection += " ${FuelPosServerState.STOPPED}"
                            }
                            FuelPosServerState.CLIENT_CONNECTED -> {
                                log(TAG, "Fuel POS connected to listener")
                                fuelPosServerConnection += " ${FuelPosServerState.CLIENT_CONNECTED}"
                            }
                            FuelPosServerState.CLIENT_DISCONNECTED -> {
                                log(TAG, "Fuel POS disconnected to listener")
                                fuelPosServerConnection += " ${FuelPosServerState.CLIENT_DISCONNECTED}"
                            }
                        }
                    }
            }
        }
    }
    private fun startFuelPosReceiver() {
        val ctx = WeakReference(this).get()!!
        if (!FuelPosService.isRunning(ctx)) FuelPosService.start(ctx)
        val filter = IntentFilter()
        filter.addAction(ACTION_FUEL_POS_REPLY)
        filter.addAction(ACTION_FUEL_POS_MESSAGE)
        filter.addAction(ACTION_FUEL_POS_CLIENT_STATE)
        filter.addAction(ACTION_POS_MESSAGE)
        registerReceiver(fuelPosReceiver, filter)
        log(TAG, "FuelPOS receiver started")
    }
    private fun stopFuelPosReceiver() {
        try {
            FuelPosService.stopFuelPosEpr()
            unregisterReceiver(fuelPosReceiver)
            log(TAG, "FuelPos receiver stopped")
        } catch (e: java.lang.Exception) {

        }
    }

    //endregion

    //region progress dialog
    private lateinit var mProgressDialog : ProgressDialog
    private var isProgressShown = false
    private fun showMaintananceProgressDialog(message :String) {
        val ctx = WeakReference(this).get()!!
        isProgressShown = true
        mProgressDialog = ProgressDialog(ctx)
        mProgressDialog.setMessage(message)
        mProgressDialog.setCancelable(false)
        mProgressDialog.show()
    }
    private fun showFccProgressDialog(message :String) {
        val ctx = WeakReference(this).get()!!
        isProgressShown = true
        mProgressDialog = ProgressDialog(ctx)
        mProgressDialog.setMessage(message)
        mProgressDialog.setCancelable(false)
        mProgressDialog.show()
        fccRequestsTimer.start()
    }
    private fun hideProgressDialog() {
        try {
            if(isProgressShown)
            {
                isProgressShown = false
                mProgressDialog.dismiss()
            }
        } catch (e:Exception) { }
    }

    private var fccRequestsTimer = object : CountDownTimer(15000,1000){
        override fun onTick(p0: Long) {}

        override fun onFinish() {
            hideProgressDialog()

            if(fccCommunication.trim().isEmpty() && fccConnectionStatus.isEmpty())
                fccConnectionStatus = "false"
            /*else
                fccConnectionStatus += "true"*/

            if(fuelPos!!.isExist){
                stopFuelPosReceiver()
            }

            var msg = getString(R.string.service_status)+" $fccServiceStatus\n"
                msg += getString(R.string.connection_status)+" $fccConnectionStatus\n"
                msg += getString(R.string.communication_status)+" $fccCommunication\n"
                msg += getString(R.string.error_code_p)+": $fccError\n"
            showDialog(getString(R.string.fcc_connenction), msg)
            clearFccStatuses()
        }
    }

    override fun onDestroy() {
        if(::checkServerTask.isInitialized){
            try { checkServerTask.cancel(true) } catch (e:Exception) {}
        }
        if(::mProgressDialog.isInitialized){
            try { mProgressDialog.dismiss() } catch (e:Exception) {}
        }

        try{ fccRequestsTimer.cancel() } catch (e: java.lang.Exception){ }
        try {
            if(fusion!!.EXIST)
            {
                stopIfsfReceiver()
            }
            if(fuelPos!!.isExist)
            {
                stopFuelPosReceiver()
            }
        } catch (e: Exception){}

        super.onDestroy()

    }
    //endregion

    private fun checkTeleCollectParameters() {
        val ctx = WeakReference(this).get()!!
        this.showMaintananceProgressDialog(getString(R.string.please_wait_request_processing))
        val language =  LocaleManager.getLanguage(ctx).lowercase()
        mViewModel.requestData(MainApp.longApiService.getAllReferenceData(
            prefs.baseUrl + NetworkRequestEndPoints.GET_ALL_REFERENCE_DATA,
            MainApp.sn!!,
            preferenceModel!!.blockListVersionNo!!.toString(),
            preferenceModel!!.greyListVersionNo!!.toString(),
            language
        ), {
            hideProgressDialog()
            if (it.reponse != "0") {
                if(checkAppExpiry())
                {
                    showDialog(getString(R.string.subscription_expired), getString(R.string.your_subscription_has_expired_please_contact_rising_hightech))
                }
                else {
                    showDialog(getString(R.string.success), getString(R.string.telecollect_success))
                }
            }
            else {
                showDialog(getString(R.string.error), "${it.error!!}, ${it.reponse}")
            }
        })
    }

    private fun checkDiscountApi() {
        val ctx = WeakReference(this).get()!!
        ctx.showMaintananceProgressDialog(getString(R.string.please_wait_request_processing))
        mViewModel.requestData(MainApp.longApiService.getAllDiscountDetails(
            prefs.baseUrl + NetworkRequestEndPoints.GET_DISCOUNT_DETAILS,
             MainApp.sn!!
        ),
            {
                hideProgressDialog()
                if (it.success != "0") {
                    showDialog(getString(R.string.success), "${it.success}, ${it.error!!}")
                } else {
                    showDialog(getString(R.string.failed_to_get_discount_details), it.error!!)
                }
            })
    }

    private fun checkUploadLogs(){
        val ctx = WeakReference(this).get()!!
        this.showMaintananceProgressDialog(getString(R.string.please_wait_request_processing))

        val inputStream: InputStream = resources.openRawResource(R.raw.test_file)
        val key = Support.generateMD5("abcde${MainApp.sn}fghij")

        try{
            val outputDir: File = ctx.cacheDir // context being the Activity pointer
            val outputFile = File.createTempFile("test_log_file", ".test", outputDir)
            inputStream.use { input ->
                val outputStream = FileOutputStream(outputFile)
                outputStream.use { output ->
                    val buffer = ByteArray(4 * 1024) // buffer size
                    while (true) {
                        val byteCount = input.read(buffer)
                        if (byteCount < 0) break
                        output.write(buffer, 0, byteCount)
                    }
                    output.flush()
                }
            }

            sendLogFiles(outputFile, key!!,"Maintenance test",0)
        } catch (e:Exception){
            e.printStackTrace()
        }
    }

    private fun sendLogFiles(file: File,key: String,message: String,isErrorLog: Int){
        mViewModel.requestData(MainApp.longApiService.sendLogFiles(url= prefs.baseUrl+ NetworkRequestEndPoints.SENDLOGFILES,
            file = createFormData(file, "file1", "*/*") ,
            sn = createFormData( MainApp.sn!!),
            hashkey = createFormData(key), nbr = createFormData("1"),
            message = createFormData(message), is_error_log = createFormData("$isErrorLog"),connection = "close"),
            {
                hideProgressDialog()
                if(it.reponse=="1"){
                    showDialog(getString(R.string.success), getString(R.string.logs_uploaded_successfully))
                } else {
                    showDialog(getString(R.string.error), it.contenu)
                }
            })
    }

    private fun checkCardUpdate(){
        this.showMaintananceProgressDialog(getString(R.string.please_wait_request_processing))
        mViewModel.getCardDetails("7000710024282100096","1")
    }
    fun checkTIMSStatus() {
        this.showMaintananceProgressDialog(getString(R.string.please_wait_request_processing))
            val timsGenerate = TIMSInvoiceGenerate(this, true)
            timsGenerate.invoiceReceiptListner =
                object : TIMSInvoiceGenerate.InvoiceReceiptListner {
                    override fun onSuccess(model: CloseReceiptRes?, mTransaction: TransactionModel?) {
                        runOnUiThread {

                            hideProgressDialog()
                            showDialog(
                                getString(R.string.connection_status),
                                getString(R.string.success)
                            )
                        }
                    }


                    override fun onFailed(message: String,method:String) {
                        runOnUiThread {

                            hideProgressDialog()
                            showDialog(getString(R.string.connection_status), message)
                        }
                    }

                }
            timsGenerate.startTIMSServer(referenceModel!!.fiscal_printer!!)
        }


}