package app.rht.petrolcard.ui.loyalty.model

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.annotations.SerializedName


class LoyaltyCustomer(
    @SerializedName("dt")
    var date: String? = null,
    @SerializedName("sn")
    var sn: String? = null,
    @SerializedName("badge")
    var badge: String? = null,
    @SerializedName("pan")
    var pan: String? = null,
    @SerializedName("tag")
    var tag: String? = null,
    @SerializedName("nom")
    var fName: String? = null,
    @SerializedName("prenom")
    var lName: String? = null,
    @SerializedName("cin")
    var driverId: String? = null,
    @SerializedName("tel")
    var tel: String? = null,
    @SerializedName("matricule")
    var plateNo: String? = null,
    @SerializedName("num_carte_grise")
    var vRegNo: String? = null,
) {
    fun createJSON(): String? {
        val gsonBuilder = GsonBuilder()
        gsonBuilder.serializeNulls()
        gsonBuilder.setDateFormat("yyyy-MM-dd HH:mm:ss")
        val monGson = gsonBuilder.create()
        return monGson.toJson(this)
    }
}