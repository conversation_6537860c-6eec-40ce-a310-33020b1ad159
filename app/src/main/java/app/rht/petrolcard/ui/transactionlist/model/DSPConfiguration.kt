package app.rht.petrolcard.ui.transactionlist.model
import com.google.gson.annotations.SerializedName


import androidx.annotation.Keep
@Keep
data class DSPConfiguration(
    @SerializedName("ServiceResponse")
    val serviceResponse: ServiceResponseDSP
)

@Keep
data class ServiceResponseDSP(
//    @SerializedName("ApplicationSender")
//    val applicationSender: Int,
    @SerializedName("FDCdata")
    val fDCdata: FDCdataDSP,
//    @SerializedName("OverallResult")
//    val overallResult: String,
//    @SerializedName("RequestID")
//    val requestID: Int,
//    @SerializedName("RequestType")
//    val requestType: String,
//    @SerializedName("WorkstationID")
//    val workstationID: String,
//    @SerializedName("xmlns:xsd")
//    val xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    val xmlnsXsi: String
)

@Keep
data class FDCdataDSP(
    @SerializedName("DeviceClass")
    val deviceClasses: List<DeviceClas>,
//    @SerializedName("FDCTimeStamp")
//    val fDCTimeStamp: String
)

@Keep
data class DeviceClas(
//    @SerializedName("DeviceClass")
//    val deviceClass: DeviceClassDSP,
//    @SerializedName("DeviceID")
//    val deviceID: Int,
//    @SerializedName("ErrorCode")
//    val errorCode: String,
    @SerializedName("Product")
    val products: List<ProductDSP>,
//    @SerializedName("Type")
//    val type: String
)

//@Keep
//data class DeviceClassDSP(
//    @SerializedName("DeviceID")
//    val deviceID: Int,
//    @SerializedName("ErrorCode")
//    val errorCode: String,
////    @SerializedName("Nozzle")
////    val nozzle: List<NozzleDSP>,
//    @SerializedName("PumpNo")
//    val pumpNo: Int,
//    @SerializedName("Type")
//    val type: String
//)
@Keep
data class ProductDSP(
   // @SerializedName("FuelPrice")
   // val fuelPrice: FuelPrice,
    @SerializedName("ProductColour")
    val productColour: String,
    @SerializedName("ProductName")
    val productName: String,
    @SerializedName("ProductNo")
    val productNo: Int
)

//data class NozzleDSP(
////    @SerializedName("NozzleNo")
////    val nozzleNo: Int,
//    @SerializedName("ProductID")
//    val productID: ProductID
//)

//data class ProductID(
//    @SerializedName("ProductNo")
//    val productNo: Int,
//    @SerializedName("TankNo1")
//    val tankNo1: Int
//)

//data class FuelPrice(
//    @SerializedName("content")
//    val content: Int,
//    @SerializedName("ModeNo")
//    val modeNo: Int
//)