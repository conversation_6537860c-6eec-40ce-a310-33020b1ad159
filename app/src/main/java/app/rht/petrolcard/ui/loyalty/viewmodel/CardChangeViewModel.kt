package app.rht.petrolcard.ui.loyalty.viewmodel

import ReplaceCardResponse
import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.loyalty.model.LoyaltyCardDetailsModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File

class CardChangeViewModel constructor(
private val mNetworkService: ApiService,
private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {

    var loyaltyCardResponse = MutableLiveData<BaseResponse<LoyaltyCardDetailsModel>>()
    var replaceCardResponse = MutableLiveData<BaseResponse<ReplaceCardResponse>>()

    fun getLoyaltyCardDetails(driverId:String) {
        val sn = MainApp.sn!!
        val url = (preferencesHelper.baseUrl+ NetworkRequestEndPoints.GET_LOYALTY_CARD_DETAILS).replace("-tpe","")
        val serial = sn.toRequestBody("text/plain".toMediaType())
        val driver = driverId.toRequestBody("text/plain".toMediaType())

        requestData(mNetworkService.getLoyaltyCardDetails(url,serial,driver), {
            loyaltyCardResponse.postValue(it)
        })
    }

    fun replaceLoyaltyCard(driverIdDoc: File, vehicleIdDoc: File,oldCardNumber:String,newCardNumber:String) {
        val sn = MainApp.sn!!
        val url = (preferencesHelper.baseUrl+ NetworkRequestEndPoints.GET_REPLACE_LOYALTY_CARD).replace("-tpe","")

        val serial = sn.toRequestBody("text/plain".toMediaType())
        val oldCard = oldCardNumber.toRequestBody("text/plain".toMediaType())
        val newCard = newCardNumber.toRequestBody("text/plain".toMediaType())

        val driverDoc = createRequestBody("cin_file",driverIdDoc)
        val vehicleDoc = createRequestBody("carte_grise_file",vehicleIdDoc)

        requestData(mNetworkService.replaceLoyaltyCard(url,serial,oldCard,newCard,driverDoc,vehicleDoc), {
            replaceCardResponse.postValue(it)
        })
    }

    private fun createRequestBody(fileName:String,file:File) : MultipartBody.Part {
        val requestFile = file.asRequestBody("multipart/form-data".toMediaType())
        return MultipartBody.Part.createFormData(fileName, file.name, requestFile)
    }
}