package app.rht.petrolcard.ui.loyalty.model

import com.google.gson.Gson
import com.google.gson.GsonBuilder

class GiftQrCode(
    var date: String? = null,
    var term: Int = 0,
    var sta: Int = 0,
    var pmp: String? = null,
    var pan: String? = null,
    var tag: String? = null,
    var ref: String? = null,
    var pdt: Int? = 0,
    var qte: String? = null,
    var pu: String? = null,
    var amount: String? = null,
    var flag: Int = 0,
    var sequence: String? = null,
    var article: String? = null,
) {
    fun createJSON(): String? {
        val gsonBuilder = GsonBuilder()
        gsonBuilder.serializeNulls()
        gsonBuilder.setDateFormat("yyyy-MM-dd HH:mm:ss")
        val monGson = gsonBuilder.create()
        return monGson.toJson(this)
    }
}