package app.rht.petrolcard.ui.loyalty.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.loyalty.model.LoyaltyGift
import app.rht.petrolcard.ui.loyalty.model.GiftTokenResponse
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

class GiftRedeemViewModel constructor(
private val mNetworkService: ApiService,
private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {

    var redeemGiftResponse = MutableLiveData<BaseResponse<String>>()

    fun redeemGift(pan:String, token:String) {
        val sn = MainApp.sn!!
        val url = (preferencesHelper.baseUrl+ NetworkRequestEndPoints.REDEEM_GIFT).replace("-tpe","")
        val serial = sn.toRequestBody("text/plain".toMediaType())
        val card = pan.toRequestBody("text/plain".toMediaType())
        val giftToken = token.toRequestBody("text/plain".toMediaType())

        requestData(mNetworkService.redeemGift(url,serial,card,giftToken), {
            redeemGiftResponse.postValue(it)
        })
    }
}