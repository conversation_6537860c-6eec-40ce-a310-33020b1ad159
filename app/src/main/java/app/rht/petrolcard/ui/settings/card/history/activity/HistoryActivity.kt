package app.rht.petrolcard.ui.settings.card.history.activity

import android.content.Intent
import android.os.*
import android.view.View
import androidx.appcompat.content.res.AppCompatResources
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.databinding.ActivityHistoryBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel
import app.rht.petrolcard.ui.settings.card.history.model.HistoryItemModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.card.common.activity.ManageCardActivity
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import com.pax.dal.IDAL
import wangpos.sdk4.libbasebinder.BankCard
import java.lang.Exception
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.an.deviceinfo.device.model.App
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import java.math.BigDecimal
import java.util.*
import kotlin.math.abs


class HistoryActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<HistoryItemModel> {
    private var TAG= "HistoryActivity"
    var dal: IDAL? = null
    var ret = -1
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""
    var authKey:String? = ""
    var cardType:Int? = 0
    var cardModel: CardStaticStructureModel?=null
    private var isFirstUse = false
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode = 0
    private var mToday: Date? = null
    private var terminalDateErr = false
    private lateinit var mBinding: ActivityHistoryBinding
    private var errorMessage = ""
    var returnValue = 0
    private var mProduct: ProductModel? = null //mProduitToCheckout2
    private var cardHolderName = ""
    private val historyList: ArrayList<HistoryItemModel> = ArrayList<HistoryItemModel>()
    private lateinit var adapter : RecyclerViewArrayAdapter<HistoryItemModel>
    private var dailyAmount =""
    private var monthlyAmount = ""
    private var weeklyAmount = ""
    var cardBalanceTxt = ""
    var unitTxt = ""
    private var totalMonthlyLimit = 0.0 // plafondMensuel
    private var totalWeeklyLimit = 0.0 // plafondHebdo
    private var totalDailyLimit = 0.0 //plafondJournalier
    private var NBR_TRS_MONTHLY = 0
    private var NBR_TRS_WEEKLY = 0
    private var NBR_TRS_DAILY = 0
    private var remainingMonthlyAmount = 0.0 //plafondMensuelCompt
    private var remainingWeeklyAmount = 0.0 //plafondHebdoCompt
    private var remainingDailyAmount = 0.0 //plafondJournaliercompt
    private var REMAINING_NBR_TRS_MONTHLY = 0
    private var REMAINING_NBR_TRS_WEEKLY = 0
    private var REMAINING_NBR_TRS_DAILY = 0
    private var totalMonthlyCount = 0
    private var totalWeeklyCount = 0
    private var totalDailyCount = 0
    var remainingCardCeilings:String?= ""
    var isLitreUnit= false

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_history)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        mToday = Support.getDateComparison(Date())
        val readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
        setupToolbar()
        mBinding.message.text =getString(R.string.history_loading)
        setupRecyclerview()
        getIntentExtras()
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }
    private fun setupToolbar()
    {
        mBinding.toolbarHistory.toolbar.tvTitle.text = getString(R.string.transaction_history)
        mBinding.toolbarHistory.toolbar.setNavigationOnClickListener {
            mBinding.toolbarHistory.toolbar.isEnabled = false
           startActivity(Intent(this,ManageCardActivity::class.java))
            finish()
        }
    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        if(!historyList.isNullOrEmpty())
        {
            mBinding.emptyList.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyList.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
        adapter = RecyclerViewArrayAdapter(historyList,this)
        mBinding.mListView.adapter = adapter
    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
               mBankCard = BankCard(this@HistoryActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                   mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@HistoryActivity) //icCpuReader Not Required for BTPE
                    log(TAG, "infoCarte::: $infoCarte")
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19) else return -1 //Abort Transaction

                    getCardStaticStructure()
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@HistoryActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@HistoryActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        return if(intentExtrasModel!!.panNumber != panNumber) {
                            -1
                        } else {
                            getCardRestrictionInfo()
                            cardHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this@HistoryActivity)!!
                            return if (UtilsCardInfo.verifyPIN(mBankCard, icCpuReader, intentExtrasModel!!.mPinNumberCard , this@HistoryActivity)) {
                                addHistoryLastTrx("01", "02")
                                addHistoryLastTrx("02", "03")
                                addHistoryLastTrx("03", "04")
                                addHistoryLastTrx("04", "05")
                                addHistoryLastTrx("05", "06")
                                addHistoryLastTrx("06", "07")
                                addHistoryLastTrx("08", "09")
                                addHistoryLastTrx("09", "0A")
                                1
                            } else {
                                -1
                            }
                        }

                    }
                    else {
                        showSnakeBar(getString(R.string.card_reading_failed))
                    }
                }
            }
            catch (e:Exception)
            {
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
               // mViewModel.generateLogs(e.message!!,0)
                e.printStackTrace()
                return -1
            }
            return returnValue
        }
        fun addHistoryLastTrx(index: String, index1: String) {
            val lastTRX: String = UtilsCardInfo.lastTransc(mBankCard, icCpuReader, index, this@HistoryActivity)!!
            val lastTRX1: String = UtilsCardInfo.lastTransc(mBankCard, icCpuReader, index1, this@HistoryActivity)!!
            // montant
            val amountHex = lastTRX.substring(4, 12)
            val amount1: Double = Utils.hextoDouble(amountHex)
            // montant
            val amountHex1 = lastTRX1.substring(4, 12)
            val amount2: Double = Utils.hextoDouble(amountHex1)
            val nbreTrx = lastTRX.substring(0, 4)
            if (lastTRX != null && lastTRX.length > 40) {
                Handler(Looper.getMainLooper()).post {
                    val date = lastTRX.substring(12, 26)
                    val type = lastTRX.substring(26, 28)
                    val produit = if (lastTRX.substring(32, 36).contains("FF")) lastTRX.substring(32, 34) else lastTRX.substring(32, 36)
                    val mProduitDAO = ProductsDao()
                    mProduitDAO.open()
                    mProduct = mProduitDAO.getProductById(produit.toInt())
                    val list = mProduitDAO.getAllProducts()
                    mProduitDAO.close()
                    log(TAG, "PRODUCT::: ${Support.formatDouble(amount1 - amount2) + " " + prefs.currency}")
                    log(TAG, "PRODUCT::: "+ produit.toInt())
                    log(TAG, "PRODUCT::: "+ Gson().toJson(mProduct))
                    log(TAG, "PRODUCT LIST::: "+ Gson().toJson(list))

                    var productName = getString(R.string.product_not_available)
//                    if (mProduct != null && mProduct!!.categorie!!.contains("FUEL")) {
//                        productName = Support.getFusionProductName(mProduct!!.fcc_prod_id)!!
//                    }

                    if (mProduct != null && mProduct!!.libelle != null) {
                        productName = mProduct!!.libelle!!
                    }
                    else if(produit == "0099")
                    {
                        productName = getString(R.string.amount_refund)
                    }
                    else if(produit == "0100") // product is null in preauth amount then show following
                    {
                        productName = getString(R.string.fuel_trx_lock_amount)
                    }
                    if(produit.toInt() == PRODUCT.RECHARGE && cardType == AppConstant.POSTPAID_CARD)
                    {
                        productName = getString(R.string.card_refill)
                    }
                    else if(mProduct == null && produit == "0017" && cardType == AppConstant.PREPAID_CARD){
                        productName = getString(R.string.recharge_card)
                    }
                    if (amount1 > 0 && abs(amount1 - amount2) != 0.0) {
                        historyList.add(
                            HistoryItemModel(
                                type,
                                Support.formatDouble(amount1 - amount2) + " " + prefs.currency,
                                formatDate(date),
                                produit,
                                productName
                            )
                        )
                    }
                }
            }

        }

        override fun onPreExecute() {
            showLoading(true)
        }
        override fun onPostExecute(result: Int?) {
            when (result) {
                -1 -> {
                    UtilsCardInfo.beep(mCore, 10)
                    var title = resources.getString(R.string.error)
                    var msg = getString(R.string.transaction_cancelled)
                    if (terminalDateErr) {
                        title = resources.getString(R.string.TPE_DATE_ERROR_SYNCH)
                        msg = resources.getString(R.string.TPE_DATE_ERROR)
                    } else {
                        if (panNumber != null)
                            msg = resources.getString(R.string.card_changed)
                        else resources.getString(R.string.read_card_failed)
                    }
                    gotoAbortMessageActivity(title, msg)
                }
                -2 ->{
                    val title = resources.getString(R.string.error)
                    gotoAbortMessageActivity(title, errorMessage)
                }
                0 -> {
                    val title = resources.getString(R.string.error)
                    val msg = getString(R.string.failed_update)
                    gotoAbortMessageActivity(title, msg)
                }
                1 -> {
                    showLoading(false)
                    setupRecyclerview()
                }
            }


        }
        override fun onProgressUpdate(vararg values: IntArray) {

        }
        override fun onCancelled(result: Int?) {

        }
    }
    fun showLoading(boolean: Boolean)
    {
        runOnUiThread()
        {
            if(boolean)
            {
                mBinding.progressBar.visibility=View.VISIBLE
                mBinding.DetailsLayout.visibility=View.GONE
            }
            else
            {
                mBinding.progressBar.visibility=View.GONE
                mBinding.DetailsLayout.visibility=View.VISIBLE
            }
        }
    }
    fun getCardStaticStructure()
    {
        if(infoCarte != null && panNumber!!.isNotEmpty())
        {
            cardModel = UtilsCardInfo.readCardStaticStructureInfo(mBankCard,icCpuReader,this)
            val jsonData= (gson.toJson(cardModel))
            "Card Static Structure : $jsonData".also { log( TAG,it) }
            cardType = cardModel!!.cardType.toInt()
            if(cardModel!!.cardStatus == "F")
            {
                isFirstUse = true
            }
        }
    }
    override fun setObserver() {
    }
    fun formatDate(date: String): String? {
        val dateF: String
        dateF =
            date.substring(0, 4) + "/" + date.substring(4, 6) + "/" + date.substring(6, 8) + "  " +
                    date.substring(8, 10) + ":" + date.substring(10, 12) + ":" + date.substring(
                12,
                14
            )
        return dateF
    }
    override fun onItemClick(view: View, `object`: HistoryItemModel) {
        MultiClickPreventer.preventMultiClick(view)
    }
    fun getCardRestrictionInfo()
    {
        if (intentExtrasModel!!.productCode != null) {
            mProduct = getProductDetailsByCode(intentExtrasModel!!.productCode!!)
        }
        //TotalCard Ceiling Limits
        val cardCeilings = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "02", "32", this).replace("F", "0")
        totalMonthlyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 0, 12) //Monthly card limit
        totalWeeklyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 12, 24) //weekly ceiling cap
        totalDailyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 24, 36) //ceiling day of the card
        NBR_TRS_MONTHLY = UtilsCardInfo.getCardCeilingCount( cardCeilings, 36, 44) // get No. of Trx //NBR MONTHLY TRS of the card
        NBR_TRS_WEEKLY = UtilsCardInfo.getCardCeilingCount( cardCeilings, 44, 52) //NBR TRS WEEKLY from the card
        NBR_TRS_DAILY = UtilsCardInfo.getCardCeilingCount( cardCeilings, 52, 60) // NBR TRS DAILY of the card
        cardHolderName = UtilsCardInfo.readCardHolderName( mBankCard,icCpuReader, this)!!
        if(cardModel!!.cardCeilingUnit == "1")
        {
            isLitreUnit = true
        }
        remainingCardCeilings = if (isLitreUnit) {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", this).replace("F", "0")
        } else {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", this).replace("F", "0")
        }
        remainingMonthlyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  0, 12)
        remainingWeeklyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  12, 24)
        remainingDailyAmount = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  24, 36)
        REMAINING_NBR_TRS_MONTHLY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  36, 44).toInt()
        REMAINING_NBR_TRS_WEEKLY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!, 44, 52).toInt()
        REMAINING_NBR_TRS_DAILY = UtilsCardInfo.getCardCeilings( remainingCardCeilings!!,  52, 60).toInt()

         totalMonthlyCount = NBR_TRS_MONTHLY - REMAINING_NBR_TRS_MONTHLY
         totalWeeklyCount = NBR_TRS_WEEKLY - REMAINING_NBR_TRS_WEEKLY
         totalDailyCount = NBR_TRS_DAILY - REMAINING_NBR_TRS_DAILY

        if(totalMonthlyCount < 0)
        {
            totalMonthlyCount = 0
        }
        if(totalWeeklyCount < 0)
        {
            totalWeeklyCount = 0
        }
        if(totalDailyCount < 0)
        {
            totalDailyCount = 0
        }
//        val cardBalance = if(cardType == AppConstant.PREPAID_CARD) {
//            UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this)
//        } else {
//            Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount)
//        } //commented this to check actual postapid balance
        val cardBalance = UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this)
        monthlyAmount =  ""+(Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount))
        weeklyAmount = ""+(Math.max(0.0, totalWeeklyLimit - remainingWeeklyAmount))
        dailyAmount = ""+(Math.max(0.0, totalDailyLimit - remainingDailyAmount))
        cardBalanceTxt = ""+(Support.formatDoubleAffichage(cardBalance.toDouble()).toString())

        if (isLitreUnit) {
            unitTxt = getString(R.string.liters)
        } else {
            unitTxt =prefs.currency
            dailyAmount = dailyAmount.toDouble().toString()
            weeklyAmount = weeklyAmount.toDouble().toString()
            monthlyAmount = monthlyAmount.toDouble().toString()
        }
        runOnUiThread {
            if (cardType!! == AppConstant.PREPAID_CARD)
                mBinding.layoutCardBalance.visibility = View.VISIBLE
            else
                mBinding.layoutCardBalance.visibility = View.VISIBLE  //GONE

            setTextValues()
        }
    }
    fun setTextValues()
    {
        val dTxt =BigDecimal.valueOf(dailyAmount.replace(" ","").toDouble())
        val wTxt = BigDecimal.valueOf(weeklyAmount.replace(" ","").toDouble())
        val mTxt = BigDecimal.valueOf(monthlyAmount.replace(" ","").toDouble())
        val cbTxt = BigDecimal.valueOf(cardBalanceTxt.replace(" ","").toDouble())

        val df=  decimalFormat.format(dTxt)
        val wf=  decimalFormat.format(wTxt)
        val mf=  decimalFormat.format(mTxt)
        val cbf=  decimalFormat.format(cbTxt)
        ("$df $unitTxt").also { mBinding.dailyCeilingTxt.text = it }
        ("$wf $unitTxt").also { mBinding.weeklyCeilingTxt.text = it }
        ("$mf $unitTxt").also { mBinding.monthlyCeilingTxt.text = it }
        ("$cbf $unitTxt").also { mBinding.cardBalanceTxt.text = it }

        mBinding.cardNumber.text = panNumber
        mBinding.cardHolderName.text = cardHolderName
        val exp = cardModel!!.expiredDate.split("-")
        (exp[0] + "-" + exp[1]).also { mBinding.expiryDate.text = it }
        when (cardType) {
            AppConstant.POSTPAID_CARD -> {
                mBinding.cardType.text = AppConstant.POSTPAID_CARD_TXT
            }
            AppConstant.PREPAID_CARD -> {
                mBinding.cardType.text = AppConstant.PREPAID_CARD_TXT
            }
            AppConstant.LOYALTY_CARD -> {
                mBinding.cardType.text = AppConstant.LOYALTY_CARD_TEXT
            }
        }
        mBinding.mtrx.text = totalMonthlyCount.toString()
        mBinding.wtrx.text = totalWeeklyCount.toString()
        mBinding.dtrx.text = totalDailyCount.toString()
        mBinding.ceilingLayout.setOnClickListener {
            if(mBinding.ceilingLayoutDetails.visibility == View.VISIBLE)
            {
                mBinding.ceilingLayoutDetails.visibility = View.GONE
                mBinding.expandIconCC.setImageDrawable(AppCompatResources.getDrawable(this,R.drawable.ic_arrow_down))
            }
            else
            {
                mBinding.ceilingLayoutDetails.visibility =View.VISIBLE
                mBinding.expandIconCC.setImageDrawable(AppCompatResources.getDrawable(this,R.drawable.ic_arrow_up))

            }
        }
        mBinding.cardHistoryLayout.setOnClickListener {
            if(mBinding.mListView.visibility == View.VISIBLE)
            {
                mBinding.mListView.visibility = View.GONE
                mBinding.iconCh.setImageDrawable(AppCompatResources.getDrawable(this,R.drawable.ic_arrow_down))
            }
            else
            {
                mBinding.mListView.visibility =View.VISIBLE
                mBinding.iconCh.setImageDrawable(AppCompatResources.getDrawable(this,R.drawable.ic_arrow_up))

            }
        }
    }
    override fun onBackPressed() {

    }
}