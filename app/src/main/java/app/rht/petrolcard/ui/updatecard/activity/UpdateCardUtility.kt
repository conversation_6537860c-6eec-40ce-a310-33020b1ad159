package app.rht.petrolcard.ui.updatecard.activity

import android.app.Activity
import android.os.Build
import android.os.Bundle
import androidx.annotation.RequiresApi
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityCardUtilBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.updatecard.model.*
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extensions.showSnakeBar
import com.afollestad.materialdialogs.MaterialDialog
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.BankCard



@Suppress("DEPRECATION")
class UpdateCardUtility : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private val TAG = UpdateCardUtility::class.java.simpleName
    private lateinit var mBinding: ActivityCardUtilBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    var returnValue=0
    private var mBankCard: BankCard? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String = ""
    var authKey = ""
    var nomPorteur = ""
    private var dateExpirationCard: String? = null
    private var cardExpired = false
    var statusCard = "1"
    private var cardModel: CardResponseModel? = null
    private var infoCarte: String? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_card_util)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        getInitIntentExtras()
        mBinding.updateCardType.setOnClickListener {
            val readCardTask = ReadCardAsyncTask()
            readCardTask.execute()
        }

    }


    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }

    }
    var errorMessage = ""
    @RequiresApi(Build.VERSION_CODES.O)
    override fun setObserver() {
        mViewModel.cardDetailsObserver.observe(this) {
            updateCardDetails(it)
        }
    }
    override fun onInternetEnableDisable(status: Boolean) {
        if(!status)
        {
            showRetryDialog()
        }
    }
    private fun showRetryDialog() {
        if (!(this as Activity).isFinishing) {
            val dialog = MyMaterialDialog(
                applicationContext,
                getString(R.string.error),
                getString(R.string.try_update_card),
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        setBeep()
                        val readCardTask = ReadCardAsyncTask()
                        readCardTask.execute()
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        setBeep()
                        gotoAbortMessageActivity(
                            getString(R.string.error),
                            getString(R.string.transaction_cancelled)
                        )
                    }

                })
        }
    }

    private fun updateCardDetails(it: BaseResponse<CardDetailsResponse>) {
        try {
            if (UtilsCardInfo.verifyPIN(
                    mBankCard,
                    icCpuReader,
                   "0000",
                    this
                )
            ) {
                infoCarte = UtilsCardInfo.updateCarteType(
                    this@UpdateCardUtility,
                    icCpuReader,
                    mBankCard,
                    infoCarte,
                    it.contenu!!.carte!!.type_id!!
                )
            }
        }
        catch (e:Exception)
        {
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
           // mViewModel.generateLogs(e.message!!,0)
            e.printStackTrace()
        }
    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@UpdateCardUtility)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                     infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@UpdateCardUtility) //icCpuReader Not Required for BTPE
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19) else return -1 //Abort Transaction

                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@UpdateCardUtility)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@UpdateCardUtility)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                      return 1
                    }
                }

            }
            catch (e: Exception)
            {
                e.printStackTrace()
            }
            return returnValue
        }
        override fun onPreExecute() {

        }
        override fun onPostExecute(result: Int?) {
            if(result == 1)
            {
                mViewModel.getCardDetails(panNumber,statusCard)

            } else if (result == 0) {
                showSnakeBar(resources.getString(R.string.pin_errone_again))
                finish()
            }
            else if(result == 2)
            {
                finish()
            }
            else if(result == 3)
            {
                gotoAbortMessageActivity(resources.getString(R.string.error), resources.getString(R.string.card_expired))
            }
            else if(result == 4 || result == -1)
            {
                gotoAbortMessageActivity(resources.getString(R.string.error), resources.getString(R.string.read_card_failed))
            }
            else if(result == 5)
            {
                gotoAbortMessageActivity(resources.getString(R.string.card_opposee), resources.getString(R.string.contact_agent))
            }
        }
        override fun onProgressUpdate(vararg values: IntArray) {
        }
    }


    override fun onBackPressed() {

    }

}
