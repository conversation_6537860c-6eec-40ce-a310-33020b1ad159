package app.rht.petrolcard.ui.nfc.activity

import android.app.Activity
import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.os.RemoteException
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.*
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityNfcBinding
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.DebitCardLimitsActivity
import app.rht.petrolcard.ui.nfc.model.NFCEnum
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.card.unlockpin.activity.EnterNewPinActivity
import app.rht.petrolcard.ui.updatemilage.activity.UpdateMilageActivity
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.paxutils.modules.picc.PiccTester
import com.afollestad.materialdialogs.MaterialDialog
import com.pax.dal.entity.EPiccType
import com.usdk.apiservice.aidl.DeviceServiceData
import com.usdk.apiservice.aidl.UDeviceService
import com.usdk.apiservice.aidl.constants.RFDeviceName
import com.usdk.apiservice.aidl.rfreader.OnPassAndActiveListener
import com.usdk.apiservice.aidl.rfreader.URFReader
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.Core
import wangpos.sdk4.libbasebinder.HEX
import java.lang.ref.WeakReference
import java.text.DecimalFormat

class NfcActivity : BaseActivity<CommonViewModel>(CommonViewModel::class){

    private val TAG = NfcActivity::class.simpleName
    private var intentExtrasModel: IntentExtrasModel? = null
    private lateinit var mBinding: ActivityNfcBinding
    lateinit var mIntent: Intent
    var tagNFC: String? = null
    var mBankCard: BankCard? = null
    var listnfcrecord: List<NfcTagModel> = ArrayList<NfcTagModel>()
    var workFlowTransaction: String? = null
    private var isKM = false
    private var m_Kilometre = ""
    private var isOptionnel = false
    private var back = false
    private var itemTagkM: NfcTagModel? = null
    private var vehicleNumber: String? = null
    private var tagUID: String? = null
    private lateinit var piccType: EPiccType
    private lateinit var mScanningNFCTask : ScanningNFCTask
    var stationMode = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_nfc)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getInitIntentExtras()
        setupToolbar()
    }
    private fun setupToolbar()
    {
        mBinding.toolbarNFc.toolbar.tvTitle.text = getString(R.string.vehicle_verification)
        mBinding.toolbarNFc.toolbar.setNavigationOnClickListener {
            mBinding.toolbarNFc.toolbar.isEnabled = false
            if (mBankCard != null) {
                try {
                    if (BuildConfig.POS_TYPE == "B_TPE") {
                        mBankCard!!.breakOffCommand()
                        mBankCard!!.openCloseCardReader(BankCard.CARD_MODE_PICC, 0x02)

                    }
                } catch (ex: RemoteException) {
                    ex.stackTrace
                }
            }
            gotoAbortMessageActivity(getString(R.string.error),getString(R.string.transaction_cancelled),intentExtrasModel!!.mTransaction)
        }
    }
    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        if(intentExtrasModel!!.listnfcrecord != null)
        {
            listnfcrecord=intentExtrasModel!!.listnfcrecord!!
        }
    }
    fun readNfcScan() {
        cancelPreviousNfcScanIfRunning()             //added this condition to resolve ANR issue
        mScanningNFCTask = ScanningNFCTask()
        mScanningNFCTask.execute()
    }

    private fun cancelPreviousNfcScanIfRunning(){
        try {
            if(::mScanningNFCTask.isInitialized) {
                mScanningNFCTask.cancel(true)
            }
        } catch (e:Exception){e.printStackTrace()}
    }
    override fun onStop() {
        cancelPreviousNfcScanIfRunning()
        super.onStop()
    }

    override fun onStart() {
        super.onStart()
        readNfcScan()
    }


    override fun onResume() {
        super.onResume()
        Support.setNavigationStatus(this, false)
    }

    inner class ScanningNFCTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            var respdata = ByteArray(28)
            var resplen  = IntArray(1)
            var retvalue = 0
            var retvalueCard = 0
            var sn =  ByteArray(16)
            var pes = IntArray(1)
            var resSN = 0
            var result : Int = 0

            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    val ctx = WeakReference(this@NfcActivity).get()
                    mBankCard = BankCard(ctx)
                    tagUID = null
                    if (mBankCard != null) retvalue = mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL,
                        BankCard.CARD_MODE_PICC,
                        60,
                        respdata,
                        resplen,
                        AppConstant.TPE_APP
                    )

                    if (mBankCard != null) resSN = mBankCard!!.getCardSNFunction(sn, pes)
                    tagUID = HEX.bytesToHex(sn)
                } else if (BuildConfig.POS_TYPE.equals("PAX",ignoreCase = true)) {
                    var i = 0
                    piccType = EPiccType.INTERNAL
                    PiccTester.getInstance(piccType).open()
                    var tag: String = PiccTester.getInstance(piccType).detectPaxTAG()
                    while (tag.equals("", ignoreCase = true) && i < 20) {
                        i++
                        Thread.sleep(500)
                        tag = PiccTester.getInstance(piccType).detectPaxTAG()
                    }
                    if (tag.isNotEmpty()) tagUID = tag
                }
            }
            catch (e: RemoteException) {
                e.printStackTrace()
                result = 3
            }
            catch (e: InterruptedException) {
                e.printStackTrace()
                result = 3
            }
            var nfcLength = 0
            var minLength = 5
            var maxLength = 14

            if(BuildConfig.POS_TYPE == "PAX" && tagUID != null)
            {
                nfcLength = tagUID!!.length
            }
            else if(BuildConfig.POS_TYPE == "B_TPE")
            {
                nfcLength =  pes[0]
            }
            if(prefs.getReferenceModel()!!.station!!.nfc_read_min_length != null) {
                minLength = prefs.getReferenceModel()!!.station!!.nfc_read_min_length!!
                maxLength = prefs.getReferenceModel()!!.station!!.nfc_read_max_length!!
            }
            if (nfcLength in minLength..maxLength) {

                setBeep()
                log("equals", "pes[0] == 7")
                if (tagUID!!.length >= 14) {
                    tagNFC = tagUID!!.substring(0, 14)
                } else {
                    tagNFC = tagUID!!
                }
                log("tagNFC =>", "tagNFC--->>>${tagNFC}")
                val itemT: NfcTagModel? = UtilsCardInfo.searchforNFCTag(
                    listnfcrecord,
                    NFCEnum.NFC,
                    tagNFC
                )
                if(itemT != null && itemT.nfc != null) {
                    result = 1

                    vehicleNumber = Utils.convertHexToString(itemT.matricule).trim()

                    intentExtrasModel!!.tagsNFC = tagNFC
                    intentExtrasModel!!.vehicleNumber = vehicleNumber
                    if(intentExtrasModel!!.mTransaction!= null && vehicleNumber!=null && vehicleNumber!!.isNotEmpty()){
                        intentExtrasModel!!.mTransaction!!.vehicleNumber = vehicleNumber
                        intentExtrasModel!!.mTransaction!!.tagNFC = tagNFC
                    }


                    if (!intentExtrasModel!!.loyaltyTrx && itemT.km!!.isNotEmpty() && (Integer.valueOf(itemT.km!!) == 1 || Integer.valueOf(itemT.km!!) == 2)) {
                        isKM = true
                        itemTagkM = itemT

                    }
                    if (!intentExtrasModel!!.loyaltyTrx && itemT.km!!.isNotEmpty() && Integer.valueOf(
                            itemT.km!!
                        ) == 2
                    )
                        isOptionnel = true
                }
                else
                {
                    result = 0
                }
            }
            else {
                result = 2
            }
            log(TAG,"NFC Scan Result: $result")
            return result
        }
        override fun onPreExecute() {
        }
        override fun onPostExecute(message: Int?) {
            if (back) { }
            else if(message == 0) {
                showRetryDialog(resources.getString(R.string.scan_other_tag))

            }
            else if(message == 1){
                resultOneOperation()
            }
            else if (message == 2) {
                showRetryDialog(resources.getString(R.string.tag_nfc_ko))

                if (!isFinishing && !isDestroyed) {
                    showRetryDialog(resources.getString(R.string.tag_nfc_ko))
                }
            }
            else if (message == 3) {
                setBeep()
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.tag_read_error),intentExtrasModel!!.mTransaction)
            }
        }
        override fun onProgressUpdate(vararg values: IntArray) {

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }

    private fun showRetryDialog(message :String) {
        if (!(this as Activity).isFinishing) {
            val timer = object : CountDownTimer(10000, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                    //here you can have your logic to set text to edittext
                    println("Popup Time Remaining: $minutes:$seconds")
                }

                override fun onFinish() {
                    val unAttendantMode = prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                    if (unAttendantMode) {
                        setBeep()
                        gotoAbortMessageActivity(getString(R.string.error), getString(R.string.transaction_cancelled),intentExtrasModel!!.mTransaction)
                    }
                }
            }
            if (prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
                timer.start()

            val dialog = MyMaterialDialog(
                this,
                getString(R.string.error),
                "" + message,
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        setBeep()
                        readNfcScan()
                        timer.cancel()
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        setBeep()
                        gotoAbortMessageActivity(getString(R.string.error), getString(R.string.transaction_cancelled),intentExtrasModel!!.mTransaction)
                        timer.cancel()
                    }

                })
        }
    }

    fun gotoDebitCardLimitsActivity()
    {
        val mIntent = Intent(this@NfcActivity, DebitCardLimitsActivity::class.java)
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(mIntent)
        finish()
    }
    private fun resultOneOperation() {
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - NFC Verification Success - "+tagNFC))
        intentExtrasModel!!.vehicleVerificationTagId = tagNFC
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.tagNFC = tagNFC
        }
        if(prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!! && intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.REBATE_DISCOUNT &&  intentExtrasModel!!.mTransaction!!.isDiscountTransaction == 1)
        {
            gotoPumpSelectionActivity()
        }
       else if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_UPDATE_MILEAGE) {
            val mIntent = Intent(this, UpdateMilageActivity::class.java)
            mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(mIntent)
            finish()
        }
        else if(intentExtrasModel!!.loyaltyTrx)
        {
            intent = if (prefs.getStationModel()!!.mode_pompiste == AppConstant.CODE) {
                Intent(this, AttendantCodeActivity::class.java)
            } else {
                Intent(this, AttendantTagActivity::class.java)
            }
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            startActivity(intent)
        }
        else if (intentExtrasModel!!.workFlowTransaction == Workflow.OTHER_PRODUCTS || intentExtrasModel!!.workFlowTransaction == Workflow.SHOP_PRODUCTS) {
            gotoDebitCardLimitsActivity()
        }
        else {
            mIntent = Intent(this@NfcActivity, DebitCardLimitsActivity::class.java)
            mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            if (isKM) {
                showMileageDialog(this)
            } else {
                startActivity(mIntent)
                finish()
            }
        }
    }

    private fun gotoPumpSelectionActivity() {
        val i = Intent(this@NfcActivity, PumpSelectionActivity::class.java)
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
        finish()
    }
    var mileageDialog: AlertDialog? = null
    fun showMileageDialog(context: Context?) {
        val ctx = WeakReference(context).get()
        val builder = AlertDialog.Builder(ctx)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_milage, null)
        builder.setView(layout)
        val enterMileage: AppCompatEditText = layout.findViewById(R.id.enterAmount)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val oldKmTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)

        if (itemTagkM != null && itemTagkM!!.kmValeur != null) {

            try {
                val formatter = DecimalFormat("#,###,###",localeFormat)
                val get_value: String = formatter.format(itemTagkM!!.kmValeur!!.trim().toInt().toLong())
                oldKmTxt.text = getString(R.string.old_km) + get_value.replace(",".toRegex(), " ")
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }

        cancelButton.setOnClickListener { v: View? ->
            mileageDialog!!.dismiss()
            setBeep()
            gotoAbortMessageActivity(getString(R.string.error),getString(R.string.transaction_cancelled),intentExtrasModel!!.mTransaction)}
        submitButton.setOnClickListener {
                v: View? ->
            if (!isOptionnel) {
                try {
                    if (enterMileage.text.isNullOrEmpty()) {
                        enterMileage.error = getString(R.string.kilo_plz)
                    } else {
                        m_Kilometre = enterMileage.text.toString()/*.trim { it <= ' ' }*/.replace(" ", "")
                        if (m_Kilometre.length > 6) {
                            enterMileage.error = resources.getString(R.string.kilo_six)
                        } else if (itemTagkM != null && itemTagkM!!.kmValeur != null && m_Kilometre.toDouble() < itemTagkM!!.kmValeur!!.toDouble()) {
                            enterMileage.error = resources.getString(R.string.kilo_depasse)
                        } else {
                            setBeep()
                            mileageDialog!!.dismiss()
                            intentExtrasModel!!.km = m_Kilometre
                            if(intentExtrasModel!!.mTransaction != null) {
                                intentExtrasModel!!.mTransaction!!.kilometrage = m_Kilometre
                            }
                            mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                            startActivity(mIntent)
                            finish()
                        }
                    }
                }
                catch (e: NumberFormatException) {
                    e.printStackTrace()
                    //  showToast(e.message)
                }
            }
            else {
                m_Kilometre =
                    if (enterMileage.text.toString().trim { it <= ' ' }.isNotEmpty()) enterMileage.text.toString().trim { it <= ' ' }.replace(" ", "")
                    else {
                        "0"
                    }
                setBeep()
                intentExtrasModel!!.km = m_Kilometre
                mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(mIntent)
                finish()
            }
        }
        mileageDialog = builder.create()
        mileageDialog!!.show()
    }


    override fun setObserver() {

    }
    override fun onBackPressed() {

    }
}