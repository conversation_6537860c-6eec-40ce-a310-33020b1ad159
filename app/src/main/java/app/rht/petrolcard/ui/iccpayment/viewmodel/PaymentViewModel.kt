package app.rht.petrolcard.ui.iccpayment.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.iccpayment.model.ActivationDetails
import app.rht.petrolcard.ui.iccpayment.model.DiscountResponse
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel


class PaymentViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {
    var activationObservable = MutableLiveData<BaseResponse<ActivationDetails>>()
    var discountObservable = MutableLiveData<BaseResponse<DiscountResponse>>()
    var discountBalanceObservable = MutableLiveData<BaseResponse<DiscountResponse>>()

    fun cardActivationDetails(pan:String) {
         val baseUrl = preferencesHelper.baseUrl.replace("-tpe","")
        requestData(mNetworkService.activationDetails(baseUrl+ NetworkRequestEndPoints.ACTIVATION_DETAILS,
            MainApp.sn!!,pan),
            {
                activationObservable.postValue(it)
            })
    }

    fun validateDiscountBalance(pan:String,amount:String) {
        requestData(mNetworkService.authorizeDiscount(preferencesHelper.baseUrl+ NetworkRequestEndPoints.AUTHORIZE_DISCOUNT,pan,amount,MainApp.sn!!),
            {
                discountObservable.postValue(it)
            })
    }
    fun getDiscountBalance(pan:String) {
        requestData(mNetworkService.getDiscountBalance(preferencesHelper.baseUrl+ NetworkRequestEndPoints.GET_DISCOUNT_BALANCE,pan,MainApp.sn!!),
            {
                discountBalanceObservable.postValue(it)
            })
    }

}
