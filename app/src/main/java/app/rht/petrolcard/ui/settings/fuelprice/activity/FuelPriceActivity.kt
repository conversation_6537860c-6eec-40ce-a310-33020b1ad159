package app.rht.petrolcard.ui.settings.fuelprice.activity

import android.app.AlertDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.AuditDao
import app.rht.petrolcard.database.baseclass.PriceDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.databinding.ActivityFuelPriceBinding
import app.rht.petrolcard.ui.badge.activity.ManagerCodeActivity
import app.rht.petrolcard.ui.badge.activity.ManagerTagActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.model.AuditModel
import app.rht.petrolcard.ui.reference.model.PriceModel

import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.transactionlist.model.OfflineProductsModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException
import java.util.ArrayList

@Suppress("DEPRECATION")
class FuelPriceActivity : BaseActivity<CommonViewModel>(CommonViewModel::class), RecyclerViewArrayAdapter.OnItemClickListener<OfflineProductsModel> {

    private val TAG = FuelPriceActivity::class.simpleName
    private lateinit var mBinding: ActivityFuelPriceBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    private var productList : ArrayList<ProductModel> = ArrayList()
    private var offlineProductsModel : ArrayList<OfflineProductsModel> = ArrayList()
    private var priceModel : PriceModel? = null
    var mSelectedModel: OfflineProductsModel? = null
    var color = "#FF8212"
    var icon = ""

    lateinit var listAdapter :  RecyclerViewArrayAdapter<OfflineProductsModel>

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_fuel_price)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        getAllProductDetails()
        setupRecyclerView()
        setupToolbar()

    }
    private fun setupToolbar()
    {
        mBinding.toolbarofflineList.toolbar.tvTitle.text = getString(R.string.fuel_price)
        mBinding.toolbarofflineList.toolbar.setNavigationOnClickListener {
            mBinding.toolbarofflineList.toolbar.isEnabled = false
            finish()
        }
    }
    private fun setupRecyclerView(){
        mBinding.mListView.removeAllViews()
        listAdapter = RecyclerViewArrayAdapter(offlineProductsModel,this)
        mBinding.mListView.adapter = listAdapter
        listAdapter.notifyDataSetChanged()
    }
    override fun setObserver() {

    }
    private fun getAllProductDetails() {
        offlineProductsModel.clear()
        try {
            val mProductsDao = ProductsDao()
            mProductsDao.open()
            productList = mProductsDao.getFuelProducts()
            mProductsDao.close()
            val mPriceslistDao = PriceDao()
            mPriceslistDao.open()

            if (productList != null && productList.isNotEmpty()) {
                for (prod in productList) {
                    run {
                        priceModel = mPriceslistDao.selectionnerLastPriceByIdProduit(prod.productID)
                        getSubProductList(prod.productID)
                        offlineProductsModel.add(OfflineProductsModel(productsModel = prod, priceModel = priceModel!!,priceFullValue = priceModel!!.unitPrice.toString()+prefs.currency +"/ L",colorCode = color,icon = icon))
                    }
                }
            }
            mPriceslistDao.close()
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun getSubProductList(productNo: Int){
        if(prefs.getFuelProductList() != null) {
            for (product in prefs.getFuelProductList()!!) {
                if (productNo == product.id) {
                    color = product.color_code
                    icon = product.icon
                }
            }
        }
    }

    override fun onItemClick(view: View, model: OfflineProductsModel) {
        mSelectedModel = model
        gotoManagerActivity()
    }
    override fun onBackPressed() {

    }


    //region priceChange
    private fun openPriceChangeDialog(context: Context?) {
        val builder = AlertDialog.Builder(context)
        builder.setTitle("")
        builder.setCancelable(false)

        val layout: View = layoutInflater.inflate(R.layout.dialog_fuel_price, null)
        builder.setView(layout)
        val valueDialog = builder.create()

        val tvAmount: AppCompatEditText = layout.findViewById(R.id.enterValue)
        val tvQty: TextView = layout.findViewById(R.id.tvQty)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)

        val fuelQtyUnit = prefs.getReferenceModel()!!.FUEL_QTY_UNIT!!
        tvQty.text = fuelQtyUnit

        cancelButton.setOnClickListener { _: View? -> valueDialog!!.dismiss() }
        submitButton.setOnClickListener { _: View? ->
            when {
                tvAmount.text.toString().isEmpty() -> { tvAmount.error = getString(R.string.enter_amount) }
                else -> {
                    run {
                        try {
                            val enteredValue = tvAmount.text.toString()
                            mSelectedModel!!.priceModel!!.unitPrice = enteredValue.toDouble()
                            val priceDao = PriceDao()
                            priceDao.open()
                            priceDao.updatePrice(mSelectedModel!!.priceModel!!)
                            priceDao.close()

                            createAudit(mSelectedModel!!.priceModel!!)

                            getAllProductDetails()
                            listAdapter.notifyDataSetChanged()

                        } catch (Ex: SQLException) {
                            Ex.printStackTrace()
                        }
                        valueDialog!!.dismiss()
                    }
                }

            }
        }
        valueDialog!!.show()
    }

    private fun gotoManagerActivity()
    {
        intent = if (prefs.getStationModel()!!.mode_pompiste == AppConstant.CODE) {
            Intent(this, ManagerCodeActivity::class.java)
        } else {
            Intent(this, ManagerTagActivity::class.java)
        }
        intentExtrasModel = IntentExtrasModel()
        intentExtrasModel!!.workFlowTransaction = Workflow.PRICE_CHANGE
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        activityResultLaunch.launch(intent)
    }
    var activityResultLaunch = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppConstant.CARD_NFC_BADGE) {
            intentExtrasModel = result.data!!.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL)
            openPriceChangeDialog(this)
        }
    }

    private fun createAudit(item: PriceModel){
        val auditItem = AuditModel()
        val json = Gson().toJson(auditItem)
        auditItem.activity = "Fuel Price"
        auditItem.action = "Product id ${item.idproduit} Price Changed, new price ${item.unitPrice}, data: $json"
        auditItem.timestamp = getCurrentTimestamp()
        auditItem.performedBy = intentExtrasModel!!.idPompiste

        val auditDao = AuditDao()
        auditDao.open()
        val inserted = auditDao.insertAudit(auditItem)
        auditDao.close()

        Log.e(TAG,"Audit Log created $inserted : $json")
    }
    //endregion
}
