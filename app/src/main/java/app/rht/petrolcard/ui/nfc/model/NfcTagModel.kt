package app.rht.petrolcard.ui.nfc.model

import android.os.Parcel
import android.os.Parcelable
import app.rht.petrolcard.utils.Utils
import java.util.*


open class NfcTagModel : Parcelable {
    var nfc: String?
    var rfid: String? = null
    var gps: String? = null
    var km: String?
    var matricule: String?
    var kmValeur: String? = null
    var pad: String? = null
    var position = 0
    var index = 0


    constructor(nfc: String?, km: String?, matricule: String?) {
        this.nfc = nfc
        this.km = km
        this.matricule = matricule
    }

    constructor(
        nfc: String?,
        rfid: String?,
        gps: String?,
        km: String?,
        matricule: String?,
        kmValeur: String?,
        position: Int
    ) {
        this.nfc = nfc
        this.rfid = rfid
        this.gps = gps
        this.km = km
        this.matricule = matricule
        this.kmValeur = kmValeur
        this.position = position
    }

    constructor(
        nfc: String?,
        rfid: String?,
        gps: String?,
        km: String?,
        matricule: String?,
        kmValeur: String?,
        position: Int,
        pad: String?
    ) {
        this.nfc = nfc
        this.rfid = rfid
        this.gps = gps
        this.km = km
        this.matricule = Utils.encodeHexString(Utils.matriculepad(matricule!!)).uppercase(Locale.getDefault())
        this.kmValeur = Utils.kmValeurpad(kmValeur!!) // used one
        this.position = position
        this.pad = pad
    }

    override fun toString(): String {
        return "Nfctags{nfc=$nfc, rfid=$rfid, gps=$gps, km=$km, matricule=$matricule, kmValeur=$kmValeur, pad=$pad, Position $position}"
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
        dest.writeString(nfc)
        dest.writeString(rfid)
        dest.writeString(gps)
        dest.writeString(km)
        dest.writeString(matricule)
        dest.writeString(kmValeur)
        dest.writeString(pad)
        dest.writeInt(position)
        dest.writeInt(index)
    }

    protected constructor(`in`: Parcel) {
        nfc = `in`.readString()
        rfid = `in`.readString()
        gps = `in`.readString()
        km = `in`.readString()
        matricule = `in`.readString()
        kmValeur = `in`.readString()
        pad = `in`.readString()
        position = `in`.readInt()
        index = `in`.readInt()
    }

    companion object CREATOR : Parcelable.Creator<NfcTagModel> {
        override fun createFromParcel(parcel: Parcel): NfcTagModel {
            return NfcTagModel(parcel)
        }

        override fun newArray(size: Int): Array<NfcTagModel?> {
            return arrayOfNulls(size)
        }
    }


}
enum class NFCEnum {
    NFC, Matricule
}