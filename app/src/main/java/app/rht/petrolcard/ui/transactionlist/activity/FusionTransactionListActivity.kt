package app.rht.petrolcard.ui.transactionlist.activity

import android.app.AlertDialog
import android.content.*
import android.graphics.Typeface
import android.os.Bundle
import android.text.Html
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.databinding.ActivityTransactionListBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.ModePayActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.transactionlist.model.*
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.OFFLINE_TRX_MODE
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.tax.TaxModel
import app.rht.petrolcard.utils.tax.TaxUtils
import com.altafrazzaque.ifsfcomm.*
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.w3c.dom.Document
import org.xml.sax.SAXException
import java.io.*
import java.net.Socket
import java.nio.charset.StandardCharsets
import java.util.*
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.parsers.ParserConfigurationException
import kotlin.collections.ArrayList


class FusionTransactionListActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<TransactionFromFcc>,View.OnClickListener  {
    private lateinit var mBinding: ActivityTransactionListBinding
    private var TAG = FusionTransactionListActivity::class.simpleName
    private var stationMode=0
    private var intentExtrasModel: IntentExtrasModel? = null
    private var transactionToPay: TransactionFromFcc? = null
    private var carburant: String? = null
    private var volume: String? = null
    private var montant: String? = null
    private val mReponseFusion: ResponseFusion? = null
    private var fusionProductList: ArrayList<ProductPT> = ArrayList()
    private var mTerminal: TerminalModel? = null
    private var transactionList: ArrayList<TransactionFromFcc> = java.util.ArrayList()
    private var fuelTransactionStatusDAO: FCCTransactionsDao? = null
    private var mTransaction: TransactionModel? = null
    private var mProduitDAO: ProductsDao? = null
    var selectedProduct: ProductModel? = null
    private var mTransactionDAO: TransactionDao? = null
    var daoError = false
    var socket: Socket? = null
    var command: String? = null
    var response: String? = null
    var produit: String? = null
    var pump = 0
    var amount = 0.0
    var quantite = 0.0
    var idterminal = 0
    var result = false
    var fusionFuelTrxList: MutableList<TransactionFromFcc> = ArrayList()
    lateinit var fusion : FusionModel
    var decimal  = 2
    lateinit var mOperationsAdapter : RecyclerViewArrayAdapter<TransactionFromFcc>
    var fuelQtyUnit = "L"
    var trxCount = 0
    var pumpSize = 0
    var fiscalPrinterModel: FiscalPrinterModel? = null
    var rfidPumpsModel = ArrayList<RFIDPumpsModel>()
    var isSigned = false
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_transaction_list)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getIntentData()
        setupToolbar()

        showTransactionsInRecyclerView()
         FusionService.getProductTable()
            val type: Int = fusion.COMM_TYPE
            if (type == 1) {
                startIfsfReceiver()
              //  FusionService.getDSPConfigurationList() should implement
                searchTransactionsInFusion()
            } else if (type == 2) {
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_communication_type))
                //SearchTransactionUsingTcp().execute() Later integrate this
            }
    }

    private fun getIntentData() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        fusion = prefs.getFusionModel()!!
        val referenceModel = prefs.getReferenceModel()
        mTerminal = referenceModel!!.terminal
        fiscalPrinterModel = referenceModel.fiscal_printer
        fuelQtyUnit = referenceModel.FUEL_QTY_UNIT!!
        if (referenceModel.RFID_TERMINALS != null) {
            rfidPumpsModel.clear()
            rfidPumpsModel.addAll(referenceModel.RFID_TERMINALS.pumps)
        }
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if (intentExtrasModel!!.loyaltyTrx) {
                stationMode = 1
            }
        }
        transactionToPay = null
    }

    private fun setupToolbar()
    {
        mBinding.toolbarTransactionList.toolbar.tvTitle.text = resources.getString(R.string.transaction_list_title)
        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.setOnClickListener(this)
        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.setImageResource(R.drawable.ic_baseline_refresh_24)
        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.visibility = View.VISIBLE
        mBinding.toolbarTransactionList.toolbar.setNavigationOnClickListener {
        mBinding.toolbarTransactionList.toolbar.isEnabled = false
        setBeep()
        gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel)) }
    }
    private fun startIfsfReceiver() {
        val filter = IntentFilter()
        filter.addAction(ACTION_IFSF_READ_DATA)
        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
        registerReceiver(ifsfRecever, filter)
    }
    var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            try {
                if (action == ACTION_IFSF_READ_DATA) {
                    val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
                    performNextStepProductTask(msg!!)
                }
                if (action == ACTION_IFSF_CONNECTION_STATE) {
                    val state = intent.getStringExtra(IFSF_CONNECTION_STATE)
                    log(TAG, "Received: $state")
                    if (state == "TIMEOUT") {
                        //gotoOfflineModePage()
                        showNoTrxLayout(true, getString(R.string.unable_to_connect_fcc))
                    }
                    if (state == "Connected") {
                        //searchTransactionsInFusion()
                    }
                }
            } catch (e: Exception) {
                log(TAG, "ERROR IN BR RECEIVER")
                e.printStackTrace()
                log(TAG, "Exception: " + e.message)
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
              //  mViewModel.generateLogs(e.message!!,0)
            }
        }
    }

    private fun performNextStepProductTask(message: String) {

        var message = message
        val errorCode: String = StringUtils.substringBetween(message, "<ErrorCode>", "</ErrorCode>")
        val errorMessage = FusionService.getFusionErrorMessage(errorCode)

        if(errorCode!=null && !errorCode.contains("ERRCD_OK") && !errorCode.contains("ERRCD_NOTRANS")) {
            val errMsg = FusionService.getErrorMessage(errorCode)
            showSnakeBarColor("$errMsg ")
        }

        if (errorCode.contains("ERRCD_OK")) {
            if (message.contains("FDCMessage")) {
                val messageType = StringUtils.substringBetween(message, "MessageType=\"", "\"")
                if (messageType.equals("FuelSaleTrx")) {
                    Log.i(TAG,"FuelSaleTrx Received :: "+messageType)
                    getFccTransactions()
                }
            }
            else if (message.contains("ServiceResponse")) {
                message = formatMessage(message)
                val overallResult: String =
                    StringUtils.substringBetween(message, "OverallResult=\"", "\"")
                val requestType: String = StringUtils.substringBetween(message, "RequestType=\"", "\"")
                val gson = Gson()
                if (overallResult == "Success" && errorCode == "ERRCD_OK") {
                    val jsonString: String = Support.xmlToJsonString(message)!!
                    when (requestType) {
                        "GetFuelSaleTrxDetails" -> {
                            try {
                                trxCount ++
                                log(TAG,"trxCount:: $trxCount")
                                GetFuelSaleTrxDetails(message,requestType)
                                log(TAG, "GetFuelSaleTrxDetails:: $message")
                            } catch (e: JsonSyntaxException) {
                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                                e.printStackTrace()
                            }
                        }
//                        "FuelSaleTrx" -> {
//                            try {
//                                GetFuelSaleTrxDetails(message,requestType)
//                            } catch (e: JsonSyntaxException) {
//                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                                e.printStackTrace()
//                            }
//                        }
                        "GetProductTable" -> {
                            try {
                                val transactions: ServiceResponseGPT = gson.fromJson(jsonString, ServiceResponseGPT::class.java)
                                fusionProductList.addAll(transactions.serviceResponse.fDCdata.fuelProducts.product)
                                val fusionProductList1 = Gson().toJson(transactions)
                                if (fusionProductList.isNotEmpty()) {
                                    FusionService.prefs.saveProductTable(fusionProductList1)
                                    log(TAG, "Product table update: $fusionProductList1")
                                }
                            } catch (e: Exception) {
                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                                log(TAG, "Error occurred while fetching products from fusion: " + e.message)
                                e.printStackTrace()
                            }
                        }
                        "GetDSPConfiguration" -> {
                            try {
                                log(TAG,"DSP CONFIG::: $jsonString")
                                dspConfiguration = gson.fromJson(jsonString, DSPConfiguration::class.java)
                                val devices = dspConfiguration.serviceResponse.fDCdata.deviceClasses
                                for(device in devices)
                                {
                                    val products = device.products
                                    for(product in products)
                                    {
                                        if(!productColors.contains(product))
                                        {
                                            productColors.add(product)
                                            log(TAG,"##### Product color added in list (Product: ${product.productName} ---- #${product.productColour})")
                                        }
                                    }
                                }
                            } catch (e: Exception) {
                                log(TAG, "Error occurred while fetching products from fusion: " + e.message)
                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                                e.printStackTrace()
                            }
                        }
                    }
                } else {
                    showToast(overallResult)
                    finish()
                }
            }
        }
        else
        {
            trxCount ++
            log(TAG,"trxCount:: $trxCount")
            if(trxCount == pumpSize)
            {
                getFccTransactions()
            }

        }
//        else {
//            log(TAG, errorMessage)
//        }
    }

    private fun gotoOfflineModePage() {
        if (stationMode != 3) {
            if (mReponseFusion?.error != null) showToast( mReponseFusion.error)
            else showToast(resources.getString(R.string.offline))
        } else {
            showToast( resources.getString(R.string.no_transaction_found))
        }
        // go directly to offline mode, no need to put a popup
        val intent = Intent(applicationContext, OfflineTransactionListActivity::class.java)
       intentExtrasModel!!.stationMode = OFFLINE_TRX_MODE
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        applicationContext.startActivity(intent)
      //  finish()
    }
    private fun searchTransactionsInFusion() {
        showProgress(true)
        clearFccFuelTrxList()
        trxCount=0
        pumpSize=0
        mBinding.progressMessage.visibility = View.VISIBLE
        mBinding.progressMessage.setText(R.string.searching_transactions)
            log(TAG,"sending ifsf command to get latest transactions from fusion")

            Timer().schedule(object : TimerTask() {
                override fun run() {
                    if (!rfidPumpsModel.isNullOrEmpty()) {
                         pumpSize = rfidPumpsModel.size
                        if (pumpSize > 0 ) {
                            for (pumpsModel in rfidPumpsModel) {
                                transactionList.clear()
                                FusionService.GetAllFuelSaleTrxDetails(pumpsModel.pump_number.toString())
                            }
                        } else {
                            log( TAG, "Pump List Not Available")
                            showNoTrxLayout(true,getString(R.string.pumps_not_mapped_to_this_terminal))
                        }
                        log(TAG, "pumpsModels from API :: $rfidPumpsModel")
                    }

                }
            }, 1000)

    }
    //endregion
    private fun showProgress(isVisible: Boolean) {
        if (isVisible) {
            mBinding.progressBarLayout.visibility = View.VISIBLE
            mBinding.listViewLayout.visibility = View.GONE
         //   onDisableTouchEvents()
        } else {
            mBinding.progressBarLayout.visibility = View.GONE
            mBinding.listViewLayout.visibility = View.VISIBLE

         //   onEnableTouchEvents()
        }
    }
    private fun getFusionProductName(productNo: Int): String {
        if (fusionProductList.isNotEmpty()) {
            for (product in fusionProductList) {

                if (productNo == product.productNo) {
                    return product.productName
                }
            }
        }
        return ""
    }
    private lateinit var dspConfiguration: DSPConfiguration
    private var productColors: ArrayList<ProductDSP> = ArrayList()
    private fun getFusionProductColor(productNo: Int): String {
        for (product in productColors) {
            if (productNo == product.productNo) {
                return "#${product.productColour}"
            }
        }
        return "#FF8212"
    }

    private fun addTransactionsInDB(fusionTransaction:TransactionFromFcc) {
        mBinding.progressMessage.text = ""
        mBinding.progressMessage.visibility = View.VISIBLE
        fuelTransactionStatusDAO = FCCTransactionsDao()
        fuelTransactionStatusDAO!!.open()
        fuelTransactionStatusDAO!!.insert(fusionTransaction)
        fuelTransactionStatusDAO!!.close()
    }
    fun getFccTransactions()
    {
        fusionFuelTrxList.clear()
        transactionList.clear()
        fuelTransactionStatusDAO = FCCTransactionsDao()
        fuelTransactionStatusDAO!!.open()
        fusionFuelTrxList = fuelTransactionStatusDAO!!.getAllTransactionList()

        fuelTransactionStatusDAO!!.close()
        if (fusionFuelTrxList.isNotEmpty()) {
            var size = fusionFuelTrxList.size
            if (fusionFuelTrxList.size > 20) size = 20 //Commented this for missing trx
            for (i in 0 until size) {
                transactionList.add(fusionFuelTrxList[i])
            }
        }
        updateRecyclerView()
    }
    private fun showTransactionsInRecyclerView() {
            showProgress(true)
            Collections.sort(transactionList, TransactionFromFccComparator())
            mOperationsAdapter = RecyclerViewArrayAdapter(transactionList,this)
            mBinding.mListView.adapter = mOperationsAdapter
            mOperationsAdapter.notifyDataSetChanged()

    }

    private fun clearFccFuelTrxList() {
        fusionFuelTrxList.clear()
        mBinding.progressMessage.visibility = View.GONE
    }

    override fun setObserver() {

    }

    override fun onItemClick(view: View, model: TransactionFromFcc) {
        MultiClickPreventer.preventMultiClick(view)
        prefs.isSignInBackground =false
        if(view.id == R.id.transactionListLayout) {
            UtilsCardInfo.beep(mCore, 10)
            transactionToPay = model
            prefs.saveTransactionModel(model)
           log(TAG, " selectedTransactionToPay :: "+gson.toJson(transactionToPay))

            if (transactionToPay != null) {
                carburant = transactionToPay!!.produit
                volume = transactionToPay!!.quantite.toString()
                montant = montant+ " " + transactionToPay!!.currency

                if(fuelVat.enabled){
                    showTaxAmountDialog(carburant!!,volume!!, transactionToPay!!.amount.toString())
                }
                else {
                    showPaymentDialog(carburant!!,volume!!, transactionToPay!!.amount.toString())
                }

            } else {
                showToast(resources.getString(R.string.no_trx_selected))
            }
        }
    }

    private fun showPaymentDialog(productName:String, qty: String, amount:String){
        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)
        builder.setMessage(
            Html.fromHtml(
                "<font color='#000000'>" + resources.getString(R.string.validate_transaction) + "</font>" +
                        "<br/><br/>"
                        + resources.getString(R.string.label_carburant) + " : <strong>" + productName + "</strong>" +
                        "<br/>" +
                        "Volume : <strong>" + qty + "</strong>" +
                        "<br/>" +
                        resources.getString(R.string.amount_pay) + " : <strong>" + amount + "</strong>"
            )
        )
        builder.setCancelable(false)
        builder.setNegativeButton(
            resources.getString(R.string.no)
        ) { dialog, which ->
            prefs.isSignInBackground =true
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
        }
        builder.setPositiveButton(
            resources.getString(R.string.yes)
        ) { dialog, which ->
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
            getProductDetails(transactionToPay!!.productId!!)
        }
        val alert = builder.create()
        alert.show()
        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        nbutton.setTextColor(
            ContextCompat.getColor(this, R.color.redLight)
        )
        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        nbutton.textSize = 20f
        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        pbutton.setTextColor(
            ContextCompat.getColor(this, R.color.greenLight)
        )
        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        pbutton.textSize = 20f
    }
    var taxModel: TaxModel? = null
    private fun showTaxAmountDialog(productName:String, qty: String, amount:String) {
        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)

        var isInclusive = true
        var type = "Incl."
        taxModel = if(fuelVat.enabled) {
            isInclusive = fuelVat.type == 0
            type = if(isInclusive) "Incl." else  "Excl."
            TaxUtils.calculate(amount.toDouble(),fuelVat.percentage!!.toDouble(),isInclusive)
        } else /*if(shopVat.enabled)*/ {
            isInclusive = shopVat.type == 0
            type = if(isInclusive) "Incl." else  "Excl."
            TaxUtils.calculate(amount.toDouble(),shopVat.percentage!!.toDouble(),isInclusive)
        }

        builder.setMessage(Html.fromHtml(("<font color='#000000'>" + resources.getString(R.string.validate_transaction) + "</font><br/><br/>" +
                getString(R.string.label_carburant) + " : <strong>" + productName + "</strong><br/>" +
                getString(R.string.qty) + " : <strong>" + "$qty $fuelQtyUnit" + "</strong><br/><br/>" +
                getString(R.string.net_amount) + " : <strong>" + Support.formatString(taxModel!!.netAmount) + " " + prefs.currency + "</strong>" + "<br/>" +
                getString(R.string.tax_amount) + " (${taxModel!!.taxPercentile}% $type)" + " : <strong>" + Support.formatString(taxModel!!.taxAmount).toString() + " " + prefs.currency + "</strong>" + "<br/><br/>" +
                getString(R.string.total_amount) + " : <strong>" + taxModel!!.totalAmount.toString() + " " + prefs.currency + "</strong>"
                )))
        builder.setCancelable(false)
        builder.setNegativeButton(resources.getString(R.string.no)) { dialog, which ->
            prefs.isSignInBackground =true
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
        }
        builder.setPositiveButton(resources.getString(R.string.yes)) { dialog, which ->
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
            intentExtrasModel!!.taxModel = taxModel
            getProductDetails(transactionToPay!!.productId!!)
        }
        val alert = builder.create()
        alert.show()
        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        nbutton.setTextColor(ContextCompat.getColor(this, R.color.redLight))
        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        nbutton.textSize = 20f
        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        pbutton.setTextColor(ContextCompat.getColor(this, R.color.greenLight))
        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        pbutton.textSize = 20f
    }
    //endregion
    override fun onClick(view: View) {
        MultiClickPreventer.preventMultiClick(view)
        UtilsCardInfo.beep(mCore, 10)
        when (view.id) {
            R.id.toolbar_right_image -> {
                refreshTransactions()
            }
        }
    }
    private fun refreshTransactions() {
        transactionList.clear()
        showProgress(true)
        mBinding.progressMessage.text = getString(R.string.fetching_latest_transactions)
            val type: Int = fusion.COMM_TYPE
            if (type == 1) {
                searchTransactionsInFusion()
            } else if (type == 2) {
                //SearchTransactionUsingTcp().execute() Later Integrate this
        }
    }

    fun goToPayment(selectedProduct: ProductModel?) {
        val intent1 = Intent(this, ModePayActivity::class.java) // commented
        intentExtrasModel!!.mTransaction=mTransaction
        intentExtrasModel!!.selectedProduct=selectedProduct
        log(TAG,"mTransaction:: "+gson.toJson(mTransaction))
        intent1.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        log(TAG,"isLoyalty:: "+intentExtrasModel!!.loyaltyTrx)
        startActivity(intent1)
    }

    private fun stopIfsfReceiver() {
        try {
            unregisterReceiver(ifsfRecever)
        } catch (e: Exception) {
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
         //   mViewModel.generateLogs(e.message!!,0)
        }
       log(TAG, "IFSF RECEIVER STOPPED")
    }

    private fun getProductDetails(productChosen:Int) {
        try {
            mProduitDAO = ProductsDao()
            mProduitDAO!!.open()
            selectedProduct = mProduitDAO!!.getProductById(productChosen)
           log(TAG, "productChosen::: $productChosen")
           log(TAG,"getAllProducts ::: "+mProduitDAO!!.getAllProducts())
           log(TAG, "selectedProduct ::: $selectedProduct")
            mProduitDAO!!.close()
            getTransactionDetails()
        } catch (ex: SQLiteException) {
            daoError = true
            performNextStepProductTask(false)
            ex.printStackTrace()
            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
          //  mViewModel.generateLogs(ex.message!!,0)
        }
    }
    private fun getTransactionDetails() {
        if (selectedProduct != null) {
            saveTransactionDetails()
        } else {
            showDialog(getString(R.string.product_mismatch),getString(R.string.this_product_not_available_on_server))
           log(TAG, "### ### ### product => null")
        }
    }

    private fun saveTransactionDetails() {

        try {

            if(transactionToPay != null && transactionToPay!!.fusionSaleId!!.isNotEmpty()) {
                mTransactionDAO = TransactionDao()
                mTransactionDAO!!.open()
                mTransaction = mTransactionDAO!!.checkAvailableTransactions(transactionToPay!!.pump.toString(),transactionToPay!!.ref_transaction!!)!!

                if(mTransaction != null && mTransaction!!.reference != null)
                {
                    if(taxModel != null)
                    {
                        mTransaction!!.vatAmount = Support.formatString(taxModel!!.taxAmount)
                        mTransaction!!.netAmount = Support.formatString(taxModel!!.netAmount)
                        mTransaction!!.amount =  taxModel!!.totalAmount
                        if(fuelVat.enabled || shopVat.enabled)
                        {
                            mTransaction!!.vatPercentage =prefs.getReferenceModel()!!.fuelVat!!.percentage
                            mTransaction!!.vatType =prefs.getReferenceModel()!!.fuelVat!!.type
                        }
                    }
                    mTransaction!!.fccProductId = selectedProduct!!.fcc_prod_id.toString()
                    mTransaction!!.categoryId =intentExtrasModel!!.categoryId
                    mTransaction!!.idTerminal = mTerminal!!.terminalId
                    mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
                    mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
                    mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
                    mTransaction!!.fccSaleId = transactionToPay!!.fusionSaleId
                    mTransaction!!.flagTelecollecte = 0
                    mTransactionDAO!!.updateTransactionsByReferenceID(mTransaction!!)
                }
                else
                {
                    mTransaction = TransactionModel()
                    if(taxModel != null)
                    {
                        mTransaction!!.vatAmount =Support.formatString(taxModel!!.taxAmount)
                        mTransaction!!.netAmount = Support.formatString(taxModel!!.netAmount)
                        mTransaction!!.amount =  taxModel!!.totalAmount
                        if(fuelVat.enabled || shopVat.enabled)
                        {
                            mTransaction!!.vatPercentage =prefs.getReferenceModel()!!.fuelVat!!.percentage
                            mTransaction!!.vatType =prefs.getReferenceModel()!!.fuelVat!!.type
                        }
                    }
                    mTransaction!!.categoryId =intentExtrasModel!!.categoryId
                    mTransaction!!.idTerminal = mTerminal!!.terminalId
                    mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
                    mTransaction!!.idProduit = selectedProduct!!.productID
                    mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
                    mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
                    mTransaction!!.dateTransaction = transactionToPay!!.dh_transaction
                    mTransaction!!.amount =  transactionToPay!!.amount
                    mTransaction!!.quantite = transactionToPay!!.quantite
                    mTransaction!!.unitPrice = transactionToPay!!.pu
                    mTransaction!!.flagController = 1
                    mTransaction!!.sequenceController = transactionToPay!!.ref_transaction
                    mTransaction!!.kilometrage = ""
                    mTransaction!!.pan = intentExtrasModel!!.panNumber
                    mTransaction!!.flagTelecollecte = 0
                    mTransaction!!.pumpId = transactionToPay!!.pump.toString()
                    mTransaction!!.fccSaleId = transactionToPay!!.fusionSaleId
                    mTransaction!!.transactionStatus = 0
                    mTransaction!!.productName = selectedProduct!!.libelle
                    mTransaction!!.reference = "TRX" + Support.generateReference(this@FusionTransactionListActivity)/*Support.generateReference(mTransaction!!.dateTransaction, "", "", this@TransactionListActivity)*/
                    mTransaction!!.fccProductId = selectedProduct!!.fcc_prod_id.toString()
                    mTransaction!!.timsSignDetails!!.fuelQtyUnit = transactionToPay!!.fuelQtyUnit
                    mTransaction!!.timsSignDetails!!.hsCode = transactionToPay!!.hsCode
                    intentExtrasModel!!.mTransaction = mTransaction
                    insertTransactionData(mTransaction!!)
                }
                log(TAG, "mTransaction !!! --> " + gson.toJson(mTransaction))
                mTransactionDAO!!.close()
                performNextStepProductTask(true)
            } else {
                showDialog(getString(R.string.error),getString(R.string.sale_id_is_missing_please_enable_sale_Id_in_fcc))
            }


        } catch (ex: SQLiteException) {
            ex.printStackTrace()
            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
            // mViewModel.generateLogs(ex.message!!,0)
        }
    }
    private fun performNextStepProductTask(isTrue:Boolean) {
        when {
            isTrue -> {
                goToPayment(selectedProduct)
            }
            daoError -> {
                UtilsCardInfo.beep(mCore, 10)
                showDialog(resources.getString(R.string.error),getString(R.string.data_error))
            }
            else -> {
                showDialog(resources.getString(R.string.error), resources.getString(R.string.no_product))
            }
        }
        showProgress(false)
    }
    override fun onStop() {
        if(fusion.EXIST)
            stopIfsfReceiver()
        super.onStop()
    }
    override fun onBackPressed() {

    }

    private fun showNoTrxLayout(isVisible: Boolean, title:String ="") {
        mBinding.tvFccConnectionMessage.text = getString(R.string.no_transaction_found)
        if (isVisible) {
            mBinding.noTransactionLayout.visibility = View.VISIBLE
        } else {
            mBinding.noTransactionLayout.visibility = View.GONE
            if(title.isNotEmpty() ){
                mBinding.tvFccConnectionMessage.text = title.replace(":","")
            }
        }
    }
    fun updateRecyclerView() {
        showProgress(false)
        try {
            if (transactionList.isNotEmpty()) {
                Collections.sort(transactionList, TransactionFromFccComparator())
                mOperationsAdapter.notifyDataSetChanged()
                clearFccFuelTrxList()
            } else {
                showNoTrxLayout(true)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    fun noFccConnLayoutButtonClick(view: View) {
        setBeep()
        when(view){
            mBinding.btnRefreshTrx -> {
                showNoTrxLayout(false)
                showProgress(true)

                refreshTransactions()
            }
            mBinding.btnOfflinePage -> {
                gotoOfflineModePage()
            }
            mBinding.btnHome -> {
                val mIntent: Intent =
                    if (prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
                        Intent(this, UnattendantModePayActivity::class.java)
                    } else {
                        Intent(this, MenuActivity::class.java)
                    }

                startActivity(mIntent)
               // finish()
            }
        }
    }
    private fun getProductModel(id:Int) : ProductModel? {
        var product : ProductModel? = null
        try {
            val productDAO = ProductsDao()
            productDAO.open()
            product = productDAO.getProductByFCCId(id)
            productDAO.close()

        } catch (e:Exception) {
            e.printStackTrace()
        }
        return product
    }
    private fun GetFuelSaleTrxDetails(message: String,requestType:String) {
        try {

            val stream = ByteArrayInputStream(message.toByteArray(StandardCharsets.UTF_8))
            try {
                val trxDetails = ArrayList<TransactionFromFcc>()
                val dbFactory = DocumentBuilderFactory.newInstance()
                val dBuilder = dbFactory.newDocumentBuilder()
                val doc: Document = dBuilder.parse(stream)
                val element = doc.documentElement
                element.normalize()
                val nList = doc.getElementsByTagName("DeviceClass")
                if(nList.length > 0)
                {
                for (i in 0 until nList.length) {
                    val node = nList.item(i)
                    if (node.hasAttributes()) {
                        val attributes = node.attributes
                        val type = attributes.getNamedItem("Type").nodeValue
                        if (type == "FP") // PUMP
                        {
                            val pumpNo= attributes.getNamedItem("PumpNo").nodeValue
                            val transactionSeqNo = attributes.getNamedItem("TransactionSeqNo").nodeValue
                            val nozzleNo = attributes.getNamedItem("NozzleNo").nodeValue
                            val fusionSaleId = attributes.getNamedItem("FusionSaleId").nodeValue
                            /* Start Check Product No available in RFID Terminal  List*/
                            for (pumpsModel in rfidPumpsModel) {
                            for(nozzle in pumpsModel.nozzles)
                            {
                                if(nozzle.fcc_prod_id.toString() == nozzleNo)
                                {
                                    /* Start Check Transaction Available on DB */
                                    val transactionDao = TransactionDao()
                                    transactionDao.open()
                                    fuelTransactionStatusDAO = FCCTransactionsDao()
                                    fuelTransactionStatusDAO!!.open()
                                    if (transactionDao.checkTransactionCount(fusionSaleId) == 0 && fuelTransactionStatusDAO!!.isTransactionAvailable(fusionSaleId) == 0) {
                                        val trx = TransactionFromFcc()
                                        /*  Start Attributes of Device Class  */
                                        if(attributes.getNamedItem("PumpNo").nodeValue != null)  trx.pump = attributes.getNamedItem("PumpNo").nodeValue.toInt()
                                        if(attributes.getNamedItem("NozzleNo").nodeValue != null)   trx.hose = attributes.getNamedItem("NozzleNo").nodeValue.toInt()
                                        trx.fusionSaleId = attributes.getNamedItem("FusionSaleId").nodeValue
                                        trx.ref_transaction = attributes.getNamedItem("TransactionSeqNo").nodeValue

                                        /*  End Attributes of Device Class  */

                                        /*  Start Nodes of FDC Data  */
                                        val childNodes = node.childNodes
                                        if(childNodes.length > 0) {
                                            for (j in 0 until childNodes.length) {
                                                val child = childNodes.item(j)
                                                when (child.nodeName) {
                                                    "ReleaseToken" -> {
                                                        trx.releaseToken = child.firstChild.nodeValue
                                                    }
                                                    "ProductNo" -> {

                                                        if(child.firstChild.nodeValue != null) trx.fccProductId = child.firstChild.nodeValue.toInt()
                                                    }
                                                    "Amount" -> {
                                                        if(child.firstChild.nodeValue != null) trx.amount = child.firstChild.nodeValue.toDouble()
                                                    }
                                                    "Volume" -> {
                                                        if(child.firstChild.nodeValue != null)  trx.quantite = child.firstChild.nodeValue.toDouble()
                                                    }
                                                    "UnitPrice" -> {
                                                        if(child.firstChild.nodeValue != null)  trx.pu = child.firstChild.nodeValue.toDouble()
                                                    }
                                                    "ProductName" -> {
                                                        if(child.firstChild.nodeValue != null) trx.produit = child.firstChild.nodeValue else getFusionProductName(trx.fccProductId!!)
                                                    }
                                                    "ProductUM" -> {
                                                        if(child.firstChild.nodeValue != null)  trx.fuelQtyUnit = child.firstChild.nodeValue else fuelQtyUnit
                                                    }
                                                    "StartTimeStamp" -> {
                                                        if(child.firstChild.nodeValue != null)  trx.dh_transaction = child.firstChild.nodeValue else Support.dateToString(Date())
                                                        val timestamp = Support.convertToTimeStamp(trx.dh_transaction!!)
                                                        trx.transactionTimestamp = timestamp
                                                    }
                                                }
                                                /*  Nodes of FDC Data  */
                                            }
                                        }
                                        /*  End Nodes of FDC Data  */

                                        /* Other Parameters */
                                        trx.currency = prefs.currency
                                        val productsModel= getProductModel(trx.fccProductId!!)
                                        if(productsModel != null) {
                                            trx.hsCode = productsModel.hs_code
                                            trx.productId = productsModel.productID
                                            if(trx.produit.isNullOrEmpty())
                                            {
                                                trx.produit = productsModel.libelle
                                            }
                                        }
                                        trx.hexColor = getFusionProductColor(trx.fccProductId!!)
                                        if (FusionService.fuelVat.enabled) {
                                            FusionService.isInclusive = FusionService.fuelVat.type == 0
                                            FusionService.type = if (FusionService.isInclusive) "Incl." else "Excl."
                                            FusionService.taxModel = TaxUtils.calculate(trx.amount,
                                                FusionService.fuelVat.percentage!!.toDouble(),
                                                FusionService.isInclusive
                                            )
                                            val vatAmount = Support.formatString(FusionService.taxModel!!.taxAmount)!!.toDouble()
                                            trx.vatAmount = vatAmount.toString()
                                        }
                                        trxDetails.add(trx)
                                        if(trx.quantite != 0.0)
                                        {
                                            addTransactionsInDB(trx)
                                        }

                                    }
                                    transactionDao.close()
                                    fuelTransactionStatusDAO!!.close()
                                    /* End Check Transaction Available on DB */
                                }
                            }
                            }
                            /* End Check Product No available in RFID Terminal  List*/


                        }
                    }
                }
                    println("GetFuelSaleTrxDetails: ${Gson().toJson(trxDetails)}")
                }
                if(/* requestType == "FuelSaleTrx" ||*/ pumpSize == trxCount)
                {
                    getFccTransactions()
                }

            } catch (e: Exception) {
                e.printStackTrace()
            }
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: ParserConfigurationException) {
            e.printStackTrace()
        } catch (e: SAXException) {
            e.printStackTrace()
        }
    }
}
