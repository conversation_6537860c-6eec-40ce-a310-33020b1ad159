package app.rht.petrolcard.ui.reference.model
import androidx.annotation.Keep
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

@Keep
data class FuelPOSModel(
    @SerializedName("DECIMAL") @Expose
    val decimal: Int,

    @SerializedName("DELETEFILES") @Expose
    val deleteFiles: <PERSON><PERSON><PERSON>,

    @SerializedName("EXIST") @Expose
    val isExist: <PERSON><PERSON><PERSON>,

    @SerializedName("FTPPASS") @Expose
    val ftpPass: String?,

    @SerializedName("FTPPORT") @Expose
    val ftpPort: Int,

    @SerializedName("FTPUSER") @Expose
    val ftpUser: String?,

    @SerializedName("ID") @Expose
    val id: String?,

    @SerializedName("IP") @Expose
    val ipAddress: String?,

    @SerializedName("PassExt") @Expose
    val passExt: String? = null,

    @SerializedName("READPATH") @Expose
    val ftpReadPath: String?,

    @SerializedName("SCHEDULE") @Expose
    val jobSchedule: Int,

    @SerializedName("TCPLISTNER") @Expose
    val tcpListener: Int,

    @SerializedName("TCPPASS") @Expose
    val tcpPass: String?,

    @SerializedName("TCPPORT") @Expose
    val tcpPort: Int,

    @SerializedName("TCPUSER") @Expose
    val tcpUser: String?,

    @SerializedName("TPEMASTER") @Expose
    val tpeMaster: Boolean,

    @SerializedName("UserExt") @Expose
    val userExt: String? = null,

    @SerializedName("WRITEPATH") @Expose
    val ftpWritePath: String?,

    @SerializedName("totalAmountDecimal") @Expose
    val totalAmountDecimal: Int?,

    @SerializedName("quantityDecimal") @Expose
    val quantityDecimal: Int?,

    @SerializedName("unitPriceDecimal") @Expose
    val unitPriceDecimal: Int?,

    @SerializedName("transactionDataTimeout") @Expose
    val transactionDataTimeout: String?,
)
