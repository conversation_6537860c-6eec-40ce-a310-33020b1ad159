package app.rht.petrolcard.ui.badge.activity

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityAttendantCodeBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.database.SQLiteException
import android.content.Intent
import android.graphics.Color
import android.view.View
import android.widget.TextView
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.fuelprice.activity.FuelPriceActivity
import app.rht.petrolcard.utils.constant.AppConstant.MANAGER_ROLE_ID
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.passwordview.ActionListener
import kotlinx.android.synthetic.main.toolbar.view.*


@Suppress("DEPRECATION")
class ManagerCodeActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private lateinit var mBinding: ActivityAttendantCodeBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var mUsersDao: UsersDao? = null
    var stationMode=0
    var enteredText = ""
    private val TAG = ManagerCodeActivity::class.simpleName
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_attendant_code)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }

        setupNumberKeys()
        setupToolbar()

        if(prefs.getReferenceModel()!!.station!!.mode_pompiste == "EITHER"){
            mBinding.btnTagVerification.visibility = View.VISIBLE
        }
        else {
            if(BuildConfig.DEBUG)
            {
                mBinding.btnTagVerification.visibility = View.VISIBLE
            }
            else{
                mBinding.btnTagVerification.visibility = View.GONE
            }
        }
        mBinding.btnTagVerification.setOnClickListener {
            MultiClickPreventer.preventMultiClick(it)
            val mIntent = Intent(this, ManagerTagActivity::class.java)
            mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            startActivity(intent)
            finish()
        }
    }

    private fun setupToolbar()
    {
        mBinding.toolbarAttendantCode.toolbar.tvTitle.text = getString(R.string.manager_code)
            mBinding.toolbarAttendantCode.toolbar.setNavigationOnClickListener {
                setBeep()
                mBinding.toolbarAttendantCode.toolbar.isEnabled = false
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
            }

    }


    fun verifyPasscode(code:String)
    {
        try {
            mUsersDao = UsersDao()
            mUsersDao!!.open()
            val attendantModel  = mUsersDao!!.getAttendantListByCode(code)
            mUsersDao!!.close()
            if (attendantModel != null && attendantModel.role == MANAGER_ROLE_ID.toString()) {
                mBinding.passwordView.correctAnimation()
                intentExtrasModel!!.mPinNumberAttendant = code
                intentExtrasModel!!.idPompiste = attendantModel.id.toString()
                mBinding.pinMessage.visibility= View.GONE
                mBinding.pinMessage.text = ""
                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Manager verification code success - "+attendantModel.id.toString()))
                gotoNextScreen()
            } else {
                mBinding.passwordView.incorrectAnimation()
                mBinding.pinMessage.setTextColor(Color.RED)
                mBinding.pinMessage.visibility= View.VISIBLE
                mBinding.pinMessage.text = getString(R.string.invalid_manager_code)
                //mBinding.passCodeView.setError(true)
            }
        } catch (ex: SQLiteException) {
            ex.printStackTrace()
        }
    }
    private fun gotoNextScreen()
    {
            if (intentExtrasModel!!.workFlowTransaction == Workflow.PRICE_CHANGE) {
                intentExtrasModel!!.priceChangeVerified = true
                val intent = Intent()
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                setResult(AppConstant.CARD_NFC_BADGE, intent)
                finish()
            }
            else {
                val intent = Intent(this, SettingsActivity::class.java)
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(intent)
                finish()
            }
    }
    override fun setObserver() {

    }
    private fun setupNumberKeys()
    {
        mBinding.text0.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text1.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text2.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text3.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text4.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text5.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text6.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text7.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text8.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text9.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.textD.setOnClickListener {setBeep()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.appendInputText("")
            enteredText =""
        }
        mBinding.textSubmit.setOnClickListener {
            setBeep()

            verifyPasscode(enteredText)
        }
        mBinding.passwordView.setListener(object : ActionListener {
            override fun onCompleteInput(inputText: String) {
                enteredText = inputText
            }

            override fun onEndJudgeAnimation() {
                mBinding.passwordView.reset()
            }
        })

    }
    override fun onBackPressed() {

    }
    override fun onResume() {
        super.onResume()
        enteredText = ""
    }
}
