package app.rht.petrolcard.ui.attendantcode.activity

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityAttendantCodeBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import net.sqlcipher.database.SQLiteException
import android.content.Intent
import android.graphics.Color
import android.view.View
import android.widget.TextView
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.ui.amountselection.activity.AmountFullTankActivity
import app.rht.petrolcard.ui.amountselection.activity.EnterAmountActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.reference.model.GasStationAttendantModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.transactionlist.activity.OfflineTransactionListActivity
import app.rht.petrolcard.ui.transactionlist.activity.FuelposTransactionListActivity
import app.rht.petrolcard.ui.transactionlist.activity.FusionTransactionListActivity
import app.rht.petrolcard.utils.constant.Workflow.OTHER_PRODUCTS
import app.rht.petrolcard.utils.constant.Workflow.SHOP_PRODUCTS
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.passwordview.ActionListener
import kotlinx.android.synthetic.main.toolbar.view.*

class AttendantCodeActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private lateinit var mBinding: ActivityAttendantCodeBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var mUsersDao: UsersDao? = null
    var stationMode=0
    var enteredText = ""
    var referenceModel: ReferenceModel? = null
    private var TAG = AttendantCodeActivity::class.simpleName
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_attendant_code)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        init()
    }
    fun init()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        referenceModel =  prefs.getReferenceModel()!!

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        setupNumberKeys()
        setupToolbar()

        if(referenceModel!!.station!!.mode_pompiste == "EITHER"){
            mBinding.btnTagVerification.visibility = View.VISIBLE
        }
        else {
            mBinding.btnTagVerification.visibility = View.GONE
        }
        mBinding.btnTagVerification.setOnClickListener {
            MultiClickPreventer.preventMultiClick(it)
            val mIntent = Intent(this, AttendantTagActivity::class.java)
            mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            startActivity(mIntent)
              finish()
        }
    }

    private fun setupToolbar()
    {
        mBinding.toolbarAttendantCode.toolbar.tvTitle.text = getString(R.string.attendant_code_title)
        mBinding.toolbarAttendantCode.toolbar.setNavigationOnClickListener {
            setBeep()
            mBinding.toolbarAttendantCode.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled), getString(R.string.transaction_cancel))
        }
    }
    private fun verifyPasscode(code:String)
    {
        log(TAG, "********** Verifying attendant code **********")
        try {
            mUsersDao = UsersDao()
            mUsersDao!!.open()
            val attendantModel  = mUsersDao!!.getAttendantListByCode(code)
            mUsersDao!!.close()

            if (attendantModel != null) {
                log(TAG, "********** Entered Code: $code Attendant code ${attendantModel.codepompiste}")
                if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE )
                {
                    if(attendantModel.doCashTrxUnAttendedOPT != AppConstant.DO_CASHTRX_UNATTENDANT)
                    {
                        gotoAbortMessageActivity(getString(R.string.access_denied), getString(R.string.no_permission))
                        log(TAG, "********** attendant not allowed for cash trx")
                    }
                    else
                    {
                        setValues(code,attendantModel)
                        gotoUnAtttendantNextActivity()
                    }
                }
                else
                {
                    setValues(code,attendantModel)
                    gotoNextScreen()
                }

            }
            else {
                mBinding.passwordView.incorrectAnimation()
                mBinding.pinMessage.setTextColor(Color.RED)
                mBinding.pinMessage.visibility= View.VISIBLE
                mBinding.pinMessage.text = getString(R.string.invalid_attendant_code)
                //mBinding.passCodeView.setError(true)

                log(TAG, "********** Entered Code: $code but attendant model is null")
            }
        } catch (ex: SQLiteException) {
            ex.printStackTrace()
            log(TAG, "********** exception ${ex.cause} ${ex.message}")
        }
    }

    private fun setValues(code:String, attendantModel: GasStationAttendantModel)
    {
        mBinding.passwordView.correctAnimation()
        intentExtrasModel!!.mPinNumberAttendant = code
        intentExtrasModel!!.idPompiste = attendantModel.id.toString()
        intentExtrasModel!!.attendantName = attendantModel.firstname
        mBinding.pinMessage.visibility= View.GONE
        mBinding.pinMessage.text = ""
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Attendant code verification success - "+ attendantModel.id.toString()))
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.idPompiste = attendantModel.id.toString()
            intentExtrasModel!!.mTransaction!!.codePompiste =code
        }
    }


    private fun gotoUnAtttendantNextActivity()
    {
        intentExtrasModel!!.workFlowTransaction = Workflow.TAXI_FUEL
        intentExtrasModel!!.stationMode = AppConstant.BEFORE_TRX_MODE
        //intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "go to payment amount selection page"))
        val intent = Intent(this, AmountFullTankActivity::class.java)
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
        finish()

    }
    private fun gotoNextScreen()
    {
        val intent: Intent

        when (intentExtrasModel!!.workFlowTransaction) {
            Workflow.TAXI_FUEL -> {

                when (stationMode) {

                    1 -> {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - goto offline transaction list page"))
                        intent = Intent(this, OfflineTransactionListActivity::class.java)
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                        startActivity(intent)
                        finish()
                    }
                    3 //after transaction mode
                    -> {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - goto after transaction list page"))
                        gotoTransactionListActivity()
                    }
                    2 //before transaction mode
                    -> {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - go to payment amount selection page"))
                        intent = Intent(this, AmountFullTankActivity::class.java)
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                        startActivity(intent)
                        finish()
                    }
                    else -> {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - goto after transaction list page"))
                        gotoTransactionListActivity()
                    }
                }
            }
            OTHER_PRODUCTS, SHOP_PRODUCTS -> {
                intent = Intent(this, EnterAmountActivity::class.java)
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)
                finish()
            }
            Workflow.SETTINGS_UPDATE_MILEAGE ->
            {
                gotoCheckRestrictions()
            }
            else -> {
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.workflow_not_available))
            }
        }
    }
    fun gotoCheckRestrictions()
    {
        val i = Intent(this, CheckCardRestrictionsActivity::class.java)
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
        finish()
    }
    private fun gotoTransactionListActivity() {
        val intent1 = if (prefs.getReferenceModel()!!.FUSION!!.EXIST) {
            Intent(this, FusionTransactionListActivity::class.java)
        } else {
            Intent(this, FuelposTransactionListActivity::class.java)
        }
        intent1.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent1)
        finish()
    }
    override fun setObserver() {
    }
    private fun setupNumberKeys()
    {
        mBinding.text0.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text1.setOnClickListener { setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text2.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text3.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text4.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text5.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text6.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text7.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text8.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.text9.setOnClickListener {setBeep()
            mBinding.passwordView.appendInputText((it as TextView).text.toString()) }
        mBinding.textD.setOnClickListener {setBeep()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.removeInputText()
            mBinding.passwordView.appendInputText("")
            enteredText =""
        }
        mBinding.textSubmit.setOnClickListener {
            setBeep()
            verifyPasscode(enteredText)
        }
        mBinding.passwordView.setListener(object : ActionListener {
            override fun onCompleteInput(inputText: String) {
                enteredText = inputText
            }

            override fun onEndJudgeAnimation() {
                mBinding.passwordView.reset()
            }
        })

    }

    override fun onStart() {
        super.onStart()
        init()
    }

    override fun onResume() {
        super.onResume()
        enteredText = ""
        mBinding.passwordView.removeInputText()
        mBinding.passwordView.removeInputText()
        mBinding.passwordView.removeInputText()
        mBinding.passwordView.removeInputText()
    }
}
