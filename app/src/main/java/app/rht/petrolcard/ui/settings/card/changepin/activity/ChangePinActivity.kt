package app.rht.petrolcard.ui.settings.card.changepin.activity

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.RemoteException
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityChangePinBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.settings.card.changepin.viewmodel.ChangePinViewModel
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.UtilsCardInfo.modifyPIN
import app.rht.petrolcard.utils.UtilsCardInfo.pan
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showFailedDialog
import app.rht.petrolcard.utils.extensions.showSnakeBar
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.Core
import java.lang.Exception
import java.util.*


class ChangePinActivity : BaseActivity<ChangePinViewModel>(ChangePinViewModel::class) {
    private val TAG = ChangePinActivity::class.simpleName
    private var intentExtrasModel: IntentExtrasModel? = null

    private lateinit var mBinding: ActivityChangePinBinding
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""

    var stationMode=0
    private var pinCard = ""
    var authKey = ""
    var pinPrevious = ""
    var pinConfirm = ""
    var dateExpirationCard:String? = ""
    var cardExpired = false
    private var errorMessage = ""
    private var errorTitle = "Error"
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_change_pin)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        getIntentExtras()
        ReadCardAsyncTask().execute()
    }
    private fun setupToolbar()
    {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.change_pin)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
            finish()
        }
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_CHANGE_PIN)
        {
            mBinding.insertLayout.visibility = View.GONE
            mBinding.progressBar.visibility = View.VISIBLE
        }
        else
        {
            mBinding.insertLayout.visibility = View.VISIBLE
            mBinding.progressBar.visibility = View.GONE
        }
    }

    override fun setObserver() {
        mViewModel.unlockCardObserver.observe(this) {
            if (it.reponse == "1") {
                if (updateCardPin()) {
                    val dt = Support.dateToStringPlafond(Date())
                    mViewModel.unlockCardNotification(panNumber!!, dt)
                } else {
                    gotoAbortMessageActivity(
                        getString(R.string.error),
                        getString(R.string.pin_change_failed)
                    )
                }
            } else {
                gotoAbortMessageActivity(getString(R.string.error), it.error!!)
            }
        }

        mViewModel.unCardNotificationObserver.observe(this) {
            if (it.reponse == "1") {
                Handler().postDelayed({
                    gotoSuccessMessageActivity(
                        getString(R.string.success),
                        resources.getString(R.string.success_pin_changed)
                    )
                }, 3000)
            } else {
                showFailedDialog(getString(R.string.error), it.error!!)
            }
        }
    }

    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@ChangePinActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@ChangePinActivity) //icCpuReader Not Required for BTPE
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19)
                    else {
                        errorTitle = getString(R.string.unknown_card)
                        errorMessage = getString(R.string.card_error_9)
                        return -1
                    } //Abort Transaction
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@ChangePinActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@ChangePinActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        UtilsCardInfo.readRestrictionCard(mBankCard, icCpuReader, this@ChangePinActivity)
                        infoCarte = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "02", "28", this@ChangePinActivity)
                        dateExpirationCard = UtilsCardInfo.getDateExpCard(mBankCard, infoCarte)
                        val mToday: Date = Support.getDateComparison(Date())!!
                        cardExpired = UtilsCardInfo.isCardExpired(mToday, dateExpirationCard)
                        return if(panNumber != null) {
                            if (cardExpired) {
                                errorMessage =resources.getString(R.string.contact_agent)
                                errorTitle =  resources.getString(R.string.card_expired)
                                -2
                            } else {
                                1
                            }
                        } else {
                            errorMessage=resources.getString(R.string.read_card_failed)
                            errorTitle =  resources.getString(R.string.unknown_card)
                            -3
                        }
                    }
                    else
                    {
                        errorTitle = getString(R.string.invalid_card)
                        errorMessage = getString(R.string.card_error_10)
                        return -1
                    }
                }
                else
                {
                    try {
                        if (BuildConfig.POS_TYPE == "B_TPE") mBankCard!!.breakOffCommand()
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }

            }
            catch (e: Exception)
            {
                e.printStackTrace()
                errorMessage=resources.getString(R.string.read_card_failed)
                errorTitle =  resources.getString(R.string.unknown_card)
                return -3
            }
            return 0
        }
        override fun onPreExecute() {

        }
        override fun onPostExecute(result: Int?) {

            when (result) {
                -1,-2,-3-> {
                    gotoAbortMessageActivity(errorTitle,errorMessage)
                }
                1 -> {
                    if (Support.checkBlackList(this@ChangePinActivity, panNumber)) {
                        val msg =resources.getString(R.string.contact_agent)
                        val title = resources.getString(R.string.unable_to_unblock)
                        gotoAbortMessageActivity(title,msg)
                    } else {
                        //mViewModel.unlockCard(panNumber!!)
                       val isUpdated = updateCardPin()
                        if (isUpdated) {
                            //Handler().postDelayed({
                                gotoSuccessMessageActivity(getString(R.string.success), resources.getString(R.string.success_pin_changed))
                            //}, 3000)
                        } else {
                            gotoAbortMessageActivity(getString(R.string.error), getString(R.string.pin_change_failed))
                        }
                    }

                }
                else -> {
                    val msg =resources.getString(R.string.read_card_failed)
                    val title = resources.getString(R.string.error)
                    gotoAbortMessageActivity(title,msg)
                }
            }


        }
        override fun onProgressUpdate(vararg values: IntArray) {
            mBinding.msgText.text = getString(R.string.card_inserted_successfully)

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }

    private fun updateCardPin():Boolean {
        return UtilsCardInfo.unblockPin(mBankCard, icCpuReader, authKey, this) && UtilsCardInfo.modifyPIN(intentExtrasModel!!.mPinNew, mBankCard, icCpuReader, this)
    }
}