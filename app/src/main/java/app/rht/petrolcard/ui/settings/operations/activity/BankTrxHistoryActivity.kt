package app.rht.petrolcard.ui.settings.operations.activity

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityBankTrxHistoryBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.card.pendingtrx.model.PendingRefundModel
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException

class BankTrxHistoryActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),
RecyclerViewArrayAdapter.OnItemClickListener<PendingRefundModel>  {

    private var TAG = BankTrxHistoryActivity::class.simpleName
    private lateinit var mBinding: ActivityBankTrxHistoryBinding
    private var transactionList: ArrayList<PendingRefundModel> = ArrayList()
    private lateinit var adapter : RecyclerViewArrayAdapter<PendingRefundModel>
    private var intentExtrasModel: IntentExtrasModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_bank_trx_history)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        getTransactionDetails()
        setupRecyclerview()
        getIntentExtras()
        searchListner()
    }

    private fun searchListner()
    {
        mBinding.searchBtn.setOnClickListener {
            searchTransactions()
        }
    }
    private fun  searchTransactions()
    {
        val mTransactionTaxiDAO = TransactionDao()
        mTransactionTaxiDAO.open()
        val mesTransactionsByPAN = mTransactionTaxiDAO.getDuplicateByPAN(10,   mBinding.searchTxt.text.toString())
        if(mesTransactionsByPAN == null || mesTransactionsByPAN.isEmpty())
        {
            resetView()
        }
        else
        {
            for(trans in mesTransactionsByPAN)
            {

                val message = if(trans.modepay == AppConstant.CARD_VALUE){
                    if(trans.transactionRefundExported == 1)
                        getString(R.string.exported)
                    else
                        getString(R.string.not_exported)
                } else {
                    getString(R.string.sent_to_bank)
                }

                transactionList.add(PendingRefundModel(trans,getProduct(trans)!!, message))
                adapter.notifyDataSetChanged()
                resetView()
            }
        }
        mTransactionTaxiDAO.close()
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }
    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.bank_trx_history)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish() }
    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        resetView()
        log(TAG,"transactionList:::"+gson.toJson(transactionList))
        adapter = RecyclerViewArrayAdapter(transactionList,this)
        mBinding.mListView.adapter = adapter
        adapter.notifyDataSetChanged()
    }
    fun resetView()
    {
        if(!transactionList.isNullOrEmpty())
        {
            mBinding.emptyTXT.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyTXT.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
    }

    override fun setObserver() {}

    override fun onItemClick(view: View, `object`: PendingRefundModel) {}


    private fun getTransactionDetails() {
        try {
            val mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            val transactions = mTransactionTaxiDAO.getTeleCollectTransactionByFlag(0)
            Log.e(TAG,"TRXS : ${Gson().toJson(transactions)}")
            mTransactionTaxiDAO.close()
            transactionList.clear()
            for(trans in transactions)
            {
                val prod = getProduct(trans)
                if(prod != null)
                {
                    if(trans.preAuthAmount!=null && trans.preAuthAmount!!.isNotEmpty() && trans.amount!=null && trans.modepay == AppConstant.VISA_VALUE){

                        val message = if(trans.modepay == AppConstant.VISA_VALUE){
                            if(trans.refundStatus == "1")
                                getString(R.string.sent_to_bank)
                            else
                                getString(R.string.not_sent_to_bank)
                        } else { "" }

                        val preAuthAmt = trans.preAuthAmount!!.toDouble()
                        if(preAuthAmt> trans.amount!!){
                            transactionList.add(PendingRefundModel(trans,getProduct(trans), message))
                        }
                    }
                }
            }

           /* if(transactions.isEmpty())
                mBinding.exportBtn.visibility = View.GONE
            else
                mBinding.exportBtn.visibility = View.VISIBLE*/


        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun getProduct(transaction: TransactionModel) : ProductModel?{
        var product : ProductModel? = null
        runOnUiThread {
            val productsDAO = ProductsDao()
            productsDAO.open()
            product = productsDAO.getProductById(transaction.idProduit!!)
            productsDAO.close()
            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product!!.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product!!.libelle = fusionProductName
                }
            }

        }
        return product
    }
}