package app.rht.petrolcard.ui.qrcodeticket.activity

import android.annotation.SuppressLint
import android.content.Intent
import android.graphics.Bitmap
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityQrCodeTicketBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.loyalty.utils.QRCode
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketResponse
import app.rht.petrolcard.ui.reference.model.QrCodeTicket
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.bumptech.glide.Glide
import com.google.gson.Gson
import com.google.zxing.WriterException
import kotlinx.android.synthetic.main.toolbar.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class QrCodeTicketActivity : BaseActivity<CommonViewModel>(CommonViewModel::class){

    private val TAG = QrCodeTicketActivity::class.java.simpleName
    private lateinit var mBinding: ActivityQrCodeTicketBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    private var qrCodeTicketModel: QrCodeTicket? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_qr_code_ticket)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        showCloseButton(false)
        intentExtrasModel  = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?


        if(intentExtrasModel!=null){
            val transaction = intentExtrasModel!!.mTransaction
            val amount = if(transaction!!.amount != null) transaction.amount!!.toInt() else 0
            mViewModel.getFuelUpQrCode(amount)
        }

        lifecycleScope.launch(Dispatchers.IO){
            qrCodeTicketModel = prefs.getReferenceModel()!!.qrCodeTicket
        }

        mBinding.btnClose.setOnClickListener {
            MultiClickPreventer.preventMultiClick(it)
            setBeep()
            gotoTicketActivity()
        }

        setupToolbar()
    }

    private fun setupToolbar() {
        mBinding.toolbarTicket.toolbar.tvTitle.text = getString(R.string.qr_code)
        mBinding.toolbarTicket.toolbar.setNavigationOnClickListener {
            mBinding.toolbarTicket.toolbar.isEnabled = false
            //gotoTicketActivity()
        }
    }

    override fun setObserver() {
        mViewModel.qrCodeTicket.observe(this) {                 //added QR code on receipt for fuelUP  https://app.clickup.com/t/2zx27vd
            val fuelUpQRCodeResponse = it
            try {
                if(fuelUpQRCodeResponse!=null) {
                    val json = Gson().toJson(fuelUpQRCodeResponse)
                    if (json.isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.qrCodeTicket = it

                        val trxDao = TransactionDao()
                        trxDao.open()
                        trxDao.updateTransactionsByReferenceID(intentExtrasModel!!.mTransaction!!)
                        trxDao.close()

                        showQrBitmap(json)
                    }
                } else {
                    gotoTicketActivity()
                }
            } catch (e: WriterException) {
                Log.e(TAG, e.toString())
                gotoTicketActivity()
            }
        }
        mViewModel.qrCodeTicketError.observe(this){
           /* if(BuildConfig.DEBUG){
                mBinding.message.text = it
                val json = """{
                    "code":
                    "eyJpdiI6IjdSTlpRaGdnTDkweWkzeHYrSkE2alE9PSIsInZhbHVlIjoiU1VmV2g0WDA2MVFPcmwxMDhybW FXTnkxU2g4NTF5RjV0Mzg5QTFFcjV2dz0iLCJtYWMiOiIxOGUyYjk2ZmFhM2M4NDVhNWU4OGMwYTE3Nzg5YmUzYjZlYmU2Nzk2ZjQ0NGZhNWM1N2ZiN2MxNDY0NjdiZjYxIiwidGFnIjoiIn0=",
                    "ip": "************"
                }"""

                val item = Gson().fromJson(json,QRCodeTicketResponse::class.java)
                intentExtrasModel!!.mTransaction!!.qrCodeTicket = item
                showQrBitmap(json)

            } else {*/
                mBinding.message.text = it
                mBinding.ivQrCode.visibility = View.VISIBLE
                Glide.with(this)
                    .load(R.drawable.s_scanning)
                    .into(mBinding.ivQrCode)
            /*}*/
            showCloseButton(true)
        }
    }


    //region fuelUP Timer Dialog
    private var fuelUpQrCodeBitmap : Bitmap? = null
    private fun startTimer(){
        stopTimer()
        val timeOut : Int = try{ if (qrCodeTicketModel!!.scanTimeout != null) qrCodeTicketModel!!.scanTimeout!!.toInt() else 30  } catch (e:Exception) { 30 }
        fuelUpQrDialogTimer = object : CountDownTimer(timeOut*1000L,1000) {
            override fun onTick(milliseconds: Long) {
                val minutes = milliseconds / 1000 / 60
                val seconds = milliseconds / 1000 % 60
                log(TAG, "FUELUP QR CODE DIALOG: $minutes:$seconds")

                mBinding.tvTime.text = " $seconds "
            }

            override fun onFinish() {
                gotoTicketActivity()
            }
        }
        fuelUpQrDialogTimer.start()
    }
    private fun stopTimer(){
        try { if(::fuelUpQrDialogTimer.isInitialized) fuelUpQrDialogTimer.cancel() } catch (e:Exception) { e.printStackTrace() }
    }
    private lateinit var fuelUpQrDialogTimer : CountDownTimer
    //endregion

    private fun gotoTicketActivity() {
        val intent = Intent()
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        setResult(RESULT_OK, intent)
        finish()
    }

    override fun onStop() {
        stopTimer()
        super.onStop()
    }

    /*override fun onError(errorData: ErrorData) {
        super.onError(errorData)

        if(BuildConfig.DEBUG){
            mBinding.message.text = errorData.message
            val json = """{
                    "code":
                    "eyJpdiI6IjdSTlpRaGdnTDkweWkzeHYrSkE2alE9PSIsInZhbHVlIjoiU1VmV2g0WDA2MVFPcmwxMDhybWFXTnkxU2g4NTF5RjV0Mzg5QTFFcjV2dz0iLCJtYWMiOiIxOGUyYjk2ZmFhM2M4NDVhNWU4OGMwYTE3Nzg5YmUzYjZlYmU2Nzk2ZjQ0NGZhNWM1N2ZiN2MxNDY0NjdiZjYxIiwidGFnIjoiIn0=",
                    "ip": "************"
                }"""

            val item = Gson().fromJson(json,QRCodeTicketResponse::class.java)
            intentExtrasModel!!.mTransaction!!.qrCodeTicket = item
            showQrBitmap(json)
        } else {
            mBinding.message.text = errorData.message
            mBinding.ivQrCode.visibility = View.VISIBLE
            Glide.with(this)
                .load(R.drawable.s_scanning)
                .into(mBinding.ivQrCode)
        }
        showCloseButton(true)
    }*/

    private fun showCloseButton(visible: Boolean){
        if(visible) {
            mBinding.btnClose.visibility = View.VISIBLE
            mBinding.tvTime.visibility = View.VISIBLE
            startTimer()
        } else {
            mBinding.btnClose.visibility = View.GONE
            mBinding.tvTime.visibility = View.GONE
            stopTimer()
        }
    }


    private fun showQrBitmap(json:String){
        lifecycleScope.launch(Dispatchers.IO){
            val bitmap = QRCode.generateBitmap(json)
            fuelUpQrCodeBitmap = Support.getResizedBitmap(bitmap, 300, 300)
            launch(Dispatchers.Main){
                mBinding.message.text = if(qrCodeTicketModel!=null) qrCodeTicketModel!!.scanMessage else getString(R.string.please_scan_qr_code)
                mBinding.ivQrCode.visibility = View.VISIBLE
                Glide.with(this@QrCodeTicketActivity)
                    .load(fuelUpQrCodeBitmap)
                    .into(mBinding.ivQrCode)

                showCloseButton(true)
            }
        }
    }
}