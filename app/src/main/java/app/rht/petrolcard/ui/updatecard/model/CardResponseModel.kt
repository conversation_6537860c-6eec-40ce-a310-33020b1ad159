package app.rht.petrolcard.ui.updatecard.model

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

import androidx.annotation.Keep
@Keep
data class CardResponseModel(
    private var id: Int = 0,
    var pan: String? = null,
    var type_id: Int? = 0,
    var customers_id: Int = 0,
    var status: Int? = 0,
    var online: Int? = 0,
    var nom_sur_carte: String? = null,
    var jawaz: String? = null,
    var Plafond_Mensuel: Double? = null,
    var Plafond_Hebdomadaire: Double? = null,
    var Plafond_Journalier: Double? = null,
    var Plafond_Mensuel_Litre: Double? = null,
    var Plafond_Hebdomadaire_Litre: Double? = null,
    var Plafond_Journalier_Litre: Double? = null,
    var NBR_Trs_Mensuel: Long? = null,
    var NBR_Trs_Hebdomadaire: Long? = null,
    var NBR_Trs_Journalier: Long? = null,
    var Unite: Int? = 0,
    var Plafond_Min_Prepayee: Double? = null,
    var Plafond_Max_Prepayee: Double? = null,
    var Nbr_transaction_offline: Long? = null,
    var Solde_Cumulable: Long? = null,
    var NFC: Int? = 0,
    var profil_HORAIRE: Long? = null,
    var profil_JRS_FREE: Long? = null,
    var profil_ARTICLE: Long? = null,
    var profil_SECTEUR: Long? = null,
    var profil_STATION: Long? = null,
    var date_activation: String? = null,
    var date_expiration: String? = null,
    var date_impression: String? = null,
    var user_created: String? = null,
    var user_updated: String? = null,
    var created_at: String? = null,
    var updated_at: String? = null,
    var deleted: Int? = 0,
    var discount_id: Int? = 0,
    var discount_type: Int? = 0,
    val serialVersionUID: Long? = -2880194585729731068L

)