package app.rht.petrolcard.ui.product.model

import android.os.Parcel
import android.os.Parcelable

class FuelTrxCommonModel(
    val pump:String?,
    val nozzle:String?,
    val amount:String?,
    val quantity:String?,
    val unityPrice:String?,
):Parcelable
{
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(pump)
        parcel.writeString(nozzle)
        parcel.writeString(amount)
        parcel.writeString(quantity)
        parcel.writeString(unityPrice)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<FuelTrxCommonModel> {
        override fun createFromParcel(parcel: Parcel): FuelTrxCommonModel {
            return FuelTrxCommonModel(parcel)
        }

        override fun newArray(size: Int): Array<FuelTrxCommonModel?> {
            return arrayOfNulls(size)
        }
    }

}