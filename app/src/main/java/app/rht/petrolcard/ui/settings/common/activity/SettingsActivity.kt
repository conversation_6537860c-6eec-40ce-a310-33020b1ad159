package app.rht.petrolcard.ui.settings.common.activity

import android.annotation.SuppressLint
import android.content.*
import android.os.*
import android.util.Log
import android.view.View
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivitySettingsBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.ui.common.dialog.DialogProgressBar
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.model.SettingsMenuModel
import app.rht.petrolcard.ui.settings.appupdate.dialog.AppUpdate
import app.rht.petrolcard.ui.settings.bankcard.activity.ManageBankCardActivity
import app.rht.petrolcard.ui.settings.card.common.activity.ManageCardActivity
import app.rht.petrolcard.ui.settings.card.common.activity.ManageUninstallActivity
import app.rht.petrolcard.ui.settings.common.model.SettingItemModel
import app.rht.petrolcard.ui.settings.common.viewmodel.SettingsViewModel
import app.rht.petrolcard.ui.settings.fuelprice.activity.FuelPriceActivity
import app.rht.petrolcard.ui.settings.language.activity.ManageLanguageActivity
import app.rht.petrolcard.ui.settings.maintenance.activity.MaintenanceActivity
import app.rht.petrolcard.ui.settings.operations.activity.ManageOperationsActivity
import app.rht.petrolcard.ui.settings.terminal_settings.activity.TerminalSettingsActivity
import app.rht.petrolcard.utils.Connectivity
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.constant.AppConstant.DEVICE_NAVIGATION_BAR
import app.rht.petrolcard.utils.constant.AppConstant.DEVICE_STATUS_BAR
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.helpers.appupdate.ApkVersion
import constant.UiType
import kotlinx.android.synthetic.main.toolbar.view.*
import listener.OnInitUiListener
import model.UiConfig
import model.UpdateConfig
import org.apache.commons.lang3.exception.ExceptionUtils
import update.UpdateAppUtils
import java.lang.ref.WeakReference
import kotlin.system.exitProcess


class SettingsActivity : BaseActivity<SettingsViewModel>(SettingsViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<SettingItemModel> {

    private lateinit var mBinding: ActivitySettingsBinding

    private val TAG = SettingsActivity::class.simpleName

    private val CARD_ITEM = "card_settings"
    private val BANK_CARD_ITEM = "bank_card_settings"
    private val TELECOLLECT_ITEM = "telecollect"
    private val OPERATION_ITEM = "operation_settings"
    private val FUEL_PRICE_ITEM = "fuel_price_settings"
    private val LANGUAGE_ITEM = "language_settings"
    private val APP_UPDATE_ITEM = "app_update_settings"
    private val BACKUP_ITEM = "backup_settings"
    private val LOGS_ITEM = "logs_settings"
    private val CLOSE_APP = "restart_settings"
    private val DISABLE_STATUS_BAR_ITEM = "disable_status_bar"
    private val ENABLE_STATUS_BAR_ITEM = "enable_status_bar"
    private val DISABLE_NAVIGATION_BAR_ITEM = "disable_navigation_bar"
    private val ENABLE_NAVIGATION_BAR_ITEM = "enable_navigation_bar"
    private val UNINSTALL_APPS = "uninstall_apps"
    private val RESTART_APP = "restarta_app"
    private val MAINTEINACE_APP = "maintenance_app"
    private val TERMINAL_SETTINGS_APP = "terminal_settings_app"
    var referenceModel:ReferenceModel?=null
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_settings)

        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_settings)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        setupToolbar()
        referenceModel =prefs.getReferenceModel()!!
        val settingsMenuModel = referenceModel!!.settingsMenuModel
        if(settingsMenuModel!=null){
            mBinding.settingMenuLayout.visibility = View.GONE
            configureSettingsMenu(settingsMenuModel)
        }
        else {
            mBinding.settingMenuLayout.visibility = View.VISIBLE
        }
        startTelecollectService()
    }

    @SuppressLint("SetTextI18n")
    private fun setupToolbar() {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.settings)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            val mIntent = Intent(this, MenuActivity::class.java)
            startActivity(mIntent)
            finish()
        }
        mBinding.tvAppVersion.text = "v${BuildConfig.VERSION_NAME} (${BuildConfig.VERSION_CODE})"
    }


    private fun configureSettingsMenu(settingsMenuModel: SettingsMenuModel){
        try{
            if(settingsMenuModel.fleetCard!!) settingsItems.add(SettingItemModel(R.drawable.ic_card, "#F47776", getString(
                            R.string.fleet_card), CARD_ITEM))
            if(settingsMenuModel.bankCard!!) settingsItems.add(SettingItemModel(R.drawable.ic_visa,"#8DC41F",getString(
                            R.string.bank_card), BANK_CARD_ITEM))
            if(settingsMenuModel.fleetCard!!) settingsItems.add(SettingItemModel(R.drawable.upload_cloud,"#449EFF",getString(
                            R.string.telecollect_s),TELECOLLECT_ITEM))  //used webp replace with small size
            if(settingsMenuModel.operation!!) settingsItems.add(SettingItemModel(R.drawable.ic_receipt,"#FFB13E",getString(R.string.operations),OPERATION_ITEM))
            if(settingsMenuModel.fuelPrice!!) settingsItems.add(SettingItemModel(R.drawable.ic_price, "#9B76E9", getString(R.string.fuel_price), FUEL_PRICE_ITEM))
            settingsItems.add(SettingItemModel(R.drawable.ic_upgrade, "#8DC41F", getString(R.string.app_update), APP_UPDATE_ITEM))
            if (prefs.getBooleanSharedPreferences(DEVICE_STATUS_BAR)) {
                if(settingsMenuModel.statusBar!!) settingsItems.add(SettingItemModel(R.drawable.status_bar_icon, "#FF68D7", getString(R.string.disable_status_bar), DISABLE_STATUS_BAR_ITEM))    //used png replace with small size
            } else {
                if(settingsMenuModel.statusBar!!) settingsItems.add(SettingItemModel(R.drawable.status_bar_icon, "#FF68D7", getString(
                                    R.string.enable_status_bar), ENABLE_STATUS_BAR_ITEM))        //used png replace with small size
            }
            if (prefs.getBooleanSharedPreferences(DEVICE_NAVIGATION_BAR)) {
                if(settingsMenuModel.navigationBar!!) settingsItems.add(SettingItemModel(R.drawable.navigation_bar_icon, "#76D6F4", getString(
                                    R.string.disable_navigation_bar), DISABLE_NAVIGATION_BAR_ITEM))   //used png replace with small size
            } else {
                if(settingsMenuModel.navigationBar!!) settingsItems.add(SettingItemModel(R.drawable.navigation_bar_icon, "#76D6F4", getString(
                                    R.string.enable_navigation_bar), ENABLE_NAVIGATION_BAR_ITEM))        //used png replace with small size
            }
            if(settingsMenuModel.logs!!) settingsItems.add(SettingItemModel(R.drawable.ic_log, "#8DC41F", getString(R.string.setting_logs), LOGS_ITEM))
            if(settingsMenuModel.uninstallApps!! || BuildConfig.DEBUG) settingsItems.add(SettingItemModel(R.drawable.uninstall, "#F47776", getString(
                            R.string.uninstall_apps), UNINSTALL_APPS))                   //used png replace with small size

            settingsItems.add(SettingItemModel(R.drawable.ic_backup, "#009750", getString(R.string.backup), BACKUP_ITEM))

            settingsItems.add(SettingItemModel(R.drawable.magic_wand, "#0097FF", getString(R.string.maintainance), MAINTEINACE_APP))
            if(settingsMenuModel.terminalParams==null)
                settingsItems.add(SettingItemModel(R.drawable.terminal_parameters, "#fbc531", getString(R.string.terminal_settings_params), TERMINAL_SETTINGS_APP))
            else {
                if(settingsMenuModel.terminalParams!!)
                    settingsItems.add(SettingItemModel(R.drawable.terminal_parameters, "#fbc531", getString(R.string.terminal_settings_params), TERMINAL_SETTINGS_APP))
            }
            settingsItems.add(SettingItemModel(R.drawable.ic_round_loop_24, "#ff6348", getString(R.string.restart_app), RESTART_APP))
            if(settingsMenuModel.closeApp!!) settingsItems.add(SettingItemModel(R.drawable.ic_close_app, "#FF7246", getString(
                            R.string.close_app), CLOSE_APP))

            settingsItems.add(SettingItemModel(R.drawable.ic_language_choice, "#FF68D7", getString(R.string.language), LANGUAGE_ITEM))

            mBinding.rvSettings.removeAllViews()
            val mSettingsAdapter = RecyclerViewArrayAdapter(settingsItems, this)
            mBinding.rvSettings.adapter = mSettingsAdapter
            mSettingsAdapter.notifyDataSetChanged()

        } catch ( e: Exception){
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
         //   mViewModel.generateLogs(e.message!!,0)
        }
    }

    private var settingsItems: ArrayList<SettingItemModel> = ArrayList()

    override fun onItemClick(view: View, item: SettingItemModel) {
        log(TAG, "Clicked: ${item.id}")
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        when (item.id) {
            CARD_ITEM -> {
                startActivity(Intent(this, ManageCardActivity::class.java))
            }
            BANK_CARD_ITEM -> {
                startActivity(Intent(this, ManageBankCardActivity::class.java))
            }
            TELECOLLECT_ITEM -> {
                MultiClickPreventer.preventMultiClick(view)
                if (Connectivity.isNetworkAvailable(this))
                    {
                        teleCollect()
                    }
                else
                {
                    showNetworkDialog()
                }
            }

            OPERATION_ITEM -> {
                startActivity(Intent(this, ManageOperationsActivity::class.java))
            }
            FUEL_PRICE_ITEM -> {
                startActivity(Intent(this, FuelPriceActivity::class.java))
            }
            LANGUAGE_ITEM -> {
                val refresh = Intent(this, ManageLanguageActivity::class.java)
                startActivity(refresh)
            }
            APP_UPDATE_ITEM -> {
                mViewModel.checkAppUpdateAvailable()
            }
            BACKUP_ITEM -> {
                //startActivity(Intent(this, BackupActivity::class.java))
                sendDbFile()
            }
            LOGS_ITEM -> {
               showToast (getString(R.string.log_will_upload_to_server_shortly))
               mViewModel.generateLogs("",0)
            }
            CLOSE_APP -> {
                val homeIntent = Intent(Intent.ACTION_MAIN)
                homeIntent.addCategory(Intent.CATEGORY_HOME)
                homeIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                startActivity(homeIntent)
                exitProcess(1)
            }
            MAINTEINACE_APP -> {
                startActivity(Intent(this, MaintenanceActivity::class.java))
            }
            TERMINAL_SETTINGS_APP -> {
                startActivity(Intent(this, TerminalSettingsActivity::class.java))
            }
            RESTART_APP -> {
                Utils.doRestart(this)
            }
            ENABLE_NAVIGATION_BAR_ITEM -> {
                if(BuildConfig.POS_TYPE == "B_TPE")
                    enableWoPosNavBar(true)

                prefs.saveBooleanSharedPreferences(DEVICE_NAVIGATION_BAR, true)
                startActivity(Intent(this, SettingsActivity::class.java))
                finish()
            }
            DISABLE_NAVIGATION_BAR_ITEM -> {
                if(BuildConfig.POS_TYPE == "B_TPE")
                    enableWoPosNavBar(false)
                prefs.saveBooleanSharedPreferences(DEVICE_NAVIGATION_BAR, false)
                startActivity(Intent(this, SettingsActivity::class.java))
                finish()
            }
            ENABLE_STATUS_BAR_ITEM -> {
                if(BuildConfig.POS_TYPE == "B_TPE")
                    enableWoPosNavBar(true)

                prefs.saveBooleanSharedPreferences(DEVICE_STATUS_BAR, true)
                startActivity(Intent(this, SettingsActivity::class.java))
                finish()
            }
            DISABLE_STATUS_BAR_ITEM -> {
                if(BuildConfig.POS_TYPE == "B_TPE")
                    enableWoPosNavBar(false)
                prefs.saveBooleanSharedPreferences(DEVICE_STATUS_BAR, false)
                startActivity(Intent(this, SettingsActivity::class.java))
                finish()
            }
            UNINSTALL_APPS -> {
                startActivity(Intent(this, ManageUninstallActivity::class.java))
            }
        }
    }

    private fun teleCollect() {
        startTelecollectTimer()
        val referenceValue = "TLC" + Support.generateReference()
        showTelecollectProgressDialog(referenceValue)
        val scheduleTask = scheduleService!!.ScheduledTeleCollectTask(referenceValue,false)
        scheduleTask.execute()
    }

    lateinit var telecollectProgressDialog : DialogProgressBar
    fun showTelecollectProgressDialog(referenceValue:String) {
        telecollectProgressDialog = DialogProgressBar()
        telecollectProgressDialog.title =     getString(R.string.processing)
        telecollectProgressDialog.message =   getString(R.string.reference_batch)+"\n $referenceValue"
        telecollectProgressDialog.listener = object : DialogProgressBar.OnClickListener {
            override fun onClick() {
                setBeep()
                telecollectProgressDialog.dismiss()
            }
        }
        telecollectProgressDialog.show(supportFragmentManager, "DialogProgressBar")
    }
    override fun onBackPressed() {

    }

    override fun onStop() {
        super.onStop()
    }
    override fun setObserver() {
        val ctx = WeakReference(this).get()!!
        mViewModel.uploadFileResponse.observe(ctx){
            log(TAG, "Upload File Response : $it")
            val response = it
            if(response.reponse == "1"){
                log(TAG, "Upload File Response Success")
            }
            else {
                log(TAG, "Upload File Response Failed")
            }
        }
        mViewModel.appUpdateObserver.observe(ctx) {
            if (it != null && it.reponse != "0") {
                val version = it.contenu!!.latestVersion
                val url =  it.contenu.latestVersionUrl
                val releaseNotes =  it.contenu.releaseNotes
                val isUpdate = ApkVersion(version).compareTo(ApkVersion(BuildConfig.VERSION_CODE.toString()))
                var isForceUpdate = false
                if(prefs.getReferenceModel() != null && prefs.getReferenceModel()!!.force_update != null && prefs.getReferenceModel()!!.force_update!!)
                {
                    isForceUpdate = true
                }
                if(isUpdate == 1)
                {
                    val ctx = WeakReference(this).get()!!
                    AppUpdate(ctx,isForceUpdate,true).checkAppUpdate(version,url,releaseNotes,isUpdate)
                }
                else{
                    showErrorDialog(getString(R.string.new_update_not_available),getString(
                        R.string.application_is_already_updated))
                }
            }
        }
        mViewModel.noInternetResponse.observe(ctx) {
            onInternetEnableDisable(false)
            //  showNetworkDialog()
        }
    }
    //endregion



    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        telecollectProgressDialog.dismiss()
        gotoAbortMessageActivity("Error - "+errorData.statusCode,errorData.message!!, SettingsActivity::class.java)
    }
    private fun showErrorDialog(title:String,message:String)
    {
        val ctx = WeakReference(this).get()!!
        UpdateAppUtils.init(ctx)
        var layout: View? = null
        val updateConfig = UpdateConfig(
            alwaysShowDownLoadDialog = true,
            apkSavePath = Utils.getStorageDir("App Updates"),
            apkSaveName = "",
            checkWifi = false,
            isShowNotification = false
        )
        UpdateAppUtils
            .getInstance()
            .apkUrl("")
            .updateTitle(getString(R.string.title))
            .updateContent(getString(R.string.message))
            .updateConfig(updateConfig)
            .uiConfig(UiConfig(uiType = UiType.CUSTOM, customLayoutId = R.layout.dialog_app_update_error))
            .setOnInitUiListener(object : OnInitUiListener {
                override fun onInitUpdateUi(view: View?, updateConfig: UpdateConfig, uiConfig: UiConfig) {
                    layout = view
                    view?.findViewById<TextView>(R.id.title)?.text =title
                    view?.findViewById<TextView>(R.id.message)?.text = message

                }
            })
            .update()
    }
    var mBound = false
    fun startTelecollectService()
    {
        val intent = Intent(this, ScheduledTeleCollectService::class.java)
        bindService(intent, mConnection, BIND_AUTO_CREATE)
    }
    override fun onDestroy() {
        Log.i(TAG,"onDestroy")
        scheduleService!!.startCountDownTimer()
        stopScheduleTelecollectService()
        super.onDestroy()
    }
    fun stopScheduleTelecollectService(){
        try {
            //Unbind from the service
            if (mBound) {
                unbindService(mConnection)
                mBound = false
            }
        } catch (e: Exception) {
            log(TAG, "stopScheduleTelecollectService: " + e.message)
        }
    }

    private var scheduleService: ScheduledTeleCollectService? = null
    private val mConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            Log.d("ServiceConnected", "Connect")
            val binder: ScheduledTeleCollectService.LocalBinder = service as ScheduledTeleCollectService.LocalBinder
            scheduleService = binder.service
            scheduleService!!.startCountDownTimer()
            scheduleService!!.setTelecollectMessageListner(object : ScheduledTeleCollectService.TelecollectListner {
                override fun onTelecollectSuccess(title: String, message: String) {
                    log(TAG, "onTelecollectSuccess :: Receieved $message $title")
                    if(::telecollectProgressDialog.isInitialized){
                        try { telecollectProgressDialog.dismiss() } catch (e:Exception) {}
                    }
                    stopTelecpollectTimer()
                    gotoSuccessMessageActivity(title,message)
                    printTicket(scheduleService!!.mTeleCollectData, true)
                }

                override fun onTelecollectFailed(title: String, message: String) {
                    log(TAG, "onTelecollectFailed :: Receieved $message $title")
                    if(::telecollectProgressDialog.isInitialized){
                        try { telecollectProgressDialog.dismiss() } catch (e:Exception) {}
                    }
                    stopTelecpollectTimer()
                    showErrorOnProgress(title, message)
                }
            })
            mBound = true
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            log(TAG, "onServiceDisconnected")
            mBound = false
            scheduleService!!.stopCountDownTimer()
        }
    }
    //region Telecollect Timer Dialog
    private fun startTelecollectTimer(){
        stopTelecpollectTimer()
        if(telecollectTimer != null) {
            telecollectTimer.start()
        }
    }
    private fun stopTelecpollectTimer(){
        try {
            if(telecollectTimer != null)
            {
                telecollectTimer.cancel()
            }
        } catch (e:Exception) { e.printStackTrace() }
    }
    private val telecollectTimer = object : CountDownTimer(60000,1000) {
        @SuppressLint("SetTextI18n")
        override fun onTick(milliseconds: Long) {
            val minutes = milliseconds / 1000 / 60
            val seconds = milliseconds / 1000 % 60
            log(TAG, "telecollectTimer: $minutes:$seconds")
        }

        override fun onFinish() {
            stopTelecpollectTimer()
            if(::telecollectProgressDialog.isInitialized){
                try {
                    if(telecollectProgressDialog.isVisible)
                    {
                        telecollectProgressDialog.dismiss()

                    }
                } catch (e:Exception) {}
            }
            if(scheduleService!!.isTelecollectSuccess)
            {
                gotoSuccessMessageActivity(resources.getString(R.string.terminal_setting_success), MainApp.appContext.resources.getString(R.string.telecollecte_ok))
            }
            else
            {
                showDialog(getString(R.string.time_out),getString(R.string.failed_to_connect_with_server))
            }
        }

    }
}