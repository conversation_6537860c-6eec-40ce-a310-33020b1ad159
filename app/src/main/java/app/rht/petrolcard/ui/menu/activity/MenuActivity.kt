package app.rht.petrolcard.ui.menu.activity

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.app.PendingIntent
import android.content.*
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.net.Uri
import android.os.*
import android.provider.Settings
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.apimodel.apiresponsel.RedirectModel
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.databinding.ActivityMenuBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.ui.Dw14PrinterActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.common.dialog.DialogProgressBar
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.TransactionStepLog
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.loyalty.activity.LoyaltyDashboardActivity
import app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.modepay.model.SplitPaymentModel
import app.rht.petrolcard.ui.reference.activity.ReferenceActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.settings.appupdate.dialog.AppUpdate
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.fuelpos.ACTION_FUEL_POS_CLIENT_STATE
import app.rht.petrolcard.utils.fuelpos.FUEL_POS_CLIENT_STATE
import app.rht.petrolcard.utils.fuelpos.FuelPosClientState
import app.rht.petrolcard.utils.fuelpos.FuelPosService
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.helpers.appupdate.ApkVersion
import app.rht.petrolcard.utils.tims.ZfpLabServerManager
import com.an.deviceinfo.device.model.Battery
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.crossbowffs.remotepreferences.RemotePreferences
import com.dantsu.escposprinter.connection.usb.UsbPrintersConnections
import lecho.lib.hellocharts.model.ColumnChartData
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.File
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.*


@Suppress("DEPRECATION")
class MenuActivity : BaseActivity<MenuViewModel>(MenuViewModel::class),
    RecyclerViewArrayAdapter.OnItemClickListener<CategoryListModel> {
    private lateinit var mBinding: ActivityMenuBinding
    private var TAG = MenuActivity::class.simpleName
    private var categoriesList: ArrayList<CategoryListModel> = ArrayList()
    private lateinit var adapter: RecyclerViewArrayAdapter<CategoryListModel>
    var referenceModel: ReferenceModel? = null
    private var stationMode: Int = 0
    private var intentExtrasModel: IntentExtrasModel? = null
    private var batteryStatusReceiver: BroadcastReceiver? = null
    private var scheduleService: ScheduledTeleCollectService? = null
    var dialogBattery: AlertDialog? = null
    var percentageText: TextView? = null
    var batteryPercentage: Int = 0
    var mBound = false
    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme()
        window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)  // keep screen on this page
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_menu)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.logReferenceNo  = ""
        mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
        prefs.isCurrentActivityisMenu = true
        prefs.isRestartApplication = "false"
        startTelecollectService()
        registerBatteryStatusReceiver()
        //requestUsbPermission()
        init()
    }

    var preferenceModel: PreferenceModel? = null
    var fuelpos: FuelPOSModel? = null
    var fusion: FusionModel? = null

    var serverDate: String = ""
    var timezoneDate: String? = null
    var isDateCorrect = false
    var station : StationModel? = null
    var modePay : Int = 1

    fun init()
    {
        prefs.isSignInBackground =true
      //  Thread {
            preferenceModel = prefs.getPreferenceModel()
            referenceModel = prefs.getReferenceModel()
            fuelpos = referenceModel!!.FUELPOS
            fusion = referenceModel!!.FUSION
            serverDate = referenceModel!!.SERVER!!.DATEHEURE
            isDateCorrect = UtilsDate.isSameDay(Date(),serverDate)
        log(TAG, "isTimeAutomatic():: " + isTimeAutomatic())
        station = referenceModel!!.station
        modePay = referenceModel!!.station!!.mode
        //  }.start()

        loadLogo()

        if (checkAppExpiry()) {
            dialogShowAppExpired()
        }
         setupViews()
        setUpRecyclerView()

        //try {
            stationMode = prefs.getStationModel()!!.mode

            checkClearTransctionFirst = if (referenceModel!!.terminalConfig != null) {
                referenceModel!!.terminalConfig!!.clearBeforeTrx ?: false
            } else {
                false
            }

            if (stationMode != 1 && terminalType != AppConstant.UN_ATTENDANT_MODE) {
                startFccService()
            }

            intentExtrasModel = IntentExtrasModel()
            intentExtrasModel!!.splitPaymentModel = SplitPaymentModel()
            intentExtrasModel!!.splitPaymentModel!!.isSplitPayment = false
            if (BuildConfig.DEBUG) {
                executeTestFunction()
            }
            updateCounterCards()
            if (terminalType == AppConstant.UN_ATTENDANT_MODE) {
                val i = Intent(this, UnattendantModePayActivity::class.java)
                startActivity(i)
                finishAffinity()
            }

            if (!BuildConfig.DEBUG && preferenceModel != null && preferenceModel!!.BATTERY_ALERT != null && !(BuildConfig.POS_TYPE == "PAX" && MainApp.deviceName.contains(
                    "IM30"
                ))
            ) {
                getBatteryPercentageAlert()
            }
            shareRemotePrefs()
            updateManageCards()

    }

    private fun executeTestFunction() {

        mBinding.logo.setOnLongClickListener { arg0: View? ->
            prefs.isRestartApplication = "true"
            if (BuildConfig.DEBUG) {
                log(TAG,"Selected Language :: "+LocaleManager.getLanguage(this))
                val trxDao = TransactionDao()
                trxDao.open()
                UtilsCardInfo.Log(TAG, "trxModel List : ${gson.toJson(trxDao.getTransactions())}")
                trxDao.close()
                //val i = Intent(this, Dw14PrinterActivity::class.java)
                //startActivity(i)
            }
            true
        }
    }
    private fun startFccService() {
        if(fusion!!.EXIST) {
            if(!FusionService.isRunning(this))
                FusionService.start(this)
            else
                log(TAG,"FusionService is already running")
        }
        else if(fuelpos!=null && fuelpos!!.isExist) {
            if(!FuelPosService.isRunning(this))
                FuelPosService.start(this)
            else
                log(TAG,"FuelPosService is already running")
        }
    }

    private fun setupViews() {

        if(station!=null)
            mBinding.tvStationName.text = station!!.name.toUpperCase(Locale.ROOT)

        updatePaymentStatusView()
        mBinding.tvAppVersion.text = "v ${BuildConfig.VERSION_NAME}"

        // setupTotalSaleChart()
    }

    private fun loadLogo() {
        runOnUiThread {
            val imageURL =
                prefs.logoPath //File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).toString() + File.separator + AppConstant.LOGO_NAME)
            if (imageURL != null) {
                Glide.with(this)
                    .load(Uri.fromFile(File(imageURL)))
                    .apply(RequestOptions().override(512, 512))
                    .into(mBinding.logo)
            } else {
                Glide.with(this)
                    .load(R.drawable.logo)
                    .apply(RequestOptions().override(512, 512))
                    .into(mBinding.logo)
            }
        }
    }

    private fun updateCounterCards(){
      try{
          val transactionDao = TransactionDao()
          transactionDao.open()
          val totalTransactionsAmt = transactionDao.getTotalAmount()
          val totalRechargesAmt = transactionDao.getTotalRechargeAmount()
          val totalTransactionsCount = transactionDao.getTotalTransactionsCount()
          val totalRechargesCount = transactionDao.getRechargeTransactionsCount()
          transactionDao.close()

                var trxCount = "$totalTransactionsAmt"
                var rchCount = "$totalRechargesAmt"

            if (totalTransactionsCount < 1000)
                trxCount = String.format("%03d", totalTransactionsCount)

            if (totalRechargesCount < 1000)
                rchCount = String.format("%03d", totalRechargesCount)

            mBinding.tvTrxWorthAmount.text =
                "${getString(R.string.worth)} ${Support.formatDouble(totalTransactionsAmt)} ${prefs.currency}"
            mBinding.tvRechWorthAmount.text =
                "${getString(R.string.worth)} ${Support.formatDouble(totalRechargesAmt)} ${prefs.currency}"

            mBinding.tvTotalTrxCount.text = "$trxCount"
            mBinding.tvTotalRechargeCount.text = "$rchCount"

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    //region USB connection
    /*private val ACTION_USB_PERMISSION: String = "com.android.example.USB_PERMISSION"
    private fun requestUsbPermission(){
        val usbConnection = UsbPrintersConnections.selectFirstConnected(this)
        val usbManager = this.getSystemService(Context.USB_SERVICE) as UsbManager?
        val permissionIntent = PendingIntent.getBroadcast(
            this,
            0,
            Intent(ACTION_USB_PERMISSION),
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_MUTABLE else 0
        )
        val filter = IntentFilter()
        filter.addAction(ACTION_USB_PERMISSION)
        registerReceiver(usbReceiver, filter)
        usbManager!!.requestPermission(usbConnection!!.device, permissionIntent)
    }
    private fun resetUsbReceiver(){
        unregisterReceiver(usbReceiver)
    }
    private val usbReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (ACTION_USB_PERMISSION == action) {
                synchronized(this) {
                    val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager?
                    val usbDevice = intent.getParcelableExtra<Parcelable>(UsbManager.EXTRA_DEVICE) as UsbDevice?
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (usbManager != null && usbDevice != null) {
                            log(TAG,"USB printer connected")
                        }
                        else {
                            log(TAG,"USB printer null")
                        }
                    }
                }
            }
        }
    }*/
    //endregion

    lateinit var shareRemotePrefsTask: ShareRemotePrefsTask
    private fun shareRemotePrefs() {
        shareRemotePrefsTask = ShareRemotePrefsTask()
        shareRemotePrefsTask.execute()
    }

    inner class ShareRemotePrefsTask : CoroutineAsyncTask<String, String, Boolean>() {
        override fun doInBackground(vararg params: String): Boolean {
            val sharedpref: SharedPreferences = RemotePreferences(this@MenuActivity, AppConstant.PREF_AUTHORITY_NAME, "main_prefs")
            val editor: SharedPreferences.Editor = sharedpref.edit()
            editor.putString(AppConstant.PREFERENCES_MANAGER_ATTENDANT, gson.toJson(referenceModel!!.pompiste))
            editor.putString(AppConstant.PREFERENCES_MANAGER_STATION, gson.toJson(referenceModel!!.station))
            editor.putString(AppConstant.PREFERENCES_MANAGER_COMPANY, gson.toJson(referenceModel!!.COMPANY))
            //log(TAG, "Manager Code: " + gson.toJson(referenceModel!!.pompiste))
            editor.apply()
            return false
        }

        override fun onPreExecute() {

        }

        override fun onPostExecute(result: Boolean?) {

        }
    }

    private fun updatePaymentStatusView() {
        //Thread{

        if (modePay == 1) {
            Log.i(TAG, "ONLINE MODE")
            mBinding.tvTransactionMode.text = getString(R.string.offline)
            //mBinding.ivTransactionMode.setColorFilter(ContextCompat.getColor(this, R.color.white), android.graphics.PorterDuff.Mode.MULTIPLY)
            mBinding.ivTransactionMode.setColorFilter(ContextCompat.getColor(this, R.color.red))
        } else {
            if (modePay == 2) {
                mBinding.tvTransactionMode.text = getString(R.string.online_before_trx)
                Log.i(TAG, "ONLINE MODE: BEFORE TRX")
            } else if (modePay == 3) {
                mBinding.tvTransactionMode.text = getString(R.string.online_after_trx)
                Log.i(TAG, "ONLINE MODE: AFTER TRX")
            }
            mBinding.ivTransactionMode.setColorFilter(ContextCompat.getColor(this, R.color.green))
        }
        //}.start()
    }

    //region clock code
    private val clockTimer = object: CountDownTimer(10000,1000){
        override fun onTick(p0: Long) {
            val currentDateAndTime: String = clockSdf.format(Date())
            mBinding.tvTimeStamp.text = currentDateAndTime
        }

        override fun onFinish() {
            this.start()
        }

    }
    private lateinit var clockSdf : SimpleDateFormat
    private fun startClock(){
        mBinding.tvTimeStamp.text = ""
        clockSdf = SimpleDateFormat("MMM dd yyyy, hh:mm:ss aa")
        try {
            clockTimer.cancel()
            clockTimer.start()
        } catch (e:Exception){
            Log.e(TAG,"EXCEPTIONNNNNNN:::: ${e.cause}: ${e.message}")
        }
    }
    private fun stopClock(){
        try {
            clockTimer.cancel()
        } catch (e:Exception){
            Log.e(TAG,"EXCEPTIONNNNNNN:::: ${e.cause}: ${e.message}")
        }
    }
    //endregion

    override fun onPause() {
        super.onPause()
        stopClock()
    }
    override fun onResume() {
        // updatePaymentStatusView()
        if (intentExtrasModel != null) {
            intentExtrasModel!!.isQtySelected = false
        }
        super.onResume()
        if (!isDateCorrect && !isTimeAutomatic()) {
            showDateTimeDialog()
        }
        startClock()
        mBinding.tvTerminalSn.text = Support.maskString(MainApp.sn?:"","*",5)
        // updateCounterCards()
    }
    override fun setObserver() {
        mViewModel.appUpdateObserver.observe(this) {
            if (it != null && it.reponse != "0") {
                val version = it.contenu!!.latestVersion
                val url =  it.contenu.latestVersionUrl
                val releaseNotes =  it.contenu.releaseNotes
                val isUpdate = ApkVersion(version).compareTo(ApkVersion(BuildConfig.VERSION_CODE.toString()))
                val isForceUpdate =  it.contenu.isForceUpdate
                if(isUpdate == 1)
                {
                        val ctx = WeakReference(this).get()!!
                        AppUpdate(ctx,isForceUpdate!!,false).checkAppUpdate(version,url,releaseNotes,isUpdate)
                }
            }
        }
    }
    fun checkTimsConfigurations():Boolean
    {
        return if(referenceModel!!.fiscal_printer!!.isAvailable && referenceModel!!.fiscal_printer!!.isTIMSRequired == AppConstant.TIMS_REQUIRED) {
            if(referenceModel!!.timsAppPackageName.isNullOrEmpty() || referenceModel!!.fiscal_printer!!.IPESD.isNullOrEmpty() || referenceModel!!.fiscal_printer!!.TIMS_API_URL.isNullOrEmpty() || referenceModel!!.fiscal_printer!!.printerPassword.isNullOrEmpty()) {
              showDialog(getString(R.string.fiscal_printer_configuration),getString(R.string.please_configure_fp_setup))
                false
            } else if(!isPackageInstalled(referenceModel!!.timsAppPackageName!!)) {
                checkZFBLABAppInstalled()
                false
            } else {
                true
            }
        } else {
            true
        }
    }


    private var checkClearTransctionFirst = true
    override fun onItemClick(view: View, `object`: CategoryListModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        hideNavBarAndStatusBarForcefully()                  //disabling status and navigation bar
        if (!isDateCorrect && !isTimeAutomatic()) {
            showDateTimeDialog()
        } else {
            if (checkTimsConfigurations()) {
                prefs.logReferenceNo = ""
                prefs.logReferenceNo = Support.generateNewReferenceNumber(this)
                mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
                intentExtrasModel!!.categoryId = `object`.category_id
                val trxLog = TransactionStepLog()
                trxLog.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Category - " + `object`.category_id))
                intentExtrasModel!!.transactionStepLog = trxLog
              //  intentExtrasModel!!.mTransaction!!.reference = "TRX"+prefs.logReferenceNo
                setupTestFairy()
                when (`object`.category_id) {

                    PRODUCT.FUEL_CATEGORY_ID -> {
                        if (stationMode == AppConstant.OFFLINE_TRX_MODE || BuildConfig.DEBUG) {
                            performFuelTransactionSteps()
                        } else if (((stationMode == AppConstant.BEFORE_TRX_MODE || stationMode == AppConstant.AFTER_TRX_MODE) && (fusion!!.EXIST || fuelpos!!.isExist))) {
                            if (fusion!!.EXIST && !FusionService.fccConnected()) {
                                dialogShowFCCAlert(true)
                            } else if (fuelpos!!.isExist && stationMode == AppConstant.BEFORE_TRX_MODE /*&& !FuelPosService.isConnected()*/) {
                                checkFuelPosConnection()
                            } else {
                                performFuelTransactionSteps()
                            }

                        } else {
                            showDialog(
                                getString(R.string.fcc_configuration_on_fbs),
                                getString(R.string.please_assign_fcc_on_the_station)
                            )
                        }

                    }
                    PRODUCT.SERVICE_CATEGORY_ID -> {
                        hideNavBarAndStatusBarForcefully()
                        mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
                        val intent = Intent(this, ServiceMenuActivity::class.java)
                        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
                        intentExtrasModel!!.workFlowTransaction = Workflow.OTHER_PRODUCTS
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                        startActivity(intent)
                        finish()
                    }
                    PRODUCT.SHOP_CATEGORY_ID -> {
                        hideNavBarAndStatusBarForcefully()
                        mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
                        val intent = goDirection()
                        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
                        intentExtrasModel!!.workFlowTransaction = Workflow.SHOP_PRODUCTS
                        intentExtrasModel!!.selectedProduct =
                            getProductDetailsByID(`object`.category_id.toString())
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                        startActivity(intent)
                        finish()
                    }
                }

            }
        }
    }


    lateinit var fPosConnectionDialog: android.app.AlertDialog
    private fun checkFuelPosConnection() {
        startFuelPosReceiver()
        fPosConnectionDialog = getCommonProgressBar(
            getString(R.string.please_wait),
            getString(R.string.connecting_to_pump_please_wait),
            this
        )
        fPosConnectionDialog.show()
        if (!FuelPosService.isRunning(this))
            FuelPosService.start(this)
        FuelPosService.startFuelPosEpr()
    }

    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action
            log(TAG, "FUELPOS ACTION $action")
            var message: String? = ""
            when (action) {
                ACTION_FUEL_POS_CLIENT_STATE ->
                    if (intent.hasExtra(FUEL_POS_CLIENT_STATE)) {
                        when (intent.getStringExtra(FUEL_POS_CLIENT_STATE)) {
                            FuelPosClientState.CONNECTED -> {
                                log(TAG, "Fuel POS connected")
                                try {
                                    if (::fPosConnectionDialog.isInitialized) {
                                        fPosConnectionDialog.dismiss()
                                    }
                                } catch (e: Exception) {
                                }
                                stopFuelPosReceiver()
                                FuelPosService.stopFuelPosEpr()
                                performFuelTransactionSteps()
                            }
                            FuelPosClientState.CONNECTING -> {
                                log(TAG, "Fuel POS connecting")
                            }
                            FuelPosClientState.DISCONNECTED -> {
                                log(TAG, "Fuel POS disconnected")
                                try {
                                    if (::fPosConnectionDialog.isInitialized) {
                                        fPosConnectionDialog.dismiss()
                                    }
                                } catch (e: Exception) {
                                }
                                stopFuelPosReceiver()
                                dialogShowFCCAlert(false)
                            }
                            else -> {
                                try {
                                    if (::fPosConnectionDialog.isInitialized) {
                                        fPosConnectionDialog.dismiss()
                                    }
                                } catch (e: Exception) {
                                }
                                stopFuelPosReceiver()
                                dialogShowFCCAlert(false)
                            }
                        }
                    }
            }
        }
    }

    private fun startFuelPosReceiver() {
        if (!FuelPosService.isRunning(this)) FuelPosService.start(this)
        val filter = IntentFilter()
        filter.addAction(ACTION_FUEL_POS_CLIENT_STATE)
        registerReceiver(fuelPosReceiver, filter)
        log(TAG, "FuelPOS receiver started")
    }

    private fun stopFuelPosReceiver() {
        try {
            unregisterReceiver(fuelPosReceiver)
            log(TAG, "FuelPos receiver stopped")
            //FuelPosService.stopFuelPosStateChecker()
        } catch (e: java.lang.Exception) {

        }
    }


    private fun performFuelTransactionSteps() {
        hideNavBarAndStatusBarForcefully()
        mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME + "_" + prefs.logReferenceNo)
        // open fuel activity
        val modePay = prefs.getStationModel()!!.mode
        if (fusion!!.EXIST) {
            if (!FusionService.isRunning(this)) {
                FusionService.start(this)
            }
            //startIfsfReceiver()
            if (modePay == 2 && checkClearTransctionFirst) {
                FusionService.checkTransactionsToClear()
                gotoFuelTrxStep()
            } else {
                gotoFuelTrxStep()
            }

        } else {
            gotoFuelTrxStep()
        }
    }
    //endregion

    private fun gotoFuelTrxStep(){
        //stopIfsfReceiver()
        val intent =  goDirection()

        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        intentExtrasModel!!.workFlowTransaction = Workflow.TAXI_FUEL

        val trxLog = TransactionStepLog()
        trxLog.actions!!.add(Action(action = "Fuel Clicked"))
        intentExtrasModel!!.transactionStepLog = trxLog

        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent)
        finish()
    }

    private fun setUpRecyclerView() {
        try{
            categoriesList.clear()
            if (referenceModel != null && !referenceModel!!.category_list.isNullOrEmpty()) {
                for (list in referenceModel!!.category_list!!) {
                    if (list.category_id == PRODUCT.FUEL_CATEGORY_ID || list.category_id == PRODUCT.SERVICE_CATEGORY_ID || list.category_id == PRODUCT.SHOP_CATEGORY_ID) {
                        categoriesList.add(list)
                    }
                }
            }
            if(categoriesList.isEmpty())
            {
                mBinding.noCategories.visibility =View.VISIBLE
                mBinding.rvCategories.visibility =View.GONE
            }
            else
            {
                mBinding.noCategories.visibility =View.GONE
                mBinding.rvCategories.visibility =View.VISIBLE
            }
            adapter = RecyclerViewArrayAdapter(categoriesList, this)
            mBinding.rvCategories.adapter = adapter
            if (categoriesList.size == 2) {
                mBinding.rvCategories.layoutManager = GridLayoutManager(this, 2)
            }
            if (categoriesList.size > 2) {
                mBinding.rvCategories.layoutManager = GridLayoutManager(this, 3)
            }
            updateMoreCategoriesView()
        } catch (e: Exception) {
            e.printStackTrace()
            log(TAG, e.message + ExceptionUtils.getStackTrace(e))
            //mViewModel.generateLogs(e.message!!,0)
        }
    }

    var isCategoryCollapsed = false
    private fun updateMoreCategoriesView() {
        if (categoriesList.size > 6 && isCategoryCollapsed) {
            adapter.itemCount = categoriesList.size
            isCategoryCollapsed = false
            mBinding.btnMoreCategory.text = getString(R.string.less)
            mBinding.btnMoreCategory.visibility = View.VISIBLE
        } else {
            if (categoriesList.size > 6) {
                mBinding.btnMoreCategory.text = getString(R.string.more)
                adapter.itemCount = 6
                mBinding.btnMoreCategory.visibility = View.VISIBLE
            } else {
                adapter.itemCount = categoriesList.size
                mBinding.btnMoreCategory.visibility = View.GONE
            }
            isCategoryCollapsed = true
        }
        adapter.notifyDataSetChanged()
    }

    private fun updateManageCards() {
        val terminalConfig = referenceModel!!.terminalConfig
        if (terminalConfig?.homePageScreen != null) {
            val manageCards = terminalConfig.homePageScreen!!.manageScreen
            if (manageCards != null) {
                if (manageCards.settings!!)
                    mBinding.cardSettings.visibility = View.VISIBLE
                else
                    mBinding.cardSettings.visibility = View.GONE

                if (manageCards.telecollect!!)
                    mBinding.cardTeleCollect.visibility = View.VISIBLE
                else
                    mBinding.cardTeleCollect.visibility = View.GONE

                if (manageCards.loyaltyCard!!)
                    mBinding.cardLoyalty.visibility = View.VISIBLE
                else
                    mBinding.cardLoyalty.visibility = View.GONE
            }

            val counters = terminalConfig.homePageScreen!!.counters
            if (counters != null) {
                if (counters.totalTrx!!)
                    mBinding.totalTrxCard.visibility = View.VISIBLE
                else
                    mBinding.totalTrxCard.visibility = View.GONE

                if (counters.totalRecharge!!)
                    mBinding.totalRechCard.visibility = View.VISIBLE
                else
                    mBinding.totalRechCard.visibility = View.GONE
            }
        }
    }

    fun showMoreCategories(v: View) {
        updateMoreCategoriesView()
    }
    var isClickled=false
    fun onManageItemClicked(view: View) {
        if(!isClickled) {
            isClickled = true
            MultiClickPreventer.preventMultiClick(view)
            println("button clicked")
            prefs.isRestartApplication = "false"
            prefs.isCurrentActivityisMenu = false
            setupTestFairy("Settings_${MainApp.sn}")
            setBeep()
            if(scheduleService != null) { scheduleService!!.stopCountDownTimer() }
            // disableNavigationBar()
            when (view.id) {
                R.id.cardSettings -> {
                    startActivity(Intent(this, SettingsActivity::class.java))
                    finish()
                }
                R.id.cardTeleCollect -> {
                    if (Connectivity.isNetworkAvailable(this))
                    {
                        teleCollect()
                    }
                    else
                    {
                        showNetworkDialog()
                    }
                }
                R.id.cardLoyalty -> {
                    startActivity(Intent(this, LoyaltyDashboardActivity::class.java))
                    finish()
                }
            }
        }
        Handler(Looper.getMainLooper()).postDelayed({
            isClickled = false
        }, 5000)
    }

    var hashkey = ""
    var sn: String? = null

    private fun teleCollect() {
        startTelecollectTimer()
        val referenceValue = "TLC" + Support.generateReference()
        showTelecollectProgressDialog(referenceValue)
        mViewModel.checkAppUpdateAvailable()
        val scheduleTask = scheduleService!!.ScheduledTeleCollectTask(referenceValue,false)
        scheduleTask.execute()
    }
    lateinit var telecollectProgressDialog : DialogProgressBar
    private fun showTelecollectProgressDialog(referenceValue:String) {
        telecollectProgressDialog = DialogProgressBar()
        telecollectProgressDialog.title = getString(R.string.processing)
        telecollectProgressDialog.message = "REFERENCE BATCH \n $referenceValue"
        telecollectProgressDialog.listener = object : DialogProgressBar.OnClickListener {
            override fun onClick() {
                setBeep()
                telecollectProgressDialog.dismiss()
                mViewModel.redirect.postValue(
                    RedirectModel(
                        mClass = MenuActivity::class.java,
                        isTopFinish = true,
                        flagIsTopClearTask = true
                    )
                )
            }
        }
        telecollectProgressDialog.show(supportFragmentManager, "DialogProgressBar")
    }
    //region Telecollect Timer Dialog
    private fun startTelecollectTimer(){
        stopTelecpollectTimer()
        if(telecollectTimer != null) {
            telecollectTimer.start()
        }
    }
    private fun stopTelecpollectTimer(){
        try {
            if(telecollectTimer != null)
            {
                telecollectTimer.cancel()
            }
        } catch (e:Exception) { e.printStackTrace() }
    }
    private val telecollectTimer = object : CountDownTimer(60000,1000) {
        @SuppressLint("SetTextI18n")
        override fun onTick(milliseconds: Long) {
            val minutes = milliseconds / 1000 / 60
            val seconds = milliseconds / 1000 % 60
            log(TAG, "telecollectTimer: $minutes:$seconds")
        }

        override fun onFinish() {
            stopTelecpollectTimer()
            if(::telecollectProgressDialog.isInitialized){
                try {
                    if(telecollectProgressDialog.isVisible)
                    {
                        telecollectProgressDialog.dismiss()

                    }
                } catch (e:Exception) {}
            }
            if(scheduleService!!.isTelecollectSuccess)
            {
                gotoSuccessMessageActivity(resources.getString(R.string.terminal_setting_success), MainApp.appContext.resources.getString(R.string.telecollecte_ok))
            }
            else
            {
                showDialog(getString(R.string.time_out),getString(R.string.failed_to_connect_with_server))
            }
        }

    }
    //endregion
    lateinit var data: ColumnChartData

    private fun goDirection(): Intent {
        val i: Intent
        if (intentExtrasModel!!.loyaltyTrx) {
            intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Check card restrictions"))
            i = Intent(this, CheckCardRestrictionsActivity::class.java)
            intentExtrasModel!!.typePay = AppConstant.CARD_VALUE
        } else {
            intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - check attendant pin"))
            i = if (prefs.getStationModel()!!.mode_pompiste == "CODE") {
                Intent(this, AttendantCodeActivity::class.java)
            } else {
                Intent(this, AttendantTagActivity::class.java)
            }
        }
        return i
    }

    override fun onBackPressed() {
//        mBinding.headerLayout.visibility = View.VISIBLE
//        mBinding.ivTicketPreview.visibility = View.GONE
//        finish()
    }

    override fun onStart() {
        super.onStart()

    }

    private fun registerBatteryStatusReceiver() {
        batteryStatusReceiver = BatteryBroadcastReceiver()
        registerReceiver(batteryStatusReceiver, IntentFilter(Intent.ACTION_BATTERY_CHANGED))
    }

    inner class BatteryBroadcastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            if (intent.action == "android.intent.action.BATTERY_CHANGED") {
                val battery = Battery(this@MenuActivity)
                val currentBatteryPercentage = battery.batteryPercent
                if (currentBatteryPercentage != batteryPercentage) {
                    batteryPercentage = battery.batteryPercent
                    log(TAG, "BATTERY_CHANGED: $batteryPercentage")
                    if (dialogBattery != null && dialogBattery!!.isShowing) {
                        if (batteryPercentage < preferenceModel!!.BATTERY_ALERT!!) {
                            if (percentageText != null) {
                                percentageText!!.text = "Low Battery - $batteryPercentage%"
                            }
                        } else {
                            dialogBattery!!.dismiss()
                        }
                    }
                }
            }
        }
    }

    private fun dialogBatteryAlert(percentage: String) {
        dialogBattery = AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogBattery!!.setContentView(R.layout.dialog_battery_alert)
        percentageText = dialogBattery!!.findViewById<TextView>(R.id.title)!!
        val message2 = dialogBattery!!.findViewById<TextView>(R.id.message2)!!
        percentageText!!.text = getString(R.string.low_battery) + percentage
        message2.text =
            getString(R.string.battery_low_message_2) + " " + prefs.getPreferenceModel()!!.BATTERY_ALERT!! + "% " + getString(
                R.string.battery_low_message_3
            )
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = dialogBattery!!.window
        lp.copyFrom(window!!.attributes)
        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogBattery!!.setCancelable(false)
        dialogBattery!!.setCanceledOnTouchOutside(false)
    }

    private fun getBatteryPercentageAlert() {
        val battery = Battery(this)
        batteryPercentage = battery.batteryPercent
        if (batteryPercentage < prefs.getPreferenceModel()!!.BATTERY_ALERT!! && !BuildConfig.DEBUG) {
            dialogBatteryAlert(batteryPercentage.toString())
        }
        log(TAG, "batteryPercentage ::$batteryPercentage")
    }

    override fun onStop() {
        //resetUsbReceiver()
        try {  unregisterReceiver(batteryStatusReceiver) } catch (e:Exception){}
        if(::shareRemotePrefsTask.isInitialized){
            try { shareRemotePrefsTask.cancel(true) } catch (e:Exception){}
        }
        super.onStop()
    }



    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        telecollectProgressDialog.dismiss()
        gotoAbortMessageActivity(
            "Error - " + errorData.statusCode,
            errorData.message!!,
            MenuActivity::class.java
        )
        //  mViewModel.generateLogs(errorData.message,0)
    }

    private fun dialogShowAppExpired() {
        val dialogExpired = AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogExpired!!.setContentView(R.layout.dialog_expired_app)
        val button = dialogExpired.findViewById<Button>(R.id.button)!!
        button.setOnClickListener {
            RedirectModel(
                mClass = ReferenceActivity::class.java,
                isTopFinish = true,
                flagIsTopClearTask = true
            )
        }
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = dialogExpired.window
        lp.copyFrom(window!!.attributes)
        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogExpired.setCancelable(false)
        dialogExpired.setCanceledOnTouchOutside(false)
    }

    fun isTimeAutomatic(): Boolean {
        return Settings.System.getInt(this.contentResolver, Settings.System.AUTO_TIME, 0) == 1
    }

    fun showDateTimeDialog() {
        val dialogDateTime = AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogDateTime.setContentView(R.layout.dialog_datetime)
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = dialogDateTime.window
        lp.copyFrom(window!!.attributes)
        val actionDone = dialogDateTime.findViewById<TextView>(R.id.actionDone)
        val imageView = dialogDateTime.findViewById<ImageView>(R.id.imageView)
        val title = dialogDateTime.findViewById<TextView>(R.id.title)
        val message = dialogDateTime.findViewById<TextView>(R.id.message)
        val isFrench = LocaleManager.getLanguage(this) == LocaleManager.LANGUAGE_FRENCH
        if (isFrench) {
            imageView!!.setImageDrawable(getDrawable(R.drawable.french_date_image))
        } else {
            imageView!!.setImageDrawable(getDrawable(R.drawable.english_date))
        }

        title!!.text = getString(R.string.auto_date_title)
        message!!.text = getString(R.string.auto_time_message)
        actionDone!!.setOnClickListener {
            dialogDateTime.dismiss()
            startActivityForResult(Intent(Settings.ACTION_DATE_SETTINGS), 0)
        }
        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogDateTime.setCancelable(false)
        dialogDateTime.setCanceledOnTouchOutside(false)
    }
    fun showErrorDialog(title:String,msg: String?) {
        runOnUiThread {
            if (!(this as Activity).isFinishing) {
                val dialog = Dialog(this)
                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                dialog.setCancelable(false)
                dialog.setContentView(R.layout.dialog_fail_retry)
                dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
                val tvTitle = dialog.findViewById<TextView>(R.id.title)
                val tvMessage = dialog.findViewById<TextView>(R.id.message)
                val dialogButton = dialog.findViewById<TextView>(R.id.action_done)
                val btnRetry = dialog.findViewById<TextView>(R.id.btnRetry)
                tvTitle.text = title
                tvMessage.text = msg

                dialogButton.setOnClickListener {
                    dialog.dismiss()
                    setBeep()
                }
                btnRetry.setOnClickListener {
                    dialog.dismiss()
                    setBeep()
                    performFuelTransactionSteps()
                }
                dialog.show()
            }
        }
    }
    fun checkZFBLABAppInstalled()
    {
        if (ZfpLabServerManager.isServerInstalled(this))
        {
            prefs.isTimsServerInstalled = true
        } else
        {
          //  ZfpLabServerManager.startServerService(this)
            showInstallationDialog(this, getString(R.string.tremol_app_not_found),getString(R.string.please_install_to_continue_the_transaction))
        }
    }

    fun showInstallationDialog(activity: Activity?, title: String?, message: CharSequence?) {
        val builder = android.app.AlertDialog.Builder(activity)
        if (title != null) builder.setTitle(title)
        builder.setCancelable(false)
        builder.setMessage(message)
        builder.setPositiveButton(
            "Download"
        ) { dialog, whichButton ->
            try {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("market://details?id=com.tremol.zfplabserver")
                startActivity(intent)
            } catch (anfe: ActivityNotFoundException) {
                startActivity(
                    Intent(
                        Intent.ACTION_VIEW,
                        Uri.parse("https://play.google.com/store/apps/details?id=com.tremol.zfplabserver")
                    )
                )

            }
        }
        builder.setNegativeButton(
            "Cancel"
        ) {
                dialog, whichButton -> dialog.dismiss()

        }
        builder.show()
    }
    override fun onDestroy() {
        Log.i(TAG,"onDestroy")
        prefs.isRestartApplication = "false"
        prefs.isCurrentActivityisMenu = false
        stopScheduleTelecollectService()
        if(scheduleService != null) { scheduleService!!.stopCountDownTimer() }
        super.onDestroy()
    }
    fun stopScheduleTelecollectService(){
        try {
             //Unbind from the service
        if (mBound) {
            unbindService(mConnection)
            mBound = false
        }
        } catch (e: Exception) {
            log(TAG, "stopScheduleTelecollectService: " + e.message)
        }
    }
    fun startTelecollectService()
    {
        val intent = Intent(this, ScheduledTeleCollectService::class.java)
        bindService(intent, mConnection, BIND_AUTO_CREATE)
    }

    private val mConnection: ServiceConnection = object : ServiceConnection {
        override fun onServiceConnected(className: ComponentName, service: IBinder) {
            Log.d("ServiceConnected", "Connect")
            val binder: ScheduledTeleCollectService.LocalBinder = service as ScheduledTeleCollectService.LocalBinder
             scheduleService = binder.service
            scheduleService!!.startCountDownTimer()
            executeScheduleTelecollect()
            scheduleService!!.setTelecollectMessageListner(object : ScheduledTeleCollectService.TelecollectListner {
                override fun onTelecollectSuccess(title: String, message: String) {
                    log(TAG, "onTelecollectSuccess :: Receieved $message $title")
                    if(::telecollectProgressDialog.isInitialized){
                        try { telecollectProgressDialog.dismiss() } catch (e:Exception) {}
                    }
                    stopTelecpollectTimer()
                    gotoSuccessMessageActivity(title,message)
                    log(TAG, "scheduleService!!.mTeleCollectData ${gson.toJson(scheduleService!!.mTeleCollectData)}")
                    printTicket(scheduleService!!.mTeleCollectData, true)
                }

                override fun onTelecollectFailed(title: String, message: String) {
                    log(TAG, "onTelecollectFailed :: Receieved $message $title")
                    if(::telecollectProgressDialog.isInitialized){
                        try { telecollectProgressDialog.dismiss() } catch (e:Exception) {}
                    }
                    stopTelecpollectTimer()
                    showErrorOnProgress(title, message)
                }
            })
            mBound = true
        }

        override fun onServiceDisconnected(arg0: ComponentName) {
            log(TAG, "onServiceDisconnected")
            mBound = false
            scheduleService!!.stopCountDownTimer()
        }
    }

    private fun executeScheduleTelecollect() {
        var trxCount = 5
        if (referenceModel != null && referenceModel!!.telecollect_transaction_count != null) {
            trxCount = referenceModel!!.telecollect_transaction_count!!
        }
        if (prefs.transactionCount >= trxCount) {
            val referenceValue = "TLC" + Support.generateReference()
            val scheduleTask = scheduleService!!.ScheduledTeleCollectTask(
                referenceValue,
                isScheduleTelecollect = true,
                isReferenceRequired = false
            )
            scheduleTask.execute()
        }
    }

}
