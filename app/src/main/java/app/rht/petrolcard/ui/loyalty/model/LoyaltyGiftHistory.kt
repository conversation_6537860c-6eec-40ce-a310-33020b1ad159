package app.rht.petrolcard.ui.loyalty.model

import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

/*

@Keep
data class LoyaltyGiftHistory(
    @SerializedName("id") @Expose
    var id: Int? = null,
    @SerializedName("name")
    @Expose
    var name: String? = null,

    @SerializedName("description")
    @Expose
    var description: String? = null,

    @SerializedName("picture")
    @Expose
    var picture: String? = null,

    @SerializedName("point")
    @Expose
    var point: Int? = null,

    @SerializedName("actif")
    @Expose
    var actif: Int? = null,
) : BaseModel() {

}


*/
import androidx.annotation.Keep
@Keep
data class LoyaltyGiftHistory(
    @SerializedName("actif")
    var actif: Int?,
    @SerializedName("created_at")
    var createdAt: String?,
    @SerializedName("created_by")
    var createdBy: Int?,
    @SerializedName("description")
    var description: String?,
    @SerializedName("id")
    var id: Int?,
    @SerializedName("id_customer")
    var idCustomer: Int?,
    @SerializedName("id_gift")
    var idGift: Int?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("picture")
    var picture: String?,
    @SerializedName("point")
    var point: Int?
) : BaseModel()
