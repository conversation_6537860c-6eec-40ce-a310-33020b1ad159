package app.rht.petrolcard.ui.reference.model
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class GasStationAttendantModel(
    val codepompiste: String,
    val firstname: String?,
    val id: Int,
    val lastname: String?,
    val role: String,
    val authenticationMethod: Int?,
    val authenticationKey: String?,
    val usersButton: String?,
    val tag: String?,
    val doCashTrxUnAttendedOPT:String?,
) {
    fun getFullName(): String {
        return "$firstname $lastname"
    }
}