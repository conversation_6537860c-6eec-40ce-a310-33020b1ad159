package app.rht.petrolcard.ui.common.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.ui.reference.model.NozzelsModel
import app.rht.petrolcard.utils.Support
import com.google.gson.Gson
import java.util.*
import kotlin.collections.ArrayList
@Keep
data class TransactionStepLog(
    var actions: ArrayList<Action>? = ArrayList(),
    var date:String? = "${Support.getFormatDate(Date())}",
    var terminal:String? = "${MainApp.sn}",
): Parcelable {
    constructor(source: Parcel): this(
        source.createTypedArrayList(Action.CREATOR)!!,
        source.readString(),
        source.readString(),
    )
    fun toJson() : String {
        return Gson().toJson(this)
    }
    override fun describeContents() = 0

    override fun writeToParcel(dest: Parcel, flags: Int) = with(dest) {
        writeTypedList(actions)
        writeString(date)
        writeString(terminal)

    }

    companion object CREATOR : Parcelable.Creator<TransactionStepLog> {
        override fun createFromParcel(parcel: Parcel): TransactionStepLog {
            return TransactionStepLog(parcel)
        }

        override fun newArray(size: Int): Array<TransactionStepLog?> {
            return arrayOfNulls(size)
        }
    }
}
@Keep
data class Action(
    var action:String?="",
    var time:String? = "${Support.getFormatDate(Date())}",
):Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(action)
        parcel.writeString(time)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Action> {
        override fun createFromParcel(parcel: Parcel): Action {
            return Action(parcel)
        }

        override fun newArray(size: Int): Array<Action?> {
            return arrayOfNulls(size)
        }
    }
}

