package app.rht.petrolcard.ui.settings.operations.activity

import android.app.DatePickerDialog
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.DatePicker
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityDisputedTransactionsBinding
import app.rht.petrolcard.ui.badge.activity.BadgeActivity
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.modepay.activity.BankPaymentProgressActivity
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.operations.model.PendingTransactionModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException
import java.text.SimpleDateFormat
import java.util.*


class DisputedTransactionActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<PendingTransactionModel> {
    private var TAG= DisputedTransactionActivity::class.java.simpleName
    private lateinit var mBinding: ActivityDisputedTransactionsBinding
    private var transactionList: ArrayList<PendingTransactionModel> = ArrayList<PendingTransactionModel>()
    private lateinit var adapter : RecyclerViewArrayAdapter<PendingTransactionModel>
    private var intentExtrasModel: IntentExtrasModel? = null
    var selectedDate = ""
     var lastTrxId = 0
    var previousAuthorizedAmount = 0.0

    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_disputed_transactions)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()

        selectedDate = SimpleDateFormat("yyyy-MM-dd",Locale.US).format(Date())
        val selectedText =  SimpleDateFormat("yyyy-MM-dd",Locale.US).format(Date())
        mBinding.selectDate.text = selectedText.toString()
        getTransactionDetails()
        getIntentExtras()
        mBinding.selectDate.setOnClickListener {
            showCalendar()
        }
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }
    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.disputed_transactions)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish() }
    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        resetView()
        log(TAG,"transactionList:::"+gson.toJson(transactionList))
        adapter = RecyclerViewArrayAdapter(transactionList,this)
        mBinding.mListView.adapter = adapter
        adapter.notifyDataSetChanged()
        adapter.setContext(this)
    }
    fun resetView()
    {
        if(!transactionList.isEmpty())
        {
            mBinding.emptyTXT.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyTXT.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
    }
    override fun setObserver() {
    }
    override fun onItemClick(view: View, model: PendingTransactionModel) {
        MultiClickPreventer.preventMultiClick(view)
        if(view.id == R.id.markComplete)
        {
            if(intentExtrasModel!!.mTransaction == null)
            {
                intentExtrasModel!!.mTransaction = TransactionModel()
            }
            intentExtrasModel!!.mTransaction = model.transactionModel
            intentExtrasModel!!.selectedProduct = model.productModel
            lastTrxId = model.transactionModel!!.id!!
           gotoBadgeActivity()
        }

    }
    fun gotoBadgeActivity()
    {
        val intent= Intent(this, BadgeActivity::class.java)
        activityResultLaunch.launch(intent)
    }
    var activityResultLaunch = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == AppConstant.CARD_NFC_BADGE) {
             val authorizedManagerID = result.data!!.getIntExtra("authorizedManagerID",0)
            intentExtrasModel!!.mTransaction!!.authorizedManagerID = authorizedManagerID
            showAmountQtyDialog(this)
        }
    }
    fun completeTransaction()
    {
        try {
            val mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            intentExtrasModel!!.mTransaction!!.isDisputedTrx = 1
            mTransactionTaxiDAO.updateTransactionsByReferenceID(intentExtrasModel!!.mTransaction!!)
            mTransactionTaxiDAO.close()
            val intent = Intent(this, TicketActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            startActivity(intent)
            finish()
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    fun getTransactionDetails()
    {
        transactionList.clear()
        try {
            val mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            val transactions = mTransactionTaxiDAO.getDisputedTransactions(selectedDate)
            mTransactionTaxiDAO.close()
            for(trans in transactions!!)
            {
                var transactionStatus = getString(R.string.disputed_trx)
                if(trans.isDisputedTrx == 0)
                {
                    transactionStatus = getString(R.string.completed)
                }
                val prod = getProduct(trans)
                if(prod != null)
                {
                    transactionList.add(PendingTransactionModel(trans,getProduct(trans)!!,transactionStatus))

                }
            }
            setupRecyclerview()
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun getProduct(transaction:TransactionModel) : ProductModel?{
        var product : ProductModel? = null
        runOnUiThread {
            val productsDAO = ProductsDao()
            productsDAO.open()
            product = productsDAO.getProductById(transaction.idProduit!!)
            productsDAO.close()
            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product!!.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product!!.libelle = fusionProductName
                }
            }

        }
        return product
    }

    private lateinit var mCalendar: Calendar
    private fun showCalendar() {
        mCalendar = Calendar.getInstance()
        val mYear: Int = mCalendar.get(Calendar.YEAR)
        val mMonth: Int = mCalendar.get(Calendar.MONTH)
        val mDay: Int = mCalendar.get(Calendar.DAY_OF_MONTH)
        val mDatePicker = DatePickerDialog(this,
            { _: DatePicker?, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                mCalendar.set(Calendar.YEAR, year)
                mCalendar.set(Calendar.MONTH, monthOfYear)
                mCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                val mon = monthOfYear + 1
                var mon2 = mon.toString()

                val day = dayOfMonth
                var day2 = dayOfMonth.toString()
                if (day <= 9) {
                    day2 = "0$day"
                }
                if (mon <= 9) {
                    mon2 = "0$mon"
                }
                selectedDate = "$year-${mon2}-$day2"
                mBinding.selectDate.text = selectedDate
                getTransactionDetails()
            }, mYear, mMonth, mDay
        )
        mDatePicker.datePicker.maxDate= System.currentTimeMillis()
        mDatePicker.show()
    }
    var valuesDialog:android.app.AlertDialog?=null
    fun showAmountQtyDialog(context: Context?) {
        previousAuthorizedAmount =  intentExtrasModel!!.mTransaction!!.amount!!
        val builder = android.app.AlertDialog.Builder(context)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_disputed_values, null)
        builder.setView(layout)
        val enterAmount: AppCompatEditText = layout.findViewById(R.id.enterAmount)
        val enterQty: AppCompatEditText = layout.findViewById(R.id.enterQty)
        val submitButton: AppCompatButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val msgTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)
        cancelButton.setOnClickListener { v: View? -> valuesDialog!!.dismiss() }
        submitButton.setOnClickListener { v: View? ->
            when {
                enterAmount.text.toString().isEmpty() -> {
                    enterAmount.error = getString(R.string.enter_amount)
                }
                enterQty.text.toString().isEmpty() -> {
                    enterQty.error = getString(R.string.enter_quantity)
                }
                enterAmount.text.toString().toDouble() > previousAuthorizedAmount-> {
                    enterAmount.error = "Amount should be lesser than or equal to Pre-auth amount"
                }
                else -> {
                    valuesDialog!!.dismiss()
                    intentExtrasModel!!.workFlowTransaction = Workflow.SETTINGS_DISPUTED_TRANSACTION
                    intentExtrasModel!!.volume = enterQty.text.toString()
                  if(enterAmount.text.toString().toDouble() < previousAuthorizedAmount && intentExtrasModel!!.mTransaction!!.modepay == AppConstant.VISA_VALUE)
                  {
                      if (!isPackageInstalled("com.ebe.edc.nbe")){
                          showDialog(getString(R.string.bank_payment_application_not_found), getString(
                                  R.string.please_install_bank_app
                              ))
                      }
                      else {
                          val intent = Intent(this, BankPaymentProgressActivity::class.java)

                          intentExtrasModel!!.mTransaction!!.preAuthAmount = previousAuthorizedAmount.toString()
                         val refund = (previousAuthorizedAmount - enterAmount.text.toString().toDouble())
                          val finalAmount = refund.toLong() * 100
                          intentExtrasModel!!.refundAmount = finalAmount.toString()
                          log(TAG, "refund:: $refund")
                          log(TAG,"refundAmount:: "+  intentExtrasModel!!.refundAmount)
                          intentExtrasModel!!.bankRequestType = AppConstant.REFUND_REQUEST
                          intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                          startActivity(intent)
                          finish()
                      }
                  }
                    else{
                      completeTransaction()
                  }

                }

            }
        }
        valuesDialog = builder.create()!!
        valuesDialog!!.show()
    }
}