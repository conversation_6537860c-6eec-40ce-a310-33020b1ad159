package app.rht.petrolcard.ui.settings.card.pendingtrx.activity

import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.databinding.ActivityPendingRefundsBinding
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.ui.common.dialog.DialogProgressBar
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.PhotoModel
import app.rht.petrolcard.ui.menu.model.TeleCollectFormatModel
import app.rht.petrolcard.ui.menu.model.TelecollectDataModel
import app.rht.petrolcard.ui.reference.model.AuditModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TerminalModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.card.pendingtrx.model.PendingRefundModel
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.AppUtils.Companion.createFormData
import app.rht.petrolcard.utils.AppUtils.Companion.generateTeleCollectJson
import app.rht.petrolcard.utils.constant.AppConstant
import com.afollestad.materialdialogs.MaterialDialog
import com.google.gson.Gson
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException
import java.io.File
import java.io.IOException
import java.lang.ref.WeakReference
import java.security.NoSuchAlgorithmException
import java.util.*
import kotlin.collections.ArrayList

class PendingRefundsActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),
    RecyclerViewArrayAdapter.OnItemClickListener<PendingRefundModel> {

    private var TAG = PendingRefundsActivity::class.simpleName
    private lateinit var mBinding: ActivityPendingRefundsBinding
    private var transactionList: ArrayList<PendingRefundModel> = ArrayList()
    private lateinit var adapter : RecyclerViewArrayAdapter<PendingRefundModel>
    private var intentExtrasModel: IntentExtrasModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_pending_refunds)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        getTransactionDetails()
        setupRecyclerview()
        getIntentExtras()
        searchListner()
    }

    fun searchListner()
    {
        mBinding.searchBtn.setOnClickListener {
            searchTransactions()
        }
    }
    fun searchTransactions()
    {
        val mTransactionTaxiDAO = TransactionDao()
        mTransactionTaxiDAO.open()
        val mesTransactionsByPAN = mTransactionTaxiDAO.getDuplicateByPAN(10,   mBinding.searchTxt.text.toString())
        if(mesTransactionsByPAN == null || mesTransactionsByPAN.isEmpty())
        {
            resetView()
        }
        else
        {
            for(trans in mesTransactionsByPAN)
            {
                val message = if(trans.modepay == AppConstant.CARD_VALUE){
                    if(trans.transactionRefundExported == 1)
                        getString(R.string.exported)
                    else
                        getString(R.string.not_exported)
                } else {
                    getString(R.string.sent_to_bank)
                }

                transactionList.add(PendingRefundModel(trans,getProduct(trans)!!,message))
                adapter.notifyDataSetChanged()
                resetView()
            }
        }
        mTransactionTaxiDAO.close()
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }
    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.pending_refund)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish() }
    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        resetView()
        log(TAG,"transactionList:::"+gson.toJson(transactionList))
        adapter = RecyclerViewArrayAdapter(transactionList,this)
        mBinding.mListView.adapter = adapter
        adapter.notifyDataSetChanged()
    }
    fun resetView()
    {
        if(!transactionList.isNullOrEmpty())
        {
            mBinding.emptyTXT.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyTXT.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
    }
    override fun setObserver() {
    }
    override fun onItemClick(view: View, model: PendingRefundModel) {

    }

    fun exportRefundFile(view : View){

        MyMaterialDialog(
            this,
            getString(R.string.confirm),
            getString(R.string.please_confirm_to_export_refund_file)+"\nFile location: Storage/FBS Pay/Refund Files",
            getString(R.string.export),
            getString(R.string.cancel),
            object : MyMaterialDialogListener {
                override fun onPositiveClick(dialog: MaterialDialog) {
                    ScheduledTeleCollectTask(isProgressRequired = true, isPrintRequired = false).execute()
                    dialog.dismiss()
                }
                override fun onNegativeClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                }
            }
        )
    }

    private fun getTransactionDetails()
    {
        try {
            val mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            val transactions = mTransactionTaxiDAO.getFleetCardPendingTransaction()
            Log.e(TAG,"REFUND TRXS : ${Gson().toJson(transactions)}")
            mTransactionTaxiDAO.close()
            transactionList.clear()
            for(trans in transactions)
            {

                val message = if(trans.modepay == AppConstant.CARD_VALUE){
                    if(trans.transactionRefundExported == 1)
                       getString(R.string.exported)
                    else
                        getString(R.string.not_exported)
                } else {
                    getString(R.string.sent_to_bank)
                }

                val prod = getProduct(trans)
                //if(prod != null)
                //{
                    /*if(trans.preAuthAmount!=null && trans.preAuthAmount!!.isNotEmpty() && trans.amount!=null){
                        val preAuthAmt = trans.preAuthAmount!!.toDouble()
                        if(preAuthAmt> trans.amount!!){*/
                            transactionList.add(PendingRefundModel(trans,getProduct(trans),message))
                   /*     }
                    }*/
                //}
            }

            if(transactions.isEmpty())
                mBinding.exportBtn.visibility = View.GONE
            else
                mBinding.exportBtn.visibility = View.VISIBLE


        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun getProduct(transaction: TransactionModel) : ProductModel?{
        var product : ProductModel? = null
        runOnUiThread {
            val productsDAO = ProductsDao()
            productsDAO.open()
            product = productsDAO.getProductById(transaction.idProduit!!)
            productsDAO.close()
            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product!!.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product!!.libelle = fusionProductName
                }
            }

        }
        return product
    }

    private inner class ScheduledTeleCollectTask(private val isProgressRequired: Boolean, private val isPrintRequired: Boolean = true) : CoroutineAsyncTask<String, String, Boolean>() {
        var teleCollectValidation = false
        var validationParametrage = false
        private var sn = ""
        private var hashkey = ""
        private var hashTelecollecte = ""
        private var tlcFile: File? = null
        private var nbTransactions: Int = 0
        private var nbTransactionsLoyalty: Int? = null
        private var nbAnnulationsTransactions: Int = 0
        private var nbRecharges: Int = 0
        private var nbAnnulationsRecharges: Int = 0
        private var nbTransactionsTaxis: Int = 0
        private var totalTransactions: Double = 0.0
        private var totalAnnulationsTransactions: Double = 0.0
        private var totalRecharges: Double = 0.0
        private var totalAnnulationsRecharges: Double = 0.0
        private var totalTransactionsTaxis: Double = 0.0
        private lateinit var mTransactionTaxiDAO: TransactionDao
        private lateinit var mesTransactionsTaxi: List<TransactionModel>
        private var auditLogs= java.util.ArrayList<AuditModel>()
        private lateinit var mTeleCollectData: TelecollectDataModel
        private var loyaltyTransactions: List<TransactionModel>? = null
        private lateinit var mTerminalDAO: TerminalDao
        private lateinit var mTerminal: TerminalModel
        private lateinit var teleCollectFormat: TeleCollectFormatModel
        private lateinit var teleCollectJSON: kotlin.String
        private var mTelecollecteDAO: TeleCollectDao? = null
        var telecollecteRow = 0
        var referenceValue = ""
        private var mesPhotos: List<PhotoModel>? = null

        override fun doInBackground(vararg params: String): Boolean {
            try {
                teleCollectValidation = true
                validationParametrage = true
                hashTelecollecte = "no_hash_calculated"
                tlcFile = null

                nbTransactions = 0
                nbTransactionsLoyalty = 0

                nbAnnulationsTransactions = 0
                nbRecharges = 0
                nbAnnulationsRecharges = 0
                nbTransactionsTaxis = 0

                totalTransactions = 0.0
                totalAnnulationsTransactions = 0.0
                totalRecharges = 0.0
                totalAnnulationsRecharges = 0.0
                totalTransactionsTaxis = 0.0
                Handler(Looper.getMainLooper()).post {
                    //get Transactions data from DB
                    try {
                        mTransactionTaxiDAO = TransactionDao()
                        mTransactionTaxiDAO.open()
                        mesTransactionsTaxi =
                            mTransactionTaxiDAO.getTeleCollectTransactionByFlag(0)
                        mTransactionTaxiDAO.close()
                    } catch (Ex: SQLException) {
                        Ex.printStackTrace()
                    }

                    var i = 0
                    while (i < mesTransactionsTaxi.size && (mesTransactionsTaxi[i].detailArticle != "loy")) {
                        if (mesTransactionsTaxi[i].idTypeTransaction == 1) { // =1 trx
                            nbTransactions++
                            totalTransactions += mesTransactionsTaxi[i].amount!!
                        }
                        if (mesTransactionsTaxi[i].idTypeTransaction == 2) { // =2 ann trx
                            nbAnnulationsTransactions++
                            totalAnnulationsTransactions += mesTransactionsTaxi[i].amount!!
                        }
                        if (mesTransactionsTaxi[i].idTypeTransaction == 3) { // =3 recharge
                            nbRecharges++
                            totalRecharges += mesTransactionsTaxi[i].amount!!
                        }
                        if (mesTransactionsTaxi[i].idTypeTransaction == 4) { // =4 ann recharge
                            nbAnnulationsRecharges++
                            totalAnnulationsRecharges += mesTransactionsTaxi[i].amount!!
                        }
                        i++
                    }

                    //get Loyalty transactions data from DB
                    try {
                        mTransactionTaxiDAO = TransactionDao()
                        mTransactionTaxiDAO.open()
                        loyaltyTransactions = mTransactionTaxiDAO.getTeleCollectLoyaltyByFlag(0)
                        mTransactionTaxiDAO.close()
                    } catch (Ex: SQLException) {
                        Ex.printStackTrace()
                    }

                    i = 0
                    while (loyaltyTransactions != null && i < loyaltyTransactions!!.size) {
                        if (loyaltyTransactions!![i].amount!! > 0) {
                            nbTransactionsTaxis++ // trx loyalty
                            totalTransactionsTaxis += loyaltyTransactions!![i].amount!! // amount loyalty
                        }
                        i++
                    }
                    referenceValue = "TLC" + Support.generateReference()
                    if (isProgressRequired) {
                        progressDialog.message = referenceValue
                        progressDialog.setMessageValue(referenceValue)
                    }

                    mTeleCollectData = TelecollectDataModel(
                        dateTelecollecte = Support.dateToString(Date()),
                        nombreTransactions = nbTransactions,
                        nombreAnnulationsTransactions = nbAnnulationsTransactions,
                        nombreRecharges = nbRecharges,
                        nombreAnnulationsRecharges = nbAnnulationsRecharges,
                        totalTransactions = totalTransactions,
                        totalAnnulationsTransactions = totalAnnulationsTransactions,
                        totalRecharges = totalRecharges,
                        totalAnnulationsRecharges = totalAnnulationsRecharges,
                        nbTransactionsTaxis = nbTransactionsTaxis,
                        totalTransactionsTaxis = totalTransactionsTaxis,
                        reference = referenceValue,
                    )

                    try {
                        val auditDao = AuditDao()
                        auditDao.open()
                        auditLogs = auditDao.getAudits()
                        auditDao.close()
                    } catch (e:Exception){
                        e.printStackTrace()
                    }

                    teleCollectFormat = TeleCollectFormatModel(
                        mTeleCollectData,
                        loyaltyTransactions!!,
                        mesTransactionsTaxi,
                        auditLogs
                    )
                    teleCollectJSON = generateTeleCollectJson(teleCollectFormat)
                    val jsonFile = teleCollectFormat.mTelecollecte.reference + ".json"
                    Support.writeFile(teleCollectJSON, jsonFile, this@PendingRefundsActivity)
                    File("$filesDir/$jsonFile").also { tlcFile = it }

                    log(TAG, "jsonFile:: $jsonFile")
                    log(TAG, "teleCollectJSON:: $teleCollectJSON")
                    log(TAG, "tlcFile:::$tlcFile")

                    try {
                        hashTelecollecte = Support.generateSHA1(tlcFile).toString()
                    } catch (e: NoSuchAlgorithmException) {
                        e.printStackTrace()
                        teleCollectValidation = false
                    } catch (e: IOException) {
                        e.printStackTrace()
                        teleCollectValidation = false
                    }

                    sn = Support.getSN()!!

                    //String hashkey = "";
                    hashkey = Support.generateMD5("abcde" + sn + "fghij")!!
                    log("NOT HASHED KEY => ", "abcde" + sn + "fghij")
                    log("HASHED KEY => ", hashkey)
                    sendOfflinePhotos()
                    if (hashkey == "") {
                        teleCollectValidation = false
                    }
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                return false
            }
            return teleCollectValidation
        }

        override fun onPreExecute() {
            if (isProgressRequired) {
                showProgressDialog()
            }
        }

        override fun onPostExecute(result: Boolean?) {
            when (result) {
                false -> {
                    val transactionDao = TransactionDao()
                    transactionDao.open()
                    for(transaction in mesTransactionsTaxi){
                        transaction.transactionRefundExported = 1
                        transactionDao.updateTransactionsByReferenceID(transaction)
                        //transactionDao.updateTeleCollectFlagByReferenceID(transaction.reference!!, 0)
                    }
                    showToast(getString(R.string.refund_file_exported_sucessfully))
                }
                true -> {
                    if(::teleCollectFormat.isInitialized){
                        val jsonFile = teleCollectFormat.mTelecollecte.reference + ".json"
                        val file = File("$filesDir/$jsonFile")
                        sendTeleCollectFile(hashkey, hashTelecollecte, file)
                    }
                }
            }
            getTransactionDetails()
            setupRecyclerview()
        }
        private fun sendOfflinePhotos() {
            var mPhotoDAO = PhotoDao()
            mPhotoDAO.open()
            mesPhotos = mPhotoDAO.selectionnerByFlag(0)
            mPhotoDAO.close()
            if(!mesPhotos.isNullOrEmpty())
            {
                log(TAG,"mesPhotos::" +gson.toJson(mesPhotos))
                for(photo in mesPhotos!!)
                {
                    if(photo.fieldsOptionalString != null)
                    {
                        val imageUri = Uri.parse(photo.fieldsOptionalString) //mCurrentPhotoPath
                        val fileKDO = File(imageUri.path)

                        MainApp.longApiService.offlinePhotoUpload(
                            url = prefs.baseUrl + NetworkRequestEndPoints.OFFLINEUPLOAD,
                            file = createFormData(fileKDO, "file", "*/*"),
                            sn = createFormData( MainApp.sn!!),
                            hashkey = createFormData(hashkey),
                            name = createFormData(photo.referenceTransaction!!)
                        ).subscribeOn(rx.schedulers.Schedulers.io())
                            .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                            .subscribe({
                                if (it != null && it.reponse != "OK") {
                                    log(TAG, "Photo Upload Success")
                                    mPhotoDAO = PhotoDao()
                                    mPhotoDAO.open()
                                    mPhotoDAO.updateFlagPhotoById(photo.id, 1)
                                    mPhotoDAO.close()
                                }
                            },
                                {
                                    log(TAG, "Photo Upload Failed")
                                    it.printStackTrace()
                                })
                    }
                }
            }
        }

        private fun sendTeleCollectFile(key: String, hash: String, file: File) {
            MainApp.longApiService.sendTeleCollectFile(
                url = prefs.baseUrl + NetworkRequestEndPoints.SEND_TELECOLLECT_FILE,
                file = createFormData(file, "file", "*/*"),
                sn = createFormData( MainApp.sn!!),
                key = createFormData(key),
                hash = createFormData(hash)
            ).subscribeOn(rx.schedulers.Schedulers.io())
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe({
                    if (it != null && it.reponse != "0") {
                        log(TAG, "Scheduled Send Telecollect Success")
                        getTelecollectData()
                        generateLogs("",0)
                    } else {
                        if (isProgressRequired) {
                            showErrorOnProgress(getString(R.string.error), it.error!!)
                            generateLogs(it.error!!,1)
                        }
                    }
                },
                    {
                        log(TAG, "Scheduled Send Telecollect Failed")
                        it.printStackTrace()
                        generateLogs("Failed to Send telecollect File - 2",1)
                        if (isProgressRequired) {
                            showErrorOnProgress(getString(R.string.error), "Server Configuration Error - 2")
                            generateLogs(it.message!!,1)
                        }
                    })
        }

        private fun showErrorOnProgress(title: String, message: String) {
            //showDialog(title, message)
            showToast(message)
            progressDialog.dismiss()
        }

        private fun getTelecollectData() {
            val language =  LocaleManager.getLanguage(this@PendingRefundsActivity).lowercase()
            MainApp.longApiService.getAllReferenceData(
                prefs.baseUrl + NetworkRequestEndPoints.GET_ALL_REFERENCE_DATA,
                 MainApp.sn!!,
                prefs.getPreferenceModel()!!.blockListVersionNo!!.toString(),
                prefs.getPreferenceModel()!!.greyListVersionNo!!.toString(),
                language
            ).subscribeOn(rx.schedulers.Schedulers.io())
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe({
                    if (it != null && it.reponse != "0") {
                        log(TAG, "Scheduled Get Telecollect Success")
                        validateTeleCollect(true)
                        // To Check Should USe SD Card
                        if (it.contenu!!.USE_SD_CARD != null) {
                            prefs.isUseSdCard = it.contenu.USE_SD_CARD!!
                        }
                        // Save Terminal Details
                        if (it.contenu.terminal != null) {
                            val stationTitle =
                                BuildConfig.VERSION_NAME + " | " + it.contenu.terminal.stationName + " (" + it.contenu.terminal.stationId + ")"
                            // Should Check this model Later
                            prefs.savePreferenceModel(
                                PreferenceModel(
                                    blockListVersionNo = it.contenu.blackListVersion,
                                    greyListVersionNo = it.contenu.GreyListVersion,
                                    firstTimeLoadingApp = false,
                                    aReferencer = true,
                                    blockage = false,
                                    suggestedPrice = false,
                                    tlcTimeStamp = System.currentTimeMillis(),
                                    transactionLimit = it.contenu.terminal.maxRefillAmount,
                                    rechargeLimit = it.contenu.terminal.maxRechargeLimit,

                                    stationTitle = stationTitle,
                                    //badgeGrants = it.contenu.badge,
                                    stationID = it.contenu.terminal.stationId,
                                    terminalID = it.contenu.terminal.terminalId,
                                    sectorId = it.contenu.terminal.sectorId,

                                    occuranceNetwork = true,
                                    BATTERY_ALERT = it.contenu.BATTERY_ALERT,
                                    RFID_VERIFICATION_TYPE = it.contenu.RFID_VERIFICATION_TYPE,
                                    TELECOLLECT_TIME = it.contenu.TELECOLLECT_TIME,
                                    MAX_REFILL_AMNT = it.contenu.MAX_REFILL_AMNT
                                )
                            )
                            ScheduledTeleCollectService.saveDataInDB(this@PendingRefundsActivity,it.contenu)
                            if(prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!!)
                            {
                                getDiscountDetailsData()
                            }
                            else
                            {
                                scheduleTelecollectSuccess()
                            }

                        }

                    } else {
                        if (isProgressRequired) {
                            showErrorOnProgress(getString(R.string.error), it.error!!)
                        }
                    }
                },
                    {
                        Log.e(TAG, "Scheduled Get Telecollect Failed")
                        it.printStackTrace()
                        if (isProgressRequired) {
                            showErrorOnProgress(getString(R.string.error), it.message!!)
                        }
                    })
        }
        private fun scheduleTelecollectSuccess() {
            if (isProgressRequired) {
                progressDialog.setView(resources.getString(R.string.terminal_setting_success) + "\n" + resources.getString(
                    R.string.telecollecte_ok
                ))
            }
            if(isPrintRequired)
                printTicket(mTeleCollectData)
        }
        private fun getDiscountDetailsData() {
            MainApp.longApiService.getAllDiscountDetails(
                prefs.baseUrl + NetworkRequestEndPoints.GET_DISCOUNT_DETAILS,
                 MainApp.sn!!
            ).subscribeOn(rx.schedulers.Schedulers.io())
                .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                .subscribe({
                    if (it != null && it.success != "0") {
                        scheduleTelecollectSuccess()
                        prefs.saveDiscountDetails(it.discount_details!!)
                    } else {

                        if (isProgressRequired) {
                            if(prefs.getReferenceModel()!!.IMPLEMENT_DISCOUNT!!)
                            {
                                showErrorOnProgress(getString(R.string.failed_to_get_discount_details), it.error!!)
                                generateLogs(it.error!!,1)
                            }
                            else
                            {

                                scheduleTelecollectSuccess()
                            }

                        }
                    }
                },
                    {
                        log(TAG, "Discount Get Details Telecollect Failed")
                        if (isProgressRequired) {
                            showErrorOnProgress(
                                getString(R.string.failed_to_get_discount_details),
                                it.message!!
                            )
                            generateLogs(it.message!!,1)
                        }

                        it.printStackTrace()
                    })
        }
        private fun validateTeleCollect(isSuccess: Boolean) {
            if (isSuccess) {
                Support.updateBlockage(this@PendingRefundsActivity)
                val mTelecollecteDAO = TeleCollectDao()
                mTelecollecteDAO.open()
                val telecollecteRow = mTelecollecteDAO.insertTeleCollectDao(mTeleCollectData)
                mTelecollecteDAO.close()

                mTransactionTaxiDAO = TransactionDao()
                mTransactionTaxiDAO.open()
                for (i in mesTransactionsTaxi.indices) {
                    mTransactionTaxiDAO.updateTeleCollectFlagById(
                        mesTransactionsTaxi[i].id!!,
                        telecollecteRow
                    )
                }
                for (i in mesTransactionsTaxi.indices) {
                    mTransactionTaxiDAO.updateTeleCollectFlagById(
                        mesTransactionsTaxi[i].id!!,
                        telecollecteRow
                    )
                }
                val mesTransactionsTaxiTelecollectees: List<*> =
                    mTransactionTaxiDAO.getTransactions()
                for (i in mesTransactionsTaxiTelecollectees.indices) {
                    log(TAG, "mesTransactionsTaxiTelecollectees => " + mesTransactionsTaxiTelecollectees[i].toString())
                }

                val auditDao = AuditDao()
                auditDao.open()
                for(item in auditLogs){
                    item.teleCollectStatus = 1
                    auditDao.updateAudit(item)
                }
                auditDao.close()

                mTransactionTaxiDAO.close()

            }

        }
        lateinit var progressDialog : DialogProgressBar
        private fun showProgressDialog() {
            progressDialog = DialogProgressBar()
            progressDialog.title =     getString(R.string.processing)
            progressDialog.message =   getString(R.string.reference_batch)+"\n $referenceValue"
            progressDialog.listener = object : DialogProgressBar.OnClickListener {
                override fun onClick() {
                    setBeep()
                    progressDialog.dismiss()
                }
            }
            progressDialog.show(supportFragmentManager, "DialogProgressBar")
        }
    }
}