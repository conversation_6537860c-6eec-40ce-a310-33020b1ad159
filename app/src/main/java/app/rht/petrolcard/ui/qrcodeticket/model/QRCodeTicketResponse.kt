package app.rht.petrolcard.ui.qrcodeticket.model


import android.os.Parcel
import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep


@Keep
data class QRCodeTicketResponse(
    @SerializedName("code")
    var code: String?,
    @SerializedName("ip")
    var ip: String?,
    @SerializedName("message")
    var message: String?,
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    ) {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(code)
        parcel.writeString(ip)
        parcel.writeString(message)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<QRCodeTicketResponse> {
        override fun createFromParcel(parcel: Parcel): QRCodeTicketResponse {
            return QRCodeTicketResponse(parcel)
        }

        override fun newArray(size: Int): Array<QRCodeTicketResponse?> {
            return arrayOfNulls(size)
        }
    }
}