package app.rht.petrolcard.ui.cardpincode.activity

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import android.view.Window
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.databinding.ActivityCardpinCodeBinding
import app.rht.petrolcard.ui.amountselection.activity.EnterAmountActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardCeilingsLimitsActivity
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.SplitPaymentActivity
import app.rht.petrolcard.ui.nfc.activity.NfcActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.card.history.activity.HistoryActivity
import app.rht.petrolcard.ui.settings.card.unlockpin.activity.EnterNewPinActivity
import app.rht.petrolcard.ui.updatecard.activity.UpdateCardActivity
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.passwordview.ActionListener
import app.rht.petrolcard.utils.paxutils.icc.IccTester
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.activity_attendant_code.*
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard


@Suppress("DEPRECATION")
class VerifyPinActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private var TAG= VerifyPinActivity::class.simpleName
    private lateinit var mBinding: ActivityCardpinCodeBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var mUsersDao: UsersDao? = null
    var stationMode=0
    private var pinCount = 3
    private var pinCard = ""
    private var mBankCard: BankCard? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String = ""
    var authKey = ""

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_cardpin_code)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        pinCount = prefs.getStationModel()!!.tryPinCount!!
        getInitIntentExtras()
        setupNumberKeys()
        setupToolbar()
        initCardReader()

        //mainApp.startIdleScreenTimer(this,60)
    }
    fun getInitIntentExtras()
    {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        log(TAG,"WorkFLOW:: "+intentExtrasModel!!.workFlowTransaction!!)
        if (intentExtrasModel!!.isCardUpdate!!) {
            mBinding.pinMessage.setTextColor(Color.GREEN)
            mBinding.pinMessage.visibility=View.VISIBLE
            mBinding.pinMessage.text = getString(R.string.enter_pin_for_update)
        }

        log(TAG,"Audit Logs:: "+intentExtrasModel!!.transactionStepLog!!)
    }

    fun proceedToNextStep() {
        setBeep()
        pinCount = prefs.getStationModel()!!.tryPinCount!!
        intentExtrasModel!!.mPinNumberCard = pinCard

        if(intentExtrasModel!!.isCardUpdate!!)
        {
            val intent= Intent(this, UpdateCardActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_CARD_UPDATE)
            {
                startActivity(intent)
            }
            else
            {
                startActivityForResult(intent,AppConstant.CARD_UPDATE_CODE_RESULT)
            }

        }
        else
        {
            gotoNextActivity()
        }
    }
    private fun gotoNextActivity() {
        val i:Intent = when (intentExtrasModel!!.workFlowTransaction) {
            Workflow.SETTINGS_RECHARGE_CARD -> {
                val intent = Intent(this, EnterAmountActivity::class.java)
                intent
            }
            Workflow.SETTINGS_CARD_HISTORY -> {
                Intent(this, HistoryActivity::class.java)
            }
            Workflow.SETTINGS_CARD_CHANGE_PIN -> {
                Intent(this, EnterNewPinActivity::class.java)
            }
            Workflow.SETTINGS_UPDATE_MILEAGE -> {
                Intent(this, NfcActivity::class.java)
            }
            else -> {
                Intent(this, CheckCardCeilingsLimitsActivity::class.java)
            }
        }
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(i)
        finish()
    }

    private fun setupToolbar() {
        mBinding.toolbarVerifyPin.toolbar.tvTitle.text = getString(R.string.card_pin_verification)
        mBinding.toolbarVerifyPin.toolbar.setNavigationOnClickListener {
            mBinding.toolbarVerifyPin.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
        }
    }

    private fun initCardReader(){
        if (BuildConfig.POS_TYPE == "B_TPE") {
            mBankCard = BankCard(this@VerifyPinActivity)
        } else if (BuildConfig.POS_TYPE == "PAX") {
            UtilsCardInfo.connectPAX()
        }
        cardRemoveChecker.start()
    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    val infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@VerifyPinActivity) //icCpuReader Not Required for BTPE
                    if (infoCarte != null && infoCarte.isNotEmpty())
                        panNumber = infoCarte.substring(0, 19) else return -1 //Abort Transaction

                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@VerifyPinActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@VerifyPinActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        if(intentExtrasModel!!.panNumber != panNumber)
                        {
                            return -1
                        }
                        else if(pinCard != null && UtilsCardInfo.verifyPIN(mBankCard, icCpuReader, pinCard, this@VerifyPinActivity)) {
                            runOnUiThread {
                                password_view.correctAnimation()
                                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} -Verify Pin - True"))

                            }
                            proceedToNextStep()
                        }
                        else {
                            runOnUiThread {
                                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} -Verify Pin - False"))
                                password_view.incorrectAnimation()
                            mBinding.pinMessage.setTextColor(Color.RED)
                            mBinding.pinMessage.visibility = View.VISIBLE
                            pinCard = ""
                                log(TAG, "pinCount:: $pinCount")
                                log(TAG,"prefs.getStationModel()!!.tryPinCount!!:: "+prefs.getStationModel()!!.tryPinCount!!)
                                if(pinCount == 1 || pinCount > prefs.getStationModel()!!.tryPinCount!!)
                                {
                                   gotoAbortMessageActivity(getString(R.string.card_locked_msg),getString(R.string.contact_agent))
                                }
                                else
                                {
                                    log(TAG, "pinCount -1 :: $pinCount")
                                    log(TAG,"prefs.getStationModel()!!.tryPinCount!!:: "+prefs.getStationModel()!!.tryPinCount!!)
                                    pinCount -= 1
                                    if(pinCount == 1)
                                    {
                                        mBinding.pinMessage.text =getString(R.string.pin_lock_warning)
                                    }
                                    else
                                    {
                                        val text = resources.getString(R.string.vous_reste) + " " + pinCount+ " " + resources.getString(R.string.attempts_left)
                                        mBinding.pinMessage.text =text
                                    }

                                }
                        }
                        }

                    }
                    else
                    {
                        showSnakeBar(getString(R.string.card_reading_failed))
                    }
                }
            }
            catch (e: Exception)
            {
                e.printStackTrace()
            }
            return 0
        }
        override fun onPreExecute() {
            mBinding.progressBar.visibility = View.VISIBLE
        }
        override fun onPostExecute(result: Int?) {
            mBinding.progressBar.visibility = View.GONE
            if(result == -1)
            {
                UtilsCardInfo.beep(mCore, 10)
                var title = resources.getString(R.string.error)
                var msg = ""
                if (panNumber != null)
                    msg = resources.getString(R.string.card_changed)
                else resources.getString(R.string.read_card_failed)

                gotoAbortMessageActivity(title,msg)
            }


        }
        override fun onProgressUpdate(vararg values: IntArray) {
            mBinding.progressBar.visibility =View.VISIBLE
        }
    }
    private fun setupNumberKeys() {
        text_0.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_1.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_2.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_3.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_4.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_5.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_6.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_7.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_8.setOnClickListener { setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_9.setOnClickListener {setBeep()
            password_view.appendInputText((it as TextView).text.toString()) }
        text_d.setOnClickListener { setBeep()
            password_view.removeInputText()
            password_view.appendInputText("")
            password_view.removeInputText()
            password_view.removeInputText()
            password_view.removeInputText()
            password_view.removeInputText()
            pinCard =""
        }
        text_submit.setOnClickListener {
            if(pinCard.length == 4)
            {
                val readCardTask = ReadCardAsyncTask()
                readCardTask.execute()
            }
            else
            {
                showSnakeBar(getString(R.string.please_enter_4_digit_pin))
            }
        }
        password_view.setListener(object : ActionListener {
            override fun onCompleteInput(inputText: String) {
                pinCard = inputText
            }

            override fun onEndJudgeAnimation() {
                password_view.reset()
            }
        })

    }
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == AppConstant.CARD_UPDATE_CODE_RESULT) {
            gotoNextActivity()
        }
    }
    override fun setObserver() {

    }

    override fun onBackPressed() {

    }

    private var isCardRemovedShown = false
    private val cardRemoveChecker = object : CountDownTimer(10000,1000){
        override fun onTick(p0: Long) {
            log(TAG,"===== CARD CHECKER")
            if(!isCardRemovedShown)
            {
                if(BuildConfig.POS_TYPE == AppConstant.PAX){

                    if(!IccTester.getInstance().detect( 0.toByte()))
                    {
                        isCardRemovedShown = true
                        showErrorDialog(getString(R.string.error),getString(R.string.card_removed))
                    }
                }
                else if(BuildConfig.POS_TYPE == AppConstant.B_TPE){
                    if(mBankCard!!.iccDetect() != BankCard.CARD_DETECT_EXIST){
                        isCardRemovedShown = true
                        showErrorDialog(getString(R.string.error),getString(R.string.card_removed))
                    }
                }
            }
        }

        override fun onFinish() {
            this.start()
        }
    }

    fun showErrorDialog(title:String,msg: String?) {
        cardRemoveChecker.cancel()
        if(!(this as Activity).isFinishing)
        {
            val dialog = Dialog(this)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialog.setCancelable(false)
            dialog.setContentView(R.layout.dialog_failed_message)
            dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
            val tvTitle = dialog.findViewById<TextView>(R.id.title)
            val tvMessage = dialog.findViewById<TextView>(R.id.message)
            val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

            tvTitle.text = title
            tvMessage.text = msg

            dialogButton.setOnClickListener {
                dialog.dismiss()
                setBeep()
                if(intentExtrasModel!!.splitPaymentModel != null && intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!)
                {
                    val mIntent = Intent(this, SplitPaymentActivity::class.java)
                    resetPayment()
                    mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                    startActivity(mIntent)
                    finish()
                }
                else
                {
                    val mIntent = Intent(this, MenuActivity::class.java)
                    startActivity(mIntent)
                    finish()
                }
            }
            dialog.show()
        }
    }

    private fun resetPayment() {
        if(!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!)
        {
            intentExtrasModel!!.splitPaymentModel!!.firstModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.firstPaymentName = ""
//            intentExtrasModel!!.mTransaction!!.amount = intentExtrasModel!!.splitPaymentModel!!.totalAmount!!.toDouble()
        }
        else
        {
            intentExtrasModel!!.splitPaymentModel!!.secondModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.secondPaymentName = ""

        }
        intentExtrasModel!!.splitPaymentModel!!.isError = true
    }

    override fun onDestroy() {
        super.onDestroy()
        cardRemoveChecker.cancel()
    }

    override fun onStop() {
        super.onStop()
        cardRemoveChecker.cancel()
    }

}
