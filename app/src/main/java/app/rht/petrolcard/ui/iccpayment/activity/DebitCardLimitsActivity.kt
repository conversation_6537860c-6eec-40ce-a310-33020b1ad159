package app.rht.petrolcard.ui.iccpayment.activity

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.os.*
import android.view.View
import android.view.Window
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityDebitLimitsBinding
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.SplitPaymentActivity
import app.rht.petrolcard.ui.nfc.model.NFCEnum
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.paxutils.icc.IccTester
import com.google.gson.Gson
import com.pax.dal.IDAL
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.BankCard
import java.math.BigDecimal
import java.sql.SQLException
import java.util.*
import kotlin.math.max


class DebitCardLimitsActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private var TAG = DebitCardLimitsActivity::class.java.simpleName
    var dal: IDAL? = null
    var ret = -1
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""
    var authKey: String? = ""
    var cardType: Int? = 0
    var cardModel: CardStaticStructureModel? = null
    private var isFirstUse = false
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode = 0
    private var mToday: Date? = null
    private var terminalDateErr = false
    private lateinit var mBinding: ActivityDebitLimitsBinding
    private var errorMessage = ""
    var returnValue = 0
    private var totalMonthlyLimit = 0.0 // plafondMensuel
    private var totalWeeklyLimit = 0.0 // plafondHebdo
    private var totalDailyLimit = 0.0 //plafondJournalier
    private var CARD_NBR_TRS_OFF = 0
    var isLitreUnit = false
    var remainingCardCeilings: String? = ""
    private var remainingMonthlyAmount = 0.0 //plafondMensuelCompt
    private var remainingWeeklyAmount = 0.0 //plafondHebdoCompt
    private var remainingDailyAmount = 0.0 //plafondJournaliercompt
    private var REMAINING_NBR_TRS_MONTHLY = 0 //COMPTEUR_NBR_TRS_MENSUEL
    private var REMAINING_NBR_TRS_WEEKLY = 0 //COMPTEUR_NBR_TRS_HEBDO
    private var REMAINING_NBR_TRS_DAILY = 0 //COMPTEUR_NBR_TRS_JOURNALIER
    private var monthlyAmount = ""
    private var weeklyAmount = ""
    private var dailyAmount = ""
    var cardBalance = 0.0
    var cardBalanceTxt = ""
    private var infoCreditPostPayee: String? = null
    var readPlf: String? = null
    private var mProduct: ProductModel? = null //mProduitToCheckout2
    private var cardHolderName = ""
    private var resultNFC: String? = null
    var listnfcrecord: ArrayList<NfcTagModel> = ArrayList<NfcTagModel>()
    private var compteurTrxDebit: Long? = null
    private var volume: String? = null
    private var preferenceModel: PreferenceModel? = null
    var isOnPoxExecuted = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_debit_limits)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG, "CurrentActivity ${prefs.mCurrentActivity}")
        getInitIntentExtras()

        mToday = Support.getDateComparison(Date())
        setupToolbar()

        preferenceModel = prefs.getPreferenceModel()
        if (intentExtrasModel!!.selectedProduct != null) {
            mProduct = intentExtrasModel!!.selectedProduct
        } else if (intentExtrasModel!!.mTransaction != null &&
            intentExtrasModel!!.mTransaction!!.idProduit != null
            && intentExtrasModel!!.mTransaction!!.idProduit != 0
        ) {
            mProduct =
                getProductDetailsByID(intentExtrasModel!!.mTransaction!!.idProduit!!.toString())
        }

        intentExtrasModel!!.selectedProduct = mProduct

        val readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
    }

    private fun setupToolbar() {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.payment_verification)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
        }
    }

    fun getInitIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL)
                as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if (intentExtrasModel!!.loyaltyTrx) {
                stationMode = 1
            }
        }
        log(TAG, "WorkFLOW:: " + intentExtrasModel!!.workFlowTransaction!!)
    }


    fun isCardDetected(): Boolean {
        var whileCondition = false
        try {
            whileCondition = BankCard.CARD_DETECT_EXIST == mBankCard!!.iccDetect()
        } catch (e: RemoteException) {
            e.printStackTrace()
        }
        return whileCondition
    } // eof detect

    inner class ReadCardAsyncTask : CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@DebitCardLimitsActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX(5)
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC,
                        5, responseData, responseLength, AppConstant.TPE_APP
                    )
                }
                cardRemoveChecker.start()
                if(BuildConfig.POS_TYPE == AppConstant.PAX){
                    if(!IccTester.getInstance().detect(0.toByte()))
                    {
                        log(TAG,"Card Removed")
                        return -1
                    }
                }
                if (BuildConfig.POS_TYPE == "B_TPE" && !isCardDetected()) {
                    log(TAG,"Card Removed")
                    return -1
                }

                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05" ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07" ||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(
                        mBankCard, icCpuReader,
                        this@DebitCardLimitsActivity
                    ) //icCpuReader Not Required for BTPE
                    log(TAG, "infoCarte::: $infoCarte")
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19) else return -1 //Abort Transaction

                    getCardStaticStructure()
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(
                        mBankCard,
                        icCpuReader,
                        authKey,
                        this@DebitCardLimitsActivity
                    )
                    val externalAuth2 = UtilsCardInfo.externalAuth2(
                        mBankCard,
                        icCpuReader,
                        authKey,
                        this@DebitCardLimitsActivity
                    )

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        getCardRestrictionInfo()
                        if (intentExtrasModel!!.panNumber != panNumber) {
                            return -1
                        } else {
                            return performDebitCreditOperation()
                        }

                    } else {
                        showSnakeBar(getString(R.string.card_reading_failed))
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                log(TAG, e.printStackTrace().toString())
                //  mViewModel.generateLogs(errorMessage,1)
                return -1
            }
            return returnValue
        }

        override fun onPreExecute() {

        }

        override fun onPostExecute(result: Int?) {
            if(!isOnPoxExecuted)
            {
                proccedOnPostExecute(result)
            }

        }

        override fun onProgressUpdate(vararg values: IntArray) {
            mBinding.msgText.text = getString(R.string.card_inserted_success)
            mBinding.insertLayout.visibility = View.GONE
            mBinding.progressBar.visibility = View.VISIBLE

        }

        override fun onCancelled(result: Int?) {

        }

        override fun onCancelled() {
        }
    }

    private fun proccedOnPostExecute(result: Int?) {
        isOnPoxExecuted = true
        when (result) {
            -1 -> {

                try {
                    cardRemoveChecker.cancel()
                } catch (e:Exception) {
                    e.printStackTrace()
                }
                UtilsCardInfo.beep(mCore, 10)


                // newly added to not send 0 balance in case of card remove --- altaf
                if(intentExtrasModel!!.mTransaction!= null){
                    val oldCardBal = intentExtrasModel!!.mTransaction!!.soldeCard!!.toDouble()
                    //val trxAmount = intentExtrasModel!!.mTransaction!!.amount
                    intentExtrasModel!!.mTransaction!!.soldeCard = ""+ oldCardBal  //(oldCardBal - trxAmount!!).toString()

                    log(TAG, "soldeCard:: 1 " + intentExtrasModel!!.mTransaction!!.soldeCard)
                    intentExtrasModel!!.cardBalance = intentExtrasModel!!.mTransaction!!.soldeCard
                    updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
                }
                // addition end here



                var title = resources.getString(R.string.error)
                var msg = getString(R.string.transaction_cancelled)
                if (terminalDateErr) {
                    title = resources.getString(R.string.TPE_DATE_ERROR_SYNCH)
                    msg = resources.getString(R.string.TPE_DATE_ERROR)
                } else {
                    if (panNumber != null)
                        msg = resources.getString(R.string.card_changed)
                    else resources.getString(R.string.read_card_failed)
                }
                if (BuildConfig.POS_TYPE == AppConstant.PAX &&
                    !IccTester.getInstance().detect(0.toByte())
                ) {
                    showSnakeBarColor(getString(R.string.card_removed), true)
                } else if (BuildConfig.POS_TYPE == "B_TPE" && !isCardDetected()) {
                    showSnakeBarColor(getString(R.string.card_removed), true)
                } else if (intentExtrasModel!!.panNumber != panNumber) {
                    msg = getString(R.string.card_changed_during_transaction)
                    showSnakeBarColor(msg, true)
                }
                cardNotUpdated(msg) //update card details
            }
            -2 -> {
                val title = resources.getString(R.string.error)
                if (intentExtrasModel!!.fleetCardRequestType == AppConstant.VOID_REQUEST
                    || intentExtrasModel!!.fleetCardRequestType == AppConstant.REFUND_REQUEST
                ) { //card not detected in refund
                    cardNotUpdated(getString(R.string.failed_update))
                } else {
                    showErrorDialog(title, errorMessage)
                }
            }

            0 -> {
                cardNotUpdated(getString(R.string.failed_update))
            }
            1 -> {
                intentExtrasModel!!.refundStatus = 1 // refund success and card updated
                gotoNextActivity()
            }
        }
    }

    fun getCardStaticStructure() {
        if (infoCarte != null && panNumber!!.isNotEmpty()) {
            cardModel = UtilsCardInfo.readCardStaticStructureInfo(mBankCard, icCpuReader, this)
            val jsonData = (gson.toJson(cardModel))
            "Card Static Structure : $jsonData".also { log(TAG, it) }
            cardType = cardModel!!.cardType.toInt()
            intentExtrasModel!!.verificationType = cardModel!!.verificationType.toInt()
            if (cardModel!!.cardStatus == "F") {
                isFirstUse = true
            }
        }
    }

    override fun setObserver() {

    }

    fun getCardRestrictionInfo() {
        log(TAG, "PRODUCT IN CARD LIMIT:::: $mProduct")
        cardHolderName = UtilsCardInfo.readCardHolderName(mBankCard, icCpuReader, this)!!
        //Remaining Card Limits
        if (cardModel!!.cardCeilingUnit == "1") {
            isLitreUnit = true
        }
        //TotalCard Ceiling Limits
        val cardCeilings =
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "02", "32", this)
                .replace("F", "0")
        totalMonthlyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 0, 12) //Monthly card limit
        totalWeeklyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 12, 24) //weekly ceiling cap
        totalDailyLimit =
            UtilsCardInfo.getCardCeilings(cardCeilings, 24, 36) //ceiling day of the card
        // counter ceiling
        remainingCardCeilings = if (isLitreUnit) {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "03", "32", this)
                .replace("F", "0")
        } else {
            UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F07", "01", "32", this)
                .replace("F", "0")
        }
        remainingMonthlyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
        remainingWeeklyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 12, 24)
        remainingDailyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)

        REMAINING_NBR_TRS_MONTHLY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
        REMAINING_NBR_TRS_WEEKLY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 44, 52).toInt()
        REMAINING_NBR_TRS_DAILY =
            UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()
        cardBalance = if (cardType == AppConstant.PREPAID_CARD) {
            UtilsCardInfo.getAmount(mBankCard, icCpuReader, 4, 12, this)
        } else {
            max(0.0, totalMonthlyLimit - remainingMonthlyAmount)
        }
        monthlyAmount = "" + (max(0.0, totalMonthlyLimit - remainingMonthlyAmount))
        weeklyAmount = "" + (max(0.0, totalWeeklyLimit - remainingWeeklyAmount))
        dailyAmount = "" + (max(0.0, totalDailyLimit - remainingDailyAmount))
        cardBalanceTxt = "" + (Support.formatDoubleAffichage(cardBalance).toString())
        intentExtrasModel!!.cardType = cardType
    }

    private fun printLog() {
        log(TAG, "totalMonthlyLimit ::: $totalMonthlyLimit")
        log(TAG, "totalWeeklyLimit ::: $totalWeeklyLimit")
        log(TAG, "totalDailyLimit ::: $totalDailyLimit")
        log(TAG, "remainingMonthlyAmount ::: $remainingMonthlyAmount")
        log(TAG, "remainingWeeklyAmount ::: $remainingWeeklyAmount")
        log(TAG, "remainingDailyAmount ::: $remainingDailyAmount")
        log(TAG, "REMAINING_NBR_TRS_MONTHLY ::: $REMAINING_NBR_TRS_MONTHLY")
        log(TAG, "REMAINING_NBR_TRS_WEEKLY ::: $REMAINING_NBR_TRS_WEEKLY")
        log(TAG, "REMAINING_NBR_TRS_DAILY ::: $REMAINING_NBR_TRS_DAILY")
        log(TAG, "cardBalance ::: $cardBalance")
        log(
            TAG,
            "cardBalance Actual ::: " + UtilsCardInfo.getAmount(mBankCard, icCpuReader, 4, 12, this)
        )
        log(TAG, "CARD_NBR_TRS_OFF ::: $CARD_NBR_TRS_OFF")
    }


    fun gotoTicketActivity() {

        Handler(Looper.getMainLooper()).post {
            val i: Intent?
            intentExtrasModel!!.splitPaymentModel!!.isError = false
            if (intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!) {
                i = Intent(this, SplitPaymentActivity::class.java)
                if (intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!) {
                    intentExtrasModel!!.splitPaymentModel!!.isSecondPaymentDone = true
                    intentExtrasModel!!.splitPaymentModel!!.secondPaymentAmount =
                        intentExtrasModel!!.mTransaction!!.amount
                } else {
                    intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone = true
                    intentExtrasModel!!.splitPaymentModel!!.firstPaymentAmount =
                        intentExtrasModel!!.mTransaction!!.amount
                }
                intentExtrasModel!!.panNumber = panNumber

            }
//            else  if(intentExtrasModel!!.isDiscountTransaction!!)
//            {
//                mBinding.progressBar.visibility =View.GONE
//                mBinding.paymentSuccess.visibility =View.VISIBLE
//                i = Intent(this, PrintDiscountTicketActivity::class.java)
//            }
            else {
                mBinding.progressBar.visibility = View.GONE
                mBinding.paymentSuccess.visibility = View.VISIBLE
                i = Intent(this, TicketActivity::class.java)
            }
            i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(i)
            finish()
        }

    }

    fun getDebitAmount() {
        var trxAmount = 0.0

        if (intentExtrasModel!!.mTransaction != null) {
            volume = if (isLitreUnit && intentExtrasModel!!.mTransaction != null) {
                intentExtrasModel!!.mTransaction!!.quantite.toString() + ""
            } else if (intentExtrasModel!!.isDiscountTransaction!!) {
                (intentExtrasModel!!.mTransaction!!.amount!!.toDouble() -
                        intentExtrasModel!!.discountAmount!!.toDouble()).toString()
            } else {
                intentExtrasModel!!.mTransaction!!.amount!!.toString()
            }

        } else if (stationMode == AppConstant.OFFLINE_TRX_MODE ||
            stationMode == AppConstant.BEFORE_TRX_MODE) {
            if (isLitreUnit) {
                if (intentExtrasModel!!.selectedPrice != null &&
                    intentExtrasModel!!.selectedPrice!!.unitPrice > 0)
                    volume =
                        (intentExtrasModel!!.amount!!.toDouble() /
                                intentExtrasModel!!.selectedPrice!!.unitPrice).toString() + ""
            } else {
                volume = intentExtrasModel!!.amount!!
            }
        } else volume = intentExtrasModel!!.amount!!
    }

    private fun debitCardLimits(): Int {
        try {
            if (intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.REBATE_DISCOUNT
                && intentExtrasModel!!.mTransaction!!.isDiscountTransaction == 1) {
                val discountAmt =
                    Support.formatString(intentExtrasModel!!.mTransaction!!.discountAmount!!.toDouble())
                volume =
                    (intentExtrasModel!!.mTransaction!!.amount!!.toDouble()
                     - discountAmt!!.toDouble()).toString()
            } else {
                getDebitAmount()
            }
            var prd = if (mProduct != null) Utils.padZero(
                mProduct!!.productID.toString() + "",
                4
            ) else "0000"
            if (prd == "0000" && intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL) {
                prd =
                    "0100" // manually added for 100 to show FUEL in card history
                          // if product is null in preauth amount
            }
            log(TAG, "Product id in Debit Card: $prd")
            var nowDate = Support.dateToStringX(Date())
            log(TAG, "nowDate: $nowDate")
            val transData = Utils.padZero(nowDate, 14).toString() + "00" + Utils.padZero(
                preferenceModel!!.stationID.toString() + "",
                4
            ) + prd + (if (intentExtrasModel!!.mTransaction != null) Support.dateToStringX(
                intentExtrasModel!!.mTransaction!!.dateTransaction!!
            ) else "FFFFFFFFFFFFFF") + "FFFFFFFFFF"

            val ammtHex = Utils.amountToHex(volume)
            if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
                returnValue = 1
            }
            if (intentExtrasModel!!.mPinNumberCard != null && UtilsCardInfo.verifyPIN(
                    mBankCard,
                    icCpuReader,
                    intentExtrasModel!!.mPinNumberCard,
                    this
                )
            ) {
                if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
                    if (intentExtrasModel!!.loyaltyTrx) {
                        if (intentExtrasModel!!.mTransaction != null)
                            intentExtrasModel!!.mTransaction!!.detailArticle =
                            AppConstant.DETAIL_ARTICLE
                        returnValue = 1
                    }
                }
                else if (cardType != null && cardType == AppConstant.POSTPAID_CARD
                    || cardType == AppConstant.PREPAID_CARD)
                {
                    remainingCardCeilings = prefs.remCardCeiling!!
                    remainingMonthlyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
                    remainingWeeklyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 12, 24)
                    remainingDailyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)

                    val dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
                    REMAINING_NBR_TRS_MONTHLY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
                    REMAINING_NBR_TRS_WEEKLY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 44, 52).toInt()
                    REMAINING_NBR_TRS_DAILY =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()

                    intentExtrasModel!!.typeCard = cardType!!.toString()

                    if (volume!!.toDouble() <= dailyAmount.toDouble() && volume!!.toDouble()
                        <= weeklyAmount.toDouble() && volume!!.toDouble() <= monthlyAmount.toDouble()) {
                        if (volume!!.toDouble() > cardBalance) {
                            returnValue = -2
                            errorMessage = resources.getString(R.string.soldeCardErr) + "!"
                            return -2
                        }
                        else if (volume!!.toDouble() <= cardBalance) {
                            readPlf = UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings)

                            val plfMonthNew =
                                ((remainingMonthlyAmount + volume!!.toDouble()) * 100).toLong()
                            val plfWeekNew =
                                ((remainingWeeklyAmount + volume!!.toDouble()) * 100).toLong()
                            val plfDailyNew =
                                ((remainingDailyAmount + volume!!.toDouble()) * 100).toLong()

                            val nbreTrxMonthNewCompt =
                                ((REMAINING_NBR_TRS_MONTHLY + 1) * 100).toLong()
                            val nbreTrxWeekNewCompt =
                                ((REMAINING_NBR_TRS_WEEKLY + 1) * 100).toLong()
                            val nbreTrxDayNewCompt = ((REMAINING_NBR_TRS_DAILY + 1) * 100).toLong()
                            printLog()

                            intentExtrasModel!!.transactionStepLog!!.actions!!.add(
                                Action(action = "TRX${prefs.logReferenceNo} - Debit Amount - " + volume))

                            if (UtilsCardInfo.debit(
                                    mBankCard,
                                    icCpuReader,
                                    ammtHex,
                                    authKey,
                                    transData,
                                    this
                                ) &&
                                if (isLitreUnit)
                                    UtilsCardInfo.updateInfoPlafondCardCompt(
                                        mBankCard,
                                        icCpuReader,
                                        readPlf,
                                        "03",
                                        plfMonthNew.toString() + "",
                                        plfWeekNew.toString() + "",
                                        plfDailyNew.toString() + "",
                                        "" + nbreTrxMonthNewCompt,
                                        "" + nbreTrxWeekNewCompt,
                                        "" + nbreTrxDayNewCompt,
                                        this
                                    )
                                else
                                    UtilsCardInfo.updateInfoPlafondCardCompt(
                                        mBankCard,
                                        icCpuReader,
                                        readPlf,
                                        "01",
                                        plfMonthNew.toString() + "",
                                        plfWeekNew.toString() + "",
                                        plfDailyNew.toString() + "",
                                        "" + nbreTrxMonthNewCompt,
                                        "" + nbreTrxWeekNewCompt,
                                        "" + nbreTrxDayNewCompt,
                                        this
                                    )
                            ) {

                                prefs.isPaymentDone = true
                                log(TAG, "isPaymentDone ${prefs.isPaymentDone}")
                                remainingCardCeilings = if (isLitreUnit) {
                                    UtilsCardInfo.readRecordLinear(
                                        mBankCard,
                                        icCpuReader,
                                        "2F07",
                                        "03",
                                        "32",
                                        this
                                    ).replace("F", "0")
                                }
                                else {
                                    UtilsCardInfo.readRecordLinear(
                                        mBankCard,
                                        icCpuReader,
                                        "2F07",
                                        "01",
                                        "32",
                                        this
                                    ).replace("F", "0")
                                }
                                prefs.remCardCeiling = remainingCardCeilings

                                infoCreditPostPayee = UtilsCardInfo.readRecordLinear(
                                    mBankCard,
                                    icCpuReader,
                                    "2F07",
                                    "06",
                                    "32",
                                    this
                                ).replace("F", "0")

                                compteurTrxDebit =
                                    UtilsCardInfo.getCompteurTransactionPanDebit(infoCreditPostPayee!!)
                                intentExtrasModel!!.compteurTrxDebit = compteurTrxDebit.toString()
                                UtilsCardInfo.updateCompteurTrxDebit(mBankCard,
                                icCpuReader, infoCreditPostPayee, compteurTrxDebit!! + 1, this)

                                if (stationMode != AppConstant.OFFLINE_TRX_MODE) {
                                    val readPlfPrep = UtilsCardInfo.readRecordLinear(
                                        mBankCard, icCpuReader, "2F09", "05",
                                        "28", this)
                                    UtilsCardInfo.updateNbreTrxOFFCompteur(
                                        mBankCard, icCpuReader, readPlfPrep,
                                        "" + (CARD_NBR_TRS_OFF + 1), this)
                                    CARD_NBR_TRS_OFF =
                                        UtilsCardInfo.getCardCeilingCount(readPlfPrep, 31, 35)
                                }
                                updateKm()
                                if (intentExtrasModel!!.mTransaction != null) {
                                    intentExtrasModel!!.mTransaction!!.preAuthAmount =
                                        intentExtrasModel!!.amount
                                }
                                getCardLimits()
                            }
                            returnValue = 1
                        } else {
                            returnValue = -2
                            errorMessage = resources.getString(R.string.transaction_failure)
                            return -2
                        }
                    } else {
                        returnValue = -2
                        errorMessage = resources.getString(R.string.soldeErr) + "!"
                        return -2
                    }
                } else {
                    returnValue = -2
                    errorMessage = resources.getString(R.string.plafond_depase)
                    return -2
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
            log(TAG, e.message + " " + e.cause)
            log(TAG, e.message + ExceptionUtils.getStackTrace(e))
            return -2
        }
        return returnValue
    }

    fun getCardLimits() {
        try {
            val cardCeilings =
                UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
                    "2F07", "02", "32", this).replace("F", "0")
            totalMonthlyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 0, 12) //Monthly card limit
            totalWeeklyLimit = UtilsCardInfo.getCardCeilings(cardCeilings, 12, 24) //weekly ceiling cap
            totalDailyLimit =
                UtilsCardInfo.getCardCeilings(cardCeilings, 24, 36) //ceiling day of the card
            // counter ceiling
            remainingCardCeilings = if (isLitreUnit) {
                UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
                    "2F07", "03", "32", this).replace("F", "0")
            } else {
                UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader,
                    "2F07", "01", "32", this).replace("F", "0")
            }
            remainingMonthlyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
            remainingWeeklyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 12, 24)
            remainingDailyAmount = UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)
            monthlyAmount = "" + (Math.max(0.0, totalMonthlyLimit - remainingMonthlyAmount))
            weeklyAmount = "" + (Math.max(0.0, totalWeeklyLimit - remainingWeeklyAmount))
            dailyAmount = "" + (Math.max(0.0, totalDailyLimit - remainingDailyAmount))
            intentExtrasModel!!.monthlyCeiling = monthlyAmount
            intentExtrasModel!!.weeklyCeiling = weeklyAmount
            intentExtrasModel!!.dailyCeiling = dailyAmount

            if (intentExtrasModel!!.mTransaction != null) {
                //intentExtrasModel!!.mTransaction!!.soldeCard = UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()    old implementation,, commented by altaf
                val balReadFromCard =  UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString().toDouble()

                // newly added to not send 0 balance in case of card remove --- altaf
                if(balReadFromCard > 0)
                    intentExtrasModel!!.mTransaction!!.soldeCard = UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()
                else {
                    val oldCardBal = intentExtrasModel!!.mTransaction!!.soldeCard!!.toDouble()
                    //val trxAmount = intentExtrasModel!!.mTransaction!!.amount
                    intentExtrasModel!!.mTransaction!!.soldeCard = ""+oldCardBal // (oldCardBal - trxAmount!!).toString()
                }
                //addition ends here

                log(TAG, "soldeCard:: 1 " + intentExtrasModel!!.mTransaction!!.soldeCard)
                intentExtrasModel!!.cardBalance = intentExtrasModel!!.mTransaction!!.soldeCard
            } else {
                intentExtrasModel!!.cardBalance = UtilsCardInfo.getAmountCardString(mBankCard, icCpuReader, 4, 12, this).toString()
            }
            REMAINING_NBR_TRS_MONTHLY =
                UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
            REMAINING_NBR_TRS_WEEKLY =
                UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 44, 52).toInt()
            REMAINING_NBR_TRS_DAILY =
                UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()
            val df = BigDecimal.valueOf(intentExtrasModel!!.dailyCeiling!!.toDouble())
            val wf = BigDecimal.valueOf(intentExtrasModel!!.weeklyCeiling!!.toDouble())
            val mf = BigDecimal.valueOf(intentExtrasModel!!.monthlyCeiling!!.toDouble())

            if (intentExtrasModel!!.mTransaction != null) {
                intentExtrasModel!!.mTransaction!!.dailyCeiling =
                    decimalFormat.format(df).replace(" ", "")
                intentExtrasModel!!.mTransaction!!.weeklyCeiling =
                    decimalFormat.format(wf).replace(" ", "")
                intentExtrasModel!!.mTransaction!!.monthlyCeiling =
                    decimalFormat.format(mf).replace(" ", "")
                intentExtrasModel!!.mTransaction!!.remainingDailyCount =
                    REMAINING_NBR_TRS_DAILY.toString()
                intentExtrasModel!!.mTransaction!!.remainingMonthlyCount =
                    REMAINING_NBR_TRS_MONTHLY.toString()
                intentExtrasModel!!.mTransaction!!.remainingWeeklyCount =
                    REMAINING_NBR_TRS_WEEKLY.toString()
                intentExtrasModel!!.mTransaction!!.kilometrage = intentExtrasModel!!.km
                intentExtrasModel!!.mTransaction!!.dateTransaction =  Support.dateToString(Date()) //Added By Divya, to fix this issue https://app.clickup.com/t/86775tccz
                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    fun updateKm() {
        if (intentExtrasModel!!.km != null && intentExtrasModel!!.vehicleVerificationTagId != null) {
            resultNFC = UtilsCardInfo.readBinaryFile(mBankCard, icCpuReader,
                "2F20", "5F", this)
            listnfcrecord = UtilsCardInfo.getNFCList(resultNFC!!)

            val itemNFC: NfcTagModel? = UtilsCardInfo.searchforNFCTag(
                listnfcrecord,
                NFCEnum.NFC,
                intentExtrasModel!!.vehicleVerificationTagId
            )
            if (itemNFC != null) {
                itemNFC.kmValeur = intentExtrasModel!!.km
                val dataNFCKM = UtilsCardInfo.updateKM(
                    itemNFC,
                    resultNFC!!, itemNFC.position
                )
                val resultado = UtilsCardInfo.updateBinaryFile(
                    mBankCard,
                    icCpuReader,
                    "2F20",
                    "5F",
                    dataNFCKM.substring(0, 190).uppercase(),
                    this
                )
            }
        }
    }


    private fun resetPayment() {
        if (!intentExtrasModel!!.splitPaymentModel!!.isFirstPaymentDone!!) {
            intentExtrasModel!!.splitPaymentModel!!.firstModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.firstPaymentName = ""

        } else {
            intentExtrasModel!!.splitPaymentModel!!.secondModeOfPayment = 0
            intentExtrasModel!!.splitPaymentModel!!.secondPaymentName = ""
        }
        intentExtrasModel!!.splitPaymentModel!!.isError = true
    }

    private fun showErrorDialog(title: String, msg: String?) {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_failed_message)
        dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        val tvTitle = dialog.findViewById<TextView>(R.id.title)
        val tvMessage = dialog.findViewById<TextView>(R.id.message)
        val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

        tvTitle.text = title
        tvMessage.text = msg


        var timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                //here you can have your logic to set text to edittext
                println("Popup Time Remaining: $minutes:$seconds")
            }

            override fun onFinish() {
                val unAttendantMode =
                    prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    if (intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!) {
                        val mIntent =
                            Intent(this@DebitCardLimitsActivity, SplitPaymentActivity::class.java)
                        resetPayment()
                        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                        startActivity(mIntent)
                        //   finish()
                    } else {
                        val mIntent = Intent(this@DebitCardLimitsActivity, MenuActivity::class.java)
                        startActivity(mIntent)
                        //finish()
                    }
                }
            }
        }

        if (prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()

        dialogButton.setOnClickListener {
            setBeep()
            timer.cancel()
            if (intentExtrasModel!!.splitPaymentModel!!.isSplitPayment!!) {
                val mIntent = Intent(this, SplitPaymentActivity::class.java)
                resetPayment()
                mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(mIntent)
                //  finish()
            } else {
                val mIntent = Intent(this, MenuActivity::class.java)
                startActivity(mIntent)
                finish()
            }

        }
        dialog.show()
    }

    override fun onBackPressed() {}

    var cardNotUpdateReceived = false

    private fun cardNotUpdated(message: String) {
        if (!cardNotUpdateReceived) {
            cardNotUpdateReceived = true
            if (intentExtrasModel!!.fleetCardRequestType == AppConstant.VOID_REQUEST) {
                intentExtrasModel!!.refundStatus = 0
                val title = resources.getString(R.string.error)
                val msg = message
                saveCancelTransaction() //saving canceled trx
                showErrorDialog(title, msg)
            } else if (intentExtrasModel!!.fleetCardRequestType == AppConstant.REFUND_REQUEST) {
                intentExtrasModel!!.refundStatus = 0
                if (intentExtrasModel!!.mTransaction != null) {
                    intentExtrasModel!!.mTransaction!!.refundStatus = "0"
                    intentExtrasModel!!.mTransaction!!.refundAmount =
                        intentExtrasModel!!.refundAmount!!
                    updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
                }
                gotoNextActivity()
            } else {
                val title = resources.getString(R.string.error)
                val msg = message//getString(R.string.failed_update)
                showErrorDialog(title, msg)
            }
        }
    }

    private fun performDebitCreditOperation(): Int {
        var result = 1
        if (intentExtrasModel!!.fleetCardRequestType == AppConstant.VOID_REQUEST) {

            result = creditCardLimits(intentExtrasModel!!.preAuthAmount!!, true)
        } else if (intentExtrasModel!!.fleetCardRequestType == AppConstant.REFUND_REQUEST) {
            var refundAmount = (intentExtrasModel!!.preAuthAmount!!.toDouble()
                    - intentExtrasModel!!.amount!!.toDouble()).toString()
            refundAmount = intentExtrasModel!!.refundAmount!!
            result = creditCardLimits(refundAmount, false)
        } else {
            result = debitCardLimits()
        }
        return result
    }

    private fun gotoNextActivity() {
        if (intentExtrasModel!!.fleetCardRequestType == AppConstant.VOID_REQUEST) {
            gotoAbortMessageActivity(
                getString(R.string.transaction),
                getString(R.string.transaction_cancelled)
            )
        } else if (intentExtrasModel!!.fleetCardRequestType == AppConstant.REFUND_REQUEST) {
            intent = Intent(this, TicketActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
            finish()
        } else {
            when (stationMode) {
                AppConstant.OFFLINE_TRX_MODE, AppConstant.AFTER_TRX_MODE -> {
                    intent = Intent(this, TicketActivity::class.java)
                }
                AppConstant.BEFORE_TRX_MODE -> {
                    if (intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL) {
                        intent = Intent(this, PumpSelectionActivity::class.java)
                    } else {
                        intent = Intent(this, TicketActivity::class.java)
                    }

                }
            }

            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
            finish()
            // finish()
        }
    }

    //region trx refund cancel critical methods

    private fun creditCardLimits(amount: String, updateTrxCount: Boolean): Int {
        try {
            val prd = if (mProduct != null)
                Utils.padZero(mProduct!!.productID.toString() + "", 4)
            else "0099" //added 0099 for refund //"0000"

            log(TAG, "Product id in Credit Card: $prd")

            val nowDate = Support.dateToStringX(Date())
            val transData = Utils.padZero(nowDate, 14).toString() +
            "01" + Utils.padZero(preferenceModel!!.stationID.toString() + "", 4) +
             prd + (if (intentExtrasModel!!.mTransaction != null)
             Support.dateToStringX(intentExtrasModel!!.mTransaction!!.dateTransaction!!)
            else "FFFFFFFFFFFFFF") + "FFFFFFFFFF"

            val ammtHex = Utils.amountToHex(amount)

            if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
                returnValue = 1
            }
            if (intentExtrasModel!!.mPinNumberCard != null &&
                UtilsCardInfo.verifyPIN(
                    mBankCard,
                    icCpuReader,
                    intentExtrasModel!!.mPinNumberCard,
                    this
                ))
            {
                if (cardType != null && cardType == AppConstant.LOYALTY_CARD) {
                    if (intentExtrasModel!!.loyaltyTrx) {
                        if (intentExtrasModel!!.mTransaction != null)
                            intentExtrasModel!!.mTransaction!!.detailArticle =
                                AppConstant.DETAIL_ARTICLE
                        returnValue = 1
                    }
                }
                else if (cardType != null && cardType == AppConstant.POSTPAID_CARD
                    || cardType == AppConstant.PREPAID_CARD)
                {
                    remainingCardCeilings = prefs.remCardCeiling!!
                    remainingMonthlyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 0, 12)
                    remainingWeeklyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 12, 24)
                    remainingDailyAmount =
                        UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 24, 36)

                    val dateLastPLF = UtilsCardInfo.getDateLastPLF(remainingCardCeilings!!)
                    REMAINING_NBR_TRS_MONTHLY =
                    UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 36, 44).toInt()
                    REMAINING_NBR_TRS_WEEKLY =
                    UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 44, 52).toInt()
                    REMAINING_NBR_TRS_DAILY =
                   UtilsCardInfo.getCardCeilings(remainingCardCeilings!!, 52, 60).toInt()

                    // To fix outofbound exception because of minus value
                    if (remainingMonthlyAmount <= 0) {
                        remainingMonthlyAmount = amount.toDouble()
                    } else {
                        remainingMonthlyAmount -= amount.toDouble()
                    }
                    if (remainingWeeklyAmount <= 0) {
                        remainingWeeklyAmount = amount.toDouble()
                    } else {
                        remainingWeeklyAmount -= amount.toDouble()
                    }
                    if (remainingDailyAmount <= 0) {
                        remainingDailyAmount = amount.toDouble()
                    } else {
                        remainingDailyAmount -= amount.toDouble()
                    }
                    if (updateTrxCount) {
                        if (REMAINING_NBR_TRS_MONTHLY <= 0) {
                            REMAINING_NBR_TRS_MONTHLY = 1
                        } else {
                            REMAINING_NBR_TRS_MONTHLY -= 1
                        }
                        if (REMAINING_NBR_TRS_WEEKLY <= 0) {
                            REMAINING_NBR_TRS_WEEKLY = 1
                        } else {
                            REMAINING_NBR_TRS_WEEKLY -= 1
                        }
                        if (REMAINING_NBR_TRS_DAILY <= 0) {
                            REMAINING_NBR_TRS_DAILY = 1
                        } else {
                            REMAINING_NBR_TRS_DAILY -= 1
                        }
                    }

                    intentExtrasModel!!.typeCard = cardType!!.toString()

                    readPlf = UtilsCardInfo.readCardCeilingLimits(remainingCardCeilings)

                    val plfMonthNew = ((remainingMonthlyAmount) * 100).toLong()
                    val plfWeekNew = ((remainingWeeklyAmount) * 100).toLong()
                    val plfDailyNew = ((remainingDailyAmount) * 100).toLong()

                    val nbreTrxMonthNewCompt = ((REMAINING_NBR_TRS_MONTHLY) * 100).toLong()
                    val nbreTrxWeekNewCompt = ((REMAINING_NBR_TRS_WEEKLY) * 100).toLong()
                    val nbreTrxDayNewCompt = ((REMAINING_NBR_TRS_DAILY) * 100).toLong()
                    printLog()

                    intentExtrasModel!!.transactionStepLog!!.actions!!.add(
                        Action(action = "TRX${prefs.logReferenceNo} - Credit Amount - " + amount)
                    )
                    val cardBalanceUpdated = UtilsCardInfo.credit(
                        mBankCard,
                        icCpuReader,
                        ammtHex,
                        authKey,
                        transData,
                        this@DebitCardLimitsActivity
                    )
                    if (cardBalanceUpdated &&
                        if (isLitreUnit)
                            UtilsCardInfo.updateInfoPlafondCardCompt(
                                mBankCard,
                                icCpuReader,
                                readPlf,
                                "03",
                                plfMonthNew.toString() + "",
                                plfWeekNew.toString() + "",
                                plfDailyNew.toString() + "",
                                "" + nbreTrxMonthNewCompt,
                                "" + nbreTrxWeekNewCompt,
                                "" + nbreTrxDayNewCompt,
                                this
                            )
                        else
                            UtilsCardInfo.updateInfoPlafondCardCompt(
                                mBankCard,
                                icCpuReader,
                                readPlf,
                                "01",
                                plfMonthNew.toString() + "",
                                plfWeekNew.toString() + "",
                                plfDailyNew.toString() + "",
                                "" + nbreTrxMonthNewCompt,
                                "" + nbreTrxWeekNewCompt,
                                "" + nbreTrxDayNewCompt,
                                this
                            ))
                    {

                        remainingCardCeilings = if (isLitreUnit) {
                            UtilsCardInfo.readRecordLinear(
                                mBankCard,
                                icCpuReader,
                                "2F07",
                                "03",
                                "32",
                                this
                            ).replace("F", "0")
                        }
                        else {
                            UtilsCardInfo.readRecordLinear(
                                mBankCard,
                                icCpuReader,
                                "2F07",
                                "01",
                                "32",
                                this
                            ).replace("F", "0")
                        }
                        prefs.remCardCeiling = remainingCardCeilings

                        infoCreditPostPayee = UtilsCardInfo.readRecordLinear(
                            mBankCard,
                            icCpuReader,
                            "2F07",
                            "06",
                            "32",
                            this
                        ).replace("F", "0")
                        val compteurTrxCredit =
                            UtilsCardInfo.getCompteurTransactionPanCredit(infoCreditPostPayee!!)
                        intentExtrasModel!!.compteurTrxCredit = compteurTrxCredit.toString()
                        UtilsCardInfo.updateCompteurTrxCredit(
                            mBankCard,
                            icCpuReader,
                            infoCreditPostPayee,
                            compteurTrxCredit + 1,
                            this
                        )

                        if (stationMode != AppConstant.OFFLINE_TRX_MODE) {
                            val readPlfPrep = UtilsCardInfo.readRecordLinear(
                                mBankCard,
                                icCpuReader,
                                "2F09",
                                "05",
                                "28",
                                this
                            )
                            UtilsCardInfo.updateNbreTrxOFFCompteur(
                                mBankCard,
                                icCpuReader,
                                readPlfPrep,
                                "" + (CARD_NBR_TRS_OFF),
                                this
                            )
                            CARD_NBR_TRS_OFF =
                                UtilsCardInfo.getCardCeilingCount(readPlfPrep, 31, 35)
                        }
                        getCardLimits()

                        returnValue = 1
                    } else
                        returnValue = -2

                } else {
                    returnValue = -2
                    errorMessage = resources.getString(R.string.plafond_depase)
                    return -2
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
            log(TAG, e.message + " " + e.cause)
            log(TAG, e.message + ExceptionUtils.getStackTrace(e))
            // mViewModel.generateLogs(e.message!!,0)
            return -2
        }
        return returnValue
    }
    //region just for testing purpose
    private fun updateCardRemainingLimits(
        readCard: String?,
        monthlyLimit: String?,
        weeklyLimit: String?,
        dailyLimit: String?,
        monthlyTrxLimit: String?,
        weeklyTrxLimit: String?,
        dailyTrxLimit: String?,
        mBankCard: BankCard?,
        icCpuReader: UICCpuReader?,
        context: Context?
    ): Boolean {
        var updatedRecord1 = ""
        val sm = sdf
        val date = Date()
        val dateplfrecord2 = sm.format(date).replace("-".toRegex(), "")

        updatedRecord1 =
            UtilsCardInfo.prepareRecordLinear(readCard, 0, 12, Utils.padZero(monthlyLimit, 12))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
            updatedRecord1,
            12,
            24,
            Utils.padZero(weeklyLimit, 12)
        )
        updatedRecord1 =
            UtilsCardInfo.prepareRecordLinear(updatedRecord1, 24, 36, Utils.padZero(dailyLimit, 12))
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
            updatedRecord1,
            36,
            44,
            Utils.padZero(monthlyTrxLimit, 8)
        )
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
            updatedRecord1,
            44,
            52,
            Utils.padZero(weeklyTrxLimit, 8)
        )
        updatedRecord1 = UtilsCardInfo.prepareRecordLinear(
            updatedRecord1,
            52,
            60,
            Utils.padZero(dailyTrxLimit, 8)
        )

        val result = if (isLitreUnit) {
            UtilsCardInfo.updateRecordLinear(
                mBankCard,
                icCpuReader,
                "2F07",
                "03",
                "32",
                updatedRecord1,
                context
            )
        } else {
            UtilsCardInfo.updateRecordLinear(
                mBankCard,
                icCpuReader,
                "2F07",
                "01",
                "32",
                updatedRecord1,
                context
            )
        }
        return result.contains("9000")
    }

    private fun dummyCardLimits(): Int {
        var isUpdated = false
        try {
            val readPlfPost = UtilsCardInfo.readInfoPlafondCardPost(
                mBankCard,
                icCpuReader,
                this@DebitCardLimitsActivity
            )
            val readPlfPrep = UtilsCardInfo.readInfoPlafondCardPre(
                mBankCard,
                icCpuReader,
                this@DebitCardLimitsActivity
            )

            if (
                UtilsCardInfo.verifyPIN(
                    mBankCard,
                    icCpuReader,
                    intentExtrasModel!!.mPinNumberCard,
                    this@DebitCardLimitsActivity
                ) &&
                UtilsCardInfo.updateInfoPlafondCardPost(
                    readPlfPost,
                    "" + (******** * 100.toLong()),
                    "" + (2800000 * 100.toLong()),
                    "" + (280000 * 100.toLong()),
                    255.toString() + "",
                    255.toString() + "",
                    255.toString() + "",
                    mBankCard, icCpuReader, this@DebitCardLimitsActivity
                ) &&
                updateCardRemainingLimits(
                    readPlfPost,
                    "" + (0 * 100).toLong(),
                    "" + (0 * 100).toLong(),
                    "" + (0 * 100).toLong(),
                    "0",
                    "0",
                    "0",
                    mBankCard,
                    icCpuReader,
                    this@DebitCardLimitsActivity
                )
            ) {
                isUpdated = if (cardType == AppConstant.PREPAID_CARD) {
                    UtilsCardInfo.updateInfoPlafondCardPre(
                        readPlfPrep,
                        "" + (1 * 100).toLong(),
                        "" + ******** * 100.toLong(),
                        "255",
                        "1",
                        "1",
                        "0",
                        intentExtrasModel!!.cardDiscountId.toString(),
                        mBankCard, icCpuReader, this@DebitCardLimitsActivity
                    )
                } else {
                    true
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            log(TAG, e.message + " " + e.cause)
            isUpdated = false
        }
        return if (isUpdated) 1 else -2
    }

    //endregion
    private fun saveCancelTransaction() {
        Handler(Looper.getMainLooper()).post {
            val mTransaction = TransactionModel()
            val pan = intentExtrasModel!!.panNumber
            val preAuthAmt = intentExtrasModel!!.preAuthAmount!!
            mTransaction.idTerminal =
                if (prefs.getReferenceModel()!!.terminal != null) prefs.getReferenceModel()!!.terminal!!.terminalId else 0
            mTransaction.idTypeTransaction =
                1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
            mTransaction.idProduit = if (mProduct != null) mProduct!!.productID else null
            mTransaction.codePompiste = intentExtrasModel!!.mPinNumberAttendant
            mTransaction.idPompiste =
                prefs.getPompisteId(intentExtrasModel!!.mPinNumberAttendant.toString())
            mTransaction.dateTransaction = Support.dateToString(Date())
            mTransaction.amount = 0.0//intentExtrasModel!!.amount!!.toDouble()
            mTransaction.quantite = 0.0
            mTransaction.unitPrice = 0.0
            mTransaction.reference = ""
            mTransaction.flagController = 0
            mTransaction.sequenceController = ""
            mTransaction.tagNFC = intentExtrasModel!!.badge
            mTransaction.kilometrage = intentExtrasModel!!.km
            mTransaction.pan = pan
            mTransaction.nomPorteur = cardHolderName
            mTransaction.dateExp = "" //cardModel!!.expiredDate
            mTransaction.detailArticle = ""
            mTransaction.flagTelecollecte = 0
            mTransaction.modepay = intentExtrasModel!!.typePay
            mTransaction.panLoyalty = ""
            mTransaction.soldeCard = ""
            mTransaction.pumpId = ""
            mTransaction.reference =
                "TRX" + Support.generateReference(this)/*Support.generateReference(mTransaction.dateTransaction, intentExtrasModel!!.compteurTrxDebit, "", this)*/
            //mTransaction.reference = "TRX" + Support.genererReference(mTransaction.dateTransaction, "", "", this)
            mTransaction.preAuthAmount = preAuthAmt
            mTransaction.refundAmount = intentExtrasModel!!.refundAmount
            mTransaction.refundStatus = "0"

            // newly added to not send 0 balance in case of card remove --- altaf
            if(intentExtrasModel!!.mTransaction!= null){
                val oldCardBal = intentExtrasModel!!.mTransaction!!.soldeCard!!.toDouble()
                //val trxAmount = intentExtrasModel!!.mTransaction!!.amount
                mTransaction.soldeCard = ""+ oldCardBal  //(oldCardBal - trxAmount!!).toString()
            }
            // addition end here

            try {
                log(TAG, "TRX CANCELED: ${Gson().toJson(mTransaction)}")

                updateTransactionByReferenceId(mTransaction)
            } catch (e: SQLException) {
                e.printStackTrace()
                log(TAG, e.message + " " + e.cause)
            }
        }
    }

    override fun onDestroy() {
        try {
            cardRemoveChecker.cancel()
        } catch (e:Exception) {
            e.printStackTrace()
        }
        super.onDestroy()
    }

    override fun onPause() {
        super.onPause()

    }
    private val cardRemoveChecker = object : CountDownTimer(3000,1000){
        override fun onTick(p0: Long) {
            if(BuildConfig.POS_TYPE == AppConstant.PAX){
                if(!IccTester.getInstance().detect( 0.toByte()) && !isOnPoxExecuted)
                {
                    proccedOnPostExecute(-1)
                }
            }
            if (BuildConfig.POS_TYPE == "B_TPE" && !isCardDetected() && !isOnPoxExecuted) {
                proccedOnPostExecute(-1)
            }
        }

        override fun onFinish() {
            this.start()
        }

    }
    override fun onStop() {
        try {
            cardRemoveChecker.cancel()
        } catch (e:Exception) {
            e.printStackTrace()
        }
        super.onStop()
    }

}