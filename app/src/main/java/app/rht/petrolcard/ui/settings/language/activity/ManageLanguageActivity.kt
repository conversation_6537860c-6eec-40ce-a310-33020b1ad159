package app.rht.petrolcard.ui.settings.language.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityManageCardBinding
import app.rht.petrolcard.ui.badge.activity.BadgeActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.card.common.model.CardItemModel
import app.rht.petrolcard.utils.constant.AppConstant
import kotlinx.android.synthetic.main.toolbar.view.*
import java.util.ArrayList

import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.settings.card.unblockcard.activity.UnblockActivity
import app.rht.petrolcard.ui.settings.card.unlockpin.activity.EnterNewPinActivity
import app.rht.petrolcard.utils.LocaleManager
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.paxutils.system.SysTester


class ManageLanguageActivity: BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<CardItemModel> {

    private lateinit var mBinding: ActivityManageCardBinding

    private val TAG = SettingsActivity::class.simpleName

    private var intentExtrasModel: IntentExtrasModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_manage_card)

        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_manage_card)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        intentExtrasModel = IntentExtrasModel()
        setupItemsInList()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.language)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener { finish() }
    }

    override fun setObserver() {

    }
    override fun onItemClick(view: View, item: CardItemModel) {
        setBeep()
        when(item.id){
            "1" -> {
             changeLanguage(LocaleManager.LANGUAGE_ENGLISH)
            }
            "2" -> {
                changeLanguage(LocaleManager.LANGUAGE_FRENCH)
            }
            "3" -> {
                changeLanguage(LocaleManager.LANGUAGE_ARABIC)
            }


        }
    }
    fun changeLanguage(language:String)
    {
        LocaleManager.setNewLocale(this,language)
        val refresh = Intent(this, MenuActivity::class.java)
        startActivity(refresh)
        finish()
    }

    private var settingsItems : ArrayList<CardItemModel> = ArrayList()
    private fun setupItemsInList(){
        settingsItems.add(CardItemModel(R.drawable.english,getString(R.string.english),"1","#F97F51"))
        settingsItems.add(CardItemModel(R.drawable.french,getString(R.string.french),"2","#1B9CFC"))
      //  settingsItems.add(CardItemModel(R.drawable.arabic,getString(R.string.arabic),"3","#FC427B"))
        mBinding.rvSettings.removeAllViews()
        val mSettingsAdapter = RecyclerViewArrayAdapter(settingsItems,this)
        mBinding.rvSettings.adapter = mSettingsAdapter
        mSettingsAdapter.setContext(this)
        mSettingsAdapter.notifyDataSetChanged()

    }

}