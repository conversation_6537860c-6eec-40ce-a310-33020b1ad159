package app.rht.petrolcard.ui.loyalty.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.loyalty.model.LoyaltyGiftHistory
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.Support.Companion.log
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

class RedeemHistoryViewModel  constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {

    private val TAG = RedeemHistoryViewModel::class.simpleName
    var loyaltyGiftHistoryResponse = MutableLiveData<BaseResponse<List<LoyaltyGiftHistory>>>()
    fun getLoyaltyGifts(pan:String) {

        log(TAG,"GETTING LOYALTY CARD GIFT HISTORY")

        val sn = MainApp.sn!!
        val url = (preferencesHelper.baseUrl+ NetworkRequestEndPoints.GET_LOYALTY_GIFTS_HISTORY).replace("-tpe","")
        val serial = sn.toRequestBody("text/plain".toMediaType())
        val card = pan.toRequestBody("text/plain".toMediaType())

        requestData(mNetworkService.getGiftHistoryList(url,serial,card), {
         loyaltyGiftHistoryResponse.postValue(it)
        })
    }

}