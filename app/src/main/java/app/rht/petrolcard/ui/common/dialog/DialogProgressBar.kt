package app.rht.petrolcard.ui.common.dialog

import android.app.ActionBar
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.*
import android.view.WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.RedirectModel
import app.rht.petrolcard.baseClasses.dialog.BaseDialogBottomSheet
import app.rht.petrolcard.databinding.DialogProgressbarBinding
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel

class DialogProgressBar : BaseDialogBottomSheet<CommonViewModel>(CommonViewModel::class), View.OnClickListener {

    var listener: OnClickListener? = null
    private lateinit var mBinding: DialogProgressbarBinding
    var title = "Processing..."
    var message = ""
    var percentage = ""
    var handler = Handler()
    var iscancel = false
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.HONEYCOMB)
        {
            dialog!!.setCanceledOnTouchOutside(false)
        }
        else
        {
            dialog!!.window!!.clearFlags(FLAG_WATCH_OUTSIDE_TOUCH)
        }
        mBinding = DialogProgressbarBinding.inflate(getThemeLayoutInflater(inflater), container, false)
        dialog!!.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog!!.window!!.setBackgroundDrawableResource(android.R.color.transparent)
//        dialog!!.window!!.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE, WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
        seTCancelable(false)
        mBinding.actionDone.setOnClickListener(this)
        mBinding.title.text = title
        mBinding.message.text = message
        return mBinding.root
    }

    interface OnClickListener {
        fun onClick()
    }
    fun setMessageValue(message:String)
    {
        mBinding.message.text = message
    }
    fun setView(message:String)
    {
        mBinding.message.text = message
        mBinding.title.text = getString(R.string.telecollect_success)
        mBinding.arrowSuccess.visibility = View.VISIBLE
        mBinding.arrowAnimation.visibility = View.GONE
        mBinding.actionDone.visibility = View.GONE
        Handler(Looper.getMainLooper()).postDelayed({  mViewModel.redirect.postValue(
            RedirectModel(
                mClass = MenuActivity::class.java,
                isTopFinish = true,
                flagIsTopClearTask = true
            )
        ) }, 3000)

    }
    override fun onClick(p0: View?) {
        when (p0!!.id) {
            R.id.action_cancel ->
            {
                iscancel=true
                listener?.onClick()
            }
            R.id.action_done ->
            {
                iscancel=true
                listener?.onClick()
            }
        }
    }

    override fun setObserver() {

    }
}