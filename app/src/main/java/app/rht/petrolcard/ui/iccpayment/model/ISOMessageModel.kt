package app.rht.petrolcard.ui.iccpayment.model

import androidx.annotation.Keep

@Keep
class ISOMessageModel(
    val transactionAmount:String,
    val price:String,
    val attendantCode:String,
    val attendantID:String,
    val articleID:String,
    val authenticationType:String,
    val paymentType:String,
    val stationID:String,
    val sequenceNumber:String,
    val panNumber:String,
    val terminalID:String,
    val referenceNumber:String,
    val vehicleTagNumber:String,
    val volume:String
)

class ValidationISOModel(
    val articleID:String,
    val stationID:String,
    val panNumber:String,
    val vehicleTagNumber:String,
    val paymentType: String
)