package app.rht.petrolcard.ui.loyalty.fragments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import androidx.appcompat.app.AlertDialog
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.baseClasses.fragment.BaseFragment
import app.rht.petrolcard.databinding.FragmentAvailableGiftsBinding
import app.rht.petrolcard.ui.loyalty.activity.LoyaltyBalanceActivity
import app.rht.petrolcard.ui.loyalty.model.GiftQrCode
import app.rht.petrolcard.ui.loyalty.model.LoyaltyGift
import app.rht.petrolcard.ui.loyalty.utils.QRCode
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.loyalty.viewmodel.AvailableGiftsViewModel
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import java.util.*


class AvailableGiftsFragment : BaseFragment<AvailableGiftsViewModel>(AvailableGiftsViewModel::class), RecyclerViewArrayAdapter.OnItemClickListener<LoyaltyGift>{

    companion object {
        val TAG = AvailableGiftsFragment::class.simpleName
        private var loyaltyGifts = ArrayList<LoyaltyGift>()
    }

    private lateinit var adapter : RecyclerViewArrayAdapter<LoyaltyGift>
    private lateinit var mBinding: FragmentAvailableGiftsBinding
    private var cardNumber = ""

    override fun createDataBinding(inflater: LayoutInflater, container: ViewGroup?): View {
        mBinding = FragmentAvailableGiftsBinding.inflate(inflater, container, false)
        mBinding.lifecycleOwner = this
        mBinding.model = mViewModel

        return mBinding.root
    }

    override fun onStart() {
        super.onStart()
        init()
    }

    override fun onError(errorData: ErrorData) {

    }

    fun init() {
        //mViewModel.getNotificationsAppointment()// api call
        loading(true)
        setUpRecyclerView()
    }

    private fun setUpRecyclerView() {
        adapter = RecyclerViewArrayAdapter(loyaltyGifts, this)
        mBinding.rvGifts.adapter = adapter
    }

    override fun onItemClick(view: View, item: LoyaltyGift) {
        MultiClickPreventer.preventMultiClick(view)
        println("${item.name} -- ${item.point}")
        showRedeemDialog(item)
    }

    fun getAvailableGifts(pan:String){
        cardNumber = pan
        loading(true)
        mViewModel.getLoyaltyGifts(pan)
    }

    override fun setObserver() {
        mViewModel.loyaltyGiftResponse.observe(this){
            val response = it
            if(response.reponse == "1"){
                loading(false)
                if(response.contenu!!.isEmpty())
                    noDataFound(true)
                else{
                    noDataFound(false)
                    loading(false)
                    loyaltyGifts.clear()
                    loyaltyGifts.addAll(response.contenu)
                    //rvGifts.adapter!!.notifyDataSetChanged()
                }

            }
            else {
                noDataFound(true)
                showToast(response.error)
            }
        }

        mViewModel.redeemGiftResponse.observe(this){
            val response = it
            if(response.reponse=="0"){
                showToast(response.error)
            }
            else
            {
                //val tokenNumber = response.contenu!!.token
                PrintReceipt(response.contenu!!.token!!).execute()
            }
        }
    }


    inner class PrintReceipt(var tokenNumber: String) : CoroutineAsyncTask<Void, Void,Void?>(){

        override fun onPreExecute() {
            super.onPreExecute()
            showPrintDialog()
        }

        override fun doInBackground(vararg params: Void): Void? {
            printReceipt(tokenNumber)
            return null
        }
        private fun printReceipt(tokenNumber: String){
            try{
                showToast(getString(R.string.gift_redeemed_successfully))
            val terminal = prefs.getReferenceModel()!!.terminal!!
            val commands = ArrayList<PrintCmd>()
            //commands.add(PrintCmd(Support.logoImageBytes(),128,AlignmentType.CENTER, PrintContentType.IMAGE))
            commands.add(PrintCmd(" ", AlignmentType.CENTER))
            commands.add(PrintCmd("\n" + Support.getDateTicket(Date()) + " (" + if(terminal != null)"${ terminal.terminalId })" else "(0)",AlignmentType.CENTER))
            commands.add(PrintCmd("\n"+getString(R.string.loyalty_gift),AlignmentType.CENTER))
            commands.add(PrintCmd("\n${terminal.stationName} (${terminal.stationId})"))
            commands.add(PrintCmd("\n${terminal.address}"))
            commands.add(PrintCmd("\n${terminal.city}"))
            commands.add(PrintCmd("\n${getString(R.string.fiscal_label)} : ${terminal.fiscalId}"))
            commands.add(PrintCmd("\n${getString(R.string.pan_label)} : $cardNumber"))
            commands.add(PrintCmd("\n${getString(R.string.gift_label)} : "+if(selectedGift!=null)selectedGift!!.name else "****"))

            val qrCode = GiftQrCode()
            qrCode.date = Support.getDateTicket(Date())
            qrCode.term = terminal.terminalId
            qrCode.sta = terminal.stationId
            qrCode.pmp = "*"
            qrCode.pan = cardNumber
            qrCode.tag = ""
            qrCode.ref = tokenNumber
            qrCode.pdt = if (selectedGift != null) selectedGift!!.id else 0
            qrCode.qte = ""
            qrCode.pu = ""
            qrCode.amount = if (selectedGift != null) selectedGift!!.point.toString() + "" else "0"
            qrCode.flag = 0
            qrCode.sequence = ""
            qrCode.article = if (selectedGift != null) selectedGift!!.name else "***"

            val barCodeContent = qrCode.createJSON()

            commands.add(PrintCmd(QRCode.generateControlKey(barCodeContent)!!,512,512, AlignmentType.CENTER,PrintContentType.IMAGE))
            commands.add(PrintCmd("--- "+getString(R.string.loyalty_reciept)+" ---",AlignmentType.CENTER))
            //commands.add(PrintCmd(Support.footerImageBytes(), 256, AlignmentType.CENTER, PrintContentType.IMAGE))

            TicketPrinter(requireActivity()).printTicket(commands)
            requireActivity().finish()
            }
            catch (e:Exception) {
                e.printStackTrace()
            }
        }
    }



    private fun loading(visible:Boolean){
        if(visible)
            mBinding.loadingLayout.visibility = View.VISIBLE
        else
            mBinding.loadingLayout.visibility = View.GONE

    }
    private fun noDataFound(visible:Boolean){
        if(visible)
            mBinding.noDataLayout.visibility = View.VISIBLE
        else
            mBinding.noDataLayout.visibility = View.GONE
    }

    private var selectedGift: LoyaltyGift? = null

    private fun showRedeemDialog(gift: LoyaltyGift) {
        selectedGift = gift
        val dialogLang = AlertDialog.Builder(requireActivity(), R.style.MyStyleDialog).show()
        dialogLang.setContentView(R.layout.dialog_redeem_gift)
        dialogLang.findViewById<View>(R.id.myTextViewOk)!!.setOnClickListener {
            dialogLang.dismiss()
            if (activity != null) (activity as LoyaltyBalanceActivity).setBeep()
            val pan = cardNumber.replace(" ","")
            mViewModel.redeemGift(pan,"${gift.id}")

        }
        dialogLang.findViewById<View>(R.id.myTextViewKo)!!.setOnClickListener {
            if (activity != null) (activity as LoyaltyBalanceActivity).setBeep()
            dialogLang.dismiss()
        }

        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = dialogLang.window
        lp.copyFrom(window!!.attributes)

        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogLang.setCancelable(false)
        dialogLang.setCanceledOnTouchOutside(false)
    }


    private fun showPrintDialog() {
        val printerDialog = AlertDialog.Builder(requireActivity(), R.style.MyStyleDialog).show()
        printerDialog.setContentView(R.layout.dialog_printing)
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = printerDialog.window
        lp.copyFrom(window!!.attributes)

        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        printerDialog.setCancelable(false)
        printerDialog.setCanceledOnTouchOutside(false)
    }

}