package app.rht.petrolcard.ui.loyalty.activity

import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import android.text.Html
import android.view.Window
import android.widget.Button
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityQrScanBinding
import app.rht.petrolcard.utils.constant.AppConstant
import kotlinx.android.synthetic.main.activity_loyalty_activation.*
import kotlinx.android.synthetic.main.toolbar.view.*
import java.util.*
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.database.baseclass.PriceDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.ui.loyalty.utils.QRCode
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.loyalty.viewmodel.GiftRedeemViewModel
import app.rht.petrolcard.ui.ticket.activity.TicketType
import app.rht.petrolcard.ui.ticket.model.BarCode
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import com.google.zxing.BarcodeFormat
import com.google.zxing.Result
import com.kaopiz.kprogresshud.KProgressHUD
import kotlinx.android.synthetic.main.activity_qr_scan.*

import me.dm7.barcodescanner.zxing.ZXingScannerView
import net.sqlcipher.database.SQLiteException

val FUEL_GIFT_REQUEST = "fuelGiftRequest"
class QrScanActivity : BaseActivity<GiftRedeemViewModel>(GiftRedeemViewModel::class) , ZXingScannerView.ResultHandler {
    private val TAG = QrScanActivity::class.simpleName
    private lateinit var mBinding: ActivityQrScanBinding

    private var fuelGiftRequest = false


    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_qr_scan)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        initViews()
    }

    private fun initViews() {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.redeem_gift)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            onBackPressed()
        }

        progressView = KProgressHUD.create(this)
            .setStyle(KProgressHUD.Style.SPIN_INDETERMINATE)
            .setLabel(getString(R.string.please_wait))
            .setDetailsLabel(getString(R.string.verifying_loyalty_details))
            .setCancellable(false)
            .setAnimationSpeed(2)
            .setDimAmount(0.5f)


        scanLoyaltyCard()
    }

    private fun scanQr(){
        mBinding.qrScanner.setResultHandler(this)
        val formats = listOf(BarcodeFormat.QR_CODE)
        mBinding.qrScanner.setFormats(formats)
        mBinding.qrScanner.flash = false
        mBinding.qrScanner.setAutoFocus(true)
        mBinding.qrScanner.startCamera()
        mBinding.qrScanner.setLaserEnabled(true)
        mBinding.qrScanner.isSoundEffectsEnabled = true

    }

    private fun stopScanning(){
        mBinding.qrScanner.stopCameraPreview()
        mBinding.qrScanner.stopCamera()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopScanning()
    }

    //region NFC scanning
    private fun scanNfcTag(cardNumber:String){
        val intent = Intent(this, NfcScanActivity::class.java)
        intent.putExtra(AppConstant.CARD_NUMBER,cardNumber)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,false)
        nfcTagResultLauncher.launch(intent)
    }

    private var scannedTag = ""
    private var nfcTagResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                scannedTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                if(!nfcTag.equals(scannedTag,true)){
                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag),LoyaltyDashboardActivity::class.java)
                }
                else
                {
                    scanQr()
                }
            }
            else{
                gotoAbortMessageActivity(getString(R.string.try_again),getString(R.string.nfc_scanning_failed))
            }
        }
    }
    //endregion

    //region Card scanning
    private var cardNumber = ""
    private var cardHolder = ""
    private var nfcTag = ""
    private fun scanLoyaltyCard() {
        val intent = Intent(this, ICCLoyaltyActivity::class.java)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,true)
        loyaltyCardResultLauncher.launch(intent)
    }
    private var loyaltyCardResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){

                cardNumber = data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)!!
                cardHolder = data.getStringExtra(AppConstant.LOYALTY_CARD_HOLDER)!!
                nfcTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!

                log(TAG,"data received ++++ ${data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)} ----  $cardHolder --- $nfcTag")
                scanNfcTag(cardNumber)
            }
            else{
                gotoAbortMessageActivity(getString(R.string.try_again),getString(R.string.card_reading_failed))
            }
        }
    }
    //endregion


    override fun setObserver() {
        mViewModel.redeemGiftResponse.observe(this){
            log(TAG,"########### $it")
            progressView.dismiss()
            if(it.reponse =="0"){
                //showToast(it.error!!)
                gotoAbortMessageActivity(getString(R.string.error),it.error!!,LoyaltyDashboardActivity::class.java)
            }
            else {
                qrScanner.stopCameraPreview()
                qrScanner.stopCamera()

                if (fuelGiftRequest) {
                    showToast(getString(R.string.coming_soon))
                } else {
                    PrintTicketTask(TicketType.Customer).execute()
                }
            }
        }
    }

    private lateinit var giftCode:BarCode
    override fun handleResult(rawResult: Result?) {
        log(TAG,"SCAN RESULT: $rawResult")
        if (rawResult != null) {
            try {
                setBeep()
                var jsonDecrypte = ""
                jsonDecrypte =
                    if (BuildConfig.POS_TYPE == "LANDI")
                        rawResult.toString()
                    else QRCode.decrypt(
                        rawResult.toString()
                    )
                log(TAG, "----------- Decrypted QR code  ! $jsonDecrypte---------")
                val gson = Gson()

                if (jsonDecrypte.isNotEmpty())
                    giftCode = gson.fromJson(jsonDecrypte, BarCode::class.java)
               log(TAG, "----------- Decrypted QR code  ! " + (if (giftCode != null) giftCode.article else "***") + " : " + (if (giftCode != null) giftCode.amount.toString() + "" else "0 DHS") + "---------")
                if (giftCode.article != null && giftCode.amount != null) {
                    showMessageOKCancel(getString(R.string.gift_label)+": ${giftCode.article}\n${getString(R.string.points)}: ${giftCode.amount}") { dialog, which ->
                        setBeep()
                        if (which == DialogInterface.BUTTON_POSITIVE) {
                            showLoyaltyPointsDialog()
                            dialog.dismiss()
                        } else {
                            mBinding.qrScanner.resumeCameraPreview(this@QrScanActivity)
                        }
                    }

                } else showMessageCancel(getString(R.string.invalid_qr_code)
                ) { dialog, which ->
                    setBeep()
                    mBinding.qrScanner.resumeCameraPreview(this@QrScanActivity)
                }
            } catch (Ex: JsonSyntaxException) {
                Ex.printStackTrace()
                log(TAG, "----------- Decrypted QR code json parse exception  : " + Ex.message + "---------")
            }
        }
    }

    private fun showMessageOKCancel(message: String, okListener: DialogInterface.OnClickListener) {
        AlertDialog.Builder(this)
            .setMessage(message)
            .setPositiveButton(getString(R.string.confirm), okListener)
            .setNegativeButton(getString(R.string.cancel), okListener)
            .setCancelable(false)
            .create()
            .show()
    }

    private fun showMessageCancel(message: String, okListener: DialogInterface.OnClickListener) {
        AlertDialog.Builder(this)
            .setMessage(message)
            .setNegativeButton(getString(R.string.cancel), okListener)
            .setCancelable(false)
            .create()
            .show()
    }

    private lateinit var progressView : KProgressHUD

    private fun showLoyaltyPointsDialog(){
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_loyalty_point)
        dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        dialog.findViewById<TextView>(R.id.tvGiftName).text = giftCode.article
        dialog.findViewById<TextView>(R.id.tvGiftPoints).text = "${giftCode.amount} ${getString(R.string.points)}"

        val dialogButton: Button = dialog.findViewById(R.id.btnVerify) as Button
        dialogButton.setOnClickListener {
            setBeep()
            verifyLoyaltyDetails()
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun verifyLoyaltyDetails(){
        progressView.show()
        try {
            if (giftCode != null) {
                val productDao = ProductsDao()
                productDao.open()
                val productToCheckout = productDao.getProductById(giftCode.pdt)
                productDao.close()
                if (productToCheckout != null) {
                    val priceDao = PriceDao()
                    priceDao.open()
                    val productToCheckout2 = priceDao.selectionnerLastPriceByIdProduit(productToCheckout.productID)
                    priceDao.close()
                }
            }
        } catch (Ex: SQLiteException) {
            log(TAG,Ex.message!!)
            Support.showExceptionError(this, this)
        }

        progressView.show()
        mViewModel.redeemGift(cardNumber,giftCode.ref!!)
    }

    lateinit var mPrintDialog : AlertDialog
    inner class PrintTicketTask(val ticketType:TicketType) : CoroutineAsyncTask<Void,Void,Void?>(){

        override fun onPreExecute() {
            super.onPreExecute()
            mPrintDialog = getMyPrintDialog()
            mPrintDialog.show()
        }

        override fun doInBackground(vararg params: Void): Void? {
            printGiftReceipt(ticketType)
            return null
        }

        override fun onPostExecute(result: Void?) {
            super.onPostExecute(result)
            mPrintDialog = getMyPrintDialog()
            mPrintDialog.show()
            if(ticketType == TicketType.Customer){
                showAttendantPrintDialog()
            }
            else
            {
                gotoSuccessMessageActivity(getString(R.string.success),getString(R.string.loyalty_details_updated),LoyaltyDashboardActivity::class.java)
            }
        }
    }

    private fun printGiftReceipt(ticketType:TicketType) {

        try{
            val terminal = prefs.getReferenceModel()!!.terminal!!
            val commands = ArrayList<PrintCmd>()

            commands.add(PrintCmd(Support.logoImageBytes(this),128,128,AlignmentType.CENTER, PrintContentType.IMAGE))
            commands.add(PrintCmd(" ", AlignmentType.CENTER))
            commands.add(PrintCmd("\n" + Support.getDateTicket(Date()) + " (" + if(terminal != null)"${ terminal.terminalId })" else "(0)", AlignmentType.CENTER))
            commands.add(PrintCmd("\n"+getString(R.string.gift_redeem), AlignmentType.CENTER, true))
            commands.add(PrintCmd("\n${terminal.stationName} (${terminal.stationId})"))
            commands.add(PrintCmd("\n${terminal.address}"))
            commands.add(PrintCmd("\n${terminal.city}"))
            commands.add(PrintCmd("\n${getString(R.string.fiscal_label)} : ${terminal.fiscalId}"))

            commands.add(PrintCmd("\n${getString(R.string.pan_label)} : $cardNumber"))
            commands.add(PrintCmd("\n${getString(R.string.gift_label)} : "+if(giftCode.article!=null)giftCode.article!! else "****"))
            commands.add(PrintCmd("\n${getString(R.string.points)} : "+giftCode.amount))
            commands.add(PrintCmd(" "))

            if(ticketType== TicketType.Customer){
                commands.add(PrintCmd("--- "+getString(R.string.customer_receipt)+" ---", AlignmentType.CENTER))
                commands.add(PrintCmd(Support.footerImageBytes(this), 256,128, AlignmentType.CENTER, PrintContentType.IMAGE))
            }
            else {
                commands.add(PrintCmd("--- "+getString(R.string.merchant_receipt)+" ---", AlignmentType.CENTER))
            }

            TicketPrinter(this).printTicket(commands)

        }
        catch (e:Exception) {
            e.printStackTrace()
        }
    }

    fun showAttendantPrintDialog(){
        MaterialDialog(this)
            .title(text = getString(R.string.confirm))
            .message(text = Html.fromHtml("<font color='#000000'>"+getString(R.string.do_you_want_to_print_merchant_receipt)+"</font>"))
            .cancelable(false)
            .show {
                cornerRadius(res = R.dimen.my_corner_radius)
                positiveButton(text = getString(R.string.print),click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        setBeep()
                        PrintTicketTask(TicketType.Attendant).execute()
                    }
                })
                negativeButton(text = getString(R.string.cancel), click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        setBeep()
                        val i = Intent(this@QrScanActivity,LoyaltyDashboardActivity::class.java)
                        startActivity(i)
                        finish()
                    }
                })
            }
    }
}