package app.rht.petrolcard.ui.product.model;

import java.util.Comparator;

import app.rht.petrolcard.ui.transactionlist.model.DeviceClassGAFT;

public class DeviceClassComparator implements Comparator<DeviceClassGAFT> {
    @Override
    public int compare(DeviceClassGAFT obj1, DeviceClassGAFT obj2) {
        int ob1 = Integer.parseInt(String.valueOf(obj1.getTransactionSeqNo()));
        int ob2 = Integer.parseInt(String.valueOf(obj2.getTransactionSeqNo()));
        return (ob1 < ob2) ? -1 : (ob1 > ob2) ? 1 : 0;
    }
}