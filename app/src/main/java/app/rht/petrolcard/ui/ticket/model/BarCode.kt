package app.rht.petrolcard.ui.ticket.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
data class BarCode(
    var date: String? = null,
    val term: Int = 0,
    val sta: Int = 0,
    val pmp: String? = null,
    val pan: String? = null,
    val tag: String? = null,
    val ref: String? = null,
    val pdt: Int = 0,
    val qte: String? = null,
    val pu: String? = null,
    val amount: String? =null,
    val flag: Int = 0,
    val sequence: String? =null,
    val article: String? =null
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readInt(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readString()
    )

    companion object CREATOR : Parcelable.Creator<BarCode> {
        override fun createFromParcel(parcel: Parcel): BarCode {
            return BarCode(parcel)
        }

        override fun newArray(size: Int): Array<BarCode?> {
            return arrayOfNulls(size)
        }
    }

    override fun describeContents(): Int {
        return 0
    }

    override fun writeToParcel(dest: Parcel, flags: Int) {
       dest.writeString(date)
       dest.writeInt(term)
       dest.writeInt(sta)
       dest.writeString(pmp)
       dest.writeString(pan)
       dest.writeString(tag)
       dest.writeString(ref)
       dest.writeInt(pdt)
       dest.writeString(qte)
       dest.writeString(pu)
       dest.writeString(amount)
       dest.writeInt(flag)
       dest.writeString(sequence)
       dest.writeString(article)
    }

}
