package app.rht.petrolcard.ui.settings.operations.activity.fpos;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Filterable;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Filter;

import androidx.multidex.MultiDexApplication;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import app.rht.petrolcard.R;
import app.rht.petrolcard.ui.reference.model.FuelPOSModel;
import app.rht.petrolcard.ui.reference.model.ReceiptSetting;
import app.rht.petrolcard.utils.AppPreferencesHelper;
import app.rht.petrolcard.utils.constant.AppConstant;
import app.rht.petrolcard.utils.fuelpos.FuelPosConstantsKt;
import app.rht.petrolcard.utils.fuelpos.models.TransactionData;

public class FuelPosTrxAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> implements Filterable
{
    private static final int TYPE_HEADER = 0;
    private static final int TYPE_ROW = 1;
    private static final int TYPE_ROW_COLORFUL = 2;

    private final List<TransactionData> transactionList;
    private List<TransactionData> filteredClubList;
    private final Context context;

    private final AppPreferencesHelper prefs;
    private int totalDecimal, qtyDecimal,unitDecimal, vatDecimal;
    private final FuelPOSModel fuelpos;
    private ReceiptSetting receiptSetting;
    public FuelPosTrxAdapter(Context context, List<TransactionData> transactionList)
    {
        this.context = context;
        this.transactionList = transactionList;
        this.filteredClubList = transactionList;
        prefs = new AppPreferencesHelper(context.getSharedPreferences(AppConstant.PREF_NAME, MultiDexApplication.MODE_PRIVATE));

        fuelpos = prefs.getFuelPosModel();

        try{
            receiptSetting = prefs.getReferenceModel().getTerminalConfig().getReceiptSetting();
            vatDecimal = receiptSetting.getVatDecimal();
        } catch (NullPointerException e) { vatDecimal = 2; }

        try{ totalDecimal = fuelpos.getTotalAmountDecimal() ;} catch (NullPointerException e){ totalDecimal = 2; }
        try{ qtyDecimal = fuelpos.getTotalAmountDecimal() ;} catch (NullPointerException e){ qtyDecimal = 2; }
        try{ unitDecimal = fuelpos.getTotalAmountDecimal() ;} catch (NullPointerException e){ unitDecimal = 2; }

    }

    @Override
    public int getItemViewType(int position)
    {
        if(position == 0){
            return TYPE_HEADER;
        }
        else {
            if (position % 2 == 0)
            {
                return TYPE_ROW_COLORFUL;
            }
            return TYPE_ROW;
        }


    }

    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(ViewGroup viewGroup, int viewType)
    {
        if(viewType == TYPE_HEADER){
            View view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_fpos_trx_header, viewGroup, false);
            return new FposTrxHeaderViewHolder(view);
        }
        else {
            View view;
            if (viewType == TYPE_ROW)
            {
                view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_fpos_trx, viewGroup, false);
            }
            else {
                view = LayoutInflater.from(viewGroup.getContext()).inflate(R.layout.item_fpos_trx_color,
                        viewGroup, false);
            }
            return new FposTrxViewHolder(view);
        }
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onBindViewHolder(RecyclerView.ViewHolder viewHolder, int position)
    {
        TransactionData item = filteredClubList.get(position);
        if(viewHolder instanceof FposTrxViewHolder)
        {
            FposTrxViewHolder holder = (FposTrxViewHolder) viewHolder;

            String totalAmount = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),totalDecimal);
            String qty = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),qtyDecimal);
            String unitPrice = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),unitDecimal);
            String vat = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),vatDecimal);

            holder.tvId.setText(""+item.getId());
            holder.tvTrxToken.setText(item.getTrxToken());
            holder.tvPump.setText(item.getPump());
            holder.tvSuccessCode.setText(item.getCompletionCode());
            holder.tvCurrency.setText(item.getCurrency());
            holder.tvChecksum.setText(item.getChecksum());
            holder.tvDateTime.setText(item.getDateTime());
            holder.tvProductCode.setText(item.getProductCode());
            holder.tvProductName.setText(item.getProductName());
            holder.tvQty.setText(qty);
            holder.tvUnitPrice.setText(unitPrice);
            holder.tvVatPercent.setText(item.getVatPercentage());
            holder.tvVatAmount.setText(vat);
            holder.tvTotalAmount.setText(totalAmount);
            holder.tvStatus.setText(""+item.getTeleCollectStatus());
        }
        else {
            /*FposTrxHeaderViewHolder holder = (FposTrxHeaderViewHolder) viewHolder;

            String totalAmount = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),totalDecimal);
            String qty = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),qtyDecimal);
            String unitPrice = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),unitDecimal);
            String vat = ""+FuelPosConstantsKt.toDecimalValue(item.getTotalAmount(),vatDecimal);
            holder.tvId.setText(""+item.getId());
            holder.tvTrxToken.setText(item.getTrxToken());
            holder.tvPump.setText(item.getPump());
            holder.tvSuccessCode.setText(item.getCompletionCode());
            holder.tvCurrency.setText(item.getCurrency());
            holder.tvChecksum.setText(item.getChecksum());
            holder.tvDateTime.setText(item.getDateTime());
            holder.tvProductCode.setText(item.getProductCode());
            holder.tvProductName.setText(item.getProductName());
            holder.tvQty.setText(qty);
            holder.tvUnitPrice.setText(unitPrice);
            holder.tvVatPercent.setText(item.getVatPercentage());
            holder.tvVatAmount.setText(vat);
            holder.tvTotalAmount.setText(totalAmount);
            holder.tvStatus.setText(""+item.getTeleCollectStatus());*/
        }
    }

    @Override
    public int getItemCount()
    {
        return filteredClubList.size();
    }

    public class FposTrxViewHolder extends RecyclerView.ViewHolder
    {
        public TextView tvId, tvTrxToken, tvSuccessCode, tvPump, tvCurrency, tvChecksum, tvDateTime, tvProductName, tvProductCode, tvQty, tvUnitPrice, tvVatPercent, tvVatAmount, tvTotalAmount, tvStatus;
        public ImageView imgLogo;

        public FposTrxViewHolder(View view)
        {
            super(view);
            tvId = view.findViewById(R.id.tvId);
            tvTrxToken = view.findViewById(R.id.tvTrxToken);
            tvSuccessCode = view.findViewById(R.id.tvSuccessCode);
            tvPump = view.findViewById(R.id.tvPump);
            tvCurrency = view.findViewById(R.id.tvCurrency);
            tvChecksum = view.findViewById(R.id.tvChecksum);
            tvDateTime = view.findViewById(R.id.tvDateTime);
            tvProductName = view.findViewById(R.id.tvProduct);
            tvProductCode = view.findViewById(R.id.tvProductCode);
            tvQty = view.findViewById(R.id.tvQty);
            tvUnitPrice = view.findViewById(R.id.tvUnitPrice);
            tvVatPercent = view.findViewById(R.id.tvVatPercent);
            tvVatAmount = view.findViewById(R.id.tvVatAmount);
            tvTotalAmount = view.findViewById(R.id.tvTotalAmount);
            tvStatus = view.findViewById(R.id.tvStatus);

        }
    }

    public class FposTrxHeaderViewHolder extends RecyclerView.ViewHolder
    {
        public TextView tvId, tvTrxToken, tvSuccessCode, tvPump, tvCurrency, tvChecksum, tvDateTime, tvProductName, tvProductCode, tvQty, tvUnitPrice, tvVatPercent, tvVatAmount, tvTotalAmount, tvStatus;
        public ImageView imgLogo;

        public FposTrxHeaderViewHolder(View view)
        {
            super(view);
            tvId = view.findViewById(R.id.tvId);
            tvTrxToken = view.findViewById(R.id.tvTrxToken);
            tvSuccessCode = view.findViewById(R.id.tvSuccessCode);
            tvPump = view.findViewById(R.id.tvPump);
            tvCurrency = view.findViewById(R.id.tvCurrency);
            tvChecksum = view.findViewById(R.id.tvChecksum);
            tvDateTime = view.findViewById(R.id.tvDateTime);
            tvProductName = view.findViewById(R.id.tvProduct);
            tvProductCode = view.findViewById(R.id.tvProductCode);
            tvQty = view.findViewById(R.id.tvQty);
            tvUnitPrice = view.findViewById(R.id.tvUnitPrice);
            tvVatPercent = view.findViewById(R.id.tvVatPercent);
            tvVatAmount = view.findViewById(R.id.tvVatAmount);
            tvTotalAmount = view.findViewById(R.id.tvTotalAmount);
            tvStatus = view.findViewById(R.id.tvStatus);

        }
    }

    @Override
    public Filter getFilter()
    {
        return new Filter()
        {
            @Override
            protected FilterResults performFiltering(CharSequence charSequence)
            {
                String charString = charSequence.toString();
                if (charString.isEmpty())
                {
                    filteredClubList = transactionList;
                } else
                {
                    List<TransactionData> filteredList = new ArrayList<>();
                    for (TransactionData item : transactionList)
                    {
                        // name match condition. this might differ depending on your requirement
                        // here we are looking for name
                        if (item.getDateTime().toLowerCase().contains(charString.toLowerCase()) )
                        {
                            filteredList.add(item);
                        }
                    }

                    filteredClubList = filteredList;
                }

                FilterResults filterResults = new FilterResults();
                filterResults.values = filteredClubList;
                return filterResults;
            }

            @Override
            protected void publishResults(CharSequence charSequence, FilterResults filterResults)
            {
                filteredClubList = (ArrayList<TransactionData>) filterResults.values;

                // refresh the list with filtered data
                notifyDataSetChanged();
            }
        };
    }
}