package app.rht.petrolcard.ui.settings.operations.model

import android.view.View
import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.constant.AppConstant
import java.util.*

class DuplicateTransactionModel (
    val transactionModel:TransactionModel?,
    val productModel:ProductModel?
):BaseModel() {
    fun getPaidAmount(): String {
        return if (transactionModel!!.isDiscountTransaction == 1) {
            stringFormatDouble(transactionModel.amount!!.toDouble() - transactionModel.discountAmount!!.toDouble())
        } else {
            stringFormatDouble(transactionModel.amount!!.toDouble())
        }
    }
    fun isCardTransaction():Boolean
    {
        return transactionModel!!.modepay == AppConstant.CARD_VALUE
    }
    fun stringFormatDouble(double: Double):String
    {
        return String.format(Locale.US, "%.2f", double)
    }

    fun showStarIfLoyaltyTrx() : Int {
        return if(transactionModel!=null &&  transactionModel.detailArticle == "loy"){
            View.VISIBLE
        } else
            View.GONE
    }
}