package app.rht.petrolcard.ui.amountselection.activity

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.view.MotionEvent
import android.view.View
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.lifecycleScope
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityEnterAmountBinding
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.iccpayment.activity.DebitCardLimitsActivity
import app.rht.petrolcard.ui.modepay.activity.BankPaymentProgressActivity
import app.rht.petrolcard.ui.modepay.activity.ModePayActivity
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.card.recharge.activity.RechargeModeofPaymentActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.utils.LocaleManager
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.UtilsCardInfo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.CARD_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.VISA_VALUE
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.TaxType
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.fuelpos.decimal
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.helpers.bigDecimalUtils.BigDecimalUtils
import app.rht.petrolcard.utils.tax.TaxUtils
import kotlinx.android.synthetic.main.toolbar.view.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.lang.ref.WeakReference
import java.math.BigDecimal
import java.text.DecimalFormat
import java.util.*

@Suppress("DEPRECATION")
class EnterAmountActivity : BaseActivity<CommonViewModel>(CommonViewModel::class), View.OnTouchListener {

    private lateinit var mBinding: ActivityEnterAmountBinding
    private var TAG = EnterAmountActivity::class.simpleName
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    var transactionLimit="0"
    var rechargeLimit="0"
    var kilometrage=""
    var EnteredText="0"
    var counter = 0
    var tailleKilometrage = 0
    var flagDot = 0
    var minimumRechrageAMount: String? = null
    var minimumTransactionLimit: String? = null
    var lastChar: String? = null
    var isFrench = false
    var referenceModel: ReferenceModel? = null
    private var preferenceModel :PreferenceModel?= null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_enter_amount)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        isFrench = LocaleManager.getLanguage(this) == LocaleManager.LANGUAGE_FRENCH



        lifecycleScope.launch(Dispatchers.IO){
            preferenceModel = prefs.getPreferenceModel()
            getIntentExtras()

            minimumRechrageAMount = "1"
            if(intentExtrasModel!!.minimumRechargeAmount != null && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD)
            {
                minimumRechrageAMount = intentExtrasModel!!.minimumRechargeAmount
            }

            minimumTransactionLimit = "1"

            transactionLimit = preferenceModel!!.transactionLimit!!
            rechargeLimit = preferenceModel!!.rechargeLimit!!
            flagDot = 0
            counter = 0
            tailleKilometrage = 12
            kilometrage = ""
            EnteredText = "0"

            referenceModel = prefs.getReferenceModel()

            launch(Dispatchers.Main){
                setupToolbar()
                touchlistners()
            }
        }

        //MainApp.startIdleScreenTimer(this,60)
    }

    @SuppressLint("ClickableViewAccessibility")
    private fun touchlistners()
    {
        val reference = WeakReference(this).get()!!
        mBinding.text0.setOnTouchListener(reference)
        mBinding.text1.setOnTouchListener(reference)
        mBinding.text2.setOnTouchListener(reference)
        mBinding.text3.setOnTouchListener(reference)
        mBinding.text4.setOnTouchListener(reference)
        mBinding.text5.setOnTouchListener(reference)
        mBinding.text6.setOnTouchListener(reference)
        mBinding.text7.setOnTouchListener(reference)
        mBinding.text8.setOnTouchListener(reference)
        mBinding.text9.setOnTouchListener(reference)
        mBinding.textDot.setOnTouchListener(reference)
        mBinding.textD.setOnTouchListener(reference)
        mBinding.textSubmit.setOnTouchListener(reference)
    }

    private fun setupToolbar() {
        mBinding.toolbarEnterAmount.toolbar.tvTitle.text = getString(R.string.fuel_amount)
        mBinding.toolbarEnterAmount.toolbar.setNavigationOnClickListener {
            mBinding.toolbarEnterAmount.toolbar.isEnabled = false
            if (intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD) {
                showSnakeBarColor(getString(R.string.transaction_cancelled), true)
                Handler(Looper.getMainLooper()).postDelayed({
                    finish()
                }, 2000)
            } else {
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
            }

            mBinding.currencyTxt.text = "0" + " " + prefs.currency
        }
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
         transactionLimit = preferenceModel!!.transactionLimit!!

    }

    @SuppressLint("SetTextI18n")
    override fun onTouch(v: View, event: MotionEvent): Boolean {
        when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_POINTER_DOWN -> {
                when (v.id) {
                    R.id.text_dot ->
                        if (counter < tailleKilometrage - 1 && flagDot == 0) {

                            if (counter != 0) {
                                kilometrage += if(!isFrench) "." else ","
                            } else {
                                kilometrage += if(!isFrench) "0." else "0,"
                                counter++
                            }

                            flagDot = 1
                            mBinding.currencyTxt.text = kilometrage + " " + prefs.currency
                            UtilsCardInfo.beep(mCore, 10)
                        }
                    R.id.text_0 -> if (counter < tailleKilometrage && counter != 0) {
                        kilometrage += "0"
                        setInput()
                    }
                    R.id.text_1 ->
                        if (counter < tailleKilometrage) {
                        kilometrage += "1"
                            setInput()
                    }
                    R.id.text_2 -> if (counter < tailleKilometrage) {
                        kilometrage += "2"
                        setInput()
                    }
                    R.id.text_3 -> if (counter < tailleKilometrage) {
                        kilometrage += "3"
                        setInput()
                    }
                    R.id.text_4 -> if (counter < tailleKilometrage) {
                        kilometrage += "4"
                        setInput()
                    }
                    R.id.text_5 -> if (counter < tailleKilometrage) {
                        kilometrage += "5"
                        setInput()
                    }
                    R.id.text_6 -> if (counter < tailleKilometrage) {
                        kilometrage += "6"
                        setInput()
                    }
                    R.id.text_7 -> if (counter < tailleKilometrage) {
                        kilometrage += "7"
                        setInput()
                    }
                    R.id.text_8 -> if (counter < tailleKilometrage) {
                        kilometrage += "8"
                        setInput()
                    }
                    R.id.text_9 -> if (counter < tailleKilometrage) {
                        kilometrage += "9"
                        setInput()
                    }
                    R.id.text_d -> {
                        kilometrage = ""
                        counter = 0
                        flagDot = 0
                        mBinding.currencyTxt.text = "0" + " " + prefs.currency
                        UtilsCardInfo.beep(mCore, 10)
                    }
                    R.id.text_submit -> {
                        MultiClickPreventer.preventMultiClick(v)
                        UtilsCardInfo.beep(mCore, 10)
                        lastChar = if (counter != 0) {
                            kilometrage.substring(kilometrage.length - 1)
                        } else {
                            "0"
                        }
                        if (lastChar != "." && lastChar !=",") {
                            EnteredText = if (counter == 0) {
                                "0"
                            } else {
                                kilometrage
                            }
                            flagDot = 0

                            if(isFrench) EnteredText =  EnteredText.replace(",",".")

                            val df = DecimalFormat("#.##",localeFormat)
                            val decEntertedText = BigDecimal(EnteredText)
                            val decTransactionLimit = BigDecimal(transactionLimit)
                            val decRechargeLimit = BigDecimal(rechargeLimit)
                            val decMinimumRechargeAMount = BigDecimal(minimumRechrageAMount)
                            transactionLimit = df.format(decTransactionLimit)
                            rechargeLimit = df.format(decRechargeLimit)
                            if (BigDecimalUtils.`is`(decEntertedText).gt(rechargeLimit) && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD) {
                                showSnakeBar(getString(R.string.maximum_recharge_amount) + " " + rechargeLimit)
                            }
                            else if (BigDecimalUtils.`is`(decEntertedText).lt(decMinimumRechargeAMount) && intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD) { // hardCode
                                showSnakeBar(getString(R.string.minimum_recharge_amount) + " " + minimumRechrageAMount )
                            }
                            else if (intentExtrasModel!!.workFlowTransaction != Workflow.SETTINGS_RECHARGE_CARD && BigDecimalUtils.`is`(decEntertedText).gt(decTransactionLimit)) {
                                showSnakeBar(getString(R.string.maximum_transaction_amount) + " " + transactionLimit)
                            }
                            else if (intentExtrasModel!!.workFlowTransaction != Workflow.SETTINGS_RECHARGE_CARD && BigDecimalUtils.`is`(decEntertedText).lt(decMinimumRechargeAMount)) { // hardCode
                                showSnakeBar(getString(R.string.minimum_transaction_amount) + " " + minimumTransactionLimit )
                            } else {
                                if((fuelVat.enabled || shopVat.enabled)  && intentExtrasModel!!.workFlowTransaction != Workflow.SETTINGS_RECHARGE_CARD){
                                    showTaxAmountDialog(decEntertedText.toString())
                                }
                                else {
                                    showConfirmAmountDialog(df, decEntertedText)
                                }

                            }

                        } else {
                            showSnakeBar(getString(R.string.the_amount_wrong))
                        }
                    }
                }
            }
        } //end of onTouch
        return true
    }

    var formattedAmount = ""
    private fun showConfirmAmountDialog(df: DecimalFormat, decKilometrageNumber : BigDecimal) {
        val ctx = WeakReference(this).get()!!
        val builder = AlertDialog.Builder(ctx, R.style.MyStyleDialog)
        formattedAmount = df.format(decKilometrageNumber)

        val amountToDisplay = if(isFrench) formattedAmount.replace(".",",") else formattedAmount

        println(formattedAmount) //prints 2
        builder.setMessage(Html.fromHtml(("<font color='#000000'>" + resources.getString(R.string.validate_amount) + "</font>" + "<br/><br/>"  + "  <strong>   " + amountToDisplay.toString() + " " + prefs.currency + "</strong>" + "<br/>")))
        builder.setCancelable(false)
        builder.setNegativeButton(resources.getString(R.string.no)) { dialog, which ->
            EnteredText = "0"
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
        }
        builder.setPositiveButton(resources.getString(R.string.yes)
        ) { dialog, which ->
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
            intentExtrasModel!!.amount = formattedAmount
            intentExtrasModel!!.preset_amount = "" + formattedAmount
            gotoNextStep()

        }
        val alert = builder.create()
        alert.show()
        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        nbutton.setTextColor(ContextCompat.getColor(ctx, R.color.redLight))
        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        nbutton.textSize = 20f
        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        pbutton.setTextColor(ContextCompat.getColor(ctx, R.color.greenLight))
        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        pbutton.textSize = 20f
    }

    private fun showTaxAmountDialog(amount:String) {
        val ctx = WeakReference(this).get()!!
        val builder = AlertDialog.Builder(ctx, R.style.MyStyleDialog)

        var isInclusive = true
        var type = "Incl."
        val vatDecimal = try { referenceModel!!.terminalConfig!!.receiptSetting!!.vatDecimal!! } catch (e:Exception) { 2 }

        val taxModel = if(fuelVat.enabled) {
            isInclusive = fuelVat.type == 0
            type = if(isInclusive) "Incl." else  "Excl."
            TaxUtils.calculate(amount.toDouble(),fuelVat.percentage!!.toDouble(),isInclusive)
        } else /*if(shopVat.enabled)*/ {
            isInclusive = shopVat.type == 0
            type = if(isInclusive) "Incl." else  "Excl."
            TaxUtils.calculate(amount.toDouble(),shopVat.percentage!!.toDouble(),isInclusive)
        }
        if( fuelVat.type == TaxType.EXCLUSIVE || shopVat.type == TaxType.EXCLUSIVE)
        {
            builder.setMessage(Html.fromHtml(("<font color='#000000'>" + getString(R.string.please_confirm_your_payment) + "</font><br/><br/>" +
                    getString(R.string.net_amount) + " : <strong>" + if(isFrench) taxModel.netAmount.decimal(vatDecimal).replace(".",",") else taxModel.netAmount.decimal(vatDecimal)  + " " + prefs.currency + "</strong>" + "<br/>" +
                    getString(R.string.tax_amount) + " (${taxModel.taxPercentile}% $type)" + " : <strong>" +  if(isFrench) taxModel.taxAmount.decimal(vatDecimal).replace(".",",") else taxModel.taxAmount.decimal(vatDecimal) + " " + prefs.currency + "</strong>" + "<br/>" +
                    getString(R.string.total_amount) + " : <strong>" + if(isFrench) taxModel.totalAmount.decimal(vatDecimal).replace(".",",") else taxModel.totalAmount.decimal(vatDecimal) + " " + prefs.currency + "</strong>"
                    )))
        }
        else
        {
            builder.setMessage(Html.fromHtml(("<font color='#000000'>" + resources.getString(R.string.validate_amount) + "</font>" + "<br/><br/>"  + "  <strong>   " + if(isFrench) amount.replace(".",",") else amount  + " " + prefs.currency + "</strong>" + "<br/>")))
        }

        builder.setCancelable(false)
        builder.setNegativeButton(resources.getString(R.string.no)) { dialog, which ->
            EnteredText = "0"
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
        }
        builder.setPositiveButton(resources.getString(R.string.yes)) { dialog, which ->
            UtilsCardInfo.beep(mCore, 10)
            dialog.dismiss()
            intentExtrasModel!!.amount = taxModel.totalAmount.toString()
            intentExtrasModel!!.preset_amount = "" + taxModel.totalAmount.toString()
            intentExtrasModel!!.taxModel = taxModel
            gotoNextStep()
        }
        val alert = builder.create()
        alert.show()
        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
        nbutton.setTextColor(ContextCompat.getColor(ctx, R.color.redLight))
        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        nbutton.textSize = 20f
        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
        pbutton.setTextColor(ContextCompat.getColor(ctx, R.color.greenLight))
        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
        pbutton.textSize = 20f
    }
    private fun goDirection(): Intent {
        return when {
            intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD -> {
                Intent(this, RechargeModeofPaymentActivity::class.java)
            }
            prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE -> {
                when (intentExtrasModel!!.typePay) {
                    CARD_VALUE-> {
                        Intent(this, CheckCardRestrictionsActivity::class.java)
                    }
                    VISA_VALUE -> {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - go to bank page : Entered Amount: $formattedAmount"))
                        Intent(this, BankPaymentProgressActivity::class.java)
                    }
                    else -> {
                        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - go to pump selection : Entered Amount : $formattedAmount"))
                        Intent(this, PumpSelectionActivity::class.java)
                    }
                }
            }
            else -> {
               if(intentExtrasModel!!.loyaltyTrx)
               {
                   if(BuildConfig.REBATE_DISCOUNT_REQUIRED && referenceModel!!.IMPLEMENT_DISCOUNT!! && intentExtrasModel!!.mTransaction!!.discountType == DISCOUNT_TYPE.REBATE_DISCOUNT && intentExtrasModel!!.mTransaction!!.isDiscountTransaction == 1)
                    {
                        Intent(this, ModePayActivity::class.java)
                    }
                   else {
                       Intent(this, DebitCardLimitsActivity::class.java)
                   }
               }
                    else {
                        Intent(this, ModePayActivity::class.java)
                }
            }
        }
    }
    private fun gotoNextStep() {
        try {
            if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE || intentExtrasModel!!.loyaltyTrx) {
            val mTransactionDAO = TransactionDao()
            mTransactionDAO.open()
            if(intentExtrasModel!!.mTransaction == null)
            {
                intentExtrasModel!!.mTransaction = TransactionModel()
            }
            if(intentExtrasModel!!.loyaltyTrx)
            {
                intentExtrasModel!!.mTransaction!!.modepay = LOYALTY_VALUE
            }
            if(!intentExtrasModel!!.tagsNFC.isNullOrEmpty()){
                intentExtrasModel!!.mTransaction!!.tagNFC = intentExtrasModel!!.tagsNFC
            }
            if(!intentExtrasModel!!.vehicleNumber.isNullOrEmpty()){
                intentExtrasModel!!.mTransaction!!.vehicleNumber = intentExtrasModel!!.vehicleNumber
            }
            if(intentExtrasModel!!.taxModel != null)
            {
                intentExtrasModel!!.mTransaction!!.vatAmount = intentExtrasModel!!.taxModel!!.taxAmount.toString()
                intentExtrasModel!!.mTransaction!!.netAmount = intentExtrasModel!!.taxModel!!.netAmount.toString()
                intentExtrasModel!!.mTransaction!!.amount =  intentExtrasModel!!.taxModel!!.totalAmount
                intentExtrasModel!!.mTransaction!!.vatPercentage =  fuelVat.percentage
                intentExtrasModel!!.mTransaction!!.vatType =  fuelVat.type
            }
            intentExtrasModel!!.mTransaction!!.categoryId =intentExtrasModel!!.categoryId
            intentExtrasModel!!.mTransaction!!.idTerminal = prefs.getReferenceModel()!!.terminal!!.terminalId
            intentExtrasModel!!.mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
            if(intentExtrasModel!!.selectedProduct != null)
            {
                intentExtrasModel!!.mTransaction!!.idProduit = intentExtrasModel!!.selectedProduct!!.productID
                intentExtrasModel!!.mTransaction!!.productName = intentExtrasModel!!.selectedProduct!!.libelle
                intentExtrasModel!!.mTransaction!!.fccProductId = intentExtrasModel!!.selectedProduct!!.fcc_prod_id.toString()

            }
            intentExtrasModel!!.mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
            intentExtrasModel!!.mTransaction!!.idPompiste = prefs.getPompisteId(intentExtrasModel!!.mTransaction!!.codePompiste.toString())
            intentExtrasModel!!.mTransaction!!.dateTransaction = Support.dateToString(Date())
            intentExtrasModel!!.mTransaction!!.amount =    intentExtrasModel!!.amount!!.toDouble()
            if (intentExtrasModel!!.selectedPrice != null && intentExtrasModel!!.selectedPrice!!.unitPrice > 0)
            {
                intentExtrasModel!!.volume = (intentExtrasModel!!.amount!!.toDouble() / intentExtrasModel!!.selectedPrice!!.unitPrice).toString()

            }
            if(intentExtrasModel!!.volume != null)
            {
                intentExtrasModel!!.mTransaction!!.quantite =intentExtrasModel!!.volume!!.toDouble()
            }
            if(intentExtrasModel!!.selectedPrice != null)
            {
                intentExtrasModel!!.mTransaction!!.unitPrice =intentExtrasModel!!.selectedPrice!!.unitPrice
            }
            intentExtrasModel!!.mTransaction!!.flagController = 1
            intentExtrasModel!!.mTransaction!!.kilometrage = ""
            intentExtrasModel!!.mTransaction!!.pan = intentExtrasModel!!.panNumber
            intentExtrasModel!!.mTransaction!!.flagTelecollecte = 0
            intentExtrasModel!!.mTransaction!!.pumpId = intentExtrasModel!!.mPumpNumber
            intentExtrasModel!!.mTransaction!!.transactionStatus = 0
            intentExtrasModel!!.mTransaction!!.sequenceController = ""
                if(intentExtrasModel!!.mTransaction!!.reference.isNullOrEmpty())
                {
                    intentExtrasModel!!.mTransaction!!.reference = "TRX" + Support.generateReference(this)
                }
                intentExtrasModel!!.refrenceID = intentExtrasModel!!.mTransaction!!.reference
            intentExtrasModel!!.mTransaction!!.timsSignDetails!!.fuelQtyUnit = prefs.getReferenceModel()!!.FUEL_QTY_UNIT?: "L"
            insertTransactionData(intentExtrasModel!!.mTransaction!!)
            log(TAG, "intentExtrasModel!!.mTransaction!!.!!! --> " + gson.toJson(intentExtrasModel!!.mTransaction!!))
             prefs.isTransactionCreated = true
            log(TAG, "isTransactionCreated :: true")
            mTransactionDAO.close()
                gotoNextScreen()
            }
            else
            {
                gotoNextScreen()
            }
        } catch (ex: SQLiteException) {
            ex.printStackTrace()
            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
        }
    }
    fun gotoNextScreen()
    {
        val intent = goDirection()
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        startActivity(intent)
        finish()
    }
    override fun setObserver() {

    }

    private fun setInput()
    {
        counter++
        mBinding.currencyTxt.text = kilometrage + " " + prefs.currency
        UtilsCardInfo.beep(mCore, 10)
    }

    override fun onBackPressed() {

    }

    override fun onResume() {
        super.onResume()

    }
}
