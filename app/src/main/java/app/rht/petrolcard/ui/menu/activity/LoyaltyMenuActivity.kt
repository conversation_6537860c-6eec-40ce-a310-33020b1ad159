package app.rht.petrolcard.ui.menu.activity


import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT.FUEL_CATEGORY_ID
import app.rht.petrolcard.utils.constant.PRODUCT.SERVICE_CATEGORY_ID
import app.rht.petrolcard.utils.constant.PRODUCT.SHOP_CATEGORY_ID
import java.util.*
import kotlin.collections.ArrayList
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import app.rht.petrolcard.databinding.ActivityLoyaltyMenuBinding
import app.rht.petrolcard.ui.attendantcode.activity.AttendantCodeActivity
import app.rht.petrolcard.ui.attendantcode.activity.AttendantTagActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.TransactionStepLog
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import kotlinx.android.synthetic.main.toolbar.view.*
import java.io.File


@Suppress("DEPRECATION")
class LoyaltyMenuActivity :   BaseActivity<MenuViewModel>(MenuViewModel::class), RecyclerViewArrayAdapter.OnItemClickListener<CategoryListModel> {
    private lateinit var mBinding: ActivityLoyaltyMenuBinding
    private var TAG = LoyaltyMenuActivity::class.simpleName
    private var categoriesList : ArrayList<CategoryListModel> = ArrayList()
    private lateinit var adapter : RecyclerViewArrayAdapter<CategoryListModel>
    private var stationMode: Int = 0
    private var intentExtrasModel: IntentExtrasModel? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_loyalty_menu)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        setUpRecyclerView()
        setupViews()
        intentExtrasModel = IntentExtrasModel()
        stationMode = AppConstant.OFFLINE_TRX_MODE
        intentExtrasModel!!.stationMode = stationMode
        prefs.logReferenceNo  = ""
      //  prefs.logReferenceNo  = Support.generateReference(this)
        val imageURL =  prefs.logoPath //File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).toString() + File.separator + AppConstant.LOGO_NAME)
        if(imageURL!=null){
            Glide.with(this)
                .load(Uri.fromFile(File(imageURL)))
                .apply(RequestOptions().override(512, 512))
                .into(mBinding.logo)
        } else {
            Glide.with(this)
                .load(R.drawable.logo)
                .apply(RequestOptions().override(512, 512))
                .into(mBinding.logo)
        }
    }
    private fun setupToolbar()
    {
        mBinding.toolbarLoyalty.toolbar.tvTitle.text = getString(R.string.loyalty_label)
        mBinding.toolbarLoyalty.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLoyalty.toolbar.isEnabled = false
            finish()
        }
    }
    private fun setupViews() {
        val station = prefs.getStationModel()
        mBinding.tvStationName.text = station!!.name
    }

    override fun setObserver() {

    }

    override fun onItemClick(view: View, model: CategoryListModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        val serverDate: String = "2021-08-29 13:53:07"
        val isDateCorrect = UtilsDate.isSameMonth(Date(), serverDate)
//        if (isDateCorrect)
//        {
        prefs.logReferenceNo  = Support.generateReference(this)
        intentExtrasModel!!.categoryId = model.category_id
        val trxLog = TransactionStepLog()
        trxLog.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Category - "+ model.category_id))
        intentExtrasModel!!.transactionStepLog = trxLog
        when(model.category_id){
            FUEL_CATEGORY_ID -> {
                val intent =  goDirection()
                intentExtrasModel!!.workFlowTransaction = Workflow.TAXI_FUEL
               intentExtrasModel!!.loyaltyTrx =true
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)

            }
            SERVICE_CATEGORY_ID -> {
                val intent = Intent(this, ServiceMenuActivity::class.java)
                intentExtrasModel!!.workFlowTransaction = Workflow.OTHER_PRODUCTS
               intentExtrasModel!!.loyaltyTrx =true
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)
            }
            SHOP_CATEGORY_ID -> {
                val intent =  goDirection()
                intentExtrasModel!!.workFlowTransaction = Workflow.SHOP_PRODUCTS
               intentExtrasModel!!.loyaltyTrx =true
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)
            }
        }
    }

    private fun setUpRecyclerView() {
        categoriesList.clear()
        if(!prefs.getReferenceModel()!!.category_list.isNullOrEmpty())
        {
            for(list in prefs.getReferenceModel()!!.category_list!!)
            {
                if(list.category_id == PRODUCT.FUEL_CATEGORY_ID || list.category_id == PRODUCT.SERVICE_CATEGORY_ID || list.category_id == PRODUCT.SHOP_CATEGORY_ID)
                {
                    categoriesList.add(list)
                }
            }
        }
        if(categoriesList.isEmpty())
        {
            mBinding.noCategories.visibility =View.VISIBLE
            mBinding.rvCategories.visibility =View.GONE
        }
        else
        {
            mBinding.noCategories.visibility =View.GONE
            mBinding.rvCategories.visibility =View.VISIBLE
        }
        adapter = RecyclerViewArrayAdapter(categoriesList, this)
        mBinding.rvCategories.adapter = adapter
    }

    fun goDirection(): Intent {
        val i: Intent
        when (stationMode) {
            1 -> {
                i = Intent(this, CheckCardRestrictionsActivity::class.java)
                intentExtrasModel!!.typePay=AppConstant.CARD_VALUE
            }
            else -> i =
                if (prefs.getStationModel()!!.mode_pompiste == "CODE") {
                    Intent(this, AttendantCodeActivity::class.java)
                } else {
                    Intent(this, AttendantTagActivity::class.java)
                }
        }
        return i
    }

    override fun onBackPressed() {

    }
}
