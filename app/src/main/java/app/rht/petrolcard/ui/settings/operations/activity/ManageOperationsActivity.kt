package app.rht.petrolcard.ui.settings.operations.activity

import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.card.common.model.CardItemModel
import app.rht.petrolcard.utils.constant.AppConstant
import kotlinx.android.synthetic.main.toolbar.view.*

import androidx.appcompat.app.AlertDialog
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityManageOperationsBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import java.lang.Exception
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.settings.operations.activity.fpos.PendingFuelPosTrxActivity
import app.rht.petrolcard.ui.settings.operations.model.TotalProductCount
import app.rht.petrolcard.ui.settings.operations.model.TotalTransactionCountModel
import app.rht.petrolcard.utils.LocaleManager
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import java.io.FileInputStream
import java.util.*


class ManageOperationsActivity: BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<CardItemModel> {

    private lateinit var mBinding: ActivityManageOperationsBinding

    private val TAG = SettingsActivity::class.simpleName

    private val DUPLICATE = "DUPLICATE"
    private val TRANSACTION_CANCELLATION = "TRANSACTION_CANCELLATION"
    private val JOURNAL = "JOURNAL"
    private val TOTAL =  "TOTAL"
    private val PENDING_TRANSACTION =  "PENDING_TRANSACTION"
    private val BANK_TRANSACTION =  "BANK_TRANSACTION"
    private val FPOS_PENDING_TRANSACTION =  "FPOS_PENDING_TRANSACTION"
    private val DISPUTED_TRANSACTION =  "DISPUTED_TRANSACTION"
    private var intentExtrasModel: IntentExtrasModel? = null

    private var referenceModel: ReferenceModel? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_manage_operations)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        intentExtrasModel = IntentExtrasModel()
        setupItemsInList()
        referenceModel = prefs.getReferenceModel()
    }

    private fun setupToolbar() {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.operations)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish() }
    }

    override fun setObserver() {

    }

    private fun gotoDuplicateActivity() {
        val i = Intent(this, DuplicateTransactionActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }
    private fun gotoPendingTransactionActivity() {
        val i = Intent(this, PendingTransactionsActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }
    private fun gotoDisputedTransactionActivity() {
        val i = Intent(this, DisputedTransactionActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }
    private fun gotoFposPendingTransactionActivity() {
        val i = Intent(this, PendingFuelPosTrxActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }
    private fun gotoBankTransactionHistory() {
        val i = Intent(this, BankTrxHistoryActivity::class.java)
        intentExtrasModel!!.stationMode = prefs.getStationModel()!!.mode
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
    }

    override fun onItemClick(view: View, item: CardItemModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        when(item.id){
            DUPLICATE -> {
                gotoDuplicateActivity()
            }
            TRANSACTION_CANCELLATION -> {

            }
            JOURNAL -> {
                getJournalTransactions()
            }
            TOTAL -> {
                getTotalTransactions()
            }
            PENDING_TRANSACTION -> {
                gotoPendingTransactionActivity()
            }
            FPOS_PENDING_TRANSACTION -> {
                gotoFposPendingTransactionActivity()
            }
            DISPUTED_TRANSACTION -> {
                gotoDisputedTransactionActivity()
            }
            BANK_TRANSACTION -> {
                gotoBankTransactionHistory()
            }

        }
    }

    private var settingsItems : ArrayList<CardItemModel> = ArrayList()
    private fun setupItemsInList(){
        settingsItems.add(CardItemModel(R.drawable.ic_recharge,getString(R.string.duplicate_transactions),DUPLICATE,"#F97F51"))
//        settingsItems.add(CardItemModel(R.drawable.ic_transaction_history,"Transaction Cancellation",TRANSACTION_CANCELLATION,"#1B9CFC"))
        settingsItems.add(CardItemModel(R.drawable.ic_update_card, getString(R.string.journal_label),JOURNAL,"#3B3B98"))
        settingsItems.add(CardItemModel(R.drawable.ic_unblock_card,getString(R.string.total_transactions),TOTAL,"#FC427B"))
        mBinding.rvSettings.removeAllViews()
        val mSettingsAdapter = RecyclerViewArrayAdapter(settingsItems,this)
        settingsItems.add(CardItemModel(R.drawable.ic_transaction_history,getString(R.string.pending_transactions),PENDING_TRANSACTION,"#1B9CFC"))

        if(BuildConfig.DEBUG){
            settingsItems.add(CardItemModel(R.drawable.ic_transaction_history,getString(R.string.fuelpos_pending_transaction),FPOS_PENDING_TRANSACTION,"#00b894"))
        }

        settingsItems.add(CardItemModel(R.drawable.ic_bank,getString(R.string.bank_trx_history),BANK_TRANSACTION,"#1B9CFC"))
        settingsItems.add(CardItemModel(R.drawable.ic_transaction_history,getString(R.string.disputed_transactions),DISPUTED_TRANSACTION,"#F97F51"))
        mBinding.rvSettings.adapter = mSettingsAdapter
        mSettingsAdapter.notifyDataSetChanged()
    }

    var productList : ArrayList<ProductModel> = ArrayList()
    private fun getJournalTransactions(){

        val transactionDao = TransactionDao()
        transactionDao.open()
        val transactions = transactionDao.getJournalOperation()
        transactionDao.close()

        if(printDialog==null)
            printDialog = getMyPrintDialog()

        if(transactions.isEmpty()){
            showToast(getString(R.string.no_transactions_found))
            try { printDialog!!.dismiss()
            } catch (e:Exception){
                e.printStackTrace()
            }
        }
        else {
            //printJournalReceipt(transactions)
            PrintJournalTicketTask(transactions).execute()
        }
    }


    private fun getTotalTransactions(){
        val transactionDao = TransactionDao()
        transactionDao.open()
        //val transactions = transactionDao.getTotalTransactions()
        val productsDAO = ProductsDao()
        productsDAO.open()
        val totalTrxObj = transactionDao.getTotalFuelTransaction()
       val productCount = transactionDao.getTotalProductTransactions()
        transactionDao.close()
        for(prod in productCount)
        {
            val productId = if(prod.productID == null) 0 else prod.productID!!.toInt()

            val product = productsDAO.getProductById(productId)
            if(product != null)
            {
                prod.productName =product.libelle!!
            }
            else
            {
                prod.productName = ""
            }

        }
        productsDAO.close()
        PrintTotalTicketTask(totalTrxObj,productCount).execute()
    }

    var printDialog : AlertDialog? = null
    inner class PrintJournalTicketTask(val transactions: List<TransactionModel>) : CoroutineAsyncTask<String, String, Void?>() {

        private fun commonLayout(): Bitmap? {
            var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap,400,400)

            receipt = ReceiptBuilder(1200)
            receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK).addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)
            receipt.setTextSize(65f).setTypeface(this@ManageOperationsActivity, "fonts/Roboto-Bold.ttf").setAlign(Paint.Align.CENTER).addText("").
            addText(prefs.getReferenceModel()!!.terminal!!.stationName).
            addText(prefs.getReferenceModel()!!.terminal!!.address+", "+prefs.getReferenceModel()!!.terminal!!.city)
            receipt.setTextSize(75f).setAlign(Paint.Align.CENTER).addText(getString(R.string.journal_receipt)).addText("")
            receipt.setAlign(Paint.Align.CENTER).setTypeface(this@ManageOperationsActivity, "fonts/Roboto-Regular.ttf").setTextSize(65f).setAlign(Paint.Align.CENTER).addText(Support.getDateTicket(Date())).addText("")
            setAlignment(Paint.Align.LEFT,65f)
            receipt.addText("${getString(R.string.fiscal_label)} : ${prefs.getReferenceModel()!!.terminal!!.fiscalId}")
            for(transaction in transactions){
                val product =  getProduct(transaction)

                if(transaction.modepay != AppConstant.REFUND_VALUE && transaction.amount!=null && transaction.amount!!>0 && product!=null) { //not adding refund trx in journal print receipt
                    receipt.addText("___________________________________")
                    setAlignment(Paint.Align.LEFT,65f).addText("")
                    receipt.addText("${getString(R.string.product_p)} : ${if (product != null) product.libelle!!
                    else Support.getDesignationProductFromId(transaction.idProduit!!)
                        .uppercase(Locale.ROOT)
                    }",true)
                    //log(TAG,"PRODUCT NAME::: ${product!!.libelle}")
                    receipt.addText("${getString(R.string.reference_label)} : ${transaction.reference}")
                    receipt.addText("${getString(R.string.date_label)} : ${transaction.dateTransaction}")
                    receipt.addText("${getString(R.string.amount_pay)} : ${transaction.amount} ${prefs.currency}",true)
                }
            }
            receipt.addText("")
            receipt.setAlign(Paint.Align.CENTER)
            receipt.addText("\n****${getString(R.string.journal_receipt)}****").setAlign(Paint.Align.CENTER)
            val reciept = receipt.build()
            bitmap.recycle()
            return reciept
        }

        private fun dw14Layout(): ArrayList<PrintCmd> {
            var dateFormatted = Support.dateToString(Date())
            if (dateFormatted == null || !dateFormatted.contains(" ")) {
                dateFormatted = Support.dateToStringH(Date())
            }
            val dateArray = dateFormatted!!.split(" ").toTypedArray()
            val date = dateArray[0]
            val time = dateArray[1]

            val printCommands = ArrayList<PrintCmd>()

            var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap,200,200)
            printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
            printCommands.add(PrintCmd(referenceModel!!.COMPANY.name,AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(referenceModel!!.terminal!!.stationName!!,AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(referenceModel!!.terminal!!.address+", "+ referenceModel!!.terminal!!.city,AlignmentType.CENTER,true))
            printCommands.add(PrintCmd("$date $time",AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(getString(R.string.journal_receipt),AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(getString(R.string.fiscal_label)+":[L]${prefs.getReferenceModel()!!.terminal!!.fiscalId}",AlignmentType.LEFT,true))
            for(transaction in transactions){
                val product =  getProduct(transaction)

                if(transaction.modepay != AppConstant.REFUND_VALUE && transaction.amount!=null && transaction.amount!!>0 && product!=null) { //not adding refund trx in journal print receipt
                    printCommands.add(PrintCmd("-----------------------",AlignmentType.CENTER,true))
                    printCommands.add(PrintCmd("",AlignmentType.CENTER,true))
                    printCommands.add(PrintCmd("${getString(R.string.product_p)} :[L]${if (product != null) product.libelle!!
                    else Support.getDesignationProductFromId(transaction.idProduit!!).uppercase(Locale.ROOT)}",AlignmentType.LEFT,true))
                    printCommands.add(PrintCmd("${getString(R.string.reference_label)} : ${transaction.reference}",AlignmentType.LEFT,true))
                    printCommands.add(PrintCmd("${getString(R.string.date_label)} : ${transaction.dateTransaction}",AlignmentType.LEFT,true))
                    printCommands.add(PrintCmd("${getString(R.string.amount_pay)} : ${transaction.amount} ${prefs.currency}",AlignmentType.LEFT,true))
                }
            }
            printCommands.add(PrintCmd("",AlignmentType.LEFT,true))
            printCommands.add(PrintCmd("**** ${getString(R.string.journal_receipt)} ****",AlignmentType.CENTER,true))

            return printCommands
        }

        override fun onPreExecute() {
            super.onPreExecute()

            val productsDAO = ProductsDao()
            productsDAO.open()
            productList = productsDAO.getAllProducts()
            productsDAO.close()

        }

        override fun doInBackground(vararg params: String): Void? {
            try{

                var printerType = referenceModel!!.PRINTER_TYPE
                if(BuildConfig.DEBUG){
                    printerType = AppConstant.DW14_PRINTER
                }

                if(printerType == AppConstant.DW14_PRINTER){
                    val commands = dw14Layout()
                    TicketPrinter(this@ManageOperationsActivity).printTicket(commands)
                } else {
                    val receiptBitmap = commonLayout()
                    if(receiptBitmap!=null) {
                        TicketPrinter(this@ManageOperationsActivity).printReceipt(receiptBitmap)
                        receiptBitmap.recycle()
                    }
                }


            } catch (e: Exception){
                e.printStackTrace()
            }
            return null
        }

        override fun onPostExecute(result: Void?) {
            printDialog!!.dismiss()

        }

        private fun getProduct(transaction:TransactionModel) : ProductModel?{
            var product : ProductModel? = null
            for(item in productList){
                if(item.productID == transaction.idProduit){
                    product = item
                    break
                }
            }
            return product
        }
    }

    inner class PrintTotalTicketTask(private val totalTrxObj: TotalTransactionCountModel,
                                     private val productCounts:ArrayList<TotalProductCount>) : CoroutineAsyncTask<String, String, Void?>() {
        private fun commonLayout():Bitmap? {
            var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap,400,400)
            receipt = ReceiptBuilder(1200)
            receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK)
                .addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)
            receipt.setTextSize(65f).setTypeface(this@ManageOperationsActivity, "fonts/Roboto-Bold.ttf").setAlign(Paint.Align.CENTER).addText("").
            addText(prefs.getReferenceModel()!!.terminal!!.stationName).
            addText(prefs.getReferenceModel()!!.terminal!!.address+", "+prefs.getReferenceModel()!!.terminal!!.city)
            receipt.setTextSize(75f).setAlign(Paint.Align.CENTER).addText(getString(R.string.total_transactions)).addText("")
            receipt.setAlign(Paint.Align.CENTER).setTypeface(this@ManageOperationsActivity, "fonts/Roboto-Regular.ttf").setTextSize(65f).setAlign(Paint.Align.CENTER).addText(Support.getDateTicket(Date())).addText("")
            setAlignment(Paint.Align.LEFT,65f)
            receipt.addText("${getString(R.string.total_fuel_transaction)} : ${totalTrxObj.fuel_transaction_count}")
            // receipt.addText(getString(R.string.non_fuel_transactions)+" : ${totalTrxObj.non_fuel_transaction_count}")
            // receipt.addText(getString(R.string.service_transactions)+" : ${totalTrxObj.service_transaction_count}")
            // receipt.addText(getString(R.string.shop_transactions)+" : ${totalTrxObj.shop_transaction_count}")
            for(product in productCounts)
            {
                if(!product.productName.isNullOrEmpty())
                {
                    if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this@ManageOperationsActivity))
                    {
                        setAlignment(Paint.Align.LEFT,65f).addText( "${product.productCount} : ${product.productName} TRXs")
                    }
                    else
                    {
                        setAlignment(Paint.Align.LEFT,65f).addText( "${product.productName} TRXs : ${product.productCount}")
                    }
                }
            }
            //  receipt.addText(getString(R.string.recharge_transactions)+" : ${totalTrxObj.recharge_transaction_count}")
            receipt.addText(getString(R.string.total_number_trx)+" : ${totalTrxObj.total_transaction_count}")
            //   receipt.addText(getString(R.string.total_recharge_amount)+" : ${totalTrxObj.total_recharge_amount} ${prefs.currency}")
            receipt.addText(getString(R.string.total_trx_amount)+" : ${totalTrxObj.total_transaction_amount} ${prefs.currency}")
            val reciept = receipt.build()
            return reciept
        }
        private fun dw14Layout(): ArrayList<PrintCmd> {
            var dateFormatted = Support.dateToString(Date())
            if (dateFormatted == null || !dateFormatted.contains(" ")) {
                dateFormatted = Support.dateToStringH(Date())
            }
            val dateArray = dateFormatted!!.split(" ").toTypedArray()
            val date = dateArray[0]
            val time = dateArray[1]

            val printCommands = ArrayList<PrintCmd>()

            var bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap,200,200)
            printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
            printCommands.add(PrintCmd(referenceModel!!.COMPANY.name,AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(referenceModel!!.terminal!!.stationName!!,AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(referenceModel!!.terminal!!.address+", "+ referenceModel!!.terminal!!.city,AlignmentType.CENTER,true))
            printCommands.add(PrintCmd("$date $time",AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(getString(R.string.total_transactions),AlignmentType.CENTER,true))
            printCommands.add(PrintCmd(getString(R.string.fiscal_label)+":[L]${prefs.getReferenceModel()!!.terminal!!.fiscalId}",AlignmentType.LEFT,true))
            printCommands.add(PrintCmd("${getString(R.string.total_fuel_transaction)} :[L]${totalTrxObj.fuel_transaction_count}",AlignmentType.LEFT,true))
            // receipt.addText(getString(R.string.non_fuel_transactions)+" : ${totalTrxObj.non_fuel_transaction_count}")
            // receipt.addText(getString(R.string.service_transactions)+" : ${totalTrxObj.service_transaction_count}")
            // receipt.addText(getString(R.string.shop_transactions)+" : ${totalTrxObj.shop_transaction_count}")
            for(product in productCounts)
            {
                if(!product.productName.isNullOrEmpty())
                {
                    if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this@ManageOperationsActivity))
                    {
                        printCommands.add(PrintCmd("${product.productCount}[R]:${product.productName} TRXs",AlignmentType.RIGHT,true))
                    }
                    else
                    {
                        printCommands.add(PrintCmd("${product.productName} TRXs :[L]${product.productCount}",AlignmentType.LEFT,true))
                    }
                }
            }
            printCommands.add(PrintCmd(getString(R.string.total_number_trx)+" :[L]${totalTrxObj.total_transaction_count}",AlignmentType.LEFT,true))
            printCommands.add(PrintCmd(getString(R.string.total_trx_amount)+" :[L]${totalTrxObj.total_transaction_amount} ${prefs.currency}",AlignmentType.LEFT,true))

            printCommands.add(PrintCmd("",AlignmentType.LEFT,true))
            printCommands.add(PrintCmd("**** ${getString(R.string.total_transactions)} ****",AlignmentType.CENTER,true))

            return printCommands
        }
        override fun onPreExecute() {
            super.onPreExecute()
            if(printDialog==null)
                printDialog = getMyPrintDialog()
        }

        override fun doInBackground(vararg params: String): Void? {
            try
            {
                var printerType = referenceModel!!.PRINTER_TYPE
                if(BuildConfig.DEBUG){
                    printerType = AppConstant.DW14_PRINTER
                }

                if(printerType == AppConstant.DW14_PRINTER){
                    val commands = dw14Layout()
                    TicketPrinter(this@ManageOperationsActivity).printTicket(commands)
                } else {
                    val receiptBitmap = commonLayout()
                    if(receiptBitmap!=null) {
                        TicketPrinter(this@ManageOperationsActivity).printReceipt(receiptBitmap)
                        receiptBitmap.recycle()
                    }
                }
            } catch (e: Exception){
                e.printStackTrace()
            }
            return null
        }
        override fun onPostExecute(result: Void?) {
            printDialog!!.dismiss()

        }
    }


}