package app.rht.petrolcard.ui.reference.viewmodel

import android.util.Log
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketRequest
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketResponse
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.service.model.ResponseIPContentu
import app.rht.petrolcard.ui.iccpayment.model.StoreCardDataModel
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.settings.appupdate.model.UpdateResponse
import app.rht.petrolcard.ui.updatecard.model.CardDetailsResponse
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.LogWriter
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.FUEL_SERVICE_LOG_NAME
import com.wwdablu.soumya.wzip.WZip
import com.wwdablu.soumya.wzip.WZipCallback
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.*
import java.util.concurrent.TimeUnit


open class CommonViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper,
) : BaseViewModel() {

    var appUpdateObserver = MutableLiveData<BaseResponse<UpdateResponse>>()
    var referenceDataObserver = MutableLiveData<BaseResponse<ReferenceModel>>()
    var cardDetailsObserver = MutableLiveData<BaseResponse<CardDetailsResponse>>()
    var storeCardObserver = MutableLiveData<BaseResponse<String>>()
    var updateCardNotificationObserver = MutableLiveData<BaseResponse<String>>()
    var updateCardRechargeObserver = MutableLiveData<BaseResponse<String>>()
    var updateCreditStatusObserver = MutableLiveData<BaseResponse<String>>()
    var transactionObserver = MutableLiveData<BaseResponse<String>>()
    var photoUploadObserver = MutableLiveData<BaseResponse<String>>()
    var masterFusionIpObserver = MutableLiveData<BaseResponse<ResponseIPContentu>>()
    var getDiscountDetailsObserver = MutableLiveData<DiscountBaseResponse>()
    var logFolderPath = ""
    var tempLogFolderPath = ""
    var injectkeyObserver = MutableLiveData<InjectKeyResponseModel?>()

    fun getAllReferenceData(language:String) {
        requestData(mNetworkService.getAllReferenceData(
            preferencesHelper.baseUrl+NetworkRequestEndPoints.GET_ALL_REFERENCE_DATA,
            MainApp.sn!!,
            preferencesHelper.getPreferenceModel()!!.blockListVersionNo!!.toString(),
            preferencesHelper.getPreferenceModel()!!.greyListVersionNo!!.toString(),
            language.lowercase()
        ),
            {
                referenceDataObserver.postValue(it)
            },priority = ApiService.PRIORITY_HIGH)
    }
    fun getMasterFusionIP() {
        requestData(mNetworkService.getMasterFusionIp(preferencesHelper.baseUrl+NetworkRequestEndPoints.GET_RFID_MASTER_IP,MainApp.sn!!),
            {
                masterFusionIpObserver.postValue(it)
            })
    }
    fun getCardDetails(pan:String,status:String) {
        requestData(mNetworkService.updateCard(preferencesHelper.baseUrl+NetworkRequestEndPoints.UPDATE_CARD,
            MainApp.sn!!,
            pan,
            status),
            {
                cardDetailsObserver.postValue(it)
            })
    }
//    fun storeCardData(storeCardModel: StoreCardDataModel) {
//        requestData(mNetworkService.storeCardData(preferencesHelper.baseUrl+NetworkRequestEndPoints.STORE_CARD_DATA,
//            storeCardModel),
//            {
//                storeCardObserver.postValue(it)
//            })
//    }
    fun updateCardNotification(pan:String,dt:String) {
        requestData(mNetworkService.updateCardNotification(preferencesHelper.baseUrl+NetworkRequestEndPoints.UPDATE_CARD_NOTIFICATION,
            MainApp.sn,pan,dt),
            {
                updateCardNotificationObserver.postValue(it)
            })
    }
    fun updateMassRechargeValidate(pan:String,id:Int,checksum:String) {
        requestData(mNetworkService.rechargeMassValidate(preferencesHelper.baseUrl+NetworkRequestEndPoints.RECHARGE_MASS_VALIDATE,MainApp.sn,pan,id,checksum),
            {
                updateCardRechargeObserver.postValue(it)
            })
    }
    fun updateCreditStatus(pan:String,id:Int,checksum:String,status: Int) {
        requestData(mNetworkService.updateCreditStatus(preferencesHelper.baseUrl+NetworkRequestEndPoints.UPDATE_CREDIT_STATUS,MainApp.sn,pan,id,checksum,status),
            {
                updateCreditStatusObserver.postValue(it)
            })
    }
    fun sendTransactionOnline(transactions:SendTransactionModel) {
        requestData(mNetworkService.sendTransactionOnline(preferencesHelper.baseUrl+NetworkRequestEndPoints.SEND_TRANSACTION_ONLINE,transactions),
            {
                transactionObserver.postValue(it)
            })
    }


    var tcpServiceRestartResult = MutableLiveData<String>()
//    fun restartFusionTcpService(url:String) {
//        requestData(mNetworkService.restartFusionTcpService(url),
//            {
//                tcpServiceRestartResult.postValue(it)
//            })
//    }
//    fun offlinePhotoUpload(file: File, key: String, name: String) {
//        requestData(mNetworkService.offlinePhotoUpload(preferencesHelper.baseUrl+NetworkRequestEndPoints.OFFLINEUPLOAD, file = createFormData(file, "file", "*/*"),sn = createFormData(MainApp.sn!!),hashkey = createFormData(key),name = createFormData(name)),
//            {
//                photoUploadObserver.postValue(it)
//            })
//    }
    var uploadFileResponse = MutableLiveData<BaseResponse<String>>()
    fun generateLogs(message:String,isErrorLog:Int,isDeleteLog:Boolean?=false) {
        if(preferencesHelper.isSendLogs == 1) {
            preferencesHelper.logCount = preferencesHelper.logCount + 1
            val timestamp = TimeUnit.SECONDS.toSeconds(System.currentTimeMillis()) / 1000
            Log.i("TAG", "Log Count : " + preferencesHelper.logCount)
            val logfiles = LogWriter.getAllLogs()

            //val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path

            // create copy of log files in temp folder

            tempLogFolderPath = LogWriter.getTempLogDir()
            logFolderPath = LogWriter.getLogDir()

            for(srcFile in logfiles) {
                val dst = File(tempLogFolderPath+"/${srcFile.name}")
                val `in`: InputStream = FileInputStream(srcFile)
                `in`.use { input ->
                    val out: OutputStream = FileOutputStream(dst)
                    out.use { output ->
                        // Transfer bytes from in to out
                        val buf = ByteArray(1024)
                        var len: Int
                        while (input.read(buf).also { len = it } > 0) {
                            output.write(buf, 0, len)
                        }
                    }
                }
            }
            Log.e("generateLogs","Log files copied to temp dir")


            val logs = LogWriter.getAllLogsFromTemp()

            if (logs.size > 0) {
                val wZip = WZip()
                val fileName = "Log_${MainApp.sn}_$timestamp.zip"
                wZip.zip(logs, File(tempLogFolderPath + File.separator + fileName), "logFileZipper",
                    object : WZipCallback {
                        override fun onStarted(identifier: String?) {}
                        override fun onZipCompleted(zipFile: File?, identifier: String?) {
                            Log.i("ViewModel", "FILE CREATED: " + zipFile!!.absolutePath)
                            Log.i("ViewModel", "logFileName $fileName")
                            val key = Support.generateMD5("abcde${MainApp.sn}fghij")

                            if (zipFile.exists()) {
                                val mainApp = MainApp.appContext as MainApp
                                mainApp.setFuelServiceName(FUEL_SERVICE_LOG_NAME + "_" + preferencesHelper.logCount)
                                sendLogFiles(file = zipFile, key = key!!, message = message, isErrorLog =  isErrorLog,isDeleteLog)
                            }
                        }
                        override fun onUnzipCompleted(identifier: String?) {}
                        override fun onError(throwable: Throwable?, identifier: String?) {}
                    }
                )
            }
        }
        else{
            try {
                LogWriter.deleteTempLogDir()
                LogWriter.deleteLogDir()
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }

        }
    }
    var retrySendLog = 0
    fun sendLogFiles(file: File,key: String,message: String, isErrorLog: Int,isDeleteLog:Boolean?=false) {
        mNetworkService.sendLogFiles(
            url=preferencesHelper.baseUrl+ NetworkRequestEndPoints.SENDLOGFILES,
            file = createFormData(file, "file1", "*/*") ,
            sn = createFormData(MainApp.sn!!),
            hashkey = createFormData(key), nbr = createFormData("1"),
            message = createFormData(message), is_error_log = createFormData(isErrorLog.toString()), connection = "close").subscribeOn(rx.schedulers.Schedulers.io())
            .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
            .subscribe({
                uploadFileResponse.postValue(it)
                LogWriter.deleteLogDir()
                LogWriter.deleteTempLogDir()
            }, {
                try {
                    if(isDeleteLog!! || retrySendLog >= 5)
                    {
                        LogWriter.deleteTempLogDir()
                        LogWriter.deleteLogDir()
                    }
                    else if(it.message!!.contains("expected") && retrySendLog <= 5)
                    {
                        retrySendLog++
                        sendLogFiles(file = file, key = key, message = message, isErrorLog =  isErrorLog,isDeleteLog)
                    }
                }
                catch (e:Exception)
                {
                    e.printStackTrace()
                }
                Log.e("sendLogFiles", "${it.message}")
            })
    }
    fun getAllDiscountDetails() {
        requestData(mNetworkService.getAllDiscountDetails(preferencesHelper.baseUrl+NetworkRequestEndPoints.GET_DISCOUNT_DETAILS,MainApp.sn!!),
            {
                getDiscountDetailsObserver.postValue(it)
            },priority = ApiService.PRIORITY_HIGH)
    }
    fun importInjectKey() {
        var serialNumber: String = if (BuildConfig.POS_TYPE == "LANDI") {
            AppConstant.LANDI_SERIAL_NUMBER // Hardcode
        } else {
            MainApp.sn!!
        }
     /*   if(BuildConfig.DEBUG)
        {
            serialNumber= "PP35271806013389"
        }*/
        requestData(mNetworkService.injectKeyImport(NetworkRequestEndPoints.INJECT_KEY_BASE_URL,serialNumber),
            {
                try {
                    injectkeyObserver.postValue(it)
                }
                catch (e: Exception)
                {
                    showSnakBar.postValue("Inject Key API Error")
                    e.printStackTrace()
                }


            })
    }
    fun checkAppUpdateAvailable() {
        var buildType= ""
        buildType = if(BuildConfig.POS_TYPE == "B_TPE") {
            "blue_tpe"
        } else {
            BuildConfig.POS_TYPE
        }
        requestData(mNetworkService.getAppUpdateDetails(preferencesHelper.baseUrl+ NetworkRequestEndPoints.GET_APP_UPDATE_DETAILS,buildType),
            {
                appUpdateObserver.postValue(it)
            },priority = ApiService.PRIORITY_HIGH)
    }


    val qrCodeTicket = MutableLiveData<QRCodeTicketResponse>()
    val qrCodeTicketError = MutableLiveData<String>()
    fun getFuelUpQrCode(amount:Int) {

        val referenceModel = preferencesHelper.getReferenceModel()

        var qrCodeTicketModel = QrCodeTicket(
            authToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczpcL1wvd29ya3N1aXRlLmRldlwvYjerVwvYXV0aFwvbG9naW4iLCJpYXQiOjE1ODAyODA3Mjks90ImV4cCI6MTYxMT12kwMzEyOCwibmJmIjoxNTgwMjgwNzI5LCJqdGkiOiJBYXE1QkdnT0p1dG1ycUdIIiwic3ViIjoxLCJwcnYiOiI4MThmNWM5OGFjZTIzNzUzMmQ5ZDQ5NDNmZDhlZmI1NDBiODU1YjQyIiwicmVtZW1iZXIiOjEsInR5cGUiOjF9.wK4OhcwUWa9uwFboqkZCOznjnRnjU19yzoCGCKIZUY0W",
            codeGenerateUrl = "https://banzinty.com/api/v1/fuel/code",
            scanMessage = MainApp.appContext.getString(R.string.please_scan_qr_code))

        if(referenceModel!!.qrCodeTicket!=null){
            qrCodeTicketModel = referenceModel.qrCodeTicket!!
        }

        val item = QRCodeTicketRequest(amount,qrCodeTicketModel.authToken)
        mNetworkService.generateQrCodeTicket(qrCodeTicketModel.codeGenerateUrl!!,item).subscribeOn(rx.schedulers.Schedulers.io())
            .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
            .subscribe({
                if(!it.message.isNullOrEmpty()){
                    qrCodeTicketError.postValue(it.message)
                } else {
                    qrCodeTicket.postValue(it)
                }
            }, {
                it.printStackTrace()
                Log.e("getFuelUpQrCode", "getFuelUpQrCode ERROR $it")
                qrCodeTicketError.postValue(it.message)
            })
    }

}
