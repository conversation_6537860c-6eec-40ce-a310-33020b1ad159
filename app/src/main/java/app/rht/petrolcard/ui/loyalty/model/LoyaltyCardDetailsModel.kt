package app.rht.petrolcard.ui.loyalty.model

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName

class LoyaltyCardDetailsModel(
    @SerializedName("id")
    @Expose
    val id: Int? = null,

    @SerializedName("cin")
    @Expose
    val cin: String? = null,

    @SerializedName("nom")
    @Expose
    val nom: String? = null,

    @SerializedName("prenom")
    @Expose
    val prenom: String? = null,

    @SerializedName("date_naissance")
    @Expose
    val dateNaissance: String? = null,

    @SerializedName("lieu_naissance")
    @Expose
    val lieuNaissance: String? = null,

    @SerializedName("tel")
    @Expose
    val tel: String? = null,

    @SerializedName("adresse")
    @Expose
    val adresse: String? = null,

    @SerializedName("ville")
    @Expose
    val ville: String? = null,

    @SerializedName("num_permis")
    @Expose
    val numPermis: String? = null,

    @SerializedName("categorie")
    @Expose
    val categorie: String? = null,

    @SerializedName("cin_file")
    @Expose
    val cinFile: String? = null,

    @SerializedName("permis_file")
    @Expose
    val permisFile: String? = null,

    @SerializedName("pan")
    @Expose
    val pan: String? = null,

    @SerializedName("station")
    @Expose
    val station: Int? = null,

    @SerializedName("selected")
    @Expose
    val selected: Boolean = false
)