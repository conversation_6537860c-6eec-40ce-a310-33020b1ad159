package app.rht.petrolcard.ui.reference.model

import android.graphics.Bitmap
import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import java.lang.Exception
import androidx.annotation.Keep
@Keep
data class CategoryListModel(
    val category_id: Int,
    val hs_code: String?,
    val category_name: String,
    val color_code: String,
    val icon: String,
    val sub_products: ArrayList<SubProduct>?,
    val sub_categories: List<SubProduct>?
):BaseModel()
{
    fun getCardBgColor(): Int {
        return try {
            val color = color_code
            Color.parseColor(color)
        } catch (e:Exception) {
            e.printStackTrace()
            Color.parseColor("#9B76E9")
        }
    }
}