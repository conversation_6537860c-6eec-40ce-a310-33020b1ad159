package app.rht.petrolcard.ui.product.model


import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep

//@Keep
//data class GetFpStateResponse(
//    @SerializedName("FDCMessage")
//    var fDCMessage: GfpsFDCMessage?
//)

//@Keep
//data class GfpsDeviceClass(
//    @SerializedName("DeviceID")
//    var deviceID: Int?,
//    @SerializedName("DeviceState")
//    var deviceState: String?,
//    @SerializedName("ErrorCode")
//    var errorCode: String?,
//    @SerializedName("LockingApplicationSender")
//    var lockingApplicationSender: String?,
//    @SerializedName("Nozzle")
//    var nozzle: List<GfpsNozzle>?,
//    @SerializedName("PumpNo")
//    var pumpNo: Int?,
//    @SerializedName("Type")
//    var type: String?
//)

//@Keep
//data class GfpsFDCMessage(
//    @SerializedName("ApplicationSender")
//    var applicationSender: Int?,
//    @SerializedName("FDCdata")
//    var fDCdata: GfpsFDCdata?,
//    @SerializedName("MessageID")
//    var messageID: Int?,
//    @SerializedName("MessageType")
//    var messageType: String?,
//    @SerializedName("WorkstationID")
//    var workstationID: String?,
//    @SerializedName("xmlns:xsd")
//    var xmlnsXsd: String?,
//    @SerializedName("xmlns:xsi")
//    var xmlnsXsi: String?
//)

//@Keep
//data class GfpsFDCdata(
//    @SerializedName("DeviceClass")
//    var deviceClass: GfpsDeviceClass?,
//    @SerializedName("FDCTimeStamp")
//    var fDCTimeStamp: String?
//)

//@Keep
//data class GfpsNozzle(
//    @SerializedName("ErrorCode")
//    var errorCode: String?,
//    @SerializedName("LogicalNozzle")
//    var logicalNozzle: String?,
//    @SerializedName("LogicalState")
//    var logicalState: String?,
//    @SerializedName("NozzleNo")
//    var nozzleNo: Int?,
//    @SerializedName("TankLogicalState")
//    var tankLogicalState: String?
//)