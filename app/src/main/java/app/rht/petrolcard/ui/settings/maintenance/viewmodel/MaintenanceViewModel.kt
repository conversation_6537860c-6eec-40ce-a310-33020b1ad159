
package app.rht.petrolcard.ui.settings.maintenance.viewmodel


import android.util.Log
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import java.io.File


class MaintenanceViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper)
