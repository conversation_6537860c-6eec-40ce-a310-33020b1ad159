package app.rht.petrolcard.ui.settings.common.viewmodel

import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper

class SettingsViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper)