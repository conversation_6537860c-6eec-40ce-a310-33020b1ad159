package app.rht.petrolcard.ui.iccpayment.model

import androidx.annotation.Keep

@Keep
class StoreCardDataModel (
    var sn: String? = null,
    var pan: String? = null,
    var plafondJournalier: String? = null,
    var plafondMensuel: String? = null,
    var plafondHebdo: String? = null,
    var NBR_TRS_MENSUEL: String? = null,
    var NBR_TRS_HEBDO: String? = null,
    var NBR_TRS_JOURNALIER: String? = null,
    var PLF_MIN_PREPAYE: String? = null,
    var PLF_MAX_PREPAYE: String? = null,
    var NBR_TRS_OFF: String? = null,
    var compteur_NBR_TRS_OFF: String? = null,
    var plafondJournaliercompt: String? = null,
    var plafondMensuelCompt: String? = null,
    var plafondHebdoCompt: String? = null,
    var COMPTEUR_NBR_TRS_MENSUEL: String? = null,
    var COMPTEUR_NBR_TRS_HEBDO: String? = null,
    var COMPTEUR_NBR_TRS_JOURNALIER: String? = null,
    var dateLastPLF: String? = null,
    var mRestStationCard: String? = null,
    var mRestHoraireCard: String? = null,
    var mRestHolidaysCard: String? = null,
    var mRestSecteurCard: String? = null,
    var mRestArticleCard: String? = null,
    var amountCard: String? = null,
    var dateLastCredit: String? = null,
    var amountLastCredit: String? = null,
    var compteurTrxDebit: String? = null,
    var compteurTrxCredit: String? = null
)