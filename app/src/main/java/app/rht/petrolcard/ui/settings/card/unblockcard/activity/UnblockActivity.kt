package app.rht.petrolcard.ui.settings.card.unblockcard.activity

import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.RemoteException
import androidx.appcompat.content.res.AppCompatResources
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityUnblockBinding

import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.settings.card.unblockcard.viewmodel.UnblockViewModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.extensions.showSuccessDialog
import kotlinx.android.synthetic.main.toolbar.view.*
import wangpos.sdk4.libbasebinder.BankCard
import java.lang.Exception
import java.util.*

@Suppress("DEPRECATION")
class UnblockActivity : BaseActivity<UnblockViewModel>(UnblockViewModel::class) {
    private var TAG= "UnblockActivity"
    private lateinit var mBinding: ActivityUnblockBinding
    private var mBankCard: BankCard? = null
    private var infoCarte: String? = null
    private val icCpuReader: UICCpuReader? = null
    private var panNumber: String? = ""
    private var returnValue: Int? = 0
    var authKey:String? = ""
    var dateExpirationCard:String? = ""
    var cardExpired:Boolean = false
    var cardType:Int? = 0
    private var errorMessage = ""
    private var errorTitle = "Error"
    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_unblock)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        val readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
    }
    private fun setupToolbar()
    {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.card_verification)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
            finish()
        }
    }
    override fun setObserver() {
        mViewModel.unBlockCardObserver.observe(this) {
            if (it.reponse == "1") {
                if (updateCardStatus()) {
                    val dt = Support.dateToStringPlafond(Date())
                    mViewModel.unlockCardNotification(panNumber!!, dt)
                } else {
                    gotoAbortMessageActivity(
                        getString(R.string.error),
                        getString(R.string.failed_update_card_status)
                    )
                }
            } else {
                gotoAbortMessageActivity(getString(R.string.unable_to_unblock), it.error!!)
            }
        }
        mViewModel.unCardNotificationObserver.observe(this) {
            if (it.reponse == "1") {
                gotoSuccessMessageActivity(resources.getString(R.string.success),resources.getString(R.string.card_unblocked_success))
                Handler().postDelayed({
                    startActivity(Intent(this, MenuActivity::class.java))
                    finish()
                }, 2000)
            } else {
                gotoAbortMessageActivity(getString(R.string.unable_to_unblock), it.error!!)
            }
        }

    }
    fun updateCardStatus():Boolean
    {
        //if(BuildConfig.DEBUG)
        //   return UtilsCardInfo.setStatusCard(mBankCard, icCpuReader, infoCarte, 1, this)
        //else
            return UtilsCardInfo.setStatusCard(mBankCard, icCpuReader, infoCarte, 1, this)
    }
    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        override fun doInBackground(vararg params: String): Int {
            val responseLength = IntArray(1)
            val responseData = ByteArray(80)
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(this@UnblockActivity)
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    UtilsCardInfo.connectPAX()
                }
                // B TPE
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard!!.readCard(BankCard.CARD_TYPE_NORMAL, BankCard.CARD_MODE_ICC, 60, responseData, responseLength, AppConstant.TPE_APP)
                }
                if (Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "05"  ||
                    Utils.byteArrayToHex(responseData)!!.substring(0, 2) == "07"||
                    BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX"
                ) {
                    publishProgress(0)
                    UtilsCardInfo.beep(mCore, 10)
                    infoCarte = UtilsCardInfo.getCardInfo(mBankCard,icCpuReader,this@UnblockActivity) //icCpuReader Not Required for BTPE
                    if (infoCarte != null && infoCarte!!.isNotEmpty())
                        panNumber = infoCarte!!.substring(0, 19)
                    else {
                        errorTitle = getString(R.string.unknown_card)
                        errorMessage = getString(R.string.card_error_9)
                        return -1
                    } //Abort Transaction
                    // CARD AUTHENTICATION WITH ENCRYPTED KEY
                    if (panNumber != null) {
                        authKey = assignKeyForCard(panNumber!!)
                    }
                    val externalAuth1 = UtilsCardInfo.externalAuth1(mBankCard,icCpuReader, authKey,this@UnblockActivity)
                    val externalAuth2 = UtilsCardInfo.externalAuth2(mBankCard, icCpuReader,authKey,this@UnblockActivity)

                    if (authKey != null && externalAuth1 && externalAuth2) {
                        UtilsCardInfo.readRestrictionCard(mBankCard, icCpuReader, this@UnblockActivity)
                        infoCarte = UtilsCardInfo.readRecordLinear(mBankCard, icCpuReader, "2F09", "02", "28", this@UnblockActivity)
                        dateExpirationCard = UtilsCardInfo.getDateExpCard(mBankCard, infoCarte)
                        val mToday: Date = Support.getDateComparison(Date())!!
                        cardExpired = UtilsCardInfo.isCardExpired(mToday, dateExpirationCard)
                        return if(panNumber != null) {
                            if (cardExpired) {
                                errorMessage =resources.getString(R.string.contact_agent)
                                errorTitle =  resources.getString(R.string.card_expired)
                                -2
                            } else {
                                1
                            }
                        } else {
                            -3
                        }
                    }
                    else
                    {
                        errorTitle = getString(R.string.invalid_card)
                        errorMessage = getString(R.string.card_error_10)
                        return -1
                    }
                }
                else
                {
                    try {
                        if (BuildConfig.POS_TYPE == "B_TPE") mBankCard!!.breakOffCommand()
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                    }
                }

            }
            catch (e: Exception)
            {
                e.printStackTrace()
                errorMessage=resources.getString(R.string.read_card_failed)
                errorTitle =  resources.getString(R.string.unknown_card)
                return -3
            }
            return 0
        }
        override fun onPreExecute() {

        }
        override fun onPostExecute(result: Int?) {

            when (result) {
                -1,-2,-3 -> {
                    gotoAbortMessageActivity(errorTitle,errorMessage)
                }
                1 -> {
                  if (Support.checkBlackList(this@UnblockActivity, panNumber)) {
                      val msg =resources.getString(R.string.contact_agent)
                      val title = resources.getString(R.string.unable_to_unblock)
                      gotoAbortMessageActivity(title,msg)
                    } else {
                        mViewModel.unBlockCard(panNumber!!)
                    }

                }
                else -> {
                    val msg =resources.getString(R.string.read_card_failed)
                    val title = resources.getString(R.string.unknown_card)
                    gotoAbortMessageActivity(title,msg)
                }
            }


        }
        override fun onProgressUpdate(vararg values: IntArray) {
            mBinding.msgText.text = getString(R.string.card_inserted_successfully)

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }
    override fun onBackPressed() {

    }
}
