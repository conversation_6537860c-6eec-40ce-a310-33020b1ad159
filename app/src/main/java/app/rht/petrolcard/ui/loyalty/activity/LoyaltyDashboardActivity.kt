package app.rht.petrolcard.ui.loyalty.activity

import android.content.Intent
import android.os.Bundle
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityLoyaltyDashboardBinding
import app.rht.petrolcard.ui.menu.activity.LoyaltyMenuActivity
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.extensions.showSnakeBar
import kotlinx.android.synthetic.main.toolbar.view.*

class LoyaltyDashboardActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {

    private lateinit var mBinding: ActivityLoyaltyDashboardBinding
    private val TAG = LoyaltyBalanceActivity::class.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_loyalty_dashboard)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.loyalty_label)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            onBackPressed()
        }
    }

    override fun setObserver() {

    }

    fun btnClick(view: android.view.View) {
        setBeep()
        when(view){
            mBinding.activationCard -> {
                val mIntent = Intent(this,LoyaltyActivationActivity::class.java)
                startActivity(mIntent)
            }
            mBinding.myBalanceCard -> {
                val mIntent = Intent(this,LoyaltyBalanceActivity::class.java)
                startActivity(mIntent)
            }
            mBinding.loyaltyGiftCard -> {
                val mIntent = Intent(this, QrScanActivity::class.java)
                startActivity(mIntent)
            }
            mBinding.cardChangeCard -> {
                val mIntent = Intent(this, CardChangeActivity::class.java)
                startActivity(mIntent)
            }
            mBinding.middleMarketCard -> {
                val mIntent = Intent(this, MiddleMarketActivity::class.java)
                startActivity(mIntent)
            }
            mBinding.loyaltyTransactionLayout -> {
                showSnakeBar("loyaltyTransactionLayout")
                val mIntent = Intent(this,LoyaltyMenuActivity::class.java)
                startActivity(mIntent)
            }
        }
    }
}