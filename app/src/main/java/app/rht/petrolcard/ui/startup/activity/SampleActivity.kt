package app.rht.petrolcard.ui.startup.activity

import android.os.Bundle
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivitySplashBinding

import app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel
import app.rht.petrolcard.ui.startup.viewmodel.StartupViewModel

@Suppress("DEPRECATION")
class SampleActivity : BaseActivity<StartupViewModel>(StartupViewModel::class) {

    private lateinit var mBinding: ActivitySplashBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_splash)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

    }

    override fun setObserver() {

    }
}
