package app.rht.petrolcard.ui.reference.model

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import androidx.annotation.Keep
@Keep
data class CompanyModel(
    @SerializedName("logo")
    @Expose
    val logo: String,

    @SerializedName("name")
    @Expose
    val name: String,

    @SerializedName("reload")
    @Expose
    val reload: <PERSON>ole<PERSON>,

    @SerializedName("footer")
    @Expose
    val footer: String
)