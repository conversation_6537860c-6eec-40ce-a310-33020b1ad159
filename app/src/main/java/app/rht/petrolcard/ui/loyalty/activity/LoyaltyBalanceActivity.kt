package app.rht.petrolcard.ui.loyalty.activity

import android.app.Activity
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Bundle
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import androidx.viewpager.widget.ViewPager
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityLoyaltyBalanceBinding
import app.rht.petrolcard.ui.loyalty.adapter.FragmentAdapter
import app.rht.petrolcard.ui.loyalty.fragments.AvailableGiftsFragment
import app.rht.petrolcard.ui.loyalty.fragments.GiftHistoryFragment
import app.rht.petrolcard.ui.loyalty.viewmodel.LoyaltyBalanceViewModel
import app.rht.petrolcard.ui.loyalty.model.LoyaltyBalance
import app.rht.petrolcard.ui.settings.card.history.model.HistoryItemModel
import app.rht.petrolcard.utils.constant.AppConstant.CARD_NFC_TAG
import app.rht.petrolcard.utils.constant.AppConstant.CARD_NUMBER
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_CARD_HOLDER
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_CARD_NUMBER
import kotlinx.android.synthetic.main.tabs_loyalty_balance.*
import kotlinx.android.synthetic.main.toolbar.view.*
import kotlinx.android.synthetic.main.activity_loyalty_balance.*
import java.util.ArrayList

@Suppress("DEPRECATION")
class LoyaltyBalanceActivity : BaseActivity<LoyaltyBalanceViewModel>(LoyaltyBalanceViewModel::class) {

    private lateinit var mBinding: ActivityLoyaltyBalanceBinding
    private val TAG = LoyaltyBalanceActivity::class.simpleName

    private var cardNumber = ""
    private var cardHolder = ""
    private var nfcTag = ""
    private lateinit var adapter :FragmentAdapter
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_loyalty_balance)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        def = item2.textColors

        adapter = FragmentAdapter(this, supportFragmentManager, 2)
        mBinding.viewPager.adapter = adapter

        scanLoyaltyCard()

        mBinding.viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener{
            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {

            }

            override fun onPageSelected(position: Int) {
                if(cardNumber.isNotEmpty()){
                    when(position){
                        0-> getAvailableGifts(cardNumber)
                        1-> getGiftsHistory(cardNumber)
                    }
                }
            }

            override fun onPageScrollStateChanged(state: Int) {

            }

        })
    }

    //region Card scanning
    private fun scanLoyaltyCard() {
        val intent = Intent(this, ICCLoyaltyActivity::class.java)
        loyaltyCardResultLauncher.launch(intent)
    }
    private var loyaltyCardResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                log(TAG,"data received ++++ ${data.getStringExtra(LOYALTY_CARD_NUMBER)}")
                cardNumber = data.getStringExtra(LOYALTY_CARD_NUMBER)!!
                cardHolder = data.getStringExtra(LOYALTY_CARD_HOLDER)!!
                nfcTag = data.getStringExtra(CARD_NFC_TAG)!!
                scanNfcTag(cardNumber)
            }
            else{
                gotoAbortMessageActivity("Try Again","Card reading failed")
            }
        }
    }
    //endregion

    //region NFC scanning
    private fun scanNfcTag(cardNumber:String){
        val intent = Intent(this, NfcScanActivity::class.java)
        intent.putExtra(CARD_NUMBER,cardNumber)
        nfcTagResultLauncher.launch(intent)
    }
    private var nfcTagResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                val tag = data.getStringExtra(CARD_NFC_TAG)!!
                log(TAG,"nfc data received ++++ $tag")

                mBinding.loadingLayout.visibility = View.GONE
                mViewModel.getLoyaltyBalance(cardNumber)
            }
            else{
                gotoAbortMessageActivity("Try Again","Nfc scanning failed")
            }
        }
    }
    //endregion

    private fun setupToolbar() {
        showLoading(true)
        mBinding.loyaltyCardLayout.visibility = View.GONE

        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.loyalty_balance)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            onBackPressed()
        }
    }

    override fun setObserver() {
        mViewModel.loyaltyCustomerResponse.observe(this){
            val response = it
            if(response.reponse == "1"){
                showLoading(false)
                mBinding.loyaltyCardLayout.visibility = View.VISIBLE
                mBinding.tvCardNumber.text = cardNumber.replace("....".toRegex(), "$0 ")
                setCardData(it.contenu!![0])
                adapter.getItem(0)

                getAvailableGifts(cardNumber)
                //getGiftsHistory(cardNumber)
            }
            else {
                showLoading(true)
                showToast(response.error)
                finish()
            }
        }

    }

    private fun setCardData(data: LoyaltyBalance){
        mBinding.tvCardHolderName.text = cardHolder
        mBinding.tvCardNumber.text = cardNumber
        mBinding.tvPoint.text = data.points.toString()

    }

    lateinit var def : ColorStateList
    fun onTabClick(v: View){
        when (v.id){
            R.id.item1 -> {
                select.animate().x(0f).duration = 300
                item1.setTextColor(Color.WHITE)
                item2.setTextColor(def)

                viewPager.currentItem = 0
            }
            R.id.item2 -> {
                item1.setTextColor(def)
                item2.setTextColor(Color.WHITE)
                val size: Int = item2.width
                select.animate().x(size.toFloat()).duration = 300

                viewPager.currentItem = 1
            }
        }
    }

    private fun showLoading(isVisible: Boolean){
        if(!isVisible)
            mBinding.loadingLayout.visibility = View.GONE
        else
            mBinding.loadingLayout.visibility = View.VISIBLE
    }

    private fun getAvailableGifts(pan:String){
        val fragment: Fragment = adapter.getRegisteredFragment(0)
        (fragment as AvailableGiftsFragment).getAvailableGifts(pan)
    }

    private fun getGiftsHistory(pan:String){
        val fragment: Fragment = adapter.getRegisteredFragment(1)
        (fragment as GiftHistoryFragment).getGiftHistory(pan)
    }

   /* private fun getFragment(){
        val pos: Int = mBinding.viewPager.currentItem
        val fragment: Fragment = adapter.getRegisteredFragment(pos)
        when (pos) {
            0 -> {
                (fragment as NPListFragment).refreshT()
            }
            1 -> {
                (fragment as PListFragment).refreshK()
            }
            2 -> {
                (fragment as FavouritesFragment).refreshL()
            }
        }
    }*/
}