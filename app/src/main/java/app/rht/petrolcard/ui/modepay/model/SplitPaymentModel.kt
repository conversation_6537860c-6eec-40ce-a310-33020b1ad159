package app.rht.petrolcard.ui.modepay.model

import android.os.Parcel
import android.os.Parcelable

class SplitPaymentModel() :Parcelable {
    var totalAmount: String? = "0"
    var isSplitPayment: Boolean? = false
    var isFirstPaymentDone: Boolean? = false
    var isSecondPaymentDone: Boolean? = false
    var isError: Boolean? = false
    var firstModeOfPayment: Int = 0
    var secondModeOfPayment: Int = 0
    var firstPaymentName: String? = ""
    var secondPaymentName: String? = ""
    var firstPaymentAmount: Double? = 0.0
    var secondPaymentAmount: Double? = 0.0

    constructor(parcel: Parcel) : this() {
        totalAmount = parcel.readString()
        isSplitPayment = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        isFirstPaymentDone = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        isSecondPaymentDone = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        isError = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        firstModeOfPayment = parcel.readInt()
        secondModeOfPayment = parcel.readInt()
        firstPaymentName = parcel.readString()
        secondPaymentName = parcel.readString()
        firstPaymentAmount = parcel.readValue(Double::class.java.classLoader) as? Double
        secondPaymentAmount = parcel.readValue(Double::class.java.classLoader) as? Double
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(totalAmount)
        parcel.writeValue(isSplitPayment)
        parcel.writeValue(isFirstPaymentDone)
        parcel.writeValue(isSecondPaymentDone)
        parcel.writeValue(isError)
        parcel.writeInt(firstModeOfPayment)
        parcel.writeInt(secondModeOfPayment)
        parcel.writeString(firstPaymentName)
        parcel.writeString(secondPaymentName)
        parcel.writeValue(firstPaymentAmount)
        parcel.writeValue(secondPaymentAmount)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SplitPaymentModel> {
        override fun createFromParcel(parcel: Parcel): SplitPaymentModel {
            return SplitPaymentModel(parcel)
        }

        override fun newArray(size: Int): Array<SplitPaymentModel?> {
            return arrayOfNulls(size)
        }
    }
}