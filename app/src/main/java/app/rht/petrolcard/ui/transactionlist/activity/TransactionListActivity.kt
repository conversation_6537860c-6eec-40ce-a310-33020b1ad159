//package app.rht.petrolcard.ui.transactionlist.activity
//
//import android.app.AlertDialog
//import android.content.*
//import android.graphics.Typeface
//import android.os.Bundle
//import android.os.Handler
//import android.os.Looper
//import android.text.Html
//import android.util.Log
//import android.view.View
//import androidx.core.content.ContextCompat
//import androidx.databinding.DataBindingUtil
//import app.rht.petrolcard.R
//import app.rht.petrolcard.baseClasses.activity.BaseActivity
//import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
//import app.rht.petrolcard.database.baseclass.*
//import app.rht.petrolcard.databinding.ActivityTransactionListBinding
//import app.rht.petrolcard.service.FusionService
//import app.rht.petrolcard.service.model.RFIDPumpsModel
//import app.rht.petrolcard.ui.common.model.IntentExtrasModel
//import app.rht.petrolcard.ui.menu.activity.MenuActivity
//import app.rht.petrolcard.ui.modepay.activity.ModePayActivity
//import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
//import app.rht.petrolcard.ui.reference.model.*
//import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
//import app.rht.petrolcard.ui.transactionlist.model.*
//import app.rht.petrolcard.utils.CoroutineAsyncTask
//import app.rht.petrolcard.utils.LocaleManager
//import app.rht.petrolcard.utils.Support
//import app.rht.petrolcard.utils.UtilsCardInfo
//import app.rht.petrolcard.utils.constant.AppConstant
//import app.rht.petrolcard.utils.constant.AppConstant.OFFLINE_TRX_MODE
//import app.rht.petrolcard.utils.esdPrinterJobs.UnsignedFusionTransactions
//import app.rht.petrolcard.utils.esdPrinterJobs.UnsignedTransaction
//import app.rht.petrolcard.utils.extensions.showDialog
//import app.rht.petrolcard.utils.extensions.showSnakeBar
//import app.rht.petrolcard.utils.extensions.showSnakeBarColor
//import app.rht.petrolcard.utils.fuelpos.*
//import app.rht.petrolcard.utils.fuelpos.models.PosTransaction
//import app.rht.petrolcard.utils.fuelpos.tcp.FuelPosTcpClient
//import app.rht.petrolcard.utils.helpers.MultiClickPreventer
//import app.rht.petrolcard.utils.tax.TaxModel
//import app.rht.petrolcard.utils.tax.TaxUtils
//import com.altafrazzaque.ifsfcomm.*
//import com.altafrazzaque.ifsfcomm.ifsf.models.FuelSaleTrxPrams
//import com.google.gson.Gson
//import com.google.gson.JsonSyntaxException
//import kotlinx.android.synthetic.main.toolbar.view.*
//import net.sqlcipher.SQLException
//import net.sqlcipher.database.SQLiteException
//import org.apache.commons.lang3.StringUtils
//import org.apache.commons.lang3.exception.ExceptionUtils
//import java.io.*
//import java.net.Socket
//import java.util.*
//
//
//class TransactionListActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<TransactionFromFcc>,android.view.View.OnClickListener  {
//    private lateinit var mBinding: ActivityTransactionListBinding
//    private var TAG = TransactionListActivity::class.simpleName
//    private var stationMode=0
//    private var intentExtrasModel: IntentExtrasModel? = null
//    private var transactionToPay: TransactionFromFcc? = null
//    private var carburant: String? = null
//    private var volume: String? = null
//    private var montant: String? = null
//    private val mReponseFusion: ResponseFusion? = null
//    private var fusionProductList: ArrayList<ProductPT> = ArrayList()
//    private var transactionSequenceList: ArrayList<DeviceClassGAFT> = java.util.ArrayList()
//    private var mTerminalDAO: TerminalDao? = null
//    private var mTerminal: TerminalModel? = null
//    private var mFuelPosDAO: FuelPosDao? = null
//    private var mFuelPOS: FuelPOSModel? = null
//    private var mTransactionsPOSDAO: TransactionFromFuelPosDao? = null
//    private var transactionList: ArrayList<TransactionFromFcc> = java.util.ArrayList()
//    private var fuelTransactionStatusDAO: FuelTransactionStatusDao? = null
//    private var mTransaction: TransactionModel? = null
//    private var mProduitDAO: ProductsDao? = null
//    var selectedProduct: ProductModel? = null
//    private var mTransactionDAO: TransactionDao? = null
//    var daoError = false
//    var socket: Socket? = null
//    var command: String? = null
//    var response: String? = null
//    var produit: String? = null
//    var pump = 0
//    var amount = 0.0
//    var quantite = 0.0
//    var idterminal = 0
//    var result = false
//    var fusionFuelTrxList: MutableList<TransactionFromFcc> = ArrayList()
//    lateinit var fuelpos : FuelPOSModel
//    lateinit var fusion : FusionModel
//    var decimal  = 2
//    lateinit var mOperationsAdapter : RecyclerViewArrayAdapter<TransactionFromFcc>
//    var fuelQtyUnit = "L"
//    var referenceModel : ReferenceModel? = null
//    var isFrench = false
//    override fun onCreate(savedInstanceState: Bundle?) {
//        setTheme()
//        super.onCreate(savedInstanceState)
//        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_transaction_list)
//        mBinding.model = mViewModel
//        mBinding.lifecycleOwner = this
//        mBinding.executePendingBindings()
//
//        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
//        fuelpos = prefs.getFuelPosModel()!!
//        fusion = prefs.getFusionModel()!!
//        referenceModel = prefs.getReferenceModel()
//        isFrench = LocaleManager.getLanguage(this) == LocaleManager.LANGUAGE_FRENCH
//
//        fuelQtyUnit = prefs.getReferenceModel()!!.FUEL_QTY_UNIT!!
//        if (intentExtrasModel!!.stationMode != null) {
//            stationMode = intentExtrasModel!!.stationMode!!
//            if(intentExtrasModel!!.loyaltyTrx)
//            {
//                stationMode = 1
//            }
//        }
//        transactionToPay = null
//        setupToolbar()
//        //val fuelTransactionStatusDAO = FuelTransactionStatusDao()
//        //log(TAG, "Transaction List ::" + Gson().toJson(fuelTransactionStatusDAO.getAllTransactionList1()))
//
//        if (stationMode == 3 && fuelpos.isExist) {
//            showProgress(true)
//            startFuelPosReceiver()
//           // startFuelPos()
//        } else {
//            log(TAG,    "Running Fusion Task")
//            showProgress(true)
//            FusionService.getProductTable()
//            val type: Int = fusion.COMM_TYPE
//            if (type == 1) {
//                startIfsfReceiver()
//                searchTransactionsInFusion()
//            } else if (type == 2) {
//                SearchTransactionUsingTcp().execute()
//            }
//        }
//    }
//    private fun setupToolbar()
//    {
//        mBinding.toolbarTransactionList.toolbar.tvTitle.text = resources.getString(R.string.transaction_list_title)
//        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.setOnClickListener(this)
//        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.setImageResource(R.drawable.ic_baseline_refresh_24)
//        mBinding.toolbarTransactionList.toolbar.toolbar_right_image.visibility = View.VISIBLE
//        mBinding.toolbarTransactionList.toolbar.setNavigationOnClickListener {
//        mBinding.toolbarTransactionList.toolbar.isEnabled = false
//        setBeep()
//        gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel)) }
//    }
//    private fun startIfsfReceiver() {
//        val filter = IntentFilter()
//        filter.addAction(ACTION_IFSF_READ_DATA)
//        filter.addAction(ACTION_IFSF_CONNECTION_STATE)
//        registerReceiver(ifsfRecever, filter)
//    }
//    var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
//        override fun onReceive(context: Context, intent: Intent) {
//            val action = intent.action
//            try {
//                if (action == ACTION_IFSF_READ_DATA) {
//                    val msg = intent.getStringExtra(IFSF_STRING_MESSAGE)
//                    performNextStepProductTask(msg!!)
//                }
//                if (action == ACTION_IFSF_CONNECTION_STATE) {
//                    val state = intent.getStringExtra(IFSF_CONNECTION_STATE)
//                    log(TAG, "Received: $state")
//                    if (state == "TIMEOUT") {
//                        //gotoOfflineModePage()
//                        showNoTrxLayout(true, getString(R.string.unable_to_connect_fcc))
//                    }
//                    if (state == "Connected") {
//                        searchTransactionsInFusion()
//                    }
//                }
//            } catch (e: Exception) {
//                log(TAG, "ERROR IN BR RECEIVER")
//                e.printStackTrace()
//                log(TAG, "Exception: " + e.message)
//                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//              //  mViewModel.generateLogs(e.message!!,0)
//            }
//        }
//    }
//
//    private fun performNextStepProductTask(message: String) {
//        var message = message
//        val errorCode: String = StringUtils.substringBetween(message, "<ErrorCode>", "</ErrorCode>")
//        val errorMessage = FusionService.getFusionErrorMessage(errorCode)
//
//        if(errorCode!=null && !errorCode.contains("ERRCD_OK")) {
//            val errMsg = FusionService.getErrorMessage(errorCode)
//            showSnakeBarColor("$errMsg ")
//        }
//
//        if (errorCode.contains("ERRCD_OK")) {
//            if (message.contains("ServiceResponse")) {
//                message = formatMessage(message)
//                val overallResult: String =
//                    StringUtils.substringBetween(message, "OverallResult=\"", "\"")
//                val requestType: String =
//                    StringUtils.substringBetween(message, "RequestType=\"", "\"")
//                val gson = Gson()
//                if (overallResult == "Success" && errorCode == "ERRCD_OK") {
//                    val jsonString: String = Support.xmlToJsonString(message)!!
//                    when (requestType) {
//                        "GetAvailableFuelSaleTrxs" -> {
//                            clearFccFuelTrxList()
//                            try {
//                                val serviceResponse: ServiceResponseGAFT = gson.fromJson(jsonString,
//                                    ServiceResponseGAFT::class.java
//                                )
//                                val currentTrxList: ArrayList<DeviceClassGAFT> = java.util.ArrayList()
//                                val trxList: List<DeviceClassGAFT> = serviceResponse.serviceResponse.fDCdata.deviceClasses
//                                if (prefs.getReferenceModel()!!.RFID_TERMINALS != null) {
//                                    val pumpsModels: List<RFIDPumpsModel> =
//                                        prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
//                                    if (pumpsModels != null) {
//                                        for (pumpsModel in pumpsModels) {
//                                            for (deviceClassGAFT in trxList) {
//                                                if (deviceClassGAFT.pumpNo == pumpsModel.pump_number) {
//                                                    currentTrxList.add(deviceClassGAFT)
//                                                    log(TAG, "Pump Available:: " + deviceClassGAFT.pumpNo)
//                                                }
//                                            }
//                                        }
//                                    } else {
//                                        log( TAG,
//                                            "Pump List Not Available")
//                                    }
//                                    log(
//                                       TAG,
//                                        "pumpsModels from API :: $pumpsModels"
//                                    )
//                                } else {
//                                    log(
//                                       TAG,
//                                        "Pump List Not Available"
//                                    )
//                                }
//                                updateTransactionSequenceList(currentTrxList)
//                            } catch (e: Exception) {
//                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                             //   mViewModel.generateLogs(e.message!!,0)
//                                log(TAG, "Conversion Issue, trying single transaction obj")
//                                val serviceResponse = gson.fromJson(jsonString, ServiceResponseGAFT2::class.java)
//                                if (serviceResponse != null) {
//                                    try {
//                                        val deviceClassGAFT: DeviceClassGAFT = serviceResponse.serviceResponse.fDCdata.deviceClass
//                                        log( TAG, "trxList:: $deviceClassGAFT")
//                                        val currentTrxList: MutableList<DeviceClassGAFT> =
//                                            java.util.ArrayList()
//                                        if (prefs.getReferenceModel()!!.RFID_TERMINALS != null) {
//                                            val pumpsModels: List<RFIDPumpsModel> =
//                                                prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
//                                            if (pumpsModels != null) {
//                                                for (pumpsModel in pumpsModels) {
//                                                    //for (deviceClassGAFT in trxList) {
//                                                    if (deviceClassGAFT.pumpNo == pumpsModel.pump_number) {
//                                                        currentTrxList.add(deviceClassGAFT)
//                                                        log(TAG, "Pump Available:: " + deviceClassGAFT.pumpNo)
//                                                    }
//                                                    //}
//                                                }
//                                            } else {
//                                                log(TAG, "Pump List Not Available")
//                                            }
//                                            log(TAG, "pumpsModels from API :: $pumpsModels")
//                                        } else {
//                                            log(TAG, "Pump List Not Available")
//                                        }
//                                        updateTransactionSequenceList(currentTrxList)
//                                    } catch (ex: Exception) {
//                                        log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                                        //  mViewModel.generateLogs(e.message!!,0)
//                                        ex.printStackTrace()
//                                        val stacktrace1 = ExceptionUtils.getStackTrace(ex)
//                                        log( TAG,
//                                            ex.message + " " + stacktrace1)
//                                    }
//                                }
//                            }
//                        }
//                        "GetFuelSaleTrxDetails" -> {
//                            try {
//                                val transaction: ServiceResponseGFST = gson.fromJson(jsonString, ServiceResponseGFST::class.java)
//                                addFuelTransactionToList(transaction)
//                            } catch (e: JsonSyntaxException) {
//                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                                e.printStackTrace()
//                                log(TAG, "Json syntax exception: " + e.message + " " + e.stackTrace)
//                              //  mViewModel.generateLogs(e.message!!,0)
//                            }
//                        }
//                        "GetProductTable" -> {
//                            try {
//                                val transactions: ServiceResponseGPT = gson.fromJson(jsonString, ServiceResponseGPT::class.java)
//                                fusionProductList.addAll(transactions.serviceResponse.fDCdata.fuelProducts.product)
//                                val fusionProductList1 = Gson().toJson(transactions)
//                                if (fusionProductList.isNotEmpty()) {
//                                    FusionService.prefs.saveProductTable(fusionProductList1)
//                                    log(TAG, "Product table update: $fusionProductList1")
//                                }
//                            } catch (e: Exception) {
//                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                                log(TAG, "Error occurred while fetching products from fusion: " + e.message)
//                             //   mViewModel.generateLogs(e.message!!,0)
//                                e.printStackTrace()
//                            }
//                        }
//                        "GetDSPConfiguration" -> {
//                            try {
//                                log(TAG,"DSP CONFIG::: $jsonString")
//                                dspConfiguration = gson.fromJson(jsonString, DSPConfiguration::class.java)
//                                val devices = dspConfiguration.serviceResponse.fDCdata.deviceClasses
//                                for(device in devices)
//                                {
//                                    val products = device.products
//                                    for(product in products)
//                                    {
//                                        if(!productColors.contains(product))
//                                        {
//                                            productColors.add(product)
//                                            log(TAG,"##### Product color added in list (Product: ${product.productName} ---- #${product.productColour})")
//                                        }
//                                    }
//                                }
//                            } catch (e: Exception) {
//                                log(TAG, "Error occurred while fetching products from fusion: " + e.message)
//                                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                              //  mViewModel.generateLogs(e.message!!,0)
//                                e.printStackTrace()
//                            }
//                        }
//                    }
//                } else {
//                    showToast(overallResult)
//                    finish()
//                }
//            }
//        }
//        else if(errorCode.contains("ERRCD_NOTRANS"))
//        {
//            showNoTrxLayout(true,getString(R.string.no_transaction_found))
//        }
//        else {
//            log(TAG, errorMessage)
//        }
//    }
//    /*private fun clearFusionListTrxList() {
//        fusionFuelTrxList.clear()
//        mBinding.progressMessage.visibility = View.GONE
//    }*/
//
//    private fun updateTransactionSequenceList(transactions: List<DeviceClassGAFT>) {
//        transactionSequenceList.clear()
//        transactionSequenceList.addAll(transactions)
//        log(TAG,"Transaction list updated.. " + transactionSequenceList.size)
//        log(TAG,"Transaction list updated.. " + transactionSequenceList.size)
//
//        for (transactionSequence in transactions) {
//            val trxSequenceNo: String = transactionSequence.transactionSeqNo.toString() + ""
//            val mPumpNumber: String = transactionSequence.pumpNo.toString() + ""
//            val params = FuelSaleTrxPrams(mPumpNumber, trxSequenceNo)
//            log(TAG,"Sending Command for Pump: $mPumpNumber SeqNo: $trxSequenceNo")
//            FusionService.sendFuelTrxDetailMsg(params, applicationContext)
//        }
//        if (transactionSequenceList.isEmpty()) {
//            showNoTrxLayout(true,getString(R.string.transactions_not_available))
//
//        } else {
//            mBinding.progressMessage.visibility = View.VISIBLE
//            mBinding.progressMessage.setText(R.string.transactions_found)
//            getUnpaidTransactionDetailsFromFusion()
//        }
//    }
//    private fun getUnpaidTransactionDetailsFromFusion() {
//        for (transactionSequence in transactionSequenceList) {
//            val trxSequenceNo: String = transactionSequence.transactionSeqNo.toString() + ""
//            val mPumpNumber: String = transactionSequence.pumpNo.toString() + ""
//            val params = FuelSaleTrxPrams(mPumpNumber, trxSequenceNo)
//            log(TAG, "Sending Command for Pump: $mPumpNumber SeqNo: $trxSequenceNo")
//            FusionService.sendFuelTrxDetailMsg(params, applicationContext)
//        }
//    }
//    private fun gotoOfflineModePage() {
//        if (stationMode != 3) {
//            if (mReponseFusion?.error != null) showToast( mReponseFusion.error
//            ) else showToast(resources.getString(R.string.offline))
//        } else {
//            showToast( resources.getString(R.string.no_transaction_found))
//        }
//        // go directly to offline mode, no need to put a popup
//        val intent = Intent(applicationContext, OfflineTransactionListActivity::class.java)
//       intentExtrasModel!!.stationMode = OFFLINE_TRX_MODE
//        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
//        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
//        applicationContext.startActivity(intent)
//      //  finish()
//    }
//    private fun searchTransactionsInFusion() {
//        showProgress(true)
//        mBinding.progressMessage.visibility = View.VISIBLE
//        mBinding.progressMessage.setText(R.string.searching_transactions)
//        mTerminalDAO = TerminalDao()
//        mTerminalDAO!!.open()
//        mTerminal = mTerminalDAO!!.getCurrent()
//        mTerminalDAO!!.close()
//        log(TAG, "----------- Mode ---------  $stationMode || FUEL_POS_EXIST  : " + prefs.getFuelPosModel()!!.isExist)
//        if (prefs.getFuelPosModel()!!.isExist && stationMode == 3) { // hardcode
//            try {
//                mFuelPosDAO = FuelPosDao()
//                mFuelPosDAO!!.open()
//                mFuelPOS = mFuelPosDAO!!.getCurrent()
//                mFuelPosDAO!!.close()
//                mTransactionsPOSDAO = TransactionFromFuelPosDao()
//                mTransactionsPOSDAO!!.open()
//                transactionList.addAll(mTransactionsPOSDAO!!.getTrxByFlag(30, 0, intentExtrasModel!!.mPumpNumber!!, intentExtrasModel!!.articleID!!)!!)
//                mTransactionsPOSDAO!!.close()
//                fuelTransactionStatusDAO = FuelTransactionStatusDao()
//                fuelTransactionStatusDAO!!.open()
//                for (tranFusion in transactionList) {
//                    if (!(fuelTransactionStatusDAO!!.isTransactionDone(tranFusion.ref_transaction!!))!!) {
//                        transactionList.add(tranFusion)
//                    }
//                }
//                fuelTransactionStatusDAO!!.close()
//
//            } catch (Ex: SQLException) {
//                Ex.printStackTrace()
//                log(TAG,"----------- After Transactions SQL EXception --------- :" + Ex.message)
//                log(TAG, Ex.message+ ExceptionUtils.getStackTrace(Ex))
//                //mViewModel.generateLogs(Ex.message!!,0)
//            }
//        } else {
//           // FusionService.getProductTable() // get list of available products in  fusion
//            log(TAG,"sending ifsf command to get latest transactions from fusion")
//            FusionService.getDSPConfigurationList()
//            Timer().schedule(object : TimerTask() {
//                override fun run() {
//                    FusionService.getAvailableTransactionList("*")
//                }
//            }, 3000)
//        }
//    }
//    private fun addFuelTransactionToList(serviceResponseGFST: ServiceResponseGFST) {
//        val currentTransaction: DeviceClassFST = serviceResponseGFST.serviceResponse.fDCdata.deviceClass
//        val timeStamp: String = serviceResponseGFST.serviceResponse.fDCdata.fDCTimeStamp
//        val fusionTransaction = TransactionFromFcc()
//        fusionTransaction.amount = currentTransaction.amount
//        fusionTransaction.dh_transaction=timeStamp.replace("T", " ")
//        fusionTransaction.hose=currentTransaction.nozzleNo.toInt()
//        fusionTransaction.pompiste=""
//        fusionTransaction.produit=getFusionProductName(currentTransaction.productNo.toInt())
//        fusionTransaction.pu=currentTransaction.unitPrice
//        fusionTransaction.pump=currentTransaction.pumpNo
//        fusionTransaction.quantite=currentTransaction.volume
//        fusionTransaction.ref_transaction=currentTransaction.transactionSeqNo.toString() + ""
//        fusionTransaction.rfid=""
//        fusionTransaction.currency=prefs.currency
//        fusionTransaction.hexColor = getFusionProductColor(currentTransaction.productNo.toInt())
//        fusionTransaction.productId = currentTransaction.productNo
//        fusionFuelTrxList.add(fusionTransaction)
//        mBinding.progressMessage.visibility = View.VISIBLE
//        mBinding.progressMessage.setText(R.string.loading_transactions)
//       // onEnableTouchEvents()
//        if (fusionFuelTrxList.size == transactionSequenceList.size) {
//         //   onEnableTouchEvents()
//            addTransactionsInDB()
//        }
//    }
//    inner class SearchTransactionUsingTcp : CoroutineAsyncTask<Void, Void, Boolean>() {
//        override fun onPreExecute() {
//            super.onPreExecute()
//            showProgress(true)
//        }
//
//        override fun doInBackground(vararg params: Void): Boolean {
//            val fusionUrl = Support.splitIpAndPortFromUrl(fusion.API)
//            val fusionAddress = fusionUrl[0]
//            try {
//                val socket = Socket(fusionAddress, 3456)
//                var response = ""
//                var x: Int
//                val outputStream = socket.getOutputStream()
//                val outputStreamWriter = OutputStreamWriter(outputStream)
//                outputStreamWriter.write("OP_ALLCARBURANT#20")
//                //outputStreamWriter.write("OP_CARBURANT");
//                outputStreamWriter.flush()
//                log(TAG,"TCP message sent success")
//                val inputStream = socket.getInputStream()
//                val bufferedReader = BufferedReader(InputStreamReader(inputStream))
//                val byteArray = java.util.ArrayList<Byte>()
//
//                while (true) {
//                    x = bufferedReader.read()
//                    if (x == -1) break
//                    byteArray.add(x.toByte())
//                }
//                response = String(Support.byteListToByteArray(byteArray)) //byteArray
//                log(TAG, "#### $response")
//
//                if (response != null) {
//                    val (transactions) = Gson().fromJson(
//                        response,
//                        UnsignedFusionTransactions::class.java
//                    )
//
//                    val connectedPumps: List<RFIDPumpsModel> = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
//                    if (connectedPumps.isNotEmpty() && Objects.requireNonNull(transactions).isNotEmpty()
//                    ) {
//                        for (pump in connectedPumps) {
//                            for (transaction in transactions) {
//                                log(TAG, "TERMINAL PUMP: " + transaction.pump + " TRX PUMP: " + pump.pump_number)
//                                if (transaction.pump == "" + pump.pump_number) {
//                                    log(TAG, transaction.pump + " = " + pump.pump_number)
//                                    //todo ad transaction in list
//                                  //  addFccTransactionToList(transaction)
//                                    val fusionTransaction = TransactionFromFcc()
//                                    fusionTransaction.amount = transaction.amount.toDouble()
//                                    fusionTransaction.dh_transaction = transaction.dhTransaction.replace("T", " ")
//                                    fusionTransaction.hose = transaction.hose.toInt()
//                                    fusionTransaction.pompiste = transaction.pompiste
//                                    fusionTransaction.produit = transaction.produit
//                                    fusionTransaction.pu = transaction.pu.toDouble()
//                                    fusionTransaction.pump = transaction.pump.toInt()
//                                    fusionTransaction.quantite = transaction.quantite.toDouble()
//                                    fusionTransaction.ref_transaction = transaction.trxSeqNr.toString() + ""
//                                    fusionTransaction.rfid = ""
//                                    fusionTransaction.productId = transaction.produitId!!.toInt()
//                                    //fusionTransaction.setProductId("1"); //added for testing
//                                    fusionFuelTrxList.add(fusionTransaction)
//                                    Log.i(TAG, "Fuel Trx added in list: " + fusionTransaction.ref_transaction)
//                                    showProgress(true)
////                                    mBinding.progressMessage.text = getString(R.string.loading_transactions)
//                                } else {
//                                    log(TAG, transaction.pump + " = " + pump.pump_number)
//                                }
//                            }
//                        }
//                        if (transactions.isNotEmpty()) {
//                            if (fusionFuelTrxList.isNotEmpty()) {
//                                log(
//                                    TAG,
//                                    "All Trx Added in List"
//                                )
//                                addFccTransactionInDB()
//                            }
//                        }
//                    } else {
//                        showToast( resources.getString(R.string.no_transaction_found))
//                    }
//                } else {
//                    gotoAbortMessageActivity(resources.getString(R.string.error),getString(R.string.pump_details_not_found))
//                   // finish()
//                }
//            } catch (e:Exception) {
//                e.printStackTrace()
//                log(TAG, "Exception : " + e.message + " --- " + e.cause)
//                showToast(getString(R.string.failed_to_connect_fcc))
//              //  mViewModel.restartFusionTcpService("${fusion.API}${fusion.TCP_SERVICE_URL}")
//                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//              //  mViewModel.generateLogs(e.message!!,0)
//            }
//            return false
//        }
//    }
//    //endregion
//    private fun showProgress(isVisible: Boolean) {
//        if (isVisible) {
//            mBinding.progressBarLayout.visibility = View.VISIBLE
//            mBinding.listViewLayout.visibility = View.GONE
//         //   onDisableTouchEvents()
//        } else {
//            mBinding.progressBarLayout.visibility = View.GONE
//            mBinding.listViewLayout.visibility = View.VISIBLE
//
//         //   onEnableTouchEvents()
//        }
//    }
//    private fun getFusionProductName(productNo: Int): String {
//        if (fusionProductList.isNotEmpty()) {
//            for (product in fusionProductList) {
//
//                if (productNo == product.productNo) {
//                    return product.productName
//                }
//            }
//        }
//        return ""
//    }
//
//
//    private lateinit var dspConfiguration: DSPConfiguration
//    private var productColors: ArrayList<ProductDSP> = ArrayList()
//    private fun getFusionProductColor(productNo: Int): String {
//        for (product in productColors) {
//            if (productNo == product.productNo) {
//                return "#${product.productColour}"
//            }
//        }
//        return "#FF8212"
//    }
//
//    private fun addTransactionsInDB() {
//        mBinding.progressMessage.text = ""
//        mBinding.progressMessage.visibility = View.VISIBLE
//        if (fusionFuelTrxList.isNotEmpty()) {
//            transactionList.clear()
//            fuelTransactionStatusDAO = FuelTransactionStatusDao()
//            fuelTransactionStatusDAO!!.open()
//            var size = fusionFuelTrxList.size
//            if (fusionFuelTrxList.size > 20) size = 20
//            for (i in 0 until size) {
//                if (!fuelTransactionStatusDAO!!.isTransactionDone(fusionFuelTrxList[i].ref_transaction!!)!!) {
//                    transactionList.add(fusionFuelTrxList[i])
//                }
//            }
//            fuelTransactionStatusDAO!!.close()
//            showTransactionsInRecyclerView()
//        }
//    }
//    private fun showTransactionsInRecyclerView() {
//        showProgress(false)
//        if (transactionList.isNotEmpty()) {
//          //  mBinding.mListView.removeAllViews()
//            Collections.sort(transactionList, TransactionFromFccComparator())
//            mOperationsAdapter = RecyclerViewArrayAdapter(transactionList,this)
//            mBinding.mListView.adapter = mOperationsAdapter
//            mOperationsAdapter.notifyDataSetChanged()
//            clearFccFuelTrxList()
//        } else {
//            UtilsCardInfo.beep(mCore, 10)
//            if (mReponseFusion?.error != null) {
//                showDialog("No Result",mReponseFusion.error)
//            } else {
//                showSnakeBar(getString(R.string.bo_fusion_transaction))
//                showNoTrxLayout(true)
////                gotoOfflineModePage()
//            }
//        }
//    }
//
//    private fun clearFccFuelTrxList() {
//        fusionFuelTrxList.clear()
//        mBinding.progressMessage.visibility = View.GONE
//    }
//
//    override fun setObserver() {
//
//    }
//
//    override fun onItemClick(view: View, model: TransactionFromFcc) {
//        MultiClickPreventer.preventMultiClick(view)
//        if(view.id == R.id.transactionListLayout) {
//            UtilsCardInfo.beep(mCore, 10)
//            transactionToPay = model
//
//            val transactionStatusModel = TransactionFromFcc()
//            transactionStatusModel.ref_transaction = transactionToPay!!.ref_transaction
//            transactionStatusModel.pump = transactionToPay!!.pump
//            transactionStatusModel.payment_status = 1
//            prefs.saveTransactionModel(transactionStatusModel)
//           log("" + " : ", prefs.getTransactionModel()!!.ref_transaction!!)
//
//            if (transactionToPay != null) {
//                carburant = transactionToPay!!.produit
//                volume = transactionToPay!!.quantite.toString()
//                montant = montant+ " " + prefs.getStringSharedPreferences(AppConstant.MODE_CURRENCY)
//
//                if(fuelVat.enabled){
//                    showTaxAmountDialog(carburant!!,volume!!, transactionToPay!!.amount.toString())
//                }
//                else {
//                    showPaymentDialog(carburant!!,volume!!, transactionToPay!!.amount.toString())
//                }
//
//            } else {
//                showToast(resources.getString(R.string.no_trx_selected))
//            }
//        }
//    }
//
//    private fun showPaymentDialog(productName:String, qty: String, amount:String){
//        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)
//        builder.setMessage(
//            Html.fromHtml(
//                "<font color='#000000'>" + resources.getString(R.string.validate_transaction) + "</font>" +
//                        "<br/><br/>"
//                        + resources.getString(R.string.label_carburant) + " : <strong>" + productName + "</strong>" +
//                        "<br/>" +
//                        "Volume : <strong>" + qty + "</strong>" +
//                        "<br/>" +
//                        resources.getString(R.string.amount_pay) + " : <strong>" + amount + "</strong>"
//            )
//        )
//        builder.setCancelable(false)
//        builder.setNegativeButton(
//            resources.getString(R.string.no)
//        ) { dialog, which ->
//            UtilsCardInfo.beep(mCore, 10)
//            dialog.dismiss()
//        }
//        builder.setPositiveButton(
//            resources.getString(R.string.yes)
//        ) { dialog, which ->
//            UtilsCardInfo.beep(mCore, 10)
//            dialog.dismiss()
//            getProductDetails(transactionToPay!!.productId!!)
//        }
//        val alert = builder.create()
//        alert.show()
//        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
//        nbutton.setTextColor(
//            ContextCompat.getColor(this, R.color.redLight)
//        )
//        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
//        nbutton.textSize = 20f
//        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
//        pbutton.setTextColor(
//            ContextCompat.getColor(this, R.color.greenLight)
//        )
//        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
//        pbutton.textSize = 20f
//    }
//    var taxModel: TaxModel? = null
//    private fun showTaxAmountDialog(productName:String, qty: String, amount:String) {
//        val builder = AlertDialog.Builder(this, R.style.MyStyleDialog)
//
//        var isInclusive = true
//        var type = "Incl."
//
//        val vatDecimal = try { referenceModel!!.terminalConfig!!.receiptSetting!!.vatDecimal!! } catch (e:Exception) { 2 }
//        taxModel = if(fuelVat.enabled) {
//            isInclusive = fuelVat.type == 0
//            type = if(isInclusive) "Incl." else  "Excl."
//            TaxUtils.calculate(amount.toDouble(),fuelVat.percentage!!.toDouble(),isInclusive)
//        } else /*if(shopVat.enabled)*/ {
//            isInclusive = shopVat.type == 0
//            type = if(isInclusive) "Incl." else  "Excl."
//            TaxUtils.calculate(amount.toDouble(),shopVat.percentage!!.toDouble(),isInclusive)
//        }
//
//        builder.setMessage(Html.fromHtml(("<font color='#000000'>" + resources.getString(R.string.validate_transaction) + "</font><br/><br/>" +
//                getString(R.string.label_carburant) + " : <strong>" + productName + "</strong><br/>" +
//                getString(R.string.qty) + " : <strong>" + "$qty $fuelQtyUnit" + "</strong><br/><br/>" +
//                getString(R.string.net_amount) + " : <strong>" + if(isFrench) taxModel!!.netAmount.decimal(vatDecimal).replace(".",",") else taxModel!!.netAmount.decimal(vatDecimal) + " " + prefs.currency + "</strong>" + "<br/>" +
//                getString(R.string.tax_amount) + " (${taxModel!!.taxPercentile}% $type)" + " : <strong>" + if(isFrench) taxModel!!.taxAmount.decimal(vatDecimal).replace(".",",") else taxModel!!.taxAmount.decimal(vatDecimal) + " " + prefs.currency + "</strong>" + "<br/><br/>" +
//                getString(R.string.total_amount) + " : <strong>" + if(isFrench) taxModel!!.totalAmount.decimal(vatDecimal).replace(".",",") else taxModel!!.totalAmount.decimal(vatDecimal) + " " + prefs.currency + "</strong>"
//                )))
//        builder.setCancelable(false)
//        builder.setNegativeButton(resources.getString(R.string.no)) { dialog, which ->
//            UtilsCardInfo.beep(mCore, 10)
//            dialog.dismiss()
//        }
//        builder.setPositiveButton(resources.getString(R.string.yes)) { dialog, which ->
//            UtilsCardInfo.beep(mCore, 10)
//            dialog.dismiss()
//            intentExtrasModel!!.taxModel = taxModel
//            getProductDetails(transactionToPay!!.productId!!)
//        }
//        val alert = builder.create()
//        alert.show()
//        val nbutton = alert.getButton(DialogInterface.BUTTON_NEGATIVE)
//        nbutton.setTextColor(ContextCompat.getColor(this, R.color.redLight))
//        nbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
//        nbutton.textSize = 20f
//        val pbutton = alert.getButton(DialogInterface.BUTTON_POSITIVE)
//        pbutton.setTextColor(ContextCompat.getColor(this, R.color.greenLight))
//        pbutton.typeface = Typeface.defaultFromStyle(Typeface.BOLD)
//        pbutton.textSize = 20f
//    }
//
//    //endregion
//    override fun onClick(view: View) {
//        MultiClickPreventer.preventMultiClick(view)
//        UtilsCardInfo.beep(mCore, 10)
//        when (view.id) {
//            R.id.toolbar_right_image -> {
//                refreshTransactions()
//            }
//        }
//    }
//
//    private fun refreshTransactions() {
//        transactionList.clear()
//
////        Handler(Looper.getMainLooper()).postDelayed({
////            mBinding.toolbarTransactionList.toolbarRightImage.isEnabled = true
////        }, 5000)
//
//        showProgress(true)
//        mBinding.progressMessage.text = getString(R.string.fetching_latest_transactions)
//        if (intentExtrasModel!!.stationMode == AppConstant.AFTER_TRX_MODE && fuelpos.isExist) {
//           // startFuelPos()
//            startFuelPosReceiver()
//        } else {
//            val type: Int = fusion.COMM_TYPE
//            if (type == 1) {
//                searchTransactionsInFusion()
//            } else if (type == 2) {
//                SearchTransactionUsingTcp().execute()
//            }
//        }
//    }
//
//    fun goToPayment(selectedProduct: ProductModel?) {
//        val intent1 = Intent(this, ModePayActivity::class.java) // commented
//        intentExtrasModel!!.mTransaction=mTransaction
//        intentExtrasModel!!.selectedProduct=selectedProduct
//        log(TAG,"mTransaction:: "+gson.toJson(mTransaction))
//        intent1.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
//        log(TAG,"isLoyalty:: "+intentExtrasModel!!.loyaltyTrx)
//        startActivity(intent1)
//    }
//
//    private fun stopIfsfReceiver() {
//        try {
//            unregisterReceiver(ifsfRecever)
//        } catch (e: Exception) {
//            e.printStackTrace()
//            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//         //   mViewModel.generateLogs(e.message!!,0)
//        }
//       log(TAG, "IFSF RECEIVER STOPPED")
//    }
//    private fun getProductDetails(productChosen:Int) {
//        try {
//            mProduitDAO = ProductsDao()
//            mProduitDAO!!.open()
//            selectedProduct = mProduitDAO!!.getProductByFCCId(productChosen)
//           log(TAG, "productChosen::: $productChosen")
//           log(TAG,"getAllProducts ::: "+mProduitDAO!!.getAllProducts())
//           log(TAG, "selectedProduct ::: $selectedProduct")
//            mProduitDAO!!.close()
//            getTransactionDetails()
//        } catch (ex: SQLiteException) {
//            daoError = true
//            performNextStepProductTask(false)
//            ex.printStackTrace()
//            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
//          //  mViewModel.generateLogs(ex.message!!,0)
//        }
//    }
//    private fun getTransactionDetails() {
//        if (selectedProduct != null) {
//
//            if (stationMode == 3 && prefs.getFuelPosModel()!!.isExist) {
//                try {
//                    mTransactionsPOSDAO = TransactionFromFuelPosDao()
//                    mTransactionsPOSDAO!!.open()
//                    mTransactionsPOSDAO!!.updateFlagTrxByRef(transactionToPay!!.ref_transaction!!, 1)
//                    mTransactionsPOSDAO!!.close()
//
//                } catch (E: Exception) {
//                    performNextStepProductTask(false)
//                    log(TAG, E.message+ ExceptionUtils.getStackTrace(E))
//                   // mViewModel.generateLogs(E.message!!,0)
//                }
//            }
//            saveTransactionDetails()
//
//        } else {
//            showDialog(getString(R.string.product_mismatch),getString(R.string.this_product_not_available_on_server))
//           log(TAG, "### ### ### product => null")
//        }
//    }
//
//    private fun saveTransactionDetails() {
//        try {
//            mTransactionDAO = TransactionDao()
//            mTransactionDAO!!.open()
//            mTransaction = mTransactionDAO!!.checkAvailableTransactions(transactionToPay!!.pump.toString(),transactionToPay!!.ref_transaction!!)!!
//
//            if(mTransaction != null && mTransaction!!.reference != null)
//            {
//                if(taxModel != null)
//                {
//                    mTransaction!!.vatAmount = taxModel!!.taxAmount.toString()
//                    mTransaction!!.netAmount = taxModel!!.netAmount.toString()
//                    mTransaction!!.amount =  taxModel!!.totalAmount
//                    if(fuelVat.enabled || shopVat.enabled)
//                    {
//                        mTransaction!!.vatPercentage =prefs.getReferenceModel()!!.fuelVat!!.percentage
//                        mTransaction!!.vatType =prefs.getReferenceModel()!!.fuelVat!!.type
//                    }
//                }
//                mTransaction!!.fccProductId = selectedProduct!!.fcc_prod_id.toString()
//                mTransaction!!.categoryId =intentExtrasModel!!.categoryId
//                mTransaction!!.idTerminal = prefs.getReferenceModel()!!.terminal!!.terminalId
//                mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
//                mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
//                mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
//                mTransactionDAO!!.updateTransactionsByReferenceID(mTransaction!!)
//            }
//            else
//            {
//                mTransaction = TransactionModel()
//                if(taxModel != null)
//                {
//                    mTransaction!!.vatAmount = taxModel!!.taxAmount.toString()
//                    mTransaction!!.netAmount = taxModel!!.netAmount.toString()
//                    mTransaction!!.amount =  taxModel!!.totalAmount
//                    if(fuelVat.enabled || shopVat.enabled)
//                    {
//                        mTransaction!!.vatPercentage =prefs.getReferenceModel()!!.fuelVat!!.percentage
//                        mTransaction!!.vatType =prefs.getReferenceModel()!!.fuelVat!!.type
//                    }
//                }
//                mTransaction!!.categoryId =intentExtrasModel!!.categoryId
//                mTransaction!!.idTerminal = prefs.getReferenceModel()!!.terminal!!.terminalId
//                mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
//                mTransaction!!.idProduit = selectedProduct!!.productID
//                mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
//                mTransaction!!.idPompiste = prefs.getPompisteId(mTransaction!!.codePompiste.toString())
//                mTransaction!!.dateTransaction = transactionToPay!!.dh_transaction
//                mTransaction!!.amount =  transactionToPay!!.amount
//                mTransaction!!.quantite = transactionToPay!!.quantite
//                mTransaction!!.unitPrice = transactionToPay!!.pu
//                mTransaction!!.flagController = 1
//                mTransaction!!.sequenceController = transactionToPay!!.ref_transaction
//                mTransaction!!.kilometrage = ""
//                mTransaction!!.pan = intentExtrasModel!!.panNumber
//                mTransaction!!.flagTelecollecte = 0
//                mTransaction!!.pumpId = transactionToPay!!.pump.toString()
//                mTransaction!!.transactionStatus = 0
//                mTransaction!!.productName = selectedProduct!!.libelle
//                mTransaction!!.reference = "TRX" + Support.generateReference(this@TransactionListActivity)/*Support.generateReference(mTransaction!!.dateTransaction, "", "", this@TransactionListActivity)*/
//                mTransaction!!.fccProductId = selectedProduct!!.fcc_prod_id.toString()
//                intentExtrasModel!!.mTransaction = mTransaction
//               insertTransactionData(mTransaction!!)
//            }
//            log(TAG, "mTransaction !!! --> " + gson.toJson(mTransaction))
//            mTransactionDAO!!.close()
//            performNextStepProductTask(true)
//        } catch (ex: SQLiteException) {
//            ex.printStackTrace()
//            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
//            // mViewModel.generateLogs(ex.message!!,0)
//        }
//    }
//    private fun performNextStepProductTask(isTrue:Boolean) {
//        when {
//            isTrue -> {
//                goToPayment(selectedProduct)
//            }
//            daoError -> {
//                UtilsCardInfo.beep(mCore, 10)
//                showDialog(resources.getString(R.string.error),getString(R.string.data_error))
//            }
//            else -> {
//                showDialog(resources.getString(R.string.error), resources.getString(R.string.no_product))
//            }
//        }
//        showProgress(false)
//    }
//
//
//
//    //region fuelpos
//    private lateinit var fuelPosTcpClient: FuelPosTcpClient
////    private fun startFuelPos() {
////        fuelPosTcpClient = FuelPosTcpClient(fuelpos.ipAddress!!, 7501, posMessageListener, true, 10)
////        fuelPosTcpClient.connect()
////        if (transactionList.isEmpty())
////            showProgress(true)
////        else
////            mBinding.progressMessage.text = getString(R.string.fetching_latest_transactions)
////    }
////    private fun stopFuelPos() {
////        fuelPosTcpClient.disconnect()
////    }
//    private fun startFuelPosReceiver() {
//        if (!FuelPosService.isRunning(this)) FuelPosService.start(this,)
//        val filter = IntentFilter()
//        filter.addAction(ACTION_FUEL_POS_REPLY)
//        filter.addAction(ACTION_FUEL_POS_MESSAGE)
//        filter.addAction(ACTION_FUEL_POS_CLIENT_STATE)
//        filter.addAction(ACTION_POS_MESSAGE)
//        registerReceiver(fuelPosReceiver, filter)
//        log(TAG, "FuelPOS receiver started")
//
//        FuelPosService.startExtPosClient()
//    }
//    private fun stopFuelPosReceiver() {
//        try {
//            unregisterReceiver(fuelPosReceiver)
//            log(TAG, "FuelPos receiver stopped")
//            FuelPosService.stopExtPosClient()
//        } catch (e: java.lang.Exception) {
//            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//          //  mViewModel.generateLogs(e.message!!,0)
//        }
//    }
//    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
//        override fun onReceive(context: Context?, intent: Intent) {
//            val action = intent.action
//           log(TAG, "FUELPOS ACTION $action")
//            var message: String? = ""
//            when (action) {
//                ACTION_FUEL_POS_REPLY -> {
//                    message = intent.getStringExtra(FUEL_POS_REPLY)
//                    log(TAG, "POS MESSAGE: $message")
//                }
//                ACTION_FUEL_POS_CLIENT_STATE ->{
//                    message = intent.getStringExtra(FUEL_POS_CLIENT_STATE)
//                    if(message == "TIMEOUT" || (message!=null && message.contains("TIMEOUT"))){
//                        showToast(getString(R.string.unable_to_connect_fcc)+"CONNECTION TIMEOUT")
//                        showNoTrxLayout(true)
//                        mBinding.tvFccConnectionMessage.text = getString(R.string.unable_to_connect_fcc)+" CONNECTION TIMEOUT"
//                    }
//                }
//                ACTION_POS_MESSAGE -> {
//                    message = intent.getStringExtra(POS_MESSAGE)
//                    log(TAG, "POS MESSAGE: $message")
//                    if (message!!.contains(FuelPosReply.FILLING_STATES)) {
////                        stopFuelPos()
//                            stopFuelPosReceiver()
//                        val transactions: java.util.ArrayList<PosTransaction> = FuelPosReply.getPosTransactions(message)
//                        log(TAG, "POS TRX: " + Gson().toJson(transactions))
//                        if (transactions.isEmpty()) {
//                            showNoTrxLayout(true)
//                            mBinding.progressMessage.text =  getString(R.string.fetching_latest_transactions)
//                        } else {
//
//                            decimal = fuelpos.totalAmountDecimal ?: fuelpos.decimal
//                            if (BuildConfig.DEBUG) decimal = 2
//                            val pumpsModels: List<RFIDPumpsModel> = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
//                            if (pumpsModels != null && pumpsModels.isNotEmpty()) {
//                                for (transaction in transactions) {
//                                    if (transaction.fillingState == PosFillingState.PAYABLE || transaction.fillingState == PosFillingState.PAYABLE_AGAIN){
//                                        for (pump in pumpsModels) {
//                                            //if (transaction.pumpNumber == "3" || transaction.pumpNumber == "4" ) { //testing purpose
//                                            if (transaction.pumpNumber == pump.pump_number.toString()) {
//                                                addFuelPosTransactionToList(transaction)
//                                            }
//                                        }
//                                    }
//                                }
//                                addFccTransactionInDB()
//                            } else {
//                                gotoAbortMessageActivity(resources.getString(R.string.error),getString(R.string.pump_not_assigned_terminal))
//                            }
//                        }
//                    }
//                    else if (message.contains(FuelPosReply.LOGIN_RESULT)){
//                        val result = FuelPosReply.getLoginResult(message)
//                        if(result!!.completionCode != PosCompletionCode.OK){
//                            val loginStatus = PosCompletionCode.getMessage(result.completionCode)
//                            showToast("${result.completionCode} : $loginStatus")
//                            showNoTrxLayout(true)
//                            mBinding.tvFccConnectionMessage.text = "${result.completionCode} : $loginStatus"
//                        }
//                    }
//                }
//                ACTION_FUEL_POS_MESSAGE -> {
//                    message = intent.getStringExtra(FUEL_POS_MESSAGE)
//                    log(TAG, "FUEL_POS_MESSAGE: $message")
//                }
//
//            }
//        }
//    }
//
////    private val posMessageListener = object : FuelPosTcpClient.FuelPosClientListener {
////        override fun onHeartbeat() {
////            fuelPosTcpClient.send(FuelPosCommands.posHeartbeat())
////        }
////
////        override fun onConnectionStateChanged(state: ConnectionState) {
////            if (state === ConnectionState.CONNECTED) {
////                val message = FuelPosCommands.posLogOn(fuelpos.userExt!!, fuelpos.passExt!!)
////                fuelPosTcpClient.send(message)
////            }
////        }
////
////        override fun onError(error: ConnectionError) {
////
////        }
////
////        override fun onDataSend() {
////
////        }
////
////        override fun onDataReceived(message: String, bytes: ByteArray) {
////
////        }
////
////    }
//
//    private fun addFuelPosTransactionToList(transaction: PosTransaction) {
//        try {
//            mProduitDAO = ProductsDao()
//            mProduitDAO!!.open()
//            //val product: ProductModel? = mProduitDAO!!.getProductDetailsByID(transaction.fillingFuelNumber) //product id and filling fuel number should be same in both fuel pos and fbs for product
//            val product: ProductModel? = mProduitDAO!!.getProductById(transaction.fillingFuelNumber.toInt()) //product id and filling fuel number should be same in both fuel pos and fbs for product
//            mProduitDAO!!.close()
//            try {
//                mTerminalDAO = TerminalDao()
//                mTerminalDAO!!.open()
//                mTerminal = mTerminalDAO!!.getCurrent()
//                mTerminalDAO!!.close()
//            } catch (ex: SQLiteException) {
//                log(TAG, ex.message!!)
//            }
//            if (product != null) {
//
//                val totalDecimal = fuelpos.totalAmountDecimal ?: 2
//                val qtyDecimal = fuelpos.quantityDecimal ?: 2
//                val uniteDecimal = fuelpos.unitPriceDecimal ?: 2
//
//                val fuelPosTransaction = TransactionFromFcc()
//                fuelPosTransaction.amount = toDecimalValue(transaction.fillingAmount, totalDecimal)
//                fuelPosTransaction.dh_transaction = Support.getFormatTrxDate(Date())
//                var nozzle = 0
//                try {
//                    nozzle = transaction.nozzleNumber.toInt()
//                } catch (e: Exception) {
//                    e.printStackTrace()
//                    log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//                  //  mViewModel.generateLogs(e.message!!,0)
//                }
//                fuelPosTransaction.hose = nozzle
//                fuelPosTransaction.pompiste = ""
//                fuelPosTransaction.produit = product.libelle
//                fuelPosTransaction.pu = toDecimalValue(transaction.unitPrice, uniteDecimal)
//                fuelPosTransaction.pump = transaction.pumpNumber.toInt()
//                fuelPosTransaction.quantite = toDecimalValue(transaction.fillingVolume, qtyDecimal)
//                val referenceTransaction: String =  transaction.pumpNumber + ":" +transaction.fillingId + ":"+ transaction.trxChecksum.toString() + /*":" +*/ + mTerminal!!.terminalId + mTerminal!!.stationId /*+ ":" + transaction.getMessageSequence()*/
//                fuelPosTransaction.ref_transaction = referenceTransaction
//                fuelPosTransaction.rfid = ""
//                fuelPosTransaction.currency = prefs.currency
//                fuelPosTransaction.productId = transaction.fillingFuelNumber.toInt()
//                fusionFuelTrxList.add(fuelPosTransaction)
//            } else {
//                log(TAG, "Product not found in product table ")
//            }
//            mBinding.progressBarLayout.visibility = View.VISIBLE
//        } catch (e: Exception) {
//            e.printStackTrace()
//            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
//           // mViewModel.generateLogs(e.message!!,0)
//        }
//    }
//    //endregion
//
//    override fun onDestroy() {
//        if(fusion.EXIST)
//            stopIfsfReceiver()
//        super.onDestroy()
//    }
//    override fun onBackPressed() {
//
//    }
//
//    private fun showNoTrxLayout(isVisible: Boolean, title:String ="") {
//        mBinding.tvFccConnectionMessage.text = getString(R.string.no_transaction_found)
//        if (isVisible) {
//            mBinding.noTransactionLayout.visibility = View.VISIBLE
//        } else {
//            //fccConnectionProgressBar(false);
//            mBinding.noTransactionLayout.visibility = View.GONE
//            if(title.isNotEmpty() ){
//                mBinding.tvFccConnectionMessage.text = title.replace(":","")
//            }
//        }
//        //Support.setNavigationStatus(this, false);
//    }
//
//    private fun addFccTransactionInDB() {
//        try {
//            if (fusionFuelTrxList.isNotEmpty()) {
//                Handler(Looper.getMainLooper()).post {
//                mBinding.progressMessage.text = ""
//                mBinding.progressMessage.visibility = View.VISIBLE
//                Log.i(TAG, "fusionFuelTrxList:: " + Gson().toJson(fusionFuelTrxList))
//                    transactionList.clear()
//                    val fccTransactionStatusDao = FuelTransactionStatusDao()
//                    fccTransactionStatusDao.open()
//                    val transactionCount = fccTransactionStatusDao.getTransactionCount()
//                    val size: Int = fusionFuelTrxList.size
//                    if (transactionCount == 0) {
//                        fccTransactionStatusDao.insertAllTransactions(fusionFuelTrxList)
//                    } else {
//                        if (size != 0) {
//                            for (trxF in fusionFuelTrxList) {
//                                if (!fccTransactionStatusDao.isTransactionDone(trxF.ref_transaction!!)!!) {
//                                    fccTransactionStatusDao.insert(trxF)
//                                }
//                            }
//                        }
//                    }
//                    fccTransactionStatusDao.close()
//                    transactionList = fccTransactionStatusDao.getAllTransactionList()
//
//                       if (transactionList.isNotEmpty()) { //commented after face issue on loading transactions on fuel pos
//                            updateRecyclerView()
//                        } else {
//                            showNoTrxLayout(true)
//                        }
//                        showProgress(false)
//               }
//            }
//            else{
//                showNoTrxLayout(true)
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//    fun updateRecyclerView() {
////        mBinding.toolbarTransactionList.toolbarRightImage.isEnabled = true
//        showProgress(false)
//        try {
//            if (transactionList.isNotEmpty()) {
//                Collections.sort(transactionList, TransactionFromFccComparator())
//                mOperationsAdapter = RecyclerViewArrayAdapter( transactionList, this)
//                mBinding.mListView.adapter = mOperationsAdapter
//                mOperationsAdapter.notifyDataSetChanged()
//                clearFccFuelTrxList()
//            } else {
//                showNoTrxLayout(true)
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//
//    private fun addFccTransactionToList(transaction: UnsignedTransaction) {
//        try {
//
//                val fusionTransaction = TransactionFromFcc()
//                fusionTransaction.amount = transaction.amount.toDouble()
//                fusionTransaction.dh_transaction = transaction.dhTransaction.replace("T", " ")
//                fusionTransaction.hose = transaction.hose.toInt()
//                fusionTransaction.pompiste = transaction.pompiste
//                fusionTransaction.produit = transaction.produit
//                fusionTransaction.pu = transaction.pu.toDouble()
//                fusionTransaction.pump = transaction.pump.toInt()
//                fusionTransaction.quantite = transaction.quantite.toDouble()
//                fusionTransaction.ref_transaction = transaction.trxSeqNr.toString() + ""
//                fusionTransaction.rfid = ""
//                fusionTransaction.productId = transaction.produitId!!.toInt()
//                //fusionTransaction.setProductId("1"); //added for testing
//                fusionFuelTrxList.add(fusionTransaction)
//                Log.i(TAG, "Fuel Trx added in list: " + fusionTransaction.ref_transaction)
//                showProgress(true)
//                mBinding.progressMessage.text = getString(R.string.loading_transactions)
//
//        } catch (e: Exception) {
//            e.printStackTrace()
//        }
//    }
//
//    fun noFccConnLayoutButtonClick(view: View) {
//        setBeep()
//        when(view){
//            mBinding.btnRefreshTrx -> {
//                showNoTrxLayout(false)
//                showProgress(true)
//
//                refreshTransactions()
//            }
//            mBinding.btnOfflinePage -> {
//                gotoOfflineModePage()
//            }
//            mBinding.btnHome -> {
//                val mIntent: Intent =
//                    if (prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
//                        Intent(this, UnattendantModePayActivity::class.java)
//                    } else {
//                        Intent(this, MenuActivity::class.java)
//                    }
//
//                startActivity(mIntent)
//               // finish()
//            }
//        }
//    }
//
//}
