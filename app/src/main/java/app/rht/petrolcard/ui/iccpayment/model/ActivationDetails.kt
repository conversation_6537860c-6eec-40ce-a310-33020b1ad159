package app.rht.petrolcard.ui.iccpayment.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
@Keep
data class ActivationDetails(
    @SerializedName("address")
    val address: String,
    @SerializedName("approval")
    val approval: String,
    @SerializedName("categoryId")
    val categoryId: String,
    @SerializedName("city")
    val city: String,
    @SerializedName("dob")
    val dob: String,
    @SerializedName("driverId")
    val driverId: String,
    @SerializedName("driverIdImage")
    val driverIdImage: String,
    @SerializedName("firstName")
    val firstName: String,
    @SerializedName("fiscalHp")
    val fiscalHp: String,
    @SerializedName("fuel")
    val fuel: String,
    @SerializedName("lastName")
    val lastName: String,
    @SerializedName("liecenceNumber")
    val liecenceNumber: String,
    @SerializedName("nfcTag")
    val nfcTag: String,
    @SerializedName("pan")
    val pan: String,
    @SerializedName("placeOfBirth")
    val placeOfBirth: String,
    @SerializedName("plateNumber")
    val plateNumber: String,
    @SerializedName("signedDoc")
    val signedDoc: String,
    @SerializedName("stationId")
    val stationId: Int,
    @SerializedName("telephone")
    val telephone: String,
    @SerializedName("vehicleRegImage")
    val vehicleRegImage: String,
    @SerializedName("vehicleRegNumber")
    val vehicleRegNumber: String
)