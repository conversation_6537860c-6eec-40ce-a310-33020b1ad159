package app.rht.petrolcard.ui.loyalty.utils

import android.app.Activity
import android.app.AlertDialog
import android.app.PendingIntent
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.hardware.usb.UsbDevice
import android.hardware.usb.UsbManager
import android.os.*
import android.util.Log
import android.widget.Toast
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.MainApp.Companion.getPrinter
import app.rht.petrolcard.R
import app.rht.petrolcard.ui.loyalty.utils.async.AsyncEscPosPrint
import app.rht.petrolcard.ui.loyalty.utils.async.AsyncEscPosPrinter
import app.rht.petrolcard.ui.loyalty.utils.async.AsyncUsbEscPosPrint
import app.rht.petrolcard.utils.LogWriter
import app.rht.petrolcard.utils.MyMaterialDialog
import app.rht.petrolcard.utils.MyMaterialDialogListener
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.Support.Companion.log
import app.rht.petrolcard.utils.Support.Companion.processingBitmap
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.im30UsbComm.USBComm
import app.rht.petrolcard.utils.paxutils.modules.printer.PrinterTester
import app.rht.petrolcard.utils.pt102.PT102Utils
import com.afollestad.materialdialogs.MaterialDialog
import com.citizen.sdk.ESCPOSConst
import com.citizen.sdk.ESCPOSPrinter
import com.dantsu.escposprinter.EscPosPrinter
import com.dantsu.escposprinter.connection.DeviceConnection

import com.dantsu.escposprinter.connection.usb.UsbConnection
import com.dantsu.escposprinter.connection.usb.UsbPrintersConnections
import com.dantsu.escposprinter.textparser.PrinterTextParserImg
import com.pax.dal.entity.EFontTypeAscii
import com.pax.dal.entity.EFontTypeExtCode
import com.usdk.apiservice.aidl.printer.*
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.Printer
import java.io.*
import java.lang.ref.WeakReference
import java.util.*


class TicketPrinter(val ctx: Context) {
    private val TAG = TicketPrinter::class.simpleName
    var logWriter = LogWriter("TicketPrinterLogs")
    var printerType = 1
    lateinit var usbComm : USBComm
    var printerStatusMessage =""
    var context: Context = WeakReference(ctx).get()!!

    init {
        printerType = MainApp.getPrefs().getReferenceModel()!!.PRINTER_TYPE!!

    }

    fun printReceipt(bitmap:Bitmap){
        initPrinter()
        if(printerType == AppConstant.DW14_PRINTER){
            printRequestType = 1
            bitmapToPrint = bitmap
            //the bitmap will print once we got successfull USB connection in Broadcast receiver
        } else {
            printReceiptOption1(bitmap)
        }
    }
    fun printReceipt(bitmap:Bitmap,layout:Int){
        initPrinter()
        if(printerType == AppConstant.DW14_PRINTER){
            printRequestType = 2
            bitmapToPrint = bitmap
            layoutToPrint = layout
            //the bitmap will print once we got successfull USB connection in Broadcast receiver
        } else {
            printReceiptOption2(bitmap,layout)
        }
    }
    fun printTicket(commands: ArrayList<PrintCmd>) {
        initPrinter()
        finalDwTextToPrint = ""
        if(printerType == AppConstant.DW14_PRINTER){
            printRequestType = 3
            commandsToPrint = commands
            //the bitmap will print once we got successfull USB connection in Broadcast receiver
        } else {
            printReceiptOption3(commands)
        }
    }

    private fun printReceiptOption1(bitmap: Bitmap){
        if(isPaperAvailable()) {
            println("##### W:${bitmap.width} H:${bitmap.height}")
            printBitmapReceipt(bitmap,1)
            //printBitmap(bitmap,bitmap.width,bitmap.height, AlignmentType.CENTER)
        }
        else {
            Handler(Looper.getMainLooper()).post {
                showPaperDialog(bitmap)
            }

        }
        resetPrinter()
    }
    private fun printReceiptOption2(bitmap: Bitmap,layout: Int){
        if (isPaperAvailable()) {
            println("##### W:${bitmap.width} H:${bitmap.height}")
            printBitmapReceipt(bitmap, layout)
            //printBitmap(bitmap,bitmap.width,bitmap.height, AlignmentType.CENTER)
        } else {
            Handler(Looper.getMainLooper()).post {
                try {
                    showPaperDialog(bitmap)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

        }
        resetPrinter()
    }
    private fun printReceiptOption3(commands: ArrayList<PrintCmd>){
        if (isPaperAvailable()) {
            for (cmd in commands) {
                val text = if (cmd.text != null) cmd.text else ""
                val size = cmd.size
                val bold = cmd.isBold
                val alignment = cmd.alignment
                val contentType = cmd.contentType
                if (contentType == PrintContentType.TEXT) {
                    if (size == 1 && bold)
                        boldText(text, alignment) //To Print Title with Bold
                    else if (size == 1 && !bold)
                        text(text, alignment) // To Print Normal text
                    else if (size == 2 && bold)
                        boldText2(text, alignment) // To Print Normal Bold text
                    else if (size == 2 && !bold)
                        text2(text, alignment)

                } else if (contentType == PrintContentType.IMAGE) {
                    when {
                        cmd.bitmap != null -> {
                            printBitmap(cmd.bitmap, cmd.width, cmd.height, cmd.alignment)
                        }
                        cmd.bitmapArray != null && cmd.bitmapArray.isNotEmpty() -> {
                            printBitmap(cmd.bitmapArray, cmd.width, cmd.height, cmd.alignment)
                        }
                        else -> {
//                        var inputStream: InputStream?
//                        inputStream = FileInputStream(context.filesDir.toString() + File.separator + AppConstant.LOGO_NAME)
//                        var bitmap = BitmapFactory.decodeStream(inputStream)
//                        bitmap = Bitmap.createScaledBitmap(Support.toGrayscale(bitmap), 128, 128, true)
//                        bitmap = processingBitmap(bitmap)
//                        printBitmap(bitmap,128,128,AlignmentType.CENTER)
                            log(TAG, "Bitmap: " + (cmd.bitmap != null))
                        }
                    }
                } else {
                    log(TAG, "Print content type: ${contentType.name}")
                }
            }

            if (printerType == AppConstant.PT102_PRINTER && MainApp.deviceName == "IM30") {
                usbComm.sendUsbSerial(PT102Utils.MORE_SPACE)
                usbComm.sendUsbSerial(PT102Utils.MORE_SPACE)
            }
            if (printerType == AppConstant.DW14_PRINTER && MainApp.deviceName == "IM30") {
                escPosPrinter.printFormattedText(finalDwTextToPrint)
                /*val printer = AsyncEscPosPrinter(escPosUsbConnection, escDPI, escWidthMM, escNoOfCharInLine)
                printer.addTextToPrint(finalDwTextToPrint)
                AsyncUsbEscPosPrint(context, object : AsyncEscPosPrint.OnPrintFinished() {
                    override fun onError(asyncEscPosPrinter: AsyncEscPosPrinter?, codeException: Int) {
                        Log.e("Async.OnPrintFinished", "AsyncEscPosPrint.OnPrintFinished : An error occurred !")
                    }

                    override fun onSuccess(asyncEscPosPrinter: AsyncEscPosPrinter?) {
                        Log.i("Async.OnPrintFinished", "AsyncEscPosPrint.OnPrintFinished : Print is finished !")
                    }
                }
                ).execute(printer)*/
            }
        } else {
            showPaperDialog(commands)
        }
        resetPrinter()
    }

    private fun formatText(inputText: String): String {
        return if (inputText.contains("\n\n")) """
     ${inputText.replace("\n", "")}
     
     
     """.trimIndent() else """
     ${inputText.replace("\n", "")}
     """.trimIndent()
        //else
        //    return inputText;
    }

    //region printer methods
    private fun initPrinter(){
        /*if(BuildConfig.DEBUG)
            printerType = AppConstant.DW14_PRINTER*/

        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                initWPosPrinter()
            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {
                    initA920Printer()
                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        initCitizenPrinter()
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        initPT102Printer()
                    }
                    else if(printerType==AppConstant.DW14_PRINTER){
                        initEscPosPrinter()
                    }
                }
            }
            else -> {
                initLandiPrinter()
            }
        }
    }
    private fun resetPrinter(){
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                resetWPosPrinter()
            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {
                    resetA920Printer()
                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        resetCitizenPrinter()
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        resetPT102Printer()
                    }
                    else if(printerType==AppConstant.DW14_PRINTER){
                        resetEscPosPrinter()
                    }
                }
            }
            else -> {
                resetLandiPrinter()
            }
        }
    }
    private fun getAlignment(type: AlignmentType): Int {
        return when (type) {
            AlignmentType.CENTER -> ESCPOSConst.CMP_ALIGNMENT_CENTER
            AlignmentType.RIGHT -> ESCPOSConst.CMP_ALIGNMENT_RIGHT
            else -> ESCPOSConst.CMP_ALIGNMENT_LEFT
        }
    }
    private fun setPageMargin() {
        //printer.setPageModePrintArea( "40,0,250,0");
    }
    private var finalDwTextToPrint = ""
    private fun text(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                wPosPrinter.printString(getSplitString(text), 25, getWPosAlignment(alignment), false, false)
            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {
                    paxStringNormal(text,getA920Alignment(alignment))
                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment), ESCPOSConst.CMP_FNT_DEFAULT,
                            ESCPOSConst.CMP_TXT_1WIDTH or ESCPOSConst.CMP_TXT_1HEIGHT)
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            text+"\n"
                        } else {
                            text
                        }
                        if(alignment==AlignmentType.CENTER)
                            txt = "     $txt"
                        usbComm.sendUsbSerial(PT102Utils.NORMAL_32_CHAR)
                        usbComm.sendUsbSerial(txt)
                    }
                    else if(printerType==AppConstant.DW14_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            text+"\n"
                        } else {
                            text
                        }
                        txt = when(alignment){
                            AlignmentType.CENTER -> { "[C]$txt" }
                            AlignmentType.LEFT -> { "[L]$txt" }
                            AlignmentType.RIGHT -> { "[R]$txt" }
                        }
                        finalDwTextToPrint += txt
                        //escPosPrinter.printFormattedText(txt)
                    }
                }
            }
            else -> {
                printTextLandi(landiPrinter!!, text, FONT_SIZE_NORMAL, getLandiAlignment(alignment))
            }
        }

    }
    private fun boldText(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                wPosPrinter.printString(getSplitString(text), 25, getWPosAlignment(alignment), true, false)
            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {
                    paxStringBold(text,getA920Alignment(alignment))
                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment), ESCPOSConst.CMP_FNT_BOLD,
                            ESCPOSConst.CMP_TXT_1WIDTH or ESCPOSConst.CMP_TXT_1HEIGHT
                        )
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            text+"\n"
                        } else {
                            text
                        }
                        if(alignment==AlignmentType.CENTER)
                            txt = " $txt"
                        usbComm.sendUsbSerial(PT102Utils.SMALL_WIDE_32_CHAR)
                        usbComm.sendUsbSerial(txt)
                    }
                    else if(printerType == AppConstant.DW14_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            "<b>$text</b>\n"
                        } else {
                            "<b>$text</b>"
                        }
                        txt = when(alignment){
                            AlignmentType.CENTER -> { "[C]$txt" }
                            AlignmentType.LEFT -> { "[L]$txt" }
                            AlignmentType.RIGHT -> { "[R]$txt" }
                        }
                        finalDwTextToPrint += txt
                        //escPosPrinter.printFormattedText(txt)
                    }
                }
            }
            else -> {
                printTextLandi(landiPrinter!!, text, FONT_SIZE_NORMAL, getLandiAlignment(alignment))
            }
        }
    }
    private fun text2(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                wPosPrinter.printString(getSplitString(text), 30, getWPosAlignment(alignment), false, false)
            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {
                    paxStringNormal(text,getA920Alignment(alignment))
                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment),
                            ESCPOSConst.CMP_FNT_DEFAULT,
                            ESCPOSConst.CMP_TXT_2WIDTH or ESCPOSConst.CMP_TXT_2HEIGHT)
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            text+"\n"
                        } else {
                            text
                        }
                        if(alignment==AlignmentType.CENTER)
                            txt = "     $txt"
                        usbComm.sendUsbSerial(PT102Utils.NORMAL_32_CHAR)
                        usbComm.sendUsbSerial(txt)
                    }
                    else if(printerType == AppConstant.DW14_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            "<font size='big'>$text</font>\n"
                        } else {
                            "<font size='big'>$text</font>"
                        }
                        txt = when(alignment){
                            AlignmentType.CENTER -> { "[C]$txt" }
                            AlignmentType.LEFT -> { "[L]$txt" }
                            AlignmentType.RIGHT -> { "[R]$txt" }
                        }
                        finalDwTextToPrint += txt
                        //escPosPrinter.printFormattedText(txt)
                    }
                }
            }
            else -> {
                printTextLandi(landiPrinter!!, text, FONT_SIZE_LARGE, getLandiAlignment(alignment))
            }
        }
    }
    private fun boldText2(text: String, alignment: AlignmentType) {
        setPageMargin()
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                wPosPrinter.printString(getSplitString(text), 30, getWPosAlignment(alignment), false, false)
            }
            "PAX" -> {
                if(MainApp.deviceName.contains("A920"))
                {
                    paxStringNormalBold(text,getA920Alignment(alignment))
                }
                else
                {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        citizenPrinter.printText(
                            formatText(text),
                            getAlignment(alignment),
                            ESCPOSConst.CMP_FNT_BOLD,
                            ESCPOSConst.CMP_TXT_2WIDTH or ESCPOSConst.CMP_TXT_2HEIGHT
                        )
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            text+"\n"
                        } else {
                            text
                        }
                        if(alignment==AlignmentType.CENTER)
                            txt = "     $txt"
                        usbComm.sendUsbSerial(PT102Utils.NORMAL_24_CHAR)
                        usbComm.sendUsbSerial(txt)
                    }
                    else if(printerType == AppConstant.DW14_PRINTER){
                        var txt = if(!text.contains("\n")) {
                            "<b><font size='big'>$text</font></b>\n"
                        } else {
                            "<b><font size='big'>$text</font></b>"
                        }
                        txt = when(alignment){
                            AlignmentType.CENTER -> { "[C]$txt" }
                            AlignmentType.LEFT -> { "[L]$txt" }
                            AlignmentType.RIGHT -> { "[R]$txt" }
                        }
                        finalDwTextToPrint += txt
                        //escPosPrinter.printFormattedText(txt)
                    }
                }
            }
            else -> {
                printTextLandi(landiPrinter!!, text, FONT_SIZE_LARGE, getLandiAlignment(alignment))
            }
        }
    }
    private fun printBitmapReceipt(bmp: Bitmap,layout: Int) {
        setPageMargin()
        val bitmap = bmp
        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                try {
                    var width = (bitmap.width/3)-30
                    var height = (bitmap.height/3)-20
                    if(layout == 2)
                    {
                      width = (bitmap.width)-10
                      height = (bitmap.height)
                    }

                    println("printBitmapReceipt WPOS $width -- $height")
                    //wPosResult = wPosPrinter.printImage(bmp, bmp.height, Printer.Align.CENTER)
                    wPosResult = wPosPrinter.printImageBase(bitmap, width,  height, Printer.Align.CENTER,6)
                    log(TAG, wPosResult.toString())
                    //bmp.recycle()
                } catch (ex: RemoteException) {
                    ex.printStackTrace()
                    log(TAG, ex.toString() + ExceptionUtils.getStackTrace(ex))
                } catch (ex: FileNotFoundException) {
                    ex.printStackTrace()
                    log(TAG, ex.toString() + ExceptionUtils.getStackTrace(ex))
                }
            }
            "PAX" -> {
                if (MainApp.deviceName.contains("A920")) {
                    //printA920Image(bmp)
                    try {
                        //var bmp = Bitmap.createScaledBitmap(Support.toGrayscale(bitmap)!!, bitmap.width,bitmap.height, true)
                        //bmp = processingBitmap(bmp)
                        PrinterTester.getInstance().leftIndents("0".toShort())
                        PrinterTester.getInstance().printBitmap(bmp)
                        //bmp.recycle()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                } else {
                    if (printerType == AppConstant.CITIZEN_PRINTER) {
                        //val bitmap = Bitmap.createScaledBitmap(bmp, width, height, false)
                        val result = citizenPrinter.printBitmap(
                            bitmap,
                            bitmap.height,
                            ESCPOSConst.CMP_ALIGNMENT_CENTER
                        )
                        log(TAG, "PRINT BITMAP Result: $result")
                        //bmp.recycle()
                    } else if (printerType == AppConstant.PT102_PRINTER) {
                        log(TAG, "IMAGE print not working in PT102")
                    } else if (printerType == AppConstant.DW14_PRINTER) {
                        escPosPrinter.printFormattedText("[C]<img>" + PrinterTextParserImg.bitmapToHexadecimalString(escPosPrinter, bitmap)+"</img>\n")
                    }
                }
            }
            else -> {
                try {
                    //val bitmap = Bitmap.createScaledBitmap(bmp,width,height,false)
                    printLandiImage(bitmap,AlignMode.CENTER)
                    //bmp.recycle()
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }
        }
    }
    private fun printBitmap(bmp: Bitmap, width:Int, height:Int, alignment: AlignmentType) {
        setPageMargin()

        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                try {
                    val bitmap = Bitmap.createScaledBitmap(bmp,width,height,false)
                    wPosResult = wPosPrinter.printImage(bitmap, bitmap.height, getWPosAlignment(alignment))
                    log(TAG, wPosResult.toString())
                    bitmap.recycle()
                } catch (ex: RemoteException) {
                    ex.printStackTrace()
                } catch (ex: FileNotFoundException) {
                    ex.printStackTrace()
                }
            }
            "PAX" -> {
                if (MainApp.deviceName.contains("A920")) {
                    printA920Image(bmp)
                } else {
                    if (printerType == AppConstant.CITIZEN_PRINTER) {
                        val bitmap = Bitmap.createScaledBitmap(bmp, width, height, false)
                        val result = citizenPrinter.printBitmap(
                            bitmap,
                            bitmap.height,
                            getAlignment(alignment)
                        )
                        log(TAG, "PRINT BITMAP Result: $result")
                    } else if (printerType == AppConstant.PT102_PRINTER) {
                        log(TAG, "IMAGE print not working in PT102")
                    }
                    else if (printerType == AppConstant.DW14_PRINTER) {
                        val align = when(alignment){
                            AlignmentType.LEFT -> "[L]"
                            AlignmentType.CENTER -> "[C]"
                            AlignmentType.RIGHT -> "[R]"
                        }
                        escPosPrinter.printFormattedText("$align<img>" + PrinterTextParserImg.bitmapToHexadecimalString(escPosPrinter, bmp)+"</img>")
                    }
                }
            }
            else -> {
                try {
                    val bitmap = Bitmap.createScaledBitmap(bmp,width,height,false)
                    printLandiImage(bitmap,getLandiAlignment(alignment))
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }
        }
    }
    private fun printBitmap(bmp:ByteArray,  width:Int, height:Int, alignment: AlignmentType) {
        setPageMargin()
        /*val result =   citizenPrinter.printBitmap(bitmap, width, getAlignment(alignment))
        log(TAG, "PRINT BITMAP Result: $result")*/

        when(BuildConfig.POS_TYPE){
            "B_TPE" -> {
                try {
                    var bitmap = BitmapFactory.decodeByteArray(bmp, 0, bmp.size)
                    bitmap = Bitmap.createScaledBitmap(bitmap,width,height,false)
                    wPosResult = wPosPrinter.printImage(bitmap, height, getWPosAlignment(alignment))
                    log(TAG, wPosResult.toString())
                    bitmap.recycle()
                } catch (ex: RemoteException) {
                    ex.printStackTrace()
                } catch (ex: FileNotFoundException) {
                    ex.printStackTrace()
                }
            }
            "PAX" -> {
                if (MainApp.deviceName.contains("A920")) {
                    var bmp =BitmapFactory.decodeByteArray(bmp, 0, bmp.size)
                    printA920Image(bmp)
                } else {
                    if(printerType==AppConstant.CITIZEN_PRINTER){
                        //var bitmap = BitmapFactory.decodeByteArray(bmp, 0, bmp.size)
                        val result =   citizenPrinter.printBitmap(bmp,width, height,getAlignment(alignment))
                        log(TAG, "PRINT BITMAP Result: $result")
                    }
                    else if(printerType==AppConstant.PT102_PRINTER){
                        log(TAG,"IMAGE print not working in PT102")
                    }
                    else if (printerType == AppConstant.DW14_PRINTER) {
                        try {
                            val bitmap = BitmapFactory.decodeByteArray(bmp, 0, bmp.size)
                            val align = when (alignment) {
                                AlignmentType.LEFT -> "[L]"
                                AlignmentType.CENTER -> "[C]"
                                AlignmentType.RIGHT -> "[R]"
                            }
                            escPosPrinter.printFormattedText(
                                "$align<img>" + PrinterTextParserImg.bitmapToHexadecimalString(
                                    escPosPrinter,
                                    bitmap
                                ) + "</img>"
                            )
                            bitmap.recycle()
                        }  catch (ex: RemoteException) {
                            ex.printStackTrace()
                        } catch (ex: FileNotFoundException) {
                            ex.printStackTrace()
                        }
                    }
                }
            }
            else -> {
                try {
                    printLandiImage(bmp,getLandiAlignment(alignment))
                } catch (ex: IOException) {
                    ex.printStackTrace()
                }
            }
        }

    }
    //endregion

    //region B_TPE printer
    private lateinit var wPosPrinter: wangpos.sdk4.libbasebinder.Printer
    private var wPosResult = 0
    private fun initWPosPrinter(){
        try {
            wPosPrinter = Printer(context)
            wPosResult = wPosPrinter.printInit()
        } catch (e: RemoteException) {
            e.printStackTrace()
            log(TAG, "RemoteException print  " + e.message)
        }
        log(TAG, "print data init $wPosResult")
    }
    private fun resetWPosPrinter(){
        wPosResult = wPosPrinter.printPaper(60)
        wPosResult = wPosPrinter.printFinish()
    }

    private fun getWPosAlignment(alignment: AlignmentType) : Printer.Align {
        return  when(alignment){
            AlignmentType.LEFT -> Printer.Align.LEFT
            AlignmentType.CENTER -> Printer.Align.CENTER
            AlignmentType.RIGHT -> Printer.Align.RIGHT
        }
    }
    //endregion

    //region citizen printer
    private lateinit var citizenPrinter: ESCPOSPrinter
    private var usbDevice: UsbDevice? = null
    private var result:Int = 0
    private fun initCitizenPrinter() {
        citizenPrinter = ESCPOSPrinter()
        citizenPrinter.setContext(context)
        usbDevice = null
        result = citizenPrinter.connect(ESCPOSConst.CMP_PORT_USB, usbDevice)

        if (ESCPOSConst.CMP_SUCCESS == result) {
            // Set encoding
            citizenPrinter.setEncoding("ISO-8859-1")
            // Start Transaction ( Batch )
            citizenPrinter.transactionPrint(ESCPOSConst.CMP_TP_TRANSACTION)
            log("Max. Page area", "( x,y ) : " + citizenPrinter.pageModeArea)

            // Direction set
            citizenPrinter.pageModePrintDirection = ESCPOSConst.CMP_PD_TOP_TO_BOTTOM
            logWriter.appendLog(TAG, "CitizenPrint KT")

        } else {
            // Connect Error
            log("Citizen_POS_sample1", "Connect or Printer Error : $result")
            logWriter.appendLog(
                TAG,
                "CitizenPrinter Connect error code: " + result + " message: " + showCitizenPrinterMessage(result)
            )

            Thread {
                Handler(Looper.getMainLooper()).post {
                    if (result != ESCPOSConst.CMP_E_CONNECTED) Toast.makeText(
                        context,
                        showCitizenPrinterMessage(result),
                        Toast.LENGTH_LONG
                    ).show()
                }
            }.start()
        }
    }
    private fun resetCitizenPrinter() {
        text(" ", AlignmentType.LEFT)
        text(" ", AlignmentType.LEFT)
        citizenPrinter.cutPaper(ESCPOSConst.CMP_CUT_PARTIAL_PREFEED)
        val result = citizenPrinter.transactionPrint(ESCPOSConst.CMP_TP_NORMAL)
        citizenPrinter.disconnect()
        if (ESCPOSConst.CMP_SUCCESS != result) {
            // Print process Error
            log("Citizen_POS_sample1", "Transaction Error : $result")
            logWriter.appendLog(TAG, "Transaction Error : $result")
        } else {
            logWriter.appendLog(TAG, "print success")
        }
    }
    private fun showCitizenPrinterMessage(result: Int): String {
        val word: String = when (result) {
            ESCPOSConst.CMP_SUCCESS -> "The operation is success."
            ESCPOSConst.CMP_E_CONNECTED -> "The printer is already connected."
            ESCPOSConst.CMP_E_DISCONNECT -> "The printer is not connected."
            ESCPOSConst.CMP_E_NOTCONNECT -> "Failed connection to the printer."
            ESCPOSConst.CMP_E_CONNECT_NOTFOUND -> "Failed to check the support model after connecting to the device."
            ESCPOSConst.CMP_E_CONNECT_OFFLINE -> "Failed to check the printer status after connecting to the device."
            ESCPOSConst.CMP_E_ILLEGAL -> "Unsupported operation with the Device, or an invalid parameter value was used."
            ESCPOSConst.CMP_E_OFFLINE -> "The printer is off-line."
            ESCPOSConst.CMP_E_NOEXIST -> "The file name does not exist."
            ESCPOSConst.CMP_E_FAILURE -> "The Service cannot perform the requested procedure."
            ESCPOSConst.CMP_E_TIMEOUT -> "The Service timed out waiting for a response from the printer."
            ESCPOSConst.CMP_E_NO_LIST -> "The printer cannot be found in the printer search."
            ESCPOSConst.CMP_EPTR_COVER_OPEN -> "The cover of the printer opens."
            ESCPOSConst.CMP_EPTR_REC_EMPTY -> "The printer is out of paper."
            ESCPOSConst.CMP_EPTR_BADFORMAT -> "The specified file is in an unsupported format."
            ESCPOSConst.CMP_EPTR_TOOBIG -> "The specified bitmap is either too big."
            else -> "The other error."
        }
        return word
    }
    //endregion

    //region A920 printer
    private fun initA920Printer() {
        PrinterTester.getInstance().init()
        PrinterTester.getInstance().fontSet(EFontTypeAscii.FONT_12_24, EFontTypeExtCode.FONT_16_16)
        PrinterTester.getInstance().step(20)
        PrinterTester.getInstance().setGray(1)
        PrinterTester.getInstance().setGray("1".toInt())
        PrinterTester.getInstance().setInvert(false)
        PrinterTester.getInstance().leftIndents("110".toShort())
    }
    private fun resetA920Printer() {
        PrinterTester.getInstance().printStr("\n", null)
        PrinterTester.getInstance().printStr("\n", null)
        PrinterTester.getInstance().printStr("\n", null)
        PrinterTester.getInstance().printStr("\n", null)
        PrinterTester.getInstance().printStr("\n", null)
        PrinterTester.getInstance().step(5)
        PrinterTester.getInstance().start()
    }
    private fun paxStringBold(text: String?,short: Short) {
        PrinterTester.getInstance().fontSet(EFontTypeAscii.FONT_12_48, EFontTypeExtCode.FONT_16_32)
        PrinterTester.getInstance().spaceSet(2,0)
        PrinterTester.getInstance().leftIndents(short)
        PrinterTester.getInstance().printStr(text, null)
    }
    private fun paxStringNormalBold(text: String?,short: Short) {
        PrinterTester.getInstance().fontSet(EFontTypeAscii.FONT_8_16, EFontTypeExtCode.FONT_16_16)
        PrinterTester.getInstance().setDoubleWidth(true,true)
        PrinterTester.getInstance().setDoubleHeight(true,true)
        PrinterTester.getInstance().spaceSet(0,0)
        PrinterTester.getInstance().leftIndents(short)
        PrinterTester.getInstance().printStr(text, null)
    }
    private fun paxStringNormal(text: String?,short: Short) {
        PrinterTester.getInstance().fontSet(EFontTypeAscii.FONT_12_24, EFontTypeExtCode.FONT_16_16)
        PrinterTester.getInstance().spaceSet(0,0)
        PrinterTester.getInstance().leftIndents(short)
        PrinterTester.getInstance().printStr(text, null)
    }
    private fun getA920Alignment(alignment: AlignmentType): Short{
        var align = 10
        align = when(alignment){
            AlignmentType.LEFT -> 10
            AlignmentType.CENTER -> 70
            AlignmentType.RIGHT -> 70
        }
        return  align.toShort()
    }
    private fun printA920Image(bitmap: Bitmap) {
        try {
            var bmp = Bitmap.createScaledBitmap(Support.toGrayscale(bitmap)!!, bitmap.width,bitmap.height, true)
            bmp = processingBitmap(bmp)
            PrinterTester.getInstance().printBitmap(bmp)
            bmp.recycle()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    //endregion

    //region LANDI printer
    private var landiPrinter: UPrinter? = null
    private val FONT_SIZE_SMALL = 0
    private val FONT_SIZE_NORMAL = 1
    private val FONT_SIZE_LARGE = 2
    private fun initLandiPrinter(){
        landiPrinter = getPrinter()!!
        landiPrinter!!.setPrnGray(6)
    }
    private fun resetLandiPrinter(){
        landiPrinter!!.feedLine(8)
        landiPrinter!!.startPrint(null)
    }

    @Throws(RemoteException::class, IOException::class)
    private fun printTextLandi(printer: UPrinter, text: String, fontSize: Int, alignMode: Int) {
        setFontSpec(printer, fontSize)
        printer.addText(alignMode, text)
    }
    @Throws(RemoteException::class)
    private fun setFontSpec(printer: UPrinter, fontSpec: Int) {
        when (fontSpec) {
            FONT_SIZE_SMALL -> {
                printer.setHzSize(HZSize.DOT16x16)
                printer.setHzScale(HZScale.SC1x1)
                printer.setAscSize(ASCSize.DOT16x8)
                printer.setAscScale(ASCScale.SC1x1)
            }
            FONT_SIZE_NORMAL -> {
                printer.setHzSize(HZSize.DOT24x24)
                printer.setHzScale(HZScale.SC1x1)
                printer.setAscSize(ASCSize.DOT24x12)
                printer.setAscScale(ASCScale.SC1x1)
            }
            FONT_SIZE_LARGE -> {
                printer.setHzSize(HZSize.DOT24x24)
                printer.setHzScale(HZScale.SC1x2)
                printer.setAscSize(ASCSize.DOT24x12)
                printer.setAscScale(ASCScale.SC1x2)
            }
        }
    }
    private fun getLandiAlignment(alignment: AlignmentType) : Int {
        val align = when(alignment){
            AlignmentType.LEFT -> AlignMode.LEFT
            AlignmentType.CENTER -> AlignMode.CENTER
            AlignmentType.RIGHT -> AlignMode.RIGHT
        }
        return  align
    }
    @Throws(IOException::class)
    private fun printLandiImage(bitmap: Bitmap,alignMode: Int) {
        var bmp = Bitmap.createScaledBitmap(bitmap, 128, 128, true)
        val bos = ByteArrayOutputStream()
        bmp.compress(Bitmap.CompressFormat.JPEG, 60 /*ignored for PNG*/, bos)
        bmp.recycle()
        val bitmapdata = bos.toByteArray()
        BitmapFactory.decodeByteArray(bitmapdata, 0, bitmapdata.size)
        landiPrinter!!.addImage(alignMode, bitmapdata)
    }
    private fun printLandiImage(bitmap: ByteArray,alignMode: Int) {
        BitmapFactory.decodeByteArray(bitmap, 0, bitmap.size)
        landiPrinter!!.addImage(alignMode, bitmap)
    }
    //endregion

    //region PT102 printer
    private fun initPT102Printer(){
        usbComm = USBComm(context)
        usbComm.initUSBComm()
    }
    private fun resetPT102Printer(){
        usbComm.closeUSBComm()
    }
    //endregion

    //region ESC/POS printer (CITIZEN DW-14)
    private var printRequestType = 1
    private var bitmapToPrint: Bitmap? = null
    private var layoutToPrint: Int = 1
    private var commandsToPrint: ArrayList<PrintCmd> = ArrayList()

    lateinit var escPosPrinter: EscPosPrinter
    //var asyncEscPosPrinter: AsyncEscPosPrinter? = null
    lateinit var escPosUsbConnection: UsbConnection
    var escDPI = 203
    var escWidthMM = 48f
    var escNoOfCharInLine = 32
    private val ACTION_USB_PERMISSION: String = "com.android.example.USB_PERMISSION"
    private fun initEscPosPrinter() {
        escWidthMM = 58f
        escNoOfCharInLine = 48


        val usbConnection = UsbPrintersConnections.selectFirstConnected(context)
        val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager?

        if (usbConnection == null || usbManager == null) {
            AlertDialog.Builder(context)
                .setTitle(context.getString(R.string.printer_connection))
                .setMessage(context.getString(R.string.not_usb_printer_found))
                .show()
            return
        }

        val permissionIntent = PendingIntent.getBroadcast(
            context,
            0,
            Intent(ACTION_USB_PERMISSION),
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) PendingIntent.FLAG_MUTABLE else 0
        )
        val filter = IntentFilter()
        filter.addAction(ACTION_USB_PERMISSION)
        context.registerReceiver(usbReceiver, filter)
        usbManager.requestPermission(usbConnection.device, permissionIntent)
    }
    private fun resetEscPosPrinter(){
        try { escPosPrinter.printFormattedTextAndCut("[L]\n\n") } catch (e:Exception){e.printStackTrace()}
        context.unregisterReceiver(usbReceiver)
    }

    private val usbReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            if (ACTION_USB_PERMISSION == action) {
                synchronized(this) {
                    val usbManager = context.getSystemService(Context.USB_SERVICE) as UsbManager?
                    val usbDevice = intent.getParcelableExtra<Parcelable>(UsbManager.EXTRA_DEVICE) as UsbDevice?
                    if (intent.getBooleanExtra(UsbManager.EXTRA_PERMISSION_GRANTED, false)) {
                        if (usbManager != null && usbDevice != null) {

                            escPosPrinter = EscPosPrinter(UsbConnection(usbManager, usbDevice), escDPI, escWidthMM, escNoOfCharInLine)
                            escPosUsbConnection = UsbConnection(usbManager, usbDevice)
                            //asyncEscPosPrinter = getAsyncEscPosPrinter(UsbConnection(usbManager, usbDevice))
                            Log.e(TAG,"USB printer connected")
                            when(printRequestType){
                                1 -> {
                                    if (bitmapToPrint != null) {
                                        /* val targetWidth = 1500 // 80mm printing zone with 203dpi => 383px
                                         val rescaledBitmap = Bitmap.createScaledBitmap(bitmapToPrint!!, targetWidth, (bitmapToPrint!!.height.toFloat() * targetWidth.toFloat() / bitmapToPrint!!.width.toFloat()).roundToInt(), true)
                                         Log.e(TAG,"BITMAP SIZE: ${bitmapToPrint!!.width}x${bitmapToPrint!!.height} SCALED SIZE: ${rescaledBitmap.width}x${rescaledBitmap.height}")*/

                                        printReceiptOption1(bitmapToPrint!!)
                                    } else
                                        Log.e(TAG, "BitmapToPrint is null")
                                }
                                2 -> {
                                    if (bitmapToPrint != null) {
                                        /*val targetWidth = 1500 // 80mm printing zone with 203dpi => 383px
                                        val rescaledBitmap = Bitmap.createScaledBitmap(bitmapToPrint!!, targetWidth, (bitmapToPrint!!.height.toFloat() * targetWidth.toFloat() / bitmapToPrint!!.width.toFloat()).roundToInt(), true)
                                        Log.e(TAG,"BITMAP SIZE: ${bitmapToPrint!!.width}x${bitmapToPrint!!.height} SCALED SIZE: ${rescaledBitmap.width}x${rescaledBitmap.height}")*/

                                        printReceiptOption2(bitmapToPrint!!, layoutToPrint)
                                    } else
                                        Log.e(TAG, "BitmapToPrint is null")
                                }
                                3 -> {
                                    if (commandsToPrint.isNotEmpty()) printReceiptOption3(commandsToPrint) else Log.e(TAG, "Print Commands empty")
                                }
                            }
                        }
                        else {
                            Log.e(TAG,"USB printer null")
                        }
                    }
                }
            }
        }
    }

    /*==============================================================================================
    ===================================ESC/POS PRINTER PART=========================================
    ==============================================================================================*/
    fun getAsyncEscPosPrinter(printerConnection: DeviceConnection?): AsyncEscPosPrinter? {
        val printer = AsyncEscPosPrinter(printerConnection, escDPI, escWidthMM, escNoOfCharInLine)
        return printer.addTextToPrint(finalDwTextToPrint)
    }
    //endregion

    private fun getSplitString(text:String):String{
        var txt = text
        if(text.length>27)
            txt = txt.chunked(27).joinToString(separator = "\n")
        return txt
    }

    private fun isPaperAvailable(): Boolean{
        printerStatusMessage =  context.getString(R.string.paper_not_available_in_printer)
        when(BuildConfig.POS_TYPE){
            "B_TPE"-> {
                val status = intArrayOf(0,1)
                 val statusCode = wPosPrinter.getPrinterStatus(status)
                printerStatusMessage = context.getString(R.string.out_of_paper)
                Log.i(TAG, "Printer Status::$statusCode")
                return statusCode == 0
            }
            "PAX" -> {
                when {
                    MainApp.deviceName.contains("A920") -> {
                        return if(PrinterTester.getInstance().status == 0) {
                            true
                        } else {
                            val status = PrinterTester.getInstance().status
                            printerStatusMessage = PrinterTester.getInstance().statusCode2Str(status)
                            log(TAG, "Printer Status: $status")
                            false
                        }
                    }
                    MainApp.deviceName.contains("IM30") -> {
                        //todo add paper station of printer here
                        return true //for testing purpose
                    }
                    else -> {
                        return true
                    }
                }
            }
            else -> {
                //todo add printer paper status here
                return true //for testing purpose
            }
        }
    }
    private fun showPaperDialog(commands: ArrayList<PrintCmd>){
        MyMaterialDialog(context,
            context.getString(R.string.error),
            printerStatusMessage,
            positiveBtnText = MainApp.appContext.getString(R.string.print_again),
            negativeBtnText = MainApp.appContext.getString(R.string.cancel),
            object : MyMaterialDialogListener{
                override fun onPositiveClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    printTicket(commands)
                }

                override fun onNegativeClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    return
                }
            })
    }
    private fun showPaperDialog(bitmap:Bitmap){
        if (!(context as Activity).isFinishing) {
            MyMaterialDialog(context,
                context.getString(R.string.error),
                printerStatusMessage,
                positiveBtnText = context.getString(R.string.print_again),
                negativeBtnText = context.getString(R.string.cancel),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        printReceipt(bitmap)
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        return
                    }
                })
        }
    }
}
