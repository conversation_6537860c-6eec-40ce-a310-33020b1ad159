package app.rht.petrolcard.ui.reference.model

import androidx.annotation.Keep

@Keep
data class DiscountDetailModel(
    val scheme: List<DiscountSchemeModel>,
    val date_from: String?,
    val date_time_range_type: Int?,//(1=all,2 = specific days)
    val date_to: String?,
    val discount_for: Int? = 0,//(0= specific cards,1= all_postpaid, 2 = all_prepaid,3 = all cards)
    val discount_id: Int?,
    val specific_day: List<Int>?,
    val time_from: String?,
    val time_to: String?
)