package app.rht.petrolcard.ui.settings.card.recharge.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.service.model.ResponseIPContentu
import app.rht.petrolcard.ui.updatecard.model.CardDetailsResponse
import app.rht.petrolcard.ui.iccpayment.model.StoreCardDataModel
import app.rht.petrolcard.ui.reference.model.ReferenceModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper


open class RechargeViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {
    var rechargeDataObserver = MutableLiveData<BaseResponse<String>>()


    fun rechargeCard(pan:String,amount:String) {
        requestData(mNetworkService.rechargeCard(preferencesHelper.baseUrl+NetworkRequestEndPoints.RECHARGE_CARD,
            MainApp.sn!!,
            pan,
            amount),
            {
                rechargeDataObserver.postValue(it)
            })
    }

}
