package app.rht.petrolcard.ui.loyalty.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.iccpayment.model.ActivationDetails
import app.rht.petrolcard.ui.loyalty.model.Letters
import app.rht.petrolcard.ui.loyalty.model.LoyaltyCustomerRequest
import app.rht.petrolcard.ui.loyalty.model.LoyaltyProduct
import app.rht.petrolcard.ui.loyalty.model.Station
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody

class LoyaltyActivationViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
): CommonViewModel(mNetworkService,preferencesHelper) {
    var activationDetails = MutableLiveData<BaseResponse<ActivationDetails>>()
    var loyaltyCategories = MutableLiveData<BaseResponse<List<String>>>()
    var loyaltyProducts = MutableLiveData<BaseResponse<List<LoyaltyProduct>>>()
    var stations = MutableLiveData<BaseResponse<List<Station>>>()
    var letters = MutableLiveData<BaseResponse<List<Letters>>>()
    var activateCustomer = MutableLiveData<BaseResponse<String>>()

    private val baseUrl = preferencesHelper.baseUrl.replace("-tpe","")

    fun getActivationDetails(cardNumber:String){
        val sn = MainApp.sn!!
        val url = (baseUrl+ NetworkRequestEndPoints.GET_ACTIVATION_DETAILS)

        requestData(mNetworkService.activationDetails(url, sn, cardNumber), {
            activationDetails.postValue(it)
        })
    }

    fun getLoyaltyCategories(){
        val sn = MainApp.sn!!
        val url = (baseUrl + NetworkRequestEndPoints.LOYALTY_CATEGORIES)

        requestData(mNetworkService.getCategories(url, sn), {
            loyaltyCategories.postValue(it)
        })
    }

    fun getLoyaltyProducts(){
        val sn = MainApp.sn!!
        val url = ( baseUrl + NetworkRequestEndPoints.LOYALTY_PRODUCTS)

        requestData(mNetworkService.getLoyaltyProducts(url, sn), {
            loyaltyProducts.postValue(it)
        })
    }

   /* fun getStations(){
        val sn = MainApp.sn!!
        val url = ( baseUrl + NetworkRequestEndPoints.GET_STATIONS)

        requestData(mNetworkService.getStations(url, sn), {
            stations.postValue(it)
        })
    }*/
    fun getLetters(){
        val url = ( baseUrl + NetworkRequestEndPoints.GET_LETTERS)

        requestData(mNetworkService.getLetters(url), {
            letters.postValue(it)
        })
    }

    fun activateLoyaltyCustomer(isUpdate:Boolean,item: LoyaltyCustomerRequest){
        val sn = MainApp.sn!!

        val url =
            if(!isUpdate)
                baseUrl + NetworkRequestEndPoints.LOYALTY_ACTIVATE
            else
                baseUrl + NetworkRequestEndPoints.LOYALTY_UPDATE

        val serial = sn.toRequestBody("text/plain".toMediaType())

        requestData(mNetworkService.loyaltyActivation(
            url = url,
            sn = serial,
            cin_file = item.cin_file,
            souche_file = item.souche_file,
            carte_grise_file = item.carte_grise_file,
            hashKey = item.hashKey,
            cin = item.cin,
            nom = item.nom,
            prenom = item.prenom,
            date_naissance = item.date_naissance,
            lieu_naissance = item.lieu_naissance,
            tel = item.tel,
            adresse = item.adresse,
            ville = item.ville,
            num_permis = item.num_permis,
            categorie = item.categorie,
            pan = item.pan,
            station = item.station,
            matricule = item.matricule,
            num_carte_grise = item.num_carte_grise,
            agrement = item.agrement,
            cv_fiscaux = item.cv_fiscaux,
            carburant = item.carburant,
            tag = item.tag,
            badge = item.badge,
            version = item.version,
            connection = item.connection
        ), {

            activateCustomer.postValue(it)

        })
    }

    /*fun updateLoyaltyCustomer(item: LoyaltyCustomer){
        val sn = MainApp.sn!!
        val url = ( baseUrl + NetworkRequestEndPoints.LOYALTY_UPDATE)
        val serial = RequestBody.create("text/plain".toMediaType(), sn)

        requestData(mNetworkService.loyaltyActivation(
            url = url,
            sn = serial,
            cin_file = item.cin_file,
            souche_file = item.souche_file,
            carte_grise_file = item.carte_grise_file,
            hashKey = item.hashKey,
            cin = item.cin,
            nom = item.nom,
            prenom = item.prenom,
            date_naissance = item.date_naissance,
            lieu_naissance = item.lieu_naissance,
            tel = item.tel,
            adresse = item.adresse,
            ville = item.ville,
            num_permis = item.num_permis,
            categorie = item.categorie,
            pan = item.pan,
            station = item.station,
            matricule = item.matricule,
            num_carte_grise = item.num_carte_grise,
            agrement = item.agrement,
            cv_fiscaux = item.cv_fiscaux,
            carburant = item.carburant,
            tag = item.tag,
            badge = item.badge,
            version = item.version
        ), {
            updateCustomer.postValue(it)
        })
    }*/
}