package app.rht.petrolcard.ui.settings.backup.activity

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.MainDataBaseClass
import app.rht.petrolcard.databinding.ActivityBackupBinding
import app.rht.petrolcard.databinding.ActivitySplashBinding

import app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel
import app.rht.petrolcard.ui.settings.common.viewmodel.SettingsViewModel
import app.rht.petrolcard.utils.UtilsCardInfo
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteOpenHelper
import java.io.*
import java.nio.channels.FileChannel
import java.text.SimpleDateFormat
import java.util.*

@Suppress("DEPRECATION")
class BackupActivity : BaseActivity<SettingsViewModel>(SettingsViewModel::class) {

    private lateinit var mBinding: ActivityBackupBinding
    private val CREATE_BACKUP_REQUEST_CODE = 1000
    private val PICK_RESTORE_FILE_REQUEST_CODE = 2000
    var sqLiteHelper:SQLiteOpenHelper?=null
    var mDb: SQLiteDatabase? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_backup)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        backupDatabase()
        mDb = MainDataBaseClass().mHandler!!.getWritableDatabase(UtilsCardInfo.genEncryptKey(mCore, MainDataBaseClass.sn!!))
    }

    override fun setObserver() {

    }
    fun backupDatabase()
    {
        val intent = Intent(Intent.ACTION_CREATE_DOCUMENT)
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.type = "application/octet-stream"
        val sdf = SimpleDateFormat("yyyy-MM-dd_HH-mm")
        val backupFileName = sdf.format(Date()).toString() + ".bak"
        intent.putExtra(Intent.EXTRA_TITLE, backupFileName)
        backupResultLaunch.launch(intent)
    }
    fun restoreDatabase()
    {
        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.type = "*/*"
        val mimeTypes = arrayOf("application/octet-stream")
        intent.putExtra(Intent.EXTRA_MIME_TYPES, mimeTypes)
        restoreResultLaunch.launch(intent)

    }
    var backupResultLaunch = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->

        if (result.resultCode != Activity.RESULT_OK) {
            return@registerForActivityResult
        }
        if (result.resultCode == CREATE_BACKUP_REQUEST_CODE) {
            val backupFileUri: Uri = result.data!!.data!!
            newexportDB(backupFileUri)
            val msg = "Backup has been saved!"
            Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
            mBinding.message.text = msg
            Log.i("FILE SELECT", "Save backup in: " + backupFileUri.path.toString())

        }

    }
    var restoreResultLaunch = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode != Activity.RESULT_OK) {
            return@registerForActivityResult
        }
        if (result.resultCode == PICK_RESTORE_FILE_REQUEST_CODE) {
            try {
                val uri: Uri =result.data!!.data!!
                val inputStream: InputStream? = contentResolver.openInputStream(uri)
                newrestoreDB(inputStream!!)
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
        }
    }
    private fun newrestoreDB(_inputStream: InputStream) {
        val exceptionOccurred = false
        val startTime = System.currentTimeMillis()
        try {
            val tempDir = cacheDir.path.toString()
            val newfile = File("$tempDir/newbackup.db")
            copyInputStreamToFile(_inputStream, newfile)
            restoreDatabaseFile(this, newfile, MainDataBaseClass.DatabaseFileName)
            sqLiteHelper!!.getWritableDatabase(MainApp.sn).close()
        } catch (e: java.lang.Exception) {
        }
    }

    @Throws(IOException::class)
    fun restoreDatabaseFile(context: Context, newDbFile: File, nameOfFileToRestore: String?) {
        // Close the SQLiteOpenHelper so it will commit the created empty database
        // to internal storage.
        sqLiteHelper!!.close()
        val currentDbFile = File(context.getDatabasePath(nameOfFileToRestore).path)
        //File newDbFile = new File(tempDir + "/" + nameOfFileToRestore);
        if (newDbFile.exists()) {
            copyFile(newDbFile, currentDbFile, true)
            val msg = "Database has been restored!"
            Toast.makeText(this, msg, Toast.LENGTH_SHORT).show()
            mBinding.message.text = msg
        }
    }

    // Copy an InputStream to a File.
    //
    private fun copyInputStreamToFile(`in`: InputStream, file: File) {
        var out: OutputStream? = null
        try {
            out = FileOutputStream(file)
            val buf = ByteArray(1024)
            var len: Int
            while (`in`.read(buf).also { len = it } > 0) {
                out.write(buf, 0, len)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        } finally {
            // Ensure that the InputStreams are closed even if there's an exception.
            try {
                out?.close()
                `in`.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    private fun newexportDB(_backupfileuri: Uri) {
        try {
            val tempDirectory = cacheDir
            val source: FileChannel? = null
            val destination: FileChannel? = null
            val databaseBackupFile = applicationContext.getDatabasePath(MainDataBaseClass.DatabaseFileName)
                    .toString() // getFilesDir().getPath() + "databases/SQLiteDatabase.db" ; // "/data/data/com.zakasoft.cashreceipt/databases/SQLiteDatabase.db" ; //  this.getDatabasePath(sqLiteHelper.getDatabaseName()).getPath();
            val dbbackupfile = backUpDatabaseFile(
                databaseBackupFile,
                tempDirectory.path.toString() + "/" +MainDataBaseClass.DatabaseFileName
            )
            val zipFileUri = Uri.fromFile(dbbackupfile)
            val inputStream = contentResolver.openInputStream(zipFileUri)
            val outputStream = contentResolver.openOutputStream(_backupfileuri)
            copyFile(inputStream, outputStream)
        } catch (e: java.lang.Exception) {
        }
    }
    @Throws(IOException::class)
    fun backUpDatabaseFile(pathOfFileToBackUp: String?, destinationFilePath: String?): File? {
        val currentDbFile = File(pathOfFileToBackUp)
        val newDb = File(destinationFilePath)
        if (currentDbFile.exists()) {
            copyFile(currentDbFile, newDb, false)
            return newDb
        }
        return null
    }

    fun copyFile(input: InputStream?, output: OutputStream?): Boolean {
        try {
            val buf = ByteArray(1024)
            var len: Int
            while (input!!.read(buf).also { len = it } > 0) {
                output!!.write(buf, 0, len)
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            return false
        } finally {
            try {
                input?.close()
                output?.close()
            } catch (e: java.lang.Exception) {
            }
        }
        return true
    }

    // Copy fromFile to toFile, using traditional file IO
    @Throws(IOException::class)
    fun copyFile(fromFile: File, toFile: File?, bDeleteOriginalFile: Boolean): Boolean {
        var bSuccess = true
        val inputStream = FileInputStream(fromFile)
        val outputStream = FileOutputStream(toFile)
        var fromChannel: FileChannel? = null
        var toChannel: FileChannel? = null
        try {
            fromChannel = inputStream.channel
            toChannel = outputStream.channel
            fromChannel.transferTo(0, fromChannel.size(), toChannel)
        } catch (e: java.lang.Exception) {
            bSuccess = false
        } finally {
            try {
                if (fromChannel != null) {
                    fromChannel.close()
                }
            } finally {
                if (toChannel != null) {
                    toChannel.close()
                }
            }
            if (bDeleteOriginalFile) {
                fromFile.delete()
            }
        }
        return bSuccess
    }
}
