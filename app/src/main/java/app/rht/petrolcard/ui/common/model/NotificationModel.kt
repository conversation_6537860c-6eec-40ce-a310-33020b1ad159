package app.rht.petrolcard.ui.common.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
@Keep
data class NotificationModel(
    val code: String?="",
    val message: String?="",
    val payload: String?="",
    val timestamp: String?="",
    val title: String?=""
): Parcelable
{
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(code)
        parcel.writeString(message)
        parcel.writeString(payload)
        parcel.writeString(timestamp)
        parcel.writeString(title)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<NotificationModel> {
        override fun createFromParcel(parcel: Parcel): NotificationModel {
            return NotificationModel(parcel)
        }

        override fun newArray(size: Int): Array<NotificationModel?> {
            return arrayOfNulls(size)
        }
    }

}