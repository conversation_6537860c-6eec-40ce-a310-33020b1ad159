package app.rht.petrolcard.ui.loyalty.adapter

import android.content.Context
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import app.rht.petrolcard.ui.loyalty.fragments.AvailableGiftsFragment
import app.rht.petrolcard.ui.loyalty.fragments.GiftHistoryFragment
import android.view.ViewGroup
import android.util.SparseArray
import android.view.View


class FragmentAdapter(context: Context, fm: FragmentManager, val totalTabs: Int) :
    FragmentPagerAdapter(fm) {
    private val myContext: Context = context

    private val registeredFragments = SparseArray<Fragment>()

    override fun getCount(): Int {
      return totalTabs
    }

    // this is for fragment tabs
    override fun getItem(position: Int): Fragment {
        return when (position) {
            0 -> {
                AvailableGiftsFragment()
            }
            1 -> {
                GiftHistoryFragment()
            }
            else -> AvailableGiftsFragment()
        }
    }

    override fun instantiateItem(container: ViewGroup, position: Int): Any {
        val fragment = super.instantiateItem(container, position) as Fragment
        registeredFragments.put(position, fragment)
        return fragment
    }

    override fun destroyItem(container: View, position: Int, `object`: Any) {
        registeredFragments.remove(position)
        super.destroyItem(container, position, `object`)
    }

    fun getRegisteredFragment(position: Int): Fragment {
        return registeredFragments.get(position)
    }
}