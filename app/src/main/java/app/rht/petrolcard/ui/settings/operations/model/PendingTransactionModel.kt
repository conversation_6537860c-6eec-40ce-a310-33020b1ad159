package app.rht.petrolcard.ui.settings.operations.model

import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.constant.AppConstant

class PendingTransactionModel (
    val transactionModel:TransactionModel?,
    val productModel:ProductModel?,
    val transactionStatus:String
):BaseModel()
{
    fun isCardTransaction():Boolean
    {
        return !transactionModel!!.pan.isNullOrEmpty()

    }
    fun getColor(): Int {
        return if(transactionModel!!.transactionStatus == 1) {
            Color.GREEN
        } else {
            Color.RED
        }

    }


}
