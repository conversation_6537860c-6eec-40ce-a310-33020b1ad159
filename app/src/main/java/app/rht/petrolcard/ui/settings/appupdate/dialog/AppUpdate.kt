package app.rht.petrolcard.ui.settings.appupdate.dialog

import android.content.Context
import android.util.Log
import android.view.View
import android.widget.TextView
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.utils.Utils
import app.rht.petrolcard.utils.helpers.appupdate.ApkVersion
import constant.UiType
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import listener.OnInitUiListener
import listener.UpdateDownloadListener
import model.UiConfig
import model.UpdateConfig
import update.UpdateAppUtils
import java.lang.Exception

class AppUpdate(val context: Context,val forceUpdate: Boolean,val isShowMessage:Boolean) {

    companion object
    {
        private val TAG = AppUpdate::class.simpleName
    }

    fun checkAppUpdate(version: String,url:String,releaseNotes:String,isUpdate:Int) {
            checkForUpdate(version,url,releaseNotes,isUpdate)
    }
    fun checkAppUpdateApi() {
        CoroutineScope(Dispatchers.Main).launch {
            checkForUpdateApi()
        }
    }


    private suspend fun checkForUpdateApi(){
        withContext(Dispatchers.Main) {
            Thread {
            try {
                var buildType= ""
                buildType = if(BuildConfig.POS_TYPE == "B_TPE") {
                    "blue_tpe"
                } else {
                    BuildConfig.POS_TYPE
                }
                MainApp.longApiService.getAppUpdateDetails( MainApp.getPrefs().baseUrl+ NetworkRequestEndPoints.GET_APP_UPDATE_DETAILS,buildType
                ).subscribeOn(rx.schedulers.Schedulers.io())
                    .observeOn(rx.android.schedulers.AndroidSchedulers.mainThread())
                    .subscribe ({
                        if (it != null && it.reponse != "0") {
                            val version = it.contenu!!.latestVersion
                            val url =  it.contenu.latestVersionUrl
                            val releaseNotes =  it.contenu.releaseNotes
                            val isUpdate = ApkVersion(version).compareTo(ApkVersion(BuildConfig.VERSION_CODE.toString()))

                            Log.e(TAG,"$$$$$ $isUpdate")
                            if(isUpdate == 1)
                            {
//                                showUpdateDialog(version,url,releaseNotes)
                                showUpdateDialog(version,url,releaseNotes,isUpdate)
                            }
                            else if(ApkVersion(version) <= ApkVersion(BuildConfig.VERSION_CODE.toString()) && isShowMessage)
                            {

                                showErrorDialog(context.getString(R.string.new_update_not_available),context.getString(
                                                                    R.string.application_is_already_updated))
                            }

                        }
                        else if(isShowMessage)
                        {
                            showErrorDialog(context.getString(R.string.server_error),context.getString(
                                                            R.string.failed_to_connect_with_server))
                        }
                    },
                        {
                            Log.i(TAG,"Scheduled Get Telecollect Failed")
                            if(isShowMessage)
                            {
                                showErrorDialog(context.getString(R.string.server_error),context.getString(
                                    R.string.failed_to_connect_with_server))
                            }
                            it.printStackTrace()
                        })

            } catch (e:Exception){
                Log.e(TAG,"Exception: ${e.message}")
                e.printStackTrace()
            }
            }.start()
        }
    }
    private fun checkForUpdate(version: String,url:String,releaseNotes:String,isUpdate:Int){
        if(isUpdate == 1)
        {
            showUpdateDialog(version,url,releaseNotes,isUpdate)
        }
        else if(ApkVersion(version) <= ApkVersion(BuildConfig.VERSION_CODE.toString()))
        {
            showErrorDialog(context.getString(R.string.new_update_not_available),context.getString(
                R.string.application_is_already_updated))
        }
    }

    private fun showUpdateDialog(version: String,url:String,releaseNotes:String,isUpdate:Int)
    {

        val fileName: String = url.substring(url.lastIndexOf('/') + 1)

        UpdateAppUtils.init(context)

        var layout: View? = null

        val updateConfig = UpdateConfig(
            force = forceUpdate,
            alwaysShowDownLoadDialog = true,
            apkSavePath = Utils.getStorageDir("App Updates"),
            apkSaveName = fileName,
            checkWifi = true,
            isShowNotification = true,
            notifyImgRes = R.drawable.ic_download
        )

        UpdateAppUtils
            .getInstance()
            .apkUrl(url)
            .updateTitle(context.getString(R.string.new_update_available))
            .updateContent(releaseNotes)
            .updateConfig(updateConfig)
            .uiConfig(UiConfig(uiType = UiType.CUSTOM, customLayoutId = R.layout.dialog_view_update))
            .setUpdateDownloadListener(object : UpdateDownloadListener {
                override fun onDownload(progress: Int) {
                    layout?.findViewById<TextView>(R.id.tv_update_title)?.text = context.getString(R.string.please_wait_downloading)
                    layout?.findViewById<TextView>(R.id.releaseNotes)?.text = releaseNotes
                }

                override fun onError(e: Throwable) {
                    Log.e(TAG,"Error $e")
                }

                override fun onFinish() {
                    Log.e(TAG,"Download Finished")
                    layout?.findViewById<TextView>(R.id.tv_update_title)?.text = context.getString(R.string.downloading_finished)
                    layout?.findViewById<TextView>(R.id.releaseNotes)?.text = releaseNotes
                }

                override fun onStart() {
                    Log.e(TAG,"Downloading Started")
                    layout?.findViewById<TextView>(R.id.tv_update_title)?.text = context.getString(R.string.downloading_started)
                    layout?.findViewById<TextView>(R.id.releaseNotes)?.text = releaseNotes
                }
                // do something
            })
            .setOnInitUiListener(object : OnInitUiListener {
                override fun onInitUpdateUi(view: View?, updateConfig: UpdateConfig, uiConfig: UiConfig) {
                    layout = view
                    view?.findViewById<TextView>(R.id.tv_update_title)?.text =context.getString(R.string.new_update_available)
                    layout?.findViewById<TextView>(R.id.releaseNotes)?.text = releaseNotes
                    view?.findViewById<TextView>(R.id.tv_version_name)?.text = " "
                    view?.findViewById<TextView>(R.id.btn_update_cancel)?.text = context.getString(R.string.cancel_update)
                    view?.findViewById<TextView>(R.id.btn_update_sure)?.text = context.getString(R.string.update_now)
                    // do more...
                }
            })
            .update()
    }
    private fun showErrorDialog(title:String,message:String)
    {
      UpdateAppUtils.init(context)
        var layout: View? = null
        val updateConfig = UpdateConfig(
            alwaysShowDownLoadDialog = true,
            apkSavePath = Utils.getStorageDir("App Updates"),
            apkSaveName = "",
            checkWifi = false,
            isShowNotification = false
        )
        UpdateAppUtils
            .getInstance()
            .apkUrl("")
            .updateTitle(context.getString(R.string.title))
            .updateContent(context.getString(R.string.message))
            .updateConfig(updateConfig)
            .uiConfig(UiConfig(uiType = UiType.CUSTOM, customLayoutId = R.layout.dialog_app_update_error))
            .setOnInitUiListener(object : OnInitUiListener {
                override fun onInitUpdateUi(view: View?, updateConfig: UpdateConfig, uiConfig: UiConfig) {
                    layout = view
                    view?.findViewById<TextView>(R.id.title)?.text =title
                    view?.findViewById<TextView>(R.id.message)?.text = message

                }
            })
            .update()
    }
}