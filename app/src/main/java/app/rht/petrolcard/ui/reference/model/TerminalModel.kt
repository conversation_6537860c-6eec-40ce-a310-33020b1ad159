package app.rht.petrolcard.ui.reference.model

import com.google.gson.annotations.SerializedName
import java.util.*import androidx.annotation.Keep
@Keep
data class TerminalModel(
    var id: Int? = null, // id in terminal table in db
    @SerializedName("idterminal")
    var terminalId: Int,
    var serialNumber: String?,
    var teleCollectTime: Date,
    var max_transaction_amount: Int,
    var min_transaction_amount: Int,
    var pays: String,
    var region: String,
    @SerializedName("ville")
    var city: String?,
    @SerializedName("idsecteur")
    var sectorId: Int,
    var sectorBit: Int,
    var sectorName: String,

    @SerializedName("idstation")
    var stationId: Int,
    var stationBit: Int,
    var stationType: Int,
    @SerializedName("station")
    var stationName: String?,

    @SerializedName("adresse")
    var address: String?,

    @SerializedName("idfiscal")
    var fiscalId: String?,
    var shopFlag: Int,
    var purchaseFlag: Int,
    var cancellationFlag: Int,
    var unlockTerminalFlag: Int,
    var rechargeFlag: Int,
    var lockTerminalFlag: Int,

    var ipController: String,
    var portController: String,
    var shopIp: String,
    var shopPort: String,

    var ipAddress: String,
    var netMask: String,
    var gateway: String?,
    var ssid: String,
    var wifiPassword: String,
    var inventoryCount: Int,
    var geoFence: String,
    var currency: String,

    @SerializedName("plafond_recharge")
    var maxRechargeLimit: String? = null,
    @SerializedName("plafond_transaction")
    var maxRefillAmount: String? = null,
    @SerializedName("fccTrxToken")
    var fccTrxToken: String? = null,
    @SerializedName("EPR_USER")
    var eprUsername: String? = null,
    @SerializedName("EPR_PASS")
    var eprPassword: String? = null,
    @SerializedName("EXT_USER")
    var extUsername: String? = null,
    @SerializedName("EXT_PASS")
    var extPassword: String? = null,
    @SerializedName("fuelling_type")
    var fuellingType: FuellingType? = null,
    @SerializedName("auto_db_backup")
    var autoDbBackup: Boolean? = false
)

@Keep
data class FuellingType(
    @SerializedName("amount")
    var amount : Boolean? = null,
    @SerializedName("quantity")
    var quantity : Boolean? = null,
    @SerializedName("full_tank")
    var full_tank : Boolean? = null
)
