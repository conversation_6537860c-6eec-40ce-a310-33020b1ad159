package app.rht.petrolcard.ui.loyalty.utils;

import android.graphics.Bitmap;
import android.graphics.Color;
import android.util.Log;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;

import org.jasypt.digest.StandardStringDigester;
import org.jasypt.exceptions.EncryptionOperationNotPossibleException;
import org.jasypt.util.text.BasicTextEncryptor;

import app.rht.petrolcard.utils.constant.AppConstant;

public class QRCode {

    private static final String TAG = QRCode.class.getSimpleName();

    public static Bitmap generateControlKey(String data) {
        Bitmap bmp = QRCode.toBitmap(QRCode.generateMatrix(encrypt(data)));
        return bmp;
    }

    public static Bitmap generateBitmap(String data){
        Bitmap bmp = QRCode.toBitmap(QRCode.generateMatrix(data));
        return bmp;
    }

    public static BitMatrix generateMatrix(String data) {
        BitMatrix bitMatrix = null;
        try {
            bitMatrix = new QRCodeWriter().encode(data, BarcodeFormat.QR_CODE, AppConstant.QR_CODE_SIZE, AppConstant.QR_CODE_SIZE);
        } catch (WriterException e) {
            e.printStackTrace();
        }
        return bitMatrix;
    }

    public static Bitmap toBitmap(BitMatrix matrix){
        int height = matrix.getHeight();
        int width = matrix.getWidth();
        Bitmap bmp = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565);
        for (int x = 0; x < width; x++){
            for (int y = 0; y < height; y++){
                bmp.setPixel(x, y, matrix.get(x,y) ? Color.BLACK : Color.WHITE);
            }
        }
        return bmp;
    }

    public static String toHash(String message) {
        StandardStringDigester digester = new StandardStringDigester();
        //digester.setAlgorithm("SHA-1");   // optionally set the algorithm
        digester.setAlgorithm("MD5");   // optionally set the algorithm
        digester.setIterations(50000);  // increase security by performing 50000 hashing iterations
        String digest = digester.digest(message);
        return digest;
    }

    public static String encrypt(String message) {
        BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
        textEncryptor.setPassword(AppConstant.ENCRYPTION_KEY);
        String myEncryptedText = textEncryptor.encrypt(message);
        Log.d(TAG, "encrypt: plain text => " + message);
        Log.d(TAG, "encrypt: encrypted text => " + myEncryptedText);
        Log.d(TAG, "encrypt: decrypted text => " + decrypt(myEncryptedText));
        return myEncryptedText;
    }

    public static String decrypt(String message) {

        try {

            BasicTextEncryptor textEncryptor = new BasicTextEncryptor();
            textEncryptor.setPassword(AppConstant.ENCRYPTION_KEY);
            String plainText = textEncryptor.decrypt(message);
            return plainText;

        }catch (EncryptionOperationNotPossibleException e ){
            e.getStackTrace()  ;
            return "";
        }

    }

}
