package app.rht.petrolcard.ui.modepay.activity;

import java.util.Comparator;
import java.util.Objects;

import app.rht.petrolcard.ui.modepay.model.ModePaymentModel;

public class ModePaySortList implements Comparator<ModePaymentModel> {
    @Override
    public int compare(ModePaymentModel obj1, ModePaymentModel obj2) {
        //return obj1.getPayment_name().compareTo(obj2.getPayment_name());
        return Objects.requireNonNull(obj1.getOrder()).compareTo(Objects.requireNonNull(obj2.getOrder()));
    }
}