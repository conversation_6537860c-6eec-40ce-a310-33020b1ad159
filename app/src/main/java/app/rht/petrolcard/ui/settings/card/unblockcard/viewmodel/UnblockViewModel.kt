package app.rht.petrolcard.ui.settings.card.unblockcard.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.settings.card.unblockcard.model.UnblockResponseModel
import app.rht.petrolcard.utils.AppPreferencesHelper


open class UnblockViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : BaseViewModel() {

    var unBlockCardObserver = MutableLiveData<BaseResponse<UnblockResponseModel>>()
    var unCardNotificationObserver = MutableLiveData<BaseResponse<String>>()


    fun unBlockCard(pan:String) {
        requestData(mNetworkService.unBlockCard(preferencesHelper.baseUrl+NetworkRequestEndPoints.UNLOCK_CARD,MainApp.sn,pan),
            {
                unBlockCardObserver.postValue(it)
            })
    }
    fun unlockCardNotification(pan:String,date:String) {
        requestData(mNetworkService.unlockCardNotification(preferencesHelper.baseUrl+NetworkRequestEndPoints.UNBLOCK_CARD_NOTIFICATION,MainApp.sn,pan,date),
            {
                unCardNotificationObserver.postValue(it)
            })
    }
}
