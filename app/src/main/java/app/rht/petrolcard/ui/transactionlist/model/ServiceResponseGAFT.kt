package app.rht.petrolcard.ui.transactionlist.model

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
@Keep
//GET AVAILABLE FUEL TRANSACTION
data class ServiceResponseGAFT(
    @SerializedName("ServiceResponse")
    val serviceResponse: ServiceResponseGAFTRX
)
@Keep
data class ServiceResponseGAFT2(
    @SerializedName("ServiceResponse")
    val serviceResponse: ServiceResponseGAFTRX2
)
@Keep
data class ServiceResponseGAFTRX(
    @SerializedName("ApplicationSender")
    val applicationSender: String,
    @SerializedName("FDCdata")
    val fDCdata: FDCdataGAFT,
//    @SerializedName("OverallResult")
//    val overallResult: String,
//    @SerializedName("RequestID")
//    val requestID: Int,
//    @SerializedName("RequestType")
//    val requestType: String,
//    @SerializedName("WorkstationID")
//    val workstationID: String,
//    @SerializedName("xmlns:xsd")
//    val xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    val xmlnsXsi: String
)
@Keep
data class ServiceResponseGAFTRX2(
//    @SerializedName("ApplicationSender")
//    val applicationSender: Int,
    @SerializedName("FDCdata")
    val fDCdata: FDCdataGAFT2,
//    @SerializedName("OverallResult")
//    val overallResult: String,
//    @SerializedName("RequestID")
//    val requestID: Int,
//    @SerializedName("RequestType")
//    val requestType: String,
//    @SerializedName("WorkstationID")
//    val workstationID: String,
//    @SerializedName("xmlns:xsd")
//    val xmlnsXsd: String,
//    @SerializedName("xmlns:xsi")
//    val xmlnsXsi: String
)
@Keep
data class FDCdataGAFT(
    @SerializedName("DeviceClass")
    val deviceClasses: ArrayList<DeviceClassGAFT>,
//    @SerializedName("FDCTimeStamp")
//    val fDCTimeStamp: String
)
@Keep
data class FDCdataGAFT2(
    @SerializedName("DeviceClass")
    val deviceClass: DeviceClassGAFT,
//    @SerializedName("FDCTimeStamp")
//    val fDCTimeStamp: String
)

@Keep
data class DeviceClassGAFT(
//    @SerializedName("DeviceID")
//    val deviceID: Int,
//    @SerializedName("ErrorCode")
//    val errorCode: String,
    @SerializedName("PumpNo")
    val pumpNo: Int,
//    @SerializedName("State")
//    val state: String,
    @SerializedName("TransactionSeqNo")
    val transactionSeqNo: Int,
//    @SerializedName("Type")
//    val type: String
)
