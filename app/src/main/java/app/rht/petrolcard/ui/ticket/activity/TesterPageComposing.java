package app.rht.petrolcard.ui.ticket.activity;

import android.content.Context;
import android.content.res.AssetManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Typeface;

import com.pax.gl.page.IPage;
import com.pax.gl.page.PaxGLPage;

import java.io.IOException;
import java.io.InputStream;

import app.rht.petrolcard.utils.paxutils.modules.printer.PrinterTester;

public class TesterPageComposing {
    Context context;
    public PaxGLPage iPaxGLPage;
    private Bitmap bitmap;

    private static final int FONT_BIG = 28;
    private static final int FONT_NORMAL = 20;
    private static final int FONT_BIGEST = 40;

    public TesterPageComposing(Context context) {
        this.context = context;
    }

    public void run() {
        iPaxGLPage = PaxGLPage.getInstance(context);
        IPage page = iPaxGLPage.createPage();
//         page.setTypeFace("/cache/data/public/neptune/Fangsong.ttf");
         page.setTypefaceObj(Typeface.createFromAsset(context.getAssets(), "fonts/Fangsong.ttf"));
        IPage.ILine.IUnit unit = page.createUnit();
        unit.setAlign(IPage.EAlign.CENTER);
        unit.setText("GLiPaxGlPage");
        page.addLine().addUnit().addUnit(unit).addUnit(page.createUnit().setText("Test").setAlign(IPage.EAlign.RIGHT));
        page.addLine().addUnit(" معرف المنتج", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD);
        page.addLine().addUnit("商户存根", FONT_NORMAL,IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_UNDERLINE);
        page.addLine().addUnit("商户存根", FONT_NORMAL,IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD | IPage.ILine.IUnit.TEXT_STYLE_UNDERLINE);
        page.addLine().addUnit("商户存根", FONT_NORMAL,IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL);
        page.addLine()
                .addUnit("商户存根", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD | IPage.ILine.IUnit.TEXT_STYLE_UNDERLINE, 1);
        page.addLine().addUnit("商户存根", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL, 1);
        page.addLine().addUnit("-----------------------------------------", FONT_NORMAL);
        page.addLine().addUnit("商户名称: " + "百富计算机技术", FONT_NORMAL);
        page.addLine().addUnit("商户编号: " + "111111111111111", FONT_NORMAL);

        page.addLine().addUnit("终端编号:", 40).addUnit("操作员号:", 10, IPage.EAlign.RIGHT);
        page.addLine().addUnit("22222222", FONT_NORMAL).addUnit("01", FONT_NORMAL, IPage.EAlign.RIGHT);

        page.addLine().addUnit("卡号：", FONT_NORMAL);
        page.addLine().addUnit("5454545454545454", FONT_BIG);

        page.addLine().addUnit("交易类型: " + "消费", FONT_BIG);

        page.addLine().addUnit("流水号:", FONT_NORMAL).addUnit("批次号:", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("123456", FONT_NORMAL).addUnit("000001", FONT_NORMAL, IPage.EAlign.RIGHT);

        page.addLine().addUnit("授权码:", FONT_NORMAL, IPage.EAlign.LEFT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL, 1)
                .addUnit("参考号:", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL, 1);
        page.addLine().addUnit("987654", FONT_BIGEST, IPage.EAlign.LEFT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL, 1)
                .addUnit("012345678912", FONT_NORMAL, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_NORMAL);

        page.addLine().addUnit("日期/时间:" + "2016/06/13 12:12:12", FONT_NORMAL);
        page.addLine().addUnit("金额:", FONT_BIG);
        page.addLine().addUnit("RMB 1.00", FONT_BIG, IPage.EAlign.RIGHT, IPage.ILine.IUnit.TEXT_STYLE_BOLD);

        page.addLine().addUnit("备注:", FONT_NORMAL);
        page.addLine().addUnit("----------------持卡人签名---------------", FONT_NORMAL);
//        page.addLine().addUnit(getImageFromAssetsFile("pt.bmp"));
        page.addLine().addUnit("-----------------------------------------", FONT_NORMAL);
        page.addLine()
                .addUnit("本人确认已上交易, 同意将其计入本卡账户\n\n\n\n\n", FONT_NORMAL, IPage.EAlign.CENTER, IPage.ILine.IUnit.TEXT_STYLE_UNDERLINE);

        page.addLine().addUnit("交易类型", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("笔数", FONT_NORMAL, IPage.EAlign.LEFT)
                .addUnit("金额", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("-----------------------------------------", FONT_NORMAL);
        page.addLine().addUnit("消费", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("20", FONT_NORMAL, IPage.EAlign.LEFT)
                .addUnit("12345678901234.00", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("退货", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("40", FONT_NORMAL, IPage.EAlign.LEFT)
                .addUnit("123.00", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("激活", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("80", FONT_NORMAL, IPage.EAlign.LEFT).addUnit();
        page.addLine().addUnit("预授权完成请求撤销", FONT_NORMAL, IPage.EAlign.RIGHT).addUnit("120", FONT_NORMAL, IPage.EAlign.LEFT)
                .addUnit("80.00", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("预授权完成请求", FONT_NORMAL, IPage.EAlign.CENTER).addUnit("120", FONT_NORMAL, IPage.EAlign.LEFT)
                .addUnit("80.00", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("--------------------------------------\n\n", FONT_NORMAL);

        page.addLine().addUnit("TEST 1", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("TEST 2", FONT_NORMAL, IPage.EAlign.CENTER)
                .addUnit("TEST 3", FONT_NORMAL, IPage.EAlign.CENTER).addUnit("TEST 4", FONT_NORMAL, IPage.EAlign.RIGHT);

        page.addLine().addUnit("TEST 5", FONT_NORMAL, IPage.EAlign.LEFT).addUnit("TEST 6", FONT_NORMAL, IPage.EAlign.CENTER)
                .addUnit("TEST 7", FONT_NORMAL, IPage.EAlign.CENTER).addUnit("TEST 8", FONT_NORMAL, IPage.EAlign.RIGHT)
                .addUnit("TEST 9", FONT_NORMAL, IPage.EAlign.RIGHT);
        page.addLine().addUnit("\n\n\n\n", FONT_NORMAL);
        int width = 384;
        Bitmap bitmap = iPaxGLPage.pageToBitmap(page, width);

        setBitmap(bitmap);

    }

    public Bitmap getImageFromAssetsFile(String fileName) {
        Bitmap image = null;
        AssetManager am = context.getResources().getAssets();
        try {
            InputStream is = am.open(fileName);
            image = BitmapFactory.decodeStream(is);
            is.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return image;

    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;

    }

    public Bitmap getBitmap() {
        return this.bitmap;
    }
}