package app.rht.petrolcard.ui.settings.operations.activity

import android.app.DatePickerDialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.Typeface
import android.os.*
import android.text.Html
import android.view.View
import android.widget.DatePicker
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import androidx.core.content.ContextCompat
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityDuplicateTransactionsBinding
import app.rht.petrolcard.databinding.ActivityPendingTransactionsBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.operations.model.PendingTransactionModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import com.shagi.materialdatepicker.date.DatePickerFragmentDialog
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.SQLException
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.*


class PendingTransactionsActivity : BaseActivity<CommonViewModel>(CommonViewModel::class),RecyclerViewArrayAdapter.OnItemClickListener<PendingTransactionModel> {
    private var TAG= PendingTransactionsActivity::class.java.simpleName
    private lateinit var mBinding: ActivityPendingTransactionsBinding
    private var transactionList: ArrayList<PendingTransactionModel> = ArrayList<PendingTransactionModel>()
    private lateinit var adapter : RecyclerViewArrayAdapter<PendingTransactionModel>
    private var intentExtrasModel: IntentExtrasModel? = null
    var selectedDate = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        //setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_pending_transactions)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()

        selectedDate = SimpleDateFormat("yyyy-MM-dd").format(Date())
        val selectedText =  SimpleDateFormat("yyyy-MM-dd").format(Date())
        mBinding.selectDate.text = selectedText.toString()
        getTransactionDetails()
        getIntentExtras()
        mBinding.selectDate.setOnClickListener {
            showCalendar()
        }
    }
    fun getIntentExtras() {
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
    }
    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.pending_transactions)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish() }
    }

    private fun setupRecyclerview() {
        mBinding.mListView.setHasFixedSize(true)
        resetView()
        log(TAG,"transactionList:::"+gson.toJson(transactionList))
        adapter = RecyclerViewArrayAdapter(transactionList,this)
        mBinding.mListView.adapter = adapter
        adapter.notifyDataSetChanged()
        adapter.setContext(this)

    }
    fun resetView()
    {
        if(!transactionList.isNullOrEmpty())
        {
            mBinding.emptyTXT.visibility = View.GONE
            mBinding.mListView.visibility = View.VISIBLE
        }
        else {
            mBinding.emptyTXT.visibility = View.VISIBLE
            mBinding.mListView.visibility = View.GONE
        }
    }
    override fun setObserver() {
    }
    override fun onItemClick(view: View, model: PendingTransactionModel) {
        MultiClickPreventer.preventMultiClick(view)
        if(view.id == R.id.markComplete)
        {
            try {
                val mTransactionTaxiDAO = TransactionDao()
                mTransactionTaxiDAO.open()
                val transactions = mTransactionTaxiDAO.updateTransactionStatus(model.transactionModel!!.id!!)
                showDialog("Updated Successfully",getString(R.string.update_successfully_goto_duplicate))
                getTransactionDetails()
                mTransactionTaxiDAO.close()
            } catch (Ex: SQLException) {
                Ex.printStackTrace()
            }
        }

    }
    fun getTransactionDetails()
    {
        transactionList.clear()
        try {
            val mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            val transactions = mTransactionTaxiDAO.getNotTelecollectedTransactions(selectedDate)
            mTransactionTaxiDAO.close()
            for(trans in transactions!!)
            {
                var transactionStatus = getString(R.string.pending)
                if(trans.transactionStatus == 1 && trans.flagTelecollecte == 0)
                {
                    transactionStatus = getString(R.string.completed)
                }
                else if(trans.categoryId == PRODUCT.FUEL_CATEGORY_ID && trans.sequenceController.isNullOrEmpty())
                {
                    trans.transactionStatus = 2
                   // transactionStatus = getString(R.string.cancelled)
                }
                val prod = getProduct(trans)
                if(prod != null)
                {
                    transactionList.add(PendingTransactionModel(trans,getProduct(trans)!!,transactionStatus))

                }
            }
            setupRecyclerview()
        } catch (Ex: SQLException) {
            Ex.printStackTrace()
        }
    }
    private fun getProduct(transaction:TransactionModel) : ProductModel?{
        var product : ProductModel? = null
        runOnUiThread {
            val productsDAO = ProductsDao()
            productsDAO.open()
            product = productsDAO.getProductById(transaction.idProduit!!)
            productsDAO.close()
            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product!!.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product!!.libelle = fusionProductName
                }
            }

        }
        return product
    }

    private lateinit var mCalendar: Calendar
    private fun showCalendar() {
        mCalendar = Calendar.getInstance()
        val mYear: Int = mCalendar.get(Calendar.YEAR)
        val mMonth: Int = mCalendar.get(Calendar.MONTH)
        val mDay: Int = mCalendar.get(Calendar.DAY_OF_MONTH)
        val mDatePicker = DatePickerDialog(this,
            { _: DatePicker?, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                mCalendar.set(Calendar.YEAR, year)
                mCalendar.set(Calendar.MONTH, monthOfYear)
                mCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                var mon = monthOfYear + 1
                var mon2 = mon.toString()

                var day = dayOfMonth
                var day2 = dayOfMonth.toString()
                if (day <= 9) {
                    day2 = "0$day"
                }
                if (mon <= 9) {
                    mon2 = "0$mon"
                }
                selectedDate = "$year-${mon2}-$day2"
                mBinding.selectDate.text = selectedDate
                getTransactionDetails()
            }, mYear, mMonth, mDay
        )
        mDatePicker.datePicker.maxDate= System.currentTimeMillis()
        mDatePicker.show()
    }
}