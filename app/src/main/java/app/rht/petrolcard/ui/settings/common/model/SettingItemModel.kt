package app.rht.petrolcard.ui.settings.common.model

import android.graphics.Color
import app.rht.petrolcard.baseClasses.model.BaseModel
import androidx.annotation.Keep
@Keep
data class SettingItemModel(
    val iconResource: Int,
    val iconBgColor: String,
    val title: String,
    val id: String,
)  : BaseModel() {
    fun getColor(): Int {
        return Color.parseColor(iconBgColor)
    }

    fun getIcon(): Int {
        return iconResource
    }
}