package app.rht.petrolcard.ui.loyalty.activity

import android.content.Intent
import android.os.Bundle
import android.os.RemoteException
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityNfcScanBinding
import app.rht.petrolcard.ui.badge.activity.ManagerTagActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.CARD_NUMBER
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_ACTIVATION_REQUEST
import app.rht.petrolcard.utils.paxutils.modules.ped.PedTester
import app.rht.petrolcard.utils.paxutils.modules.picc.PiccTester
import com.afollestad.materialdialogs.MaterialDialog
import com.pax.dal.entity.EPiccType
import com.usdk.apiservice.aidl.DeviceServiceData
import com.usdk.apiservice.aidl.UDeviceService
import com.usdk.apiservice.aidl.constants.RFDeviceName
import com.usdk.apiservice.aidl.data.BytesValue
import com.usdk.apiservice.aidl.emv.*
import com.usdk.apiservice.aidl.icreader.DriverID
import com.usdk.apiservice.aidl.icreader.OnInsertListener
import com.usdk.apiservice.aidl.icreader.UICCpuReader
import com.usdk.apiservice.aidl.rfreader.OnPassAndActiveListener
import com.usdk.apiservice.aidl.rfreader.RFError
import com.usdk.apiservice.aidl.rfreader.URFReader
import kotlinx.android.synthetic.main.dialog_message.*
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.HEX
import java.lang.ref.WeakReference
import java.util.*

class NfcScanActivity : BaseActivity<CommonViewModel>(CommonViewModel::class)  {

    private lateinit var mBinding: ActivityNfcScanBinding
    private val TAG: String = NfcScanActivity::class.java.simpleName
    private var mBankCard: BankCard? = null
    var tagNFC: String? = null
    private var piccType: EPiccType? = null
    //to register NFC
    var cardTagNFC: String? = null

    // LANDI CARD
    private var emv: UEMV? = null
    private var icCpuReader: UICCpuReader? = null
    private var deviceService: UDeviceService? = null
    private var rfReader: URFReader? = null

    private var tagUID: String? = null

    var pan: String? = null
    var badge: String? = null

    private var isActivationRequest = false
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_nfc_scan)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()

        if(intent.hasExtra(CARD_NUMBER))
            pan = intent.getStringExtra(CARD_NUMBER)

        if(intent.hasExtra(LOYALTY_ACTIVATION_REQUEST))
           isActivationRequest = intent.getBooleanExtra(LOYALTY_ACTIVATION_REQUEST,false)

        setupToolbar()
        mBinding.prompt.text = getString(R.string.scan_your_nfc_tag)

        //waitForSomeTime();
        if (BuildConfig.POS_TYPE != "LANDI") {
            //mScanningNFCTask = ScanningNFCTask()
            //mScanningNFCTask!!.execute()
            scanNfc()
        } else {
            try {
                rfReader = getRFCardReaderInner()
                searchAndActive()
                //searchCardNFC();
            } catch (ex: RemoteException) {
                ex.printStackTrace()
                log(TAG, ex.toString() + ExceptionUtils.getStackTrace(ex))
            }
        }

    }

    lateinit var readCardTask : ScanningNFCTask
    private fun scanNfc(){
        cancelPreviousNfcScanIfRunning()
        readCardTask = ScanningNFCTask()
        readCardTask.execute()
    }
    private fun cancelPreviousNfcScanIfRunning(){
        try {
            if(::readCardTask.isInitialized) {
                readCardTask.cancel(true)
            }
        } catch (e:Exception){e.printStackTrace()}
    }
    override fun onStop() {
        cancelPreviousNfcScanIfRunning()
        super.onStop()
    }

    override fun onBackPressed() {
        //super.onBackPressed()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.nfc_verification)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            finish()
        }

    }



    override fun setObserver() {

    }

    var mScanningNFCTask: ScanningNFCTask? = null
    inner class ScanningNFCTask internal constructor() :
        CoroutineAsyncTask<Void?, Void?, Int>() {
        var resultat = 0
        var respdata: ByteArray = ByteArray(28)
        var resplen: IntArray = IntArray(1)
        var retvalue: Int
        var respdata1: ByteArray
        var resplen1: IntArray
        var retvalue1: Int
        var sn: ByteArray
        var pes: IntArray
        var resSN: Int
        val responseLengthCard = IntArray(1)
        val responseDataCard = ByteArray(80)
        init {
            // TODO: initialization of the parameters I want to initialize
            retvalue = 0
            resplen1 = IntArray(1)
            respdata1 = ByteArray(28)
            retvalue1 = 0
            sn = ByteArray(16)
            pes = IntArray(1)
            resSN = 0
        }

        override fun doInBackground(vararg params: Void?): Int {
            setBeep()
            resultat = 1

            try{
                if(BuildConfig.POS_TYPE == "B_TPE") {
                    mBankCard = BankCard(applicationContext)
                    tagUID = ""
                    resultat = 1
                    tagNFC = "xxx TEST ONLY DELETE PLEASE"

                    if (mBankCard != null)
                        retvalue = mBankCard!!.readCard(
                            BankCard.CARD_TYPE_NORMAL,
                            BankCard.CARD_MODE_PICC,
                            60,
                            respdata,
                            resplen,
                            AppConstant.TPE_APP
                        )

                    if (mBankCard != null) resSN = mBankCard!!.getCardSNFunction(sn, pes)
                    tagUID = HEX.bytesToHex(sn)

                    if (!tagUID.isNullOrEmpty()) {
                        log("tagUID =>", "tagUID--->>>$tagUID")
                    } else {
                        log("tagUID =>", "tagUID--->>>" + null)
                    }
                }
                else if (BuildConfig.POS_TYPE == "PAX"){
                    var i = 0
                    piccType = EPiccType.INTERNAL
                    PiccTester.getInstance(piccType!!).open()
                    var tag: String = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    while (tag.equals("", ignoreCase = true) && i < 20) {
                        i++
                        Thread.sleep(500)
                        tag = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    }

                    if (tag.isNotEmpty())
                         tagUID = tag

                    if (!tagUID.isNullOrEmpty()) {
                        log("tagUID =>", "tagUID--->>>$tagUID")
                    } else {
                        log("tagUID =>", "tagUID--->>>" + null)
                        return 2
                    }
                }
            }
            catch (e :RemoteException ) {
                e.printStackTrace()
                log(TAG, e.toString() + ExceptionUtils.getStackTrace(e))
                return 1
            }
            catch (e :InterruptedException) {
                e.printStackTrace()
                log(TAG, e.toString() + ExceptionUtils.getStackTrace(e))
                return 1
            }

            var nfcLength = 0
            var minLength = 5
            var maxLength = 14

            if(BuildConfig.POS_TYPE == "PAX" && tagUID != null)
            {
                nfcLength = tagUID!!.length
            }
            else if(BuildConfig.POS_TYPE == "B_TPE")
            {
                nfcLength =  pes[0]
            }
            if(prefs.getReferenceModel()!!.station!!.nfc_read_min_length != null) {
                minLength = prefs.getReferenceModel()!!.station!!.nfc_read_min_length!!
                maxLength = prefs.getReferenceModel()!!.station!!.nfc_read_max_length!!
            }
            if (nfcLength in minLength..maxLength) {
                setBeep()
                tagNFC = if (tagUID!!.length >= 14) {
                    tagUID!!.substring(0, 14)
                } else {
                    tagUID!!
                }
                log(TAG, "*** NFC Activity => The NFC TAG has been detected!")
                log(TAG, "*** NFC Activity => The NFC TAG is => $tagNFC")

                if (isActivationRequest && cardTagNFC == null) {
                    try {
                        publishProgress()

                        if (BuildConfig.POS_TYPE == "B_TPE") {
                            mBankCard = BankCard(applicationContext)
                            retvalue = mBankCard!!.readCard(
                                BankCard.CARD_TYPE_NORMAL,
                                BankCard.CARD_MODE_ICC,
                                60,
                                responseDataCard,
                                responseLengthCard,
                                AppConstant.TPE_APP
                            )
                        }
                        else if (BuildConfig.POS_TYPE == "PAX") {
                            UtilsCardInfo.connectPAX()
                        }
                         if (tagNFC != null && Utils.byteArrayToHex(responseDataCard).substring(0, 2) == "05" || Utils.byteArrayToHex(responseDataCard).substring(0, 2) == "07" || BuildConfig.POS_TYPE == "LANDI" || BuildConfig.POS_TYPE == "PAX") {
                            var key1: String? = null
                            val infoCarte: String = UtilsCardInfo.getCardInfo(mBankCard, icCpuReader, this@NfcScanActivity)

                            if (infoCarte.isNotEmpty()) pan = infoCarte.substring(0, 19)

                            if (pan != null) {
                                log(TAG, "PAN ---> $pan")
                                if (BuildConfig.POS_TYPE == "B_TPE")
                                    key1 = UtilsCardInfo.genKey(mCore, pan)
                                else if (BuildConfig.POS_TYPE == "PAX")
                                    key1 = UtilsCardInfo.genKeyPAX(pan)
                                else {
                                    if (pan!!.endsWith("00148"))
                                        key1 = "6BB75C0B6EF18F44"
                                    else if (pan!!.endsWith("00028"))
                                            key1 = "11D85F74A9F8811E"
                                } // "F17FACC7383CD1BC"  PAN : 000324 || "358BDADAB21E9041"  PAN : 000258  ||| PAN : 00148 / key1 = "6BB75C0B6EF18F44" ||| PAN : 00028 / key1 = "11D85F74A9F8811E"
                            }
                            if (key1 != null && UtilsCardInfo.externalAuth1(
                                    mBankCard,
                                    icCpuReader,
                                    key1,
                                    this@NfcScanActivity
                                ) &&
                                UtilsCardInfo.externalAuth2(
                                    mBankCard,
                                    icCpuReader,
                                    key1,
                                    this@NfcScanActivity
                                )
                            ) {
                                var result: String? = UtilsCardInfo.readBinaryFile(
                                    mBankCard,
                                    icCpuReader,
                                    "2F20",
                                    "5F",
                                    this@NfcScanActivity
                                )
                                log(TAG, " result readBinaryFile --->$result")
                                if (result!!.isEmpty())
                                    return -4

                                var numberofnfcTag = result.substring(result.length - 2, result.length)
                                log(TAG, " NumberofnfcTag --->$numberofnfcTag")
                                if (numberofnfcTag == "00" || numberofnfcTag == "FF")
                                    log(TAG, " Tag NFC not activated")
                                if (numberofnfcTag == "FF")
                                    numberofnfcTag = "00"

                                val replaced = result.substring(0, 14)

                                result = result.replaceFirst(replaced.toRegex(), tagNFC!!)
                                val datacompined1: String = result

                                log(TAG, " Tag NFC datacompined 1 ---> " + datacompined1 + " length :" + datacompined1.length)
                                UtilsCardInfo.updateBinaryFile(
                                    mBankCard,
                                    icCpuReader,
                                    "2F20",
                                    "5F",
                                    datacompined1.uppercase(Locale.getDefault()),
                                    this@NfcScanActivity
                                )
                            } else {
                                return -4
                            }
                        } else {
                            // Carte non insérée ou mal insérée
                            try {
                                if (BuildConfig.POS_TYPE == "B_TPE") mBankCard!!.breakOffCommand()
                            } catch (e: RemoteException) {
                                e.printStackTrace()
                                return 2
                            }
                            return -4
                        }
                    } catch (e: RemoteException) {
                        e.printStackTrace()
                        return 2
                    } catch (e: Exception){
                        e.printStackTrace()
                        return 2
                    }
                }
                else {
                    if (cardTagNFC != null && cardTagNFC != tagNFC)
                        return 0
                }

            } else {
                resultat = 2
            }

            log(TAG,"## ## $tagUID")

            return resultat
        }

        override fun onProgressUpdate(vararg values: IntArray) {
            super.onProgressUpdate(*values)
            mBinding.prompt.text = getString(R.string.please_remove_your_card)
        }

        override fun onCancelled() {
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") mBankCard!!.breakOffCommand()
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }

        override fun onPostExecute(result: Int?) {
            super.onPostExecute(result)
            when(result!!){
                0 ->{
                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag))

                }
                -4 -> {
                    gotoAbortMessageActivity(getString(R.string.invalid_card),getString(R.string.card_reading_failed))
                }
                2 -> {
                    try {
                        MyMaterialDialog(
                            context = this@NfcScanActivity,
                            title = getString(R.string.confirm),
                            message = getString(R.string.do_you_want_to_scan_again),
                            positiveBtnText = getString(R.string.yes),
                            negativeBtnText = getString(R.string.no),
                            listener = object : MyMaterialDialogListener {
                                override fun onPositiveClick(dialog: MaterialDialog) {
                                    //mScanningNFCTask = ScanningNFCTask()
                                    //mScanningNFCTask!!.execute()
                                    scanNfc()
                                }

                                override fun onNegativeClick(dialog: MaterialDialog) {
                                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.transaction_cancelled))
                                }
                            }
                        )
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                /*10,11,20*/
                else -> {
                    val resultIntent = Intent()
                    resultIntent.putExtra(AppConstant.CARD_NFC_TAG,tagNFC)
                    setResult(RESULT_OK, resultIntent)
                    finish()
                }

            }
        }
    }

    @Throws(android.os.RemoteException::class)
    private fun getRFCardReaderInner(): URFReader {
        if (rfReader == null) {
            deviceService = MainApp.getDeviceService()
            val param = Bundle()
            param.putString(DeviceServiceData.RF_DEVICE_NAME, RFDeviceName.INNER)
            rfReader = URFReader.Stub.asInterface(deviceService!!.getRFReader(param))
        }
        return rfReader!!
    }

    @Throws(RemoteException::class)
    fun searchAndActive() {
        rfReader!!.searchCardAndActivate(object : OnPassAndActiveListener.Stub() {
            @Throws(RemoteException::class)
            override fun onActivate(responseData: ByteArray) {
                // TODO Activate successful event handling.
                log(TAG, "=> onActivate | cardType = ")
                tagUID = HEX.bytesToHex(responseData)

                searchCard()
            }

            @Throws(RemoteException::class)
            override fun onFail(error: Int) {
                // TODO Error handling, error see RFError.
                log(TAG,"=> onFail: Error ==  $error")
                //UtilsCard.beep(mCore,10);
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag))

            }
        })
    }

    @Throws(RemoteException::class)
    fun stopSearch() {
        rfReader!!.stopSearch()
    }

    @Throws(RemoteException::class)
    fun getICCpuReader(): UICCpuReader? {
        if (icCpuReader == null) {
            val iBinder = MainApp.getDeviceService()!!.getICReader(DriverID.ICCPU, null)
            icCpuReader = UICCpuReader.Stub.asInterface(iBinder)
        }
        return icCpuReader
    }

    @Throws(RemoteException::class)
    fun searchCard() {
        icCpuReader = getICCpuReader()
        icCpuReader!!.searchCard(object : OnInsertListener.Stub() {
            @Throws(RemoteException::class)
            override fun onCardInsert() {

                // TODO Search card success event handling, such as: power up.
                startEMV()
                emv!!.respondCard()
                UtilsCardInfo.powerUpLANDI(icCpuReader)
                log(TAG, "CARD INSERTED !")
                UtilsCardInfo.powerONLANDI(icCpuReader)
                if (UtilsCardInfo.isCardInLANDI(icCpuReader)) {
                    //ScanningNFCTask().execute()
                    scanNfc()
                } else {
                    log(TAG, "CARD NOT INSERTED")
                }
            }

            @Throws(RemoteException::class)
            override fun onFail(error: Int) {
                // TODO Error handling, error see ICError.
                log(TAG, "CARD INSERT ERROR !")
            }
        })
    }

    @Throws(RemoteException::class)
    fun getEMV(): UEMV? {
        if (emv == null) {
            emv = UEMV.Stub.asInterface(MainApp.getDeviceService()!!.emv)
        }
        return emv
    }

    @Throws(RemoteException::class)
    fun startEMV() {
        val param = Bundle()
        emv = getEMV()
        emv!!.startEMV(param, emvEventHandler)
    }

    private val emvEventHandler: EMVEventHandler = object : EMVEventHandler.Stub() {
        // EMV initialization complete (no response required)
        @Throws(RemoteException::class)
        override fun onInitEMV() {
        }

        // The kernel requests the card to perform the corresponding processing according to the flag meaning
        @Throws(RemoteException::class)
        override fun onWaitCard(flag: Int) {
            /* switch (flag) {
                case WaitCardFlag.ISS_SCRIPT_UPDATE:
                case WaitCardFlag.SHOW_CARD_AGAIN:
                    // TODO 1. The interface prompts the user to re-present the card; 2. Call the searchCard to re-search the contactless card; 3. When the contactless card is detected, call the respondCard to notify the SDK.
                    break;
                case WaitCardFlag.EXECUTE_CDCVM:
                    // TODO 1. The interface prompts the user to re-present the card; 2. Call halt to close contactless module; 3. Wait 1000ms - 1200ms,then call the searchCard to re-search the contactless card; 4. When the contactless card is detected, call the respondCard to notify the SDK.
                    break;
            }*/
        }

        // Application select
        @Throws(RemoteException::class)
        override fun onAppSelect(reSelected: Boolean, candList: List<CandidateAID>) {
            if (candList.size > 1) {
                // TODO Multi-application: in the UI thread pop-up dialog box for the user to select, and the selected AID response to the SDK
            } else {
                // Single application: AID response directly to the SDK
                val aid = candList[0].aid
            }
        }

        // Final application select
        @Throws(RemoteException::class)
        override fun onFinalSelect(finalData: FinalData) {
            // TODO 1. Call setTLVList set EMV transaction parameters; 2. Call respondEvent to wake up the kernel
        }

        // Kernel output (no response required)
        @Throws(RemoteException::class)
        override fun onSendOut(ins: Int, data: ByteArray) {
            // In order to improve the user experience, EMV standard borrowing process, you can get the card number here
        }

        // Read application record
        @Throws(RemoteException::class)
        override fun onReadRecord(record: CardRecord) {
            // Received this event on behalf of the card record has been completed, according to the main account number, public key index and other information to do the relevant operations.
            emv!!.respondEvent(null)
        }

        // Card holder verify request
        @Throws(RemoteException::class)
        override fun onCardHolderVerify(cvm: CVMMethod) {
            // TODO 1. According to cvm.getCVM() for the corresponding cardholder authentication; 2. Call respondEvent will verify the results to respond to the kernel
        }

        // Offline pin verify request
        @Throws(RemoteException::class)
        override fun onVerifyOfflinePin(
            flag: Int,
            random: ByteArray,
            publicKey: CAPublicKey,
            result: OfflinePinVerifyResult
        ) {
            // Be sure to call the Pinpad module to complete the offline PIN verification and set the result to "result"
        }

        // Online process request
        @Throws(RemoteException::class)
        override fun onOnlineProcess(transData: TransData) {
            // TODO 1. Initiate an online process; 2. Call respondEvent to respond to the kernel results online
        }

        // EMV process end notification
        @Throws(RemoteException::class)
        override fun onEndProcess(result: Int, transData: TransData) {
            // TODO Process end processing, refer to the《LANDI Light EMV Application Programming Guide》 5.13 end of the transaction processing
        }

        // Kernel input request (wait for the upper layer response, and call respondEvent interface to wake up the kernel)
        @Throws(RemoteException::class)
        override fun onObtainData(ins: Int, data: ByteArray) {
        }

        @Throws(RemoteException::class)
        override fun onCardChecked(i: Int) {
        }
    }

    @Throws(RemoteException::class)
    fun activate(cardType: Int) {
        if (rfReader!!.isExist) {
            val response = BytesValue()
            //rfReader.readBlock( 1,response);
            val ret = rfReader!!.activate(cardType, response)
            if (ret != RFError.SUCCESS) {
                // activate fail, ret see RFError
                log(TAG, "=> Onfail activate NFC CARD  =  ")
            } else {
                val responseData = response.data

                // TODO Activate successful event handling.
                log(TAG, "=> onActivate | cardType = ")
                tagUID = HEX.bytesToHex(responseData) // 040031B479C93508
                try {
                    stopSearch()
                    rfReader!!.halt()
                } catch (ex: RemoteException) {
                    ex.printStackTrace()
                }
                //mScanningNFCTask = ScanningNFCTask()
                //mScanningNFCTask!!.execute()
                scanNfc()
            }
        }
    }



}