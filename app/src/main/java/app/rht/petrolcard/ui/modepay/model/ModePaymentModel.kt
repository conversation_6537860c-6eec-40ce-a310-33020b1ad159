package app.rht.petrolcard.ui.modepay.model

import android.graphics.Color
import android.os.Parcel
import android.os.Parcelable

import app.rht.petrolcard.baseClasses.model.BaseModel
import androidx.annotation.Keep
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.reference.model.NozzelsModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import com.google.gson.annotations.SerializedName
import kotlinx.android.parcel.Parcelize

@Keep
class ModePaymentModel(
    @SerializedName("payment_id")
    var payment_id: Int=0,
    @SerializedName("payment_name")
    var payment_name: String?="",
    @SerializedName("color")
    var color: String?="",
    @SerializedName("icon")
    var icon: Int=0,
    @SerializedName("image_url")
    var image_url: String?="",
    @SerializedName("order")
    var order: Int? = 1,
    @SerializedName("preset_amount")
    var preset_amount: Double?=0.0,
    @SerializedName("fcc_payment_id")
    var fcc_payment_id: Int?=0,
    @SerializedName("sub_payments")
    var sub_payments : ArrayList<SubPayment>? = ArrayList(),
    @SerializedName("isAvailable")
    var isAvailable : Boolean? = false,
    @SerializedName("reference_no_size")
    var referenceNoSize : Int? = 14,
    @SerializedName("enter_amount")
    var isEnterAmountEnabled:Boolean? = true,
    @SerializedName("enter_quantity")
    var isEnterQtyEnabled:Boolean? = true,
    @SerializedName("enter_full_tank")
    var isEnterFullTankEnabled:Boolean? = true,
) : BaseModel(), Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readInt(),
        parcel.readString(),
        parcel.readString(),
        parcel.readInt(),
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Double::class.java.classLoader) as? Double,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.createTypedArrayList(SubPayment),
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,
        parcel.readValue(Boolean::class.java.classLoader) as? Boolean,)

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeInt(payment_id)
        parcel.writeString(payment_name)
        parcel.writeString(color)
        parcel.writeInt(icon)
        parcel.writeString(image_url)
        parcel.writeValue(order)
        parcel.writeValue(preset_amount)
        parcel.writeValue(fcc_payment_id)
        parcel.writeTypedList(sub_payments)
        parcel.writeValue(isAvailable)
        parcel.writeValue(referenceNoSize)
        parcel.writeValue(isEnterAmountEnabled)
        parcel.writeValue(isEnterQtyEnabled)
        parcel.writeValue(isEnterFullTankEnabled)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<ModePaymentModel> {
        override fun createFromParcel(parcel: Parcel): ModePaymentModel {
            return ModePaymentModel(parcel)
        }

        override fun newArray(size: Int): Array<ModePaymentModel?> {
            return arrayOfNulls(size)
        }
    }

    fun getColorInt(): Int {
        var c = "#ffffff"
        if(color!!.isNotEmpty())
        {
            c= color as String
        }
        return Color.parseColor(c)

    }

}

class SubPayment(
    @SerializedName("card_type")
    var card_type: Int?,
    @SerializedName("fcc_payment_id")
    var fcc_payment_id: Int?,
    @SerializedName("payment_name")
    var payment_name: String?,
    @SerializedName("preset_amount")
    var preset_amount: Int?
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readValue(Int::class.java.classLoader) as? Int,
        parcel.readString(),
        parcel.readValue(Int::class.java.classLoader) as? Int)

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeValue(card_type)
        parcel.writeValue(fcc_payment_id)
        parcel.writeString(payment_name)
        parcel.writeValue(preset_amount)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<SubPayment> {
        override fun createFromParcel(parcel: Parcel): SubPayment {
            return SubPayment(parcel)
        }

        override fun newArray(size: Int): Array<SubPayment?> {
            return arrayOfNulls(size)
        }
    }
}
