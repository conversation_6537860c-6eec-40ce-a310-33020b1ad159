package app.rht.petrolcard.ui.ticket.activity

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Color
import android.graphics.Paint
import android.net.Uri
import android.os.*
import android.provider.MediaStore
import android.text.Html
import android.util.Log
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.AuditDao
import app.rht.petrolcard.database.baseclass.ESDSignatureDao
import app.rht.petrolcard.database.baseclass.FCCTransactionsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityTicketBinding
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.esdsign.activity.EsdSignActivity
import app.rht.petrolcard.ui.loyalty.utils.QRCode
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.qrcodeticket.activity.QrCodeTicketActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.ticket.fragment.DialogLoyaltyFragment
import app.rht.petrolcard.ui.ticket.fragment.LoyaltyDialogListener
import app.rht.petrolcard.ui.ticket.fragment.LoyaltyDialogStatus
import app.rht.petrolcard.ui.ticket.model.FuelProductModel
import app.rht.petrolcard.ui.timssign.activity.TIMSSignActivity
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.constant.*
import app.rht.petrolcard.utils.constant.AppConstant.AFTER_TRX_MODE
import app.rht.petrolcard.utils.constant.AppConstant.ATTENDANT
import app.rht.petrolcard.utils.constant.AppConstant.AUTH_CODE
import app.rht.petrolcard.utils.constant.AppConstant.CARD_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.CUSTOMER
import app.rht.petrolcard.utils.constant.AppConstant.LOYALTY_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.NO
import app.rht.petrolcard.utils.constant.AppConstant.OFFLINE_TRX_MODE
import app.rht.petrolcard.utils.constant.AppConstant.PRINT_RECEIPT_MANDATORY
import app.rht.petrolcard.utils.constant.AppConstant.PRINT_RECEIPT_NOT_REQUIRED
import app.rht.petrolcard.utils.constant.AppConstant.PRINT_RECEIPT_REQUIRED
import app.rht.petrolcard.utils.constant.AppConstant.UN_ATTENDANT_MODE
import app.rht.petrolcard.utils.constant.AppConstant.VISA_VALUE
import app.rht.petrolcard.utils.constant.AppConstant.VOUCHER_NO
import app.rht.petrolcard.utils.constant.AppConstant.YES
import app.rht.petrolcard.utils.constant.Workflow.TAXI_FUEL
import app.rht.petrolcard.utils.fuelpos.*
import app.rht.petrolcard.utils.fuelpos.models.ClaimedTransactionResult
import app.rht.petrolcard.utils.fuelpos.models.ClearTransactionResult
import app.rht.petrolcard.utils.tax.TaxModel
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import com.altafrazzaque.ifsfcomm.ifsf.models.ClearFuelSaleTrxPrams
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import com.google.gson.Gson
import com.google.zxing.WriterException
import com.pax.gl.page.IPage
import com.pax.gl.page.PaxGLPage
import kotlinx.android.synthetic.main.toolbar.view.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.io.File
import java.io.FileInputStream
import java.lang.ref.WeakReference
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList


enum class TicketType {
    Customer,
    Attendant
}

fun Double.format(digits: Int) =  "%.${digits}f".format(Locale.US,this).replace(",",".").toDouble()


class TicketActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private lateinit var mBinding: ActivityTicketBinding

    private var TAG = TicketActivity::class.java.simpleName
    private lateinit var fuelTransactionStatusDAO : FCCTransactionsDao
    private var intentExtrasModel: IntentExtrasModel? = null
    private var stationMode: Int? = null
    private var mTransaction: TransactionModel? = null
    private var mProduit: ProductModel? = null
    private var mAmount: Double? = null
    private var fuelProductModel: FuelProductModel? = null
    private lateinit var mTransactionTaxiDAO: TransactionDao
    private var isLitre = false
    var isLoyaltyMenuSelected: String? = null
    private var esdSignatureDAO: ESDSignatureDao? = null
    var loyTrx = ""
    var isLoyalty = false
    private var panLoyalty: String? = null

    var ticketType = 1
    var qtyDecimal = 2
    var uniteDecimal = 2
    var totalDecimal = 2
    var currency = ""
    var fuelQtyUnit = "L"
    var showTicketPreview = true
    private lateinit var fuelpos: FuelPOSModel
    private lateinit var fusionModel: FusionModel
    var receiptBitmap:Bitmap?=null
    lateinit var transactionFromFcc: TransactionFromFcc
    private var isTimsRequire: Boolean = false
    private var isQrCodeTicketRequired: Boolean = false
    private var esdSignature = ""
    private var ESD_ACTIVITY_REQUEST = 1100
    private var TIMS_ACTIVITY_REQUEST = 1101
    private var QR_CODE_TICKET_ACTIVITY_REQUEST = 1102
    private var receiptDate = ""
    private var customerMessage = ""
    private var customerCopy = ""
    private var merchantCopy = ""
    private var mTerminal: TerminalModel? = null
    private var referenceModel : ReferenceModel? = null
    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_ticket)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        Support.setNavigationStatus(this, false)
        resetPaymentValues(TAG)
        init()
    }

    private fun init() {
        referenceModel = prefs.getReferenceModel()
        isTimsRequire = referenceModel!!.fiscal_printer!!.isAvailable && referenceModel!!.fiscal_printer!!.isTIMSRequired == AppConstant.TIMS_REQUIRED
            prefs.transactionCount = prefs.transactionCount + 1
        if(prefs.transactionCount >= 5 /*|| BuildConfig.DEBUG*/) {
            prefs.isRestartApplication = "true"
        }

        if(prefs.getFuelPosModel() != null)
        {
            fuelpos = prefs.getFuelPosModel()!!
        }
        if(prefs.getFusionModel() != null)
        {
            fusionModel = prefs.getFusionModel()!!
        }
        getIntentExtraModel()
        setupToolbar()
        val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting
        if(receiptSetting!=null){
            showTicketPreview = receiptSetting.receiptPreview
        }

        mBinding.ticketLayout.visibility = View.VISIBLE
        if(showTicketPreview){
            mBinding.ivTicketPreview.visibility = View.VISIBLE
            mBinding.layoutPrintingProgress.visibility = View.GONE
        }
        else {
            mBinding.ivTicketPreview.visibility = View.GONE
            mBinding.layoutPrintingProgress.visibility = View.VISIBLE
        }
        if (stationMode == AFTER_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL && (fusionModel != null && fusionModel.EXIST) && !isTimsRequire && !intentExtrasModel!!.mTransaction!!.sequenceController.isNullOrEmpty()) {
            clearFusionTransaction()
        }
        if (stationMode == AFTER_TRX_MODE && !isTimsRequire && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL && (fuelpos != null && fuelpos.isExist)) {
            claimPosTransaction()
        } else {
            getIntentDataToPrintReceipt()
        }
    }

    private fun clearFusionTransaction() {
        val paymentType = Support.getFusionPaymentType(intentExtrasModel!!.mTransaction!!.modepay!!)
        val clearTrxDetails = ClearFuelSaleTrxPrams(deviceId = intentExtrasModel!!.mTransaction!!.pumpId!!, trxSequenceNo = intentExtrasModel!!.mTransaction!!.sequenceController!!, paymentType = paymentType, attributeName = "referenceNo", attributeValue = intentExtrasModel!!.mTransaction!!.reference!!)
        FusionService.clearTrxDetails(clearTrxDetails)
        deleteFCCTransaction(true)
    }

    private fun setupToolbar() {
        mBinding.toolbarTicket.toolbar.tvTitle.text = getString(R.string.ticket)
        mBinding.toolbarTicket.toolbar.setNavigationOnClickListener {
            mBinding.toolbarTicket.toolbar.isEnabled = false
           gotoNextActivity()
        }
    }
    private fun getIntentExtraModel(){
        intentExtrasModel  = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!

            if (intentExtrasModel!!.stationMode != null) {
                stationMode = intentExtrasModel!!.stationMode!!
                if(intentExtrasModel!!.loyaltyTrx)
                {
                    stationMode = 1
                }
            }
        }
        fuelProductModel = intentExtrasModel!!.fuelProductModel
        if(stationMode == AFTER_TRX_MODE && intentExtrasModel!!.workFlowTransaction == TAXI_FUEL)
        {
            transactionFromFcc = prefs.getTransactionModel()!!
        }
        saveAuditLogs()
    }
    private fun getIntentDataToPrintReceipt() {
        log(TAG,"selectedProduct:: "+ intentExtrasModel!!.selectedProduct)
        fuelQtyUnit = referenceModel!!.FUEL_QTY_UNIT!!
        currency =prefs.currency
        mTerminal = referenceModel!!.terminal
        if( intentExtrasModel!!.amount!=null )
            mAmount =  intentExtrasModel!!.amount!!.toDouble()
        isLitre = intentExtrasModel!!.isLitreUnit!!
        getFusionProductName()
        intentExtrasModel!!.selectedProduct = mProduit
        log(TAG,"mProduct:: "+mProduit)
        mTransaction = intentExtrasModel!!.mTransaction
        getLoyaltyDetails()
        isTimsRequire = referenceModel!!.fiscal_printer!!.isAvailable && referenceModel!!.fiscal_printer!!.isTIMSRequired == AppConstant.TIMS_REQUIRED
        isQrCodeTicketRequired = if(referenceModel!!.qrCodeTicket!=null) referenceModel!!.qrCodeTicket!!.enabled?:false else false
        log(TAG, "isTimsRequire:: $isTimsRequire")

        val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting

        if(!intentExtrasModel!!.loyaltyTrx)
        {
            getDiscountDetails()
        }
        ticketConfig()
        if(referenceModel!!.PRINT_RECEIPT == PRINT_RECEIPT_NOT_REQUIRED) {
            mBinding.promptLayout.visibility = View.VISIBLE
            mBinding.prompt.text = getString(R.string.please_wait_saving_transaction_details)
        }
         if(intentExtrasModel!!.mTransaction != null && intentExtrasModel!!.mTransaction!!.isDisputedTrx == 0 && isTimsRequire  && (intentExtrasModel!!.categoryId == PRODUCT.FUEL_CATEGORY_ID || (referenceModel!!.fiscal_printer!!.isNonFuelProductsESD && intentExtrasModel!!.categoryId == PRODUCT.SHOP_CATEGORY_ID))){
            gotoTIMSSignActivity()
        }
//        else if(isEsdRequire && esdSignature.isEmpty() && (intentExtrasModel!!.categoryId == PRODUCT.FUEL_CATEGORY_ID || (referenceModel!!.fiscal_printer!!.isNonFuelProductsESD && intentExtrasModel!!.categoryId == PRODUCT.SHOP_CATEGORY_ID))){
//            gotoEsdSignActivity()
//        }
        else if(mTransaction!=null && referenceModel != null && referenceModel!!.qrCodeTicket != null && referenceModel!!.qrCodeTicket!!.enabled == true) {  //added QR code on receipt for fuelUP  https://app.clickup.com/t/2zx27vd
            intentExtrasModel!!.mTransaction!!.transactionStatus = 1
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            gotoQrCodeTicketActivity()
        }
        else {
            executePrintFlow()
        }
    }
    private fun gotoEsdSignActivity() {
        val mIntent = Intent(this, EsdSignActivity::class.java)
        intentExtrasModel!!.selectedProduct = mProduit
        intentExtrasModel!!.mTransaction = mTransaction
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivityForResult(mIntent, ESD_ACTIVITY_REQUEST)
    }
    private fun gotoTIMSSignActivity() {
        val mIntent = Intent(this, TIMSSignActivity::class.java)
        intentExtrasModel!!.selectedProduct = mProduit
        intentExtrasModel!!.mTransaction = mTransaction
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivityForResult(mIntent, TIMS_ACTIVITY_REQUEST)
    }

    private fun gotoQrCodeTicketActivity() {
        val mIntent = Intent(this, QrCodeTicketActivity::class.java)
        intentExtrasModel!!.mTransaction = mTransaction
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        //activityQRResultLaunch.launch(mIntent)
        startActivityForResult(mIntent, QR_CODE_TICKET_ACTIVITY_REQUEST) //commented this for black screen
    }
  /*  var activityQRResultLaunch = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val data: Intent? = result.data
         if (result.resultCode == RESULT_OK) {
             intentExtrasModel =
                 data!!.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
             mTransaction = intentExtrasModel!!.mTransaction
             if (mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID && stationMode == AFTER_TRX_MODE && !intentExtrasModel!!.mTransaction!!.sequenceController.isNullOrEmpty()) {
                 clearFusionTransaction()
             }
             log(TAG, "mTransaction from TIMS:: " + intentExtrasModel!!.mTransaction)
             executePrintFlow()
         }
    }*/
    private fun executePrintFlow() {
        if (isLoyalty && isLoyaltyMenuSelected != "yes") {
            showLoyaltyDialog()
        } else {
            log(TAG, "api isLoyalty: false, goWorkflow()")
            goToTrxWorkflow()
        }
    }
    //region loyalty dialog
    private var loyaltyDialog : DialogLoyaltyFragment? = null
    private var loyaltyDialogListener = object: LoyaltyDialogListener{
        override fun onCloseDialog() {
            goToTrxWorkflow()
        }
        override fun onLoyaltyDialogResponse(
            cardNumber: String?,
            isLoyaltyCard: Boolean,
            loyaltyDialogStatus: LoyaltyDialogStatus?) {
            when {
                loyaltyDialogStatus === LoyaltyDialogStatus.SUCCESS -> {
                    panLoyalty = cardNumber
                    if (mTransaction == null) {
                        mTransaction!!.panLoyalty = panLoyalty
                    } else {
                        mTransaction!!.panLoyalty = panLoyalty
                    }
                    showToast("pan = $cardNumber")
                    goToTrxWorkflow()
                }
                loyaltyDialogStatus === LoyaltyDialogStatus.NOT_FOUND -> {
                    showLoyaltyResponseDialog(getString(R.string.error), getString(R.string.loyalty_customer_not_found))
                }
                loyaltyDialogStatus === LoyaltyDialogStatus.INVALID_CARD -> {
                    //showLoyaltyResponseDialog(getString(R.string.loyalty_customer_not_found))
                    showLoyaltyResponseDialog(getString(R.string.invalid_card), getString(R.string.insert_valid_loyalty_card))
                }
                loyaltyDialogStatus === LoyaltyDialogStatus.SERVER_NOT_CONNECTED -> {
                    showLoyaltyResponseDialog(getString(R.string.error), cardNumber!!) //Error Msg
                }
            }
        }
    }
    private fun showLoyaltyDialog() {
        log(TAG, "LOYALTY: showLoyaltyDialog()")

        loyaltyDialog = DialogLoyaltyFragment(loyaltyDialogListener)
        //val bundle = Bundle()
        //loyaltyDialog!!.arguments = bundle
        loyaltyDialog!!.show(supportFragmentManager, "DialogDelete")
    }
    private fun showLoyaltyResponseDialog(title:String, msg:String){
        val ctx = WeakReference(this).get()!!
        MaterialDialog(ctx)
            .title(text = title)
            .message(text = msg)
            .cancelable(false)
            .show {
                cornerRadius(res = R.dimen.my_corner_radius)
                positiveButton(text = getString(R.string.print_ticket),click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        setBeep()
                        dialog.dismiss()
                        goToTrxWorkflow()
                    }
                })
                negativeButton(text = getString(R.string.retry), click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        setBeep()
                        dialog.dismiss()
                        showLoyaltyDialog()
                    }
                })
            }
    }

    //endregion
    private fun goToTrxWorkflow() {
        Log.i(TAG,"Executed:: goToTrxWorkflow")
        if(mTransaction!=null){
            mTransaction!!.panLoyalty = if (panLoyalty != null) panLoyalty else ""
            trxWorkFlowCommonCode()
        }
        if(Connectivity.isNetworkAvailable(this) && referenceModel!!.is_send_transaction_online != null && referenceModel!!.is_send_transaction_online!!)
        {
            val transactionModel = ArrayList<TransactionModel>()
            intentExtrasModel!!.mTransaction!!.transactionStatus = 1
            if( intentExtrasModel!!.mTransaction!!.pan == null)
            {
                intentExtrasModel!!.mTransaction!!.pan = ""
            }
            var totalPaidAmount = intentExtrasModel!!.mTransaction!!.amount
            if(!intentExtrasModel!!.mTransaction!!.discountAmount.isNullOrEmpty() && intentExtrasModel!!.mTransaction!!.isDiscountTransaction!! == 1)
            {
                totalPaidAmount = if(intentExtrasModel!!.mTransaction!!.amount!! <= 0) {
                    0.0
                } else {
                    intentExtrasModel!!.mTransaction!!.amount!!.toDouble() -  intentExtrasModel!!.mTransaction!!.discountAmount!!.toDouble()

                }
            }
            intentExtrasModel!!.mTransaction!!.totalPaidAmount = totalPaidAmount
            transactionModel.add(intentExtrasModel!!.mTransaction!!)
            val mTaxis = SendTransactionModel(transactionModel,MainApp.sn)
            mViewModel.sendTransactionOnline(mTaxis)
        }
        if(showTicketPreview){
            mBinding.ivTicketPreview.visibility = View.VISIBLE
            mBinding.layoutPrintingProgress.visibility = View.GONE
        }
        else
        {
            mBinding.ivTicketPreview.visibility = View.GONE
            mBinding.layoutPrintingProgress.visibility = View.VISIBLE
        }
        try {
            if(referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout != null && referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout == LAYOUT.LAYOUT_2) {
                receiptBitmap = getPrintBitmapLayout2()
                mBinding.ivTicketPreview.setImageBitmap(receiptBitmap)
            } else {
                receiptBitmap = getPrintBitmapLayout1()
                mBinding.ivTicketPreview.setImageBitmap(receiptBitmap)
            }
        } catch (e:Exception) {
            e.printStackTrace()
        }

        when (referenceModel!!.PRINT_RECEIPT) {
            PRINT_RECEIPT_MANDATORY -> {
                printCustomerTicket()
            }
            PRINT_RECEIPT_REQUIRED -> {
                showCustomerPrintDialog()
            }
            else -> {
                Handler(Looper.getMainLooper()).postDelayed({
                    gotoNextActivity()
                }, 2000)
            }
        }
    }
    private fun trxWorkFlowCommonCode() {
        try {
            if (intentExtrasModel!!.loyaltyTrx) {
                mTransaction!!.detailArticle = "loy"
            }
            try {
                updateTransactionStatus()
            } catch (ex: SQLiteException) {
                ex.printStackTrace()
            }
            log( TAG,mTransaction.toString())
        }
        catch (ex: SQLiteException) {
            ex.printStackTrace()
            setBeep()
            gotoAbortMessageActivity(getString(R.string.error),resources.getString(R.string.transaction_failure))
            finish()
        }
    }
    private fun getDiscountDetails() {
        if (intentExtrasModel!!.discountAmount != null && intentExtrasModel!!.isDiscountTransaction!!) {
            mTransaction!!.discountId = intentExtrasModel!!.cardDiscountId
            mTransaction!!.discountAmount = intentExtrasModel!!.discountAmount
            mTransaction!!.isDiscountTransaction = 1
        }
        else if(mTransaction!!.isDiscountTransaction == 1 && mTransaction!!.discountAmount != null){
            mTransaction!!.discountId = intentExtrasModel!!.cardDiscountId
            mTransaction!!.isDiscountTransaction = 1
        }
        else {
            mTransaction!!.discountId = 0
            mTransaction!!.discountAmount = "0.0"
        }
    }

    private fun getLoyaltyDetails() {
        isLoyaltyMenuSelected = if (intentExtrasModel!!.loyaltyTrx) YES else NO
        loyTrx = isLoyaltyMenuSelected!!
        isLoyalty = referenceModel!!.LOYALTY!!
        log(TAG, "isLoyalty: $isLoyalty ---- is Loyalty Menu selected: $isLoyaltyMenuSelected")

        stationMode = if (isLoyaltyMenuSelected != null && isLoyaltyMenuSelected == YES)
            AFTER_TRX_MODE
        else if (BuildConfig.MODE == 0)
            prefs.getStationModel()!!.mode
        else
            BuildConfig.MODE

        if (isLoyaltyMenuSelected != null && isLoyaltyMenuSelected == YES) {
            intentExtrasModel!!.typePay = LOYALTY_VALUE
        }
    }

    private fun getFusionProductName() {
        mProduit = intentExtrasModel!!.selectedProduct
        if (mProduit != null) {
            val fusionProductName: String =
                Support.getFusionProductName(this, mProduit!!.fcc_prod_id)!!
            if (fusionProductName.isNotEmpty()) {
                mProduit!!.libelle = fusionProductName
            }
        }
    }

    override fun onDestroy() {
        if(loyaltyDialog!=null){
            try{ loyaltyDialog!!.dismiss()} catch (e: Exception) { e.printStackTrace() }
        }

        if(::printNewFormattedTicket2.isInitialized){
            printNewFormattedTicket2.cancel(true)
        }

        stopFuelPosReceiver()

        mBinding.unbind()
        if(receiptBitmap != null)
        {
            try{ if(receiptBitmap!=null)receiptBitmap!!.recycle()
                if(logoBitmap != null)
                {
                    logoBitmap!!.recycle()
                }
            } catch (e:java.lang.Exception) { e.printStackTrace() }
        }
        if (BuildConfig.POS_TYPE == "PAX") { UtilsCardInfo.disconnectPAX() }
        super.onDestroy()
    }

    private fun showCustomerPrintDialog(){

        val timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
            }
            override fun onFinish() {
                val unAttendantMode = prefs.getReferenceModel()!!.TERMINAL_TYPE == UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                   gotoNextActivity()
                }
            }
        }
        if(referenceModel!!.TERMINAL_TYPE == UN_ATTENDANT_MODE)
            timer.start()
        val title = getString(R.string.transaction_success)
        if(referenceModel!!.PRINT_RECEIPT == PRINT_RECEIPT_REQUIRED)
        {
            MaterialDialog(this)
                .title(text = title)
                .message(text = Html.fromHtml("<font color='#000000'>"+getString(R.string.do_you_want_print_customer_receipt)+"</font>"))
                .cancelable(false)
                .show {
                    cornerRadius(res = R.dimen.my_corner_radius)
                    positiveButton(text = getString(R.string.print),click = object : DialogCallback{
                        override fun invoke(dialog: MaterialDialog) {
                            setBeep()
                            timer.cancel()
                            printCustomerTicket()
                        }
                    })
                    negativeButton(text = getString(R.string.cancel), click = object : DialogCallback{
                        override fun invoke(dialog: MaterialDialog) {
                            setBeep()
                            timer.cancel()
                            if(referenceModel!!.TERMINAL_TYPE == UN_ATTENDANT_MODE) {
                                gotoNextActivity()
                            }
                            else
                                showAttendantPrintDialog()
                        }
                    })
                }
        }
        else {
            log(TAG,"Print receipt not required")
            mBinding.prompt.text = getString(R.string.please_wait_saving_transaction_details)
        }
    }

    lateinit var printNewFormattedTicket2 : PrintNewFormattedTicket2
    private fun printCustomerTicket() {
        ticketType = CUSTOMER
        if (isPrinterPaperAvailable()) {
            printNewFormattedTicket2 = PrintNewFormattedTicket2()
            printNewFormattedTicket2.execute()
        } else {
            showPaperDialog()
        }
    }

    private fun showPaperDialog(){
        MyMaterialDialog(this,
            getString(R.string.error),
            printerStatusMessage,
            positiveBtnText = "Print again",
            negativeBtnText = "Cancel",
            object : MyMaterialDialogListener{
                override fun onPositiveClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    printNewFormattedTicket2 = PrintNewFormattedTicket2()
                    printNewFormattedTicket2.execute()
                }

                override fun onNegativeClick(dialog: MaterialDialog) {
                    dialog.dismiss()
                    gotoNextActivity()
                    return
                }
            })
    }
    fun showAttendantPrintDialog(){
     //   if(referenceModel!!.PRINT_RECEIPT == PRINT_RECEIPT_REQUIRED) {
            if (referenceModel!!.TERMINAL_TYPE != UN_ATTENDANT_MODE) {
                MaterialDialog(this)
                    .title(text = getString(R.string.confirm))
                    .message(text = Html.fromHtml("<font color='#000000'>" + getString(R.string.do_you_want_to_print_merchant_receipt) + "</font>"))
                    .cancelable(false)
                    .show {
                        cornerRadius(res = R.dimen.my_corner_radius)
                        positiveButton(
                            text = getString(R.string.print),
                            click = object : DialogCallback {
                                override fun invoke(dialog: MaterialDialog) {
                                    setBeep()
                                    ticketType = ATTENDANT

                                    if(isPrinterPaperAvailable())
                                    {
                                        PrintNewFormattedTicket2().execute()
                                    }
                                    else
                                    {
                                        showPaperDialog()
                                    }
                                }
                            })
                        negativeButton(
                            text = getString(R.string.cancel),
                            click = object : DialogCallback {
                                override fun invoke(dialog: MaterialDialog) {
                                    setBeep()
                                    gotoNextActivity()
                                }
                            })
                    }
            }
//        } else {
//            log(TAG,"Print receipt not required")
//            mBinding.prompt.text = getString(R.string.please_wait_saving_transaction_details)
//        }
    }
    private fun ticketConfig(){
        val terminalConfig = referenceModel!!.terminalConfig
        if(terminalConfig!=null){
            val receiptSetting = terminalConfig.receiptSetting
            if(receiptSetting!=null){
                if(!receiptSetting.dateFormat.isNullOrEmpty()){
                    val sdf = SimpleDateFormat(receiptSetting.dateFormat!!)
                    val currentDate = sdf.format(Date())
                    receiptDate =  currentDate
                }

                if(!receiptSetting.customerMessage.isNullOrEmpty()){
                    customerMessage = receiptSetting.customerMessage!!
                }

                if(!receiptSetting.customerCopy.isNullOrEmpty()){
                    customerCopy = receiptSetting.customerCopy!!
                }

                if(!receiptSetting.merchantCopy.isNullOrEmpty()){
                    merchantCopy = receiptSetting.merchantCopy!!
                }
            }
        }
        receiptDate =  if(receiptDate.isNotEmpty()) receiptDate else mTransaction!!.dateTransaction!!
        log(TAG, "receiptDate:: $receiptDate")
    }

    //region iPaxGL Page moved here from base activity
    private lateinit var iPaxGLPage:PaxGLPage
    private lateinit var receiptLayout2: IPage
    private fun initPrinterLayout2()
    {
        iPaxGLPage = PaxGLPage.getInstance(applicationContext)
        receiptLayout2 = iPaxGLPage.createPage()
    }
    private fun addLine(text1:String,text2:String,size: Int,style: Int): IPage.ILine {
        val line= receiptLayout2.addLine()
        if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this)) {
            val unit1 = receiptLayout2.createUnit()
            unit1.textStyle=style
            unit1.fontSize=size
//            unit1.align= IPage.EAlign.RIGHT
            unit1.text=text2
            line.addUnit(unit1)
            val unit2 = receiptLayout2.createUnit()
            unit2.textStyle=style
            unit2.fontSize=size
//            unit2.align= IPage.EAlign.RIGHT
            unit2.text=text1
            line.addUnit(unit2)
        }
        else
        {
            val unit1 = receiptLayout2.createUnit()
            unit1.textStyle=style
            unit1.fontSize=size
            unit1.align= IPage.EAlign.LEFT
            unit1.text=text1
            line.addUnit(unit1)
            val unit2 = receiptLayout2.createUnit()
            unit2.textStyle=style
            unit2.fontSize=size
            unit2.align= IPage.EAlign.RIGHT
            unit2.text=text2
            line.addUnit(unit2)
        }
        return line
    }
    private fun addReceiptUnit(text:String,align: IPage.EAlign?,size: Int,style: Int): IPage.ILine.IUnit {
        val unit = receiptLayout2.createUnit()
        if(align != null)
        {
            unit.align = align
        }
        if(text.isNotEmpty())
        {
            unit.text = text
        }
        if(style != 0)
        {
            unit.textStyle = style
        }
        unit.fontSize = size
        return unit
    }
    //endregion

    inner class PrintNewFormattedTicket2 : CoroutineAsyncTask<String, String, Void?>() {
        override fun doInBackground(vararg params: String): Void? {
            try {
                ticketConfig()
                val ctx = WeakReference(this@TicketActivity).get()!!

                var printerType = referenceModel!!.PRINTER_TYPE
                /*if(BuildConfig.DEBUG){
                    printerType = AppConstant.DW14_PRINTER
                }*/

                if(ticketType == CUSTOMER)
                {
                    if(receiptBitmap == null)
                    {
                        if(referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout != null && referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout == LAYOUT.LAYOUT_2 ) {
                            receiptLayout2 = iPaxGLPage.createPage()
                            receiptBitmap = getPrintBitmapLayout2()
                        } else {
                            receiptLayout2 = iPaxGLPage.createPage()
                            receiptBitmap = getPrintBitmapLayout1()
                        }
                    }
                    if(printerType != AppConstant.DW14_PRINTER){
                        TicketPrinter(ctx).printReceipt(receiptBitmap!!,referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout!!)
                    } else {
                        val commands = geDw14PrintBitmapLayout()
                        TicketPrinter(ctx).printTicket(commands)
                    }
                }
                else
                {
                    if(referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout != null && referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout == LAYOUT.LAYOUT_2 ) {
                        receiptLayout2 = iPaxGLPage.createPage()
                        receiptBitmap = getPrintBitmapLayout2()
                    } else {
                        receiptBitmap = getPrintBitmapLayout1()
                    }
                    if(printerType != AppConstant.DW14_PRINTER){
                        TicketPrinter(ctx).printReceipt(receiptBitmap!!,referenceModel!!.terminalConfig!!.receiptSetting!!.receipt_layout!!)
                    } else {
                        val commands = geDw14PrintBitmapLayout()
                        TicketPrinter(ctx).printTicket(commands)
                    }
                }
            }
            catch (e:Exception)
            {
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                //   mViewModel.generateLogs(e.message!!,0)
                e.printStackTrace()
            }
            return null
        }

        override fun onPostExecute(result: Void?) {

            if (ticketType == ATTENDANT) {
               gotoNextActivity()
            } else {
                if(referenceModel!!.TERMINAL_TYPE == UN_ATTENDANT_MODE)
                {
                    gotoNextActivity()
                }
                else
                {
                    ticketType = ATTENDANT
                    showAttendantPrintDialog()
                }
            }
        }
    }
    private fun deleteFCCTransaction(isFusion:Boolean) {
        val transaction = intentExtrasModel!!.mTransaction
        try {
            if (transaction != null) {
                fuelTransactionStatusDAO = FCCTransactionsDao()
                fuelTransactionStatusDAO.open()
                if(isFusion)
                {
                    val deletedId = fuelTransactionStatusDAO.deleteTransactionFusion(transaction.fccSaleId!!)
                    log(TAG, "Transaction Deleted :: $deletedId")
                }
                else
                {
                    val deletedId = fuelTransactionStatusDAO.deleteTransactionFuelpos(transaction.sequenceController!!)
                    log(TAG, "Transaction Deleted :: $deletedId")
                }
                fuelTransactionStatusDAO.close()

            }
        } catch (ex: SQLiteException) {
            log(TAG, ex.message!!)
        }
    }
    fun saveAuditLogs()
    {
        if(intentExtrasModel!=null && intentExtrasModel!!.transactionStepLog!=null && intentExtrasModel!!.transactionStepLog!!.actions!!.isNotEmpty()) {
            val auditDao = AuditDao()
            auditDao.open()
            val item = AuditModel()
            item.activity = "Transaction Activity"
            item.action = "Steps: ${ intentExtrasModel!!.transactionStepLog!!.toJson() }"
            item.performedBy = ""
            item.timestamp = getCurrentTimestamp()
            auditDao.insertAudit(item)
            auditDao.close()
        }
    }
    //region fuelPos
    private var unclaimFirst = false
    private fun claimPosTransaction(){
        deleteFCCTransaction(false)
        startFuelPosReceiver()
    }
    private fun showFccConnectionDialog(message:String){
        try{
            MyMaterialDialog(this,
                message,
                getString(R.string.press_retry_to_connect_fcc),
                getString(R.string.get_receipt),
                getString(R.string.retry),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        getIntentDataToPrintReceipt()
                        dialog.dismiss()
                    }
                    override fun onNegativeClick(dialog: MaterialDialog) {
                        claimPosTransaction()
                    }
                }
            )
        } catch (e:Exception){
            e.printStackTrace()
        }
    }
    private fun startFuelPosReceiver() {
        if (!FuelPosService.isRunning(this)) FuelPosService.start(this)
        val filter = IntentFilter()
        filter.addAction(ACTION_FUEL_POS_REPLY)
        filter.addAction(ACTION_FUEL_POS_MESSAGE)
        filter.addAction(ACTION_POS_MESSAGE)
        filter.addAction(ACTION_FUEL_POS_FILLING_STATE)
        registerReceiver(fuelPosReceiver, filter)
        log(TAG, "FuelPOS receiver started")

        FuelPosService.startExtPosClient()

        if(::transactionFromFcc.isInitialized) {
            val refId = transactionFromFcc.ref_transaction!!.split(":")
            // transaction.pumpNumber + ":" +transaction.fillingId + ":"+ transaction.trxChecksum.toString() + /*":" +*/ + mTerminal!!.terminalId + mTerminal!!.stationId /*+ ":" + transaction.getMessageSequence()*/
            val pumpNumber = refId[0]
            val fillingId = refId[1]

            //LOGIN then CLAIM TRX
            Handler(Looper.getMainLooper()).postDelayed({
                if (!trxClaimFlags[0]) {
                    val claimMessage: String = if (unclaimFirst) {
                        FuelPosCommands.unclaimTransaction( /*msgSeq,*/pumpNumber, fillingId)
                    } else {
                        FuelPosCommands.claimTransaction( /*msgSeq,*/pumpNumber, fillingId)
                    }
                    FuelPosService.sendViaPosProtocol(claimMessage)
                    log(TAG, "COMMAND SENT TO FUEL POS: $claimMessage")
                    trxClaimFlags[0] = true
                }
            },2000)
        }
    }
    private fun stopFuelPosReceiver() {
        try {
            unregisterReceiver(fuelPosReceiver)
            log(TAG, "FuelPos receiver stopped")
            FuelPosService.stopExtPosClient()
        } catch (e: java.lang.Exception) {
        }
    }
    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action
            log(TAG, "FUELPOS ACTION $action")
            var message: String?
            when (action) {
                ACTION_FUEL_POS_REPLY -> {
                    message = intent.getStringExtra(FUEL_POS_REPLY)
                    log(TAG, "POS MESSAGE: $message")
                }
                ACTION_POS_MESSAGE -> {
                    message = intent.getStringExtra(POS_MESSAGE)
                    //FuelPosService.sendViaPosProtocol(FuelPosCommands.posHeartbeat())
                    if(!message.isNullOrEmpty() && !message.contains("PUMPSTATES") && !message.contains("FILLINGSTATES"))
                        onFuelPosDataReceived(message)
                }
                ACTION_FUEL_POS_MESSAGE -> {
                    message = intent.getStringExtra(FUEL_POS_MESSAGE)
                    log(TAG, "FUEL_POS_MESSAGE: $message")
                }
            }
        }
    }
    private var trxClaimFlags = arrayOf(false,false,false)
    fun onFuelPosDataReceived(message:String)
    {
        val refId = transactionFromFcc.ref_transaction!!.split(":")
        val pumpNumber = refId[0]
        val fillingId = refId[1]
        if (message.contains(FuelPosReply.UNCLAIME_TRANSACTION_RESULT)) {
            val claimMessage = FuelPosCommands.claimTransaction( /*msgSeq,*/pumpNumber, fillingId)
            FuelPosService.sendViaPosProtocol(claimMessage)
            log(TAG, "COMMAND SENT TO FUEL POS: $claimMessage")
            trxClaimFlags[0] = true
        }
        //TRANSACTION CLAIMED then CLEAR TRX
        if (message.contains(FuelPosReply.CLAIM_TRANSACTION_RESULT)) {
            val result: ClaimedTransactionResult? = FuelPosReply.getClaimTransactionResult(message)
            if (result!!.pumpNumber == pumpNumber && result.fillingId == fillingId && result.completionCode == CompletionCode.OK.toString() + "") {
                val clearMessage = FuelPosCommands.clearTransaction( /*msgSeq,*/pumpNumber, fillingId)
                FuelPosService.sendViaPosProtocol(clearMessage)
                trxClaimFlags[1] = true
                if(isTimsRequire && stationMode == AFTER_TRX_MODE)
                {
                    prefs.isSignInBackground = true
                    FuelPosService.checkTransactionCountPerPump(pumpNumber.toInt())
                }
                executePrintFlow()
            } else {
                showToast(getString(R.string.unable_to_clain_transactions) + PosCompletionCode.getMessage(result.completionCode))
                stopFuelPosReceiver()
                showFccConnectionDialog(getString(R.string.unable_to_clain_transactions) + PosCompletionCode.getMessage(result.completionCode))
                unclaimFirst = true
            }
        }

        //TRANSACTION CLEARED then check paid state
        if (message.contains(FuelPosReply.CLEAR_TRANSACTION_RESULT) && trxClaimFlags[0] && trxClaimFlags[1]) {
            val result: ClearTransactionResult? = FuelPosReply.getClearTransactionResult(message)
            if (result!!.pumpNumber == pumpNumber && result.fillingId == fillingId && result.completionCode == CompletionCode.OK.toString() + "") {
                trxClaimFlags[2] = true
                stopFuelPosReceiver()
                showToast(getString(R.string.transaction_cleared_on_fcc))
            } else {
                stopFuelPosReceiver()
                showToast(getString(R.string.unable_to_clear_transaction) + PosCompletionCode.getMessage(result.completionCode))
                showFccConnectionDialog(getString(R.string.unable_to_clear_transaction)+ PosCompletionCode.getMessage(result.completionCode))
                unclaimFirst = true
            }
        }
    }

    //region esd printer
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
       /* if (requestCode == ESD_ACTIVITY_REQUEST) {
            if (resultCode == RESULT_OK) {
                esdSignature = data!!.getStringExtra("esdSignature")!!
                esdTrxRowId = data.getIntExtra("esdTrxRowId", 0)
                if(isEsdRequire)
                {
                    updateTransactionSignatureInDB(esdSignature)
                }
                executePrintFlow()
            }
        }*/
        if (requestCode == TIMS_ACTIVITY_REQUEST) {
            if (resultCode == RESULT_OK) {
                intentExtrasModel  = data!!.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
                mTransaction = intentExtrasModel!!.mTransaction
                if (stationMode == AFTER_TRX_MODE && mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID && (fusionModel != null && fusionModel.EXIST) && !intentExtrasModel!!.mTransaction!!.sequenceController.isNullOrEmpty()) {
                    clearFusionTransaction()
                    executePrintFlow()
                }
                if (stationMode == AFTER_TRX_MODE && mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID  && (fuelpos != null && fuelpos.isExist)) {
                    claimPosTransaction()
                }
                log(TAG,"mTransaction from TIMS:: "+intentExtrasModel!!.mTransaction)
               // executePrintFlow()
            }
        }
        if (requestCode == QR_CODE_TICKET_ACTIVITY_REQUEST) {
            if (resultCode == RESULT_OK) {
                intentExtrasModel  = data!!.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
                mTransaction = intentExtrasModel!!.mTransaction
                if(mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID && stationMode == AFTER_TRX_MODE && !intentExtrasModel!!.mTransaction!!.sequenceController.isNullOrEmpty())
                {
                    clearFusionTransaction()
                }
                log(TAG,"mTransaction from QR_CODE_TICKET_ACTIVITY_REQUEST:: "+intentExtrasModel!!.mTransaction)
                executePrintFlow()
            }
        }
    }


    private var esdTrxRowId: Int = 0
    private fun updateTransactionSignatureInDB(signature: String) {
        try {
            mTransactionTaxiDAO = TransactionDao()
            mTransactionTaxiDAO.open()
            mTransaction!!.transactionSignature = signature
            val row: Int = mTransactionTaxiDAO.updateTransactionsByReferenceID(mTransaction!!)
            mTransactionTaxiDAO.close()
            if(stationMode != OFFLINE_TRX_MODE)
                deleteSignedTransaction()
            Log.w("TRX INSEREE =>  ", row.toString() + "")
        } catch (ex: SQLiteException) {
            ex.printStackTrace()
        }
    }
    private fun deleteSignedTransaction() {
        esdSignatureDAO = ESDSignatureDao()
        esdSignatureDAO!!.open()
        //esdSignatureDAO.deleteSignedTrx(esdTrxRowId);
        esdSignatureDAO!!.updateESDSignFlag(esdTrxRowId)
        esdSignatureDAO!!.close()
    }
    var logoBitmap:Bitmap?=null
    //endregion
    fun getPrintBitmapLayout2():Bitmap?
    {
        try {

        initPrinterLayout2()
        val TEXT_STYLE_NORMAL = 0
        val TEXT_STYLE_BOLD = 1
        //val TEXT_STYLE_UNDERLINE = 2
        //val TEXT_STYLE_ITALIC = 4
        val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
        qtyDecimal = receiptSetting.quantityDecimal ?: 2
        uniteDecimal = receiptSetting.unitPriceDecimal ?: 2
        val vatDecimal = receiptSetting.vatDecimal ?: 2
        val netDecimal = receiptSetting.netDecimal ?: 2
        val discountDecimal = receiptSetting.discountDecimal ?: 2
        val totalDecimal = receiptSetting.totalAmountDecimal ?: 2
        val limitDecimal = receiptSetting.limitDecimal ?: 2
        var totalAmount = ""
        var discountAmount =""
        var fuel_vat_percentage =""
        var shop_vat_percentage =""
        var fuel_vatable_amount=""
        var fuel_vat_amount=""
        var shop_vat_amount =""
        var shop_vatable_amount =""
        var vat =""
        var net =""
        var vatName =""
        var qty = ""
        val fiscalId =mTerminal!!.fiscalId
        var dateFormated: String? = mTransaction!!.dateTransaction
        if (dateFormated == null || !dateFormated.contains(" ")) {
            dateFormated = Support.dateToString(Date())
        }

        val dateArray = dateFormated!!.split(" ").toTypedArray()
        val date = dateArray[0]
        val time = dateArray[1]
        if(mTransaction!!.quantite != null && mTransaction!!.quantite != 0.0)
        {
            qty = Support.getFormattedValue(this,mTransaction!!.quantite!!.decimal(qtyDecimal))
        }
//        var taxModel = intentExtrasModel!!.taxModel
//        if(taxModel == null)
//        {
//            taxModel = TaxModel(false,0.0,0.0,0.0,mTransaction!!.amount!!)
//        }
        ///log(TAG, "taxModel :: $taxModel")

        if(mTransaction!!.netAmount != null && mTransaction!!.vatAmount != null && (fuelVat.enabled || shopVat.enabled ))
        {
            net = Support.getFormattedValue(this,mTransaction!!.netAmount!!.decimal(netDecimal))
            vat = Support.getFormattedValue(this, mTransaction!!.vatAmount!!.decimal(vatDecimal))

            if(mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID)
            {
                fuel_vat_amount=vat
                fuel_vat_percentage ="${referenceModel!!.fuelVat!!.percentage} %"
                fuel_vatable_amount="${mTransaction!!.amount!!}"
                shop_vat_amount = "0"
                shop_vatable_amount ="0"
                shop_vat_percentage ="0"
                vatName ="${referenceModel!!.fuelVat!!.vat_name}"
            }
            else if(mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID)
            {
                fuel_vat_amount="0"
                fuel_vat_percentage ="0"
                fuel_vatable_amount="0"
                shop_vat_percentage ="${referenceModel!!.shopVat!!.percentage} %"
                shop_vat_amount = vat
                shop_vatable_amount ="${mTransaction!!.amount!!}"
                vatName ="${referenceModel!!.fuelVat!!.vat_name}"
            }
        }
        if(mTransaction!!.discountAmount != null && mTransaction!!.isDiscountTransaction!! == 1)
        {
            discountAmount = Support.getFormattedValue(this@TicketActivity,mTransaction!!.discountAmount!!.toDouble().decimal(discountDecimal))
            val discountAmt = mTransaction!!.discountAmount!!.toDouble()
            totalAmount = Support.getFormattedValue(this@TicketActivity,(mTransaction!!.amount!!.toDouble() - discountAmt).decimal(totalDecimal))
        }
        else
        {
            totalAmount = Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.toDouble().decimal(totalDecimal))
        }
        if(referenceModel!!.receiptFormatFields != null)
        {
            val fields = referenceModel!!.receiptFormatFields

            //Header Format
            for(fd in fields!!.receipt_header)
            {
                if(fd.is_available == 1)
                {
                    if(fd.label_type ==  ReceiptFields.LOGO_R)
                    {

                        try
                        {
                            if(!prefs.logoPath.isNullOrEmpty()) {
                                log(TAG, "Logo :: ${prefs.logoPath}")
                                logoBitmap =
                                    BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                                logoBitmap = Support.getResizedBitmap(logoBitmap!!, 200, 200)
                                receiptLayout2.addLine().addUnit(logoBitmap, IPage.EAlign.CENTER)
                            }
                        }
                        catch (e:Exception)
                        {
                            e.printStackTrace()
                        }

                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_COMPANY_NAME && mTerminal != null)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(referenceModel!!.COMPANY.name, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_STATION_NAME && mTerminal != null)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(mTerminal!!.stationName!!, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.TELECOLLECT_ADDRESS)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(mTerminal!!.address+", "+mTerminal!!.city, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.DATE)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit("$receiptDate $time", IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.DISPUTED_TRANSACTION )
                    {
                            if(mTransaction != null && mTransaction!!.isDisputedTrx == 1)
                            receiptLayout2.addLine().addUnit(addReceiptUnit(fd.label,IPage.EAlign.CENTER,fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else if(fd.label_type ==  ReceiptFields.NEW_LINE)
                    {
                        receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,fd.font_size,TEXT_STYLE_BOLD))
                    }
                    else {
                        receiptLayout2.addLine().addUnit(addReceiptUnit(fd.label, IPage.EAlign.CENTER, fd.font_size,TEXT_STYLE_BOLD))
                    }
                }
            }
            receiptLayout2.addLine().addUnit(addReceiptUnit("", IPage.EAlign.CENTER, 0,0))
            for(rm in fields.receipt_middle)
            {
                if(rm.is_available == 1)
                {
                    when  {

                        rm.label_type ==  ReceiptFields.NEW_LINE -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            receiptLayout2.addLine().addUnit()
                        }
                        rm.label_type ==  ReceiptFields.HEADER_1 && isTimsRequire -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            receiptLayout2.addLine().addUnit()
                        }
                        rm.label_type ==  ReceiptFields.TRX_ID && !mTransaction!!.reference.isNullOrEmpty()   -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label+" : "+mTransaction!!.reference,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.FPOS_SEQUENCE_NO && !mTransaction!!.sequenceController.isNullOrEmpty()   -> {
                            addLine(rm.label,"${mTransaction!!.sequenceController}",rm.font_size,TEXT_STYLE_BOLD)

                        }
                        rm.label_type ==  ReceiptFields.SALE_ID && !mTransaction!!.fccSaleId.isNullOrEmpty()   -> {
                            addLine(rm.label,"${mTransaction!!.fccSaleId}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.TRANSACTION_BY && !intentExtrasModel!!.attendantName.isNullOrEmpty()  -> {
                            val name= intentExtrasModel!!.attendantName!!.split(" ")
                            addLine(rm.label,name[name.size - 1],rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.CUSTOMER_PIN && !mTransaction!!.timsSignDetails!!.customer_details!!.customerPin.isNullOrEmpty() && mTransaction!!.timsSignDetails!!.customer_details!!.customerPin != "0000"-> {
                            addLine(rm.label,mTransaction!!.timsSignDetails!!.customer_details!!.customerPin!!,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.PAN && !mTransaction!!.pan.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("---------------------------------------------------",IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                            if(mTransaction!!.modepay == CARD_VALUE || mTransaction!!.modepay == VISA_VALUE)
                            {
                                addLine(rm.label,Support.hashPan(mTransaction!!.pan),rm.font_size,TEXT_STYLE_BOLD)
                            }
                            else {
                                if(intentExtrasModel!!.modePaymentModel != null) {
                                    addLine(
                                        intentExtrasModel!!.modePaymentModel!!.payment_name!!,
                                        mTransaction!!.pan!!,
                                        rm.font_size,
                                        TEXT_STYLE_BOLD
                                    )
                                }
                                else
                                {
                                    addLine(
                                        getModePayment(mTransaction!!.modepay)!!,
                                        mTransaction!!.pan!!,
                                        rm.font_size,
                                        TEXT_STYLE_BOLD
                                    )
                                }
                            }
                        }
                        rm.label_type ==  ReceiptFields.PAYMENT_TYPE && !mTransaction!!.modepay.isNullOrEmpty()  -> {
                            if(intentExtrasModel!!.modePaymentModel != null)
                            {
                                addLine(rm.label,"${intentExtrasModel!!.modePaymentModel!!.payment_name}",rm.font_size,TEXT_STYLE_BOLD)
                            }
                            else
                            {
                                addLine(rm.label,"${getModePayment(mTransaction!!.modepay)}",rm.font_size,TEXT_STYLE_BOLD)
                            }
                        }
                        rm.label_type ==  ReceiptFields.UNIT_PRICE && mTransaction!!.unitPrice != null && mTransaction!!.unitPrice != 0.0  -> {
                            val unit = Support.getFormattedValue(this,mTransaction!!.unitPrice!!.decimal(uniteDecimal))
                            addLine(rm.label,unit,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.REFERENCE -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.CARD_EXPIRY && !intentExtrasModel!!.dateExp.isNullOrEmpty()-> {
                            addLine(rm.label,"${intentExtrasModel!!.dateExp}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.TAG_NFC && !mTransaction!!.tagNFC.isNullOrEmpty()-> {
                            addLine(rm.label,"${mTransaction!!.tagNFC}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && isTimsRequire && (mProduit != null && mProduit!!.libelle != null && mTransaction!!.quantite != null && mTransaction!!.amount != null)-> {
                           var qtyValue=qty
                            if(mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID)
                            {
                                 qtyValue = "$qty $fuelQtyUnit"
                            }
                            receiptLayout2.addLine()
                                .addUnit(addReceiptUnit(mProduit!!.libelle!!,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(qtyValue,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal)),IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && mTransaction!!.categoryId != PRODUCT.FUEL_CATEGORY_ID && mTransaction!!.productName != null && mTransaction!!.amount != null -> {
                            receiptLayout2.addLine()
                                .addUnit(addReceiptUnit(mTransaction!!.productName!!,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal)),IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.AMOUNT && mTransaction!!.amount != null  -> {
                           addLine(rm.label,"${Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal))}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.PRODUCT_NAME && !mTransaction!!.productName.isNullOrEmpty()  -> {
                            addLine(rm.label,"${mTransaction!!.productName}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.PAN_LOYALTY && !mTransaction!!.panLoyalty.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(Support.hashPan(mTransaction!!.panLoyalty),IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.PAN_HOLDER_NAME && !mTransaction!!.nomPorteur.isNullOrEmpty()  -> {
                            addLine(rm.label,"${mTransaction!!.nomPorteur}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.PUMP_NO && !mTransaction!!.pumpId.isNullOrEmpty()  -> {
                            addLine(rm.label,"${mTransaction!!.pumpId}",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.QTY && mTransaction!!.quantite != null && mTransaction!!.quantite != 0.0 -> {
                            addLine(rm.label,"$qty $fuelQtyUnit",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.HEADER_2 && isTimsRequire -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type == ReceiptFields.DATE  && !receiptDate.isNullOrEmpty()  -> {
                            addLine(rm.label,receiptDate,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type == ReceiptFields.TIME  && !time.isNullOrEmpty()  -> {
                            addLine(rm.label,time,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.VAT &&  (fuelVat.enabled || shopVat.enabled) && !vat.isNullOrEmpty() && vat != "0.0" -> {
                            val isInclusive = fuelVat.type == 0
                            val type = if(isInclusive) "Incl." else  "Excl."
                            addLine("${rm.label} (${mTransaction!!.vatPercentage}% $type)",vat,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.VAT_NAME && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) && !vatName.isNullOrEmpty()-> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(vatName,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit("${mTransaction!!.vatPercentage}%",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(vat,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.ROUNDING_ADJUSTMENT && !mTransaction!!.roundingAdjustment.isNullOrEmpty() -> {
                            addLine(rm.label,mTransaction!!.roundingAdjustment!!,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.NET_AMOUNT && (fuelVat.enabled || shopVat.enabled) && !net.isNullOrEmpty() && net != "0.0" -> {
                            addLine(rm.label,net,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.SUB_INC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && !net.isNullOrEmpty() && net != "0.0" -> {
                            addLine(rm.label,mTransaction!!.amount!!.toString(),rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.SUB_EXC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && !net.isNullOrEmpty() && net != "0.0" -> {
                            addLine(rm.label,net,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.DISCOUNT && mTransaction!!.discountAmount != null && mTransaction!!.isDiscountTransaction == 1 -> {
                            addLine(rm.label,"-$discountAmount",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.REFUND_AMOUNT && mTransaction!!.refundAmount != null && mTransaction!!.refundStatus == "1" -> {
                            addLine(rm.label,mTransaction!!.refundAmount!!,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.TOTAL && totalAmount != null  -> {
                            addLine(rm.label,"$totalAmount $currency",rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.LINE -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.TERMINAL_SN && isTimsRequire && !MainApp.sn.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${MainApp.sn}",
                                IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.RECEIPT_NO && !mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty()  -> {
                            if(!mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty())
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo}",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            }
                            else
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo}",
                                    IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            }
                        }
                        rm.label_type ==  ReceiptFields.CU_SN && !mTransaction!!.timsSignDetails!!.controlUnitSerialNumber.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction!!.timsSignDetails!!.controlUnitSerialNumber!!}", IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.VAT_DETAILS && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && rm.label.contains("|") && (fuelVat.enabled || shopVat.enabled)  -> {
                            val vatArray = rm.label.split("|")
                            if(vatArray.count() == 3)
                            {
                                receiptLayout2.addLine().addUnit(addReceiptUnit(vatArray[0],IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                    .addUnit(addReceiptUnit(vatArray[1] ,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                    .addUnit(addReceiptUnit(vatArray[2] ,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                            }
                        }
                        rm.label_type ==  ReceiptFields.VAT_GROUP_1  && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) &&  (fuelVat.enabled || shopVat.enabled)  -> {

                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+fuel_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(fuel_vatable_amount,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(fuel_vat_amount,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.VAT_GROUP_2  && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+shop_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(shop_vatable_amount,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit(shop_vat_amount,IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.VAT_GROUP_3  && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+shop_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit("0",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit("0",IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.VAT_GROUP_4 && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label +" = "+shop_vat_percentage,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit("0",IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                                .addUnit(addReceiptUnit("0",IPage.EAlign.RIGHT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.CU_SIGN && !esdSignature.isNullOrEmpty()  -> {
                            addLine(rm.label,esdSignature,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.CU_INVOICE_NO && isTimsRequire && !mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber!!}", IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.HEADER_3 && isTimsRequire && !mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty() -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.HEADER_4 && isTimsRequire -> {
                            receiptLayout2.addLine().addUnit()
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                        }
                        rm.label_type ==  ReceiptFields.BANK_AUTH_CODE  && mTransaction!!.modepay == VISA_VALUE -> {
                            val authCode = prefs.getStringSharedPreferences(AUTH_CODE)
                            if(!authCode.isNullOrEmpty())
                                addLine(rm.label,authCode,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.BANK_VOUCHER_NUMBER  && mTransaction!!.modepay == VISA_VALUE -> {
                            val voucherNo = prefs.getStringSharedPreferences(VOUCHER_NO)
                            if(!voucherNo.isNullOrEmpty() && voucherNo!=" ")
                                addLine(rm.label,voucherNo,rm.font_size,TEXT_STYLE_BOLD)
                        }

                        rm.label_type ==  ReceiptFields.PAYMENT_REFERENCE_NUMBER  && (mTransaction!!.modepay == VISA_VALUE || mTransaction!!.modepay == "18") -> {
                            val paymentRefNo = mTransaction!!.bank_reference_num
                            if(!paymentRefNo.isNullOrEmpty())
                                addLine(rm.label,paymentRefNo,rm.font_size,TEXT_STYLE_BOLD)

                        }

                        rm.label_type ==  ReceiptFields.PAN_LIMITS && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                            receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.CENTER,rm.font_size,TEXT_STYLE_BOLD))
                            receiptLayout2.addLine().addUnit()
                        }
                        rm.label_type ==  ReceiptFields.DAILY_LIMIT && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                            val dayLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.dailyCeiling!!.decimal(limitDecimal))
                            addLine(rm.label,dayLmt,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.WEEKLY_LIMIT && !mTransaction!!.weeklyCeiling.isNullOrEmpty()  -> {
                            val weekLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.weeklyCeiling!!.decimal(limitDecimal))
                            addLine(rm.label,weekLmt,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.MONTHLY_LIMIT && !mTransaction!!.monthlyCeiling.isNullOrEmpty()  -> {
                            val monthLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.monthlyCeiling!!.decimal(limitDecimal))
                            addLine(rm.label,monthLmt,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.CARD_BALANCE && !mTransaction!!.soldeCard.isNullOrEmpty() -> {
                            val cardBal = Support.getFormattedValue(this@TicketActivity,Support.formatDoubleAffichage(mTransaction!!.soldeCard!!.toDouble())?:"0".decimal(limitDecimal))
                            addLine(rm.label,cardBal,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.FISCAL_ID && !mTerminal!!.fiscalId.isNullOrEmpty() && !mTerminal!!.fiscalId.equals("0")   -> {
                            addLine(rm.label,fiscalId!!,rm.font_size,TEXT_STYLE_BOLD)
                        }
                        rm.label_type ==  ReceiptFields.MILEAGE && !mTransaction!!.kilometrage.isNullOrEmpty() -> {
                            addLine(rm.label,mTransaction!!.kilometrage!!,rm.font_size,TEXT_STYLE_BOLD)
                        }
                            rm.label_type ==  ReceiptFields.VEHICLE_NUMBER && !mTransaction!!.vehicleNumber.isNullOrEmpty() -> {
                                addLine(rm.label,mTransaction!!.vehicleNumber!!,rm.font_size,TEXT_STYLE_BOLD)
                            }
                        rm.label_type ==  ReceiptFields.QR_CODE && isTimsRequire -> {
                            generateQRCODE()
                        }
                        rm.label_type ==  ReceiptFields.QR_CODE && isQrCodeTicketRequired  && ticketType == CUSTOMER -> {
                            generateQrCodeTicket()
                        }
                    }
                }

            }
            receiptLayout2.addLine().addUnit()
            for(rm in fields.receipt_footer) {
                if (rm.is_available == 1) {
                    if (rm.label_type == ReceiptFields.CUSTOMER_MESSAGE) {
                        receiptLayout2.addLine().addUnit(
                            addReceiptUnit(
                                "***********************************",
                                IPage.EAlign.CENTER, rm.font_size, TEXT_STYLE_BOLD
                            )
                        )

                        if (rm.label.isNotEmpty()) {
                            if (rm.label.contains("|")) {
                                val msgArray = rm.label.split("|")
                                for (msg in msgArray) {
                                    receiptLayout2.addLine().addUnit(
                                        addReceiptUnit(
                                            msg,
                                            IPage.EAlign.CENTER,
                                            rm.font_size,
                                            TEXT_STYLE_BOLD
                                        )
                                    )
                                }
                            } else {
                                receiptLayout2.addLine().addUnit(
                                    addReceiptUnit(
                                        rm.label,
                                        IPage.EAlign.CENTER,
                                        rm.font_size,
                                        TEXT_STYLE_BOLD
                                    )
                                )
                            }
                        } else {
                            receipt.setAlign(Paint.Align.CENTER).setTextSize(80f)
                                .addText(getString(R.string.thanks_for_fueling))
                        }
                        receiptLayout2.addLine().addUnit(
                            addReceiptUnit(
                                "***********************************",
                                IPage.EAlign.CENTER, rm.font_size, TEXT_STYLE_BOLD
                            )
                        )
                    } else if (rm.label_type == ReceiptFields.CUSTOMER_RECEIPT_COPY && ticketType == CUSTOMER) {
                        receiptLayout2.addLine().addUnit(
                            addReceiptUnit(
                                rm.label,
                                IPage.EAlign.CENTER,
                                rm.font_size,
                                TEXT_STYLE_BOLD
                            )
                        )
                    } else if (rm.label_type == ReceiptFields.ATTENDANT_RECEIPT_COPY && ticketType == ATTENDANT) {
                        receiptLayout2.addLine().addUnit(
                            addReceiptUnit(
                                rm.label,
                                IPage.EAlign.CENTER,
                                rm.font_size,
                                TEXT_STYLE_BOLD
                            )
                        )
                    } else if (rm.label_type == ReceiptFields.NEW_LINE) {
                        receiptLayout2.addLine().addUnit(
                            addReceiptUnit(
                                "",
                                IPage.EAlign.CENTER,
                                rm.font_size,
                                TEXT_STYLE_BOLD
                            )
                        )
                    }
                }
            }
            receiptLayout2.addLine().addUnit(addReceiptUnit("",IPage.EAlign.CENTER,2,TEXT_STYLE_BOLD)) // extra space at the end of receipt

            val width = 384
            val receiptBitmap = iPaxGLPage.pageToBitmap(receiptLayout2, width)
           // runOnUiThread {  mBinding.ivTicketPreview.setImageBitmap(receiptBitmap)  }

//             TicketPrinter(this).printReceipt(receiptBitmap,2)
            return  receiptBitmap
        }
        else
        {
            return null
        }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            return null
        }
    }

    fun geDw14PrintBitmapLayout() : ArrayList<PrintCmd> {
        val printCommands = ArrayList<PrintCmd>()

        try {
            val TEXT_STYLE_NORMAL = 0
            val TEXT_STYLE_BOLD = 1
            //val TEXT_STYLE_UNDERLINE = 2
            //val TEXT_STYLE_ITALIC = 4
            val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
            qtyDecimal = receiptSetting.quantityDecimal ?: 2
            uniteDecimal = receiptSetting.unitPriceDecimal ?: 2
            val vatDecimal = receiptSetting.vatDecimal ?: 2
            val netDecimal = receiptSetting.netDecimal ?: 2
            val discountDecimal = receiptSetting.discountDecimal ?: 2
            val totalDecimal = receiptSetting.totalAmountDecimal ?: 2
            val limitDecimal = receiptSetting.limitDecimal ?: 2
            var totalAmount = ""
            var discountAmount =""
            var fuel_vat_percentage =""
            var shop_vat_percentage =""
            var fuel_vatable_amount=""
            var fuel_vat_amount=""
            var shop_vat_amount =""
            var shop_vatable_amount =""
            var vat =""
            var net =""
            var vatName =""
            var qty = ""
            val fiscalId =mTerminal!!.fiscalId
            var dateFormated: String? = mTransaction!!.dateTransaction
            if (dateFormated == null || !dateFormated.contains(" ")) {
                dateFormated = Support.dateToString(Date())
            }

            val dateArray = dateFormated!!.split(" ").toTypedArray()
            val date = dateArray[0]
            val time = dateArray[1]
            if(mTransaction!!.quantite != null && mTransaction!!.quantite != 0.0)
            {
                qty = Support.getFormattedValue(this,mTransaction!!.quantite!!.decimal(qtyDecimal))
            }


            if(mTransaction!!.netAmount != null && mTransaction!!.vatAmount != null && (fuelVat.enabled || shopVat.enabled ))
            {
                net = Support.getFormattedValue(this,mTransaction!!.netAmount!!.decimal(netDecimal))
                vat = Support.getFormattedValue(this, mTransaction!!.vatAmount!!.decimal(vatDecimal))

                if(mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID)
                {
                    fuel_vat_amount=vat
                    fuel_vat_percentage ="${referenceModel!!.fuelVat!!.percentage} %"
                    fuel_vatable_amount="${mTransaction!!.amount!!}"
                    shop_vat_amount = "0"
                    shop_vatable_amount ="0"
                    shop_vat_percentage ="0"
                    vatName ="${referenceModel!!.fuelVat!!.vat_name}"
                }
                else if(mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID)
                {
                    fuel_vat_amount="0"
                    fuel_vat_percentage ="0"
                    fuel_vatable_amount="0"
                    shop_vat_percentage ="${referenceModel!!.shopVat!!.percentage} %"
                    shop_vat_amount = vat
                    shop_vatable_amount ="${mTransaction!!.amount!!}"
                    vatName ="${referenceModel!!.fuelVat!!.vat_name}"
                }
            }
            if(mTransaction!!.discountAmount != null && mTransaction!!.isDiscountTransaction!! == 1)
            {
                discountAmount = Support.getFormattedValue(this@TicketActivity,mTransaction!!.discountAmount!!.toDouble().decimal(discountDecimal))
                val discountAmt = mTransaction!!.discountAmount!!.toDouble()
                totalAmount = Support.getFormattedValue(this@TicketActivity,(mTransaction!!.amount!!.toDouble() - discountAmt).decimal(totalDecimal))
            }
            else
            {
                totalAmount = Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.toDouble().decimal(totalDecimal))
            }
            if(referenceModel!!.receiptFormatFields != null)
            {


                val fields = referenceModel!!.receiptFormatFields

                //Header Format
                for(fd in fields!!.receipt_header)
                {
                    if(fd.is_available == 1)
                    {
                        if(fd.label_type ==  ReceiptFields.LOGO_R)
                        {

                            try
                            {
                                if(!prefs.logoPath.isNullOrEmpty()) {
                                    log(TAG, "Logo :: ${prefs.logoPath}")
                                    logoBitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
                                    logoBitmap = Support.getResizedBitmap(logoBitmap!!, 200, 200)
                                    printCommands.add(PrintCmd(logoBitmap,PrintContentType.IMAGE))
                                }
                            }
                            catch (e:Exception)
                            {
                                e.printStackTrace()
                            }

                        }
                        else if(fd.label_type ==  ReceiptFields.TELECOLLECT_COMPANY_NAME && mTerminal != null)
                        {
                            printCommands.add(PrintCmd(referenceModel!!.COMPANY.name,AlignmentType.CENTER,true))
                        }
                        else if(fd.label_type ==  ReceiptFields.TELECOLLECT_STATION_NAME && mTerminal != null)
                        {
                            printCommands.add(PrintCmd(mTerminal!!.stationName!!,AlignmentType.CENTER,true))
                        }
                        else if(fd.label_type ==  ReceiptFields.TELECOLLECT_ADDRESS)
                        {
                            printCommands.add(PrintCmd(mTerminal!!.address+", "+mTerminal!!.city,AlignmentType.CENTER,true))
                        }
                        else if(fd.label_type ==  ReceiptFields.DATE)
                        {
                            printCommands.add(PrintCmd("$receiptDate $time",AlignmentType.CENTER,true))
                        }
                        else if(fd.label_type ==  ReceiptFields.DISPUTED_TRANSACTION )
                        {
                            if(mTransaction != null && mTransaction!!.isDisputedTrx == 1)
                                printCommands.add(PrintCmd(fd.label,AlignmentType.CENTER,true))
                        }
                        else if(fd.label_type ==  ReceiptFields.NEW_LINE)
                        {
                            printCommands.add(PrintCmd("",true))
                        }
                        else {
                            printCommands.add(PrintCmd(fd.label,true))
                        }
                    }
                }
                printCommands.add(PrintCmd("",true))
                for(rm in fields.receipt_middle)
                {
                    if(rm.is_available == 1)
                    {
                        when  {

                            rm.label_type ==  ReceiptFields.NEW_LINE -> {
                                printCommands.add(PrintCmd("",true))
                            }
                            rm.label_type ==  ReceiptFields.HEADER_1 && isTimsRequire -> {
                                printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                            }
                            rm.label_type ==  ReceiptFields.TRX_ID && !mTransaction!!.reference.isNullOrEmpty()   -> {
                                printCommands.add(PrintCmd(rm.label+" : "+mTransaction!!.reference,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.FPOS_SEQUENCE_NO && !mTransaction!!.sequenceController.isNullOrEmpty()   -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.sequenceController}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.SALE_ID && !mTransaction!!.fccSaleId.isNullOrEmpty()   -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.fccSaleId}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.TRANSACTION_BY && !intentExtrasModel!!.attendantName.isNullOrEmpty()  -> {
                                val name= intentExtrasModel!!.attendantName!!.split(" ")
                                printCommands.add(PrintCmd(rm.label+"[L]"+name[name.size - 1],AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.CUSTOMER_PIN && !mTransaction!!.timsSignDetails!!.customer_details!!.customerPin.isNullOrEmpty() && mTransaction!!.timsSignDetails!!.customer_details!!.customerPin != "0000"-> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.timsSignDetails!!.customer_details!!.customerPin!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PAN && !mTransaction!!.pan.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd("--------------------------------",AlignmentType.CENTER,true))
                                if(mTransaction!!.modepay == CARD_VALUE || mTransaction!!.modepay == VISA_VALUE)
                                {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.pan),AlignmentType.LEFT,true))
                                }
                                else {
                                    if(intentExtrasModel!!.modePaymentModel != null) {
                                        printCommands.add(PrintCmd(intentExtrasModel!!.modePaymentModel!!.payment_name!!+"[L]"+mTransaction!!.pan!!,AlignmentType.LEFT,true))
                                    }
                                    else
                                    {
                                        printCommands.add(PrintCmd(getModePayment(mTransaction!!.modepay)!!+"[L]"+mTransaction!!.pan!!,AlignmentType.LEFT,true))
                                    }
                                }
                            }
                            rm.label_type ==  ReceiptFields.PAYMENT_TYPE && !mTransaction!!.modepay.isNullOrEmpty()  -> {
                                if(intentExtrasModel!!.modePaymentModel != null)
                                {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${intentExtrasModel!!.modePaymentModel!!.payment_name}",AlignmentType.LEFT,true))
                                }
                                else
                                {
                                    printCommands.add(PrintCmd(rm.label+"[L]"+"${getModePayment(mTransaction!!.modepay)}",AlignmentType.LEFT,true))
                                }
                            }
                            rm.label_type ==  ReceiptFields.UNIT_PRICE && mTransaction!!.unitPrice != null && mTransaction!!.unitPrice != 0.0  -> {
                                val unit = Support.getFormattedValue(this,mTransaction!!.unitPrice!!.decimal(uniteDecimal))
                                printCommands.add(PrintCmd(rm.label+"[L]"+unit,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.REFERENCE -> {
                               // receiptLayout2.addLine().addUnit(addReceiptUnit(rm.label,IPage.EAlign.LEFT,rm.font_size,TEXT_STYLE_BOLD))
                                printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT ,true))
                            }
                            rm.label_type ==  ReceiptFields.CARD_EXPIRY && !intentExtrasModel!!.dateExp.isNullOrEmpty()-> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"${intentExtrasModel!!.dateExp}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.TAG_NFC && !mTransaction!!.tagNFC.isNullOrEmpty()-> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.tagNFC}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && isTimsRequire && (mProduit != null && mProduit!!.libelle != null && mTransaction!!.quantite != null && mTransaction!!.amount != null)-> {
                                var qtyValue=qty
                                if(mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) {
                                    qtyValue = "$qty $fuelQtyUnit"
                                }
                                printCommands.add(PrintCmd(mProduit!!.libelle!!+"[L]"+qtyValue+"[L]"+Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal)),AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PRODUCT_DETAILS && mTransaction!!.categoryId != PRODUCT.FUEL_CATEGORY_ID && mTransaction!!.productName != null && mTransaction!!.amount != null -> {
                                printCommands.add(PrintCmd(mTransaction!!.productName!!+"[L]"+Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal)),AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.AMOUNT && mTransaction!!.amount != null  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"${Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal))}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PRODUCT_NAME && !mTransaction!!.productName.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"${mTransaction!!.productName}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PAN_LOYALTY && !mTransaction!!.panLoyalty.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.panLoyalty),AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PAN_HOLDER_NAME && !mTransaction!!.nomPorteur.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.nomPorteur),AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.PUMP_NO && !mTransaction!!.pumpId.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+Support.hashPan(mTransaction!!.pumpId),AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.QTY && mTransaction!!.quantite != null && mTransaction!!.quantite != 0.0 -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"$qty $fuelQtyUnit",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.HEADER_2 && isTimsRequire -> {
                                printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                            }
                            rm.label_type == ReceiptFields.DATE  && !receiptDate.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+receiptDate,AlignmentType.LEFT,true))
                            }
                            rm.label_type == ReceiptFields.TIME  && !time.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+time,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.VAT &&  (fuelVat.enabled || shopVat.enabled) && !vat.isNullOrEmpty() && vat != "0.0" -> {
                                val isInclusive = fuelVat.type == 0
                                val type = if(isInclusive) "Incl." else  "Excl."
                                printCommands.add(PrintCmd("${rm.label} (${mTransaction!!.vatPercentage}% $type)\n",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.VAT_NAME && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) && !vatName.isNullOrEmpty()-> {
                                printCommands.add(PrintCmd(vatName+"[C]"+"${mTransaction!!.vatPercentage}%"+"[L]"+time,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.ROUNDING_ADJUSTMENT && !mTransaction!!.roundingAdjustment.isNullOrEmpty() -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.roundingAdjustment!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.NET_AMOUNT && (fuelVat.enabled || shopVat.enabled) && !net.isNullOrEmpty() && net != "0.0" -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+net,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.SUB_INC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && !net.isNullOrEmpty() && net != "0.0" -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.amount!!.toString(),AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.SUB_EXC_TAX_AMOUNT && (fuelVat.enabled || shopVat.enabled) && !net.isNullOrEmpty() && net != "0.0" -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+net,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.DISCOUNT && mTransaction!!.discountAmount != null && mTransaction!!.isDiscountTransaction == 1 -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"-$discountAmount",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.REFUND_AMOUNT && mTransaction!!.refundAmount != null && mTransaction!!.refundStatus == "1" -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.refundAmount!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.TOTAL && totalAmount != null  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+"$totalAmount $currency",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.LINE -> {
                                printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                            }
                            rm.label_type ==  ReceiptFields.TERMINAL_SN && isTimsRequire && !MainApp.sn.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd("${rm.label} : ${MainApp.sn}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.RECEIPT_NO && !mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty()  -> {
                                if(!mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo.isNullOrEmpty())
                                {
                                    printCommands.add(PrintCmd("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo}",AlignmentType.LEFT,true))
                                }
                                else
                                {
                                    printCommands.add(PrintCmd("${rm.label} : ${mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo}",AlignmentType.LEFT,true))
                                }
                            }
                            rm.label_type ==  ReceiptFields.CU_SN && !mTransaction!!.timsSignDetails!!.controlUnitSerialNumber.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd("${rm.label} : ${mTransaction!!.timsSignDetails!!.controlUnitSerialNumber!!}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.VAT_DETAILS && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && rm.label.contains("|") && (fuelVat.enabled || shopVat.enabled)  -> {
                                val vatArray = rm.label.split("|")
                                if(vatArray.count() == 3)
                                {
                                    printCommands.add(PrintCmd(vatArray[0]+"[L]"+vatArray[1]+"[L]"+vatArray[2],AlignmentType.LEFT,true))
                                }
                            }
                            rm.label_type ==  ReceiptFields.VAT_GROUP_1  && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) &&  (fuelVat.enabled || shopVat.enabled)  -> {
                                printCommands.add(PrintCmd(rm.label+" = [L]"+fuel_vat_percentage,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd(fuel_vatable_amount+" = [L]"+fuel_vat_percentage,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("[L][L]"+fuel_vat_amount,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.VAT_GROUP_2  && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {

                                printCommands.add(PrintCmd(rm.label+" = [L]"+shop_vat_percentage,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd(shop_vatable_amount+" = [L]"+shop_vat_percentage,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("[L][L]"+shop_vat_amount,AlignmentType.LEFT,true))

                            }
                            rm.label_type ==  ReceiptFields.VAT_GROUP_3  && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {
                                printCommands.add(PrintCmd(rm.label+" = [L]"+shop_vat_percentage,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("[L][L]0",AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("[L][L]0",AlignmentType.RIGHT,true))
                            }
                            rm.label_type ==  ReceiptFields.VAT_GROUP_4 && isTimsRequire && (mTransaction!!.categoryId == PRODUCT.SHOP_CATEGORY_ID || mTransaction!!.categoryId == PRODUCT.FUEL_CATEGORY_ID) && (fuelVat.enabled || shopVat.enabled) -> {
                                printCommands.add(PrintCmd(rm.label+" = "+shop_vat_percentage,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("[L][L]0",AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("[L][L]0",AlignmentType.LEFT,true))

                                printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("${rm.label} : [L]${mTransaction!!.timsSignDetails!!.controlUnitSerialNumber!!}",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.CU_SIGN && !esdSignature.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+esdSignature,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.CU_INVOICE_NO && isTimsRequire && !mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label+" : [L]"+mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.HEADER_3 && isTimsRequire && !mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber.isNullOrEmpty() -> {
                                printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.HEADER_4 && isTimsRequire -> {
                                printCommands.add(PrintCmd("",AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.BANK_AUTH_CODE  && mTransaction!!.modepay == VISA_VALUE -> {
                                val authCode = prefs.getStringSharedPreferences(AUTH_CODE)
                                if(!authCode.isNullOrEmpty())
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.BANK_VOUCHER_NUMBER  && mTransaction!!.modepay == VISA_VALUE -> {
                                val voucherNo = prefs.getStringSharedPreferences(VOUCHER_NO)
                                if(!voucherNo.isNullOrEmpty() && voucherNo!=" ")
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                            }

                            rm.label_type ==  ReceiptFields.PAYMENT_REFERENCE_NUMBER  && (mTransaction!!.modepay == VISA_VALUE || mTransaction!!.modepay == "18") -> {
                                val paymentRefNo = mTransaction!!.bank_reference_num
                                if(!paymentRefNo.isNullOrEmpty())
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                            }

                            rm.label_type ==  ReceiptFields.PAN_LIMITS && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                                printCommands.add(PrintCmd(rm.label,AlignmentType.LEFT,true))
                                printCommands.add(PrintCmd("",AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.DAILY_LIMIT && !mTransaction!!.dailyCeiling.isNullOrEmpty()  -> {
                                val dayLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.dailyCeiling!!.decimal(limitDecimal))
                                printCommands.add(PrintCmd(rm.label+"[L]"+dayLmt,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.WEEKLY_LIMIT && !mTransaction!!.weeklyCeiling.isNullOrEmpty()  -> {
                                val weekLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.weeklyCeiling!!.decimal(limitDecimal))
                                printCommands.add(PrintCmd(rm.label+"[L]"+weekLmt,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.MONTHLY_LIMIT && !mTransaction!!.monthlyCeiling.isNullOrEmpty()  -> {
                                val monthLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.monthlyCeiling!!.decimal(limitDecimal))
                                printCommands.add(PrintCmd(rm.label+"[L]"+monthLmt,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.CARD_BALANCE && !mTransaction!!.soldeCard.isNullOrEmpty() -> {
                                val cardBal = Support.getFormattedValue(this@TicketActivity,Support.formatDoubleAffichage(mTransaction!!.soldeCard!!.toDouble())?:"0".decimal(limitDecimal))
                                printCommands.add(PrintCmd(rm.label+"[L]"+cardBal,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.FISCAL_ID && !mTerminal!!.fiscalId.isNullOrEmpty() && !mTerminal!!.fiscalId.equals("0")   -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+fiscalId!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.MILEAGE && !mTransaction!!.kilometrage.isNullOrEmpty() -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.kilometrage!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.VEHICLE_NUMBER && !mTransaction!!.vehicleNumber.isNullOrEmpty() -> {
                                printCommands.add(PrintCmd(rm.label+"[L]"+mTransaction!!.vehicleNumber!!,AlignmentType.LEFT,true))
                            }
                            rm.label_type ==  ReceiptFields.QR_CODE && isTimsRequire -> {
                                try {
                                    var qrCode = ""
                                    if(!mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode.isNullOrEmpty()) {
                                        qrCode =mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode!!
                                    }
                                    if(qrCode.isNotEmpty()) {
                                        val content =mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode
                                        var bitmap =  QRCode.generateBitmap(content)
                                        bitmap = Support.getResizedBitmap(bitmap,300,300)
                                        receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
                                        printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
                                    }
                                } catch (e: WriterException) {
                                    Log.e("Tag", e.toString())
                                }
                            }
                            rm.label_type ==  ReceiptFields.QR_CODE && isQrCodeTicketRequired  && ticketType == CUSTOMER -> {
                                //generateQrCodeTicket()
                                try {
                                    val qrCodeResponse = intentExtrasModel!!.mTransaction!!.qrCodeTicket
                                    if(qrCodeResponse!=null) {
                                        val jsonString = Gson().toJson(qrCodeResponse)
                                        if(jsonString.isNotEmpty()){
                                            var bitmap =  QRCode.generateBitmap(jsonString)
                                            bitmap = Support.getResizedBitmap(bitmap,300,300)
                                            printCommands.add(PrintCmd(bitmap,PrintContentType.IMAGE))
                                        }
                                    }
                                } catch (e: WriterException) {
                                    Log.e("Tag", e.toString())
                                }
                            }
                        }
                    }
                }
                receiptLayout2.addLine().addUnit()
                for(rm in fields.receipt_footer) {
                    if (rm.is_available == 1) {
                        if (rm.label_type == ReceiptFields.CUSTOMER_MESSAGE) {
                            printCommands.add(PrintCmd("***********************************",AlignmentType.CENTER,true))

                            if (rm.label.isNotEmpty()) {
                                if (rm.label.contains("|")) {
                                    val msgArray = rm.label.split("|")
                                    for (msg in msgArray) {
                                        printCommands.add(PrintCmd(msg,AlignmentType.CENTER,true))
                                    }
                                } else {
                                    printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                                }
                            } else {
                                printCommands.add(PrintCmd(getString(R.string.thanks_for_fueling),AlignmentType.CENTER,true))
                            }
                            printCommands.add(PrintCmd("***********************************",AlignmentType.CENTER,true))
                        } else if (rm.label_type == ReceiptFields.CUSTOMER_RECEIPT_COPY && ticketType == CUSTOMER) {
                            printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                        } else if (rm.label_type == ReceiptFields.ATTENDANT_RECEIPT_COPY && ticketType == ATTENDANT) {
                            printCommands.add(PrintCmd(rm.label,AlignmentType.CENTER,true))
                        } else if (rm.label_type == ReceiptFields.NEW_LINE) {
                            printCommands.add(PrintCmd("",AlignmentType.CENTER,true))
                        }
                    }
                }
                printCommands.add(PrintCmd("",AlignmentType.CENTER,true))
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        if(printCommands.isEmpty())
        {
            printCommands.add(PrintCmd("Something missing in printing data, please check and try again",AlignmentType.CENTER,true))
        }
        return printCommands
    }

    private fun getPrintBitmapLayout1() : Bitmap {
        val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
        val qtyDecimal = receiptSetting.quantityDecimal ?: 2
        val uniteDecimal = receiptSetting.unitPriceDecimal ?: 2
        val vatDecimal = receiptSetting.vatDecimal ?: 2
        val netDecimal = receiptSetting.netDecimal ?: 2
        val discountDecimal = receiptSetting.discountDecimal ?: 2
        val totalDecimal = receiptSetting.totalAmountDecimal ?: 2
        val limitDecimal = receiptSetting.limitDecimal ?: 2

        var dateFormated: String? = mTransaction!!.dateTransaction
        if (dateFormated == null || !dateFormated.contains(" ")) {
            dateFormated = Support.dateToString(Date())
        }

        val dateArray = dateFormated!!.split(" ").toTypedArray()
        val date = dateArray[0]
        val time = dateArray[1]

        if(mTransaction!!.quantite == null) {
            mTransaction!!.quantite = intentExtrasModel!!.mVolumeFUSION
        }

        mTerminal = referenceModel!!.terminal

        var bitmap : Bitmap? = null
        try {
            bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap,400,400)
            receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK).addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)

        } catch (e:java.lang.Exception) { log(TAG,"LOGO Not printed on receipt  ${e.message}") }

        if(mProduit!!.libelle=="CARD RECHARGE")
            mProduit!!.libelle = "RECHARGE"

        receipt = ReceiptBuilder(1200)
        receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK).addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)
        receipt.setTextSize(65f).setTypeface(this@TicketActivity, "fonts/Roboto-Bold.ttf").setAlign(Paint.Align.CENTER).addText("").
        addText(mTerminal!!.stationName).
        addText(mTerminal!!.address+", "+mTerminal!!.city)

        if (intentExtrasModel!!.loyaltyTrx) {
            receipt.addText("")
            receipt.addText("""* * * ${resources.getString(R.string.loyalty_label)} * * *""")
        }

        if(mTransaction!!.isDisputedTrx!=null ){
            if(mTransaction != null && mTransaction!!.isDisputedTrx==1) {
                receipt.addText("")
                receipt.addText("* * * " + getString(R.string.disputed_trx) + " * * *")
            }
        }

        receipt.addText("")
        receipt.addLine()
        setAlignment(Paint.Align.LEFT,65f).setAlign(Paint.Align.CENTER).addText("${if(receiptDate.isNotEmpty())receiptDate else date} $time")
        receipt.addParagraph()

        if (mTransaction!!.pumpId != null && mTransaction!!.pumpId!!.isNotEmpty()) {
            setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.pump_no_p),false)
            setAlignment(Paint.Align.RIGHT,65f).addText(mTransaction!!.pumpId)
        }

        if(!mProduit!!.libelle.isNullOrEmpty()){
            setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.product_p),false)
            setAlignment(Paint.Align.RIGHT,65f).addText(" ${mProduit!!.libelle}")
        }

        setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.txn_type_p), false)

        var mop = if(intentExtrasModel!!.modePaymentModel!=null){
            intentExtrasModel!!.modePaymentModel!!.payment_name
        } else {
            getModePayment(mTransaction!!.modepay)
        }

        setAlignment(Paint.Align.RIGHT,65f).addText(" $mop")

        setAlignment(Paint.Align.LEFT,65f).setTypeface(this@TicketActivity, "fonts/Roboto-Bold.ttf").addText(resources.getString(R.string.txn_id_p),false)
        setAlignment(Paint.Align.RIGHT,65f).setTypeface(this@TicketActivity, "fonts/Roboto-Bold.ttf").addText(" ${mTransaction!!.reference}")
        receipt.addText("")
        receipt.addLine().setAlign(Paint.Align.CENTER)
        var taxModel = intentExtrasModel!!.taxModel
        if(taxModel == null)
        {
            taxModel = TaxModel(false,0.0,0.0,0.0,mTransaction!!.amount!!)
        }
        log(TAG, "taxModel :: $taxModel")
        if (mTransaction!!.amount != null) {
            if(intentExtrasModel!!.workFlowTransaction != Workflow.SETTINGS_RECHARGE_CARD){
                if (mTransaction!!.quantite != null && mTransaction!!.quantite != 0.0) {
                    val qty = Support.getFormattedValue(this,mTransaction!!.quantite!!.decimal(qtyDecimal))
                    setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.qty_p) , false)
                    setAlignment(Paint.Align.RIGHT,80f).addText("$qty $fuelQtyUnit")
                }
                if (mTransaction!!.unitPrice != null && mTransaction!!.unitPrice != 0.0) {
                    val unit = Support.getFormattedValue(this,mTransaction!!.unitPrice!!.decimal(uniteDecimal))
                    setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.price_p) , false)
                    setAlignment(Paint.Align.RIGHT,80f).addText("$unit $currency")
                }
                try{
                    if(!intentExtrasModel!!.loyaltyTrx && (fuelVat.enabled || shopVat.enabled))
                    {
                        val net = Support.getFormattedValue(this@TicketActivity,taxModel.netAmount.decimal(netDecimal))
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.net_amount_p), false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("$net $currency")

                        val vat = Support.getFormattedValue(this@TicketActivity, taxModel.taxAmount.decimal(vatDecimal))
                        setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.vat_p) , false)
                        setAlignment(Paint.Align.RIGHT,80f).addText("$vat $currency")
                    }
                } catch (e:Exception ){
                    e.printStackTrace()
                }
                if(intentExtrasModel!!.discountAmount != null && intentExtrasModel!!.isDiscountTransaction!!)
                {
                    val totalAmt = Support.getFormattedValue(this@TicketActivity,taxModel.totalAmount.decimal(totalDecimal))
                    setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.fuel_total_p) , false)
                    setAlignment(Paint.Align.RIGHT,80f).addText("$totalAmt $currency")

                    val discountAmt = Support.getFormattedValue(this@TicketActivity,intentExtrasModel!!.discountAmount!!.toDouble().decimal(discountDecimal))
                    setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.discount) , false)
                    setAlignment(Paint.Align.RIGHT,80f).addText("-$discountAmt $currency")

                    setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.total_p) , false)
                    val discountAmount = intentExtrasModel!!.discountAmount!!.toDouble()
                    val total = Support.getFormattedValue(this@TicketActivity,(taxModel.totalAmount - discountAmount).decimal(totalDecimal))
                    setAlignment(Paint.Align.RIGHT,80f).addText("$total $currency")
                }
                else
                {
                    val totalAmt = Support.getFormattedValue(this@TicketActivity,taxModel.totalAmount.decimal(totalDecimal))
                    setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.total_p) , false)
                    setAlignment(Paint.Align.RIGHT,80f).addText("$totalAmt $currency")
                }
            }
            else {
                receipt.addText("")
                setAlignment(Paint.Align.LEFT,80f).addText(resources.getString(R.string.total_p) , false)
                setAlignment(Paint.Align.RIGHT,80f).addText("${Support.getFormattedValue(this@TicketActivity,mTransaction!!.amount!!.decimal(totalDecimal))} $currency")
            }
        }
        receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("___________________________________")

        if(!mTransaction!!.dailyCeiling.isNullOrEmpty() && !mTransaction!!.weeklyCeiling.isNullOrEmpty() && !mTransaction!!.monthlyCeiling.isNullOrEmpty()){
            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("")
            if ( !mTransaction!!.dailyCeiling.isNullOrEmpty()) {
                val dayLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.dailyCeiling!!.decimal(limitDecimal))
                setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.daily_limit_p) , false)
                setAlignment(Paint.Align.RIGHT,80f).addText(dayLmt )
            }
            if ( !mTransaction!!.weeklyCeiling.isNullOrEmpty()) {
                val weekLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.weeklyCeiling!!.decimal(limitDecimal))
                setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.weekly_limit_p) , false)
                setAlignment(Paint.Align.RIGHT,80f).addText(weekLmt  )
            }
            if ( !mTransaction!!.monthlyCeiling.isNullOrEmpty()) {
                val monthLmt = Support.getFormattedValue(this@TicketActivity,mTransaction!!.monthlyCeiling!!.decimal(limitDecimal))
                setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.monthly_limit_p) , false)
                setAlignment(Paint.Align.RIGHT,80f).addText(monthLmt )
            }
        }

        if (mTransaction!!.soldeCard != null && intentExtrasModel!!.cardType != null && intentExtrasModel!!.cardType == AppConstant.PREPAID_CARD) {
            if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD)
                receipt.addText("")
            val cardBal = Support.getFormattedValue(this@TicketActivity,mTransaction!!.soldeCard!!.decimal(limitDecimal))
            setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.card_bal_p) , false)
            setAlignment(Paint.Align.RIGHT,80f).addText( cardBal )
            receipt.addText("")
            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("___________________________________")
        }
        else if (intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_RECHARGE_CARD && intentExtrasModel!!.cardBalance != null && mAmount != null) {
            val cardBal = Support.getFormattedValue(this@TicketActivity,Support.formatDoubleAffichage(intentExtrasModel!!.cardBalance!!.replace(",",".").toDouble() + mAmount!!)?:"0".decimal(limitDecimal))
            setAlignment(Paint.Align.LEFT,65f).addText(resources.getString(R.string.card_bal_p) , false)
            setAlignment(Paint.Align.RIGHT,80f).addText("$cardBal $currency")
            receipt.addText("")
            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("___________________________________")
        }

        if (!mTransaction!!.pan.isNullOrEmpty()) {
            receipt.addText("")
            setAlignment(Paint.Align.LEFT, 65f).addText(resources.getString(R.string.pan_p) + " " + Support.hashPan(mTransaction!!.pan))
        }
        if (!mTransaction!!.panLoyalty.isNullOrEmpty()) {
            setAlignment(Paint.Align.LEFT, 65f).addText(resources.getString(R.string.loyalty_p) + " " + mTransaction!!.panLoyalty)
        }
        if (!mTransaction!!.nomPorteur.isNullOrEmpty()) {
            setAlignment(Paint.Align.LEFT, 65f).addText(resources.getString(R.string.name_p) + " " + mTransaction!!.nomPorteur)
        }
        if (!mTransaction!!.dateExp.isNullOrEmpty()) {
            setAlignment(
                Paint.Align.LEFT,
                65f
            ).addText(resources.getString(R.string.date_exp_p) + " " + mTransaction!!.dateExp)
        }
        if (!mTransaction!!.tagNFC.isNullOrEmpty()) {
            setAlignment(
                Paint.Align.LEFT,
                65f
            ).addText(resources.getString(R.string.tag_no_p) + " " + mTransaction!!.tagNFC)
        }

        if(!mTransaction!!.kilometrage.isNullOrEmpty()){
            setAlignment(
                Paint.Align.LEFT,
                65f
            ).addText(getString(R.string.mileage) + " " + mTransaction!!.kilometrage)
        }

        if(!mTransaction!!.vehicleNumber.isNullOrEmpty()){
            setAlignment(
                Paint.Align.LEFT,
                65f
            ).addText(getString(R.string.vehicle_number) + ": " + mTransaction!!.vehicleNumber)
        }


        if (!mTerminal!!.fiscalId.isNullOrEmpty() && !mTerminal!!.fiscalId.equals("0")) {
            setAlignment(
                Paint.Align.LEFT,
                65f
            ).addText(resources.getString(R.string.fiscal_p) + " " + mTerminal!!.fiscalId)
        }

        if (mTransaction!!.transactionSignature != null && mTransaction!!.transactionSignature!!.isNotEmpty()) {
            setAlignment(
                Paint.Align.LEFT,
                65f
            ).addText(resources.getString(R.string.signature_p) + " " + mTransaction!!.transactionSignature)
        }

        if (intentExtrasModel!!.typePay == VISA_VALUE) {
            setAlignment(Paint.Align.LEFT,65f).addText(getString(R.string.auth_id_p)+" "+prefs.getStringSharedPreferences(AUTH_CODE))
            setAlignment(Paint.Align.LEFT,65f).addText(getString(R.string.payment_ref_number)+" "+mTransaction!!.bank_reference_num)
            if(!prefs.getStringSharedPreferences(VOUCHER_NO).isNullOrEmpty() && prefs.getStringSharedPreferences(VOUCHER_NO)!=" "){
                setAlignment(Paint.Align.LEFT,65f).addText(getString(R.string.voucher_p)+" "+prefs.getStringSharedPreferences(VOUCHER_NO))
            }
        }

        if(mTransaction!!.modepay=="18"){
            setAlignment(Paint.Align.LEFT, 65f).addText(getString(R.string.payment_ref_number) + " " + Support.hashPan(mTransaction!!.bank_reference_num))
        }


        receipt.addText("")
        if (ticketType == CUSTOMER) {
            setAlignment(Paint.Align.LEFT,65f)
            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")
            log(TAG, "Customer Message:: $customerMessage")

            if(customerMessage.isNotEmpty())
            {
                if(customerMessage.contains("|"))
                {
                    val msgArray = customerMessage.split("|")
                    for(msg in msgArray)
                    {
                        receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(msg)
                    }
                }
                else
                {
                    receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(customerMessage)
                }
            }
            else
            {
                customerMessage = getString(R.string.thanks_for_fueling)
                receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(customerMessage)
            }

            receipt.setTextSize(80f).setAlign(Paint.Align.CENTER).addText("----------------------------------")
            receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(customerCopy.ifEmpty { getString(R.string.customer_copy_p) })
        } else {
            receipt.setAlign(Paint.Align.CENTER).setTextSize(60f).addText(merchantCopy.ifEmpty { getString(R.string.operator_copy_p) } )
            receipt.setAlign(Paint.Align.CENTER).setTextSize(80f).addText(" ")
        }

        receiptBitmap = receipt.build()

        try {
            if(bitmap!=null){
                bitmap.recycle()
            }
        } catch (e:Exception){ }

//        mBinding.ivTicketPreview.setImageBitmap(receiptBitmap)
        return receiptBitmap!!
    }

    private fun generateQRCODE() {
        try {
            var qrCode = ""
            if(!mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode.isNullOrEmpty())
            {
                qrCode =mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode!!
            }

            if(qrCode.isNotEmpty())
            {
                val content =mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode
                var bitmap =  QRCode.generateBitmap(content)
                bitmap = Support.getResizedBitmap(bitmap,300,300)
                receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
            }
        } catch (e: WriterException) {
            Log.e("Tag", e.toString())
        }

    }

    private fun generateQrCodeTicket() {
        try {
            val qrCodeResponse = intentExtrasModel!!.mTransaction!!.qrCodeTicket
            if(qrCodeResponse!=null) {
                val jsonString = Gson().toJson(qrCodeResponse)
                if(jsonString.isNotEmpty()){
                    var bitmap =  QRCode.generateBitmap(jsonString)
                    bitmap = Support.getResizedBitmap(bitmap,300,300)
                    receiptLayout2.addLine().addUnit(bitmap,IPage.EAlign.CENTER)
                }
            }
        } catch (e: WriterException) {
            Log.e("Tag", e.toString())
        }

    }


    override fun setObserver() {
        mViewModel.transactionObserver.observe(this) {
            if(it.reponse == "1")
            {
                intentExtrasModel!!.mTransaction!!.flagTelecollecte = 1
                updateTransactionStatus()
            }
        }
    }

    private fun updateTransactionStatus(){
        intentExtrasModel!!.mTransaction!!.transactionStatus = 1
        updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
    }

    lateinit var saveProgressDialog : android.app.AlertDialog
    fun gotoNextActivity()
    {
          /*  if(prefs.transactionCount >= 5)
            {
                prefs.isRestartApplication = "true"
                mBinding.layoutPrintingProgress.visibility = View.VISIBLE
                mBinding.ivTicketPreview.visibility = View.GONE
                mBinding.prompt.text = getString(R.string.please_wait_saving_transaction_details)
                restartApplicationIdle()
            }
            else
            {
                val mIntent: Intent = if (referenceModel!!.TERMINAL_TYPE == UN_ATTENDANT_MODE) {
                    Intent(this@TicketActivity, UnattendantModePayActivity::class.java)
                } else {
                    Intent(this@TicketActivity, MenuActivity::class.java)
                }
                startActivity(mIntent)
                finish()
            }*/
             clearTrxHistoryInSp()

             val mIntent: Intent = if (referenceModel!!.TERMINAL_TYPE == UN_ATTENDANT_MODE) {
                 Intent(this@TicketActivity, UnattendantModePayActivity::class.java)
             } else {
                 Intent(this@TicketActivity, MenuActivity::class.java)
             }
             mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
             mIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
             mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
             startActivity(mIntent)
             finish()

    }
    private fun clearTrxHistoryInSp() {
        try {
            CoroutineScope(Dispatchers.IO).launch {
                try {
                    if (prefs.isDeleteLogSuccessTransaction && mTransaction!!.transactionStatus == 1 && !prefs.isPowerCutGetFuelSaleTrxMsgSent && !prefs.isPumpError) {
                        val date = SimpleDateFormat(
                            "dd-MM-yyyy",
                            Locale.getDefault()
                        ).format(Calendar.getInstance().time)
                        val dateFileName = "TRX${date}_${prefs.logCount}.log"
                        val serviceFileName = "TRX${AppConstant.FUEL_SERVICE_LOG_NAME}_${prefs.logReferenceNo}.log"
                        val trxFileName = "${mTransaction!!.reference}.log"
                        val fleetCardFileName = "TRX${AppConstant.FLEET_CARD_LOG_NAME}_${prefs.logReferenceNo}.log"
                        Log.i(TAG, "current log file :: $trxFileName")
                        Log.i(TAG, "service Log file :: $serviceFileName")
                        Log.i(TAG, "date Log file :: $dateFileName")
                        val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
                        val dir = File(path + File.separator + AppConstant.LOG_FOLDER_NAME)
                        val tFile = File(dir, trxFileName)
                        val sFile = File(dir, serviceFileName)
                        val dFile = File(dir, dateFileName)
                        val fFile = File(dir, fleetCardFileName)
                        if(tFile.exists()) { tFile.delete() }
                        if(sFile.exists()) { sFile.delete() }
                        if(dFile.exists()) { dFile.delete() }
                        if(fFile.exists()) { fFile.delete() }
                        mainApp.setFuelServiceName(AppConstant.FUEL_SERVICE_LOG_NAME)
                        prefs.logCount++

                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }

        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
    }

    override fun onBackPressed() {

    }


}


