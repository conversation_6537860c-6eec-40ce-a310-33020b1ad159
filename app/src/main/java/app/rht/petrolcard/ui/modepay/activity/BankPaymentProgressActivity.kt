package app.rht.petrolcard.ui.modepay.activity

import android.app.Activity
import android.app.AlertDialog
import android.app.Dialog
import android.content.*
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.view.Window
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityBankpaymentProgressBinding
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.ticket.activity.TicketActivity
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.REFUND_REQUEST
import app.rht.petrolcard.utils.constant.AppConstant.VOID_REQUEST
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.tax.TaxUtils
import com.ebe.ebeunifiedlibrary.factory.ITransAPI
import com.ebe.ebeunifiedlibrary.factory.TransAPIFactory
import com.ebe.ebeunifiedlibrary.message.*
import com.ebe.ebeunifiedlibrary.sdkconstants.SdkConstants
import kotlinx.android.synthetic.main.toolbar.view.*
import org.apache.commons.lang3.exception.ExceptionUtils
import org.json.JSONObject
import java.lang.Exception
import java.util.concurrent.TimeUnit
import kotlin.math.roundToInt


@Suppress("DEPRECATION")
class BankPaymentProgressActivity : BaseActivity<CommonViewModel>(CommonViewModel::class){
    private var TAG = BankPaymentProgressActivity::class.simpleName
    private lateinit var mBinding: ActivityBankpaymentProgressBinding

    private var intentExtrasModel: IntentExtrasModel? = null
    var typePay: String? = null
    var dialog: AlertDialog? = null
    var transAPI: ITransAPI? = null
    var paymentModel: ModePaymentModel? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_bankpayment_progress)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        transAPI = TransAPIFactory.createTransAPI()
        setupToolbar()

        paymentModel = intentExtrasModel!!.modePaymentModel
      //  startTimer()
        if(paymentModel!=null && !paymentModel!!.sub_payments.isNullOrEmpty()){
            val supPayments = paymentModel!!.sub_payments!!
            if(supPayments[0].card_type == 1) {
                ebeBankFlow()
            } else if(supPayments[0].card_type == 2){
                madaBankFlow()
            }
        }
        else {
            ebeBankFlow()
        }
    }

    private fun ebeBankFlow(){
        when (intentExtrasModel!!.bankRequestType) {
            VOID_REQUEST -> {
                voidEbeTransaction()
            }
            REFUND_REQUEST -> {
                sendEbeRefundRequest()
            }
            else -> {
                sendEbeRequestPaymentApp()
            }
        }
    }

    private fun getFinalAmount() : String {
        var amount = "0"
        if (intentExtrasModel!!.amount != null) {
            amount = intentExtrasModel!!.amount!!.toDouble().roundToInt().toString()
            log(TAG, "Round Of Amount :$amount")
        }
        else { // full tank selection
            if(intentExtrasModel!!.stationMode!! == AppConstant.AFTER_TRX_MODE && intentExtrasModel!!.mTransaction!=null) {
                intentExtrasModel!!.amount =  intentExtrasModel!!.mTransaction!!.amount!!.toString()
                amount = intentExtrasModel!!.amount!!
            }
            else {
                intentExtrasModel!!.amount =  prefs.getReferenceModel()!!.terminal!!.maxRefillAmount!!
                amount = intentExtrasModel!!.amount!!
            }

            if(fuelVat.enabled || shopVat.enabled){
                val taxModel = if(fuelVat.enabled) {
                    val isInclusive = fuelVat.type == 0
                    TaxUtils.calculate(intentExtrasModel!!.amount!!.toDouble(),fuelVat.percentage!!.toDouble(),isInclusive)
                } else /*if(shopVat.enabled)*/ {
                    val isInclusive = shopVat.type == 0
                    TaxUtils.calculate(intentExtrasModel!!.amount!!.toDouble(),shopVat.percentage!!.toDouble(),isInclusive)
                }
                intentExtrasModel!!.amount =  taxModel.totalAmount.toString() // calculated total amount by adding tax
                amount = intentExtrasModel!!.amount!!  //
            }
            //roundOffAmount = "200"
        }
        //val finalAmount = roundOffAmount.toLong() * 100  // commented because getting exception here in after transaction mode (NumberFormatException) try 10 amount to check exception
        return amount
    }

    //region Mada Bank Flow
    private val PAYMENT_REQUEST_CODE = 100 // Request Code to start mada app for re- sult
    private fun madaBankFlow(){
        when (intentExtrasModel!!.bankRequestType) {
            VOID_REQUEST -> {
                sendMadaVoidRequest()
            }
            REFUND_REQUEST -> {
                sendMadaRefundRequest()
            }
            else -> {
                sendMadaPurchaseRequest()
            }
        }
    }
    private fun sendMadaVoidRequest(){}
    private fun sendMadaRefundRequest(){}
    private fun sendMadaPurchaseRequest(){
        try {
            val finalAmount = getFinalAmount()      //// you can pass amount here
            if (appInstalledOrNot(AppConstant.MADA_APP_PACKAGE)) {
                val paymentIntenet = Intent()
                paymentIntenet.action = AppConstant.MADA_PURCHASE
                paymentIntenet.putExtra(Intent.EXTRA_TEXT, finalAmount.toString()) // Need to pass amount as String (Amount currency is SAR)
                //paymentIntenet.putExtra("ORDER_ID",intentExtrasModel!!.mTransaction!!.reference) // order id Optional Param
                //paymentIntenet.putExtra("CUSTOMER_RECEIPT_FLAG",true) // this is optional parameter , by default value is true . true - to enable customer receipt print, false - to disable customer receipt print
                paymentIntenet.putExtra("HOME_BUTTON_STATUS", true) // its optional intent parameter, send true to enable home button, send false to disable home button, If you are not pass anything its take default value as true and home button will be enabled.
                paymentIntenet.type = "text/plain"
                val shareIntent = Intent.createChooser(paymentIntenet, null)
                startActivityForResult(shareIntent, PAYMENT_REQUEST_CODE)
            } else {// implement your code if mada app is not installed to handle transaction
                showDialog(getString(R.string.mada_app_not_found), getString(R.string.please_install_mada_app_first))
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    private fun processMadaResponse(requestCode: Int, resultCode: Int, data: Intent?){
        when (requestCode) {
            PAYMENT_REQUEST_CODE -> {
                if (resultCode == Activity.RESULT_OK) {
                    val status = data?.getStringExtra("status")
                    if (status.equals("Approved") || status.equals("Declined")) {
                        val result = data?.getStringExtra("result")
                        log(TAG,"MADA Response: $result")
                        try {
                            if (result != null) {
                                val jsonObject = JSONObject(result)

                                val rrn = jsonObject.optString("rrn")
                                val authCode = jsonObject.optString("auth_code")
                                val tc = jsonObject.optString("tc")
                                val responseMessage = jsonObject.optString("res_desc")

                                if(status.equals("Approved")){
                                    prefs.saveStringSharedPreferences(AppConstant.AUTH_CODE, authCode)
                                    prefs.saveStringSharedPreferences(AppConstant.REFERENCE_NO, rrn)
                                    prefs.saveStringSharedPreferences(AppConstant.VOUCHER_NO, tc)
                                    when (intentExtrasModel!!.bankRequestType) {
                                        VOID_REQUEST -> {
                                            log(TAG, "Void request not implemented")
                                        }
                                        REFUND_REQUEST -> {
                                            log(TAG, "Refund request not implemented")
                                        }
                                        else -> {
                                            madaTransactionDone(jsonObject)
                                        }
                                    }
                                } else if(status.equals("Declined")){
                                    gotoAbortMessageActivity(getString(R.string.transaction_cancelled),responseMessage)
                                }
                            }
                        } catch (e: Exception) {
                            status?.let {
                                // Handle exception
                                e.printStackTrace()
                                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),e.message)
                            }
                        }
                    }
                    else {
                        val reason = data?.getStringExtra("reason") // reason for payment abort
                        gotoAbortMessageActivity(getString(R.string.transaction_cancelled),reason)
                    }
                }
            }
        }

    }
    private fun madaTransactionDone(jsonObject: JSONObject) {
        val rrn = jsonObject.optString("rrn")
        val orderId = jsonObject.optString("ORDER_ID")
        val cardScheme = jsonObject.optString("card_scheme")
        val pan = jsonObject.optString("pan")
        val card_expiry_date = jsonObject.optString("card_expiry_date")
        val stan = jsonObject.optString("stan")
        val entryMode = jsonObject.optString("entry_mode")
        val appVersion = jsonObject.optString("app_version")
        val respCode = jsonObject.optString("resp_code")
        val acqID = jsonObject.optString("acq_id")
        val terminalID = jsonObject.optString("terminal_id")
        val amount = jsonObject.optString("amount")
        val formatted_amount = jsonObject.optString("formatted_amount")
        val authCode = jsonObject.optString("auth_code")
        val tc = jsonObject.optString("tc")
        val responseMessage = jsonObject.optString("res_desc")
        val ORDER_ID = jsonObject.optString("ORDER_ID")

        typePay = AppConstant.VISA_VALUE
        intentExtrasModel!!.panNumber = pan

        val mIntent = if(intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL && intentExtrasModel!!.stationMode!! == AppConstant.BEFORE_TRX_MODE) {
            //intentExtrasModel!!.preAuthAmount = Support.getDecimalValue(response.amount,2).toString()
            if(intentExtrasModel!!.mTransaction != null)
            {
                intentExtrasModel!!.mTransaction!!.preAuthAmount = formatted_amount
                intentExtrasModel!!.mTransaction!!.amount = formatted_amount.toDouble()   //added to set new amount if user entered amount from bank app in case of 0 preset amount : for mode details : https://app.clickup.com/t/8676zc29n
                intentExtrasModel!!.mTransaction!!.pan = pan
            }
            intentExtrasModel!!.preAuthAmount = formatted_amount
            Intent(this, PumpSelectionActivity::class.java)
        }
        else {
            if(intentExtrasModel!!.mTransaction != null)
            {
                intentExtrasModel!!.mTransaction!!.pan = pan
                intentExtrasModel!!.mTransaction!!.transactionStatus =  1
                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            }
            Intent(this, TicketActivity::class.java)
        }
        intentExtrasModel!!.mTransaction!!.bank_reference_num = rrn
        intentExtrasModel!!.mTransaction!!.dateExp = card_expiry_date

        //val i = Intent(this, PumpSelectionActivity::class.java)
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(mIntent)
        //  finish()
    }
    //endregion

    private fun setupToolbar() {
        mBinding.toolbarPayment.toolbar.tvTitle.text = getString(R.string.payment_verification)
        mBinding.toolbarPayment.toolbar.setNavigationOnClickListener {
            mBinding.toolbarPayment.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.customer_cancel_transaction_))
        }
    }

    private fun showErrorMessage(title:String,msg: String?) {
        val dialog = Dialog(this)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setCancelable(false)
        dialog.setContentView(R.layout.dialog_failed_message)
        dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        val tvTitle = dialog.findViewById<TextView>(R.id.title)
        val tvMessage = dialog.findViewById<TextView>(R.id.message)
        val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

        tvTitle.text = title
        tvMessage.text = msg

        val timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                //here you can have your logic to set text to edittext
                println("Popup Time Remaining: $minutes:$seconds")
            }

            override fun onFinish() {
                val unAttendantMode = prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    val mIntent = Intent(this@BankPaymentProgressActivity,MenuActivity::class.java)
                    startActivity(mIntent)
               //     finish()
                }
            }
        }
        if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()

        dialogButton.setOnClickListener {
            setBeep()
            timer.cancel()
            dialog.dismiss()
//            if(intentExtrasModel!!.bankRequestType == VOID_REQUEST)
//            {
//                voidTransaction()
//            }
//            if(intentExtrasModel!!.bankRequestType == REFUND_REQUEST)
//            {
//                sendRefundRequest()
//
//            }
//            else
//            {
                val mIntent = Intent(this,MenuActivity::class.java)
                startActivity(mIntent)
              //  finish()
//            }

        }
        dialog.show()



    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if(paymentModel != null && !paymentModel!!.sub_payments.isNullOrEmpty()){
            val supPayments = paymentModel!!.sub_payments!!
            if(supPayments[0].card_type == 1) {
                processEbeResponse(requestCode,resultCode,data)
            } else if(supPayments[0].card_type == 2){
                processMadaResponse(requestCode,resultCode,data)
            }
        }
        else {
            processEbeResponse(requestCode,resultCode,data)
        }
    }

    override fun setObserver() {}

    private fun sendEbeRequestPaymentApp() {
        try {
            val amount =  getFinalAmount()
            val finalAmount = (amount.toDouble() * 100).toLong()
            saleRequest(finalAmount)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
    private fun saleRequest(finalAmount: Long) {
        val request: SaleMsg.Request = SaleMsg.Request()
        request.amount = finalAmount
        request.ecrRefNo = ""+prefs.getReferenceModel()!!.terminal!!.terminalId+getCurrentTimestamp()
        prefs.saveStringSharedPreferences(AppConstant.ECR_NO, java.lang.String.valueOf(request.ecrRefNo))
        log(TAG, "finalAmount :$finalAmount")
        request.category = SdkConstants.CATEGORY_SALE
        log(TAG, "SALE REQUEST  :$request")
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "Sale Request - $request"))
        transAPI!!.startTrans(this, request)
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.preAuthAmount =  intentExtrasModel!!.amount
            intentExtrasModel!!.mTransaction!!.bank_reference_num =  request.ecrRefNo
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
        }
        prefs.isPaymentDone = true
        log(TAG, "isPaymentDone ${prefs.isPaymentDone}")
    }
    private fun preAuthRequest(finalAmount: Long) {
        val request: PreAuthMsg.Request = PreAuthMsg.Request()
        request.amount = finalAmount
        request.category = SdkConstants.CATEGORY_PREAUTH
        transAPI!!.startTrans(this, request)
    }
    private fun ebeBankTransactionDone(response: TransResponse) {
        typePay = AppConstant.VISA_VALUE
        intentExtrasModel!!.panNumber = response.cardNo

        val mIntent = if(intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL && intentExtrasModel!!.stationMode!! == AppConstant.BEFORE_TRX_MODE) {
            //intentExtrasModel!!.preAuthAmount = Support.getDecimalValue(response.amount,2).toString()
                val preAuthAmount=((response.amount).toDouble()/100).toString()
            if(intentExtrasModel!!.mTransaction != null)
            {
                intentExtrasModel!!.mTransaction!!.preAuthAmount = preAuthAmount
                intentExtrasModel!!.mTransaction!!.amount = preAuthAmount.toDouble()   //added to set new amount if user entered amount from bank app in case of 0 preset amount : for mode details : https://app.clickup.com/t/8676zc29n
                intentExtrasModel!!.mTransaction!!.pan = response.cardNo
            }
            intentExtrasModel!!.preAuthAmount = preAuthAmount
            Intent(this, PumpSelectionActivity::class.java)
        }
        else {
            if(intentExtrasModel!!.mTransaction != null)
            {
                intentExtrasModel!!.mTransaction!!.pan = response.cardNo
                intentExtrasModel!!.mTransaction!!.transactionStatus =  1
                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
            }
            Intent(this, TicketActivity::class.java)
        }
        //val i = Intent(this, PumpSelectionActivity::class.java)
        mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(mIntent)
      //  finish()
    }
    private fun voidEbeTransaction() {
        val request = VoidMsg.Request()
        log(TAG,"VOID ECR NO:: "+ prefs.getStringSharedPreferences(AppConstant.ECR_NO))
        request.ecrRef = prefs.getStringSharedPreferences(AppConstant.ECR_NO)
        request.category = SdkConstants.CATEGORY_VOID
        log(TAG, "VOID REQUEST  :$request")
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "Void Request - $request"))
        transAPI!!.startTrans(this, request)
        if(intentExtrasModel!!.mTransaction != null)
        {
            intentExtrasModel!!.mTransaction!!.bank_reference_num =  request.ecrRef
            intentExtrasModel!!.mTransaction!!.transactionStatus =  0
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
        }
    }
    private fun sendEbeRefundRequest() {
        val request = OfflineRefundMsg.Request()
        request.amount = intentExtrasModel!!.refundAmount!!.toLong()
        log(TAG,"refundAmount:: "+   request.amount)
        request.ecrRefNo = ""+prefs.getReferenceModel()!!.terminal!!.terminalId+getCurrentTimestamp()
        if(intentExtrasModel!!.mTransaction != null && !intentExtrasModel!!.mTransaction!!.bank_reference_num.isNullOrEmpty())
        {
            request.saleEcrRefNo = intentExtrasModel!!.mTransaction!!.bank_reference_num
        }
        else
        {
          request.saleEcrRefNo = prefs.getStringSharedPreferences(AppConstant.ECR_NO)
        }
        request.category = SdkConstants.CATEGORY_OFFLINE_REFUND
        log(TAG, "REFUND REQUEST  :$request")
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "Refund Request - $request"))
        transAPI!!.startTrans(this, request)
        if(intentExtrasModel!!.mTransaction != null)
        {
            log(TAG,"refundAmount:: "+   intentExtrasModel!!.refundAmount)
            intentExtrasModel!!.mTransaction!!.refundAmount =  (intentExtrasModel!!.refundAmount!!.toDouble() / 100).toString()
            log(TAG," intentExtrasModel!!.mTransaction!!.refundAmount:: "+    intentExtrasModel!!.mTransaction!!.refundAmount)
            intentExtrasModel!!.mTransaction!!.bank_reference_num =  request.ecrRefNo
            updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
        }
    }
    private fun processEbeResponse(requestCode: Int, resultCode: Int, data: Intent?){
        try {
            if(data != null)
            {
                val baseResponse: BaseResponse = transAPI!!.onResult(requestCode, resultCode, data)
                log(TAG, "EDC baseResponse:: $baseResponse")
                if (requestCode == 1) {
                    if (resultCode == RESULT_OK) {
                        intentExtrasModel =
                            intent.getSerializableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
                    }
                }
//        val baseResponse: BaseResponse = transAPI!!.onResult(requestCode, resultCode, data)
//            ?: return
                val isTransResponse = baseResponse is TransResponse
                log(TAG, "EDC baseResponse:: $baseResponse")
                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "EDC Response - $baseResponse"))
                if (isTransResponse && (baseResponse as TransResponse).authCode != null) {
                    val response: TransResponse = baseResponse
                    log(TAG, "EDC Response:: $response")
                    prefs.saveStringSharedPreferences(AppConstant.AUTH_CODE, response.authCode)
                    prefs.saveStringSharedPreferences(AppConstant.REFERENCE_NO, response.refNo)
                    prefs.saveStringSharedPreferences(
                        AppConstant.VOUCHER_NO,
                        java.lang.String.valueOf(response.voucherNo)
                    )
//            prefs.saveStringSharedPreferences( AppConstant.ECR_NO, java.lang.String.valueOf(response.ecrRef))
//            prefs.saveStringSharedPreferences( AppConstant.SALE_ECR_NO, java.lang.String.valueOf(response.ecrRef))
                    when (intentExtrasModel!!.bankRequestType) {
                        VOID_REQUEST -> {
                            //intentExtrasModel!!.mTransaction!!.transactionRefundExported = 1
                            gotoMenuActivity()
                            //gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.customer_cancel_transaction_))
                            //  showErrorDialog(getString(R.string.transaction_cancelled),getString(R.string.customer_cancel_transaction_))
                        }
                        REFUND_REQUEST -> {
                            intentExtrasModel!!.isBankRefundSentToApp = 1
                            if(intentExtrasModel!!.mTransaction != null)
                            {
                                intentExtrasModel!!.mTransaction!!.transactionStatus =  1
                                intentExtrasModel!!.mTransaction!!.refundStatus =  "1"
                                if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS_DISPUTED_TRANSACTION)
                                {
                                    intentExtrasModel!!.mTransaction!!.isDisputedTrx =  0
                                    intentExtrasModel!!.mTransaction!!.quantite =  intentExtrasModel!!.volume!!.toDouble()
                                    intentExtrasModel!!.mTransaction!!.amount =  (intentExtrasModel!!.mTransaction!!.preAuthAmount!!.toDouble() - intentExtrasModel!!.mTransaction!!.refundAmount!!.toDouble())
                                }
                                updateTransactionByReferenceId(intentExtrasModel!!.mTransaction!!)
                            }
                            gotoTicketActivity()
                        }
                        else -> {
                            ebeBankTransactionDone(response)
                        }
                    }

                } else {
                    Handler(Looper.getMainLooper()).postDelayed(Runnable {
                        showErrorMessage(
                            getString(R.string.transaction_failed),
                            baseResponse.rspMsg
                        )
                    }, 2000)
                }
            }
            else
            {
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancelled))
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            when (intentExtrasModel!!.bankRequestType) {
                VOID_REQUEST -> {
                    gotoMenuActivity()
                }
            }
        }
    }

    private fun gotoTicketActivity() {
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "Go to Ticket screen from bank page"))

        val intent = Intent(this, TicketActivity::class.java)
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
        startActivity(intent)
        finish()
    }

    override fun onBackPressed() {}

    lateinit var errorDialog : Dialog
    private fun showErrorDialog(title:String,msg: String?) {
        try{
            if(!(this as Activity).isFinishing) {
                errorDialog = Dialog(this)
                errorDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                errorDialog.setCancelable(false)
                errorDialog.setContentView(R.layout.dialog_failed_message)
                errorDialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
                val tvTitle = errorDialog.findViewById<TextView>(R.id.title)
                val tvMessage = errorDialog.findViewById<TextView>(R.id.message)
                val dialogButton = errorDialog.findViewById<TextView>(R.id.action_done)

                tvTitle.text = title
                tvMessage.text = msg

                dialogButton.setOnClickListener {
                    setBeep()
                    errorDialog.dismiss()
                    gotoMenuActivity()
                }
                errorDialog.show()
                startCancelTimer()
            }
        } catch (e:Exception){
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
           // mViewModel.generateLogs(e.message!!,0)
            e.printStackTrace()
        }
    }

    var countDownTimer: CountDownTimer? = null
    private fun startCancelTimer() {
        try {
            val startTime = TimeUnit.SECONDS.toMillis(10)
            countDownTimer = object : CountDownTimer(startTime, 1000) {
                override fun onTick(millisUntilFinished: Long) {
                    minutes = millisUntilFinished / 1000 / 60
                    seconds = (millisUntilFinished / 1000 % 60)
                    log(TAG,"Remaining: $minutes:$seconds")
                }
                override fun onFinish() {
                   if(::errorDialog.isInitialized && errorDialog.isShowing){
                       gotoMenuActivity()
                   }
                }
            }.start()
        }
        catch (e:Exception) {
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            //mViewModel.generateLogs(e.message!!,0)
            e.printStackTrace()
        }
    }
    private fun gotoMenuActivity(){
        val mIntent : Intent = if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE) {
            Intent(this,UnattendantModePayActivity::class.java)
        } else {
            Intent(this,MenuActivity::class.java)
        }
        startActivity(mIntent)
     //   finish()
    }

    override fun onPause() {
        super.onPause()
        
    }


}
