package app.rht.petrolcard.ui.ticket.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.iccpayment.model.ActivationDetails
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper

class LoyaltyDialogViewModel(private val mNetworkService: ApiService, private val preferencesHelper: AppPreferencesHelper): CommonViewModel(mNetworkService,preferencesHelper)  {

    var activationDetailsObservable = MutableLiveData<BaseResponse<ActivationDetails>>()

    fun getCardActivationDetails(pan:String) {
        val serverUrl: String = preferencesHelper.baseUrl.replace("-tpe".toRegex(), "")+NetworkRequestEndPoints.ACTIVATION_DETAILS
        requestData(mNetworkService.activationDetails(serverUrl, MainApp.sn!!,pan),
            {
                activationDetailsObservable.postValue(it)
            })
    }

}
