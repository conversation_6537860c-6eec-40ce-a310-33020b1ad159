package app.rht.petrolcard.ui.attendantcode.activity

import android.app.Activity
import android.app.AlertDialog
import android.content.Intent
import android.os.Bundle
import android.os.RemoteException
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.UsersDao
import app.rht.petrolcard.databinding.ActivityAttenadatntTagBinding
import app.rht.petrolcard.ui.amountselection.activity.AmountFullTankActivity
import app.rht.petrolcard.ui.amountselection.activity.EnterAmountActivity
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity

import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.transactionlist.activity.OfflineTransactionListActivity
import app.rht.petrolcard.ui.transactionlist.activity.FuelposTransactionListActivity
import app.rht.petrolcard.ui.transactionlist.activity.FusionTransactionListActivity
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.MyMaterialDialog
import app.rht.petrolcard.utils.MyMaterialDialogListener
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.helpers.MultiClickPreventer
import app.rht.petrolcard.utils.paxutils.modules.picc.PiccTester
import com.afollestad.materialdialogs.MaterialDialog
import com.pax.dal.entity.EPiccType
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.database.SQLiteException
import wangpos.sdk4.libbasebinder.BankCard
import wangpos.sdk4.libbasebinder.HEX
import java.lang.ref.WeakReference

@Suppress("DEPRECATION")
class AttendantTagActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {

    private lateinit var mBinding: ActivityAttenadatntTagBinding
    private var mBankCard: BankCard? = null
    var tagNFC: String? = null
    private var tagUID: String? = null
    private var piccType: EPiccType? = null
    private var alert: AlertDialog? = null
    var stationMode = 0
    private var intentExtrasModel: IntentExtrasModel? = null
    private var TAG = AttendantTagActivity::class.simpleName
    lateinit var readCardTask :  ReadCardAsyncTask

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_attenadatnt_tag)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?

        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!
            if(intentExtrasModel!!.loyaltyTrx)
            {
                stationMode = 1
            }
        }
        setupToolbar()
        if(prefs.getReferenceModel()!!.station!!.mode_pompiste == "EITHER"){
            mBinding.btnCodeVerification.visibility = View.VISIBLE
        }
        else {
                mBinding.btnCodeVerification.visibility = View.GONE
        }
        mBinding.btnCodeVerification.setOnClickListener {
            MultiClickPreventer.preventMultiClick(it)
            val mIntent = Intent(this, AttendantCodeActivity::class.java)
            mIntent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
            startActivity(mIntent)
            finish()
        }
        readNfcScan()
    }
    fun readNfcScan() {
        cancelPreviousNfcScanIfRunning()             //added this condition to resolve ANR issue
        readCardTask = ReadCardAsyncTask()
        readCardTask.execute()
    }
    private fun cancelPreviousNfcScanIfRunning(){
        try {
            if(::readCardTask.isInitialized) {
                readCardTask.cancel(true)
            }
        } catch (e:Exception){e.printStackTrace()}
    }
    override fun onStop() {
        cancelPreviousNfcScanIfRunning()
        super.onStop()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarBadge.toolbar.tvTitle.text = getString(R.string.nfc_verification)
        mBinding.toolbarBadge.toolbar.setNavigationOnClickListener {
            try {
                mBinding.toolbarBadge.toolbar.isEnabled = false
            if (BuildConfig.POS_TYPE == "B_TPE") {
                if (mBankCard != null && mCore != null) {
                    mBankCard!!.breakOffCommand()
                    val ret = mBankCard!!.openCloseCardReader(BankCard.CARD_MODE_PICC, 0x02)
                }
            }
                gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
        } catch (e: RemoteException) {
            e.printStackTrace()
        }}
    }


    inner class ReadCardAsyncTask: CoroutineAsyncTask<String, String, Int>() {
        var resultat = 0
        lateinit var respdata: ByteArray
        lateinit var resplen: IntArray
        var retvalue = 0
        lateinit var sn: ByteArray
        lateinit var pes: IntArray
        var resSN = 0

        override fun doInBackground(vararg params: String): Int {
            resultat = 0
            respdata = ByteArray(28)
            resplen = IntArray(1)
            retvalue = -1
            sn = ByteArray(16)
            pes = IntArray(1)
            resSN = 0
            try {
                if (BuildConfig.POS_TYPE == "B_TPE") {
                    val ctx = WeakReference(this@AttendantTagActivity).get()
                    mBankCard = BankCard(ctx)
                    tagNFC = "NO BADGE"
                    if (mBankCard != null) retvalue = mBankCard!!.readCard(
                        BankCard.CARD_TYPE_NORMAL,
                        BankCard.CARD_MODE_PICC,
                        60,
                        respdata,
                        resplen,
                        AppConstant.TPE_APP
                    )

                    if (mBankCard != null) resSN = mBankCard!!.getCardSNFunction(sn, pes)
                    tagUID = HEX.bytesToHex(sn)
                    if (tagUID != null) {
                        log("tagUID =>", "tagUID--->>>$tagUID")
                    } else {
                        log("tagUID =>", "tagUID--->>>" + null)
                        return 0
                    }
                } else if (BuildConfig.POS_TYPE == "PAX") {
                    var i = 0
                    piccType = EPiccType.INTERNAL
                    PiccTester.getInstance(piccType!!).open()
                    var tag: String = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    while (tag.equals("", ignoreCase = true) && i < 20) {
                        i++
                        Thread.sleep(500)
                        tag = PiccTester.getInstance(piccType!!).detectPaxTAG()
                    }
                    if (tag.isNotEmpty()) tagUID = tag
                }
            } catch (e: RemoteException) {
                e.printStackTrace()
                return 0
            } catch (e: InterruptedException) {
                e.printStackTrace()
                return 0
            }
            var nfcLength = 0
            var minLength = 5
            var maxLength = 14

            if(BuildConfig.POS_TYPE == "PAX" && tagUID != null)
            {
                nfcLength = tagUID!!.length
            }
            else if(BuildConfig.POS_TYPE == "B_TPE")
            {
                nfcLength =  pes[0]
            }
            if(prefs.getReferenceModel()!!.station!!.nfc_read_min_length != null) {
                minLength = prefs.getReferenceModel()!!.station!!.nfc_read_min_length!!
                maxLength = prefs.getReferenceModel()!!.station!!.nfc_read_max_length!!
            }
            if (nfcLength in minLength..maxLength) {
                setBeep()
                tagNFC = if (tagUID!!.length >= 14) {
                    tagUID!!.substring(0, 14)
                } else {
                    tagUID
                }
                log("tagNFC =>", "tagNFC--->>>$tagNFC")
              if(tagNFC.isNullOrEmpty())
              {
                  resultat = 0
              }
                else
              {
                  resultat =1
              }
            } else {
                resultat = 0
            }
            return resultat
        }
        override fun onPreExecute() {

        }
        override fun onPostExecute(message: Int?) {

             if(message == 0)
            {
                showRetryDialog(resources.getString(R.string.rfid_errone))
            }
            else if (message == 1) {
                 try {
                     val mUsersDao = UsersDao()
                     mUsersDao.open()
                     //val u = mUsersDao.getAttendantListByCode(tagNFC!!)
                     val attendantModel = mUsersDao.getAttendantListByTag(tagNFC!!)
                     mUsersDao.close()

                     if(attendantModel == null)
                     {
                         showRetryDialog(resources.getString(R.string.scan_tag_again_attendant))
                     }
                     else
                     {
                         fun setValuse()
                         {
                             intentExtrasModel!!.idPompiste = attendantModel.id.toString()
                             intentExtrasModel!!.attendantName = attendantModel.firstname
                             intentExtrasModel!!.mPinNumberAttendant = attendantModel.authenticationKey
                             intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Attendant verification tag success - "+attendantModel.id.toString()))
                             if(intentExtrasModel!!.mTransaction != null)
                             {
                                 intentExtrasModel!!.mTransaction!!.idPompiste = attendantModel.id.toString()
                                 intentExtrasModel!!.mTransaction!!.codePompiste =attendantModel.authenticationKey
                             }
                         }
                         if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE )
                         {
                             if(attendantModel.doCashTrxUnAttendedOPT != AppConstant.DO_CASHTRX_UNATTENDANT)
                             {
                                 gotoAbortMessageActivity(
                                     getString(R.string.access_denied),
                                     getString(R.string.no_permission)
                                 )
                             }
                             else
                             {
                                 setValuse()
                                 gotoUnAtttendantNextActivity()
                             }
                         }
                         else
                         {
                             setValuse()
                             gotoNextScreen()
                         }
                     }

                 } catch (E: SQLiteException) {
                     E.printStackTrace()
                 }
            }

        }
        override fun onProgressUpdate(vararg values: IntArray) {

        }
        override fun onCancelled(result: Int?) {

        }
        override fun onCancelled() {
        }
    }

    override fun onDestroy() {
        if(::readCardTask.isInitialized){
            readCardTask.cancel(true)
        }
        super.onDestroy()
    }

    private fun gotoUnAtttendantNextActivity()
    {
        if(intentExtrasModel!!.workFlowTransaction == Workflow.SETTINGS)
        {
            val intent = Intent(this, SettingsActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
        }
        else
        {
            intentExtrasModel!!.workFlowTransaction = Workflow.TAXI_FUEL
            intentExtrasModel!!.stationMode = AppConstant.BEFORE_TRX_MODE
            val intent = Intent(this, AmountFullTankActivity::class.java)
            intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(intent)
            finish()
        }

    }

    private fun showRetryDialog(message :String){
        if (!(this as Activity).isFinishing) {
            MyMaterialDialog(
                this,
                getString(R.string.error),
                "" + message,
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        setBeep()
                        dialog.dismiss()
                        readNfcScan()
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        setBeep()
                        dialog.dismiss()
                        gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
                    }
                })
        }
    }

    override fun setObserver() {

    }


    private fun gotoNextScreen()
    {
        val intent: Intent

        when (intentExtrasModel!!.workFlowTransaction) {
            Workflow.TAXI_FUEL -> {
                when (stationMode) {

                    1 -> {
                        intent = Intent(this, OfflineTransactionListActivity::class.java)
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                        startActivity(intent)
                        finish()
                    }
                    3 //after transaction mode
                    -> {
                        gotoTransactionListActivity()
                    }
                    2 //before transaction mode
                    -> {
                        intent = Intent(this, AmountFullTankActivity::class.java)
                        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                        startActivity(intent)
                        finish()
                    }
                    else -> {
                        gotoTransactionListActivity()
                    }
                }
            }
            Workflow.OTHER_PRODUCTS,Workflow.SHOP_PRODUCTS -> {
                intent = Intent(this, EnterAmountActivity::class.java)
                intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL,intentExtrasModel)
                startActivity(intent)
                finish()
            }
            Workflow.SETTINGS_UPDATE_MILEAGE ->
            {
                gotoCheckRestrictions()
            }
            else -> {
                gotoAbortMessageActivity(getString(R.string.error),getString(R.string.workflow_not_available))
            }
        }

       // finish()
    }
    fun gotoCheckRestrictions()
    {
        val i = Intent(this, CheckCardRestrictionsActivity::class.java)
        i.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(i)
        finish()
    }
    private fun gotoTransactionListActivity() {
        val intent1 = if (prefs.getReferenceModel()!!.FUSION!!.EXIST) {
            Intent(this, FusionTransactionListActivity::class.java)
        } else {
            Intent(this, FuelposTransactionListActivity::class.java)
        }
        intent1.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        startActivity(intent1)
        finish()
    }

    override fun onBackPressed() {

    }
}
