package app.rht.petrolcard.ui.iccpayment.model

import androidx.annotation.Keep

@Keep
class CardStaticStructureModel (
    val activationDate:String,
    val blockedDate:String,
    val blockedReason:String,
    val cardCeilingUnit:String,
    val cardCode:String,
    val cardNumber:String,
    val cardStatus:String,
    val cardType:String,
    val cleintId:String,
    val expiredDate:String,
    val flagBlocking:String,
    var discountType:String="0",
    val organizationCardId:String,
    val personalCardDate:String,
    val verificationType:String
)