package app.rht.petrolcard.ui.timssign.activity

import android.app.Activity
import android.app.AlertDialog
import android.app.Dialog
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import android.view.Window
import android.widget.TextView
import androidx.annotation.RequiresApi
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityTimsBinding
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.CustomerAuthentication
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.TIMS_METHODS
import app.rht.petrolcard.utils.tims.CloseReceiptRes
import app.rht.petrolcard.utils.tims.ZfpLabServerManager
import kotlinx.android.synthetic.main.toolbar.view.*
import ticker.views.com.ticker.widgets.circular.timer.callbacks.CircularViewCallback
import ticker.views.com.ticker.widgets.circular.timer.view.CircularView

class TIMSSignActivity : BaseActivity<PaymentViewModel>(PaymentViewModel::class) {

    private lateinit var mBinding: ActivityTimsBinding
    private var TAG =TIMSSignActivity::class.java.simpleName
    private var intentExtrasModel: IntentExtrasModel? = null
    private var stationMode: Int? = null
    var invoiceResponse= CloseReceiptRes()
    var invoiceProgressDialog: AlertDialog? = null
    var circularView: CircularView? = null
    var qtyDecimal = 2
    var uniteDecimal = 2
    var totalDecimal = 2
    var fiscalPrinterModel: FiscalPrinterModel? = null

    @RequiresApi(Build.VERSION_CODES.M)
    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme()
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_tims)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        getIntentExtraModel()
        //showEnterPinDialog()
        val referenceModel = prefs.getReferenceModel()
        fiscalPrinterModel = referenceModel!!.fiscal_printer
        if(BuildConfig.DEBUG)
        {
            mBinding.customerPin.setText("P051140749R")
        }
        if(prefs.getReferenceModel()!!.fiscal_printer!!.customer_pin_authentication == CustomerAuthentication.REQUIRED)
        {
            mBinding.cancelButton.visibility = View.GONE
        }
        else
        {
            mBinding.cancelButton.visibility = View.VISIBLE
        }
        OnClickListners()
        setupToolbar()
    }
    private fun setupToolbar() {
        mBinding.toolbarTims.toolbar.tvTitle.text = getString(R.string.enter_customer_details)
        mBinding.toolbarTims.toolbar.setNavigationOnClickListener {
            mBinding.toolbarTims.toolbar.isEnabled = false
        }
    }
    fun OnClickListners() {
        mBinding.cancelButton.setOnClickListener {
            intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.customerPin= null
            pinVerificationSuccess(false)
        }
        mBinding.submitButton.setOnClickListener { v: View? ->
            when {
                mBinding.customerPin.text.toString().isEmpty() -> {
                    mBinding.customerPin.error = getString(R.string.enter_customer_pin)
                }
                else -> {
                    showProgressRight( mBinding.submitButton)
                    intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.customerPin= mBinding.customerPin.text.toString()
                    if (mBinding.extemptionNumber.text.toString().isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.exemptionNumber = mBinding.extemptionNumber.text.toString()
                    }
                    if (mBinding.companyName.text.toString().isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.companyName = mBinding.companyName.text.toString()
                    }
                    if (mBinding.headQuarters.text.toString().isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.headQuarters = mBinding.headQuarters.text.toString()
                    }
                    if (mBinding.address.text.toString().isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.address = mBinding.address.text.toString()
                    }
                    if (mBinding.postalCode.text.toString().isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.postalCode = mBinding.postalCode.text.toString()
                    }
                    if (mBinding.city.text.toString().isNotEmpty()) {
                        intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.city = mBinding.city.text.toString()
                    }
                    log(TAG, "Verify Pin:: " + intentExtrasModel!!.mTransaction!!.timsSignDetails!!.customer_details!!.customerPin)
                    pinVerificationSuccess()
                }

            }
        }
    }
    private fun getIntentExtraModel(){
        intentExtrasModel  = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            stationMode = intentExtrasModel!!.stationMode!!

            if (intentExtrasModel!!.stationMode != null) {
                stationMode = intentExtrasModel!!.stationMode!!
                if(intentExtrasModel!!.loyaltyTrx)
                {
                    stationMode = 1
                }
            }
        }
        qtyDecimal =  2
        uniteDecimal =  2
        totalDecimal =  2

    }

 /*   fun showEnterPinDialog()
    {
        val builder = AlertDialog.Builder(this)
        builder.setTitle("")
        builder.setCancelable(false)
        val layout: View = layoutInflater.inflate(R.layout.dialog_enter_customer_pin, null)
        builder.setView(layout)
        val customerPin: AppCompatEditText = layout.findViewById(R.id.customerPin)
        val extemptionNumber: AppCompatEditText = layout.findViewById(R.id.extemptionNumber)
        submitButton = layout.findViewById(R.id.submitButton)
        val cancelButton: AppCompatButton = layout.findViewById(R.id.cancelButton)
        val msgTxt: AppCompatTextView = layout.findViewById(R.id.msgTxt)
        errorTxt = layout.findViewById(R.id.errorMessage)
        if(BuildConfig.DEBUG)
        {
            customerPin.setText("P051140749R")
        }
        cancelButton.setOnClickListener { v: View? ->
            pinDialog!!.dismiss()
            pinVerificationSuccess(false)
        }
        if(prefs.getReferenceModel()!!.fiscal_printer!!.customer_pin_authentication == CustomerAuthentication.REQUIRED)
        {
            cancelButton.visibility = View.GONE
        }
        else
        {
            cancelButton.visibility = View.VISIBLE
        }
        submitButton!!.setOnClickListener { v: View? ->
            when {
                customerPin.text.toString().isEmpty() -> {
                    customerPin.error = getString(R.string.enter_customer_pin)
                }
                else ->
                {
                    showProgressRight(submitButton!!)
                    intentExtrasModel!!.mTransaction!!.customerPin =  customerPin.text.toString()
                    if(!extemptionNumber.text.toString().isNullOrEmpty())
                    {
                        intentExtrasModel!!.mTransaction!!.exemptionNumber = extemptionNumber.text.toString()
                    }
                    log(TAG,"Verify Pin:: "+ intentExtrasModel!!.mTransaction!!.customerPin)
                   pinVerificationSuccess()
                }

            }
        }
        pinDialog = builder.create()
        pinDialog!!.show()
    }*/
    fun pinVerificationSuccess(isCreditNoteRequire:Boolean=true)
    {
        hideProgressRight( mBinding.submitButton)
        val transactionDao = TransactionDao()
        val isAvailable = transactionDao.checkTransactionSigned(intentExtrasModel!!.mTransaction!!.reference!!)
        transactionDao.close()
        var roundingAdjustment = "0"
        if(intentExtrasModel!!.categoryId == PRODUCT.FUEL_CATEGORY_ID)
        {
            val roundingAmount = intentExtrasModel!!.mTransaction!!.quantite!! * intentExtrasModel!!.mTransaction!!.unitPrice!!
            roundingAdjustment = (intentExtrasModel!!.mTransaction!!.amount!!.toDouble() - roundingAmount).toString()
            intentExtrasModel!!.mTransaction!!.roundingAdjustment = Support.formatString(roundingAdjustment.toDouble())
        }
        else
        {
            intentExtrasModel!!.mTransaction!!.quantite = 1.0
            intentExtrasModel!!.mTransaction!!.unitPrice = intentExtrasModel!!.mTransaction!!.amount
        }
        showProgressBar()
        if(isAvailable)
        {
            gotoTicketActivity()
        }
        else
        {
           // GENERATE INVOICE
            generateFPInvoice(intentExtrasModel!!.mTransaction!!)
        }

    }
    private fun generateFPInvoice(mTransactionModel: TransactionModel?) {
        Handler(Looper.getMainLooper()).post {
                val timsGenerate = TIMSInvoiceGenerate(this)
            if(prefs.isTimsStarted)
            {
                timsGenerate.readPrinterStatus(sfiscalPrinterModel = fiscalPrinterModel!!,smTransaction=mTransactionModel)
            }
            else
            {
                timsGenerate.startTIMSServer(sfiscalPrinterModel = fiscalPrinterModel!!,smTransaction=mTransactionModel)
            }
                timsGenerate.invoiceReceiptListner = object : TIMSInvoiceGenerate.InvoiceReceiptListner {
                        override fun onSuccess(model: CloseReceiptRes?, mTransaction: TransactionModel?) {
                            runOnUiThread {
                                circularView!!.stopTimer()
                                hideProgressRight( mBinding.submitButton)
                                invoiceProgressDialog!!.dismiss()
                                invoiceResponse = model!!
                                gotoTicketActivity()
                            }
                        }

                        override fun onFailed(message: String,method:String) {
                            runOnUiThread {
                                circularView!!.stopTimer()
                                prefs.isTimsStarted = false
                                showErrorDialog(getString(R.string.invalid_details), message,method)
                                invoiceProgressDialog!!.dismiss()
                            }
                        }

                    }

        }
    }

    override fun setObserver() {

    }
    fun showErrorDialog(title:String,msg: String?,method:String?="") {
        if(!(this as Activity).isFinishing)
        {
            val dialog = Dialog(this)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialog.setCancelable(false)
            dialog.setContentView(R.layout.dialog_fail_retry)
            dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
            val tvTitle = dialog.findViewById<TextView>(R.id.title)
            val tvMessage = dialog.findViewById<TextView>(R.id.message)
            val dialogButton = dialog.findViewById<TextView>(R.id.action_done)
            val btnRetry = dialog.findViewById<TextView>(R.id.btnRetry)
            tvTitle.text = title
            tvMessage.text = msg

            dialogButton.setOnClickListener {
                dialog.dismiss()
                setBeep()
                pinVerificationSuccess()
            }
            btnRetry.setOnClickListener {
                if(circularView != null)
                {
                    circularView!!.stopTimer()
                }
                dialog.dismiss()
                setBeep()
                if(method != TIMS_METHODS.openReceiptWithCustomerData)
                {
                pinVerificationSuccess()
                }

            }
            dialog.show()
        }

    }
    private fun gotoTicketActivity() {
        val intent = Intent()
        intent.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
        setResult(RESULT_OK, intent)
        finish()
    }
    fun showProgressBar() {

        val builder = AlertDialog.Builder(this)
        builder.setTitle("")
        builder.setCancelable(false)
        val customLayout: View = layoutInflater.inflate(R.layout.dialog_progress, null)
        builder.setView(customLayout)
        circularView = customLayout.findViewById<CircularView>(R.id.circular_view)
        customLayout.findViewById<TextView>(R.id.dialogMessage)
        circularView!!.startTimer()
        val builderWithTimer: CircularView.OptionsBuilder = CircularView.OptionsBuilder()
            .shouldDisplayText(true)
            .setCounterInSeconds(60)
            .setCircularViewCallback(object : CircularViewCallback {
                override fun onTimerFinish() {
                    circularView!!.stopTimer()
                    if (ZfpLabServerManager.isServerInstalled(this@TIMSSignActivity))
                    {
                        ZfpLabServerManager.startServerService(this@TIMSSignActivity)
                    }
                    showErrorDialog(getString(R.string.time_out),getString(R.string.not_able_to_connect))
                    if(invoiceProgressDialog!!.isShowing)
                    {
                        invoiceProgressDialog!!.dismiss()
                    }

                }

                override fun onTimerCancelled() {}
            })
        circularView!!.setOptions(builderWithTimer)
        invoiceProgressDialog = builder.create()
        invoiceProgressDialog!!.show()
    }

    override fun onError(errorData: ErrorData) {
        super.onError(errorData)
        log(TAG,"ERROR DATA :: "+errorData.message!!)
            if(circularView != null)
            {
                circularView!!.stopTimer()
            }
            if(invoiceProgressDialog != null && invoiceProgressDialog!!.isShowing)
            {
                invoiceProgressDialog!!.dismiss()
            }
            showErrorDialog(getString(R.string.invalid_request),errorData.message)
    }

}
