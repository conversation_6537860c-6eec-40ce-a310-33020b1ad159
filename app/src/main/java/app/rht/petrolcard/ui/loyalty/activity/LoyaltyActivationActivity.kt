package app.rht.petrolcard.ui.loyalty.activity

import android.app.Activity
import android.app.DatePickerDialog
import android.content.ContentValues
import android.content.Intent
import android.graphics.Bitmap
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.text.Html
import android.view.View
import android.widget.*
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.databinding.ActivityLoyaltyActivationBinding
import app.rht.petrolcard.ui.iccpayment.model.ActivationDetails
import app.rht.petrolcard.ui.loyalty.model.*
import app.rht.petrolcard.ui.loyalty.utils.HelpersLoyalty
import app.rht.petrolcard.ui.loyalty.utils.QRCode
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.loyalty.viewmodel.LoyaltyActivationViewModel
import app.rht.petrolcard.ui.ticket.activity.TicketType
import app.rht.petrolcard.utils.CoroutineAsyncTask
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.constant.AppConstant
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import com.bumptech.glide.Glide
import kotlinx.android.synthetic.main.activity_loyalty_activation.*
import kotlinx.android.synthetic.main.toolbar.view.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.lang.Exception
import java.text.SimpleDateFormat
import java.util.*
import kotlin.collections.ArrayList
import kotlin.concurrent.schedule

class LoyaltyActivationActivity : BaseActivity<LoyaltyActivationViewModel>(LoyaltyActivationViewModel::class) {


    private val TAG = LoyaltyBalanceActivity::class.simpleName
    private lateinit var mBinding: ActivityLoyaltyActivationBinding
    private var attendantTag = ""
    override fun onCreate(savedInstanceState: Bundle?) {

        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_loyalty_activation)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        initViews()
        authorizeNFC()
        showLoading(true)


    }
    private fun initViews()
    {
        mBinding.toolbarLayout.toolbar.tvTitle.text = getString(R.string.loyalty_activation)
        mBinding.toolbarLayout.toolbar.setNavigationOnClickListener {
            mBinding.toolbarLayout.toolbar.isEnabled = false
            setBeep()
            onBackPressed()
        }

        mCalendar = Calendar.getInstance()
        mBinding.dateOfBirth.setText(formatDate(Date()))
        getStationID()
        mBinding.tvActivationStation.text = stationName
    }


    private fun formatDate(mTime: Date): String? {
        val myFormat = "yyyy-MM-dd"
        val sdf = SimpleDateFormat(myFormat, Locale.ENGLISH)
        return sdf.format(mTime)
    }

    private lateinit var mCalendar: Calendar
    private fun showCalendar(mEditText: EditText) {
        val mYear: Int = mCalendar.get(Calendar.YEAR)
        val mMonth: Int = mCalendar.get(Calendar.MONTH)
        val mDay: Int = mCalendar.get(Calendar.DAY_OF_MONTH)
        val mDatePicker = DatePickerDialog(this,
            { _: DatePicker?, year: Int, monthOfYear: Int, dayOfMonth: Int ->
                mCalendar.set(Calendar.YEAR, year)
                mCalendar.set(Calendar.MONTH, monthOfYear)
                mCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth)
                updateEditText(mEditText)
            }, mYear, mMonth, mDay
        )
        mDatePicker.show()
    }

    private fun updateEditText(mEditText: EditText) {
        mEditText.setText(formatDate(mCalendar.time))
    }

    //region Card scanning
    private var cardNumber = ""
    private var cardHolder = ""
    private var nfcTag = ""
    private fun scanLoyaltyCard() {
        val intent = Intent(this, ICCLoyaltyActivity::class.java)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,true)
        loyaltyCardResultLauncher.launch(intent)
    }
    private var loyaltyCardResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                log(TAG,"data received ++++ ${data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)}")
                cardNumber = data.getStringExtra(AppConstant.LOYALTY_CARD_NUMBER)!!
                cardHolder = data.getStringExtra(AppConstant.LOYALTY_CARD_HOLDER)!!
                nfcTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                scanNfcTag(cardNumber)
                mBinding.tvPanNumber.text = cardNumber
                mBinding.tvTagNumber.text = nfcTag

               /* if(BuildConfig.DEBUG && mBinding.tvTagNumber.text.isEmpty()) ///for testing pupose only
                    mBinding.tvTagNumber.text = "04CF0042615B84"*/
            }
            else{
                gotoAbortMessageActivity("Try Again","Card reading failed")
            }
        }
    }
    //endregion

    //region Authorize NFC
    private fun authorizeNFC(){
        val intent = Intent(this, AuthoriseNfcActivity::class.java)
        intent.putExtra(AppConstant.CARD_NUMBER,cardNumber)
        tagAuthorizationReceiver.launch(intent)
    }
    private var tagAuthorizationReceiver = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                val authorized = data.getBooleanExtra(AppConstant.TAG_AUTHORISED, false)
                attendantTag = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                //log(TAG,"nfc data received ++++ $tag")
                log(TAG,"nfc authorized: $authorized")
                if(authorized){
                    scanLoyaltyCard()
                }
                else
                {
                    gotoAbortMessageActivity("Error","Invalid ")
                }
            }

        }
    }
    //endregion


    //region NFC scanning
    private fun scanNfcTag(cardNumber:String){
        val intent = Intent(this, NfcScanActivity::class.java)
        intent.putExtra(AppConstant.CARD_NUMBER,cardNumber)
        intent.putExtra(AppConstant.LOYALTY_ACTIVATION_REQUEST,true)
        nfcTagResultLauncher.launch(intent)
    }
    //private var scannedTag = ""
    private var nfcTagResultLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            val data: Intent? = result.data
            if(data!=null){
                mBinding.tvTagNumber.text = data.getStringExtra(AppConstant.CARD_NFC_TAG)!!
                log(TAG,"nfc data received ++++ ${mBinding.tvTagNumber.text}")

                mBinding.loadingLayout.visibility = View.GONE
                getData()
            }
            else{
                gotoAbortMessageActivity("Try Again","Nfc scanning failed")
            }
        }
    }
    //endregion

    //region documents image
    private var mValues: ContentValues? = null
    var mImageUri: Uri? = null
    private val CAMERA_REQUEST_DRIVER_ID = 1888
    private val CAMERA_REQUEST_CARTE_GRISE = 2888
    private val CAMERA_REQUEST_SOUCHE = 3888

    private var mFileImgCin: File? = null
    private var mFileImgCarteGrise:File? = null
    private var mFileImgSouche:File? = null

    override fun onImagePickSuccess(file: File, imagePickRequest: Int) {
        super.onImagePickSuccess(file, imagePickRequest)

        log(TAG, "IMAGE FILE RESULT: $file $imagePickRequest")

        try {

            when(imagePickRequest){
                CAMERA_REQUEST_DRIVER_ID -> {
                    mFileImgCin = file
                    mBinding.ivDriverID.visibility = View.VISIBLE
                    val imageUri = Uri.fromFile(file)
                    Glide.with(this@LoyaltyActivationActivity)
                        .load(imageUri)
                        .thumbnail(Glide.with(this@LoyaltyActivationActivity).load(R.drawable.ic_file))
                        .into(mBinding.ivDriverID)
                }
                CAMERA_REQUEST_CARTE_GRISE -> {
                    mFileImgCarteGrise = file
                    mBinding.imgCarteGrise.visibility = View.VISIBLE
                    val imageUri = Uri.fromFile(file)
                    Glide.with(this@LoyaltyActivationActivity)
                        .load(imageUri)
                        .thumbnail(Glide.with(this@LoyaltyActivationActivity).load(R.drawable.ic_file))
                        .into(mBinding.imgCarteGrise)
                }
                CAMERA_REQUEST_SOUCHE -> {
                    mFileImgSouche = file
                    mBinding.imgSouche.visibility = View.VISIBLE
                    val imageUri = Uri.fromFile(file)
                    Glide.with(this@LoyaltyActivationActivity)
                        .load(imageUri)
                        .thumbnail(Glide.with(this@LoyaltyActivationActivity).load(R.drawable.ic_file))
                        .into(mBinding.imgSouche)
                }
            }

        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getRealPathFromURI(contentUri: Uri): String? {
        return try {
            val proj = arrayOf(MediaStore.Images.Media.DATA)
            val cursor = managedQuery(contentUri, proj, null, null, null)
            val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
            cursor.moveToFirst()
            cursor.getString(columnIndex)
        } catch (e: Exception) {
            contentUri.path
        }
    }
    //endregion

    private var activationDetails: ActivationDetails? = null
    private var categories = ArrayList<String>()
    private var products = ArrayList<String>()
    private var stations = ArrayList<String>()
    private var letters = ArrayList<String>()

    var isGetData = false
    private fun getData(){
        showLoading(true)

        if(!isGetData) {
            isGetData = true
            Timer().schedule(20000) {
                isGetData = false
            }
            mViewModel.getActivationDetails(cardNumber)
            mViewModel.getLoyaltyCategories()
            mViewModel.getLoyaltyProducts()
            //mViewModel.getStations()
            mViewModel.getLetters()
        }
    }

    private var isTicketPrintStarted = false
    override fun setObserver() {

        mViewModel.activationDetails.observe(this){
            val response = it
            if(response.reponse == "1"){
                activationDetails = response.contenu!!
                log(TAG,"###ACTIVATION DETAILS::  $response")
                if (mBinding.tvTagNumber.text.toString().equals( response.contenu.nfcTag, ignoreCase = true)){
                    updateFromData(activationDetails!!)
                    showLoading(false)
                }
                else {
                    gotoAbortMessageActivity(getString(R.string.error),getString(R.string.invalid_nfc_tag))
                    /* val i3 = Intent(this@LoyaltyActivationActivity, AbortErrorActivity::class.java)
                     i3.putExtra("abortTitle", resources.getString(R.string.error))
                     i3.putExtra("abortMessage", "Invalid NFC Tag")
                     startActivity(i3)
                     finish()*/
                }
            }
            else{
                showLoading(false)
            }
        }
        mViewModel.loyaltyCategories.observe(this){
            val response = it
            if(response.reponse == "1"){
                categories.clear()
                //categories.addAll(response.contenu!!)
                for (category in response.contenu!!) {
                    if(category.isNotEmpty())
                        categories.add(category)
                }
                createAutoCompleteTVAdapter(mBinding.spinnerCategorie, categories)

            }
        }
        mViewModel.loyaltyProducts.observe(this){
            val response = it
            if(response.reponse == "1"){
                products.clear()
                for (product in response.contenu!!) {
                    products.add(product.name!!)
                }
                createAutoCompleteTVAdapter(mBinding.spinnerCarburant, products)
            }
        }
        mViewModel.stations.observe(this){
            val response = it
            if(response.reponse == "1"){
                stations.clear()
                stations.addAll(getNamesOfStations(response.contenu!!))
            }
        }
        mViewModel.letters.observe(this){
            val response = it
            if(response.reponse == "1"){
                letters.clear()
                letters.addAll(getLettersOfPlaques(response.contenu!!))
                createSpinnerAdapter(mBinding.spinnerLettres, letters)
            }
        }
        mViewModel.activateCustomer.observe(this){
            val response = it
            showLoading(false)
            log(TAG,"${response.reponse}")
            if(response.reponse == "1"){
                showToast(getString(R.string.loyalty_details_updated))
                if(!isTicketPrintStarted){
                    isTicketPrintStarted = true
                    Timer().schedule(10000){
                        isTicketPrintStarted = false
                    }

                    PrintTicketTask(TicketType.Customer).execute()
                }
            }
            else{
                showToast(response.error)
            }
        }
    }

    private fun showLoading(isVisible: Boolean){
        if(!isVisible)
            mBinding.loadingLayout.visibility = View.GONE
        else
        {
            mBinding.loadingLayout.visibility = View.VISIBLE
            hideKeyBoard()
        }
    }

    private var hasCardData = false
    private fun updateFromData(details: ActivationDetails) {

        hideKeyBoard()

        mBinding.btnSignedDoc.text = getString(R.string.upload)
        mBinding.btnDriverId.text = getString(R.string.upload)
        hasCardData = true

        mBinding.tvPanNumber.text = details.pan
        mBinding.tvTagNumber.text = details.nfcTag
        mBinding.edittextNom.setText(details.firstName)
        mBinding.edittextPrenom.setText(details.lastName)
        mBinding.edittextCin.setText(details.driverId)
        loadImageFromUrl(mBinding.ivDriverID, details.driverIdImage)
        mBinding.edittextTel.setText(details.telephone)
        mBinding.edittextMatricule.setText(details.plateNumber)
        mBinding.dateOfBirth.setText(details.dob)
        mBinding.edittextLieuNaissance.setText(details.placeOfBirth)
        mBinding.edittextVille.setText(details.city)
        mBinding.edittextAdresse.setText(details.address)

        /*stationDropdown.setText(getStationNameById(details.getStationId()));
         createAutoCompleteTVAdapter(stationDropdown,stationNames);*/
        mBinding.edittextPermis.setText(details.liecenceNumber)
        mBinding.spinnerCategorie.setText(details.categoryId)
        createAutoCompleteTVAdapter(mBinding.spinnerCategorie, categories)
        mBinding.spinnerCarburant.setText(details.fuel)
        createAutoCompleteTVAdapter(mBinding.spinnerCarburant, products)
        mBinding.edittextCarteGrise.setText(details.vehicleRegNumber)
        loadImageFromUrl(mBinding.imgCarteGrise, details.vehicleRegImage)
        mBinding.edittextArgement.setText(details.approval)
        mBinding.edittextCvFiscaux.setText(details.fiscalHp)
        mBinding.btnActivate.text = getString(R.string.update)

        hideKeyBoard()

    }
    private fun loadImageFromUrl(iv: ImageView, url: String) {
        Glide.with(this@LoyaltyActivationActivity)
            .load(url)
            .thumbnail(
                Glide.with(this@LoyaltyActivationActivity)
                    .load(R.drawable.ic_file)
            )
            .into(iv)
    }
    private fun createAutoCompleteTVAdapter(autoCompleteTextView: AutoCompleteTextView, mData: java.util.ArrayList<String>) {
        val dataAdapter = ArrayAdapter(this, R.layout.item_autocomplete_textview, R.id.tvItemName, mData)
        autoCompleteTextView.setAdapter(dataAdapter)
    }
    private fun getLettersOfPlaques(mDetailLettresResponse: List<Letters>): ArrayList<String> {
        val mLetters = java.util.ArrayList<String>()
        for (letter in mDetailLettresResponse) mLetters.add(letter.value!!)
        return mLetters
    }
    private fun getNamesOfStations(mDetailStationsResponse: List<Station>): ArrayList<String> {
        val mNames = java.util.ArrayList<String>()
        for (mStation in mDetailStationsResponse) mNames.add(mStation.name!!)
        return mNames
    }
    private fun createSpinnerAdapter(mSpinner: Spinner, mData: java.util.ArrayList<String>) {
        val dataAdapter = ArrayAdapter(this, android.R.layout.simple_spinner_item, mData)
        dataAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        mSpinner.adapter = dataAdapter
    }

    var stationName: String? = null
    var stationId:String? = null
    private fun getStationID() {
        stationId = prefs.getReferenceModel()!!.terminal!!.stationId.toString()
        stationName = prefs.getReferenceModel()!!.terminal!!.stationName + ""
    }



    private fun focusOnView(view: View) {
        mBinding.scrollView.post(Runnable { mBinding.scrollView.scrollTo(0, view.bottom) })
    }

    private fun createRequestBody(file: File, mName: String): MultipartBody.Part {
        val requestFile = file.asRequestBody("multipart/form-data".toMediaType())
        return MultipartBody.Part.createFormData(mName, file.name, requestFile)
    }

    private lateinit var loyaltyCustomerDetails: LoyaltyCustomer

    private var isDataSent = false
    private fun sendData(){
        hideKeyBoard()
        if (mBinding.edittextNom.length() < 1) {
            mBinding.edittextNom.error = "Enter First Name"
            focusOnView(mBinding.edittextNom)
        } else if (mBinding.edittextPrenom.length() < 1) {
            mBinding.edittextPrenom.error = "Enter Last Name"
            focusOnView(mBinding.edittextPrenom)
        } else if (mBinding.edittextCin.length() < 1) {
            mBinding.edittextCin.error = "Enter Driver ID"
            focusOnView(mBinding.edittextCin)
        } else if (mFileImgCin == null && !hasCardData) {
            showToast("Please Attach Driver ID Document")
            focusOnView(mBinding.edittextCin)
        } else if (mBinding.edittextTel.length() < 1) {
            mBinding.edittextTel.error = "Enter Contact Number"
            focusOnView(mBinding.edittextTel)
        } else if (mBinding.edittextMatricule.length() < 1) {
            mBinding.edittextMatricule.error = "Enter Car Plate Number"
            focusOnView(mBinding.edittextMatricule)
        }else if (mBinding.edittextPermis.length() < 1) {
            mBinding.edittextPermis.error = "Enter Licence Number"
            focusOnView(mBinding.edittextPermis)
        } else if (mFileImgSouche == null && !hasCardData) {
            focusOnView(mBinding.btnSignedDoc)
            showToast("Please Attach Signed Document")
        }
        else {


            val fname: String = mBinding.edittextNom.text.toString().trim()
            val lname: String = mBinding.edittextNom.text.toString().trim()
            val driverId: String = mBinding.edittextCin.text.toString().trim()
            val tel: String = mBinding.edittextTel.text.toString().trim()
            val plateNo: String = mBinding.edittextMatricule.text.toString().trim()
            val dob: String = mBinding.dateOfBirth.text.toString().trim()
            val pob: String = mBinding.edittextLieuNaissance.text.toString().trim()
            val address: String = mBinding.edittextAdresse.text.toString().trim()
            val city: String = mBinding.edittextVille.text.toString().trim()
            val licNo: String = mBinding.edittextPermis.text.toString().trim()
            val vRegNo: String = mBinding.edittextCarteGrise.text.toString().trim()
            val approval: String = mBinding.edittextArgement.text.toString().trim()
            val fiscalHp: String = mBinding.edittextCvFiscaux.text.toString().trim()
            val category: String = mBinding.spinnerCategorie.text.toString().trim()
            val fuel: String = mBinding.spinnerCarburant.text.toString().trim()
            val sn = MainApp.sn
            val tag = mBinding.tvTagNumber.text.toString().trim()
            val hash = "123"


            loyaltyCustomerDetails = LoyaltyCustomer()
            loyaltyCustomerDetails.date = HelpersLoyalty.getDateUsInStringFormat(Date())
            loyaltyCustomerDetails.fName = fname
            loyaltyCustomerDetails.lName = lname
            loyaltyCustomerDetails.driverId = driverId
            loyaltyCustomerDetails.tel = tel
            loyaltyCustomerDetails.plateNo = plateNo
            loyaltyCustomerDetails.sn = MainApp.sn
            loyaltyCustomerDetails.badge = attendantTag
            loyaltyCustomerDetails.tag = mBinding.tvTagNumber.text.toString().trim()
            loyaltyCustomerDetails.pan = mBinding.tvPanNumber.text.toString().trim()
            loyaltyCustomerDetails.vRegNo = vRegNo


            val details = LoyaltyCustomerRequest()

            details.date_naissance = HelpersLoyalty.getDateUsInStringFormat(Date())
                .toRequestBody("text/plain".toMediaType())
            details.nom = fname.toRequestBody("text/plain".toMediaType())
            details.prenom = lname.toRequestBody("text/plain".toMediaType())
            details.cin = driverId.toRequestBody("text/plain".toMediaType())
            details.tel = tel.toRequestBody("text/plain".toMediaType())
            details.matricule =
                (plateNo+ "" + " "+ ""+ " ").toRequestBody("text/plain".toMediaType())
            details.date_naissance = dob.toRequestBody("text/plain".toMediaType())
            details.lieu_naissance = pob.toRequestBody("text/plain".toMediaType())
            details.adresse = address.toRequestBody("text/plain".toMediaType())
            details.ville = city.toRequestBody("text/plain".toMediaType())
            details.num_permis = licNo.toRequestBody("text/plain".toMediaType())
            details.num_carte_grise = vRegNo.toRequestBody("text/plain".toMediaType())
            details.agrement = approval.toRequestBody("text/plain".toMediaType())
            details.cv_fiscaux = fiscalHp.toRequestBody("text/plain".toMediaType())
            details.sn = sn!!.toRequestBody("text/plain".toMediaType())
            details.hashKey = hash.toRequestBody("text/plain".toMediaType())
            details.badge = attendantTag.toRequestBody("text/plain".toMediaType())
            details.version = BuildConfig.VERSION_NAME.toRequestBody("text/plain".toMediaType())
            details.categorie = category.toRequestBody("text/plain".toMediaType())
            details.station = stationId!!.toRequestBody("text/plain".toMediaType())
            details.carburant = fuel.toRequestBody("text/plain".toMediaType())

            details.pan = cardNumber.toRequestBody("text/plain".toMediaType())
            details.tag = (tag+"").toRequestBody("text/plain".toMediaType())
            details.connection = ("close").toRequestBody("text/plain".toMediaType())

            if (mFileImgCarteGrise != null) {
                val mFileCarteGrisePart = createRequestBody(mFileImgCarteGrise!!, "carte_grise_file")
                details.carte_grise_file = mFileCarteGrisePart
            } else {
                log(TAG, "Vehicle REg File missing")
            }

            if (mFileImgCin != null) {
                val mFileCinPart = createRequestBody(mFileImgCin!!, "cin_file")
                details.cin_file = mFileCinPart
            } else {
                log(TAG, "Driver ID File missing")
            }

            if (mFileImgSouche != null) {
                val mFileSoouchePart = createRequestBody(mFileImgSouche!!, "souche_file")
                details.souche_file = mFileSoouchePart
            } else {
                log(TAG, "Signed Doc File missing")
            }

            isDataSent = true

            Timer().schedule(10000){
                isDataSent = false
            }

            mViewModel.activateLoyaltyCustomer(hasCardData,details)

            showLoading(true)

        }


    }

    fun onClick(view:View){
        log(TAG,"## Button Clicked")
        when (view){
            mBinding.dobLayout -> showCalendar(mBinding.dateOfBirth)
            mBinding.btnActivate -> sendData()
            /*mBinding.btnDriverId -> captureImage(CAMERA_REQUEST_DRIVER_ID)
            mBinding.btnSignedDoc -> captureImage(CAMERA_REQUEST_SOUCHE)
            mBinding.btnVehicleRegNumber -> captureImage(CAMERA_REQUEST_CARTE_GRISE)*/

            mBinding.btnDriverId -> actionCameraToClickImage(CAMERA_REQUEST_DRIVER_ID)
            mBinding.btnSignedDoc -> actionCameraToClickImage(CAMERA_REQUEST_SOUCHE)
            mBinding.btnVehicleRegNumber -> actionCameraToClickImage(CAMERA_REQUEST_CARTE_GRISE)
        }
    }

    var mPrintDialog : AlertDialog? = null
    inner class PrintTicketTask(private val ticketType: TicketType) : CoroutineAsyncTask<String, String, Void?>() {

        override fun onPreExecute() {
            super.onPreExecute()
            if(mPrintDialog==null)
                mPrintDialog = getMyPrintDialog()
        }

        override fun doInBackground(vararg params: String): Void? {
            printReceipt(ticketType)
            return null
        }

        override fun onPostExecute(result: Void?) {
            if(ticketType == TicketType.Customer){
                showAttendantPrintDialog()
            }
            else
            {
                gotoSuccessMessageActivity(getString(R.string.success),getString(R.string.loyalty_details_updated),LoyaltyDashboardActivity::class.java)
                //startActivity(Intent(this@LoyaltyActivationActivity, LoyaltyDashboardActivity::class.java))
                //finish()
            }
        }
    }
    private fun printReceipt(ticketType: TicketType) {
        try{
            val commands = ArrayList<PrintCmd>()
            commands.add(PrintCmd("\n"+loyaltyCustomerDetails.date, AlignmentType.CENTER))
            commands.add(PrintCmd("\n"+ getString(R.string.loyalty_activation).uppercase(Locale.getDefault()), AlignmentType.CENTER,true))
            commands.add(PrintCmd("\n${getString(R.string.pan_label)} : ${loyaltyCustomerDetails.pan}"))
            commands.add(PrintCmd("\n${getString(R.string.nfc_tag)} : ${loyaltyCustomerDetails.tag}"))
            commands.add(PrintCmd("\n${getString(R.string.first_name).replace("*","")} : ${loyaltyCustomerDetails.tag}"))
            commands.add(PrintCmd("\n${getString(R.string.last_name).replace("*","")} : ${loyaltyCustomerDetails.lName}"))
            commands.add(PrintCmd("\n${getString(R.string.driver_id_lebel)} : ${loyaltyCustomerDetails.driverId}"))
            commands.add(PrintCmd("\n${getString(R.string.tel_lebel)} : ${loyaltyCustomerDetails.tel}"))
            commands.add(PrintCmd("\n${getString(R.string.reg_number)} : ${loyaltyCustomerDetails.vRegNo}"))
            commands.add(PrintCmd("\n${getString(R.string.operator_id)} : ${loyaltyCustomerDetails.badge}"))
            commands.add(PrintCmd("\n${getString(R.string.pos_sn_lebel)} : ${loyaltyCustomerDetails.sn}"))

            var qrCode : Bitmap? = null
            if(ticketType == TicketType.Customer){

                commands.add(PrintCmd("\n${getString(R.string.customer_sign_lebel)}",AlignmentType.CENTER))
                
                val qrCodeData = loyaltyCustomerDetails.createJSON()
                qrCode = QRCode.generateControlKey(qrCodeData)!!

                commands.add(PrintCmd(qrCode,512,512, AlignmentType.CENTER, PrintContentType.IMAGE))
                commands.add(PrintCmd("\n${getString(R.string.customer_sign_lebel)}",AlignmentType.CENTER))
                commands.add(PrintCmd("\n${getString(R.string.customer_receipt)}",AlignmentType.CENTER))
            }
            else {
                commands.add(PrintCmd("\n${getString(R.string.merchant_receipt)}",AlignmentType.CENTER))
            }

            TicketPrinter(this).printTicket(commands)

            if(ticketType == TicketType.Customer){
               qrCode!!.recycle()
            }

        } catch (e:Exception ){
            e.printStackTrace()
        }
    }
    fun showAttendantPrintDialog(){
        MaterialDialog(this)
            .title(text = getString(R.string.confirm))
            .message(text = Html.fromHtml("<font color='#000000'>"+getString(R.string.do_you_want_to_print_merchant_receipt)+"</font>"))
            .cancelable(false)
            .show {
                cornerRadius(res = R.dimen.my_corner_radius)
                positiveButton(text = "Print",click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        setBeep()
                        PrintTicketTask(TicketType.Attendant).execute()
                    }
                })
                negativeButton(text = "Cancel", click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        setBeep()
                        val i = Intent(this@LoyaltyActivationActivity,LoyaltyDashboardActivity::class.java)
                        startActivity(i)
                        finish()
                    }
                })
            }
    }
}