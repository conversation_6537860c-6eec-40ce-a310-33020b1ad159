package app.rht.petrolcard.ui.loyalty.viewmodel

import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.loyalty.model.LoyaltyBalance
import app.rht.petrolcard.utils.AppPreferencesHelper
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.toRequestBody


class LoyaltyBalanceViewModel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
): CommonViewModel(mNetworkService,preferencesHelper) {

    var loyaltyCustomerResponse = MutableLiveData<BaseResponse<List<LoyaltyBalance>>>()

    fun getLoyaltyBalance(cardNumber:String){
        val sn = MainApp.sn!!
        val url = (preferencesHelper.baseUrl+ NetworkRequestEndPoints.GET_LOYALTY_BALANCE).replace("-tpe","")

        val serial = sn.toRequestBody("text/plain".toMediaType())
        val card = cardNumber.toRequestBody("text/plain".toMediaType())

        requestData(mNetworkService.getLoyaltyCustomer(url, serial, card), {
            loyaltyCustomerResponse.postValue(it)
        })
    }


}