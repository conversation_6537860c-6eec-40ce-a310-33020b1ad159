package app.rht.petrolcard.ui.reference.model

import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.service.model.TerminalNozzleDetails
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.ui.modepay.model.MpesaModel
import androidx.annotation.Keep
import app.rht.petrolcard.ui.product.model.FuelPosPumpResponse
import app.rht.petrolcard.ui.product.model.PumpStateMessage
import app.rht.petrolcard.ui.ticket.model.RecieptFormatResponse

import com.google.gson.annotations.SerializedName


@Keep
data class ReferenceModel(
    val `4G`: Int?,
    val ASKFORBADGE: Boolean?=false,
    val COMPANY: CompanyModel,
    val FUELPOS: FuelPOSModel,
    val FUSION: FusionModel?,
    var GreyList: ArrayList<GreyListModel>,
    val GreyListVersion: Int,
    val fiscal_printer:FiscalPrinterModel?,
    val LOG: Int?,
    val LogTCP: Int?,
    val OFFLINEONLY: Boolean?=false,
    val PORTESD: Int,
    val SERVER: ServerModel?,
    val USE_SD_CARD: Boolean?=false,
    val WITHPICTURE: Boolean?=false,
    //val badge: String?,
   /* val badge: ArrayList<ManagerBadge> = ArrayList(),*/
    val blackListVersion: Int,
    var blacklist: ArrayList<BlackListModel>,
    val debug: Int?,
    val motifs_opposition: ArrayList<CardBlockedReasonsModel>,
    val pompes: ArrayList<PumpsModel>?,
    val pompiste: ArrayList<GasStationAttendantModel>,
    val prix: ArrayList<PriceModel>,
    val prixConseille: String?,
    val produit: ArrayList<ProductModel>,
    val restrictions_articles: ArrayList<RestrictedProductsModel>,
    val restrictions_horaire: ArrayList<RestrictedSchedulesModel>,
    val restrictions_jrs_free: ArrayList<RestrictedHolidaysModel>,
    val restrictions_secteurs: ArrayList<RestrictedSectorsModel>,
    val restrictions_stations: ArrayList<RestrictedStationModel>,
    val station: StationModel?,
    val status_cartes: List<CardStatusModel>,
    val terminal: TerminalModel? = null,
    val type_cartes: List<CardTypeModel>,
    val mpesa_credentials:MpesaModel?,
    val mtn_pay_credentials:MtnPayCredentials?,
    val url: String,
    val RFID_TERMINALS: TerminalNozzleDetails? = null,
    var isRfidAvailable: Boolean? = false,
    var LOYALTY: Boolean? = false,
    var mode_of_payment_list: List<ModePaymentModel>? = null,
    var category_list: ArrayList<CategoryListModel>? = null,
    val BATTERY_ALERT: Int?,
    val RFID_VERIFICATION_TYPE: Int?,
    val TELECOLLECT_TIME: Long?,
    val ONLINE_PAYMENT_IP: String?,
    val ONLINE_PAYMENT_PORT: Int?,
    val PRINT_RECEIPT:Int?,
    val PRINTER_TYPE:Int?,
    val FUEL_QTY_UNIT:String?,
    val MAX_REFILL_AMNT:String?,
    val FUELLING_LIMIT_TYPE:Int?,
    val IMPLEMENT_DISCOUNT:Boolean?=false,
    val TERMINAL_TYPE:Int?=2,
    val void_transaction_seconds:Long?=30,
    val disputed_timeout:Long?=15,
    @SerializedName(value = "MENU", alternate = ["menu"])
    var settingsMenuModel: SettingsMenuModel?,
    val fuelVat: VatModel?,
    val shopVat: VatModel?,
    val serviceVat: VatModel?,
    @SerializedName("TELECOLLECT_PRINT_RECEIPT")
    val teleCollectPrintReceipt:Boolean?=true,
    val APP_AUTO_RUN: Boolean?=false,
    var IS_ATTENDANT_RECHARGE_CARD: Boolean?=false,
    @SerializedName("enable_testfairy")
    var enableTestFairy:Boolean?=false,
    @SerializedName("test_fairy_token", alternate = ["test_fairy_token "])
    var testFairyToken:String?,
    val force_update:Boolean?=false,
    val app_expiry_date:String?="",
    @SerializedName("OPT_CONFIG")
    var terminalConfig: TerminalConfig?,
    @SerializedName("FUEL_POS_MESSAGE")
    var fuelPosMessage: PumpStateMessage?,
    @SerializedName("fuelpos_pump_response")
    var fuelPosPumpResponse: FuelPosPumpResponse?,
    @SerializedName("receipt_format_fields")
    var receiptFormatFields: RecieptFormatResponse?,
    @SerializedName("crash_log_email_config")
    var crashLogEmailConfig: CrashLogEmailConfig?,
    @SerializedName("tims_app_packagename")
    var timsAppPackageName: String?="",
    @SerializedName("is_send_transaction_online")
    var is_send_transaction_online: Boolean?=false,
    @SerializedName("qr_code_ticket")
    val qrCodeTicket:QrCodeTicket ? = null,
    @SerializedName("telecollect_transaction_count")
    val telecollect_transaction_count:Int? = 5 ,
  /*  @SerializedName("api_end_points")
    val api_end_points:ApiEndPointsModel? = null*/
    @SerializedName("log_delete_schedule_hour")
    val log_delete_schedule_hour:Long? = 76,
    @SerializedName("buffer_sign_transaction_count")
    val buffer_sign_transaction_count:Int? = 2,
    @SerializedName("is_delete_log_success_transaction")
    val is_delete_log_success_transaction:Boolean? = false,
    @SerializedName("is_delete_log_on_reference")
    val is_delete_log_on_reference:Boolean? = false
)

@Keep
data class SettingsMenuModel(
    @SerializedName("BankCard")
    var bankCard: Boolean?=false,
    @SerializedName("CloseApp")
    var closeApp: Boolean?=false,
    @SerializedName("FleetCard")
    var fleetCard: Boolean?=false,
    @SerializedName("FuelPrice")
    var fuelPrice: Boolean?=false,
    @SerializedName("logs")
    var logs: Boolean?=false,
    @SerializedName("NavigationBar")
    var navigationBar: Boolean?=false,
    @SerializedName("Operation")
    var operation: Boolean?=false,
    @SerializedName("StatusBar")
    var statusBar: Boolean?=false,
    @SerializedName("Telecollect")
    var telecollect: Boolean?=false,
    @SerializedName("UninstallApps")
    var uninstallApps: Boolean?=false,
    @SerializedName("TerminalParams")
    var terminalParams: Boolean?=false
)

@Keep
data class ManagerBadge(
    @SerializedName("badge")
    var badge: String?,
    @SerializedName("mngr_id")
    var mngrId: Int?,
    @SerializedName("mngr_pin")
    var mngrPin: String?
)

//region terminal configuration
@Keep
data class TerminalConfig(
    @SerializedName("card_ceiling_limits")
    var cardCeilingLimits: CardCeilingLimits?,
    @SerializedName("card_settings")
    var cardSettings: CardSettings?,
    @SerializedName("home_page")
    var homePageScreen: HomePageScreen?,
    @SerializedName("receipt_setting")
    var receiptSetting: ReceiptSetting?,
    @SerializedName("clear_before_trx")
    var clearBeforeTrx : Boolean?=false,
)

@Keep
data class CardCeilingLimits(
    @SerializedName("allowed_balance")
    var allowedBalance: Boolean?=false,
    @SerializedName("card_balance")
    var cardBalance: Boolean?=false,
    @SerializedName("daily_limit")
    var dailyLimit: Boolean?=false,
    @SerializedName("monthly_limit")
    var monthlyLimit: Boolean?=false,
    @SerializedName("weekly_limit")
    var weeklyLimit: Boolean?=false
)

@Keep
data class CardSettings(
    @SerializedName("card_unlock")
    var cardUnlock: Boolean?=false,
    @SerializedName("change_pin")
    var changePin: Boolean?=false,
    @SerializedName("import_inject_key")
    var importInjectKey:Boolean?=false,
    @SerializedName("recharge")
    var recharge: Boolean?=false,
    @SerializedName("transaction_history")
    var transactionHistory: Boolean?=false,
    @SerializedName("unblock_card")
    var unblockCard:Boolean?=false,
    @SerializedName("update_card")
    var updateCard: Boolean?=false,
    @SerializedName("pending_refund")
    var pendingRefund:Boolean?=false,
    @SerializedName("update_mileage")
    var updateMileage: Boolean?=false
)

@Keep
data class CustomerThankYouMessage(
    @SerializedName("message")
    var message: String?
)

@Keep
data class HomePageScreen(
    @SerializedName("manage")
    var manageScreen: ManageScreen?,
    @SerializedName("counters")
    var counters: Counters?
)

@Keep
data class ManageScreen(
    @SerializedName("loyalty_card")
    var loyaltyCard: Boolean?=false,
    @SerializedName("settings")
    var settings: Boolean?=false,
    @SerializedName("telecollect")
    var telecollect: Boolean?
)

@Keep
data class Counters(
    @SerializedName("total_recharge")
    var totalRecharge: Boolean?=false,
    @SerializedName("total_trx")
    var totalTrx: Boolean?=false
)

@Keep
data class ReceiptSetting(
    @SerializedName("customer_message")
    var customerMessage: String?,
    @SerializedName("date_format")
    var dateFormat: String?,
    @SerializedName("customer_copy")
    var customerCopy: String?,
    @SerializedName("merchant_copy")
    var merchantCopy: String?,
    @SerializedName("quantityDecimal")
    var quantityDecimal: Int?,
    @SerializedName("unitPriceDecimal", alternate = ["unitPriceDecimal "])
    var unitPriceDecimal: Int?,
    @SerializedName("vatDecimal")
    var vatDecimal: Int?,
    @SerializedName("netDecimal")
    var netDecimal: Int?,
    @SerializedName("discountDecimal")
    var discountDecimal: Int?,
    @SerializedName("totalAmountDecimal")
    var totalAmountDecimal: Int?,
    @SerializedName("limitDecimal")
    var limitDecimal: Int?,
    @SerializedName("receipt_preview")
    var receiptPreview : Boolean,
    @SerializedName("cancel_trx_print_receipt")
    val cancelTrxPrintReceipt:Int ?= 1,
    @SerializedName("receipt_layout")
    val receipt_layout:Int ?= 1
)


@Keep
data class CrashLogEmailConfig(
    @SerializedName("emails_with_semicolon")
    var emailsWithSemicolon: String?,
    @SerializedName("host")
    var host: String?,
    @SerializedName("password")
    var password: String?,
    @SerializedName("port")
    var port: String?,
    @SerializedName("username")
    var username: String?
)

@Keep
data class QrCodeTicket(
    @SerializedName("auth_token")
    var authToken: String?,
    @SerializedName("code_generate_url")
    var codeGenerateUrl: String?,
    @SerializedName("scan_message")
    var scanMessage: String?,
    @SerializedName("enabled")
    var enabled: Boolean? = false,
    @SerializedName("scan_timeout")
    var scanTimeout: String? = "30"
)

//endregion