package app.rht.petrolcard.ui.settings.card.common.activity

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.databinding.ActivityManageCardBinding
import app.rht.petrolcard.ui.badge.activity.BadgeActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.settings.common.activity.SettingsActivity
import app.rht.petrolcard.ui.settings.card.common.model.CardItemModel
import app.rht.petrolcard.utils.constant.AppConstant
import kotlinx.android.synthetic.main.toolbar.view.*
import java.util.ArrayList

import androidx.activity.result.contract.ActivityResultContracts.StartActivityForResult
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.settings.card.unblockcard.activity.UnblockActivity
import app.rht.petrolcard.ui.settings.card.unlockpin.activity.EnterNewPinActivity
import app.rht.petrolcard.utils.constant.Workflow
import app.rht.petrolcard.utils.paxutils.system.SysTester
import app.rht.petrolcard.utils.helpers.MultiClickPreventer


class ManageUninstallActivity: BaseActivity<CommonViewModel>(CommonViewModel::class) , RecyclerViewArrayAdapter.OnItemClickListener<CardItemModel> {

    private lateinit var mBinding: ActivityManageCardBinding

    private val TAG = SettingsActivity::class.simpleName

    private var intentExtrasModel: IntentExtrasModel? = null


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_manage_card)

        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_manage_card)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        setupToolbar()
        intentExtrasModel = IntentExtrasModel()
        setupItemsInList()
    }

    private fun setupToolbar()
    {
        mBinding.toolbarView.toolbar.tvTitle.text = getString(R.string.card_management)
        mBinding.toolbarView.toolbar.setNavigationOnClickListener {
            mBinding.toolbarView.toolbar.isEnabled = false
            finish()
        }
    }

    override fun setObserver() {

    }
    override fun onItemClick(view: View, item: CardItemModel) {
        MultiClickPreventer.preventMultiClick(view)
        setBeep()
        when(item.id){
            "1" -> {
                if (!isPackageInstalled("app.rht.petrolcard.modeN")) {
                    showToast("App Not Found")
                } else {
                    val s = SysTester.getInstance().unIntsallApp("app.rht.petrolcard.modeN")
                    if (s == 0) {
                        showToast("Uninstalled Successfully")
                    }
                }
            }
            "2" -> {
                if (!isPackageInstalled("ma.petrol.petrolCardxx.modeN")) {
                    showToast("App Not Found")
                } else {
                    val s = SysTester.getInstance().unIntsallApp("ma.petrol.petrolCardxx.modeN")
                    if (s == 0) {
                        showToast("Uninstalled Successfully")
                    }
                }
            }
            "3" -> {
                if (!isPackageInstalled("com.ebe.edc.nbe")) {
                    showToast("App Not Found")
                } else {
                    val s: Int = SysTester.getInstance().unIntsallApp("com.ebe.edc.nbe")
                    if (s == 0) {
                        showToast("Uninstalled Successfully")
                    }
                }
            }
            "4" -> {
                if (!isPackageInstalled("app.rht.pax.mylauncher")) {
                    showToast("App Not Found")
                } else {
                    val s: Int = SysTester.getInstance().unIntsallApp("app.rht.pax.mylauncher")
                    if (s == 0) {
                        showToast("Uninstalled Successfully")
                    }
                }
            }


        }
    }

    private var settingsItems : ArrayList<CardItemModel> = ArrayList()
    private fun setupItemsInList(){
        settingsItems.add(CardItemModel(R.mipmap.ic_launcher_round,"Petrol Card App V3","1","#F97F51"))
        settingsItems.add(CardItemModel(R.drawable.logo_card,"Petrol Card App V2","2","#1B9CFC"))
        settingsItems.add(CardItemModel(R.drawable.nbe_bank_logo,"NBE Bank App ","3","#FC427B"))
        settingsItems.add(CardItemModel(R.drawable.logo_card,"FBS Launcher","4","#FC427B"))
        mBinding.rvSettings.removeAllViews()
        val mSettingsAdapter = RecyclerViewArrayAdapter(settingsItems,this)
        mBinding.rvSettings.adapter = mSettingsAdapter
        mSettingsAdapter.setContext(this)
        mSettingsAdapter.notifyDataSetChanged()

    }

}