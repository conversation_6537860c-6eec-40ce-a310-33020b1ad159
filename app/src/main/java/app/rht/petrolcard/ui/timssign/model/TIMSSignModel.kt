package app.rht.petrolcard.ui.timssign.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class TIMSSignModel (
    var hsCode :String? = null,
    var fuelQtyUnit :String? =null,
    var traderSystemInvoiceNumber :String? = null,
    var controlUnitSerialNumber :String? = null,
    var invoice_details:TIMSInvoiceModel?= TIMSInvoiceModel(),
    var customer_details:TIMSCustomerDetails?=TIMSCustomerDetails()
    ):Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readParcelable(TIMSInvoiceModel::class.java.classLoader),
        parcel.readParcelable(TIMSCustomerDetails::class.java.classLoader)
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(hsCode)
        parcel.writeString(fuelQtyUnit)
        parcel.writeString(traderSystemInvoiceNumber)
        parcel.writeString(controlUnitSerialNumber)
        parcel.writeParcelable(invoice_details, flags)
        parcel.writeParcelable(customer_details, flags)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TIMSSignModel> {
        override fun createFromParcel(parcel: Parcel): TIMSSignModel {
            return TIMSSignModel(parcel)
        }

        override fun newArray(size: Int): Array<TIMSSignModel?> {
            return arrayOfNulls(size)
        }
    }
}