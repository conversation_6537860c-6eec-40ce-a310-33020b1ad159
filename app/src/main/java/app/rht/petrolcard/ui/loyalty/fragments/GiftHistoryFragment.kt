package app.rht.petrolcard.ui.loyalty.fragments

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter
import app.rht.petrolcard.baseClasses.fragment.BaseFragment
import app.rht.petrolcard.databinding.ActivityAmountFillupBinding.inflate
import app.rht.petrolcard.databinding.FragmentRedeemHistoryBinding
import app.rht.petrolcard.ui.loyalty.model.LoyaltyGiftHistory
import app.rht.petrolcard.ui.loyalty.viewmodel.RedeemHistoryViewModel
import app.rht.petrolcard.utils.helpers.MultiClickPreventer


class GiftHistoryFragment : BaseFragment<RedeemHistoryViewModel>(RedeemHistoryViewModel::class),
    RecyclerViewArrayAdapter.OnItemClickListener<LoyaltyGiftHistory> {

    companion object {
        val TAG = AvailableGiftsFragment::class.simpleName
        private var loyaltyGifts = ArrayList<LoyaltyGiftHistory>()
    }

    private lateinit var mBinding: FragmentRedeemHistoryBinding
    private lateinit var adapter : RecyclerViewArrayAdapter<LoyaltyGiftHistory>

    override fun createDataBinding(inflater: LayoutInflater, container: ViewGroup?): View {
        mBinding = FragmentRedeemHistoryBinding.inflate(inflater, container, false)
        mBinding.lifecycleOwner = this
        mBinding.model = mViewModel
        return mBinding.root
    }

    override fun onStart() {
        super.onStart()
        init()
    }

    override fun onError(errorData: ErrorData) {

    }

    fun init() {
        //mViewModel.getNotificationsAppointment()// api call
        loading(true)
        setUpRecyclerView()
    }

    private fun setUpRecyclerView() {
        adapter = RecyclerViewArrayAdapter(loyaltyGifts, this)
        mBinding.rvGifts.adapter = adapter
    }

    override fun onItemClick(view: View, item: LoyaltyGiftHistory) {
        MultiClickPreventer.preventMultiClick(view)
        println("${item.name} -- ${item.point}")
    }

    fun getGiftHistory(pan:String){
        loading(true)
        mViewModel.getLoyaltyGifts(pan)
    }

    override fun setObserver() {
        mViewModel.loyaltyGiftHistoryResponse.observe(this){
            val response = it
            if(response.reponse == "1"){
                loading(false)
                if(response.contenu!!.isEmpty())
                    noDataFound(true)
                else{
                    noDataFound(false)
                    loading(false)
                    loyaltyGifts.clear()
                    loyaltyGifts.addAll(response.contenu)
                    //rvGifts.adapter!!.notifyDataSetChanged()
                }

            }
            else {
                noDataFound(true)
                showToast(response.error)
            }
        }
    }

    private fun loading(visible:Boolean){
        if(visible)
            mBinding.loadingLayout.visibility = View.VISIBLE
        else
            mBinding.loadingLayout.visibility = View.GONE

    }
    private fun noDataFound(visible:Boolean){
        if(visible)
            mBinding.noDataLayout.visibility = View.VISIBLE
        else
            mBinding.noDataLayout.visibility = View.GONE
    }


}