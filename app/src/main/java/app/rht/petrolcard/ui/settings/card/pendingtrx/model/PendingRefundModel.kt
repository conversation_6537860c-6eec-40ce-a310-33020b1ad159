package app.rht.petrolcard.ui.settings.card.pendingtrx.model

import android.util.Log
import android.view.View
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.constant.AppConstant

class PendingRefundModel (
    val transactionModel: TransactionModel?,
    val productModel: ProductModel?,
    val statusMessage:String?
): BaseModel() {
    fun getRefundAmount() :String {
        Log.e("GET REFUND AMOUNT", "preauth amt  = ${transactionModel!!.preAuthAmount!!}  amt = ${transactionModel.amount!!}")
        val refundAmount = transactionModel.preAuthAmount!!.toDouble() - if(transactionModel.amount!=null) transactionModel.amount!!.toDouble() else 0.0

        return  refundAmount.toString()
    }

    fun getProductName() : String {
        if(productModel == null)
            return ""
        else
        return productModel.libelle!!.toString()
    }

    /*fun exportStatus() : Int {
        return if(transactionModel!!.modepay == AppConstant.CARD_VALUE){
            if(transactionModel.transactionRefundExported == 1)
                R.string.exported
            else
                R.string.exported
        } else {
            R.string.sent_to_bank
        }
    }*/
}
