package app.rht.petrolcard.ui.amountselection.activity

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.MotionEvent
import android.view.View
import androidx.databinding.DataBindingUtil
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.databinding.ActivityAmountFillupBinding
import app.rht.petrolcard.ui.common.model.Action
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.iccpayment.activity.CheckCardRestrictionsActivity
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.modepay.activity.BankPaymentProgressActivity
import app.rht.petrolcard.ui.modepay.activity.ModePayActivity
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.product.activity.PumpSelectionActivity
import app.rht.petrolcard.ui.reference.model.FuellingType
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.EnterFuelType
import app.rht.petrolcard.utils.constant.Workflow.FUEL_ENTER_AMOUNT
import app.rht.petrolcard.utils.constant.Workflow.FUEL_ENTER_QTY
import app.rht.petrolcard.utils.constant.Workflow.FUEL_FULL_TANK
import app.rht.petrolcard.utils.constant.Workflow.TAXI_FUEL
import app.rht.petrolcard.utils.tax.TaxUtils
import kotlinx.android.synthetic.main.toolbar.view.*
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.lang.Exception
import java.util.*

@Suppress("DEPRECATION")
class AmountFullTankActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private lateinit var mBinding: ActivityAmountFillupBinding
    private var intentExtrasModel: IntentExtrasModel? = null
    var stationMode=0
    private val TAG = AmountFullTankActivity::class.simpleName

    var fuellingType : FuellingType? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        mBinding = DataBindingUtil.setContentView(this, R.layout.activity_amount_fillup)
        mBinding.model = mViewModel
        mBinding.lifecycleOwner = this
        mBinding.executePendingBindings()
        prefs.mCurrentActivity = TAG
        log(TAG,"CurrentActivity ${prefs.mCurrentActivity}")
        intentExtrasModel = intent.getParcelableExtra(AppConstant.INTENT_EXTRAS_MODEL) as IntentExtrasModel?
        if (intentExtrasModel!!.stationMode != null) {
            if (intentExtrasModel!!.stationMode != null) {
                stationMode = intentExtrasModel!!.stationMode!!
                if(intentExtrasModel!!.loyaltyTrx)
                {
                    stationMode = 1
                }
            }
        }
        log(TAG,"Audit Logs:: "+intentExtrasModel!!.transactionStepLog!!.toJson())
        setupToolbar()

       setupFuellingTypes()
    }

    private fun setupFuellingTypes() {
        try {
            val  terminalModel = prefs.getReferenceModel()!!.terminal
            fuellingType = terminalModel!!.fuellingType!!

            val isAmountEnabled = checkFuelingTypeVisibility(EnterFuelType.AMOUNT)                  //added CU-8676zc29n - Add provision to disable full tank in bank payment
            val isQuantityEnabled = checkFuelingTypeVisibility(EnterFuelType.QUANTITY)
            var isFullTankEnabled = checkFuelingTypeVisibility(EnterFuelType.FULL_TANK)

            if(fuellingType!=null){
                if(/*fuellingType!!.amount!! &&*/ isAmountEnabled)
                    mBinding.layoutAmountPay.visibility = View.VISIBLE
                else
                    mBinding.layoutAmountPay.visibility = View.GONE

                if(/*fuellingType!!.amount!! &&*/ isQuantityEnabled)
                    mBinding.layoutPlainQty.visibility = View.VISIBLE
                else
                    mBinding.layoutPlainQty.visibility = View.GONE

                if(/*fuellingType!!.amount!! &&*/ isFullTankEnabled)
                    mBinding.layoutPlain.visibility = View.VISIBLE
                else
                    mBinding.layoutPlain.visibility = View.GONE

            } else {
                mBinding.layoutAmountPay.visibility = View.VISIBLE
                mBinding.layoutPlainQty.visibility = View.VISIBLE
                mBinding.layoutPlain.visibility = View.VISIBLE
            }

        } catch (e:Exception){
            mBinding.layoutAmountPay.visibility = View.VISIBLE
            mBinding.layoutPlainQty.visibility = View.VISIBLE
            mBinding.layoutPlain.visibility = View.VISIBLE
        }
    }


    private fun checkFuelingTypeVisibility(fuellingType: EnterFuelType) : Boolean {
        val modePaymentModel = intentExtrasModel!!.modePaymentModel
        val isAvailable = if(modePaymentModel != null) {
            when(fuellingType){
                EnterFuelType.AMOUNT -> {
                    modePaymentModel.isEnterAmountEnabled?: true
                }
                EnterFuelType.QUANTITY -> {
                    modePaymentModel.isEnterQtyEnabled?: true
                }
                EnterFuelType.FULL_TANK -> {
                    modePaymentModel.isEnterFullTankEnabled?: if(BuildConfig.DEBUG) false else true
                }
            }
        } else {
            true
        }
        return isAvailable
    }

    private fun setupToolbar()
    {
        mBinding.toolbarAmountFillup.toolbar.tvTitle.text = resources.getString(R.string.choose_fuel_type)
        mBinding.toolbarAmountFillup.toolbar.setNavigationOnClickListener {
            mBinding.toolbarAmountFillup.toolbar.isEnabled = false
            gotoAbortMessageActivity(getString(R.string.transaction_cancelled),getString(R.string.transaction_cancel))
        }
    }

    fun onClick(view: View) {
        var i3: Intent
        setBeep()
        when (view.id) {
            R.id.layout_amount_pay -> {
                intentExtrasModel!!.workFlowMode= FUEL_ENTER_AMOUNT
                i3 = Intent(this, EnterAmountActivity::class.java)
                intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Enter Amount"))
                intentExtrasModel!!.workFlowTransaction = TAXI_FUEL
                i3.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(i3)
                finish()
            }
            R.id.layout_plain_qty -> {
                intentExtrasModel!!.workFlowMode= FUEL_ENTER_QTY
                intentExtrasModel!!.isQtySelected = true
                gotoNextStep()
            }
            R.id.layout_plain -> {
                intentExtrasModel!!.workFlowMode=FUEL_FULL_TANK
                gotoNextStep()
            }
            else ->
            {
                i3 = Intent(this, MenuActivity::class.java)
                i3.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
                startActivity(i3)
                finish()
            }

        }

    }

   /* private fun fullTankSelection() : Intent {
        intentExtrasModel!!.isFullTankSelected = true
        intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Full Tank"))
        intentExtrasModel!!.amount =  prefs.getReferenceModel()!!.terminal!!.maxRefillAmount!!

       val i3 = if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE){
           intentExtrasModel!!.amount =  intentExtrasModel!!.preset_amount
           insertTransactionDetails()
            when (intentExtrasModel!!.typePay) {
                AppConstant.CARD_VALUE -> Intent(this, CheckCardRestrictionsActivity::class.java)
                AppConstant.VISA_VALUE -> {

                    Intent(this, BankPaymentProgressActivity::class.java)
                }
                else -> Intent(this, PumpSelectionActivity::class.java)
            }
        } else {
            Intent(this, ModePayActivity::class.java)
        }

        return i3
    }*/
   private fun gotoNextStep()  {
       intentExtrasModel!!.isFullTankSelected = true
       intentExtrasModel!!.transactionStepLog!!.actions!!.add(Action(action = "TRX${prefs.logReferenceNo} - Selected Full Tank"))
       intentExtrasModel!!.amount =  prefs.getReferenceModel()!!.terminal!!.maxRefillAmount!!

       if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE){
           intentExtrasModel!!.amount =  intentExtrasModel!!.preset_amount
           insertTransactionDetails()
       } else {
          val i3= Intent(this, ModePayActivity::class.java)
           i3.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
           startActivity(i3)
           finish()
       }
   }
    private fun insertTransactionDetails() {
        try {
            val mTransactionDAO = TransactionDao()
            mTransactionDAO.open()
            if(intentExtrasModel!!.mTransaction == null)
            {
                intentExtrasModel!!.mTransaction = TransactionModel()
            }
            if(intentExtrasModel!!.loyaltyTrx)
            {
                intentExtrasModel!!.mTransaction!!.modepay = AppConstant.LOYALTY_VALUE
            }
            if(intentExtrasModel!!.taxModel != null)
            {
                intentExtrasModel!!.mTransaction!!.vatAmount = intentExtrasModel!!.taxModel!!.taxAmount.toString()
                intentExtrasModel!!.mTransaction!!.netAmount = intentExtrasModel!!.taxModel!!.netAmount.toString()
                intentExtrasModel!!.mTransaction!!.amount =  intentExtrasModel!!.taxModel!!.totalAmount
                intentExtrasModel!!.mTransaction!!.vatPercentage =  fuelVat.percentage
                intentExtrasModel!!.mTransaction!!.vatType =  fuelVat.type
            }
            intentExtrasModel!!.mTransaction!!.categoryId =intentExtrasModel!!.categoryId
            intentExtrasModel!!.mTransaction!!.idTerminal = prefs.getReferenceModel()!!.terminal!!.terminalId
            intentExtrasModel!!.mTransaction!!.idTypeTransaction = 1 // =1 trx ; =2 ann trx ; =3 recharge ; =4 ann recharge
            if(intentExtrasModel!!.selectedProduct != null)
            {
                intentExtrasModel!!.mTransaction!!.idProduit = intentExtrasModel!!.selectedProduct!!.productID
                intentExtrasModel!!.mTransaction!!.productName = intentExtrasModel!!.selectedProduct!!.libelle
                intentExtrasModel!!.mTransaction!!.fccProductId = intentExtrasModel!!.selectedProduct!!.fcc_prod_id.toString()

            }
            intentExtrasModel!!.mTransaction!!.codePompiste = intentExtrasModel!!.mPinNumberAttendant
            intentExtrasModel!!.mTransaction!!.idPompiste = prefs.getPompisteId(intentExtrasModel!!.mTransaction!!.codePompiste.toString())
            intentExtrasModel!!.mTransaction!!.dateTransaction = Support.dateToString(Date())
            intentExtrasModel!!.mTransaction!!.amount =    intentExtrasModel!!.amount!!.toDouble()
            if (intentExtrasModel!!.selectedPrice != null && intentExtrasModel!!.selectedPrice!!.unitPrice > 0)
            {
                intentExtrasModel!!.volume = (intentExtrasModel!!.amount!!.toDouble() / intentExtrasModel!!.selectedPrice!!.unitPrice).toString()

            }
            if(intentExtrasModel!!.volume != null)
            {
                intentExtrasModel!!.mTransaction!!.quantite =intentExtrasModel!!.volume!!.toDouble()
            }
            if(intentExtrasModel!!.selectedPrice != null)
            {
                intentExtrasModel!!.mTransaction!!.unitPrice =intentExtrasModel!!.selectedPrice!!.unitPrice
            }
            intentExtrasModel!!.mTransaction!!.flagController = 1
            intentExtrasModel!!.mTransaction!!.kilometrage = ""
            intentExtrasModel!!.mTransaction!!.pan = intentExtrasModel!!.panNumber
            intentExtrasModel!!.mTransaction!!.flagTelecollecte = 0
            intentExtrasModel!!.mTransaction!!.pumpId = intentExtrasModel!!.mPumpNumber
            intentExtrasModel!!.mTransaction!!.transactionStatus = 0
            intentExtrasModel!!.mTransaction!!.sequenceController = ""
            if(intentExtrasModel!!.mTransaction!!.reference.isNullOrEmpty())
            {
                intentExtrasModel!!.mTransaction!!.reference = "TRX" + Support.generateReference(this)
            }
            intentExtrasModel!!.refrenceID = intentExtrasModel!!.mTransaction!!.reference
            insertTransactionData(intentExtrasModel!!.mTransaction!!)
            log(TAG, "intentExtrasModel!!.mTransaction!!.!!! --> " + gson.toJson(intentExtrasModel!!.mTransaction!!))
            prefs.isTransactionCreated = true
            log(TAG, "isTransactionCreated :: true")
            mTransactionDAO.close()
            val i3 = when (intentExtrasModel!!.typePay) {
                AppConstant.CARD_VALUE ->
                    Intent(this, CheckCardRestrictionsActivity::class.java)
                AppConstant.VISA_VALUE -> {
                    Intent(this, BankPaymentProgressActivity::class.java)
                }
                else -> Intent(this, PumpSelectionActivity::class.java)
            }
            i3.putExtra(AppConstant.INTENT_EXTRAS_MODEL, intentExtrasModel)
            startActivity(i3)
            finish()
        } catch (ex: SQLiteException) {
            ex.printStackTrace()
            log(TAG, ex.message+ ExceptionUtils.getStackTrace(ex))
        }
    }
    override fun onBackPressed() {

    }
    override fun setObserver() {}

    override fun onResume() {
        super.onResume()
    }
}
