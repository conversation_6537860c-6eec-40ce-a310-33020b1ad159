package app.rht.petrolcard.ui.loyalty.model
import com.google.gson.annotations.SerializedName

import androidx.annotation.Keep
@Keep
data class LoyaltyProduct(
    @SerializedName("alert_quantity")
    var alertQuantity: String?,
    @SerializedName("barcode_type")
    var barcodeType: String?,
    @SerializedName("brand_id")
    var brandId: Int?,
    @SerializedName("business_id")
    var businessId: Int?,
    @SerializedName("category_id")
    var categoryId: Int?,
    @SerializedName("created_at")
    var createdAt: String?,
    @SerializedName("created_by")
    var createdBy: Int?,
    @SerializedName("enable_sr_no")
    var enableSrNo: Int?,
    @SerializedName("enable_stock")
    var enableStock: Int?,
    @SerializedName("expiry_period")
    var expiryPeriod: Any?,
    @SerializedName("expiry_period_type")
    var expiryPeriodType: Any?,
    @SerializedName("id")
    var id: Int?,
    @SerializedName("image")
    var image: String?,
    @SerializedName("is_inactive")
    var isInactive: Int?,
    @SerializedName("name")
    var name: String?,
    @SerializedName("not_for_selling")
    var notForSelling: Int?,
    @SerializedName("product_custom_field1")
    var productCustomField1: String?,
    @SerializedName("product_custom_field2")
    var productCustomField2: Any?,
    @SerializedName("product_custom_field3")
    var productCustomField3: Any?,
    @SerializedName("product_custom_field4")
    var productCustomField4: Any?,
    @SerializedName("product_description")
    var productDescription: String?,
    @SerializedName("sku")
    var sku: String?,
    @SerializedName("sub_category_id")
    var subCategoryId: Any?,
    @SerializedName("sub_unit_ids")
    var subUnitIds: Any?,
    @SerializedName("tax")
    var tax: Int?,
    @SerializedName("tax_type")
    var taxType: String?,
    @SerializedName("type")
    var type: String?,
    @SerializedName("unit_id")
    var unitId: Int?,
    @SerializedName("updated_at")
    var updatedAt: Any?,
    @SerializedName("warranty_id")
    var warrantyId: Any?,
    @SerializedName("weight")
    var weight: Any?
)