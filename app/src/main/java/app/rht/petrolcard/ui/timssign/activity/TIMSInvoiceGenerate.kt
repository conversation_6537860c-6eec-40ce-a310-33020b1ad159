package app.rht.petrolcard.ui.timssign.activity

import android.content.Context
import android.os.Handler
import android.os.Looper
import android.util.Log
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.reference.model.VatModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.constant.TIMS_METHODS
import app.rht.petrolcard.utils.tims.CloseReceiptRes
import app.rht.petrolcard.utils.tims.OptionReceiptFormat
import com.google.gson.Gson
import org.apache.commons.lang3.exception.ExceptionUtils
import java.util.*

class TIMSInvoiceGenerate(val context: Context,val checkStatus:Boolean = false):BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private val TAG = TIMSInvoiceGenerate::class.simpleName
    var traderSystemInvoiceNumber = ""
    var cuNumber = ""
    var cuPin = ""
    var invoiceReceiptListner: InvoiceReceiptListner? = null
    var openReceiptStatus = true
    var fiscalPrinterModel: FiscalPrinterModel? = null
    var request: TransactionFromFcc = TransactionFromFcc()
    var vatModel: VatModel = VatModel()
    var mTransaction: TransactionModel? = null

    interface InvoiceReceiptListner {
        fun onSuccess(model: CloseReceiptRes?, mTransaction: TransactionModel?)
        fun onFailed(message: String,method: String)
    }

    fun startTIMSServer(
        sfiscalPrinterModel: FiscalPrinterModel,
        srequest: TransactionFromFcc = TransactionFromFcc(),
        svatModel: VatModel = VatModel(),
        smTransaction: TransactionModel? = null
    ) {
        fiscalPrinterModel = sfiscalPrinterModel
        request = srequest
        vatModel = svatModel
        mTransaction = smTransaction
        MainApp.fp!!.serverAddress = fiscalPrinterModel!!.TIMS_API_URL
        Thread {
            try {
                MainApp.fp!!.ApplyClientLibraryDefinitions()
                MainApp.fp!!.ServerCloseDeviceConnection()
                fiscalDeviceSetting()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))

            }
        }.start()
    }
    fun startTIMSServer() {
        MainApp.fp!!.serverAddress = fiscalPrinterModel!!.TIMS_API_URL
        Thread {
            try {
                MainApp.fp!!.ApplyClientLibraryDefinitions()
                fiscalDeviceSetting()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
        }.start()
    }

    fun fiscalDeviceSetting() {
        val thread = Thread {
            try {
                MainApp.fp!!.ServerSetDeviceTcpSettings(
                    fiscalPrinterModel!!.IPESD,
                    fiscalPrinterModel!!.PORTESD,
                    fiscalPrinterModel!!.printerPassword
                )
                prefs.isTimsStarted =true
                Log.i(TAG, "Settings Success")
                readPrinterStatus()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!, TIMS_METHODS.fiscalDeviceSetting)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.fiscalDeviceSetting)
                }
            }
        }
        thread.start()
    }

    fun readPrinterStatus() {
        val thread = Thread {
            try {
                val status =  MainApp.fp!!.ReadStatus()
                openReceiptStatus = status.Opened_Fiscal_Receipt
                val gson = Gson()
                val jsonInString = gson.toJson(status)
                if(!checkStatus) {
                    readSerialNumber()
                }
                else
                {
                    invoiceReceiptListner!!.onSuccess(null,null)
                }
                Log.i(TAG, "readStatus:: $jsonInString")
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.readPrinterStatus)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.readPrinterStatus)
                }
            }
        }
        thread.start()
    }
    fun readPrinterStatus( sfiscalPrinterModel: FiscalPrinterModel,
                           srequest: TransactionFromFcc = TransactionFromFcc(),
                           svatModel: VatModel = VatModel(),
                           smTransaction: TransactionModel? = null) {
        val thread = Thread {
            try {
                fiscalPrinterModel = sfiscalPrinterModel
                request = srequest
                vatModel = svatModel
                mTransaction = smTransaction
                val status = MainApp.fp!!.ReadStatus()
                openReceiptStatus = status.Opened_Fiscal_Receipt
                val gson = Gson()
                val jsonInString = gson.toJson(status)
                readSerialNumber()
                Log.i(TAG, "readStatus:: $jsonInString")
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    if((e.message!!.contains("ServerAddressNotSet") || e.message!!.contains("ErrCode: 100")))
                    {
                        startTIMSServer()
                    }
                    else
                    {
                        MainApp.fp!!.ServerCloseDeviceConnection()
                        invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.readPrinterStatus)
                    }

                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.readPrinterStatus)
                }
            }
        }
        thread.start()
    }

    private fun readSerialNumber() {
        val thread = Thread {
            try {
                val status =  MainApp.fp!!.ReadCUnumbers()
                cuNumber = status.serialNumber
                cuPin = status.piNnumber
                val gson = Gson()
                val jsonInString = gson.toJson(status)
                Log.i(TAG, "ReadCUnumbers:: $jsonInString")
                if (mTransaction != null && !mTransaction!!.timsSignDetails!!.customer_details!!.customerPin.isNullOrEmpty()) {
                    openReceiptWithCustomerData()
                } else {
                    openReceiptInvoice()
                }

            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.readSerialNumber)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.readSerialNumber)
                }
            }
        }
        thread.start()
    }

    private fun openReceiptInvoice() {
        val thread = Thread {
            try {
                var saleId = ""
                if(mTransaction != null && mTransaction!!.fccSaleId != null)
                {
                    saleId = mTransaction!!.fccSaleId!!
                }
                else if( request.fusionSaleId != null)
                {
                    saleId =request.fusionSaleId!!
                }
                traderSystemInvoiceNumber = Support.generateInvoiceNumber(saleId)


                if (!openReceiptStatus) {
                    MainApp.fp!!.OpenReceipt(OptionReceiptFormat.Brief, traderSystemInvoiceNumber)
                }
                Log.i(TAG, "OpenReceipt:: Success")
                if (mTransaction == null) {
                    createServiceInvoice()
                } else {
                    createTicketInvoice()
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                if (mTransaction == null) {
                    createServiceInvoice()
                } else {
                    createTicketInvoice()
                }
            }
        }
        thread.start()
    }

    private fun openReceiptWithCustomerData() {
        val thread = Thread {
            try {
                Log.i(TAG, "mTransaction openReceiptWithCustomerData:: " + Gson().toJson(mTransaction!!))
                    MainApp.fp!!.OpenInvoiceWithFreeCustomerData(
                        mTransaction!!.timsSignDetails!!.customer_details!!.companyName,
                        mTransaction!!.timsSignDetails!!.customer_details!!.customerPin,
                        mTransaction!!.timsSignDetails!!.customer_details!!.headQuarters,
                        mTransaction!!.timsSignDetails!!.customer_details!!.address,
                        mTransaction!!.timsSignDetails!!.customer_details!!.postalCode,
                        mTransaction!!.timsSignDetails!!.customer_details!!.exemptionNumber,
                        traderSystemInvoiceNumber
                    )
                 Log.i(TAG, "OpenReceipt:: Success")
                createTicketInvoice()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                //fp!!.ServerCloseDeviceConnection()
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.openReceiptWithCustomerData)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.openReceiptWithCustomerData)
                }
                //createInvoice()
            }
        }
        thread.start()
    }

    private fun createServiceInvoice() {
        val thread = Thread {
            try {
                Log.i(TAG, "request.productName:: " + request.produit)
                Log.i(TAG, "request.unitPrice:: " + request.pu)
                Log.i(TAG, "request.hsCode:: " + request.hsCode)
                Log.i(TAG, "request.volume:: " + request.quantite)
                var discountPercentage = 0.0
                if(mTransaction != null && mTransaction!!.isDiscountTransaction == 1 && !mTransaction!!.discountPercentage.isNullOrEmpty())
                {
                    discountPercentage = mTransaction!!.discountPercentage!!.toDouble()
                }
                MainApp.fp!!.SellPLUfromExtDB_HS(
                    request.produit,
                    request.pu,
                    request.hsCode,
                    request.quantite,
                    discountPercentage
                )
                Log.i(TAG, "SellPLUfromExtDB:: Success")
                closeReceipt()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.createServiceInvoice)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.createServiceInvoice)
                }
            }
        }
        thread.start()
    }

    private fun createTicketInvoice() {
        val thread = Thread {
            try {
                Log.i(TAG, "mTransaction.productName:: " +  mTransaction!!.productName)
                Log.i(TAG, "mTransaction.unitPrice:: " + mTransaction!!.unitPrice)
                Log.i(TAG, "mTransaction.fuelQtyUnit:: " +  mTransaction!!.timsSignDetails!!.fuelQtyUnit)
                Log.i(TAG, "mTransaction.hsCode:: " +  mTransaction!!.timsSignDetails!!.hsCode)
                Log.i(TAG, "mTransaction.productName:: " +  mTransaction!!.productName)
                Log.i(TAG, "mTransaction.vatAmount:: " + mTransaction!!.vatAmount!!)
                Log.i(TAG, "mTransaction.volume:: " + mTransaction!!.quantite!!)

                var discountPercentage = 0.0
                if(mTransaction != null && mTransaction!!.isDiscountTransaction == 1 && !mTransaction!!.discountPercentage.isNullOrEmpty())
                {
                    discountPercentage = mTransaction!!.discountPercentage!!.toDouble()
                }

                MainApp.fp!!.SellPLUfromExtDB_HS(
                    mTransaction!!.productName,
                    mTransaction!!.unitPrice,
                    mTransaction!!.timsSignDetails!!.hsCode,
                    mTransaction!!.quantite!!,
                    discountPercentage
                )
                Log.i(TAG, "SellPLUfromExtDB:: Success")
                closeReceipt()
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.createTicketInvoice)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.createTicketInvoice)
                }
            }
        }
        thread.start()
    }

    private fun closeReceipt() {
        val thread = Thread {
            try {
                val invResponse =  MainApp.fp!!.CloseReceipt()
                val res = Gson().toJson(invResponse)
                Log.i(TAG, "CloseReceipt:: $res")
                readDateTime(invResponse)
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.closeReceipt)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.closeReceipt)
                }
            }
        }
        thread.start()
    }

    private fun readDateTime(closeReceiptRes: CloseReceiptRes) {
        val thread = Thread {
            try {
                val dateTime =  MainApp.fp!!.ReadDateTime()
                Log.i(TAG, "ReadDateTime:: $dateTime")
                val dateFormat = "yyyy-MM-dd HH:mm"
                val formatedDate = Support.dateToStringFormat(dateTime, dateFormat)
                //fp!!.ServerCloseDeviceConnection()
                val lastReceiptNumber =  MainApp.fp!!.ReadLastAndTotalReceiptNum()
                if (mTransaction == null) {
                    insertTransactionData(closeReceiptRes, formatedDate!!,lastReceiptNumber.LastReceiptNum!!.toString())
                } else {
                    updateTransactionData(closeReceiptRes, formatedDate!!,lastReceiptNumber.LastReceiptNum!!.toString())
                }
            } catch (e: java.lang.Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                val dateTime = Support.dateToString(Date())
                if (mTransaction == null) {
                    insertTransactionData(closeReceiptRes, dateTime!!,"")
                } else {

                    updateTransactionData(closeReceiptRes, dateTime!!,"")
                }
            }
        }
        thread.start()

    }

    fun updateTransactionData(invResponse: CloseReceiptRes, dateTime: String,lastReceiptNo:String) {
        Handler(Looper.getMainLooper()).post {
            try {
                mTransaction!!.dateTransaction = dateTime
                mTransaction!!.transactionStatus = 1
                val transactionDao = TransactionDao()
                transactionDao.open()
                    mTransaction!!.timsSignDetails!!.invoice_details!!.dateTime = dateTime
                    mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber = invResponse.invoiceNum
                    mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode = invResponse.qRcode
                    mTransaction!!.timsSignDetails!!.invoice_details!!.timsInvoiceStatus = 1
                    mTransaction!!.timsSignDetails!!.traderSystemInvoiceNumber = traderSystemInvoiceNumber
                    mTransaction!!.timsSignDetails!!.controlUnitSerialNumber = cuNumber
                    mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo = lastReceiptNo
                    transactionDao.updateTransactionsByReferenceID(mTransaction!!)
                        if (request.id != null) {
                            request.id!!
                        }
                        invoiceReceiptListner!!.onSuccess(invResponse, mTransaction)
                val trx = Gson().toJson(invResponse)
                Log.i(TAG, "Signed transactionModel: $trx")
            } catch (e: Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.updateTransactionData)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.updateTransactionData)
                }
            }
        }
    }

    fun insertTransactionData(invResponse: CloseReceiptRes, dateTime: String,lastReceiptNo:String) {
        Handler(Looper.getMainLooper()).post {
            try {
                if (mTransaction == null) {
                    mTransaction = TransactionModel()
                }
                mTransaction!!.reference = "TRX" + Support.generateReference(MainApp.appContext,true)
                mTransaction!!.dateTransaction = dateTime
                mTransaction!!.timsSignDetails!!.invoice_details!!.dateTime = dateTime
                mTransaction!!.timsSignDetails!!.fuelQtyUnit = request.fuelQtyUnit
                mTransaction!!.timsSignDetails!!.hsCode = request.hsCode
                mTransaction!!.sequenceController = request.ref_transaction
                mTransaction!!.pumpId = request.pump.toString()
                mTransaction!!.idProduit = request.fccProductId!!.toInt()
                mTransaction!!.amount = request.amount
                mTransaction!!.quantite = request.quantite
                mTransaction!!.unitPrice = request.pu
                mTransaction!!.productName = request.produit
                mTransaction!!.timsSignDetails!!.controlUnitSerialNumber = cuNumber
                mTransaction!!.timsSignDetails!!.traderSystemInvoiceNumber = traderSystemInvoiceNumber
                mTransaction!!.vatAmount = request.vatAmount
                mTransaction!!.vatPercentage = vatModel.percentage
                mTransaction!!.vatType = vatModel.type
                mTransaction!!.categoryId = PRODUCT.FUEL_CATEGORY_ID
                mTransaction!!.timsSignDetails!!.invoice_details!!.timsInvoiceStatus = 1
                mTransaction!!.timsSignDetails!!.invoice_details!!.controlUnitInvoiceNumber = invResponse.invoiceNum
                mTransaction!!.timsSignDetails!!.invoice_details!!.invoiceQrCode = invResponse.qRcode
                mTransaction!!.transactionStatus = 1
                mTransaction!!.fccSaleId = request.fusionSaleId
                mTransaction!!.timsSignDetails!!.invoice_details!!.receiptNo = lastReceiptNo
                mTransaction!!.modepay = AppConstant.CASH_VALUE
                val transactionDao = TransactionDao()
                transactionDao.open()
                transactionDao.insertTransaction(mTransaction!!)
                Log.i(TAG, "request.id: ${request.id!!}")
                Log.i(TAG, "invoiceReceiptListner: $invoiceReceiptListner")
                invoiceReceiptListner!!.onSuccess(invResponse, mTransaction)
                Gson().toJson(invResponse)
                Log.i(TAG, "Background Signed transactionModel: $mTransaction")
            } catch (e: Exception) {
                e.printStackTrace()
                log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                if (e.message != null) {
                    invoiceReceiptListner!!.onFailed(e.message!!,TIMS_METHODS.insertTransactionData)
                } else {
                    invoiceReceiptListner!!.onFailed("",TIMS_METHODS.insertTransactionData)
                }

            }
        }
    }

    override fun setObserver() {
    }
}