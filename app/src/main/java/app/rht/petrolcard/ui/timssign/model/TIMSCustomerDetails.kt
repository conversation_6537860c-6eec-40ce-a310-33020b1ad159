package app.rht.petrolcard.ui.timssign.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep

@Keep
class TIMSCustomerDetails (
    var customerPin :String? = null,
    var exemptionNumber :String? = null,
    var companyName :String? = null,
    var headQuarters :String? = null,
    var address :String? = null,
    var postalCode :String? = null,
    var city :String? = null,
    ) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString(),
        parcel.readString()
    )

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(customerPin)
        parcel.writeString(exemptionNumber)
        parcel.writeString(companyName)
        parcel.writeString(headQuarters)
        parcel.writeString(address)
        parcel.writeString(postalCode)
        parcel.writeString(city)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TIMSCustomerDetails> {
        override fun createFromParcel(parcel: Parcel): TIMSCustomerDetails {
            return TIMSCustomerDetails(parcel)
        }

        override fun newArray(size: Int): Array<TIMSCustomerDetails?> {
            return arrayOfNulls(size)
        }
    }

}
