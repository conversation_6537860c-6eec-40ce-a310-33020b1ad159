package app.rht.petrolcard.ui.modepay.viewmodel

import android.util.Base64
import androidx.lifecycle.MutableLiveData
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.NetworkRequestEndPoints
import app.rht.petrolcard.ui.modepay.model.MtnPayAccessTokenResponse
import app.rht.petrolcard.ui.modepay.model.MtnRequestToPayBody
import app.rht.petrolcard.ui.modepay.model.MtnResponseModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.AppPreferencesHelper


class ModePayViewmodel constructor(
    private val mNetworkService: ApiService,
    private val preferencesHelper: AppPreferencesHelper
) : CommonViewModel(mNetworkService,preferencesHelper) {

    var mtnAccessTokenObserver = MutableLiveData<MtnPayAccessTokenResponse>()
    var mtnReferenceIdObserver = MutableLiveData<String>()
    var mtngetRequestStatusObserver = MutableLiveData<MtnResponseModel>()
    var mtnRequestToPayObserver = MutableLiveData<String>()
    var mtnPayBaseURL = ""
    var authorizationKey = ""
    var subscriptionKey = ""
    var mtnReferenceIDURL = ""
    fun getMtnCredentials()
    {
     mtnPayBaseURL =preferencesHelper.getMtnPayCredentials()!!.api_url
     authorizationKey = preferencesHelper.getMtnPayCredentials()!!.api_user+":"+preferencesHelper.getMtnPayCredentials()!!.api_key
     subscriptionKey = preferencesHelper.getMtnPayCredentials()!!.subscription_key
      mtnReferenceIDURL = preferencesHelper.getMtnPayCredentials()!!.reference_id_url
    }
    fun getMtnPayAccessToken() {
        getMtnCredentials()
        val authorizationKey = "Basic "+Base64.encodeToString(authorizationKey.toByteArray(), Base64.NO_WRAP)
            requestData(mNetworkService.getMtnPayAccessToken(url=mtnPayBaseURL+NetworkRequestEndPoints.MTNPAY_ACCESSTOKEN,authorization= authorizationKey,subscriptionKey= subscriptionKey),
                {
                    mtnAccessTokenObserver.postValue(it)
                }, priority = ApiService.PRIORITY_HIGH
            )
    }
    fun mtnRequestToPay(referenceID:String,token:String,body: MtnRequestToPayBody) {
        getMtnCredentials()
        requestNoResponseData(mNetworkService.mtnRequestToPay(url=mtnPayBaseURL+NetworkRequestEndPoints.MTNPAY_REQUEST_DATA,
            authorization= "Bearer $token",subscriptionKey= subscriptionKey,connection = "close",referenceID =referenceID,
        targetEnvironment = preferencesHelper.getMtnPayCredentials()!!.target_environment,body =body ),
            {
                mtnRequestToPayObserver.postValue("")
            }, priority = ApiService.PRIORITY_HIGH
        )
    }
    fun getMtnReferenceID() {
            requestData(mNetworkService.getMtnReferenceID(url=mtnReferenceIDURL,connection = "close"),
                {
                    mtnReferenceIdObserver.postValue(it)
                }, priority = ApiService.PRIORITY_HIGH
            )
        }

    fun getMTNPayResponseStatus(referenceID: String,token: String) {
        getMtnCredentials()
        requestData(mNetworkService.getMTNPayResponseStatus(
                url=mtnPayBaseURL+NetworkRequestEndPoints.MTNPAY_REQUEST_DATA+"/"+referenceID,
                authorization= "Bearer $token",
                subscriptionKey= subscriptionKey,
                connection = "close",
                targetEnvironment = preferencesHelper.getMtnPayCredentials()!!.target_environment),
                {
                    mtngetRequestStatusObserver.postValue(it)
                }, priority = ApiService.PRIORITY_HIGH
            )
        }



}
