package app.rht.petrolcard.baseClasses.model

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import androidx.databinding.BaseObservable
import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
@Keep
open class BaseModel() : BaseObservable(),Parcelable {

    @SerializedName("status_code")
    @Expose
    var status: String? = null

    @SerializedName("success_msg")
    @Expose
    var msg: String? = null

    var isvalueSelected: Boolean = true

    constructor(parcel: Parcel) : this() {
        status = parcel.readString()
        msg = parcel.readString()
        isvalueSelected = parcel.readByte() != 0.toByte()
    }

   override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(status)
        parcel.writeString(msg)
        parcel.writeByte(if (isvalueSelected) 1 else 0)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<BaseModel> {
        override fun createFromParcel(parcel: Parcel): BaseModel {
            return BaseModel(parcel)
        }

        override fun newArray(size: Int): Array<BaseModel?> {
            return arrayOfNulls(size)
        }
    }
}

