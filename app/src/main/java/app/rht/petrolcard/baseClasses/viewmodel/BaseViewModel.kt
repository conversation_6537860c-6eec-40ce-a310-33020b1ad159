package app.rht.petrolcard.baseClasses.viewmodel

import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.View
import androidx.databinding.ObservableBoolean
import androidx.databinding.ObservableField
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import app.rht.petrolcard.apimodel.apiresponsel.DialogSuccessModel
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.apimodel.apiresponsel.RedirectModel
import app.rht.petrolcard.utils.SingleLiveEvent
import app.rht.petrolcard.utils.StringUtility
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.networkRequest.CallbackWrapper
import app.rht.petrolcard.utils.constant.AppConstant
import okhttp3.Headers
import okhttp3.MediaType
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import rx.Observable
import rx.Subscriber
import rx.Subscription
import rx.android.schedulers.AndroidSchedulers
import rx.schedulers.Schedulers
import java.io.File
import java.nio.charset.Charset


abstract class BaseViewModel : ViewModel() {
    val isRefresh = ObservableBoolean(false)
    val message = ObservableField<String>()
    val progress = ObservableBoolean(false)
    val errorMessage = ObservableField<String>()
    private var subscriber: Subscription? = null
    val actionBarOnBackPressListener = SingleLiveEvent<Boolean>()
    val redirect = SingleLiveEvent<RedirectModel>()
    val showDialog = SingleLiveEvent<DialogSuccessModel>()
    val showSnakBar = SingleLiveEvent<String>()
    val dialogError =  MutableLiveData<String>()
    var logoutResponse = MutableLiveData<Boolean>()
    var noInternetResponse = MutableLiveData<Int>()
    var isNetworkConnected = true
    var forceUpdate: MutableLiveData<String> = MutableLiveData()
    var softUpdate: MutableLiveData<String> = MutableLiveData()
    protected val _mErrorMessage = MutableLiveData<ErrorData>()
    val mErrorMessage: LiveData<ErrorData>
        get() = _mErrorMessage


    fun actionBarOnBackPress(view: View) {
        actionBarOnBackPressListener.postValue(true)
    }

    fun <T> requestData(
        api: Observable<T>,
        success: (m: T) -> Unit,
        failed: (message: String?, statusCode: Int) -> Unit = onFailed,
        onServerNetworkFailed: (
            m: String?, statusCode: Int
        ) -> Unit = onFailed, priority: Int = ApiService.PRIORITY_DEFAULT

    ) {
        progress.set(true)
        api.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .unsubscribeOn(Schedulers.io())
            .subscribe(object : CallbackWrapper<T>() {
                override fun onSuccess(t: T) {
                    Log.i("BaseViewModel:", "requestData:: onSuccess$t")
                    message.set("")
                    errorMessage.set("")
                    isRefresh.set(false)
                    progress.set(false)
                    success(t)


                }

                override fun onError(message: String?, statusCode: Int?) {
                    Log.i("BaseViewModel:", "requestData:: onError$message")
                    isRefresh.set(true)
                    progress.set(false)
                   // failed(message, statusCode!!)
                    when (statusCode) {
                        //401 -> logout(message)
                        404 -> _mErrorMessage.postValue(ErrorData(message, statusCode, priority))
                       // 500 -> failed("Internal Server error", statusCode)
                        -500 -> noInternet(statusCode)
                        -300 -> poorNetwork(statusCode)
                        //-400 -> showSnakBar.postValue("Server is in maintenance mode. it will back to normal mode within some time. please wait.")
                        else -> _mErrorMessage.postValue(ErrorData(message, statusCode!!, priority))
                    }
                }

                private fun logout(message: String?) {


//                   if(!MyConstant.ISLOGOUT)
//                   {
//                    MyConstant.ISLOGOUT = true
//                    showSnakBar.postValue(message)
//                    Handler(Looper.getMainLooper()).postDelayed({
//                        redirect.postValue(
//                            RedirectModel(
//                                mClass = LoginActivity::class.java,
//                                isTopFinish = true,
//                                flagIsTopClearTask = true
//                            )
//                        )
//                    }, 1000)

//                   }

                }

                private fun noInternet(statusCode: Int?) {
                    noInternetResponse.postValue(statusCode)
                    }
                private fun poorNetwork(statusCode: Int?) {
                    noInternetResponse.postValue(statusCode)
                    }


            })
    }

    fun <T> requestNoResponseData(
        api: Observable<T>,
        success: (m: String?) -> Unit,
        failed: (message: String?, statusCode: Int) -> Unit = onFailed,
        onServerNetworkFailed: (
            m: String?, statusCode: Int
        ) -> Unit = onFailed, priority: Int = ApiService.PRIORITY_DEFAULT

    ) {
        progress.set(true)
        api.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .unsubscribeOn(Schedulers.io())
            .subscribe(object : CallbackWrapper<T>() {
                override fun onSuccess(t: T) {
                    Log.i("BaseViewModel:", "requestData:: onSuccess$t")
                    message.set("")
                    errorMessage.set("")
                    isRefresh.set(false)
                    progress.set(false)
                    if(t == null)
                    {
                        success("")
                    }
                }

                override fun onError(message: String?, statusCode: Int?) {
                    Log.i("BaseViewModel:", "requestData:: onError$message")
                    isRefresh.set(true)
                    progress.set(false)
                    when (statusCode) {
                        //401 -> logout(message)
                        404 -> _mErrorMessage.postValue(ErrorData(message, statusCode, priority))
                        // 500 -> failed("Internal Server error", statusCode)
                        -500 -> noInternet(statusCode)
                        -300 -> poorNetwork(statusCode)
                        //-400 -> showSnakBar.postValue("Server is in maintenance mode. it will back to normal mode within some time. please wait.")
                        else -> _mErrorMessage.postValue(ErrorData(message, statusCode!!, priority))
                    }
                }

                private fun logout(message: String?) {


//                   if(!MyConstant.ISLOGOUT)
//                   {
//                    MyConstant.ISLOGOUT = true
//                    showSnakBar.postValue(message)
//                    Handler(Looper.getMainLooper()).postDelayed({
//                        redirect.postValue(
//                            RedirectModel(
//                                mClass = LoginActivity::class.java,
//                                isTopFinish = true,
//                                flagIsTopClearTask = true
//                            )
//                        )
//                    }, 1000)

//                   }

                }

                private fun noInternet(statusCode: Int?) {
                    noInternetResponse.postValue(statusCode)
                }
                private fun poorNetwork(statusCode: Int?) {
                    noInternetResponse.postValue(statusCode)
                }


            })
    }
    fun <T> requestPlaceData(
        api: Observable<T>,
        success: (m: T) -> Unit,
        failed: (m: String?, statusCode: Int) -> Unit = onFailed
    ) {
        progress.set(true)
        subscriber = api.subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread())
            .unsubscribeOn(Schedulers.io())
            .subscribe(object : Subscriber<T>() {
                override fun onError(e: Throwable?) {
                    isRefresh.set(false)
                    progress.set(false)
                    failed(e?.message, 0)
                }

                override fun onNext(t: T) {
                    isRefresh.set(false)
                    progress.set(false)
                    success(t)
                }

                override fun onCompleted() {
                }
            })
    }

    val onFailed: (m: String?, code: Int) -> Unit = { message, statuscode ->
        this.message.set(message)
    }
    fun isForceUpdateNeeded(header: Headers, finalMessage: String?): Boolean? {
        var needForceUpdate: Boolean? = false

        val updateAvailable = header.get("update_available")
        val forceUpdate = header.get("force_update")

        if (updateAvailable != null &&  StringUtility.validateString(updateAvailable) && updateAvailable.equals(
                "true",
                ignoreCase = true
            )
        ) {
            if (StringUtility.validateString(forceUpdate!!) && forceUpdate.equals(
                    "true",
                    ignoreCase = true
                )
            ) {
                //force update
                this.forceUpdate.value = finalMessage
                needForceUpdate = true
            } else {
                //soft update
                this.softUpdate.value = "New update available"
                needForceUpdate = false
            }

        }
        return needForceUpdate
    }
    fun createFormData(content: String) = content.toRequestBody(AppConstant.MIME_TYPE_PLAIN_TEXT.toMediaTypeOrNull())

    fun createFormData(imageFile: File?, name: String, mimeType: String) = when (imageFile) {
        null -> null
        else -> MultipartBody.Part.createFormData(
            name,
            imageFile.name,
            imageFile.asRequestBody(mimeType.toMediaTypeOrNull())
        )
    }

}