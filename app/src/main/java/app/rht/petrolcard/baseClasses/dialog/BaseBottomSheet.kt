package app.rht.petrolcard.baseClasses.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import app.rht.petrolcard.R
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment

abstract class BaseBottomSheet(val mCallBack: OnItemClickLisnter?=null): BottomSheetDialogFragment() {

    interface OnItemClickLisnter{
        fun onItemClick(value:String?)
    }

    protected var mView: View? = null

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        mView = inflater.inflate(getLayoutResourses(), container, false)
        return mView
    }

    abstract fun getLayoutResourses():Int

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dg = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        //dg?.window?.attributes?.windowAnimations = R.style.BottomTransition

        dg.setOnShowListener { dialog ->
            val d = dialog as BottomSheetDialog
            val bottomSheet = d.findViewById(R.id.design_bottom_sheet) as FrameLayout?
            bottomSheet?.setBackgroundResource(0)
            BottomSheetBehavior.from(bottomSheet!!).state = BottomSheetBehavior.STATE_EXPANDED
        }
        return dg
    }

}