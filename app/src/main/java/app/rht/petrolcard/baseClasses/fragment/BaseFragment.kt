package app.rht.petrolcard.baseClasses.fragment

import android.content.Context
import android.content.Intent
import android.graphics.drawable.Drawable
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.format.DateFormat
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.fragment.app.Fragment
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.interfaces.ApiOnResultListener
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.common.*
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.utils.extensions.launch
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBar
import org.koin.android.ext.android.get
import org.koin.androidx.viewmodel.ext.android.getViewModel
import java.text.SimpleDateFormat
import java.util.*
import kotlin.reflect.KClass

abstract class BaseFragment<V : BaseViewModel>(private val viewModelClass: KClass<V>) : Fragment(),
    SwipeRefreshLayout.OnRefreshListener, ApiOnResultListener,
    EndlessRecyclerView.Pager {
    val prefs: AppPreferencesHelper = get()
    var mLoadingNextPage = false
    lateinit var mViewModel: V
    private lateinit var toolBarHelper: ToolBarHelper
    val sdf = SimpleDateFormat("yyyy-MM-dd")
    val sdf1 = SimpleDateFormat("HH:mm")
    abstract fun setObserver()

    protected abstract fun createDataBinding(
        inflater: LayoutInflater,
        container: ViewGroup?
    ): View

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        mViewModel = getViewModel(viewModelClass)
        setObserver()
        observeError()
        setBaseObserver()
        return createDataBinding(inflater, container)
    }
    open fun share(url: String, title: String, content: String) {
        try {
            val i = Intent(Intent.ACTION_SEND)
            i.type = "text/plain"
            i.putExtra(Intent.EXTRA_SUBJECT, title)
            var sAux =
                content
            sAux = sAux + "\n\n" + url
            i.putExtra(Intent.EXTRA_TEXT, sAux)
            startActivity(
                Intent.createChooser(
                    i,
                    "Choose One"
                )
            )
        } catch (e: Exception) {
            //  Toast.makeText(this, e.message, Toast.LENGTH_SHORT).show()
        }
    }

    fun initAllUi(activity: BaseActivity<*>) {
        toolBarHelper = ToolBarHelper(activity)
        toolBarHelper.init()

    }
    fun setRightActionListener(listener: RightActionToolBarListener?) {
        toolBarHelper.setRightActionListener(listener)
    }
    fun setRightActionOnToolBar(visibility: Int, drawable: Drawable?) {
        toolBarHelper.setRightActionOnToolBar(visibility, drawable)

    }
    fun setSearchActionListener(listener: SearchActionToolBarListener?) {
        toolBarHelper.setSearchActionListener(listener)
    }
    fun setRightActionQueryChangeListener(listener: SearchActionToolBarQueryChangeListener?) {
        toolBarHelper.setSearchActionChangeListner(listener)
    }

    fun clearSearchText() {
        toolBarHelper.clearSearchText()
    }

    override fun onRefresh() {
    }

    open fun callApi() {
        Log.d("MatchBaseFragment", "callApi BaseFragment ")
    }

    fun showToast(msg: String? = "") {
        Handler(Looper.getMainLooper()).post{
            activity?.let {
                Toast.makeText(activity, "" + msg, Toast.LENGTH_SHORT).show()
            }
        }
    }



    override fun shouldLoad(): Boolean {
        return mLoadingNextPage
    }

    override fun onScrolled(recyclerView: RecyclerView?, dx: Int, dy: Int) {

    }
    fun setBaseObserver() {
        mViewModel.let { viewModel ->

            viewModel.actionBarOnBackPressListener.observe(this, Observer {
                if (it) {
                    requireActivity().onBackPressed()
                    requireActivity().finish()
                }
            })
            viewModel.redirect.observe(this, Observer {
                it?.let {
                    launch(
                        it.mClass,
                        arguments = it.arguments,
                        isTopFinish = it.isTopFinish,
                        flagIsTopClearTask = it.flagIsTopClearTask
                    )
                }
            })
            viewModel.showDialog.observe(this, Observer {
                it?.let {
                    requireActivity().showDialog(
                        title = it.title,
                        message = it.message,
                        onPressOk = View.OnClickListener { v ->
                            if (it.goToBackScreen) {
                                requireActivity().onBackPressed()
                            }
                            it.mClass?.let { mClass ->
                                launch(
                                    mClass,
                                    arguments = it.arguments,
                                    isTopFinish = it.isTopFinish,
                                    flagIsTopClearTask = it.flagIsTopClearTask
                                )
                            }
                        }
                    )
                }
            })

            viewModel.showSnakBar.observe(this, Observer {
                it?.let {
                    requireActivity().showSnakeBar(it)
                }
            })
        }
    }

    private fun observeError() {
        mViewModel.let { viewModel ->
            viewModel.mErrorMessage.observe(requireActivity(), Observer {
                try {


                    Log.d("observeError", "observeError " + it.statusCode)
                    onError(it)
                    when (it.statusCode) {
                        0 -> {
                            requireActivity().showSnakeBar(it.message)
                        }

                        200 -> {
                            if (it.priority == ApiService.PRIORITY_HIGH) {
                                viewModel.errorMessage.set(it.message)
                                onError(it)
                            } else {
                                requireActivity().showSnakeBar(it.message)
                            }
                        }
                        else -> {
                            if (it.priority == ApiService.PRIORITY_HIGH) {
                                viewModel.errorMessage.set(it.message)
                                onError(it)
                            } else {

                                requireActivity().showSnakeBar(it.message)
                            }
                        }
                    }
                } catch (e: java.lang.Exception) {
                    e.printStackTrace()
                }

            })
        }
    }

    override fun loadNextPage() {
    }
    fun removeCurrentFragment() {
        activity?.supportFragmentManager?.beginTransaction()?.
        remove(this)?.commit()
    }
    fun getRootViewId() = android.R.id.content

    fun getDate(timestamp: Long) :String {
        val calendar = Calendar.getInstance(Locale.ENGLISH)
        calendar.timeInMillis = timestamp * 1000L
        val date = DateFormat.format("yyy-MM-dd", calendar).toString()
        return date
    }
    fun getTime(timestamp: Long) :String {
        val calendar = Calendar.getInstance(Locale.ENGLISH)
        calendar.timeInMillis = timestamp * 1000L
        val date = DateFormat.format("HH:mm", calendar).toString()
        return date
    }

    fun isNetworkConnected():Boolean
    {
        val connectivityManager: ConnectivityManager = requireContext().getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val capabilities =  connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        capabilities.also {
            if (it != null){
                if (it.hasTransport(NetworkCapabilities.TRANSPORT_WIFI))
                    return true
                else if (it.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)){
                    return true
                }
            }
        }
        return false
    }

}
