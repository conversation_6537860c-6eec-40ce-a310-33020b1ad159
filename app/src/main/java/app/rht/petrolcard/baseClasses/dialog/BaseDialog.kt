package app.rht.petrolcard.baseClasses.dialog

import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.util.Log
import android.view.*
import android.widget.FrameLayout
import android.widget.TextView
import androidx.appcompat.view.ContextThemeWrapper
import androidx.fragment.app.DialogFragment

import androidx.lifecycle.Observer
import app.rht.petrolcard.R
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.utils.extensions.launch
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.interfaces.ApiOnResultListener
import app.rht.petrolcard.utils.LogWriter
import com.google.android.material.snackbar.Snackbar
import org.koin.android.ext.android.get
import org.koin.androidx.viewmodel.ext.android.getViewModel
import java.text.SimpleDateFormat
import java.util.*
import kotlin.reflect.KClass

abstract class BaseDialog <V : BaseViewModel>(private val viewModelClass: KClass<V>) : DialogFragment(),
    ApiOnResultListener {
    val prefs: AppPreferencesHelper = get()
    lateinit var mViewModel: V


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        mViewModel = getViewModel(viewModelClass)
        setObserver()
        setBaseObserver()
        observeError()
    }
    open fun log(tag: String?, msg: String) {
        Log.i(tag, msg)
        var date = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault()).format(Calendar.getInstance().time)
        date = if(!prefs.logReferenceNo.isNullOrEmpty()) {
            prefs.logReferenceNo
        } else {
            "${date}_${prefs.logCount}"
        }
        val logWriter = LogWriter(date.toString())
        logWriter.appendLog(tag, msg)
    }
    fun setBaseObserver() {
        mViewModel.let { viewModel ->

            viewModel.actionBarOnBackPressListener.observe(this, Observer {
                if (it) {
                    requireActivity().onBackPressed()
                }
            })

            viewModel.redirect.observe(this, Observer {
                it?.let {
                    launch(
                        it.mClass,
                        arguments = it.arguments,
                        isTopFinish = it.isTopFinish,
                        flagIsTopClearTask = it.flagIsTopClearTask
                    )
                }
            })
            viewModel.showDialog.observe(this, Observer {
                it?.let {
                    requireActivity().showDialog(
                        title = it.title,
                        message = it.message,
                        onPressOk = View.OnClickListener { v ->
                            if (it.goToBackScreen) {
                                requireActivity().onBackPressed()
                            }
                            it.mClass?.let { mClass ->
                                launch(
                                    mClass,
                                    arguments = it.arguments,
                                    isTopFinish = it.isTopFinish,
                                    flagIsTopClearTask = it.flagIsTopClearTask
                                )
                            }
                        }
                    )
                }
            })

            viewModel.showSnakBar.observe(this, Observer {
                it?.let {
                    requireActivity().showSnakeBar(it)
                }
            })
        }
    }
    override fun onError(errorData: ErrorData) {

    }

    abstract fun setObserver()
    private fun observeError() {
        mViewModel.let { viewModel ->
            viewModel.mErrorMessage.observe(this, Observer {
                Log.d("observeError", "observeError " + it.statusCode)
                onError(it)
                when (it.statusCode) {
                    0 -> {
                        showSnackBar(requireView().rootView,it.message!!)
                    }
                    403 -> {

                        showSnackBar(requireView().rootView,it.message!!)
                    }
                    200 -> {
                        if (it.priority == ApiService.PRIORITY_HIGH) {
                            viewModel.errorMessage.set(it.message)
                            onError(it)
                        } else {
                            showSnackBar(requireView().rootView,it.message!!)
                        }
                    }
                    else -> {
                        if (it.priority == ApiService.PRIORITY_HIGH) {
                            viewModel.errorMessage.set(it.message)
                            onError(it)
                        } else {
                            showSnackBar(requireView().rootView,it.message!!)
                        }
                    }
                }
            })
        }
    }

    fun matchParentWidth() {
        val wmlp = dialog!!.window?.attributes
        wmlp!!.gravity = Gravity.FILL_HORIZONTAL
    }
    fun fullScreenDialog() {
        dialog!!.window!!.setLayout(WindowManager.LayoutParams.MATCH_PARENT, +WindowManager.LayoutParams.WRAP_CONTENT)
        dialog!!.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog!!.window!!.attributes.windowAnimations = R.style.DialogAnimation
        dialog!!.window!!.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        dialog!!.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        dialog!!.window!!.setGravity(Gravity.CENTER)
    }
  fun disableFullScreenDialog() {
      val lp = WindowManager.LayoutParams()
      val window = dialog!!.window
      lp.copyFrom(window!!.attributes)
      // This makes the dialog take up the full width
      lp.width = WindowManager.LayoutParams.WRAP_CONTENT
      lp.height = WindowManager.LayoutParams.WRAP_CONTENT
      window.setFlags(
          WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
          WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
      )
      window.attributes = lp
      lp.windowAnimations = R.style.MyStyleDialog
      window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
    }

    fun seTCancelable(cancelable: Boolean) {
        dialog?.requestWindowFeature(STYLE_NO_TITLE)
        isCancelable = cancelable
        dialog?.setCanceledOnTouchOutside(cancelable)
    }



    fun isValidActivity(): Boolean {
        if (activity == null || requireActivity().isFinishing) return false
        return true
    }


    fun getThemeLayoutInflater(inflater: LayoutInflater): LayoutInflater {
        var contextThemeWrapper: Context = ContextThemeWrapper(activity, R.style.AppTheme)
        val localInflater = inflater.cloneInContext(contextThemeWrapper)
        return localInflater
    }

    fun showSnackBar(view: View, msg: String) {

          val snack: Snackbar = Snackbar.make(view, msg, Snackbar.LENGTH_LONG)
        snack.setActionTextColor(Color.WHITE)
        snack.setBackgroundTint(resources.getColor(R.color.white))
        val sbView = snack.view
        val params: FrameLayout.LayoutParams = sbView.layoutParams as FrameLayout.LayoutParams
        params.gravity = Gravity.TOP
        sbView.layoutParams = params
        val textView =
            sbView.findViewById<View>(R.id.snackbar_text) as TextView
        textView.setTextColor(Color.BLACK)
        snack.show()
    }

}