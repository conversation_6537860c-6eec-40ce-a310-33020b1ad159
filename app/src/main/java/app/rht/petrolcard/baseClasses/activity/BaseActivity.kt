package app.rht.petrolcard.baseClasses.activity

import android.Manifest
import android.annotation.SuppressLint
import android.app.Activity
import android.app.AlarmManager
import android.app.Dialog
import android.app.PendingIntent
import android.content.*
import android.content.pm.PackageManager
import android.graphics.*
import android.net.ConnectivityManager
import android.net.NetworkCapabilities
import android.net.Uri
import android.os.*
import android.provider.ContactsContract
import android.provider.MediaStore
import android.text.TextUtils
import android.text.format.DateFormat
import android.util.Base64
import android.util.Log
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Button
import android.widget.TextView
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.app.AppCompatDelegate
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.content.FileProvider
import androidx.documentfile.provider.DocumentFile
import androidx.lifecycle.lifecycleScope
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.MainApp.Companion.longApiService
import app.rht.petrolcard.R
import app.rht.petrolcard.apimodel.apiresponsel.ErrorData
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.interfaces.ApiOnResultListener
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.NetworkSchedulerService
import app.rht.petrolcard.service.RestartApplicationReceiver
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.ui.loyalty.utils.HelpersLoyalty
import app.rht.petrolcard.ui.loyalty.utils.TicketPrinter
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.menu.model.TelecollectDataModel
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.startup.activity.SplashScreenActivity
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.Support.Companion.formatString
import app.rht.petrolcard.utils.Utils.isExternalStorageAvailable
import app.rht.petrolcard.utils.citizen.AlignmentType
import app.rht.petrolcard.utils.citizen.PrintCmd
import app.rht.petrolcard.utils.citizen.PrintContentType
import app.rht.petrolcard.utils.common.TimeAgo
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.AppConstant.ATTENDANT_MODE
import app.rht.petrolcard.utils.constant.AppConstant.B_TPE
import app.rht.petrolcard.utils.constant.AppConstant.SDCARD_RESULT_CODE
import app.rht.petrolcard.utils.constant.AppConstant.UN_ATTENDANT_MODE
import app.rht.petrolcard.utils.constant.DISCOUNT_TYPE
import app.rht.petrolcard.utils.constant.Events
import app.rht.petrolcard.utils.constant.PRODUCT
import app.rht.petrolcard.utils.extensions.launch
import app.rht.petrolcard.utils.extensions.showDialog
import app.rht.petrolcard.utils.extensions.showSnakeBar
import app.rht.petrolcard.utils.extensions.showSnakeBarColor
import app.rht.petrolcard.utils.fuelpos.FuelPosService
import app.rht.petrolcard.utils.paxutils.FilePathUtils
import app.rht.petrolcard.utils.paxutils.FilePathUtils.Companion.bitmapToFile
import app.rht.petrolcard.utils.paxutils.FilePathUtils.Companion.getOutputMediaFile
import app.rht.petrolcard.utils.paxutils.FilePathUtils.Companion.getOutputMediaFileUri
import app.rht.petrolcard.utils.paxutils.FilePathUtils.Companion.getRealPathFromURI_2
import app.rht.petrolcard.utils.paxutils.modules.ped.PedTester
import app.rht.petrolcard.utils.paxutils.modules.printer.PrinterTester
import app.rht.petrolcard.utils.paxutils.system.SysTester
import com.afollestad.materialdialogs.MaterialDialog
import com.github.danielfelgar.drawreceiptlib.ReceiptBuilder
import com.github.razir.progressbutton.hideProgress
import com.github.razir.progressbutton.showProgress
import com.google.firebase.messaging.FirebaseMessaging
import com.google.gson.Gson
import com.pax.dal.entity.ECheckMode
import com.pax.dal.entity.EPedKeyType
import com.pax.dal.entity.EPedType
import com.testfairy.TestFairy
import com.theartofdev.edmodo.cropper.CropImage
import com.theartofdev.edmodo.cropper.CropImageView
import com.usdk.apiservice.aidl.printer.*
import com.vforl.utils.extensions.hideKeyboard
import io.reactivex.Single
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import org.koin.android.ext.android.get
import org.koin.androidx.viewmodel.ext.android.getViewModel
import wangpos.sdk4.libbasebinder.Core
import wangpos.sdk4.libbasebinder.Printer
import wangpos.sdk4.libkeymanagerbinder.Key
import java.io.*
import java.lang.ref.WeakReference
import java.text.DecimalFormat
import java.text.DecimalFormatSymbols
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.collections.ArrayList
import kotlin.reflect.KClass
import kotlin.system.exitProcess


abstract class BaseActivity<V : BaseViewModel>(private val viewModelClass: KClass<V>) :
    AppCompatActivity(), ApiOnResultListener {
    private val TAG = BaseActivity::class.simpleName
    val prefs: AppPreferencesHelper = get()
    var canThemeChange = true
    lateinit var mViewModel: V
    var mLoadingNextPage = false
    var mCore: Core? = MainApp.mCore
    val sdf = SimpleDateFormat("yyyy-MM-dd",Locale.ENGLISH)
    val sdf1 = SimpleDateFormat("HH:mm",Locale.ENGLISH)
    private var aspectRationX: Int = 1
    private var aspectRationY: Int = 1
    private var shouldCropImage: Boolean = false
    private var documentPickrequest: Int = 6
    private var imagePickRequest: Int = 0
    private var videoPickRequest: Int = 4
    private var mCameraImageUri: Uri? = null
    val CAMERA_IMAGE_REQUEST = 1001
    private var mSelectedUploadImage = UploadImage()
    private val RC_PICK_CONTACT = 1002
    var senderId = ""
    val MEDIA_TYPE_IMAGE = 1
    var networkBroadCastReceiver: NetworkBroadCastReceiver? = null
    val localeFormat: DecimalFormatSymbols = DecimalFormatSymbols.getInstance(Locale.US)
    val decimalFormat = DecimalFormat("#.##", localeFormat)
    var downloadedSize = 0
    var totalsize = 0

    //private val localeDelegate: LocaleHelperActivityDelegate = LocaleHelperActivityDelegateImpl()
    var date: Date? = null
    lateinit var gson: Gson
    private val CAMERA_CAPTURE_IMAGE_REQUEST_CODECAMERA_CAPTURE_IMAGE_REQUEST_CODE = 100
    private var fileUri // file url to store image/video
            : Uri? = null
    var fuelVat = VatModel()
    var shopVat = VatModel()
    var receipt = ReceiptBuilder(1200)

    val FONT_BIG = 28
    val FONT_NORMAL = 24
    val FONT_XTRA_SMALL = 14
    val FONT_SMALL = 18
    val FONT_BIGEST = 40

    var sPref : PreferenceModel? = null
    private var referenceModel : ReferenceModel? = null
    var terminalType : Int? = ATTENDANT_MODE
    lateinit var mainApp : MainApp

    private val startTime = TimeUnit.MINUTES.toMillis(if(prefs.getPreferenceModel()!=null && prefs.getPreferenceModel()!!.TELECOLLECT_TIME!=null) prefs.getPreferenceModel()!!.TELECOLLECT_TIME!! else 15)

    private lateinit var preferenceThread :Thread
    private var sharedPrefsThread : Thread? = null
    override fun onCreate(savedInstanceState: Bundle?) {
       // setTheme()
        super.onCreate(savedInstanceState)

        mainApp = MainApp.appContext as MainApp
        mViewModel = getViewModel(viewModelClass)
        setObserver()
        setBaseObserver()
        observeError()
        HelpersLoyalty.enableWifi(this)
        supportActionBar?.hide()
        setStatusBar(this)
        gson = Gson()
        referenceModel = prefs.getReferenceModel()
        sPref = prefs.getPreferenceModel()
        sharedPrefsThread = Thread {
            if(sharedPrefsThread!=null && !sharedPrefsThread!!.isInterrupted){
                if(referenceModel!=null){
                    terminalType = referenceModel!!.TERMINAL_TYPE
                    if(referenceModel!=null){
                        if(referenceModel!!.APP_AUTO_RUN != null && referenceModel!!.APP_AUTO_RUN!!)
                        {
                            registerRestartApplicationReceiver()
                        }
                        fuelVat = referenceModel!!.fuelVat!!
                        shopVat = referenceModel!!.shopVat!!
                    }
                }
            }
        }
        sharedPrefsThread!!.start()
    }
    private fun init()
    {
        HelpersLoyalty.enableWifi(this)
        supportActionBar?.hide()
        setStatusBar(this)
        overridePendingTransition(R.anim.fade_in, R.anim.fade_out)
        registerNetworkBroadCastReceiver()
        //startScheduledTelecollctReceiver()

    }
    override fun finish() {
        super.finish()
        overridePendingTransition(R.anim.fade_out, R.anim.fade_in)
    }

    override fun attachBaseContext(base: Context?) {
        super.attachBaseContext(LocaleManager.setLocale(base))
    }

    var restartApplicationReceiver : RestartApplicationReceiver? = null
    private fun registerRestartApplicationReceiver() {
        val filter = IntentFilter()
        filter.addAction(Events.STOP_ACTION)
        filter.addAction(Events.START_ACTION)
        filter.addAction(Events.PAUSE_ACTION)
        filter.addAction(Events.RESUME_ACTION)
        filter.addAction(Events.DESTROY_ACTION)
        filter.addAction(Intent.ACTION_BATTERY_CHANGED)
        restartApplicationReceiver = RestartApplicationReceiver()
        registerReceiver(restartApplicationReceiver, filter)
    }


    fun sendEventsActions(event:String) {
        if(referenceModel != null && referenceModel!!.APP_AUTO_RUN != null && referenceModel!!.APP_AUTO_RUN!!) {
            val intent = Intent()
            intent.action = event
            sendBroadcast(intent)
        }
    }

    fun setBaseObserver() {
        mViewModel.let { viewModel ->

            viewModel.actionBarOnBackPressListener.observe(this) {
                if (it) {
                    finish()
                }
            }

            viewModel.redirect.observe(this) {
                it?.let {
                    launch(
                        it.mClass,
                        arguments = it.arguments,
                        isTopFinish = it.isTopFinish,
                        flagIsTopClearTask = it.flagIsTopClearTask
                    )
                }
            }
            viewModel.showDialog.observe(this) {
                it?.let {
                    showDialog(
                        title = it.title,
                        message = it.message,
                        onPressOk = { v ->
                            if (it.goToBackScreen) {
                                finish()
                            }
                            it.mClass?.let { mClass ->
                                launch(
                                    mClass,
                                    arguments = it.arguments,
                                    isTopFinish = it.isTopFinish,
                                    flagIsTopClearTask = it.flagIsTopClearTask
                                )
                            }
                        }
                    )
                }
            }

            viewModel.showSnakBar.observe(this) {
                it?.let {
                    showSnakeBar(it)
                }
            }

            viewModel.logoutResponse.observe(this) {


            }
            viewModel.noInternetResponse.observe(this) {
                onInternetEnableDisable(false)

                showNetworkDialog()
            }
        }
    }


    fun setStatusBar(activity: Activity) {
        val window = activity.window
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
    }


    @RequiresApi(Build.VERSION_CODES.M)
    fun changeStatusBarColor(user_type: Int) {
        val color = getColor(user_type)
        val window: Window = window
        window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
        window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
        window.statusBarColor = color
    }

    abstract fun setObserver()

    private fun setUseTheme(themeMode: Int) {
        AppCompatDelegate.setDefaultNightMode(themeMode)
    }

    fun showToast(msg: String? = "") {
        Handler(Looper.getMainLooper()).post {
            Toast.makeText(this, "" + msg, Toast.LENGTH_SHORT).show()
        }
    }

    override fun onError(errorData: ErrorData) {
        log(TAG, "Error Received $errorData")
       // showSnakeBar(errorData.message)
        //   generateLogs(errorData.toString(),0)
    }

    private fun observeError() {
        mViewModel.let { viewModel ->
            viewModel.mErrorMessage.observe(this) {
                log("observeError", "observeError " + it.statusCode)
                generateLogs("Server Configuration Error", 1)
                onError(it)
                when (it.statusCode) {
                    0 -> {
                        showSnakeBar(it.message)
                    }

                    401 -> {
                        gotoAbortMessageActivity(getString(R.string.error_code_401), it.message)

                    }
                    404 -> {
                        // gotoAbortMessageActivity(it.statusCode.toString(), it.message)
                        viewModel.errorMessage.set(it.message)
                        onError(it)
                    }

                    200 -> {
                        if (it.priority == ApiService.PRIORITY_HIGH) {
                            viewModel.errorMessage.set(it.message)
                            onError(it)
                        } else {
                            showSnakeBarColor(it.message,true)
                        }
                    }
                    else -> {
                        if (it.priority == ApiService.PRIORITY_HIGH) {
                            viewModel.errorMessage.set(it.message)
                            onError(it)
                        } else {
//                            gotoAbortMessageActivity(getString(R.string.error_code_) + it.statusCode, it.message)
                            showSnakeBarColor(it.message,true)
                        }
                    }
                }
            }
        }
    }


    fun getTimeZone(): String {
        prefs.timeZone = TimeZone.getDefault().id ?: ""
        return TimeZone.getDefault().id ?: ""
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun pickImage(shouldCropImage: Boolean, imagePickRequest: Int) {
        hideKeyboard()
        this.shouldCropImage = shouldCropImage
        this.imagePickRequest = imagePickRequest
        validateAspectsRatios()
        if (ContextCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
            || ContextCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ), imagePickRequest
            )
        } else {
            selectImage()
        }
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun pickImageCamera(shouldCropImage: Boolean, imagePickRequest: Int) {
        hideKeyboard()
        this.shouldCropImage = shouldCropImage
        this.imagePickRequest = imagePickRequest
        validateAspectsRatios()
        if (ContextCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
            || ContextCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ), imagePickRequest
            )
        }
    }

    fun selectImage() {
        if (shouldCropImage) {
            val intent = CropImage.activity()
                .setGuidelines(CropImageView.Guidelines.ON)
                .setFixAspectRatio(true)
                .getIntent(applicationContext)
            startActivityForResult(intent, imagePickRequest)
        } else if (!shouldCropImage && imagePickRequest == 1) {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, false)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.type = "image/*"
            startActivityForResult(intent, imagePickRequest)
        } else {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.type = "image/*"
            startActivityForResult(intent, imagePickRequest)
        }

    }

    fun selectDocument() {

        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
        intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
        intent.addCategory(Intent.CATEGORY_OPENABLE)
        intent.type = "*"
        startActivityForResult(intent, documentPickrequest)
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun pickVideo(videoPickRequest: Int) {
        hideKeyboard()
        this.shouldCropImage = shouldCropImage
        this.videoPickRequest = videoPickRequest
        validateAspectsRatios()
        if (ContextCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED
            || ContextCompat.checkSelfPermission(
                applicationContext,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(
                arrayOf(
                    Manifest.permission.CAMERA,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE
                ), videoPickRequest
            )
        } else {
            selectVideo()
        }
    }

    private fun selectVideo() {
        val option = arrayOf(getString(R.string.camera), getString(R.string.gallery))
        val builder = AlertDialog.Builder(this)
        builder.setTitle(getString(R.string.complete_action_using))
        builder.setItems(option) { _, i ->
            if (i == 0) {
                openCamera()
            } else if (i == 1) {
                openGallery()
            }
        }
        builder.show()
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun selectImageOptions() {
        val option = arrayOf(getString(R.string.camera), getString(R.string.gallery))
        val builder = AlertDialog.Builder(this)
        builder.setTitle(getString(R.string.complete_action_using))
        builder.setItems(option) { _, i ->
            if (i == 0) {
                openCameraToClickImage()
            } else if (i == 1) {
                openGalleryImages()
            }
        }
        builder.show()
    }

    private fun openGallery() {

        val intent = Intent()
        intent.type = "video/*"
        intent.action = Intent.ACTION_PICK
        startActivityForResult(Intent.createChooser(intent, getString(R.string.select_video)), videoPickRequest)

    }

    private fun openGalleryImages() {

        if (!shouldCropImage && imagePickRequest == 1) {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, false)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.type = "image/*"
            startActivityForResult(intent, imagePickRequest)
        } else {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT)
            intent.putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.type = "image/*"
            startActivityForResult(intent, imagePickRequest)
        }

    }

    private fun openCamera() {
        val cameraintent = Intent(MediaStore.ACTION_VIDEO_CAPTURE)
        startActivityForResult(cameraintent, videoPickRequest)
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        try {
            when (requestCode) {

                imagePickRequest -> {
                    when (resultCode) {
                        RESULT_OK -> {
                            if (shouldCropImage) {
                                //For Cropped Images
                                CropImage.getActivityResult(data).uri?.let { uri ->
                                    Single.fromCallable {
                                        ImageUtils.compressImage(
                                            applicationContext,
                                            FileUtil.getRealPath(applicationContext, uri)
                                                ?: throw NullPointerException("Cannot process image.. URI: $uri")
                                        )
                                    }
                                        .subscribeOn(Schedulers.io())
                                        .observeOn(AndroidSchedulers.mainThread())
                                        .subscribe(
                                            {
                                                val file = File(it)
                                                onImagePickSuccess(file, imagePickRequest)

                                            },
                                            {
                                                log("TAG", it.message!!)
                                            }
                                        )

                                }
                            } else {
                                //For Multiple Image Selection
                                if (data?.clipData != null) {
                                    var count = data.clipData!!.itemCount
                                    if (count > 10) {
                                        showSnakeBar(getString(R.string.cant_upload_morethan_10))
                                        return
                                    }
                                    for (i in 0 until count) {
                                        val imageUri: Uri = data.clipData!!.getItemAt(i).uri
                                        Single.fromCallable {
                                            ImageUtils.compressImage(
                                                this, FileUtil.getRealPath(this, imageUri)
                                                    ?: throw NullPointerException("Cannot process image. URI: $imageUri")
                                            )
                                        }
                                            .subscribeOn(Schedulers.io())
                                            .observeOn(AndroidSchedulers.mainThread())
                                            .subscribe(
                                                {
                                                    val file = File(it)
                                                    onImagePickSuccess(file, imagePickRequest)
                                                },
                                                {
                                                    log("TAG", it.message!!)
                                                }
                                            )
                                    }

                                } else if (data?.data != null) {
                                    //For Single Image Selection
                                    val imageUri: Uri = data.data!!
                                    Single.fromCallable {
                                        ImageUtils.compressImage(
                                            this, FileUtil.getRealPath(this, imageUri)
                                                ?: throw NullPointerException("Cannot process image. URI: $imageUri")
                                        )
                                    }
                                        .subscribeOn(Schedulers.io())
                                        .observeOn(AndroidSchedulers.mainThread())
                                        .subscribe(
                                            {
                                                val file = File(it)
                                                onImagePickSuccess(file, imagePickRequest)
                                            },
                                            {
                                                log("TAG", it.message!!)
                                            }
                                        )
                                }
                            }
                        }
                        CropImage.CROP_IMAGE_ACTIVITY_RESULT_ERROR_CODE -> {
                            val error = CropImage.getActivityResult(data).error
                            log("TAG", error.message!!)
                        }
                    }
                }

                captureImageRequestCode -> if (resultCode == Activity.RESULT_OK) {
                    if (mCameraImageUri != null) {
                        mSelectedUploadImage.imageFilePath =
                            FilePathUtils.getRealPathFromURI_2(this, mCameraImageUri!!)
                        mSelectedUploadImage.mediaType = MediaType.CAMERA
                        if (shouldCropImage) {
                            cropImage(mCameraImageUri)
                        } else {
                            compressImage(Intent(), mCameraImageUri!!)
                        }
                    }
                }
                else
                {
                    onImageCaptureFailed()
                }

                SDCARD_RESULT_CODE -> {
                    val treeUri: Uri? = data!!.data
                    val pickedDir: DocumentFile? = DocumentFile.fromTreeUri(this, treeUri!!)
                    grantUriPermission(
                        packageName,
                        treeUri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    )
                    contentResolver.takePersistableUriPermission(
                        treeUri,
                        Intent.FLAG_GRANT_READ_URI_PERMISSION or Intent.FLAG_GRANT_WRITE_URI_PERMISSION
                    )
                    prefs.saveStringSharedPreferences(
                        AppConstant.PREFERENCE_SDCARD_PATH,
                        pickedDir!!.uri.toString()
                    )
                    log(TAG, "sdcardPath::: " + pickedDir.uri.toString())
                }

            }
        }
        catch (e:Exception)
        {
            log(TAG, e.message+ExceptionUtils.getStackTrace(e))
            e.printStackTrace()
        }
    }

    open fun onVideoPic(videoPath: String, timeStamp: String) {

    }

    open fun onVideoCompressorPic(uri: Uri) {

    }

    private fun validateAspectsRatios() {
        if (aspectRationX == 0 || aspectRationY == 0) {
            aspectRationX = 1
            aspectRationY = 1
        }
    }

    open fun onImagePickSuccess(file: File, imagePickRequest: Int) {

    }
    open fun onImageCaptureFailed() {

    }
    open fun onContactPickSuccess(contactName: String, contactNumber: String) {

    }

    fun hideNavBarAndStatusBarForcefully(){
        if(!BuildConfig.DEBUG) {
            if (BuildConfig.POS_TYPE == "B_TPE") {
                enableWoPosNavBar(false)
                enableWoPosStatusBar(false)
            }
            if (BuildConfig.POS_TYPE == "PAX") {
                SysTester.getInstance().showStatusBar(false)
                SysTester.getInstance().enableStatusBar(false)
                SysTester.getInstance().showNavigationBar(false)
            }
        }
    }

    fun enableWoPosStatusBar(enabled:Boolean) {
        try {
            //if the mode is 0, StatusBar enabled,
            // *  if the mode is 1, StatusBar disable.
            val mode = if(enabled) 0 else 1

            val wangPosManagerClass = Class.forName("android.os.WangPosManager")
            val wangPosManager = wangPosManagerClass.newInstance()
            val statusBarMode = wangPosManagerClass.getMethod("setStatusbarMode", Int::class.javaPrimitiveType)
            statusBarMode.invoke(wangPosManager, mode)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            log(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            //  mViewModel.generateLogs(e.message!!,0)
        }
    }

    open fun setTheme() {
        if (BuildConfig.POS_TYPE == "PAX") {
            SysTester.getInstance()
                .showStatusBar(prefs.getBooleanSharedPreferences(AppConstant.DEVICE_STATUS_BAR))
            SysTester.getInstance()
                .showNavigationBar(prefs.getBooleanSharedPreferences(AppConstant.DEVICE_NAVIGATION_BAR))
            SysTester.getInstance()
                .enableStatusBar(prefs.getBooleanSharedPreferences(AppConstant.DEVICE_STATUS_BAR))
        }
        if(BuildConfig.POS_TYPE == B_TPE)
        {
            if(BuildConfig.POS_TYPE == "B_TPE"){
                enableWoPosNavBar(prefs.getBooleanSharedPreferences(AppConstant.DEVICE_NAVIGATION_BAR))
                enableWoPosStatusBar(prefs.getBooleanSharedPreferences(AppConstant.DEVICE_STATUS_BAR))
            }
        }
        requestWindowFeature(Window.FEATURE_NO_TITLE)
       // this.window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
       // changeStatusBarColor(R.color.black)

        /*this.window.setFlags(
            WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_FULLSCREEN
        )*/

//        if (prefs.isNightMode) {
//            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO)
//        } else {
//            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES)
//        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {

        }
    }

    @SuppressLint("SimpleDateFormat")
    fun convertTimestamp(s: Long, pattern: String): String? {
        return try {
            val sdf = SimpleDateFormat(pattern)
            val netDate = Date(s)
            sdf.format(netDate)
        } catch (e: Exception) {
            e.toString()
        }
    }

    fun getRootViewId() = android.R.id.content

    open fun share(url: String, title: String, content: String) {
        try {
            val i = Intent(Intent.ACTION_SEND)
            i.type = "text/plain"
            i.putExtra(Intent.EXTRA_SUBJECT, title)
            var sAux =
                content
            sAux = sAux + "\n\n" + url
            i.putExtra(Intent.EXTRA_TEXT, sAux)
            startActivity(
                Intent.createChooser(
                    i,
                    getString(R.string.choose_one)
                )
            )
        } catch (e: Exception) {
            Toast.makeText(this, e.message, Toast.LENGTH_SHORT).show()
        }
    }

    fun getCurrentTimestamp(): String {
        return java.lang.String.valueOf(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()))
    }

    fun getTimestampSeconds(): String {
        val seconds = TimeUnit.SECONDS.toSeconds(System.currentTimeMillis())/1000
        return ""+seconds
    }

    fun getTimeAgo(timestamp: Long): String {
        return TimeAgo.getTimeAgo(timestamp)
    }

    @RequiresApi(Build.VERSION_CODES.M)
    private fun openCameraToClickImage() {
        if (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.CAMERA
            ) != PackageManager.PERMISSION_GRANTED ||
            (ActivityCompat.checkSelfPermission(
                this,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED)
        ) {
            requestPermissions(
                AppConstant.PERMISSIONS_CAMERA,
                AppConstant.PERMISSIONS_REQUEST_CAMERA_IMAGE
            )
        } else {
            actionCameraToClickImage()
        }
    }

    /*To pic image from camera*/
    private var captureImageRequestCode = CAMERA_IMAGE_REQUEST
    fun actionCameraToClickImage(requestCode: Int = CAMERA_IMAGE_REQUEST) {
        captureImageRequestCode = requestCode

        mCameraImageUri = getOutputMediaFileUri()

        if (mCameraImageUri != null) {
            var intent: Intent = Intent(MediaStore.ACTION_IMAGE_CAPTURE)

            var file: File = File(mCameraImageUri!!.path!!)
            val photoUri: Uri = FileProvider.getUriForFile(
                applicationContext,
                applicationContext.packageName + ".provider",
                file
            )
            intent.putExtra(MediaStore.EXTRA_OUTPUT, photoUri)

            if (intent.resolveActivity(packageManager) != null) {
                startActivityForResult(intent, requestCode)
                animateActivity()
            } else {
                showSnakeBar(getString(R.string.camera_umage_unavailable))
            }
        } else {
            showSnakeBar(getString(R.string.file_upload_error))
        }
    }


    fun animateActivity() {
        overridePendingTransition(
            R.anim.fade_in,
            R.anim.fade_out
        )
    }

    private fun compressImage(data: Intent?, uri: Uri): Bitmap? {
        var imageBitmap: Bitmap? = null
        lateinit var imagePath: String
        if (shouldCropImage) {
            if (data == null) {
                return null
            }
        }
        try {

            mSelectedUploadImage.imageFilePath = getRealPathFromURI_2(this, uri)
            val rotation: Int = ExifUtil.getExifRotation(mSelectedUploadImage.imageFilePath)

            mSelectedUploadImage.bitmap =
                MediaStore.Images.Media.getBitmap(this.contentResolver, uri)
            if (rotation != 0) {
                val m = Matrix()
                m.preRotate(rotation.toFloat())
                mSelectedUploadImage.bitmap = Bitmap.createBitmap(
                    mSelectedUploadImage.bitmap!!,
                    0,
                    0,
                    mSelectedUploadImage.bitmap!!.width,
                    mSelectedUploadImage.bitmap!!.height,
                    m,
                    true
                )
            } else {
                mSelectedUploadImage.bitmap = Bitmap.createBitmap(
                    mSelectedUploadImage.bitmap!!,
                    0,
                    0,
                    mSelectedUploadImage.bitmap!!.width,
                    mSelectedUploadImage.bitmap!!.height
                )

            }

            /**Create a temporary file **/
            val outFile: File? = getOutputMediaFile()
            val finalImagePath: String? = outFile?.absolutePath
            if (!TextUtils.isEmpty(finalImagePath)) {
                val mHandler = Handler(Looper.getMainLooper())
                mHandler.post {
                    kotlin.run {
                        val out: FileOutputStream?
                        try {
                            out = FileOutputStream(finalImagePath)
                            mSelectedUploadImage.bitmap!!.compress(
                                Bitmap.CompressFormat.JPEG,
                                25,
                                out
                            )

                            mSelectedUploadImage.imageFilePath = finalImagePath

                            imageBitmap = mSelectedUploadImage.bitmap!!
                            imagePath = mSelectedUploadImage.imageFilePath!!

                            imageBitmap = FilePathUtils.compressImage(
                                mSelectedUploadImage.imageFilePath!!,
                                this
                            )
                            if (imageBitmap != null) {
                                val file = bitmapToFile(imageBitmap!!, this)
                                onImagePickSuccess(file, captureImageRequestCode)
                                captureImageRequestCode = 0
                            }
                        } catch (e: Exception) {
                            println(e.toString())
                        }
                    }
                }
            }


        } catch (e: Exception) {
            println(e.toString())
        }
        return imageBitmap
    }

    private fun cropImage(mCameraImageUri: Uri?) {
        try {
            CropImage.activity(mCameraImageUri)
                .setGuidelines(CropImageView.Guidelines.ON)
                .setAspectRatio(aspectRationX, aspectRationY)
                .start(this)
        } catch (e: Exception) {
            println(e.toString())
        }
    }

    fun triggerPickContactIntent() {
        val i = Intent(Intent.ACTION_PICK)
        i.type = ContactsContract.CommonDataKinds.Phone.CONTENT_TYPE
        startActivityForResult(i, RC_PICK_CONTACT)
    }

    @SuppressLint("SimpleDateFormat")
    fun getDate(timestamp: Long): String {
        val calendar = Calendar.getInstance(Locale.ENGLISH)
        calendar.timeInMillis = timestamp * 1000L

        return DateFormat.format("yyy-MM-dd", calendar).toString()
    }
    fun getTime(timestamp: Long): String {
        val calendar = Calendar.getInstance(Locale.ENGLISH)
        calendar.timeInMillis = timestamp * 1000L
        return DateFormat.format("HH:mm", calendar).toString()
    }

    @RequiresApi(Build.VERSION_CODES.M)
    fun isNetworkFound(): Boolean {
        val connectivityManager: ConnectivityManager = getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
        val capabilities =
            connectivityManager.getNetworkCapabilities(connectivityManager.activeNetwork)
        capabilities.also {
            if (it != null) {
                if (it.hasTransport(NetworkCapabilities.TRANSPORT_WIFI))
                    return true
                else if (it.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                    return true
                }
            }
        }
        return false
    }

    open fun opendialPad(mobileNumber: String) {
        val intent = Intent(Intent.ACTION_DIAL)
        intent.data = Uri.parse("tel:$mobileNumber")
        startActivity(intent)
    }

    open fun getSN(): String? {
        return MainApp.sn
    }
    /*open fun updateLocale(locale: Locale) {
        localeDelegate.setLocale(this, locale)
    }*/

    // eof sdCARD
    fun isSdCARD(): Boolean {
        return isExternalStorageAvailable() && prefs.isUseSdCard
    }

    open fun stringHourToDate(strDate: String?): Date? {
        val sdfDate = SimpleDateFormat("HH:mm:ss", Locale.ENGLISH)
        var date: Date? = null
        try {
            date = sdfDate.parse(strDate!!)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return date
    }

    private fun saveTerminalDetails(terminal: TerminalModel) {

        val mTerminalDAO = TerminalDao()
        mTerminalDAO.open()
        mTerminalDAO.clearTerminalTableData()
        mTerminalDAO.insertTerminalData(
            TerminalModel(
                terminalId = terminal.terminalId,
                serialNumber = terminal.serialNumber,
                teleCollectTime = Support.stringToTime("00:00:00")!!,
                max_transaction_amount = 10000,
                min_transaction_amount = 10,
                pays = "Maroc",
                region = "Region El Jadida",
                city = terminal.city,
                sectorName = "Zone Nord",
                sectorId = Integer.valueOf(terminal.sectorId),
                sectorBit = 0,
                stationId = Integer.valueOf(terminal.stationId),
                stationBit = 2,
                stationType = 0,  // typeStation
                stationName = terminal.stationName,
                address = terminal.address,
                fiscalId = terminal.fiscalId,
                shopFlag = 1,
                purchaseFlag = 1,
                cancellationFlag = 1,
                unlockTerminalFlag = 1,
                rechargeFlag = 1,
                lockTerminalFlag = 0,
                ipController = "**************",
                portController = "80",
                shopIp = "",
                shopPort = "",
                ipAddress = "",
                netMask = "",
                gateway = "",
                ssid = "",
                wifiPassword = "",
                inventoryCount = 140562,
                geoFence = "",
                currency = " " + prefs.currency,
                maxRefillAmount = terminal.maxRefillAmount,
                maxRechargeLimit = terminal.maxRechargeLimit
            )
        )

        val currentTerminal: TerminalModel? = mTerminalDAO.getCurrent()
        mTerminalDAO.close()
    }

    private fun saveBlackListData(blacklistData: ArrayList<BlackListModel>, blackListVersion: Int) {
        log(TAG, "BLACK LIST SIZE: ${blacklistData.size}")

        if (blacklistData.isNotEmpty()) {
            try {
                //save blacklist version in sp
                prefs.blackListVersion = blackListVersion


                val mBlackListDao = BlackListDao()
                mBlackListDao.open()
                mBlackListDao.clearBlackListData()
                mBlackListDao.insertBlackListArrayData(blacklistData)
                mBlackListDao.close()
                log(TAG, "Blacklist inserted in DB...")
                prefs.saveBlackListData(blacklistData)
            } catch (ex: SQLiteException) {
                ex.printStackTrace()
            }
        }
    }

    private fun saveGreyListData(greyListModel: ArrayList<GreyListModel>, greyListVersion: Int) {
        if (greyListModel.isNotEmpty()) {
            prefs.saveGreyList(greyListModel)
            log(TAG, "Grey list inserted in DB...")
        }
    }

    private fun saveGasStationAttendantDetails(
        attendantModel: ArrayList<GasStationAttendantModel>,
        mode: String
    ) {
        if (attendantModel.isNotEmpty()) {
            try {
                log(TAG,"ATTENDANTS: ${Gson().toJson(attendantModel)}")
                val mUsersDao = UsersDao()
                mUsersDao.open()
                mUsersDao.clearUsersTableData()
                mUsersDao.insertAttendiesArrayData(attendantModel, mode)
                mUsersDao.close()

            } catch (ex: SQLiteException) {
                ex.printStackTrace()
            }
        }
    }

    private fun saveCompanyDetails(companyModel: CompanyModel?) {
        if (companyModel != null) {
            prefs.saveBooleanSharedPreferences(AppConstant.COMPANY_RELOAD, companyModel.reload)
            prefs.saveStringSharedPreferences(AppConstant.COMPANY_LOGO, companyModel.logo)
            prefs.saveStringSharedPreferences(AppConstant.COMPANY_LOGO, companyModel.footer)
        }
    }

    private fun saveFuelPosDetails(fuelPOSModel: FuelPOSModel?) {

        if (fuelPOSModel != null) {
            prefs.saveFuelPosModel(fuelPOSModel)

            val mFuelPosDAO = FuelPosDao()
            mFuelPosDAO.open()
            mFuelPosDAO.clearFuelPosTableData()
            if (mFuelPosDAO.isOpen()) {
                mFuelPosDAO.insertFuelPosData(fuelPOSModel)
                mFuelPosDAO.close()
            }
        }
    }

    private fun saveProductDetails(categoryListModel: ArrayList<CategoryListModel>?) {
        if (categoryListModel != null) {

            val mProductDAO = ProductsDao()
            mProductDAO.open()
            mProductDAO.clearProductsTableData()

            val productModel = ArrayList<ProductModel>()

            for (model in categoryListModel) {
                when (model.category_id) {
                    PRODUCT.FUEL_CATEGORY_ID -> {
                        for (submodel in model.sub_products!!) {
                            productModel.add(
                                ProductModel(
                                    productID = submodel.id,
                                    fcc_prod_id = submodel.fcc_prod_id.toInt(),
                                    libelle = submodel.label,
                                    code = submodel.code,
                                    bit = 0,
                                    categorie = model.category_name,
                                    isAvailable = submodel.isAvailable.toString(),
                                    color_code = submodel.color_code,
                                    icon = submodel.icon,
                                    categoryId = model.category_id,
                                    hs_code = submodel.hs_code
                                )
                            )
                        }
                    }
                    PRODUCT.SERVICE_CATEGORY_ID -> {
                        for (submodel in model.sub_categories!!) {
                            productModel.add(
                                ProductModel(
                                    productID = submodel.id,
                                    fcc_prod_id = submodel.id,
                                    libelle = submodel.label,
                                    code = submodel.code,
                                    bit = 0,
                                    categorie = model.category_name,
                                    isAvailable = submodel.isAvailable.toString(),
                                    color_code = submodel.color_code,
                                    icon = submodel.icon,
                                    categoryId = model.category_id,
                                    hs_code = submodel.hs_code
                                )
                            )
                        }
                    }
                    else -> {
                        productModel.add(
                            ProductModel(
                                productID = model.category_id,
                                fcc_prod_id = model.category_id,
                                libelle = model.category_name,
                                code = model.category_id.toString(),
                                bit = 0,
                                categorie = model.category_name,
                                isAvailable = "true",
                                color_code = model.color_code,
                                icon = model.icon,
                                categoryId = model.category_id,
                                hs_code = model.hs_code
                            )
                        )
                    }
                }
            }
            mProductDAO.insertProductsArrayData(productModel)
            mProductDAO.close()
        }
    }

    private fun savePriceDetails(priceModel: ArrayList<PriceModel>) {
        if (priceModel.isNotEmpty()) {

            val mPriceDao = PriceDao()
            mPriceDao.open()
            mPriceDao.clearPriceData()
            mPriceDao.insertPriceArrayData(priceModel)
            mPriceDao.close()
        }
    }

    @Throws(IOException::class)
    fun readLocalFile(): ByteArray? {
        //InputStream input = null;
        val filename: String = if (BuildConfig.DEBUG)
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                .toString() + File.separator + AppConstant.LOGO_NAME + ".png"
        else
            filesDir.toString() + File.separator + AppConstant.LOGO_NAME + ".png"

        var bitmap = BitmapFactory.decodeFile(filename)
        bitmap = Bitmap.createScaledBitmap(bitmap, 128, 128, true)
        val bos = ByteArrayOutputStream()
        bitmap.compress(Bitmap.CompressFormat.JPEG, 60 /*ignored for PNG*/, bos)
        bitmap.recycle()
        val bitmapdata = bos.toByteArray()
        BitmapFactory.decodeByteArray(bitmapdata, 0, bitmapdata.size)
        return bitmapdata
    }

    //endregion
    /*fun saveDataInDB(it: BaseResponse<ReferenceModel>) {
        log(TAG, "saving data in db")
        try {
            SQLiteDatabase.loadLibs(this)
            saveTerminalDetails(it.contenu!!.terminal!!)

            prefs.saveReferenceModel(it.contenu)
            referenceModel = it.contenu
            if(it.contenu.mtn_pay_credentials != null)
            {
                prefs.saveMtnPayCredentials(it.contenu.mtn_pay_credentials)
            }

            saveBlackListData(it.contenu.blacklist, it.contenu.blackListVersion)
            saveGreyListData(it.contenu.GreyList, it.contenu.GreyListVersion)
            saveGasStationAttendantDetails(
                it.contenu.pompiste,
                it.contenu.station!!.mode_pompiste
            )
            saveCompanyDetails(it.contenu.COMPANY)
            saveFuelPosDetails(it.contenu.FUELPOS)
            saveProductDetails(it.contenu.category_list)
            savePriceDetails(it.contenu.prix)

            if (it.contenu.FUSION != null) { //Save Fusion Model in SharedPreferences
                prefs.saveFusionModel(it.contenu.FUSION)
            }
            *//*if (it.contenu.badge != null) { //Save Badge data in SharedPreferences
            //prefs.badge = it.contenu.badge
            prefs.saveManagerBadges(it.contenu.badge)
        }*//*
            if (it.contenu.debug != null) {//To Check Terminal is Debug
                prefs.saveIntegerSharedPreferences(AppConstant.IS_DEBUGGABLE, it.contenu.debug)
            }
            if (it.contenu.ASKFORBADGE != null) {
                prefs.saveBooleanSharedPreferences(
                    AppConstant.IS_ASK_FOR_BADGE,
                    it.contenu.ASKFORBADGE
                )
            }
            if (it.contenu.WITHPICTURE != null) {
                prefs.saveBooleanSharedPreferences(AppConstant.wITHPICTURE, it.contenu.WITHPICTURE)
            }
            if (it.contenu.OFFLINEONLY != null) {
                prefs.saveBooleanSharedPreferences(
                    AppConstant.IS_OFFLINE_ONLY,
                    it.contenu.OFFLINEONLY
                )
            }
            if (it.contenu.`4G` != null) {
                prefs.saveIntegerSharedPreferences(AppConstant.IS_4G, it.contenu.`4G`)
            }
            if (it.contenu.LogTCP != null) {
                prefs.saveIntegerSharedPreferences(AppConstant.IS_LOGTCP, it.contenu.LogTCP)
            }
            if (it.contenu.LOG != null) {
                prefs.saveIntegerSharedPreferences(AppConstant.IS_LOGGABLE, it.contenu.LOG)
            }
            if (it.contenu.SERVER != null) {
                prefs.saveServerModel(it.contenu.SERVER)
            }
            if (it.contenu.category_list != null) {
                if (!it.contenu.category_list.isNullOrEmpty()) {
                    for (prod in it.contenu.category_list!!) {
                        if (prod.category_id == PRODUCT.FUEL_CATEGORY_ID) {
                            prefs.saveFuelProductList(prod.sub_products!!)
                        }
                    }
                }
            }

            if (it.contenu.prixConseille != null && it.contenu.prixConseille == "1") {
                prefs.saveBooleanSharedPreferences(AppConstant.PRICE_CONSOLATION, true)
            } else {
                prefs.saveBooleanSharedPreferences(AppConstant.PRICE_CONSOLATION, false)
            }

            if (it.contenu.station != null) {//Save Station Model in SharedPreferences
                prefs.saveStationModel(it.contenu.station)
                prefs.currency = it.contenu.station.currency
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            log(TAG, e.message+ExceptionUtils.getStackTrace(e))
            generateLogs("Failed to save the data on Db",1)
        }
    }*/

    private fun delay(milliSeconds: Long) {
        try {
            Thread.sleep(milliSeconds)
        } catch (e: InterruptedException) {
            e.printStackTrace()
        }
    }

    fun modifyNetworkWifi() {
        if (Connectivity.isOnlineSocket(this)) {
            log(TAG, "isOnlineSocket returned true")
            log(TAG, "using WIFI")
        } else {
            log(TAG, "isOnlineSocket returned false")
            val is4G = prefs.getIntegerSharedPreferences(AppConstant.IS_4G)
            if (is4G == 1) {
                Connectivity.disableWifi(this)
                log(TAG, "switching to 4G")
                log(TAG, "start ... waitForSomeTime 7s")
                delay(15000)
                log(TAG, "stop ... waitForSomeTime 7s")
            }
        }
    }

    fun isOneDayDifferenceAndMidNight(mToday: Date?, dateLastPLF: String?): Boolean {
        val sdfDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
        var expDate: Date? = null
        try {
            expDate = sdfDate.parse(dateLastPLF)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        val calToday = Calendar.getInstance()
        calToday.time = mToday
        val calLast = Calendar.getInstance()
        calLast.time = expDate
        val temp = calToday[Calendar.HOUR_OF_DAY]
        Log.w("HOUR_OF_DAY", "HOUR_OF_DAY :$temp")
        return if (Math.abs(calToday[Calendar.DAY_OF_MONTH] - calLast[Calendar.DAY_OF_MONTH]) > 0 && temp == 23) {
            log("", "isTwoMonthsDifference ON")
            true
        } else false
    }

    fun getDateLastTrx(): String? {
        val mTransactionTaxiDAO = TransactionDao()
        if (!mTransactionTaxiDAO.isOpen()) mTransactionTaxiDAO.open()
        val mesItems = mTransactionTaxiDAO.getTeleCollectTransactionByFlag(0)
        if (mTransactionTaxiDAO.isOpen()) mTransactionTaxiDAO.close()
        var dt: String? = ""
        dt = if (mesItems.isNotEmpty()) {
            mesItems[0].dateTransaction
        } else {
            null
        }
        return dt
    }

    open fun getFormatDateXLog(date: Date?): String? {
        val sdfDate2 = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
        val strDate2 = sdfDate2.format(date)
        Log.v("getDateComparison", "date String -> $strDate2")
        return strDate2
    } // eof getDateComparison

    /*fun setLocale(lang: String) {
        log(TAG, "New Lang: $lang")
        val locale = Locale(lang)
        Locale.setDefault(locale)
        val config = Configuration()
        config.locale = locale
        baseContext.resources.updateConfiguration(config, baseContext.resources.displayMetrics)

        prefs.selectedLanguage = lang
    }*/

    fun onEnableTouchEvents() {
        window.clearFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE)
    }

    open fun onDisableTouchEvents() {
        window.setFlags(
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE,
            WindowManager.LayoutParams.FLAG_NOT_TOUCHABLE
        )
    }

    open fun getTimestamp(): String? {
        return SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(Date())
    }

    open fun getPassword(businessShortCode: String, passkey: String, timestamp: String): String? {
        val str = businessShortCode + passkey + timestamp
        return Base64.encodeToString(str.toByteArray(), Base64.NO_WRAP)
    }

    //----------------------------------Recharge Task Functions---------------------------------//
    //----------------------------------Search Restriction Task Functions---------------------------------//
    open fun getProductDetailsByCode(productCode: String): ProductModel {
        var mProduct: ProductModel? = null
        try {
            val mProductsDao = ProductsDao()
            mProductsDao.open()
            mProduct = mProductsDao.getProductsByCategoryId(productCode)
            mProductsDao.close()
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return mProduct!!
    }

    open fun getProductDetailsByID(productId: String): ProductModel {
        var mProduct: ProductModel? = null
        try {
            val mProductsDao = ProductsDao()
            mProductsDao.open()
            mProduct = mProductsDao.getProductById(productId.toInt())
            mProductsDao.close()
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return mProduct!!
    }

    open fun getTerminalDetails(): TerminalModel {
        var mTerminal: TerminalModel? = null

        try {
            val mTerminalDAO = TerminalDao()
            if (!mTerminalDAO.isOpen()) mTerminalDAO.open()
            mTerminal = mTerminalDAO.getCurrent()!!
            if (mTerminalDAO.isOpen()) mTerminalDAO.close()
        } catch (e: SQLiteException) {
            e.printStackTrace()
        }
        return mTerminal!!
    }

    open fun gotoAbortMessageActivity(title: String, msg: String, ActivityToShow: Class<out Activity>,transactionModel:TransactionModel?=null) {
        try{
            if(BuildConfig.REBATE_DISCOUNT_REQUIRED && referenceModel!!.IMPLEMENT_DISCOUNT!! && transactionModel != null && !transactionModel.preAuthId.isNullOrEmpty() && referenceModel!!.is_send_transaction_online != null && referenceModel!!.is_send_transaction_online!!) {
                transactionModel.discountType = DISCOUNT_TYPE.REBATE_DISCOUNT
                transactionModel.amount = 0.0
                updateTransactionByReferenceId(transactionModel)
                val list = ArrayList<TransactionModel>()
                list.add(transactionModel)
                lifecycleScope.launch(Dispatchers.IO){
                    ScheduledTeleCollectService.sendTransactionOnline(SendTransactionModel(list, MainApp.sn))
                }
            }
            else if(transactionModel != null)
            {
                transactionModel.amount = 0.0
                updateTransactionByReferenceId(transactionModel)
            }
            val ctx = WeakReference(this).get()!!
            val dialog = Dialog(ctx)
            dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
            dialog.setCancelable(false)
            dialog.setContentView(R.layout.dialog_failed_message)
            dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
            val tvTitle = dialog.findViewById<TextView>(R.id.title)
            val tvMessage = dialog.findViewById<TextView>(R.id.message)
            val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

            tvTitle.text = title
            tvMessage.text = msg

            dialogButton.setOnClickListener {
                setBeep()
                val mIntent = Intent(ctx, ActivityToShow)
                startActivity(mIntent)
                finish()
            }

            dialog.show()
        } catch (e:java.lang.Exception) { }

    }

    private lateinit var abortDialog : Dialog
    open fun gotoAbortMessageActivity(title: String, msg: String?,transactionModel:TransactionModel?=null) {
        runOnUiThread{
            try {
                if(transactionModel != null && BuildConfig.REBATE_DISCOUNT_REQUIRED && referenceModel!!.IMPLEMENT_DISCOUNT!! && !transactionModel.preAuthId.isNullOrEmpty() && referenceModel!!.is_send_transaction_online != null && referenceModel!!.is_send_transaction_online!!) {
                    transactionModel.discountType = DISCOUNT_TYPE.REBATE_DISCOUNT
                    transactionModel.amount = 0.0
                    updateTransactionByReferenceId(transactionModel)
                    val list = ArrayList<TransactionModel>()
                    list.add(transactionModel)
                    lifecycleScope.launch(Dispatchers.IO){
                        ScheduledTeleCollectService.sendTransactionOnline(SendTransactionModel(list, MainApp.sn))
                    }
                }
                else if(transactionModel != null)
                {
                    transactionModel.amount = 0.0
                    updateTransactionByReferenceId(transactionModel)
                }
                if (!(this as Activity).isFinishing) {
                    val ctx = WeakReference(this).get()
                    abortDialog = Dialog(ctx!!)
                    abortDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                    abortDialog.setCancelable(false)
                    abortDialog.setContentView(R.layout.dialog_failed_message)
                    abortDialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
                    val tvTitle = abortDialog.findViewById<TextView>(R.id.title)
                    val tvMessage = abortDialog.findViewById<TextView>(R.id.message)
                    val dialogButton = abortDialog.findViewById<TextView>(R.id.action_done)

                    tvTitle.text = title
                    tvMessage.text = msg

                    val timer = object : CountDownTimer(10000, 1000) {
                        override fun onTick(millisUntilFinished: Long) {
                            //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                            //here you can have your logic to set text to edittext
                            //  println("Abort Activity Popup Time Remaining: $minutes:$seconds")
                        }

                        override fun onFinish() {
                            val unAttendantMode = terminalType == UN_ATTENDANT_MODE
                            if (unAttendantMode) {
                                setBeep()
                                prefs.saveIntentModel(null)
                                val mIntent: Intent =
                                    if (terminalType == UN_ATTENDANT_MODE) {
                                        Intent(this@BaseActivity, UnattendantModePayActivity::class.java)
                                    } else {
                                        Intent(this@BaseActivity, MenuActivity::class.java)
                                    }

                                mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
                                mIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                                startActivity(mIntent)
                                finish()
                            }
                        }
                    }
                    if(terminalType == UN_ATTENDANT_MODE)
                        timer.start()

                    dialogButton.setOnClickListener {
                        setBeep()
                        timer.cancel()
                        abortDialog.dismiss()
                        val mIntent: Intent =
                            if (terminalType == UN_ATTENDANT_MODE) {
                                Intent(this, UnattendantModePayActivity::class.java)
                            } else {
                                Intent(this, MenuActivity::class.java)
                            }

                        mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
                        mIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                        startActivity(mIntent)
                        finish()
                    }
                    abortDialog.show()
                }
            } catch (e:Exception){
                log(TAG, e.message+ExceptionUtils.getStackTrace(e))
                //e.printStackTrace()
            }
        }
    }
    open fun gotoAbortMessageActivity(title: String, msg: String?) {
        runOnUiThread{
            try {
                if (!(this as Activity).isFinishing) {
                    val ctx = WeakReference(this).get()
                    abortDialog = Dialog(ctx!!)
                    abortDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                    abortDialog.setCancelable(false)
                    abortDialog.setContentView(R.layout.dialog_failed_message)
                    abortDialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
                    val tvTitle = abortDialog.findViewById<TextView>(R.id.title)
                    val tvMessage = abortDialog.findViewById<TextView>(R.id.message)
                    val dialogButton = abortDialog.findViewById<TextView>(R.id.action_done)

                    tvTitle.text = title
                    tvMessage.text = msg

                    val timer = object : CountDownTimer(10000, 1000) {
                        override fun onTick(millisUntilFinished: Long) {
                            //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                            //here you can have your logic to set text to edittext
                            //  println("Abort Activity Popup Time Remaining: $minutes:$seconds")
                        }

                        override fun onFinish() {
                            val unAttendantMode = terminalType == UN_ATTENDANT_MODE
                            if (unAttendantMode) {
                                setBeep()
                                prefs.saveIntentModel(null)
                                val mIntent: Intent =
                                    if (terminalType == UN_ATTENDANT_MODE) {
                                        Intent(this@BaseActivity, UnattendantModePayActivity::class.java)
                                    } else {
                                        Intent(this@BaseActivity, MenuActivity::class.java)
                                    }
                                mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
                                mIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                                mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                                startActivity(mIntent)
                                finish()
                            }
                        }
                    }
                    if(terminalType == UN_ATTENDANT_MODE)
                        timer.start()

                    dialogButton.setOnClickListener {
                        setBeep()
                        timer.cancel()
                        abortDialog.dismiss()
                        val mIntent: Intent =
                            if (terminalType == UN_ATTENDANT_MODE) {
                                Intent(this, UnattendantModePayActivity::class.java)
                            } else {
                                Intent(this, MenuActivity::class.java)
                            }
                        mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
                        mIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                        mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
                        startActivity(mIntent)
                        finish()
                    }
                    if(!abortDialog.isShowing)
                    {
                        abortDialog.show()
                    }

                }
            } catch (e:Exception){
                log(TAG, e.message+ExceptionUtils.getStackTrace(e))
                //e.printStackTrace()
            }
        }
    }

    private lateinit var successDialog : Dialog
    open fun gotoSuccessMessageActivity(
        title: String,
        msg: String?,
        ActivityToShow: Class<out Activity?>
    ) {
        val ctx = WeakReference(this).get()
        successDialog = Dialog(ctx!!)
        successDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        successDialog.setCancelable(false)
        successDialog.setContentView(R.layout.dialog_success_message)
        successDialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
        val tvTitle = successDialog.findViewById<TextView>(R.id.title)
        val tvMessage = successDialog.findViewById<TextView>(R.id.message)
        val dialogButton = successDialog.findViewById<TextView>(R.id.action_done)

        tvTitle.text = title
        tvMessage.text = msg

        val timer = object : CountDownTimer(10000, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                //mTextField.setText("seconds remaining: " + millisUntilFinished / 1000);
                //here you can have your logic to set text to edittext
                //  println("Popup Time Remaining: $minutes:$seconds")
            }

            override fun onFinish() {
                successDialog.dismiss()
                val unAttendantMode = prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
                if (unAttendantMode) {
                    setBeep()
                    val mIntent = Intent(this@BaseActivity, ActivityToShow)
                    startActivity(mIntent)
                    finish()
                }
            }
        }
        if(prefs.getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE)
            timer.start()

        dialogButton.setOnClickListener {
            setBeep()
            successDialog.dismiss()
            timer.cancel()
            val mIntent = Intent(this, ActivityToShow)
            startActivity(mIntent)
            finish()
        }
        successDialog.show()

    }

    open fun gotoSuccessMessageActivity(title: String, msg: String?) {
       try{
           val dialog = Dialog(this)
           dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
           dialog.setCancelable(false)
           dialog.setContentView(R.layout.dialog_success_message)
           dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
           val tvTitle = dialog.findViewById<TextView>(R.id.title)
           val tvMessage = dialog.findViewById<TextView>(R.id.message)
           val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

           tvTitle.text = title
           tvMessage.text = msg

           dialogButton.setOnClickListener {
               setBeep()
               dialog.dismiss()
               val mIntent = Intent(this, MenuActivity::class.java)
               mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK
               mIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
               mIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
               startActivity(mIntent)
               finish()
           }
           dialog.show()
       } catch (e:Exception){
           e.printStackTrace()
       }
    }

    open fun log(tag: String?, msg: String) {
        lifecycleScope.launch(Dispatchers.IO){
            var date = SimpleDateFormat(
                "dd-MM-yyyy",
                Locale.getDefault()
            ).format(Calendar.getInstance().time)
            date = if (!prefs.logReferenceNo.isNullOrEmpty()) {
                prefs.logReferenceNo
            } else {
                val count = prefs.logCount
                "${date}_${count}"
            }
            if(prefs.isSendLogs == 1) {
                val logWriter = LogWriter(date.toString())
                logWriter.appendLog(tag, msg)
            }
            else
            {
                Log.i(TAG,msg)
            }
        }
    }

    fun checkGreyList(context: Context, mPan: String?): Boolean {
        /* var mListeGrise: GreyListModel? = null
        try {
            val mListeGriseDAO = GreyListDao(context)
            mListeGriseDAO.open()
            mListeGrise = mListeGriseDAO.getGreyListByPan(mPan!!)
            mListeGriseDAO.close()

        } catch (Ex: SQLiteException) {
            Ex.printStackTrace()
        }
        return mListeGrise != null*/
        var isGreyListAvailable = false
        val greyList = prefs.getGreyList()
        if (greyList != null && greyList.isNotEmpty()) {
            for (item in greyList) {
                if (item.pan!=null && item.pan.contains(mPan!!)) {
                    isGreyListAvailable = true
                    break
                }
            }
        }
        return isGreyListAvailable
    } // eof checkGreyList

    fun deletePanFromGreyList(mPan: String) {
        val greyList = prefs.getGreyList()
        if (greyList != null && greyList.isNotEmpty()) {
            for (item in greyList) {
                if (item.pan!=null && item.pan == mPan) {
                    greyList.remove(item)
                    break
                }
            }
        }
        prefs.saveGreyList(greyList!!)
    }

    fun deletePanFromBlackList(mPan: String) {
        var referenceModel1 = prefs.getReferenceModel()!!
        val blackList = referenceModel1.blacklist
        if (blackList != null && blackList.isNotEmpty()) {
            for (item in blackList) {
                if (item.pan!=null && item.pan.contains(mPan)) {
                    blackList.remove(item)
                    break
                }
            }
        }
        referenceModel1.blacklist = blackList
        prefs.saveReferenceModel(referenceModel1)
        referenceModel = referenceModel1
    }

    fun checkBlackList(c: Context, mPan: String?): Boolean {
        var isCardInBlackList = false
        val blackList = prefs.getReferenceModel()!!.blacklist
        if (blackList != null && blackList.isNotEmpty()) {
            for (item in blackList) {
                if (item.pan!=null && item.pan.contains(mPan!!)) {
                    isCardInBlackList = true
                    break
                }
            }
        }
        /*var mListeNoire: BlackListModel? = null
        try {
           // runOnUiThread {
                val mListeNoireDAO = BlackListDao()
                mListeNoireDAO.open()
                mListeNoire = mListeNoireDAO.selectionnerByPan(mPan!!)
                mListeNoireDAO.close()
           // }
        } catch (Ex: SQLiteException) {
            Ex.printStackTrace()
        }
        return mListeNoire != null
        */
        return isCardInBlackList
    } // eof checkBlackList

    //Card Functions
    fun assignKeyForCard(panNumber: String): String {
        var authKey = ""
        if (BuildConfig.POS_TYPE == "B_TPE")
            authKey = UtilsCardInfo.genKey(MainApp.mCore, panNumber)
        else if (BuildConfig.POS_TYPE == "PAX")
            authKey = UtilsCardInfo.genKeyPAX(panNumber)
        else {
            if (panNumber.endsWith("00148"))
                authKey = "6BB75C0B6EF18F44"
            else if (panNumber.endsWith("00028"))
                authKey = "11D85F74A9F8811E"
        }

        return authKey
    }

    fun setBeep() {
        lifecycleScope.launch(Dispatchers.IO){
            UtilsCardInfo.beep(MainApp.mCore, 10)
        }
    }

    fun formatMessage(message: String): String {
        var message = message
        message = if (message.contains("</ServiceResponse>")) {
            //split = response.split("</ServiceResponse>");
            "<ServiceResponse " + StringUtils.substringBetween(
                message,
                "<ServiceResponse",
                "</ServiceResponse>"
            ) + "</ServiceResponse>"
        } else {
            val msgArr =
                message.split("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />").toTypedArray()
            if (msgArr.size > 2) println(msgArr[0] + ", " + msgArr[1])
            if (!msgArr[0].contains("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />")) "<ServiceResponse>" + StringUtils.substringBetween(
                msgArr[0], "<ServiceResponse>", "</ServiceResponse>"
            ) + "</ServiceResponse>" else msgArr[0] + "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />"
        }
        return message
    }

    fun getAllProductsList(): ArrayList<ProductModel> {
        val mProduitDAO = ProductsDao()
        mProduitDAO.open()
        val ProductList = mProduitDAO.getAllProducts()
        mProduitDAO.close()
        return ProductList
    }


    fun hideKeyBoard() {
        var view = currentFocus
        if (view == null) {
            view = View(this)
        }
        val imm = getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    //region timer

    fun showErrorOnProgress(title: String?, message: String?) {
        showDialog(title, message)
    }
    var minutes = 0L
    var seconds = 0L

    fun printTicket(mTeleCollectData: TelecollectDataModel,isSuccessMessageRequired:Boolean=false) {
        try {
            var printerType = referenceModel!!.PRINTER_TYPE
           /* if(BuildConfig.DEBUG){
                printerType = AppConstant.DW14_PRINTER
            }*/

            if(printerType == AppConstant.DW14_PRINTER){
                dwPrintLayout(mTeleCollectData)
            } else {
                commonLayout(mTeleCollectData)
            }
        } catch (e: Exception) {
            log(TAG,"Exception ${e.message}")
        }
    }

    private fun commonLayout(mTeleCollectData: TelecollectDataModel){

        receipt = ReceiptBuilder(1200)
        var bitmap: Bitmap? = null
        try {
            bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap, 400, 400)
            receipt.setMargin(0, 0).setAlign(Paint.Align.CENTER).setColor(Color.BLACK)
                .addLine(180).setAlign(Paint.Align.CENTER).addParagraph().addImage(bitmap)

        } catch (e: java.lang.Exception) {
            log(TAG, "LOGO Not printed on receipt  ${e.message}")
        }

        receipt.setAlign(Paint.Align.CENTER).setTypeface(this, "fonts/Roboto-Bold.ttf")
        receipt.setTextSize(95f).addText(prefs.getStationModel()!!.name)
            .addText(prefs.getReferenceModel()!!.terminal!!.address)
            .addText(prefs.getReferenceModel()!!.terminal!!.city)
        receipt.setAlign(Paint.Align.CENTER).setTypeface(this, "fonts/Roboto-Regular.ttf")
            .setTextSize(85f).setAlign(Paint.Align.CENTER)
            .addText(Support.getDateTicket(Date())).addText("")
        receipt.setTextSize(85f).setAlign(Paint.Align.CENTER)
            .addText(getString(R.string.telecollecte_ok)).addText("")
        setAlignment(
            Paint.Align.LEFT,
            75f
        ).addText(
            "\n${getString(R.string.reference_label)} : ${
                mTeleCollectData.reference!!.substring(0, 15)
            }"
        )
        receipt.addText("\n${mTeleCollectData.reference!!.substring(15)}")
        receipt.addText("\n___________________________________").addText("")
        receipt.addText(getString(R.string.no_of_transaction) + mTeleCollectData.nbTransactionsTicket)
        receipt.addText(
            getString(R.string.total_amount_) + Support.getFormattedValue(
                this@BaseActivity,
                Support.getFormattedValue(
                    this@BaseActivity,
                    "${formatString(mTeleCollectData.totalTransactionsTicket)}"
                )
            )
        )
//                receipt.addText(getString(R.string.cancelled_trans) +mTeleCollectData.nombreAnnulationsTransactions)
        receipt.addText(getString(R.string.card_recharge) + mTeleCollectData.nbRechargesTicket)
        receipt.addText(
            getString(R.string.tota_recharge_amount) + Support.getFormattedValue(
                this@BaseActivity,
                "${mTeleCollectData.totalRechargesTicket}"
            )
        )
        receipt.addText(getString(R.string.loyalty_trans) + mTeleCollectData.nbTransactionsTaxis)
        receipt.addText(
            getString(R.string.loyalty_amount) + Support.getFormattedValue(
                this@BaseActivity,
                "${mTeleCollectData.totalTransactionsTaxis}"
            )
        ).addText("")
        receipt.addText("\n___________________________________").addText("")
        receipt.addText(getString(R.string.total_transactions_) + (mTeleCollectData.nbTransactionsTicket - mTeleCollectData.nombreAnnulationsTransactions).toInt())
        receipt.addText(
            getString(R.string.toatl_) + Support.getFormattedValue(
                this@BaseActivity, Support.getFormattedValue(
                    this@BaseActivity,
                    Support.getFormattedTotal(
                        mTeleCollectData.totalTransactionsTicket,
                        mTeleCollectData.totalTransactionsTaxis.toString(),
                        mTeleCollectData.totalAnnulationsTransactions
                    ).toString()
                )
            ) + " " + prefs.currency
        )
        receipt.addText("")
        receipt.addText("")
        val reciept = receipt.build()
        TicketPrinter(this).printReceipt(reciept)
    }
    private fun dwPrintLayout( mTeleCollectData: TelecollectDataModel) {
        val printCommands = ArrayList<PrintCmd>()
        receipt = ReceiptBuilder(1200)
        var bitmap: Bitmap? = null
        try {
            bitmap = BitmapFactory.decodeStream(FileInputStream(prefs.logoPath))
            bitmap = Support.getResizedBitmap(bitmap, 400, 400)
            printCommands.add(PrintCmd(bitmap, PrintContentType.IMAGE))
        } catch (e: java.lang.Exception) {
            log(TAG, "LOGO Not printed on receipt  ${e.message}")
        }

        printCommands.add(PrintCmd(referenceModel!!.COMPANY.name,AlignmentType.CENTER,true))
        printCommands.add(PrintCmd(prefs.getStationModel()!!.name,AlignmentType.CENTER,true))
        printCommands.add(PrintCmd(referenceModel!!.terminal!!.address+", "+referenceModel!!.terminal!!.city,AlignmentType.CENTER,true))
        printCommands.add(PrintCmd(Support.getDateTicket(Date()),AlignmentType.CENTER,true))
        printCommands.add(PrintCmd(getString(R.string.telecollecte_ok),AlignmentType.CENTER,true))

        printCommands.add(PrintCmd(getString(R.string.reference_label)+": "+mTeleCollectData.reference!!.substring(0, 15),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(mTeleCollectData.reference!!.substring(15),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd("------------------------",AlignmentType.CENTER,true))
        printCommands.add(PrintCmd(getString(R.string.no_of_transaction)+":[L]" + mTeleCollectData.nbTransactionsTicket,AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(getString(R.string.total_amount)+":[L]" + Support.getFormattedValue(this@BaseActivity, "${formatString(mTeleCollectData.totalTransactionsTicket)}"),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(getString(R.string.card_recharge)+":[L]" + mTeleCollectData.nbRechargesTicket,AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(getString(R.string.total_recharge_amount)+":[L]" +Support.getFormattedValue(this@BaseActivity, "${mTeleCollectData.totalRechargesTicket}"),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(getString(R.string.loyalty_trans)+":[L]" +Support.getFormattedValue(this@BaseActivity, "${mTeleCollectData.nbTransactionsTaxis}"),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(getString(R.string.loyalty_amount)+":[L]" + Support.getFormattedValue(this@BaseActivity, "${mTeleCollectData.totalTransactionsTaxis}"),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd("------------------------",AlignmentType.CENTER,true))
        printCommands.add(PrintCmd(getString(R.string.total_transactions_)+":[L]" + (mTeleCollectData.nbTransactionsTicket - mTeleCollectData.nombreAnnulationsTransactions),AlignmentType.LEFT,true))
        printCommands.add(PrintCmd(getString(R.string.toatl_)+":[L]"+Support.getFormattedValue(
            this@BaseActivity,
            Support.getFormattedTotal(
                mTeleCollectData.totalTransactionsTicket,
                mTeleCollectData.totalTransactionsTaxis.toString(),
                mTeleCollectData.totalAnnulationsTransactions
            ).toString()+" " + prefs.currency
        ),AlignmentType.LEFT,true))

        TicketPrinter(this).printTicket(printCommands)


    }

    //endregion

    private var isScheduleTelecollectEnabled = false
    /*fun enableScheduleTelecollect(enable:Boolean){
        isScheduleTelecollectEnabled = enable
        log(TAG, "isScheduleTelecollectEnabled: $enable")
        if(isScheduleTelecollectEnabled)
            startScheduledTelecollctReceiver()
        else
            stopScheduledTelecollctReceiver()
    }*/

    //endregion

    // LANDI PRINTER
    var printer: UPrinter? = null
    private val FONT_SIZE_SMALL = 0
    val FONT_SIZE_NORMAL = 1
    val FONT_SIZE_LARGE = 2
    fun printTextLandi(
        printer: UPrinter,
        text: String,
        fontSize: Int,
        alignMode: Int
    ) {

        // Print text with  font size
        setFontSpec(printer, fontSize)
        printer.addText(alignMode, text)
    }

    @Throws(RemoteException::class)
    private fun setFontSpec(printer: UPrinter, fontSpec: Int) {
        when (fontSpec) {
            FONT_SIZE_SMALL -> {
                printer.setHzSize(HZSize.DOT16x16)
                printer.setHzScale(HZScale.SC1x1)
                printer.setAscSize(ASCSize.DOT16x8)
                printer.setAscScale(ASCScale.SC1x1)
            }
            FONT_SIZE_NORMAL -> {
                printer.setHzSize(HZSize.DOT24x24)
                printer.setHzScale(HZScale.SC1x1)
                printer.setAscSize(ASCSize.DOT16x8)
                printer.setAscScale(ASCScale.SC1x1)
            }
            FONT_SIZE_LARGE -> {
                printer.setHzSize(HZSize.DOT24x24)
                printer.setHzScale(HZScale.SC1x2)
                printer.setAscSize(ASCSize.DOT16x8)
                printer.setAscScale(ASCScale.SC1x2)
            }
        }
    }

    lateinit var mPrinter: Printer
    fun testPrintImage() {
        var result = 0
        val inputStream: InputStream
        try {
            inputStream = if (BuildConfig.DEBUG) FileInputStream(
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                    .toString() + File.separator + AppConstant.LOGO_NAME + ".png"
            ) else FileInputStream(
                filesDir.toString() + File.separator + AppConstant.LOGO_NAME + ".png"
            )
            var bitmap = BitmapFactory.decodeStream(inputStream)
            bitmap = Bitmap.createScaledBitmap(bitmap, 128, 128, true)
            result =
                mPrinter.printImage(bitmap, 120, wangpos.sdk4.libbasebinder.Printer.Align.CENTER)
            log(TAG, result.toString())
            bitmap.recycle()
        } catch (ex: RemoteException) {
            ex.printStackTrace()
        } catch (ex: FileNotFoundException) {
            ex.printStackTrace()
        }
    }

    fun testPrintImage(
        image: String,
        c: Context
    ) {
        var result = 0
        try {
            val inputStream = c.assets.open(image)
            var bitmap = BitmapFactory.decodeStream(inputStream)
            bitmap = Bitmap.createScaledBitmap(bitmap, 256, 128, true)
            result =
                mPrinter.printImage(bitmap, 120, wangpos.sdk4.libbasebinder.Printer.Align.CENTER)
            log(TAG, result.toString())
            bitmap.recycle()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (ex: RemoteException) {
            ex.printStackTrace()
        }
    }

    fun getMyPrintDialog(): AlertDialog {
        val printerDialog = AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        printerDialog.setContentView(R.layout.dialog_printing)
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = printerDialog.window
        lp.copyFrom(window!!.attributes)

        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        printerDialog.setCancelable(false)
        printerDialog.setCanceledOnTouchOutside(false)
        return printerDialog
    }

    fun getFirebaseRegId() {
        FirebaseMessaging.getInstance().token
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    if (task.result != null && !TextUtils.isEmpty(task.result)) {
                        val token: String = task.result!!
                        prefs.fireBaseToken = token
                        log(TAG, "#### Firebase Reg Id ####$token")
                    }
                }
            }
    }

    open fun checkMemoryCardPermission() {

        if (prefs.isUseSdCard && prefs.getStringSharedPreferences(AppConstant.PREFERENCE_SDCARD_PATH)
                .isEmpty()
        ) { // ask permission to write data in Memory card
            MyMaterialDialog(
                applicationContext,
                getString(R.string.permission_required),
                getString(R.string.please_allow_sd_card_access_permission) + getString(R.string.app_name) + getString(
                    R.string.files),
                getString(R.string.allow),
                getString(R.string.cancel),
                object : MyMaterialDialogListener {
                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                    }

                    override fun onPositiveClick(dialog: MaterialDialog) {
                        try {
                            startActivityForResult(
                                Intent(Intent.ACTION_OPEN_DOCUMENT_TREE),
                                AppConstant.SDCARD_RESULT_CODE
                            )
                        } catch (e: java.lang.Exception) {
                            e.printStackTrace()
                        }
                    }
                })
        }
    }

    fun showNetworkDialog() {
        val dialogNetwork = AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogNetwork.setContentView(R.layout.dialog_network_not_available)
        // Grab the window of the dialog, and change the width
        val lp = WindowManager.LayoutParams()
        val window = dialogNetwork.window
        lp.copyFrom(window!!.attributes)
        val btnRetryFcc = dialogNetwork.findViewById<TextView>(R.id.btnRetryFcc)
        btnRetryFcc!!.setOnClickListener {
            dialogNetwork.dismiss()
        }
        // This makes the dialog take up the full width
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED,
            WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED
        )
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogNetwork.setCancelable(false)
        dialogNetwork.setCanceledOnTouchOutside(false)
    }


    inner class NetworkBroadCastReceiver : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            onInternetEnableDisable(intent.getBooleanExtra(AppConstant.INTENT_KEY_DATA, false))
        }
    }
    private lateinit var networkThread : Thread
    private fun registerNetworkBroadCastReceiver() {
        networkThread = Thread {
            if(!networkThread.isInterrupted) {
                networkBroadCastReceiver = NetworkBroadCastReceiver()
                if (networkBroadCastReceiver != null) {
                    val filter = IntentFilter(NetworkSchedulerService.BROADCAST_KEY_NETWORK_AVAILABILITY)
                    registerReceiver(networkBroadCastReceiver, filter)
                }
            }
        }
        networkThread.start()
    }

    open fun onInternetEnableDisable(status: Boolean) {
        log(TAG, "Network Status:: $status")
        /*if (status) {

        }*/

    }

    open fun goBackDialog(context: Context) {
        try {
            MyMaterialDialog(
                context,
                getString(R.string.confirm),
                getString(R.string.are_you_sure_to_cancel),
                getString(R.string.yes),
                getString(R.string.no),
                object : MyMaterialDialogListener {
                    override fun onPositiveClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                        gotoAbortMessageActivity(
                            getString(R.string.transaction_cancelled),
                            getString(R.string.customer_cancel_transaction_)
                        )
                    }

                    override fun onNegativeClick(dialog: MaterialDialog) {
                        dialog.dismiss()
                    }
                }
            )
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    /*fun getFuelPosModePayment(modePay: String?): String {
        return if (modePay != null) {
            when (modePay) {
                AppConstant.CARD_VALUE -> {
                    "FLEET CARD"
                }
                AppConstant.MOBILE_VALUE -> {
                    "MOBILE"
                }
                AppConstant.QRCODE_VALUE -> {
                    "QR"
                }
                AppConstant.VISA_VALUE -> {
                    "CARD"
                }
                AppConstant.RFID_VALUE -> {
                    "RFID"
                }
                AppConstant.SPLIT_PAYMENT -> {
                    "SPLIT"
                }
                AppConstant.DISCOUNT_PAYMENT -> {
                    "DISCOUNT"
                }
                AppConstant.BANK_VALUE -> {
                    "CARD"
                }
                AppConstant.LOYALTY_VALUE -> {
                    "LOYALTY"
                }
                else -> {
                    "CASH"
                }
            }
        } else {
            "CASH"
        }
    }*/
    fun getFuelPosModePaymentId(modePay: String?): String {
        val mopList = prefs.getReferenceModel()!!.mode_of_payment_list
        var paymentId = "01"
        if(!mopList.isNullOrEmpty()){
            if(modePay!!.length<2){
                paymentId = modePay.padStart(2, '0')
            }
        }
        return paymentId
    }
    fun getFuelPosModePayment(modePay: String?): String {
        val mopList = prefs.getReferenceModel()!!.mode_of_payment_list
        var paymentName = "CASH"
        if(!mopList.isNullOrEmpty()){
            for(payment in mopList){
                if("${payment.payment_id}" == modePay) {
                    paymentName = payment.payment_name!!.uppercase(Locale.US)
                }
            }
        }
        return paymentName
    }

    fun getModePayment(modePay: String?): String? {
        /*if (modePay != null) {
           when (modePay) {
               AppConstant.CARD_VALUE -> {
                   getString(R.string.card)
               }
               AppConstant.MOBILE_VALUE -> {
                   getString(R.string.mobile)
               }
               AppConstant.QRCODE_VALUE -> {
                   getString(R.string.qr_code)
               }
               AppConstant.VISA_VALUE -> {
                   getString(R.string.visa)
               }
               AppConstant.RFID_VALUE -> {
                   getString(R.string.rfid)
               }
               AppConstant.SPLIT_PAYMENT -> {
                   getString(R.string.split)
               }
               AppConstant.DISCOUNT_PAYMENT -> {
                   getString(R.string.discount)
               }
               AppConstant.BANK_VALUE -> {
                   getString(R.string.bank_card)
               }
               AppConstant.LOYALTY_VALUE -> {
                   getString(R.string.loyalty_card)
               }
               AppConstant.MTN_PAY_VALUE -> {
                   getString(R.string.mtn_pay)
               }
               else -> {
                   getString(R.string.cash)
               }
           }
       } else {
           getString(R.string.cash)
       }*/
        var modePayName = getString(R.string.cash)
        if(modePay != null) {
            if (referenceModel != null) {
                val modePayList = referenceModel!!.mode_of_payment_list!!
                for (mode in modePayList) {
                    if (modePay == "${mode.payment_id}") {
                        modePayName = mode.payment_name ?: getString(R.string.cash)
                        break
                    }
                }
            }
        }

        return modePayName
    }

    fun getPrinterStatusMsg(status: Int): String {
        when (status) {
            1 -> {
                return getString(R.string.printer_busy)
            }
            2 -> {
                return getString(R.string.out_of_paper)
            }
            3 -> {
                return getString(R.string.printer_format_error)
            }
            4 -> {
                return getString(R.string.printer_malfunction)
            }
            5 -> {
                return getString(R.string.printer_over_heat)
            }
            6 -> {
                return getString(R.string.printer_voltage_low)
            }
            7 -> {
                return getString(R.string.printer_unfinished)
            }
            8 -> {
                return getString(R.string.cut_jam_error)
            }
            9 -> {
                return getString(R.string.cover_open_error)
            }
            10 -> {
                return getString(R.string.printer_font_not_installed)
            }
            11 -> {
                return getString(R.string.data_package_long)
            }
            else -> {
                return getString(R.string.printer_error)
            }
        }

    }


    var referenceValue = ""


    fun isPackageInstalled(packageName: String): Boolean {
        return try {
            val pm = packageManager
            pm.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        }
    }

    fun appInstalledOrNot(/*context: Context,*/ uri: String): Boolean {
        val pm = this.packageManager
        try {
            pm.getPackageInfo(uri,PackageManager.GET_ACTIVITIES)
            return true
        } catch (e: PackageManager.NameNotFoundException) {
            e.printStackTrace()
        }
        return false
    }


    override fun onUserInteraction() {
        super.onUserInteraction()

        //idle screen timer
        try{
           if(referenceModel!=null){
               val unAttendantMode = referenceModel!!.TERMINAL_TYPE == UN_ATTENDANT_MODE
           }
        } catch (e:Exception){
            e.printStackTrace()
        }
    }

    override fun onDestroy() {
        sendEventsActions(Events.DESTROY_ACTION)
        try {
            if(::abortDialog.isInitialized){
                try { abortDialog.dismiss() } catch (e:Exception) {}
            }
            if(::successDialog.isInitialized){
                try { successDialog.dismiss() } catch (e:Exception) {}
            }
            if(networkBroadCastReceiver!=null){
                try{ unregisterReceiver(networkBroadCastReceiver) } catch (e:Exception) {}
            }
            if(restartApplicationReceiver!=null){
                try { unregisterReceiver(restartApplicationReceiver) } catch (e:Exception) {}
            }
            try {  preferenceThread.interrupt() } catch (e:Exception){}
            try { networkThread.interrupt() } catch (e:Exception){}
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }

        super.onDestroy()
    }

    override fun onStart() {
      sendEventsActions(Events.START_ACTION)
        super.onStart()
        init()
    }
    override fun onPause() {
        super.onPause()
    sendEventsActions(Events.PAUSE_ACTION)
       // 
    }
    override fun onResume() {
        super.onResume()
     sendEventsActions(Events.RESUME_ACTION)

    }

    override fun onStop() {
       sendEventsActions(Events.STOP_ACTION)
      /*  if(!prefs.isReferenceLoad) {
            startCountDownTimer()
        }*/
        super.onStop()
    }
    open fun isDateWithinRange(fromDate: String,toDate:String): Boolean {
        val d = sdf.format(Date())
        val currentDate = SimpleDateFormat("yyyyy-MM-dd").parse(d)
        val startDate = SimpleDateFormat("yyyyy-MM-dd").parse(fromDate)
        val endDate = SimpleDateFormat("yyyyy-MM-dd").parse(toDate)
        return currentDate!!.time >= startDate!!.time &&
                currentDate.time <= endDate!!.time
    }
    open fun isTimeWithinRange(
        fromTime: String?,
        toTime: String?
    ): Boolean {
        var isBetween = false
        try {
            val currentTime = sdf1.format(Date())
            log(TAG, "currentTime:: $currentTime")
            val startTime = SimpleDateFormat("HH:mm").parse(fromTime!!)
            val endTime = SimpleDateFormat("HH:mm").parse(toTime!!)
            val d = SimpleDateFormat("HH:mm").parse(currentTime)

            log(TAG, "d:: $d")
            log(TAG, "startTime:: $startTime")
            log(TAG, "endTime:: $endTime")
            if (startTime!!.before(d) && endTime!!.after(d)) {
                isBetween = true
            }
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        return isBetween
    }
    fun stringFormatDouble(double: Double):String
    {
        return String.format(Locale.US, "%.2f", double)
    }

    fun isCurrentDayAvailable(day:List<Int>):Boolean
    {
        val c: Calendar = Calendar.getInstance()
        val dayOfWeek: Int = c.get(Calendar.DAY_OF_WEEK)
        var currentDay = 0
        when (dayOfWeek) {
            Calendar.MONDAY -> {
                currentDay = 0
            }
            Calendar.TUESDAY  -> {
                currentDay = 1
            }
            Calendar.WEDNESDAY  -> {
                currentDay = 2
            }
            Calendar.THURSDAY  -> {
                currentDay = 3
            }
            Calendar.FRIDAY  -> {
                currentDay = 4
            }
            Calendar.SATURDAY  -> {
                currentDay = 5
            }
            Calendar.SUNDAY -> {
                currentDay = 6
            }
        }

        log(TAG, "Current Day :: $currentDay")
        return day.contains(currentDay)
    }

    var printerStatusMessage= ""
    fun isPrinterPaperAvailable(): Boolean{
        printerStatusMessage =  getString(R.string.paper_not_available_in_printer)
        when(BuildConfig.POS_TYPE){
            "B_TPE"-> {
                val wPosPrinter = Printer(this)
                val wPosResult = wPosPrinter.printInit()
                val status = intArrayOf(1,2)
                val statusCode = wPosPrinter.getPrinterStatus(status)
                Support.log(TAG, "Printer Status:: $statusCode")
                return if(statusCode == 0) {
                    true
                } else {
                    printerStatusMessage = getString(R.string.out_of_paper)
                    Support.log(TAG, "Printer Status: $status")
                    false
                }
            }
            "PAX" -> {
                when {
                    MainApp.deviceName.contains("A920") -> {
                        PrinterTester.getInstance().init()
                        return if(PrinterTester.getInstance().status == 0) {
                            true
                        } else {
                            val status = PrinterTester.getInstance().status
                            printerStatusMessage = PrinterTester.getInstance().statusCode2Str(status)
                            Support.log(TAG, "Printer Status: $status")
                            false
                        }
                    }
                    MainApp.deviceName.contains("IM30") -> {
                        //todo add paper station of printer here
                        return true //for testing purpose
                    }
                    else -> {
                        return true
                    }
                }
            }
            else -> {
                //todo add printer paper status here
                return true //for testing purpose
            }
        }
    }
    fun setAlignment(align: Paint.Align,size: Float):ReceiptBuilder
    {
        return if(LocaleManager.LANGUAGE_ARABIC == LocaleManager.getLanguage(this)) {
            when (align) {
                Paint.Align.RIGHT -> {
                    receipt.setAlign(Paint.Align.LEFT).setTextSize(size).setTypeface(this, "fonts/Roboto-Bold.ttf")
                }
                Paint.Align.LEFT -> {
                    receipt.setAlign(Paint.Align.RIGHT).setTextSize(size).setTypeface(this, "fonts/Roboto-Bold.ttf")
                }
                else -> {
                    receipt.setAlign(align).setTextSize(size).setTypeface(this, "fonts/Roboto-Bold.ttf")
                }
            }
        } else {
            receipt.setAlign(align).setTextSize(size).setTypeface(this, "fonts/Roboto-Regular.ttf")
        }
    }

    fun generateLogs(message:String,isErrorLog:Int) {
        val model =CommonViewModel(longApiService,prefs)
        model.generateLogs(message,isErrorLog)

    }
    //____________________________INJECT KEY START_________________________
    //private var mKey: Key? = null
    private val CertData = ByteArray(8)
    private var ret = -1
    private val checkval = ByteArray(1) //TLK no check
    fun importInjectKey(result: InjectKeyResponseModel?) {
        lifecycleScope.launch(Dispatchers.IO){
            try {

//        result format : {"id":"1","c1":"04F454203DB35475B65B51F40470BF5D","c2":"B65461AE97FD9B0DE986312985BC7946","c3":"68E640A1A45B08E68CD94FBA072FF1E9","r1":"","r2":"","r3":""}
                if (result != null) {
                    try {

                        val C1 = result.c1
                        val C2 = result.c2
                        val C3 = result.c3
                        if (BuildConfig.POS_TYPE == AppConstant.B_TPE)
                            importTLK(result)
                        else if (BuildConfig.POS_TYPE == AppConstant.PAX) {
                            val xorKeys: String = xorKEYS(C1, C2, C3)
                            val res: Boolean = genPAXkey(xorKeys)
                            if (res) {
                                prefs.isKeyImported = true
                                log(TAG,"TLK import success")
                            } else {
                                prefs.isKeyImported = false
                                log(TAG,"TLK import fail")

                            }
                        }
                    } catch (e: Exception) {
                        prefs.isKeyImported = false
                        generateLogs("Failed to Import Inject Key",1)
                        e.printStackTrace()
                    }
                } else {
                    prefs.isKeyImported = false
                    generateLogs("Failed to Import Inject Key",1)
                    log(TAG,"Error connexion to server. Empty data !!")
                }
            }
            catch (e:Exception)
            {
                log(TAG, e.message+ExceptionUtils.getStackTrace(e))
                log(TAG, "Failed to Import Inject Key :: "+e.printStackTrace())
                generateLogs("Failed to Import Inject Key",1)
                e.printStackTrace()
            }
        }
    }

    var mKey : Key? = null
    fun getKeyContext(){
        Thread{
            mKey = Key(applicationContext)
        }.start()

    }


    fun importTLK(result: InjectKeyResponseModel) {
        try {
            val key: ByteArray = ByteUtil.hexString2Bytes(AppConstant.TLK) //TLK
            val checkval = ByteArray(1) //TLK no check
            try {
                ret = mKey!!.updateKeyEx(
                    Key.KEY_REQUEST_TLK.toInt(),
                    Key.KEY_PROTECT_ZERO.toInt(),
                    CertData,
                    key,
                    false,
                    checkval.size,
                    checkval,
                    AppConstant.TPE_APP,
                    AppConstant.TPE_APP_id

                )
            } catch (e: RemoteException) {
                prefs.isKeyImported = false
                e.printStackTrace()
                log(TAG,e.message+" "+e.cause)
                generateLogs("Failed to Import Inject Key",1)
            }
            if (ret == 0) {
                prefs.isKeyImported = true
                log(TAG,"TLK import success")
                generateLogs("Failed to Import Inject Key",1)

            } else {
                prefs.isKeyImported = false
                log(TAG,"TLK import fail code : $ret")
                generateLogs("Failed to Import Inject Key",1)
                return
            }
        }
        catch (e:Exception)
        {
            log(TAG, e.message+ExceptionUtils.getStackTrace(e))
            prefs.isKeyImported = false
            e.printStackTrace()
            generateLogs("Failed to Import Inject Key",1)
        }
        importTMK(result)
    }
    fun importTMK(result: InjectKeyResponseModel) {
        try {
            val tMKEncrypt = Encryptor.encrypt(AppConstant.TMK, AppConstant.TLK)
            val key = ByteUtil.hexString2Bytes(tMKEncrypt) //TMK
            val CertData = ByteArray(8) //Reserved.
            var ret = -1
            try {

                ret = mKey!!.updateKeyEx(
                    Key.KEY_REQUEST_TMK.toInt(),
                    Key.KEY_PROTECT_TLK.toInt(),
                    CertData,
                    key,
                    false,
                    checkval.size, checkval,
                    AppConstant.TPE_APP,
                    AppConstant.TPE_APP_id
                )
            } catch (e: RemoteException) {
                e.printStackTrace()
                log(TAG,e.message+" "+e.cause)
                generateLogs("Failed to Import Inject Key",1)
            }
            if (ret == 0) {
                prefs.isKeyImported = true
                log(TAG,"TLK import success")
                generateLogs("Failed to Import Inject Key",1)
            } else {
                prefs.isKeyImported = false
                log(TAG,"TLK import fail code : $ret")
                generateLogs("Failed to Import Inject Key",1)
                return
            }
        } catch (ex: Exception) {
            prefs.isKeyImported = false
            Toast.makeText(applicationContext, "Exception $ex", Toast.LENGTH_LONG).show()
            generateLogs("Failed to Import Inject Key",1)
        }
        importDEK(result)
    }
    fun importDEK(result: InjectKeyResponseModel) {
        //DEK
        val DEKPLAIN = xorKEYS(result.c1,result.c2,result.c3)
        val DEKEncrypt = Encryptor.encrypt(DEKPLAIN, AppConstant.TMK) //DEK
        val dekCheckVal = "675F8A"
        val key = ByteUtil.hexString2Bytes(DEKEncrypt) //DEK
        val checkval = ByteUtil.hexString2Bytes(dekCheckVal)
        println(DEKPLAIN) // DA46752F0E15C79ED3042F6786E337F2
        var ret = -1
        try {
            ret = mKey!!.updateKeyEx(
                Key.KEY_REQUEST_DEK.toInt(),
                Key.KEY_PROTECT_TMK.toInt(),
                CertData,
                key,
                false,
                checkval.size,
                checkval,
                AppConstant.TPE_APP,
                AppConstant.TPE_APP_id
            )
        } catch (e: RemoteException) {
            prefs.isKeyImported = false
            e.printStackTrace()
            log(TAG,e.message+" "+e.cause)
            generateLogs("Failed to Import Inject Key",1)
        }
        if (ret == 0) {
            prefs.isKeyImported = true
            log(TAG,"TLK import success")
            generateLogs("Failed to Import Inject Key",1)

        } else {
            prefs.isKeyImported = false
            log(TAG,"TLK import fail code : $ret")
            generateLogs("Failed to Import Inject Key",1)
            return
        }
    }

    fun xorKEYS(C1: String, C2: String, C3: String): String {
        val C1parity: ByteArray = ByteUtil.hexString2Bytes(C1)
        val C2parity: ByteArray = ByteUtil.hexString2Bytes(C2)
        val C3parity: ByteArray = ByteUtil.hexString2Bytes(C3)
        val R1: ByteArray = ByteUtil.xor(C1parity, C2parity)
        val R2: ByteArray = ByteUtil.xor(R1, C3parity)
        val DEKPLAIN: String = ByteUtil.bytes2HexString(R2)
        println(DEKPLAIN)
        return DEKPLAIN
    }

    fun genPAXkey(keyMaster: String): Boolean {
        var result =false
        try {
            result  = PedTester.getInstance(EPedType.INTERNAL).writeKey(
                EPedKeyType.TLK,
                0x00.toByte(), EPedKeyType.TDK,
                3.toByte(), ByteUtil.hexStringToByteArray(keyMaster), ECheckMode.KCV_NONE, null
            )
            val tdkKcv: ByteArray = PedTester.getInstance(EPedType.INTERNAL).kcV_TDK
            log("TDK KCV", "TDK KCV : " + ByteUtil.ByteArraytoHexString(tdkKcv))
        }
        catch (e:Exception)
        {
            log(TAG, e.message+ExceptionUtils.getStackTrace(e))
            e.printStackTrace()
        }

        return result
    }

    //____________________________INJECT KEY END___________________________

    open fun isExpired(mToday: Date, dateExp: String): Boolean {
        val sdfDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
        var expDate: Date? = null
        try {
            expDate = sdfDate.parse(dateExp)
        } catch (e: ParseException) {
            e.printStackTrace()
        }
        if (mToday.after(expDate)) {
            log(TAG,"App Expired")
            generateLogs("App Expired",1)
            return true
        }
        if (mToday.before(expDate)) {
            log(TAG,"App Not Expired")
            return false
        }
        return true
    }
    fun checkAppExpiry():Boolean
    {
        return if(prefs.getReferenceModel() != null && prefs.getReferenceModel()!!.app_expiry_date != null) {
            var today = Support.getDateComparison(Date())
//            if(BuildConfig.DEBUG)
//            {
//                val dtStart = "2023-05-11"
//                val format = SimpleDateFormat("yyyy-MM-dd")
//                try {
//                    today = format.parse(dtStart)
//                } catch (e: ParseException) {
//                    e.printStackTrace()
//                }
//            }

            isExpired(today!!,prefs.getReferenceModel()!!.app_expiry_date!!)
        } else {
            false
        }
    }

    fun getFiscalErrorMessage(code:String):String
    {
        when (code) {
            "0100" -> {
                return "PIN OK" //	This code indicates that the provided PIN code is correct.
            }
            "1500" -> {
                return "PIN code required"	//If the credentials are invalid.
            }
            "2100" -> {
                return "PIN not OK"	//PIN code sent by the POS is invalid
            }
            "1300" -> {
                return "Smart Card is not present"	//Secure Element card is not inserted in the FD smart card reader.
            }
            "2220" -> {
                return "SE Communication Failed	FD cannot connect to the Secure Element applet"
            }
            "2310" -> {
                return "Invalid hscode"	//Hs code sent by the POS are not defined.
            }
            "2800" -> {
                return	"Field required" //A field is required (a mandatory invoice request field is missing).
            }
            "2801" -> {
                return "Field value too long"	//The length of the field value is longer than expected.
            }
            "2802" -> {
                return "Field value too short"	//The length of the field value is shorter than expected.
            }
            "2803" -> {
                return "Invalid field length"	//The length of the field value is shorter or longer than expected.
            }
            "2804" -> {
                return "Field out of range"	//A field value is out of expected range.
            }
            "2805" -> {
                return "Invalid field value"	//A field contains an invalid value.
            }
            "2806" -> {
                return "Invalid data format"	//The data format is invalid.
            }
            "2807" -> {
                return "List too short. The list of items or the list of hscode in the invoice request does not contain at least one element (item/hscode)."
            }
            "2808" -> {
                return "List too long. The list of items or the list of hscode in the invoice request exceeds the maximum allowed number of elements (items/hscode) or byte size."
            }
            "2900" -> {
                return "Device is not configured.Тhe device must be registered on the TIMS server."
            }
            else ->
            {
                return "Invalid PIN"
            }

        }

    }
     fun showProgressRight(button: Button) {
        button.showProgress {
            buttonTextRes = R.string.loading
            progressColor = Color.GREEN
        }
        button.isEnabled = false
    }
     fun hideProgressRight(button: Button) {
        button.isEnabled = true
        button.hideProgress(R.string.submit)
    }


    private inner class SendDbTimerTask :  TimerTask(){
        override fun run() {
            val prefs = MainApp.getPrefs()
            val path = Environment.getExternalStorageDirectory().path
            val dbPath = path+ AppConstant.EXTERNAL_STORAGE_DB_PATH
            val dbFile = File(dbPath)
            val dbKey = prefs.backupDbKey
            val dbFileName = MainDataBaseClass.DatabaseFileNameV3
            if(dbFile.exists()) {
                val key = Support.generateMD5("abcde${MainApp.sn}fghij")
                val message = """{
                "fileType" : "Database backup file",
                "fileKey" : "$dbKey",
                "fileName" : "$dbFileName"
            }""".trimIndent()
                val model = CommonViewModel(longApiService,prefs)
                model.sendLogFiles(dbFile,key!!,message,0)
                showToast(MainApp.appContext.getString(R.string.database_file_will_upload_shortly))
            }
            else {
                showToast(MainApp.appContext.getString(R.string.database_file_not_exists))
            }
        }
    }

    fun sendDbFile(){
        log(TAG,"DB BACKUP WILL UPLOAD IN 5 Seconds")
        /*Timer().schedule(5000) {
            val path = Environment.getExternalStorageDirectory().path
            val dbPath = path+ AppConstant.EXTERNAL_STORAGE_DB_PATH
            val dbFile = File(dbPath)
            val dbKey = prefs.backupDbKey
            val dbFileName = MainDataBaseClass.DatabaseFileNameV3
            if(dbFile.exists()) {
                val key = Support.generateMD5("abcde${MainApp.sn}fghij")
                val message = """{
                "fileType" : "Database backup file",
                "fileKey" : "$dbKey",
                "fileName" : "$dbFileName"
            }""".trimIndent()
                val model = CommonViewModel(longApiService,prefs)
                model.sendLogFiles(dbFile,key!!,message,0)
                showToast(getString(R.string.database_file_will_upload_shortly))
            }
            else {
                showToast(getString(R.string.database_file_not_exists))
            }
        }*/

        Timer().schedule(SendDbTimerTask(),5000)
    }

fun updateTransactionByReferenceId(mTransaction:TransactionModel)
{
    Handler(Looper.getMainLooper()).post {
        try {
            val mTransactionDAO = TransactionDao()
            mTransactionDAO.open()
            mTransactionDAO.updateTransactionsByReferenceID(mTransaction)
            mTransactionDAO.close()
            log(TAG, "Updated Transaction Data:: ${gson.toJson(mTransaction)}")
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
            showDialog(
                getString(R.string.error),
                getString(R.string.failed_to_update_transaction_data)
            )

        }
    }
}
    fun insertTransactionData(mTransaction:TransactionModel)
    {
        try {
            val mTransactionDAO = TransactionDao()
            mTransactionDAO.open()
            mTransactionDAO.insertTransaction(mTransaction)
            mTransactionDAO.close()
            log(TAG, "Inserted Transaction Data:: ${gson.toJson(mTransaction)}")
        }
        catch (e: java.lang.Exception)
        {
            e.printStackTrace()
            showDialog(getString(R.string.error), getString(R.string.failed_to_update_transaction_data))
        }
    }
    open fun getDateFromTimeZOne(timezone: String?): Date? {
        val c = Calendar.getInstance()
        val date = c.time //current date and time in UTC
        val df = SimpleDateFormat("yyyy-MM-dd")
        df.timeZone = TimeZone.getTimeZone(timezone) //format in given timezone
        val date1 = df.parse(df.format(date))
        return date1
    }
    private fun restartFccService(referenceModel: ReferenceModel) {
        if(referenceModel.FUSION!!.EXIST) {
            if(FusionService.isRunning(this))
                FusionService.stop(this)

            Handler(Looper.getMainLooper()).postDelayed({
                FusionService.start(this)
            },5000)
        }
        else if(referenceModel.FUELPOS != null && referenceModel.FUELPOS.isExist) {

            if(FuelPosService.isRunning(this))
                FuelPosService.stop(this)

            Handler(Looper.getMainLooper()).postDelayed({
                FuelPosService.start(this)
            },5000)
        }
    }

     fun dialogShowFCCAlert(isFusionExist:Boolean=true) {
        val dialogFCC = androidx.appcompat.app.AlertDialog.Builder(this, R.style.MyStyleDialog).show()
        dialogFCC.setContentView(R.layout.dialog_fcc_not_connected)
        val btnRetryFcc = dialogFCC.findViewById<TextView>(R.id.btnRetryFcc)
        btnRetryFcc!!.setOnClickListener {
            //dialogFCC.dismiss()
            val connectivity =  Connectivity.isNetworkAvailable(this)
            if(connectivity){
                if (isFusionExist) {
                    if(!FusionService.isRunning(this))
                        FusionService.start(this)

                    val fccConnected =  FusionService.fccConnected()
                    if(!fccConnected) {
                        FusionService.connectFcc(this)
                    }
                }
                else
                {
                    if(!FuelPosService.isRunning(this))
                        FuelPosService.start(this)
                        FuelPosService.restartFuelPosEpr()
                }
                dialogFCC.dismiss()
            } else {
                showToast(getString(R.string.please_check_internet_connection))
            }
        }
        val lp = WindowManager.LayoutParams()
        val window = dialogFCC.window
        lp.copyFrom(window!!.attributes)
        lp.width = WindowManager.LayoutParams.WRAP_CONTENT
        lp.height = WindowManager.LayoutParams.WRAP_CONTENT
        window.setFlags(WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED, WindowManager.LayoutParams.FLAG_HARDWARE_ACCELERATED)
        window.attributes = lp
        window.setBackgroundDrawable(resources.getDrawable(R.color.tranparent))
        dialogFCC.setCancelable(false)
        dialogFCC.setCanceledOnTouchOutside(false)

    }
    fun restartApplicationIdle() {
        prefs.isFirstTime = true
        prefs.isRestartApplication = "true"
        prefs.isCurrentActivityisMenu = false
        //sending broadcast to launcher app
        val intent = Intent()
        intent.action = BuildConfig.APPLICATION_ID
        intent.putExtra("isShowLoader", "true")
        intent.putExtra("message", getString(R.string.please_wait_saving_transaction_details))
        intent.addFlags(Intent.FLAG_INCLUDE_STOPPED_PACKAGES)
        intent.component = ComponentName("app.rht.pax.mylauncher", "app.rht.pax.mylauncher.service.BroadCastReceiver")
        sendBroadcast(intent)

        val mIntent = Intent(applicationContext, SplashScreenActivity::class.java)
        val mPendingIntentId = 123456
        val mPendingIntent = PendingIntent.getActivity(
            applicationContext,
            mPendingIntentId,
            mIntent,
            PendingIntent.FLAG_CANCEL_CURRENT
        )
        val mgr = applicationContext.getSystemService(ALARM_SERVICE) as AlarmManager
        mgr[AlarmManager.RTC, System.currentTimeMillis() + 3000] = mPendingIntent
        exitProcess(0)
    }
    fun disableNavigationBar()
    {
        if(BuildConfig.POS_TYPE == "B_TPE")
            enableWoPosNavBar(false)
        prefs.saveBooleanSharedPreferences(AppConstant.DEVICE_NAVIGATION_BAR, false)
    }
    fun enableNavigationBar()
    {
        if(BuildConfig.POS_TYPE == "B_TPE")
            enableWoPosNavBar(true)
        prefs.saveBooleanSharedPreferences(AppConstant.DEVICE_NAVIGATION_BAR, true)

    }
     fun enableWoPosNavBar(enabled:Boolean) {
        try {
            val mode = if(enabled) 0 else 1
            val wangPosManagerClass = Class.forName("android.os.WangPosManager")
            val wangPosManager = wangPosManagerClass.newInstance()
            val navBarMode = wangPosManagerClass.getMethod("setScreenMode", Int::class.javaPrimitiveType)
            navBarMode.invoke(wangPosManager, mode)
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }
     fun setupTestFairy(from:String="") {
         try {
             val referenceModel = prefs.getReferenceModel()
             var testFairyEnabled = false
             var testFairyToken = "SDK-yI8L1qcQ"
             if (referenceModel != null && referenceModel.enableTestFairy!! && !referenceModel.testFairyToken.isNullOrEmpty()) {
                 testFairyEnabled = true
                 testFairyToken = referenceModel.testFairyToken!!
             }
             if (testFairyEnabled) {
                 TestFairy.stop()
                 TestFairy.begin(this, testFairyToken)
                 if (prefs.logReferenceNo.isEmpty()) {
                     TestFairy.setUserId(from)
                 } else {
                     TestFairy.setUserId("TRX${prefs.logReferenceNo}")
                 }
                 TestFairy.setAttribute("serialNumber", MainApp.sn)
             }
         } catch (e: Exception) {
             e.printStackTrace()
         }
     }
    fun resetPaymentValues(tag:String)
    {
        prefs.isPaymentDone = false
        prefs.isPaymentMethodClicked  = false
        prefs.isTransactionCreated = false
        prefs.savePumpModel(null)
        prefs.saveNozzleClicked(false)
        prefs.saveIntentModel(null)
        prefs.saveNozzleModel(null)
        prefs.isPumpError = false
        prefs.isPowerCutGetFuelSaleTrxMsgSent = false
        prefs.saveFuellingStates(null)
        prefs.mCurrentActivity = ""
        log(tag,"paymentValuesResetDone")
    }
    fun resetPumpHistory(tag:String)
    {
        prefs.savePumpModel(null)
        prefs.saveNozzleClicked(false)
        prefs.saveIntentModel(null)
        prefs.saveNozzleModel(null)
        prefs.isPumpError = false
        prefs.isPowerCutGetFuelSaleTrxMsgSent = false
        prefs.saveFuellingStates(null)
        prefs.mCurrentActivity = ""
        log(tag,"paymentValuesResetDone")
    }
}
