package app.rht.petrolcard.baseClasses.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.StringRes
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.RecyclerView
import app.rht.petrolcard.BR
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.model.BaseModel
import app.rht.petrolcard.interfaces.OnItemRecyclerViewListener
import app.rht.petrolcard.interfaces.OnTextChangeWithTagListener
import app.rht.petrolcard.ui.loyalty.model.ChildLoyaltyCardModel
import app.rht.petrolcard.ui.loyalty.model.LoyaltyGift
import app.rht.petrolcard.ui.loyalty.model.LoyaltyGiftHistory
import app.rht.petrolcard.ui.modepay.activity.UnattendantModePayActivity
import app.rht.petrolcard.ui.modepay.model.ModePaymentModel
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.settings.card.common.activity.ManageUninstallActivity
import app.rht.petrolcard.ui.settings.card.history.model.HistoryItemModel
import app.rht.petrolcard.ui.settings.card.common.model.CardItemModel
import app.rht.petrolcard.ui.settings.card.pendingtrx.model.PendingRefundModel
import app.rht.petrolcard.ui.settings.common.model.SettingItemModel
import app.rht.petrolcard.ui.settings.operations.activity.DisputedTransactionActivity
import app.rht.petrolcard.ui.settings.operations.model.DuplicateTransactionModel
import app.rht.petrolcard.ui.settings.operations.model.PendingTransactionModel
import app.rht.petrolcard.ui.transactionlist.model.OfflineProductsModel
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc
import java.lang.ref.WeakReference

import java.util.*
import kotlin.collections.ArrayList

/**
 * Created by Admin on 01-09-2016.
 */
open class RecyclerViewArrayAdapter<T : BaseModel>(
    private val mObjects: List<T>,
    onItemClickListener: OnItemClickListener<T>
) : RecyclerView.Adapter<RecyclerViewArrayAdapter.MyViewHolder>() {

    private var selectionMode = ListSelectionMode.NONE
    private val onItemClickListener: OnItemClickListener<T>?
    private var emptyTextView: TextView? = null
    private var emptyViewText = R.string.default_empty_list_info
    private var fragment: Fragment? = null
    private var context: Context? = null
    private var onDemand: Boolean = false
    var onItemRecyclerViewListener: OnItemRecyclerViewListener? = null
    var onTextChangeWithTagListener: OnTextChangeWithTagListener? = null
    var listItemCount = 0


    fun setTextChangeWithTagListener(onTextChangeWithTagListener: OnTextChangeWithTagListener?) {
        this.onTextChangeWithTagListener = onTextChangeWithTagListener
    }
    override fun getItemCount(): Int {
        if (emptyTextView != null) {
            if (mObjects.size == 0) {
                emptyTextView!!.visibility = View.VISIBLE
                emptyTextView!!.setText(emptyViewText)
            } else {
                emptyTextView!!.visibility = View.INVISIBLE
            }
        }
        return if(listItemCount == 0)
            mObjects.size
        else
            listItemCount
    }


    fun setItemCount(count : Int){
        listItemCount= count
    }



    /* override fun getItemCount(): Int {
         return mObjects.size
     }*/

    val selectedItems: ArrayList<T>
        get() {
            val selectedItems = ArrayList<T>()
            for (mObject in mObjects) {
                if (mObject.isvalueSelected) {
                    selectedItems.add(mObject)
                }
            }
            return selectedItems
        }

    fun getmObjects(): List<T> {
        return mObjects
    }

    fun setEmptyTextView(emptyTextView: TextView, @StringRes emptyViewText: Int) {
        this.emptyTextView = emptyTextView
        this.emptyViewText = emptyViewText
    }

    fun setSelectedItems(previouslySelectedItems: List<T>) {
        for (mObject in mObjects) {
            mObject.isvalueSelected = previouslySelectedItems.contains(mObject)
        }
    }

    fun setContext(context: Context) {
        val ctx = WeakReference(context).get()!!
        this.context = ctx
    }

    fun setOnDemand(onDemand: Boolean) {
        this.onDemand = onDemand
    }

    fun setSelectionMode(selectionMode: ListSelectionMode) {
        this.selectionMode = selectionMode
    }

    fun setFragment(fragment: Fragment) {
        this.fragment = fragment
    }

    interface OnItemClickListener<T> {

        fun onItemClick(view: View, `object`: T)
    }

    init {
        this.selectionMode = selectionMode
        this.onItemClickListener = WeakReference(onItemClickListener).get()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): MyViewHolder {
        val binding: ViewDataBinding = DataBindingUtil.inflate(LayoutInflater.from(parent.context), viewType, parent, false)
        return MyViewHolder(binding)
    }

    override fun onBindViewHolder(holder: MyViewHolder, position: Int) {
        holder.binding.setVariable(BR.item, getItem(position))
        holder.binding.setVariable(BR.itemClickListener, this.onItemClickListener)
    }

    override fun getItemViewType(position: Int): Int {
        if (mObjects[position] is CategoryListModel) {
            return R.layout.item_category_menu
        }
        else if (mObjects[position] is TransactionFromFcc) {
            return R.layout.item_transaction_list
        }
        else if (mObjects[position] is ModePaymentModel) {
            return if(context is UnattendantModePayActivity) {
                R.layout.item_unattendant_modepay
            } else {
                R.layout.item_modepay
            }
        }
        else if (mObjects[position] is SettingItemModel) {
            return R.layout.item_setting_card
        }
        else if (mObjects[position] is CardItemModel) {
            return R.layout.item_manage_card
        }
        else if (mObjects[position] is OfflineProductsModel) {
            return R.layout.item_offline_transaction_list
        }
        else if (mObjects[position] is PumpsModel) {
            return R.layout.item_pump_list
        }
        else if (mObjects[position] is NozzelsModel) {
            return R.layout.item_product_list
        }
        else if (mObjects[position] is LoyaltyGift) {
            return R.layout.item_loyalty_gift
        }
        else if (mObjects[position] is LoyaltyGiftHistory) {
            return R.layout.item_loyalty_gift_history
        }
        else if (mObjects[position] is SubProduct) {
            return R.layout.item_service_list
        }
        else if (mObjects[position] is HistoryItemModel) {
            return R.layout.item_history
        }
        else if (mObjects[position] is ChildLoyaltyCardModel) {
            return R.layout.item_child_loyalty_card_item
        }
        else if (mObjects[position] is DuplicateTransactionModel) {
            return R.layout.item_duplicate_transactions
        }
        else if (mObjects[position] is PendingTransactionModel) {
            return if(context is DisputedTransactionActivity) {
                R.layout.item_disputed_trx
            } else {
                R.layout.item_pending_transaction
            }

        }
        else if (mObjects[position] is PendingRefundModel) {
            return R.layout.item_refund_status
        }
        return -1
    }

    /**
     * Adds the specified object at the end of the array.
     *
     * @param object The object to add at the end of the array.
     */
    fun add(`object`: T) {
        mObjects.toMutableList().add(`object`)
        notifyItemInserted(itemCount - 1)
    }

    fun addAll(objects: List<T>) {
        val posStart = mObjects.size
        mObjects.toMutableList().addAll(objects)
        notifyItemRangeInserted(posStart, itemCount - 1)
    }

    /**
     * Remove all elements from the list.
     */
    fun clear() {
        val size = itemCount
        mObjects.toMutableList().clear()
        notifyItemRangeRemoved(0, size)
    }


    fun getItem(position: Int): T {
        return mObjects[position]
    }

    override fun getItemId(position: Int): Long {
        return position.toLong()
    }

    /**
     * Returns the position of the specified item in the array.
     *
     * @param item The item to retrieve the position of.
     * @return The position of the specified item.
     */
    fun getPosition(item: T): Int {
        return mObjects.indexOf(item)
    }


    /**
     * Inserts the specified object at the specified index in the array.
     *
     * @param object The object to insert into the array.
     * @param index  The index at which the object must be inserted.
     */
    fun insert(`object`: T, index: Int) {
        mObjects.toMutableList().add(index, `object`)
        notifyItemInserted(index)
    }


    fun insert(objects: List<T>, index: Int) {
        val posStart = mObjects.size
        mObjects.toMutableList().addAll(index, objects)
        notifyItemRangeInserted(posStart, itemCount - 1)
    }

    /**
     * Removes the specified object from the array.
     *
     * @param object The object to remove.
     */
    fun remove(`object`: T) {
        val position = getPosition(`object`)
        mObjects.toMutableList().remove(`object`)
        notifyItemRemoved(position)
    }

    /**
     * Sorts the content of this adapter using the specified comparator.
     *
     * @param comparator The comparator used to sort the objects contained in this adapter.
     */
    fun sort(comparator: Comparator<in T>) {
        Collections.sort(mObjects, comparator)
        notifyItemRangeChanged(0, itemCount)
    }


    fun addItem(position: Int, model: T) {
        mObjects.toMutableList().add(position, model)
        notifyItemInserted(position)
    }

    fun setItem(position: Int, model: T) {
        mObjects.toMutableList()[position] = model
        notifyItemChanged(position)
    }


    fun moveItem(fromPosition: Int, toPosition: Int) {
        val model = mObjects.toMutableList().removeAt(fromPosition)
        mObjects.toMutableList().add(toPosition, model)
        notifyItemMoved(fromPosition, toPosition)
    }

    fun update(models: List<T>, replaceExisting: Boolean) {
        /*removal of objects creates problem with pagination logic, so this is skipped.*/
        //        applyAndAnimateRemovals(models);
        applyAndAnimateAdditions(models, replaceExisting)
        // applyAndAnimateMovedItems(models);

        //Collections.sort(models);
    }

    private fun applyAndAnimateRemovals(newModels: List<T>) {
        for (mObject in mObjects) {
            if (!newModels.contains(mObject)) {
                remove(mObject)
            }
        }
    }

    private fun applyAndAnimateAdditions(newModels: List<T>, replaceExisting: Boolean) {
        var i = 0
        val count = newModels.size
        while (i < count) {
            val model = newModels[i]

            val index = mObjects.indexOf(model)
            if (!mObjects.contains(model)) {
                addItem(i, model)
            } else {
                if (replaceExisting) {
                    setItem(index, model)
                }
            }
            i++
        }
    }

    private fun applyAndAnimateMovedItems(newModels: List<T>) {
        for (toPosition in newModels.indices.reversed()) {
            val model = newModels[toPosition]
            val fromPosition = mObjects.indexOf(model)
            if (fromPosition >= 0 && fromPosition != toPosition) {
                moveItem(fromPosition, toPosition)
            }
        }
    }

    class MyViewHolder(binding: ViewDataBinding) : RecyclerView.ViewHolder(binding.root) {

        var binding: ViewDataBinding
            internal set


        init {
            this.binding = binding
            this.binding.executePendingBindings()
        }
    }

}
