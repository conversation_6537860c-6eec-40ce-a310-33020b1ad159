package app.rht.petrolcard.baseClasses.activity

import android.content.Context
import android.os.Bundle
import android.util.Log
import app.rht.petrolcard.baseClasses.viewmodel.BaseViewModel
import com.nabinbhandari.android.permissions.PermissionHandler
import com.nabinbhandari.android.permissions.Permissions
import kotlin.reflect.KClass

abstract class BasePermissionActivity<T : BaseViewModel>(private val viewModelClass: KClass<T>) :
    BaseActivity<T>(viewModelClass){

    val mTAG = "BasePermissionActivity"
    var fromSplash = true

    open fun isPermissionAllowed(flag: <PERSON><PERSON>an) {}

    private val permissionHandler = object : PermissionHandler() {
        override fun onGranted() {
            Log.d(mTAG, "permissionHandler onGranted")
            isPermissionAllowed(true)
        }

        override fun onDenied(context: Context?, deniedPermissions: java.util.ArrayList<String>?) {
            isPermissionAllowed(false)
        }

        override fun onJustBlocked(
            context: Context?,
            justBlockedList: java.util.ArrayList<String>?,
            deniedPermissions: java.util.ArrayList<String>?
        ) {
            isPermissionAllowed(false)
        }
    }
    fun showPermissionNeededDialog( permissions:Array<String>) {
    Permissions.check(
            this,
            permissions,
            null,
            null,
            permissionHandler
        )

    }
}