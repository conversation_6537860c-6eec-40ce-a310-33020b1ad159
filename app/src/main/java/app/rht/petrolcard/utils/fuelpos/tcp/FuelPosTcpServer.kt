package app.rht.petrolcard.utils.fuelpos.tcp

import android.os.Handler
import android.util.Log
import java.io.*
import java.lang.Exception
import android.os.Looper
import app.rht.petrolcard.utils.LogWriter
import java.net.*
import kotlin.text.Charsets.UTF_8

class FuelPosTcpServer {
    private var mMessageListener: OnMessageReceived? = null
    private var mConnectListener: OnConnect? = null
    private var mDisconnectListener: OnDisconnect? = null
    private var mServerClosedListener: OnServerClose? = null
    private var mServerStartListener: OnServerStart? = null
    private lateinit var serverSocket: ServerSocket
    private var lastClientIndex: Short = 0
    private val clients: MutableMap<Int, Client> = HashMap()

    private var serverThread : ServerThread? = null

    val logWriter = LogWriter("FuelPosTcpServer")

    var isServerRunning = false
        private set

    fun startServer(port: String?) {
        startServer(Integer.valueOf(port))
    }

    fun startServer(port: Int) {
        serverThread = ServerThread(port)
        serverThread!!.start()
    }

    inner class ServerThread(val port: Int) : Thread() {
        override fun run() {
            isServerRunning = true
            var socket: Socket? = null
            try {
                serverSocket = ServerSocket(port)
            } catch (e: IOException) {
                e.printStackTrace()
                isServerRunning = false
            }
            Log.d(TAG, "startServer: " + (mServerStartListener != null))
            if (mServerStartListener != null)
                Handler(Looper.getMainLooper()).post { mServerStartListener!!.serverStarted(port) }

            while (isServerRunning) {
                println("Accepting client")
                try {
                    socket = serverSocket.accept()
                    val client = Client(socket)
                    lastClientIndex++
                    clients[lastClientIndex.toInt()] = client
                    Thread(client).start()
                    client.setIndex(lastClientIndex.toInt())
                    if (mConnectListener != null)
                        Handler(Looper.getMainLooper()).post {
                            mConnectListener!!.connected(
                                socket,
                                socket.localAddress,
                                +socket.localPort,
                                socket.localSocketAddress,
                                lastClientIndex.toInt()
                            ) }

                } catch (e: IOException) {
                    isServerRunning = false
                    break
                }
            }
            if (mServerClosedListener != null)
                Handler(Looper.getMainLooper()).post { mServerClosedListener!!.serverClosed(port) }
        }
    }

    fun closeServer() {
        try {
            Log.d(TAG, "closeServer: ")
            isServerRunning = false
            serverSocket.close()
            kickAll()
            serverThread!!.interrupt()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: UninitializedPropertyAccessException) {
            e.printStackTrace()
        }
    }

    fun kickAll() {
        for (client in clients.values) client.kill()
    }

    fun kick(clientIndex: Int) {
        clients[clientIndex]!!.kill()
    }

    fun sendln(clientIndex: Int, message: String?) {
        clients[clientIndex]!!.getOutput().println(message)
        clients[clientIndex]!!.getOutput().flush()
    }

    fun send(clientIndex: Int, message: String?) {
        val writer = clients[clientIndex]!!.getOutput()
        val clientAddress = clients[clientIndex]!!.getSocket().inetAddress
        writer.print(message)
        writer.flush()
        Handler(Looper.getMainLooper()).post {
            logWriter.appendLog(TAG,"**Sent to Client $clientAddress, Message: $message")
            logWriter.appendLog(TAG,"**Has any error occurred while sending message: " + writer.checkError())
        }
    }

    fun broadcast(message: String?) {
        val thread = Thread {
            for (client in clients.values) {
                client.getOutput().print(message)
                client.getOutput().flush()
                logWriter.appendLog(TAG, "* Broadcast sent to clients: $message")
            }
        }
        thread.run()
    }

    fun broadcastln(message: String?) {
        for (client in clients.values) {
            client.getOutput().println(message)
            client.getOutput().flush()
        }
    }

    fun getClients(): Map<Int, Client> {
        return clients
    }

    fun getClientsCount(): Int {
        return clients.size
    }

    //---------------------------------------------[Listeners]----------------------------------------------//
    fun setOnMessageReceivedListener(listener: OnMessageReceived?) {
        mMessageListener = listener
    }

    fun setOnConnectListener(listener: OnConnect?) {
        mConnectListener = listener
    }

    fun setOnDisconnectListener(listener: OnDisconnect?) {
        mDisconnectListener = listener
    }

    fun setOnServerClosedListener(listener: OnServerClose?) {
        mServerClosedListener = listener
    }

    fun setOnServerStartListener(listener: OnServerStart?) {
        mServerStartListener = listener
    }

    //---------------------------------------------[Interfaces]---------------------------------------------//
    interface OnMessageReceived {
        fun messageReceived(message: String?, bytes:ByteArray,clientIndex: Int)
    }

    interface OnConnect {
        fun connected(
            socket: Socket?,
            localAddress: InetAddress?,
            port: Int,
            localSocketAddress: SocketAddress?,
            clientIndex: Int
        )
    }

    interface OnDisconnect {
        fun disconnected(
            socket: Socket?,
            localAddress: InetAddress?,
            port: Int,
            localSocketAddress: SocketAddress?,
            clientIndex: Int
        )
    }

    interface OnServerClose {
        fun serverClosed(port: Int)
    }

    interface OnServerStart {
        fun serverStarted(port: Int)
    }

    //--------------------------------------------[Client class]--------------------------------------------//
    inner class Client(clientSocket: Socket) : Runnable {
        private lateinit var output: PrintWriter
        private val socket: Socket = clientSocket
        private lateinit var input: BufferedReader
        private var clientIndex = 0
        private var inputStream: InputStream? = null
        private var outputStream: OutputStream? = null
        override fun run() {
            var buffer :ByteArray
            while (isServerRunning) {
                println("Read line (Client: $clientIndex)")
                try {
                    buffer = ByteArray(4096) //4MB
                    inputStream = socket.getInputStream()
                    val dataLength = inputStream!!.read(buffer)
                    if (dataLength != -1) {
                        val data = ByteArray(dataLength)
                        for (i in 0 until dataLength) data[i] = buffer[i]

                        Handler(Looper.getMainLooper()).post {
                            val dataString = String(data)
                            mMessageListener!!.messageReceived(
                                dataString,
                                data,
                                clientIndex
                            )
                            logWriter.appendLog(TAG, "Received from  ${socket.inetAddress} : $dataString")
                        }
                    } else {
                        socket.close()
                        clients.remove(clientIndex)
                        if (mDisconnectListener != null)
                            Handler(Looper.getMainLooper()).post {
                                mDisconnectListener!!.disconnected(
                                    socket,
                                    socket.localAddress,
                                    +socket.localPort,
                                    socket.localSocketAddress,
                                    clientIndex)
                            }
                        break
                    }

                } catch (e: IOException) {
                    e.printStackTrace()
                } catch (e: SocketException) {
                    //e.printStackTrace()
                    Handler(Looper.getMainLooper()).post {
                        mDisconnectListener!!.disconnected(
                            socket,
                            socket.localAddress,
                            +socket.localPort,
                            socket.localSocketAddress,
                            clientIndex)
                    }
                }
            }
        }

        fun send(data:String){
            send(data.toByteArray(UTF_8))
        }

        fun send(data: ByteArray) {
            val thread = Thread {
                try {
                    outputStream = socket.getOutputStream()
                    outputStream!!.write(data)
                    Handler(Looper.getMainLooper()).post { logWriter.appendLog(TAG,"Data sent to ${socket.inetAddress}, data: ${String(data, UTF_8)}") }
                } catch (e: IOException) {
                    e.printStackTrace()
                    Handler(Looper.getMainLooper()).post {
                        Log.d(TAG, "Error sending data")
                        logWriter.appendLog(TAG,"Error ${e.message}, ${e.cause}")
                    }
                } catch (e: SocketException){
                    kill()
                }
            }
            thread.start()
        }

        fun kill() {
            try {
                socket.shutdownInput()
            } catch (e: Exception) {
            }
            try {
                socket.shutdownOutput()
            } catch (e: Exception) { }
            try {
                if (inputStream != null) inputStream!!.close()
                if (outputStream != null) outputStream!!.close()
                socket.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }

        fun setIndex(index: Int) {
            clientIndex = index
        }

        fun getOutput(): PrintWriter {
            return output
        }

        fun getSocket(): Socket {
            return socket
        }

        init {
            try {
                input = BufferedReader(InputStreamReader(socket.getInputStream()))
                output = PrintWriter(BufferedWriter(OutputStreamWriter(socket.getOutputStream())), true)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    companion object {
        private const val TAG = "TCPServer"
    }
}
