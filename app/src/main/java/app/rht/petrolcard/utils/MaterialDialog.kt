package app.rht.petrolcard.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import app.rht.petrolcard.R
import com.afollestad.materialdialogs.DialogCallback
import com.afollestad.materialdialogs.MaterialDialog
import java.lang.ref.WeakReference

fun askShowMemoryCardDialog(context: Context, activity: Activity){
    val message = context.getString(R.string.please_allow_sd_card).replace("#", context.getString(R.string.app_name))
    MaterialDialog(context)
        .title(text = context.getString(R.string.permission_required))
        .message(text = message)
        .cancelable(false)
        .show {
            cornerRadius(res = R.dimen.material_dialog_corner)

            positiveButton(text = context.getString(R.string.allow)) {
                activity.startActivityForResult(Intent(Intent.ACTION_OPEN_DOCUMENT_TREE), 42)
            }
            negativeButton(text = context.getString(R.string.cancel)) {
                this.dismiss()
            }
        }
}


class MyMaterialDialog(
    val context: Context,
    val title:String,
    val message:String,
    val positiveBtnText : String,
    val negativeBtnText : String,
    val listener: MyMaterialDialogListener) {
    init {
        val litener = WeakReference(listener).get()!!
        val ctx = WeakReference(context).get()!!
       MaterialDialog(ctx)
            .title(text = title)
            .message(text = message)
            .cancelable(false)
           .show {
                cornerRadius(res = R.dimen.material_dialog_corner)
                positiveButton(text = positiveBtnText,click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        litener.onPositiveClick(dialog)
                    }
                })
                negativeButton(text = negativeBtnText, click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        litener.onNegativeClick(dialog)
                    }
                })
            }
    }
}

interface MyMaterialDialogListener {
    fun onPositiveClick(dialog: MaterialDialog)
    fun onNegativeClick(dialog: MaterialDialog)
}


class MyMaterialDialogSingle(
    val context: Context,
    val title:String,
    val message:String,
    val btnText: String,
    val listener: MyMaterialDialogSingleListener) {
    init {
        val litener = WeakReference(listener).get()!!
        val ctx = WeakReference(context).get()!!
        MaterialDialog(ctx)
            .title(text = title)
            .message(text = message)
            .cancelable(false)
            .show {
                cornerRadius(res = R.dimen.material_dialog_corner)
                positiveButton(text = btnText,click = object : DialogCallback{
                    override fun invoke(dialog: MaterialDialog) {
                        litener.onButtonClick(dialog)
                    }
                })
            }
    }
}
interface MyMaterialDialogSingleListener {
    fun onButtonClick(dialog: MaterialDialog)
}