package app.rht.petrolcard.utils

import android.annotation.SuppressLint
import android.content.Context
import android.net.wifi.WifiManager
import android.util.Log
import androidx.preference.PreferenceManager
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.utils.constant.AppConstant
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.text.DateFormat
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class HelpersLoyalty {
    companion object {
        private val TAG = HelpersLoyalty::class.java.simpleName

        fun dateToString(date: Date?): String? {
            val sdfDate =
                SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            return sdfDate.format(date)
        }

        fun getSN(c: Context): String? {
            return MainApp.sn
        }

        fun getServerUrl(c: Context?): String? {
            val sharedPreferences = PreferenceManager.getDefaultSharedPreferences(c)
            val serverUrl = sharedPreferences.getString(AppConstant.URL_WS, null)!!
                .replace("-tpe".toRegex(), "")
            Log.i(TAG, serverUrl)
            return serverUrl
        }

        fun getDateTicket(date: Date?): String? {
            val dateFormat: DateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm:ss")
            return dateFormat.format(date)
        }

        fun getDateUsInStringFormat(date: Date?): String? {
            val dateFormat: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
            return dateFormat.format(date)
        }

        fun maskPan(s: String): String? {
            val length = s.length
            return if (length >= 8) {
                s.substring(0, length - 8) + "XXXXXXXX"
            } else {
                s
            }
        }

        fun maskCin(s: String): String? {
            val length = s.length
            //Check whether or not the string contains at least eight characters; if not, this method is useless
            return if (length >= 3) {
                s.substring(0, length - 3) + "XXX"
            } else {
                s
            }
        }
        fun formatDouble(d: Double): String? {
            return String.format(Locale.US, "%.2f", d)
        } // eof formatDouble

        fun stringToDate(strDate: String?): Date? {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            var date: Date? = null
            try {
                date = sdfDate.parse(strDate)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            return date
        }

        fun md5(s: String): String? {
            try {
                // Create MD5 Hash
                val digest = MessageDigest.getInstance("MD5")
                digest.update(s.toByteArray())
                val messageDigest = digest.digest()

                // Create Hex String
                val hexString = StringBuffer()
                for (i in messageDigest.indices) hexString.append(
                    Integer.toHexString(
                        0xFF and messageDigest[i]
                            .toInt()
                    )
                )
                return hexString.toString()
            } catch (e: NoSuchAlgorithmException) {
                e.printStackTrace()
            }
            return ""
        }

        fun stringToDateToStringForScreen(strDate: String?): String? {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            var date: Date? = null
            try {
                date = sdfDate.parse(strDate)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            var s = ""
            val dateFormat1: DateFormat = SimpleDateFormat("dd/MM/yyyy")
            s += "Le " + dateFormat1.format(date)
            val dateFormat2: DateFormat = SimpleDateFormat("HH:mm:ss")
            s += """
                 
                 À ${dateFormat2.format(date)}
                 """.trimIndent()
            return s
        }

        open fun disableWifi(c: Context) {
            @SuppressLint("WifiManagerLeak") val wifiManager =
                c.getSystemService(Context.WIFI_SERVICE) as WifiManager
            if (wifiManager.isWifiEnabled) {
                wifiManager.isWifiEnabled = false
            }
        }

        fun enableWifi(c: Context) {
            @SuppressLint("WifiManagerLeak") val wifiManager =
                c.getSystemService(Context.WIFI_SERVICE) as WifiManager
            if (!wifiManager.isWifiEnabled) {
                wifiManager.isWifiEnabled = true
            }
        }


    }
}