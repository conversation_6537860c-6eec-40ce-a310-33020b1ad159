package app.rht.petrolcard.utils.tims;

public class StatusRes {
   /**
    *Power down in opened fiscal receipt
    */
    public boolean Power_down_in_opened_fiscal_receipt;
    public boolean getPower_down_in_opened_fiscal_receipt() {
       return Power_down_in_opened_fiscal_receipt;
    }
    protected void setPower_down_in_opened_fiscal_receipt(boolean value) {
       Power_down_in_opened_fiscal_receipt = value;
    }

   /**
    *DateTime not set
    */
    public boolean DateTime_not_set;
    public boolean getDateTime_not_set() {
       return DateTime_not_set;
    }
    protected void setDateTime_not_set(boolean value) {
       DateTime_not_set = value;
    }

   /**
    *DateTime wrong
    */
    public boolean DateTime_wrong;
    public boolean getDateTime_wrong() {
       return DateTime_wrong;
    }
    protected void setDateTime_wrong(boolean value) {
       DateTime_wrong = value;
    }

   /**
    *RAM reset
    */
    public boolean RAM_reset;
    public boolean getRAM_reset() {
       return RAM_reset;
    }
    protected void setRAM_reset(boolean value) {
       RAM_reset = value;
    }

   /**
    *Hardware clock error
    */
    public boolean Hardware_clock_error;
    public boolean getHardware_clock_error() {
       return Hardware_clock_error;
    }
    protected void setHardware_clock_error(boolean value) {
       Hardware_clock_error = value;
    }

   /**
    *Reports registers Overflow
    */
    public boolean Reports_registers_Overflow;
    public boolean getReports_registers_Overflow() {
       return Reports_registers_Overflow;
    }
    protected void setReports_registers_Overflow(boolean value) {
       Reports_registers_Overflow = value;
    }

   /**
    *Opened Fiscal Receipt
    */
    public boolean Opened_Fiscal_Receipt;
    public boolean getOpened_Fiscal_Receipt() {
       return Opened_Fiscal_Receipt;
    }
    protected void setOpened_Fiscal_Receipt(boolean value) {
       Opened_Fiscal_Receipt = value;
    }

   /**
    *Receipt Invoice Type
    */
    public boolean Receipt_Invoice_Type;
    public boolean getReceipt_Invoice_Type() {
       return Receipt_Invoice_Type;
    }
    protected void setReceipt_Invoice_Type(boolean value) {
       Receipt_Invoice_Type = value;
    }

   /**
    *SD card near full
    */
    public boolean SD_card_near_full;
    public boolean getSD_card_near_full() {
       return SD_card_near_full;
    }
    protected void setSD_card_near_full(boolean value) {
       SD_card_near_full = value;
    }

   /**
    *SD card full
    */
    public boolean SD_card_full;
    public boolean getSD_card_full() {
       return SD_card_full;
    }
    protected void setSD_card_full(boolean value) {
       SD_card_full = value;
    }

   /**
    *CU fiscalized
    */
    public boolean CU_fiscalized;
    public boolean getCU_fiscalized() {
       return CU_fiscalized;
    }
    protected void setCU_fiscalized(boolean value) {
       CU_fiscalized = value;
    }

   /**
    *CU produced
    */
    public boolean CU_produced;
    public boolean getCU_produced() {
       return CU_produced;
    }
    protected void setCU_produced(boolean value) {
       CU_produced = value;
    }

   /**
    *Paired with TIMS
    */
    public boolean Paired_with_TIMS;
    public boolean getPaired_with_TIMS() {
       return Paired_with_TIMS;
    }
    protected void setPaired_with_TIMS(boolean value) {
       Paired_with_TIMS = value;
    }

   /**
    *Unsent receipts
    */
    public boolean Unsent_receipts;
    public boolean getUnsent_receipts() {
       return Unsent_receipts;
    }
    protected void setUnsent_receipts(boolean value) {
       Unsent_receipts = value;
    }

   /**
    *No Sec.IC
    */
    public boolean No_Sec_IC;
    public boolean getNo_Sec_IC() {
       return No_Sec_IC;
    }
    protected void setNo_Sec_IC(boolean value) {
       No_Sec_IC = value;
    }

   /**
    *No certificates
    */
    public boolean No_certificates;
    public boolean getNo_certificates() {
       return No_certificates;
    }
    protected void setNo_certificates(boolean value) {
       No_certificates = value;
    }

   /**
    *Service jumper
    */
    public boolean Service_jumper;
    public boolean getService_jumper() {
       return Service_jumper;
    }
    protected void setService_jumper(boolean value) {
       Service_jumper = value;
    }

   /**
    *Missing SD card
    */
    public boolean Missing_SD_card;
    public boolean getMissing_SD_card() {
       return Missing_SD_card;
    }
    protected void setMissing_SD_card(boolean value) {
       Missing_SD_card = value;
    }

   /**
    *Wrong SD card
    */
    public boolean Wrong_SD_card;
    public boolean getWrong_SD_card() {
       return Wrong_SD_card;
    }
    protected void setWrong_SD_card(boolean value) {
       Wrong_SD_card = value;
    }
}
