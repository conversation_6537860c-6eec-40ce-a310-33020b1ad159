package app.rht.petrolcard.utils;

import android.os.Build;
import android.util.Log;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

import app.rht.petrolcard.BuildConfig;

/**
 * Created on 2019-09-06.
 */
public class UtilsDate {

    private static final String TAG = UtilsDate.class.getSimpleName();


    public static boolean isCardExpired(Date mToday, String dateExp){


        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateExp);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (mToday.after(expDate)) {
            Log.e(" ", "card expired");
            return true ;
        }

        if (mToday.before(expDate)) {
            Log.e("", "card  not expired ");
            return false ;

        }

        return true ;
    }

    public static boolean isNextDayPLF(Date mToday,String dateLastPlf){


        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPlf);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (mToday.after(expDate) ) {
            Log.e("", "UPDATE ON");
            return true ;
        }

        if (mToday.before(expDate)) {
            Log.e("", "UPDATE OFF ");
            return false ;

        }

        return false ;
    }

    public static boolean isDatebetween(Date now,String dateDebut,String dateFin){


        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        Date debut = null;
        Date fin = null;

        try {
            debut = sdfDate.parse(dateDebut);
            fin = sdfDate.parse(dateFin);

        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (now.after(fin) || now.before(debut) ) {
            Log.e("", "UPDATE ON");
            return false ;
        } else return true ;

    }

    public static boolean isDateHourbetween(String nowDate,String dateDebut,String dateFin){


        SimpleDateFormat sdfDate = new SimpleDateFormat("HH:mm:ss", Locale.ENGLISH);
        Date debut = null;
        Date fin = null;
        Date now = null;




        try {
            debut = sdfDate.parse(dateDebut);
            fin = sdfDate.parse(dateFin);
            now = sdfDate.parse(nowDate);



        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (now.after(fin) || now.before(debut) ) {
            Log.e("", "UPDATE ON");
            return true ;
        } else return false ;

    }

    public static boolean isMonday(Date mToday){


        Calendar cal = Calendar.getInstance();
        cal.setTime(mToday);
        boolean monday = cal.get(Calendar.DAY_OF_WEEK) == Calendar.MONDAY;

        if (monday ) {
            Log.e("", "isMonday ON");
            return true ;
        }else
            return false ;

    }

    public static boolean isSameDay(Date mToday,int other, int month){

        Calendar cal = Calendar.getInstance();
        cal.setTime(mToday);

        boolean sameday = (cal.get(Calendar.DAY_OF_WEEK) == other) &&(cal.get(Calendar.WEEK_OF_MONTH) == month);

        if (sameday ) {
            Log.e("", "issameday ON");
            return true ;
        }else
            return false ;

    }

    public static boolean isSameDay(Date mToday,String otherDate){

        try {

            Calendar calToday = Calendar.getInstance();
            calToday.setTime(mToday);

            Calendar cal = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            cal.setTime(sdf.parse(otherDate));// all done

            boolean sameDay = calToday.get(Calendar.DAY_OF_YEAR) == cal.get(Calendar.DAY_OF_YEAR) &&
                    calToday.get(Calendar.YEAR) == cal.get(Calendar.YEAR) ;

            if (sameDay) {
                Log.e("", "issameday ON");
                return true ;
            }

        }catch (ParseException e){

            e.printStackTrace();

        }

        return false ;

    }
    public static boolean isSameDay1(String timeZone){

        TimeZone tz1 = TimeZone.getTimeZone("GMT+05:30");
        Calendar c = Calendar.getInstance(tz1);
        String time = c.getTime().toString();
        Log.i(TAG,"TimeZone:: "+time);
        Log.i(TAG,"TimeZone:: M "+c.get(Calendar.MONTH));
        Log.i(TAG,"TimeZone:: Y"+c.get(Calendar.YEAR));
        Log.i(TAG,"TimeZone:: D"+c.get(Calendar.DATE));

        Calendar calToday = Calendar.getInstance();
        Log.i(TAG,"calToday :: "+calToday.getTime());

        Calendar cal = Calendar.getInstance();
        TimeZone tz = TimeZone.getTimeZone(timeZone);
        cal.setTimeZone(tz);
        Log.i(TAG,"cal :: "+cal.getTime());

        boolean sameDay = calToday.get(Calendar.DATE) == cal.get(Calendar.DATE) &&
                calToday.get(Calendar.YEAR) == cal.get(Calendar.YEAR) && calToday.get(Calendar.MONTH) == cal.get(Calendar.MONTH) ;

        if (sameDay) {
            Log.e("", "issameday ON");
            return true ;
        }

        return false ;

    }

    public static boolean isSameYear(Date mToday,String otherDate){

        try {

            Calendar calToday = Calendar.getInstance();
            calToday.setTime(mToday);
            boolean ok = false ;

            Calendar cal = Calendar.getInstance();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
            if(otherDate != null && !otherDate.isEmpty()) {
                Date d = sdf.parse(otherDate) ;
                if(d!=null)
                    cal.setTime(d);// all done
                else ok = true ;

            }

            boolean sameYear = calToday.get(Calendar.YEAR) == cal.get(Calendar.YEAR) ;

            if (sameYear || ok) {
                Log.e("", "sameYear ON");
                return true ;
            }

        }catch (ParseException e){

            e.printStackTrace();

        }

        return false ;

    }


    public static boolean isAfterToday(Date mToday,String otherDate){

        try {

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);

            Calendar calToday = Calendar.getInstance();
            calToday.setTime(mToday);

            Calendar cal = Calendar.getInstance();
            Date other = sdf.parse(otherDate);
            cal.setTime(other); // all done


            if (cal.after(calToday)) {
                if(BuildConfig.DEBUG){
                    return false;
                }
                Log.e("", "isAfterToday ON");
               if(BuildConfig.DEBUG){                  //added to bypass if previous transaction date is more than current date
                    return false;
                }
                return true;
            }

        }catch (ParseException e){

            e.printStackTrace();

        }

        return false ;

    }

    public static boolean isSameMonth(Date mToday,String dateLastPLF){



        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPLF);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calToday = Calendar.getInstance();
        calToday.setTime(mToday);

        Calendar calLast = Calendar.getInstance();
        calLast.setTime(expDate);

        if (calToday.get(Calendar.YEAR) == calLast.get(Calendar.YEAR) && calToday.get(Calendar.MONTH) == calLast.get(Calendar.MONTH) ) {
            Log.e("", "isSameMonth ON");
            return true ;
        }else
            return false ;

    }

    public static boolean isTwoMonthsDifference(Date mToday,String dateLastPLF){



        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPLF);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calToday = Calendar.getInstance();
        calToday.setTime(mToday);

        Calendar calLast = Calendar.getInstance();
        calLast.setTime(expDate);

        if (Math.abs(calToday.get(Calendar.MONTH) - calLast.get(Calendar.MONTH) ) > 1) {
            Log.e("", "isTwoMonthsDifference ON");
            return true ;
        }else
            return false ;

    }

    public static boolean isOneMonthsDifference(Date mToday,String dateLastPLF){



        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPLF);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calToday = Calendar.getInstance();
        calToday.setTime(mToday);

        Calendar calLast = Calendar.getInstance();
        calLast.setTime(expDate);

        if (Math.abs(calToday.get(Calendar.MONTH) - calLast.get(Calendar.MONTH) )  > 0) {
            Log.e("", "isTwoMonthsDifference ON");
            return true ;
        }else
            return false ;

    }

    public static boolean isOneDayDifference(Date mToday,String dateLastPLF){



        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPLF);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calToday = Calendar.getInstance();
        calToday.setTime(mToday);

        Calendar calLast = Calendar.getInstance();
        calLast.setTime(expDate);

        if (Math.abs(calToday.get(Calendar.DAY_OF_MONTH) - calLast.get(Calendar.DAY_OF_MONTH) ) > 0) {
            Log.e("", "isTwoMonthsDifference ON");
            return true;
        }else return false;

    }

    public static boolean isOneDayDifferenceAndMidNight(Date mToday,String dateLastPLF){



        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPLF);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calToday = Calendar.getInstance();
        calToday.setTime(mToday);

        Calendar calLast = Calendar.getInstance();
        calLast.setTime(expDate);

        int temp = calToday.get(Calendar.HOUR_OF_DAY) ;
        Log.w("HOUR_OF_DAY", "HOUR_OF_DAY :" + temp );

        if (Math.abs(calToday.get(Calendar.DAY_OF_MONTH) - calLast.get(Calendar.DAY_OF_MONTH) ) > 0 && temp == 23 ) {
            Log.e("", "isTwoMonthsDifference ON");
            return true ;
        }else
            return false ;

    }

    public static boolean isSameWeek(Date mToday,String dateLastPLF){



        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateLastPLF);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calToday = Calendar.getInstance(Locale.GERMAN);
        calToday.setTime(mToday);
        int year1 = calToday.get(Calendar.YEAR);
        int mounth1 = calToday.get(Calendar.MONTH);
        int week1 = calToday.get(Calendar.WEEK_OF_YEAR);

        Log.e("", "isSameWeek ON DAY_OF_WEEK --->"+ Calendar.DAY_OF_WEEK);
        Log.e("", "isSameWeek ON YEAR --->"+ Calendar.YEAR);
        Log.e("", "isSameWeek ON WEEK_OF_YEAR --->"+ Calendar.WEEK_OF_YEAR);



        Calendar calLast = Calendar.getInstance(Locale.GERMAN);
        calLast.setTime(expDate);
        int year2 = calLast.get(Calendar.YEAR);
        int mounth2 = calLast.get(Calendar.MONTH);
        int week2 = calLast.get(Calendar.WEEK_OF_YEAR);


        Log.e("", "isSameWeek ON DAY_OF_WEEK --->"+ Calendar.DAY_OF_WEEK);
        Log.e("", "isSameWeek ON YEAR --->"+ Calendar.YEAR);
        Log.e("", "isSameWeek ON WEEK_OF_YEAR --->"+ Calendar.WEEK_OF_YEAR);

        //if (calToday.get(Calendar.MONTH) == calLast.get(Calendar.MONTH) ) {
        if (year1 == year2 && week1 == week2 && mounth1 == mounth2 ) {

            Log.e("", "isSameMonth ON");
            return true ;
        }else
            return false ;

    }


    public String getCurrentDate(){
        SimpleDateFormat sm = new  SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        Log.i(TAG," date"+datepersomm.replaceAll("-", ""));
        return datepersomm.replaceAll("-", "");
    }

}
