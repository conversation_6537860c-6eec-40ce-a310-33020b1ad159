/*
 *
 *  * Copyright (C) 2017 Safaricom, Ltd.
 *  *
 *  * Licensed under the Apache License, Version 2.0 (the "License");
 *  * you may not use this file except in compliance with the License.
 *  * You may obtain a copy of the License at
 *  *
 *  * http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  * Unless required by applicable law or agreed to in writing, software
 *  * distributed under the License is distributed on an "AS IS" BASIS,
 *  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  * See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */

package app.rht.petrolcard.utils.mpesaservices;

import com.google.gson.annotations.SerializedName;

/**
 * Created  on 5/28/2017.
 */

public class STKPush {

    @SerializedName("BusinessShortCode")
    private final String businessShortCode;
    @SerializedName("Password")
    private final String password;
    @SerializedName("Timestamp")
    private final String timestamp;
    @SerializedName("TransactionType")
    private final String transactionType;
    @SerializedName("Amount")
    private final String amount;
    @SerializedName("PartyA")
    private final String partyA;
    @SerializedName("PartyB")
    private final String partyB;
    @SerializedName("PhoneNumber")
    private final String phoneNumber;
    @SerializedName("CallBackURL")
    private final String callBackURL;
    @SerializedName("AccountReference")
    private final String accountReference;
    @SerializedName("TransactionDesc")
    private final String transactionDesc;

    public STKPush(String businessShortCode, String password, String timestamp, String transactionType,
                   String amount, String partyA, String partyB, String phoneNumber, String callBackURL,
                   String accountReference, String transactionDesc) {
        this.businessShortCode = businessShortCode;
        this.password = password;
        this.timestamp = timestamp;
        this.transactionType = transactionType;
        this.amount = amount;
        this.partyA = partyA;
        this.partyB = partyB;
        this.phoneNumber = phoneNumber;
        this.callBackURL = callBackURL;
        this.accountReference = accountReference;
        this.transactionDesc = transactionDesc;
    }
}