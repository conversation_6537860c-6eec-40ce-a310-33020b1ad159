package app.rht.petrolcard.utils.pt102

class PT102Utils {
    companion object{
        val INIT_PRINTER = getBytes(intArrayOf(0x1B,0x40)) //"1B 40"
        val CUT_PAPER = getBytes(intArrayOf(0x1D,0x56,0x01)) //"1D 56 01"
        val PRINT_MODE = getBytes(intArrayOf(0x12)) //"12"
        val READ_PRINT_STATUS = getBytes(intArrayOf(0x1B,0x3F)) //"1B 3F"
        val LINE_FEED = getBytes(intArrayOf(0x0A)) //"0A"
        val PRINT_LOGO = getBytes(intArrayOf(0x1B,0x4C,0x48)) //"1B 4C 48"

        val NORMAL_24_CHAR = getBytes(intArrayOf(0x1B,0x21,0x00)) //12X24 Normal 24 char/line.
        val NORMAL_32_CHAR = getBytes(intArrayOf(0x1B,0x21,0x02)) //12X24 Normal 32 char/line.
        val SMALL_32_CHAR = getBytes(intArrayOf(0x1B,0x21,0x01)) //9X24 Small 32 char/line.
        val SMALL_42_CHAR = getBytes(intArrayOf(0x1B,0x21,0x03)) //9X24 Small 42 char/line.

        val NORMAL_TALL_24_CHAR = getBytes(intArrayOf(0x1B,0x21,0x10)) //12X24 double height 24 char/line
        val NORMAL_TALL_32_CHAR = getBytes(intArrayOf(0x1B,0x21,0x12)) //12X24 double height 32 char/line.
        val SMALL_TALL_24_CHAR = getBytes(intArrayOf(0x1B,0x21,0x11))  //9X24 double height 32 char/line.
        val SMALL_TALL_32_CHAR = getBytes(intArrayOf(0x1B,0x21,0x13))  //9X24 double height 42 char/line.

        val NORMAL_WIDE_32_CHAR = getBytes(intArrayOf(0x1B,0x21,0x22)) //12X24 double width 16 char/line.
        val SMALL_WIDE_32_CHAR = getBytes(intArrayOf(0x1B,0x21,0x23))  //9X24 double width 21 char/line.

        val NORMAL_WIDE_16_CHAR = getBytes(intArrayOf(0x1B,0x21,0x32)) //12X24 double width height 16 char/line.
        val SMALL_WIDE_24_CHAR = getBytes(intArrayOf(0x1B,0x21,0x33))  //9X24 double width height 21 char/line.

        val MORE_SPACE = getBytes(intArrayOf(0x1B,0x4A,0x80)) //"1B 4A 80"
        private fun getBytes(ints: IntArray): ByteArray {
            return ints.foldIndexed(ByteArray(ints.size)) { i, a, v -> a.apply {
                    set(
                        i,
                        v.toByte()
                    )
                }
            }
        }

    }
}