package app.rht.petrolcard.utils

import android.text.TextUtils
import android.util.Patterns
import android.widget.EditText
import android.widget.TextView
import java.util.*
import java.util.regex.Pattern


object StringUtility {

     fun extractEmails(all: String): Array<String> {
        val EMAIL_REXP = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)" +
                "*@" + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$"

        val pattern: Pattern = Pattern.compile(EMAIL_REXP)
        val tokenizer = StringTokenizer(all, ";")
        val mailList: MutableList<String> = ArrayList()
        var currentMail: String
        while (tokenizer.hasMoreElements()) {
            currentMail = tokenizer.nextElement() as String
            if (pattern.matcher(currentMail.trim { it <= ' ' }).matches()) {
                mailList.add(currentMail)
            }
        }
        return mailList.toTypedArray()
    }

    fun stringNotNull(str: String?): Boolean {
        return str != null
    }

    fun stringNotEmpty(str: String): Boolean {
        return !str.isEmpty()
    }


    /*
    * Validate input string for null object and not empty string.
    * */
    @JvmStatic
    fun validateString(str: String?): Boolean {
        return stringNotNull(str) && stringNotEmpty(str!!.trim())
    }

  /*  fun validateString(vararg str: String): Boolean {
        var result = true
        for (s in str) {
            result = stringNotNull(s) && stringNotEmpty(s)
            if (!result) return result
        }
        return result
    }*/

    fun validateEditText(editText: EditText): Boolean {
        return stringNotEmpty(editText.text.toString().trim { it <= ' ' })
    }

    fun validateTextView(textView: TextView): Boolean {
        return stringNotEmpty(textView.text.toString().trim { it <= ' ' })
    }

    fun validateEmail(email: String): Boolean {
        //val emailPattern = Pattern.compile("[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+(aero|asia|biz|cat|com|coop|edu|gov|info|int|jobs|mil|mobi|museum|name|net|org|pro|tel|travel|ac|ad|ae|af|ag|ai|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cu|cv|cx|cy|cz|de|dj|dk|dm|do|dz|ec|ee|eg|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sj|sk|sl|sm|sn|so|sr|st|su|sv|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tp|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|yu|za|zm|zw)\\b", 2)
        //return emailPattern.matcher(email).matches()
        return Patterns.EMAIL_ADDRESS.matcher(email).matches()
    }

    fun validatePassword(password: String): Boolean {
        val PASSWORD_PATTERN = "((?=.*[a-z])(?=.*\\d)(?=.*[A-Z]).{8,20})"
        val emailPattern = Pattern.compile(PASSWORD_PATTERN)
        return emailPattern.matcher(password).matches()
    }

    fun validateUrl(url: String): Boolean {
        //final Pattern urlPattern = Pattern.compile("[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+(aero|asia|biz|cat|com|coop|edu|gov|info|int|jobs|mil|mobi|museum|name|net|org|pro|tel|travel|ac|ad|ae|af|ag|ai|al|am|an|ao|aq|ar|as|at|au|aw|ax|az|ba|bb|bd|be|bf|bg|bh|bi|bj|bm|bn|bo|br|bs|bt|bv|bw|by|bz|ca|cc|cd|cf|cg|ch|ci|ck|cl|cm|cn|co|cr|cu|cv|cx|cy|cz|de|dj|dk|dm|do|dz|ec|ee|eg|er|es|et|eu|fi|fj|fk|fm|fo|fr|ga|gb|gd|ge|gf|gg|gh|gi|gl|gm|gn|gp|gq|gr|gs|gt|gu|gw|gy|hk|hm|hn|hr|ht|hu|id|ie|il|im|in|io|iq|ir|is|it|je|jm|jo|jp|ke|kg|kh|ki|km|kn|kp|kr|kw|ky|kz|la|lb|lc|li|lk|lr|ls|lt|lu|lv|ly|ma|mc|md|me|mg|mh|mk|ml|mm|mn|mo|mp|mq|mr|ms|mt|mu|mv|mw|mx|my|mz|na|nc|ne|nf|ng|ni|nl|no|np|nr|nu|nz|om|pa|pe|pf|pg|ph|pk|pl|pm|pn|pr|ps|pt|pw|py|qa|re|ro|rs|ru|rw|sa|sb|sc|sd|se|sg|sh|si|sj|sk|sl|sm|sn|so|sr|st|su|sv|sy|sz|tc|td|tf|tg|th|tj|tk|tl|tm|tn|to|tp|tr|tt|tv|tw|tz|ua|ug|uk|us|uy|uz|va|vc|ve|vg|vi|vn|vu|wf|ws|ye|yt|yu|za|zm|zw)\\b", 2);
        return Patterns.WEB_URL.matcher(url).matches()
    }

    @JvmOverloads
    fun capitalize(str: String, vararg delimiters: Char): String? {
        val delimLen = delimiters.size
        if (!TextUtils.isEmpty(str) && delimLen != 0) {
            val buffer = str.toCharArray()
            var capitalizeNext = true

            for (i in buffer.indices) {
                val ch = buffer[i]
                if (isDelimiter(ch, delimiters)) {
                    capitalizeNext = true
                } else if (capitalizeNext) {
                    buffer[i] = Character.toTitleCase(ch)
                    capitalizeNext = false
                }
            }

            return String(buffer)
        } else {
            return str
        }
    }

    private fun isDelimiter(ch: Char, delimiters: CharArray?): Boolean {
        if (delimiters == null) {
            return Character.isWhitespace(ch)
        } else {
            val `len$` = delimiters.size

            for (`i$` in 0 until `len$`) {
                val delimiter = delimiters[`i$`]
                if (ch == delimiter) {
                    return true
                }
            }

            return false
        }
    }

}
