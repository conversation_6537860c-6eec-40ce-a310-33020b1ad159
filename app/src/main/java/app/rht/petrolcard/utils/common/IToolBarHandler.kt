package app.rht.petrolcard.utils.common

import android.graphics.drawable.Drawable

interface IToolBarHandler {
    fun toggleLeft()
    fun unlockDrawer()
    fun lockDrawer()
    fun isDrawerOpen(): Boolean

    fun setToolBar(visibility: Int)
    fun setAppBarBgColor(color: Int)
    fun setLeftMenuOnToolBar(visibility: Int)
    fun setCenterTitleOnToolBar(visibility: Int, title: String?)
    fun setBackOnToolBAr(visibility: Int)
    fun setRightActionOnToolBar(visibility: Int, drawable: Drawable?)
    fun changeColorRightAction(color: Int)
    fun setRightActionListener(listener: RightActionToolBarListener?)
    fun setSearchActionListener(listener: SearchActionToolBarListener?)
    fun setSearchActionChangeListner(listener: SearchActionToolBarQueryChangeListener?)

    fun setProfileImageOnToolBar(visibility: Int, path: String?)
    fun setNameOnToolBar(visibility: Int, title: String)
    fun setTextOnToolBar(visibility: Int, text: String)
}