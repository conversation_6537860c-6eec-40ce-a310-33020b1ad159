package app.rht.petrolcard.utils.fuelpos

import FuelPosSingleTcpServer
import android.app.*
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.core.app.NotificationCompat
import androidx.core.content.ContextCompat
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.database.baseclass.FCCTransactionsDao
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TransactionDao
import app.rht.petrolcard.service.FusionService
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.product.model.FuelTrxCommonModel
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.timssign.activity.TIMSInvoiceGenerate
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.LogWriter
import app.rht.petrolcard.utils.Support
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.FUSION_PAYMENT_TYPES
import app.rht.petrolcard.utils.fuelpos.FuelPosCommands.Companion.deleteTransaction
import app.rht.petrolcard.utils.fuelpos.FuelPosCommands.Companion.logOn
import app.rht.petrolcard.utils.fuelpos.models.ClaimedTransactionResult
import app.rht.petrolcard.utils.fuelpos.models.ClearTransactionResult
import app.rht.petrolcard.utils.fuelpos.models.PosTransaction
import app.rht.petrolcard.utils.fuelpos.models.TransactionData
import app.rht.petrolcard.utils.fuelpos.tcp.ConnectionError
import app.rht.petrolcard.utils.fuelpos.tcp.ConnectionState
import app.rht.petrolcard.utils.fuelpos.tcp.FuelPosTcpClient
import app.rht.petrolcard.utils.tax.TaxModel
import app.rht.petrolcard.utils.tax.TaxUtils
import app.rht.petrolcard.utils.tims.CloseReceiptRes
import com.altafrazzaque.ifsfcomm.ifsf.models.ClearFuelSaleTrxPrams
import com.google.gson.Gson
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import java.net.*
import java.util.*


fun Context.toast(message:String){
    Toast.makeText(applicationContext,message,Toast.LENGTH_SHORT).show()
}

fun getIpAddress(): String {
    var ip = ""
    try {
        val enumNetworkInterfaces = NetworkInterface
            .getNetworkInterfaces()
        while (enumNetworkInterfaces.hasMoreElements()) {
            val networkInterface = enumNetworkInterfaces
                .nextElement()
            val enumInetAddress = networkInterface
                .inetAddresses
            while (enumInetAddress.hasMoreElements()) {
                val inetAddress = enumInetAddress.nextElement()
                if (inetAddress.isSiteLocalAddress) {
                    ip += inetAddress.hostAddress
                }
            }
        }
    } catch (e: SocketException) {
        e.printStackTrace()
        ip += "Something Wrong! ${e.message}\n"
    }
    return ip
}

class FuelPosService : Service() {

    companion object {
        private val TAG = FuelPosService::class.simpleName
        private const val ACTION_START_FOREGROUND_SERVICE = "ACTION_START_FOREGROUND_SERVICE"
        private const val ACTION_STOP_FOREGROUND_SERVICE = "ACTION_STOP_FOREGROUND_SERVICE"
        private const val ACTION_CONNECT_FCC = "ACTION_START_FCC_SERVICE"
        private const val ACTION_DISCONNECT_FCC = "ACTION_STOP_FCC_SERVICE"
        lateinit var ip: String
        var port: Int = 7510
        var listenerPort: Int = 7778
        private var isServerStarted = false
        private val logWriter = LogWriter("FuelPosService")
        var terminal: TerminalModel? = null
        var prefs = MainApp.getPrefs()
        var stationMode = 0

        @JvmStatic fun isRunning(context: Context/*, serviceClass: Class<*>*/): Boolean { //for all service class use = serviceClass: Class<*> as parameter
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            for (service in activityManager.getRunningServices(Integer.MAX_VALUE)) {
                if (/*serviceClass.name*/FuelPosService::class.java.name == service.service.className) {
                    return true
                }
            }
            return false
        }
        @JvmStatic fun start(context: Context){

            fuelpos = prefs.getFuelPosModel()!!
             val referenceModel = prefs.getReferenceModel()
            fiscalPrinterModel = referenceModel!!.fiscal_printer!!
            if(fiscalPrinterModel.isAvailable && fiscalPrinterModel.isTIMSRequired == AppConstant.TIMS_REQUIRED)
            {
                fuelQtyUnit = referenceModel.FUEL_QTY_UNIT ?: "L"
                fuelVat = referenceModel.fuelVat!!
            }
            stationMode = FusionService.prefs.getStationModel()!!.mode
            this.ip = fuelpos.ipAddress!!
            this.port = fuelpos.tcpPort
            this.listenerPort =  fuelpos.tcpListener
            val mIntent = Intent(context, FuelPosService::class.java)
            mIntent.action = ACTION_START_FOREGROUND_SERVICE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,mIntent)
            } else {
                context.startService(mIntent)
            }
        }
        @JvmStatic fun stop(context: Context){
            val intent = Intent(context, FuelPosService::class.java)
            intent.action = ACTION_STOP_FOREGROUND_SERVICE

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
                //context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }

        /*@JvmStatic fun restart(){
            logWriter.appendLog(TAG,"Restart FuelPOS called")
            stopFuelPos()
            Handler(Looper.getMainLooper()).postDelayed({
                startFuelPos()
            },2000)
        }*/
        @JvmStatic fun isConnected() : Boolean {
            return try {
                if(::tcpClient.isInitialized) {
                    logWriter.appendLog(TAG,"CLIENT CONNECTION:::: ${tcpClient.isConnected}")
                    tcpClient.isConnected
                } else {
                    false
                }
            } catch (e: Exception) {
                logWriter.appendLog(TAG,"EXCEPTION: ${e.message}, ${e.cause}")
                return false
            }
        }

        //region FuelPos EPR Methods
        var sendCount = 0
        private lateinit var tcpClient : FuelPosTcpClient
        private lateinit var tcpServer: FuelPosSingleTcpServer

        @JvmStatic fun restartFuelPosEpr(){
            logWriter.appendLog(TAG,"restartFuelPosEpr()")
            stopFuelPosEpr()
            startFuelPosEpr()
        }
        @JvmStatic fun startFuelPosEpr(){
            logWriter.appendLog(TAG,"startFuelPosEpr()")

            try {
                logWriter.appendLog(TAG,"START TERMINAL CLIENT")
                initTcpClient()
                logWriter.appendLog(TAG,"TERMINAL CLIENT STARTED")
            } catch (e:Exception){
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }

            //startFuelPosEprServer()
        }
        @JvmStatic fun stopFuelPosEpr() {
            logWriter.appendLog(TAG,"stopFuelPosEpr()")
            try{
                logWriter.appendLog(TAG,"STOP TERMINAL CLIENT")
                if(::tcpClient.isInitialized)
                    tcpClient.disconnect()
                logWriter.appendLog(TAG,"TERMINAL CLIENT STOPPED")
            } catch (e:Exception){ logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e)) }

            logWriter.appendLog(TAG,"STOP TERMINAL SERVER")

            //stopFuelPosEprServer()
        }

        @JvmStatic fun startFuelPosEprServer(){
            if(!isServerStarted){
                tcpServer = FuelPosSingleTcpServer(tcpServerListener)

                if(!tcpServer.isServerRunning){
                    tcpServer.startServer(listenerPort)
                } else {  logWriter.appendLog(TAG, "TERMINAL SERVER ALREADY RUNNING")  }

            } else {
                logWriter.appendLog(TAG,"TERMINAL SERVER ALREADY STARTED")
            }
        }
        @JvmStatic fun stopFuelPosEprServer(){
            if(isServerStarted) {
                try{
                    if(::tcpServer.isInitialized && tcpServer.isServerRunning) {
                        tcpServer.closeServer()
                    }
                    logWriter.appendLog(TAG,"TERMINAL SERVER STOPPED")
                } catch (e:Exception){
                    e.printStackTrace()
                    logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                }
            } else {
                logWriter.appendLog(TAG,"TERMINAL SERVER ALREADY STOPPED")
            }
        }
        @JvmStatic fun restartFuelPosEprServer(){
            stopFuelPosEprServer()
            startFuelPosEprServer()
        }


        private fun initTcpClient(){
            try {
                if(::ip.isInitialized) {
                    tcpClient = FuelPosTcpClient(ip, port, tcpClientListener)
                    tcpClient.connect()
                }
                else
                {
                    start(MainApp.appContext)
                }
            } catch (e:Exception)
            {
             e.printStackTrace()
              start(MainApp.appContext)
            }

        }

        private fun sendLogOnMessage(){
            var message = ""
            try{
                terminal = mSharedPrefsUtil.getReferenceModel()!!.terminal
                //message = logOn(fuelpos.tcpUser!!, fuelpos.tcpPass!!)
                message = logOn(terminal!!.eprUsername!!, terminal!!.eprPassword!!)
                tcpClient.send(message)
            } catch (e:Exception){
                message = logOn("EPR_01", "83E83B5B2")
                tcpClient.send(message)
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
            logWriter.appendLog(TAG, "heartbeat sent: $message")
        }

        private val tcpClientListener = object : FuelPosTcpClient.FuelPosClientListener {
            override fun onHeartbeat() {
                //tcpClient.send(logOn("EPR_01", "83E83B5B2"))
                sendLogOnMessage()
            }
            override fun onConnectionStateChanged(state: ConnectionState) {
                when (state) {
                    ConnectionState.CONNECTED -> {
                        logWriter.appendLog(TAG,"Connected")
                        sendBroadCastMessage(
                            ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,
                            FuelPosClientState.CONNECTED)
                        sendLogOnMessage()
                    }
                    ConnectionState.CONNECTING -> {
                        logWriter.appendLog(TAG,"Connecting")
                        sendBroadCastMessage(
                            ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,
                            FuelPosClientState.CONNECTING)
                    }
                    else -> {
                        logWriter.appendLog(TAG,"Disconnected")
                        sendBroadCastMessage(
                            ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,
                            FuelPosClientState.DISCONNECTED)
                    }
                }
            }
            override fun onError(error: ConnectionError) {
                logWriter.appendLog(TAG,"Error: ${error.name}")
                if(sendCount < 1 && error == ConnectionError.DATA_SEND_FAILED) {
                    sendCount++
                    sendRequest(oldRequest)
                }
                else
                {
                    sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,ConnectionState.DISCONNECTED.name, error = error.name)
                    //sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE, FuelPosClientState.DISCONNECTED)
                }
            }
            override fun onDataSend() {
                Log.i(TAG, "Data Send Success")
                sendCount = 0
                oldRequest = ""
            }
            override fun onDataReceived(message: String, bytes: ByteArray) {
                var st = ""
                for (b in bytes) { st += "${String.format("%02X", b)} " }
                Log.i(TAG,"Packet Received: $message ")
                sendBroadCastMessage(ACTION_FUEL_POS_REPLY, FUEL_POS_REPLY,message)
            }
        }
        private var tcpServerListener = object: FuelPosSingleTcpServer.TcpServerListener {
            override fun serverStarted(port: Int) {
                isServerStarted = true
                logWriter.appendLog(TAG,"FUEL POS Server Started: $port")
                sendBroadCastMessage(
                    ACTION_FUEL_POS_SERVER_STATE,
                    FUEL_POS_SERVER_STATE,
                    FuelPosServerState.STOPPED)
            }

            override fun serverClosed(port: Int) {
                isServerStarted = false
                logWriter.appendLog(TAG,"FUEL POS Server Stopped: $port")
                sendBroadCastMessage(
                    ACTION_FUEL_POS_SERVER_STATE,
                    FUEL_POS_SERVER_STATE,
                    FuelPosServerState.STARTED)
            }

            override fun clientConnected(
                socket: Socket?,
                localAddress: InetAddress?,
                port: Int,
                localSocketAddress: SocketAddress?,
                clientIndex: Int,
            ) {
                logWriter.appendLog(TAG,"Clinet Connected: ${socket!!.inetAddress}")
                sendBroadCastMessage(
                    ACTION_FUEL_POS_SERVER_STATE,
                    FUEL_POS_SERVER_STATE,
                    FuelPosServerState.CLIENT_CONNECTED)
            }

            override fun clientDisconnected(
                socket: Socket?,
                localAddress: InetAddress?,
                port: Int,
                localSocketAddress: SocketAddress?,
                clientIndex: Int,
            ) {
                logWriter.appendLog(TAG,"Client Disconnected: ${socket!!.inetAddress}")
                sendBroadCastMessage(
                    ACTION_FUEL_POS_SERVER_STATE,
                    FUEL_POS_SERVER_STATE,
                    FuelPosServerState.CLIENT_DISCONNECTED)
            }

            override fun messageReceived(message: String?, bytes: ByteArray, clientIndex: Int) {
                logWriter.appendLog(TAG,"FUEL POS Server Received: $message")

                if(!message.isNullOrEmpty() && message.contains(FuelPosReply.TRANSACTION_DATA))
                    performNextStep(message)

                sendBroadCastMessage(ACTION_FUEL_POS_MESSAGE, FUEL_POS_MESSAGE,message!!)
            }

        }
        private fun performNextStep(message: String) {
            if (message.contains(FuelPosReply.TRANSACTION_DATA)) {
                val item = FuelPosReply.getTransactionDataMessage(message)
                logWriter.appendLog(TAG,"TRANSACTION DATA IN FUEL POS SERVICE: $message")

                if(item!=null){
                    deleteFuelPosTransaction(item)
                } else {
                    logWriter.appendLog(TAG,"NULL TRANSACTION DATA FOUND: $message")
                }

            }
        }

//        private fun signEsdIfAvailable(item: TransactionData?){
//            if (item!=null && item.totalAmount != "0" && item.completionCode == "${CompletionCode.OK}") {
//                val mPref = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))
//                val decimal = mPref.getDecimal()
//                val totalAmount = toDecimalValue(item.totalAmount, decimal)
//
//                //val command = deleteTransaction(item.trxToken)
//                //logWriter.appendLog(TAG,"DELETE COMMAND SENT : $command")
//                //logWriter.appendLog(TAG, command)
//                //sendReplyToClient(command, fuelpos.ipAddress!!)
//
//                if (item.completionCode == "0") {
//                    if(fiscalPrinterModel.isAvailable) {
//                        val terminalNozzleDetails = referenceModel!!.RFID_TERMINALS
//                        if (terminalNozzleDetails != null) {
//                            val pumpsModels = terminalNozzleDetails.pumps
//                            if (pumpsModels != null && pumpsModels.isNotEmpty()) {
//                                for (terminalPump in pumpsModels) {
//                                    if (item.pump == "${terminalPump.pump_number}") {
//                                        logWriter.appendLog(TAG,"${terminalPump.pump_number} = ${item.pump}")
//                                        for(nozzle in terminalPump.nozzles)
//                                        {
//                                            Log.i(TAG,"fbs_prod_id: "+nozzle.fbs_prod_id)
//                                            Log.i(TAG,"productCode: "+item.productCode)
//                                            if(nozzle.fbs_prod_id.toString() == item.productCode)
//                                                 {
//                                                addFuelTransactionToList(item)
//                                           }
//                                        }
//                                        //saveTransactionInEsdSignTable(item)
//                                    }
//                                    else
//                                    {
//                                        logWriter.appendLog(TAG,"${terminalPump.pump_number} != ${item.pump}")
//                                    }
//                                }
//                            }
//                        }
//                    }
//                } else {
//                    logWriter.appendLog(TAG,"Message Status: ${CompletionCode.getMessage(item.completionCode)}")
//                }
//
//            } else {
//                if (BuildConfig.DEBUG) {
//                    /* Handler().postDelayed({
//                         val command = deleteTransaction(item!!.trxToken)
//                         logWriter.appendLog(TAG, command)
//                         sendRequest(command)
//                     }, 100)*/
//                }
//            }
//        }

        private var oldRequest = ""
        @JvmStatic fun sendRequest(request:String){
            if(tcpClient.isConnected)
                tcpClient.send(request)
            else
            {
                logWriter.appendLog(TAG,"tcpClient not connected")

                //restart()
                //oldRequest = request
                //tcpClient.send(request)
            }
        }
        @JvmStatic fun sendReplyToClient(message:String,clientIp:String) {
            for(client in tcpServer.getClients()){
                val ip = "${client.value.getSocket().inetAddress}".replace("/","")
                logWriter.appendLog(TAG,"CLIENT: $ip = $clientIp")
                if(ip.contains(clientIp)){
                    val thread = Thread {
                        //tcpServer.send(client.key,message)
                        tcpServer.broadcast(message)
                    }
                    thread.start()
                }
            }
        }

        // as bunseng's suggestion terminal should send delete message for all the transaction which terminal is receiving from fuelPOS
        private fun deleteFuelPosTransaction(transactionData : TransactionData){
            Handler(Looper.getMainLooper()).postDelayed({
                logWriter.appendLog(TAG,"SENDING DELETE TRANSACTION FROM SERVICE")
                val command = deleteTransaction(transactionData.trxToken)
                sendReplyToClient(command, fuelpos.ipAddress!!)

                val releaseToken = transactionData.trxToken

                try {
                    val transactionDao = TransactionDao()
                    var transactionModel = transactionDao.getTransactionByReleaseToken(releaseToken)
                    if (transactionModel != null && transactionModel.flagTelecollecte == 0) {

                        val fccSaleId = transactionData.trxToken
                        val seqNo = transactionData.trxToken
                        val fpPump = transactionData.pump
                        val fpProduct = transactionData.productCode
                        val productName = transactionData.productName
                        val endTimeStamp = transactionData.dateTime

                        var paymentType = FUSION_PAYMENT_TYPES.CASH_VALUE
                        var referenceNo = ""

                        val receiptSetting = referenceModel!!.terminalConfig!!.receiptSetting!!
                        val qtyDecimal = receiptSetting.quantityDecimal ?: 2
                        val uniteDecimal = receiptSetting.unitPriceDecimal ?: 2
                        val totalDecimal = receiptSetting.totalAmountDecimal ?: 2

                        val fuelTrxCommonModel = FuelTrxCommonModel(transactionData.pump, transactionData.productCode,
                            toDecimalValue(transactionData.totalAmount, totalDecimal).toString() + "",
                            toDecimalValue(transactionData.quantity, qtyDecimal).toString() + "",
                            toDecimalValue(transactionData.unitPrice, uniteDecimal).toString() + ""
                        )

                        paymentType = Support.getFusionPaymentType(transactionModel.modepay!!)
                        referenceNo = transactionModel.reference!!

                        transactionModel.pumpId = fpPump
                        transactionModel.sequenceController = seqNo
                        transactionModel.amount = fuelTrxCommonModel.amount!!.toDouble()
                        transactionModel.quantite =  fuelTrxCommonModel.quantity!!.toDouble()
                        transactionModel.unitPrice = fuelTrxCommonModel.unityPrice!!.toDouble()
                        transactionModel.fccProductId = fpProduct
                        transactionModel.fccSaleId = fccSaleId
                        transactionModel.productName = productName
                        transactionModel.dateTransaction = Support.dateToString(Date()) //Support.fuelPosDateToDateString(endTimeStamp) commented fuelpos trx date
                        transactionModel.fccReleaseToken = releaseToken
                        transactionModel.reference = referenceNo
                        transactionModel.transactionStatus = 1                  // added to make trx completed
                        transactionModel.flagTelecollecte = 0                   // Added to resolve fuelUP Missing trx Issue (if transaction is not completed due to pump error,
                        transactionModel.isPumpError = 0                        // after that incomplete transaction is tele-collected and after tele-collection same transaction cleared

                        transactionDao.updateTransactionsByReferenceID(transactionModel)
                    }
                    else {
                       /* transactionModel = TransactionModel()
                        transactionModel.reference = Support.generateNewReferenceNumber(MainApp.appContext)*/

                        logWriter.appendLog(TAG,"TRX NOT FOUND IN DB FOR RELEASE TOKEN $releaseToken or transaction is already telecollected")
                    }
                }
                catch (e:Exception)
                {
                    e.printStackTrace()
                }

            },200)
        }
        //endregion

        //region FuelPos EXT Methods
        private lateinit var extPosClient: FuelPosTcpClient
        private val pumpStateListener = object : FuelPosTcpClient.FuelPosClientListener {
            override fun onHeartbeat() {
                Log.i(TAG,"onHeartbeat:: "+FuelPosCommands.posHeartbeat())
                try{
                    if(extPosClient.isConnected)
                        extPosClient.send(FuelPosCommands.posHeartbeat())
                } catch (e:Exception){
                    e.printStackTrace()
                    logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                }
                logWriter.appendLog(TAG, "pump state heartbeat sent")

            }

            override fun onConnectionStateChanged(state: ConnectionState) {
                Log.i(TAG, "onConnectionStateChanged:: $state")
                if (state === ConnectionState.CONNECTED) {

                    var senderId = Support.getSN()!!
                    if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)

                    val extUsername = (terminal!!.extUsername ?: fuelpos.userExt) ?: "EXTPOS_1"
                    val extPassword =  (terminal!!.extPassword ?: fuelpos.passExt) ?: "8E493E2DE"

                    val message = FuelPosCommands.posLogOn(extUsername, extPassword,senderId)
                    //val message = "LOGIN 1:0|99990:EXTPOS_1|99991:8E493E2DE|99992:EXTPOS1|99993:TPEBTEST|99994:1|\r\n"
                    //val message = "LOGIN 1:0|99990:EXTPOS_1|99991:85165D19C|99992:EXTPOS1|99993:TPEBTEST|99994:1|\r\n"
                    extPosClient.send(message)
                }
            }

            override fun onError(error: ConnectionError) {
                logWriter.appendLog(TAG,"pos Connection error ${error.name}")
                if(error.name == "TIMEOUT"){
                    sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,ConnectionError.TIMEOUT.name)
                }
                else
                    sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,ConnectionState.DISCONNECTED.name, error = error.name)
                //startPosPumpStateClient()
            }

            override fun onDataSend() {
                logWriter.appendLog(TAG,"pos Data send success to pos")
            }

            override fun onDataReceived(message: String, bytes: ByteArray) {
                logWriter.appendLog(TAG, "POS MESSAGE: $message")
                //if(!message.contains("230 9999"))
                sendBroadCastMessage(ACTION_POS_MESSAGE, POS_MESSAGE,message)
//                if (stationMode == AppConstant.AFTER_TRX_MODE) {
//                    saveTransactiononDB(message)
//                }
            }

        }

        @JvmStatic fun startExtPosClient(){
           try{
               this.ip = fuelpos.ipAddress!!
               this.port = fuelpos.tcpPort
               extPosClient = FuelPosTcpClient(ip, 7501, pumpStateListener,true, 10)
               extPosClient.connect()
           } catch (e: RuntimeException){
               logWriter.appendLog(TAG,"Unable to connect pump StateClient")
           }
        }
        @JvmStatic fun stopExtPosClient(){
            extPosClient.disconnect()
        }

        @JvmStatic fun sendViaPosProtocol(message: String){
            try {
                if(extPosClient.isConnected)
                {
                    extPosClient.send(message)
                }
            } catch (e:Exception){
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
        }
        //endregion


        private fun sendBroadCastMessage(action:String, key:String, message: String, error:String = ""){
            val intent = Intent(action)
            if(error.isNotEmpty()){
                intent.putExtra(key, "$message - $error")
            }
            else {
                intent.putExtra(key, message)
            }
            MainApp.appContext.sendBroadcast(intent)
        }

        private var mSharedPrefsUtil : AppPreferencesHelper
        private var fiscalPrinterModel: FiscalPrinterModel
        var fuelQtyUnit = "L"
        var fuelVat = VatModel()
        private var fuelpos: FuelPOSModel
        var referenceModel:ReferenceModel?=null
        init {
            mSharedPrefsUtil = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))
            referenceModel=mSharedPrefsUtil.getReferenceModel()
            try{
                terminal = referenceModel!!.terminal
                Log.e(TAG, "Terminal details: ${Gson().toJson(terminal)}")
            }
            catch (e:Exception) {
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
            terminal = referenceModel!!.terminal
            fiscalPrinterModel = referenceModel!!.fiscal_printer!!
            fuelpos = referenceModel!!.FUELPOS
        }


        /*          TIMS region       */
        var isInclusive = true
        var type = "Incl."
        var taxModel: TaxModel? = null
        var vatAmount = 0.0

        fun saveTransactiononDB(message:String)
        {
            if (message.contains(FuelPosReply.FILLING_STATES)) {
                stopFuelPosReceiver()
                val transactions: ArrayList<PosTransaction> = FuelPosReply.getPosTransactions(message)
                logWriter.appendLog(TAG, "POS TRX: " + Gson().toJson(transactions))
                    val transactionDao = TransactionDao()
                    transactionDao.open()
                    var decimal = fuelpos.totalAmountDecimal ?: fuelpos.decimal
                    val pumpsModels: List<RFIDPumpsModel> = prefs.getReferenceModel()!!.RFID_TERMINALS!!.pumps
                    if (pumpsModels != null && pumpsModels.isNotEmpty()) {
                        for (transaction in transactions) {
                            if (transaction.fillingState == PosFillingState.PAYABLE || transaction.fillingState == PosFillingState.PAYABLE_AGAIN){
                                for (pump in pumpsModels) {
                                    if (transaction.pumpNumber == pump.pump_number.toString()) {
                                        for(nozzle in pump.nozzles)
                                        {
                                          if(nozzle.nozzle_number.toString() == transaction.nozzleNumber)
                                            {
                                            if(transaction.fillingVolume.toDouble() != 0.0)
                                            {
                                                // val referenceTransaction: String =  transaction.pumpNumber + ":" +transaction.fillingId + ":"+ transaction.trxChecksum + mTerminal!!.terminalId + mTerminal!!.stationId
                                                if (transactionDao.checkTransactionCount(transaction.trxChecksum) == 0) {
                                                    addFuelTransactionToList(transaction)
                                                }
                                            }
                                            }
                                        }

                                    }
                                }
                            }
                        }

                    }
                    transactionDao.close()
            }
        }

        fun addFuelTransactionToList(currentTransaction: PosTransaction)
        {
            try {
                val productsModel= getProductModel(currentTransaction.fillingFuelNumber.toInt())
                if (productsModel != null) {
                    var nozzle = 0
                    try {
                        nozzle = currentTransaction.nozzleNumber.toInt()
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                    //Calculate VAT
                    if (fuelVat.enabled) {
                        isInclusive = fuelVat.type == 0
                        type = if (isInclusive) "Incl." else "Excl."
                        taxModel = TaxUtils.calculate(
                            currentTransaction.fillingAmount.toDouble(),
                            fuelVat.percentage!!.toDouble(),
                            isInclusive
                        )
                        vatAmount = Support.formatString(taxModel!!.taxAmount)!!.toDouble()
                    }
                    val totalDecimal = fuelpos.totalAmountDecimal ?: 2
                    val qtyDecimal = fuelpos.quantityDecimal ?: 2
                    val uniteDecimal = fuelpos.unitPriceDecimal ?: 2
                    val fuelPosTransaction = TransactionFromFcc()
                    fuelPosTransaction.amount = toDecimalValue(currentTransaction.fillingAmount, totalDecimal)
                    fuelPosTransaction.dh_transaction = Support.getFormatTrxDate(Date())
                    fuelPosTransaction.hose = nozzle
                    fuelPosTransaction.pompiste = ""
                    fuelPosTransaction.pu =
                        toDecimalValue(currentTransaction.unitPrice, uniteDecimal)
                    fuelPosTransaction.pump = currentTransaction.pumpNumber.toInt()
                    fuelPosTransaction.quantite =
                        toDecimalValue(currentTransaction.fillingVolume, qtyDecimal)
                    val referenceTransaction: String = currentTransaction.pumpNumber + ":" + currentTransaction.fillingId + ":" + currentTransaction.trxChecksum + terminal!!.terminalId + terminal!!.stationId
                    fuelPosTransaction.ref_transaction = referenceTransaction
                    fuelPosTransaction.rfid = ""
                    fuelPosTransaction.currency = prefs.currency
                    fuelPosTransaction.productId =productsModel.productID
                    fuelPosTransaction.releaseToken = currentTransaction.trxChecksum
                    fuelPosTransaction.fusionSaleId = currentTransaction.trxChecksum
                    fuelPosTransaction.fuelQtyUnit = fuelQtyUnit
                    fuelPosTransaction.vatAmount = vatAmount.toString()
                    if (productsModel != null) {
                        fuelPosTransaction.produit = productsModel.libelle
                        fuelPosTransaction.hsCode = productsModel.hs_code
                        fuelPosTransaction.productId = productsModel.productID
                    } else {
                        fuelPosTransaction.produit = "Product ${currentTransaction.fillingFuelNumber}"
                    }

                    val fuelTransactionStatusDAO = FCCTransactionsDao()
                    fuelTransactionStatusDAO.open()
                    fuelPosTransaction.id = fuelTransactionStatusDAO.insert(fuelPosTransaction)
                    fuelTransactionStatusDAO.close()
                    if (prefs.isSignInBackground && fiscalPrinterModel.isAvailable && fiscalPrinterModel.isTIMSRequired == AppConstant.TIMS_REQUIRED && referenceModel!!.station!!.mode == AppConstant.AFTER_TRX_MODE) {
                        checkTransactionCountPerPump(fuelPosTransaction.pump)
                    }
                }
            } catch (e: Exception) {
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }


        }
        private fun getProductModel(id:Int) : ProductModel? {
            var product : ProductModel? = null
            try {
                val productDAO = ProductsDao()
                productDAO.open()
                product = productDAO.getProductById(id.toInt())
                productDAO.close()

            } catch (e:Exception) {
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                e.printStackTrace()
            }
            return product
        }
        @JvmStatic fun checkTransactionCountPerPump(pumpNo:Int)
        {
            var buffer_sign_transaction_count = 2
            if(referenceModel != null && referenceModel!!.buffer_sign_transaction_count != null)
            {
                buffer_sign_transaction_count = referenceModel!!.buffer_sign_transaction_count!!
            }
            if(buffer_sign_transaction_count > 0 && prefs.isSignInBackground && fiscalPrinterModel.isAvailable && fiscalPrinterModel.isTIMSRequired == AppConstant.TIMS_REQUIRED && referenceModel!!.station!!.mode == AppConstant.AFTER_TRX_MODE) {
                Handler(Looper.getMainLooper()).post {
                    try {
                        val fuelTransactionStatusDAO = FCCTransactionsDao()
                        fuelTransactionStatusDAO.open()
                        val count = fuelTransactionStatusDAO.checkTransactionCountPerPump(pumpNo)
                        logWriter.appendLog(TAG,"pendingTransactionCount : $count")
                        if(count >= buffer_sign_transaction_count)
                        {
                            val transactionFromFcc = fuelTransactionStatusDAO.getPumpUnsignedTransaction(pumpNo)
                            if (transactionFromFcc != null) {
                                generateFPInvoice(
                                    transactionFromFcc,
                                    fuelTransactionStatusDAO,
                                    fuelVat
                                )
                            }
                        }
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }

        }
        private fun generateFPInvoice(transactionFromFcc: TransactionFromFcc?, fuelTransactionStatusDAO: FCCTransactionsDao, fuelVatModel: VatModel) {
            Handler(Looper.getMainLooper()).post {
                val timsGenerate = TIMSInvoiceGenerate(MainApp.appContext)
                timsGenerate.invoiceReceiptListner = object : TIMSInvoiceGenerate.InvoiceReceiptListner {
                    override fun onSuccess(model: CloseReceiptRes?, mTransaction: TransactionModel?) {
                        fuelTransactionStatusDAO.deleteTransactionFusion(mTransaction!!.fccSaleId!!)
                        lastClaimTransaction = mTransaction
                        startFuelPosReceiver()
                        checkTransactionCountPerPump(mTransaction.pumpId!!.toInt())
                    }
                    override fun onFailed(message: String,method:String) {
                        prefs.isTimsStarted = false
                        checkTransactionCountPerPump(transactionFromFcc!!.pump)
                        logWriter.appendLog(TAG, "Failed to Sign ::$message")
                    }

                }
                timsGenerate.startTIMSServer(fiscalPrinterModel, transactionFromFcc!!,fuelVatModel)
            }
        }

        /* End TIMS region */
        //endregion

    /** region clear Transaction */
    var lastClaimTransaction:TransactionModel?=null
    private var unclaimFirst = false
    private fun startFuelPosReceiver() {
        if (!isRunning(MainApp.appContext)) start(MainApp.appContext)
        val filter = IntentFilter()
        filter.addAction(ACTION_FUEL_POS_REPLY)
        filter.addAction(ACTION_FUEL_POS_MESSAGE)
        filter.addAction(ACTION_POS_MESSAGE)
        filter.addAction(ACTION_FUEL_POS_FILLING_STATE)
        MainApp.appContext.registerReceiver(fuelPosReceiver, filter)
        logWriter.appendLog(TAG, "FuelPOS receiver started")
        startExtPosClient()
        if(lastClaimTransaction != null) {
            val refId = lastClaimTransaction!!.sequenceController!!.split(":")
            // transaction.pumpNumber + ":" +transaction.fillingId + ":"+ transaction.trxChecksum.toString() + /*":" +*/ + mTerminal!!.terminalId + mTerminal!!.stationId /*+ ":" + transaction.getMessageSequence()*/
            val pumpNumber = refId[0]
            val fillingId = refId[1]

            //LOGIN then CLAIM TRX
            Handler(Looper.getMainLooper()).postDelayed({
                if (!trxClaimFlags[0]) {
                    val claimMessage: String = if (unclaimFirst) {
                        FuelPosCommands.unclaimTransaction( /*msgSeq,*/pumpNumber, fillingId)
                    } else {
                        FuelPosCommands.claimTransaction( /*msgSeq,*/pumpNumber, fillingId)
                    }
                    sendViaPosProtocol(claimMessage)
                    logWriter.appendLog(TAG, "COMMAND SENT TO FUEL POS: $claimMessage")
                    trxClaimFlags[0] = true
                }
            },2000)
        }
    }
    private fun stopFuelPosReceiver() {
        try {
            lastClaimTransaction = null
            MainApp.appContext.unregisterReceiver(fuelPosReceiver)
            logWriter.appendLog(TAG, "FuelPos receiver stopped")
            stopExtPosClient()
        } catch (e: java.lang.Exception) {
        }
    }
    private var fuelPosReceiver: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context?, intent: Intent) {
            val action = intent.action
            logWriter.appendLog(TAG, "FUELPOS ACTION $action")
            var message: String?
            when (action) {
                ACTION_FUEL_POS_REPLY -> {
                    message = intent.getStringExtra(FUEL_POS_REPLY)
                    logWriter.appendLog(TAG, "POS MESSAGE: $message")
                }
                ACTION_POS_MESSAGE -> {
                    message = intent.getStringExtra(POS_MESSAGE)
                    if(!message.isNullOrEmpty() && !message.contains("PUMPSTATES") && !message.contains("FILLINGSTATES"))
                        onFuelPosDataReceived(message)
                }
                ACTION_FUEL_POS_MESSAGE -> {
                    message = intent.getStringExtra(FUEL_POS_MESSAGE)
                    logWriter.appendLog(TAG, "FUEL_POS_MESSAGE: $message")
                }
            }
        }
    }
    private var trxClaimFlags = arrayOf(false,false,false)
    fun onFuelPosDataReceived(message:String)
    {
        val refId = lastClaimTransaction!!.sequenceController!!.split(":")
        val pumpNumber = refId[0]
        val fillingId = refId[1]
        if (message.contains(FuelPosReply.UNCLAIME_TRANSACTION_RESULT)) {
            val claimMessage = FuelPosCommands.claimTransaction( /*msgSeq,*/pumpNumber, fillingId)
            sendViaPosProtocol(claimMessage)
            logWriter.appendLog(TAG, "COMMAND SENT TO FUEL POS: $claimMessage")
            trxClaimFlags[0] = true
        }
        //TRANSACTION CLAIMED then CLEAR TRX
        if (message.contains(FuelPosReply.CLAIM_TRANSACTION_RESULT)) {
            val result: ClaimedTransactionResult? = FuelPosReply.getClaimTransactionResult(message)
            if (result!!.pumpNumber == pumpNumber && result.fillingId == fillingId && result.completionCode == CompletionCode.OK.toString() + "") {
                val clearMessage = FuelPosCommands.clearTransaction( /*msgSeq,*/pumpNumber, fillingId)
                sendViaPosProtocol(clearMessage)
                trxClaimFlags[1] = true
            } else {
                stopFuelPosReceiver()
                unclaimFirst = true
            }
        }
        //TRANSACTION CLEARED then check paid state
        if (message.contains(FuelPosReply.CLEAR_TRANSACTION_RESULT) && trxClaimFlags[0] && trxClaimFlags[1]) {
            val result: ClearTransactionResult? = FuelPosReply.getClearTransactionResult(message)
            if (result!!.pumpNumber == pumpNumber && result.fillingId == fillingId && result.completionCode == CompletionCode.OK.toString() + "") {
                trxClaimFlags[2] = true
                stopFuelPosReceiver()
            } else {
                stopFuelPosReceiver()
                unclaimFirst = true
            }
        }
    }

    /** endregion clear Transaction */

    }
    override fun onBind(intent: Intent?): IBinder? {
        // TODO: Return the communication channel to the service.
        //throw UnsupportedOperationException("Not yet implemented")
        return null
    }

    override fun onCreate() {
        super.onCreate()
        startForegroundNotification()
        logWriter.appendLog(TAG, "FuelPosService onCreate().")
    }


    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if(intent != null) {
            when (intent.action) {
                ACTION_START_FOREGROUND_SERVICE -> {
                    //startFuelPos()
                    //toast("FuelPosService started.")
                }
                ACTION_STOP_FOREGROUND_SERVICE -> {
                    //stopFuelPos()
                    stopForegroundService()
                    //toast("FuelPosService stopped.")
                }
                ACTION_CONNECT_FCC -> {
                    /*try{
                        tcpClient.connect()
                    } catch (e:UninitializedPropertyAccessException){
                        initTcpClient()
                    }*/

                    //extPosClient.connect()
                }
                ACTION_DISCONNECT_FCC -> {
                    //tcpClient.disconnect()
                    //extPosClient.connect()
                }
            }
        }

        return START_NOT_STICKY //super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        super.onDestroy()
        logWriter.appendLog(TAG,"On Destroy Called")
        //stopFuelPos()
        //stopFuelPos()
    }

    //region foreground notification
    private val CHANNEL_ID = "fuelPosService"
    private val CHANNEL_NAME = "FuelPosService"
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(channelId: String, channelName: String): String {
        val channel = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_DEFAULT)
        channel.lightColor = Color.BLUE
        channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val service = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        service.createNotificationChannel(channel)
        return channelId
    }
    private fun startForegroundNotification() {
        logWriter.appendLog(TAG, "Start FuelPosService.")

        val intent = Intent(this, MenuActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, 0)

        val defaultSoundUri = Uri.parse("android.resource://app.rht.petrolcard/" + R.raw.connect)

        val channelId =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel(CHANNEL_ID, CHANNEL_NAME)
            } else { "" }

        val notificationBuilder = NotificationCompat.Builder(this, channelId )

        val notification = notificationBuilder.setOngoing(true)
            .setSmallIcon(android.R.drawable.stat_notify_sync)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setOnlyAlertOnce(true)
            //.setCategory(Notification.CATEGORY_SERVICE)
            .setContentTitle("FuelPOS")
            .setContentText("Service Running")
            .setSound(defaultSoundUri)
            .setSilent(true)
            .setFullScreenIntent(pendingIntent,true)
            .build()

        startForeground(101, notification)
    }
    private fun stopForegroundService() {
        logWriter.appendLog(TAG, "Stop FuelPosService.")
        stopForeground(true)
        stopSelf()
    }
    //endregion


}