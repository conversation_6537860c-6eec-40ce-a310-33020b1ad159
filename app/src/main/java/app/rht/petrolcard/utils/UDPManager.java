package app.rht.petrolcard.utils;

import static app.rht.petrolcard.utils.LogUtils.log;

import android.util.Log;

import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.SocketException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public  class UDPManager {
    private static UDPManager udpManager;
    private static final String TAG = "UDPBuild";
    //     Single CPU thread pool size
    private static final int POOL_SIZE = 5;
    private static final int BUFFER_LENGTH = 1024;
    private final byte[] receiveByte = new byte[BUFFER_LENGTH];

    private boolean isThreadRunning = false;

    private DatagramSocket client;
    private DatagramPacket receivePacket;

    private final ExecutorService mThreadPool;
    private Thread clientThread;

    private static String SOCKET_HOST = "127.0.0.1";
    private static int SOCKET_UDP_PORT = 5445;

    private OnUDPReceiveCallbackBlock udpReceiveCallback;
    //     Constructor privatization
    private UDPManager() {
        super();
        int cpuNumbers = Runtime.getRuntime().availableProcessors();
//         Initialize the thread pool according to the number of CPUs
        mThreadPool = Executors.newFixedThreadPool(cpuNumbers * POOL_SIZE);
    }
    //     Provide a global static method
    public static UDPManager getUdpManager(String host, int port) {
        if (udpManager == null) {
            SOCKET_HOST = host;
            SOCKET_UDP_PORT = port;
            synchronized (UDPManager.class) {
                if (udpManager == null) {
                    udpManager = new UDPManager();
                }
            }
        }
        return udpManager;
    }

    public  void  startUDPSocket () throws SocketException {
        if (client != null) return;

//             Indicates that this Socket is monitoring data on the set port.
            log(TAG, "receive client...");
            client = new DatagramSocket(SOCKET_UDP_PORT);

            if (receivePacket == null) {
                receivePacket = new DatagramPacket(receiveByte, BUFFER_LENGTH);
            }
            startSocketThread();
    }
    /**
     * Open the thread to send data
     **/
    private void startSocketThread() {
        clientThread = new Thread(new Runnable() {
            @Override
            public  void  run () {
                log(TAG, "clientThread is running...");
                receiveMessage();
            }
        });
        isThreadRunning = true;
        clientThread.start();
    }
    /**
     * Process the received message
     **/
    private void receiveMessage() {
        while (isThreadRunning) {
            if (client != null) {
                try {
                    client.receive(receivePacket);
                } catch (IOException e) {
                    Log . e( TAG , " Failed to receive UDP packet! Thread stopped " );
                    stopUDPSocket();
                    e . printStackTrace ();
                    return;
                }
            }

            if (receivePacket == null || receivePacket.getLength() == 0) {
                Log . e( TAG , " Unable to receive UDP data or the received UDP data is empty " );
                continue;
            }
            String strReceive = new String(receivePacket.getData(), 0, receivePacket.getLength());
            log(TAG, strReceive + " from " + receivePacket.getAddress().getHostAddress() + ":" + receivePacket.getPort());
//             Analyze the received json information
            if (udpReceiveCallback != null) {
                udpReceiveCallback.OnParserComplete(receivePacket);
            }
//After             receiving UDP data, reset the length. Otherwise, the data packet received next time may be truncated.
            if (receivePacket != null) {
                receivePacket.setLength(BUFFER_LENGTH);
            }
        }
    }
    /**
     * Stop UDP
     **/
    public void stopUDPSocket() {
        isThreadRunning = false;
        receivePacket = null;
        if (clientThread != null) {
            clientThread.interrupt();
        }
        if (client != null) {
            client.close();
            client = null;
        }
        removeCallback();
    }
    /**
     * send Message
     **/
    public void sendMessage(String message) {
        if (client == null) {
            try{
                startUDPSocket ();
            } catch (SocketException e) {
                e.printStackTrace();
            }
        }
        mThreadPool.execute(() -> {
            try {
                InetAddress targetAddress = InetAddress.getByName(SOCKET_HOST);

                DatagramPacket packet = new DatagramPacket(message.getBytes(), message.length(), targetAddress, SOCKET_UDP_PORT);
                client.send(packet);
            } catch (IOException e) {
                e . printStackTrace ();
            }
        });
    }

    public interface OnUDPReceiveCallbackBlock {
        void OnParserComplete(DatagramPacket data);
    }
    public void setUdpReceiveCallback(OnUDPReceiveCallbackBlock callback) {
        this.udpReceiveCallback = callback;
    }
    public void removeCallback(){
        udpReceiveCallback = null;
    }
}