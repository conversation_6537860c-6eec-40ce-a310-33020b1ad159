package app.rht.petrolcard.utils.common

import android.graphics.drawable.Drawable
import android.view.View
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import kotlinx.android.synthetic.main.toolbar.*


private const val TAG = "ToolBarHelper"

open class ToolBarHelper(activity: BaseActivity<*>) : IToolBarHandler,View.OnClickListener {

    private var toolbar: androidx.appcompat.widget.Toolbar = activity.toolbar
    private var listenerSearchAction: SearchActionToolBarListener? = null
    private var listenerRightAction: RightActionToolBarListener? = null
    private var listenerSearchActionQuerChange: SearchActionToolBarQueryChangeListener? = null

    fun init() {
    }
    override fun toggleLeft() {
        TODO("Not yet implemented")
    }

    override fun unlockDrawer() {
        TODO("Not yet implemented")
    }

    override fun lockDrawer() {
        TODO("Not yet implemented")
    }

    override fun isDrawerOpen(): Boolean {
        TODO("Not yet implemented")
    }

    override fun setToolBar(visibility: Int) {
        TODO("Not yet implemented")
    }

    override fun setAppBarBgColor(color: Int) {
        TODO("Not yet implemented")
    }

    override fun setLeftMenuOnToolBar(visibility: Int) {
        TODO("Not yet implemented")
    }

    override fun setCenterTitleOnToolBar(visibility: Int, title: String?) {
        TODO("Not yet implemented")
    }

    override fun setBackOnToolBAr(visibility: Int) {
        TODO("Not yet implemented")
    }

    override fun changeColorRightAction(color: Int) {
        TODO("Not yet implemented")
    }

    override fun setSearchActionListener(listener: SearchActionToolBarListener?) {
        if (listener != null)
            this.listenerSearchAction = listener
    }
    override fun setSearchActionChangeListner(listenerRightActionQuerChange: SearchActionToolBarQueryChangeListener?) {
        if (listenerRightActionQuerChange != null)
            this.listenerSearchActionQuerChange = listenerRightActionQuerChange
    }
    override fun setRightActionListener(listener: RightActionToolBarListener?) {
        if (listener != null)
            this.listenerRightAction = listener
    }

    override fun setProfileImageOnToolBar(visibility: Int, path: String?) {
        TODO("Not yet implemented")
    }

    override fun setNameOnToolBar(visibility: Int, title: String) {
        TODO("Not yet implemented")
    }

    override fun setTextOnToolBar(visibility: Int, text: String) {
        TODO("Not yet implemented")
    }
    fun clearSearchText() {

    }
    override fun setRightActionOnToolBar(visibility: Int, drawable: Drawable?) {

    }
    override fun onClick(view: View?) {

        when (view!!.id) {

        }
    }


}