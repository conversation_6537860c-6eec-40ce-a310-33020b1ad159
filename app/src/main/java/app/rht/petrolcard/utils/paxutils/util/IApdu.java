package app.rht.petrolcard.utils.paxutils.util;

/**
 * Apdu interface
 * 
 */
public interface IApdu {

    /**
     * APDU request. CLA(1B) + INS(1B) + P1(1B) + P2(1B) + Lc(0, 1 or 2B) + data(Lc) + Le(0, 1 or 2B). NOTE: By default,
     * Lc is not present if length of request data is 0, call {@link IApduReq#setLcAlwaysPresent()} to make it always
     * present(i.e. Lc is 0 if data length is 0); By default, <PERSON> is present, call {@link IApduReq#setLeNotPresent()} to
     * make it not present; By default, <PERSON><PERSON> and <PERSON> occupies 1 byte if present, call
     * {@link IApduReq#setLengthOfLcLeTo2Bytes()} to change it to 2 bytes when needed.
     */
    interface IApduReq {

        /**
         * By default, Lc &amp; <PERSON> occupies 1 byte. By calling this method, change it to 2 bytes
         */
        void setLengthOfLcLeTo2Bytes();

        /**
         * By default, Lc is not present if length of request data is 0. By calling this method, Lc always present(i.e.
         * Lc is 0 if data length is 0)
         */
        void setLcAlwaysPresent();

        /**
         * By default, Le is always present. By calling this method, Le is not present
         */
        void setLeNotPresent();

        void setCla(byte cla);

        byte getCla();

        void setIns(byte ins);

        byte getIns();

        void setP1(byte p1);

        byte getP1();

        void setP2(byte p2);

        byte getP2();

        void setData(byte[] data);

        byte[] getData();

        void setLe(short le);

        short getLe();

        /**
         * pack APDU request
         * 
         * @return packed bytes
         */
        byte[] pack();
    }

    /**
     * APDU response. data + SW1(1B) + SW2(1B)
     */
    interface IApduResp {
        byte[] getData();

        short getStatus();

        void setData(byte[] data);

        void setStatus(String status);

        String getStatusString();
    }

    /**
     * constructor, P1 = 0, P2 = 0, Lc = 0, data = null, Le = 0x00(1B) or 0xFFFF(2B)
     * 
     * @param cla
     *            CLA
     * @param ins
     *            INS
     * @return APDU request
     */
    IApduReq createReq(byte cla, byte ins);

    /**
     * constructor, P1 = 0, P2 = 0, Le = 0x00(1B) or 0xFFFF(2B)
     * 
     * @param cla
     *            CLA
     * @param ins
     *            INS
     * @param data
     *            [input] data
     * @return APDU request
     */
    IApduReq createReq(byte cla, byte ins, byte[] data);

    /**
     * constructor, P1 = 0, P2 = 0,
     * 
     * @param cla
     *            CLA
     * @param ins
     *            INS
     * @param data
     *            [input] data
     * @param le
     *            Le
     * @return APDU request
     */
    IApduReq createReq(byte cla, byte ins, byte[] data, short le);

    /**
     * constructor, Lc = 0, data = null, Le = 0x00(1B) or 0xFFFF(2B)
     * 
     * @param cla
     *            CLA
     * @param ins
     *            INS
     * @param p1
     *            P1
     * @param p2
     *            P2
     * @return APDU request
     */
    IApduReq createReq(byte cla, byte ins, byte p1, byte p2);

    /**
     * constructor, Le = 0x00(1B) or 0xFFFF(2B)
     * 
     * @param cla
     *            CLA
     * @param ins
     *            INS
     * @param p1
     *            P1
     * @param p2
     *            P2
     * @param data
     *            [input] data
     * @return APDU request
     */
    IApduReq createReq(byte cla, byte ins, byte p1, byte p2, byte[] data);

    /**
     * constructor
     * 
     * @param cla
     *            CLA
     * @param ins
     *            INS
     * @param p1
     *            P1
     * @param p2
     *            P2
     * @param data
     *            [input] data
     * @param le
     *            Le
     * @return APDU request
     */
    IApduReq createReq(byte cla, byte ins, byte p1, byte p2, byte[] data, short le);

    /**
     * unpack APDU response
     * 
     * @param resp
     *            [input] apdu response data bytes
     * @return IApdueResp interface
     */
    IApduResp unpack(byte[] resp);

}
