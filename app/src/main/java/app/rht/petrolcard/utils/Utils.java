package app.rht.petrolcard.utils;

import android.app.Activity;
import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.wifi.WifiManager;
import android.os.Environment;
import android.text.TextUtils;
import android.text.format.Formatter;
import android.util.Base64;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.preference.PreferenceManager;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Locale;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;




public class Utils {

    private static final String TAG = Utils.class.getSimpleName();

    private static final int WRITE_REQUEST_CODE = 43;

    private static final int sizeOfIntInHalfBytes = 8;
    private static final int numberOfBitsInAHalfByte = 4;
    private static final int halfByte = 0x0F;
    private static final char[] hexDigits = {
    '0', '1', '2', '3', '4', '5', '6', '7',
    '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'
    };

    public static String getTimestamp() {
        return new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault()).format(new Date());
    }

    public static String getPassword(String businessShortCode, String passkey, String timestamp) {
        String str = businessShortCode + passkey + timestamp;
        //encode the password to Base64
        return Base64.encodeToString(str.getBytes(), Base64.NO_WRAP);
    }

    public static void doRestart(Context c) {
        try {
            //check if the context is given
            if (c != null) {
                //fetch the packagemanager so we can get the default launch activity
                // (you can replace this intent with any other activity if you want
                PackageManager pm = c.getPackageManager();
                //check if we got the PackageManager
                if (pm != null) {
                    //create the intent with the default start activity for your application
                    Intent mStartActivity = pm.getLaunchIntentForPackage(
                            c.getPackageName()
                    );
                    if (mStartActivity != null) {
                        mStartActivity.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        //create a pending intent so the application is restarted after System.exit(0) was called.
                        // We use an AlarmManager to call this intent in 100ms
                        int mPendingIntentId = 223344;
                        PendingIntent mPendingIntent = PendingIntent
                                .getActivity(c, mPendingIntentId, mStartActivity,
                                        PendingIntent.FLAG_CANCEL_CURRENT);
                        AlarmManager mgr = (AlarmManager) c.getSystemService(Context.ALARM_SERVICE);
                        mgr.set(AlarmManager.RTC, System.currentTimeMillis() + 100, mPendingIntent);
                        //kill the application
                        System.exit(0);
                    } else {
                        log(TAG, "Was not able to restart application, mStartActivity null");
                    }
                } else {
                    log(TAG, "Was not able to restart application, PM null");
                }
            } else {
                log(TAG, "Was not able to restart application, Context null");
            }
        } catch (Exception ex) {
            log(TAG, "Was not able to restart application");
        }
    }

    public static String sanitizePhoneNumber(String phone) {

        if (phone.equals("")) {
            return "";
        }

        if (phone.length() < 11 & phone.startsWith("0")) {
            return phone.replaceFirst("^0", "254");
        }
        if (phone.length() == 13 && phone.startsWith("+")) {
            return phone.replaceFirst("^+", "");
        }
        return phone;
    }


	public static String byteArrayToHex(byte[] a) {
        StringBuilder sb = new StringBuilder(a.length * 2);
        for(byte b: a) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString().toUpperCase();
    } // eof byteArrayToHex

    public static  byte[] fromHexString(String hexString) {
        String hexVal = "0123456789ABCDEF";
        byte[] out = new byte[hexString.length() / 2];

        int n = hexString.length();

        for( int i = 0; i < n; i += 2 ) {
            //make a bit representation in an int of the hex value
            int hn = hexVal.indexOf( hexString.charAt( i ) );
            int ln = hexVal.indexOf( hexString.charAt( i + 1 ) );

            //now just shift the high order nibble and add them together
            out[i/2] = (byte)( ( hn << 4 ) | ln );
        }

        return out;
    } // eof fromHexString


    public static String bytes2HexString(byte[] data) {
        if (data == null)
            return "";
        StringBuilder buffer = new StringBuilder();
        for (byte b : data) {
            String hex = Integer.toHexString(b & 0xff);
            if (hex.length() == 1) {
                buffer.append('0');
            }
            buffer.append(hex);
        }
        return buffer.toString().toUpperCase();
    }


    public static String padZero(String data, int length ){

        String datareturn="";

        String pad ="00000000000000000000000000000000000000000000000000000";
        //String pad ="FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF";

        if (data.length()<length){
                datareturn = pad.substring(0, length-data.length())+data;
                return datareturn ;
        }else  return data;
    }

    public static String padEmptyRight(String data, int length ){

        String datareturn="";

        String pad ="FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF";

        if (data.length()<length){
            datareturn = data+pad.substring(0, length-data.length());
            return datareturn ;
        }else  return data;
    }

    public static String padZeroRight(String data, int length ){

        String datareturn="";

        String pad ="00000000000000000000000000000000000000000000000000000";

        if (data.length()<length){
            datareturn = data+pad.substring(0, length-data.length());
            return datareturn ;
        }else  return data;
    }

    public static String padZeroLEFT(String data, int length ){

        String datareturn="";

        String pad ="00000000000000000000000000000000000000000000000000000";

        if (data.length()<length){
            datareturn =pad.substring(0, length-data.length())+data;
            return datareturn ;
        }else  return data;
    }


    /**
     * Converts a hex string to byte array.
     */
    public static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i+1), 16));
        }
        return data;
    } // hexStringToByteArray
	
    public static byte[] xor(byte[] data1, byte[] data2) {
        // make data2 the largest...
        if (data1.length > data2.length) {
            byte[] tmp = data2;
            data2 = data1;
            data1 = tmp;
        }
        for (int i = 0; i < data1.length; i++) {
            data2[i] ^= data1[i];
        }
        return data2;
    } // eof xor

    public static String ByteArraytoHexString(byte[] ba) {
        StringBuilder str = new StringBuilder();
        for(int i = 0; i < ba.length; i++) {
            str.append(String.format("%x", ba[i]));
        }
        return str.toString();
    } // eof toHexString
    public static String byteArrayToHexString(final byte[] byteArray, int startPos, int length) throws Exception {
        if (byteArray == null) {
            return "";
        }
        if(byteArray.length < startPos+length){
            throw new Exception("startPos("+startPos+")+length("+length+") > byteArray.length("+byteArray.length+")");
        }
        StringBuilder hexData = new StringBuilder();
        int onebyte;
        for (int i = 0; i < length; i++) {
            onebyte = ((0x000000ff & byteArray[startPos+i]) | 0xffffff00);
            hexData.append(Integer.toHexString(onebyte).substring(6));
        }
        return hexData.toString();
    }
    public static String stringToHex(String string) {
        StringBuilder buf = new StringBuilder(200);
        for (char ch: string.toCharArray()) {
            buf.append(String.format("%x", (int) ch));
        }
        return buf.toString();
    } // eof stringToHex

    public static String amountToHex(String amount) {
	    double amm = Double.parseDouble(amount.replaceAll(",","."));
        long t  =    (long) (amm * 100);
        Log.i(TAG," int "+t);
        String inhex = Long.toHexString(t);
        String hex = adaptAmount(inhex);
        //int inhexint = Integer.parseInt(inhex, 16);

        //double d = (double)inhexint /100;
        //return  String.valueOf(d);
        Log.i(TAG," hex "+hex);

        return hex ;
    } // eof stringToHex

    public static String hextoString(String inhex){

        Long inhexint = Long.parseLong(inhex, 16);
        Log.i(TAG," Last inhexint --->  "+inhexint);


        double d = (double)inhexint /100;
        return  String.valueOf(d);


    }

    public static double hextoDouble(String inhex){

        Long inhexint = Long.parseLong(inhex, 16);
        Log.i(TAG," Last inhexint --->  "+inhexint);


        double d = (double)inhexint /100;
        return  d;


    }

    public static int hextoInteger(String inhex){

        int inhexint = Integer.parseInt(inhex, 16);
        Log.i(TAG," Last inhexint --->  "+inhexint);

        return  inhexint;


    }

    public static String encodeHexString(String sourceText) {

        byte[] rawData = sourceText.getBytes();

        StringBuffer hexText = new StringBuffer();
        String initialHex = null;
        int initHexLength = 0;

        for (int i = 0; i < rawData.length; i++) {
            int positiveValue = rawData[i] & 0x000000FF;
            initialHex = Integer.toHexString(positiveValue);
            initHexLength = initialHex.length();
            while (initHexLength++ < 2) {
                hexText.append("0");
            }
            hexText.append(initialHex);
        }
        return hexText.toString();
    }

    public static String matriculepad(String matricule ){

        int length = matricule.length();
        //   String pad ="0000000000000";

        String padspaces ="             ";
        if (length<12){

            return    matricule = padspaces.substring(0, 12-length)+matricule;

        }else  return matricule;


    }

    public static String kmValeurpad(String kmValeurn ){

        int length = kmValeurn.length();
        String padspaces ="0000000000000";

        if (length<6){

            return    kmValeurn = padspaces.substring(0, 6-length)+kmValeurn;

        }else  return kmValeurn;


    }


    public static String convertHexToString(String hex){
        String ascii = "";
        String str;

        // Convert hex string to "even" length
        int rmd, length;
        length = hex.length();
        rmd = length % 2;

        if(rmd==1) {
            hex = "0" + hex;
        }

        // split into two characters
        if(!hex.equals("FFFFFFFFFFFFFFFFFFFFFFFF")){
            for( int i = 0; i < hex.length() - 1; i += 2) {
                //split the hex into pairs
                String pair = hex.substring(i, (i + 2));
                //convert hex to decimal
                int dec = Integer.parseInt(pair, 16);
                str = checkCode(dec);
                //ascii = ascii + " " + str;
                ascii = ascii + "" + str;
            }
        }
        return ascii;
    } // eof convertHexToString

    public static String checkCode(int dec) {
        String str;

        //convert the decimal to character
        str = Character.toString((char) dec);

        if(dec < 32 || dec > 126 && dec < 161) {
            str = "n/a";
        }
        return str;
    } // eof CheckCode

    public static byte[] xor1(byte[] a, byte[] b) {
        byte[] result = null;
        if (a.length > b.length) {
            for (int i = 0; i < b.length; i++) {
                result = new byte[b.length];
                result[i] = (byte) (((int) a[i]) ^ ((int) b[i]));
            }
        } else {
            for (int i = 0; i < a.length; i++) {
                result = new byte[a.length];
                result[i] = (byte) (((int) a[i]) ^ ((int) b[i]));
            }
        }
        return result;
    } // eof xor1

    public static final byte[] xor2(byte[] abyte0, byte[] abyte1) {
        byte[] abyte2 = new byte[abyte0.length];

        for(int i = 0; i < abyte0.length; ++i) {
            abyte2[i] = (byte)(abyte0[i] ^ abyte1[i]);
        }

        return abyte2;
    }
    
    public static int hex2decimal(String s) {
        String digits = "0123456789ABCDEF";
        s = s.toUpperCase();
        int val = 0;
        for (int i = 0; i < s.length(); i++) {
            char c = s.charAt(i);
            int d = digits.indexOf(c);
            val = 16*val + d;
        }
        return val;
    } // eof hex2decimal

    public static String decToHex(int dec) {
        StringBuilder hexBuilder = new StringBuilder(sizeOfIntInHalfBytes);
        hexBuilder.setLength(sizeOfIntInHalfBytes);
        for (int i = sizeOfIntInHalfBytes - 1; i >= 0; --i) {
            int j = dec & halfByte;
            hexBuilder.setCharAt(i, hexDigits[j]);
            dec >>= numberOfBitsInAHalfByte;
        }
        return hexBuilder.toString(); 
    } // eof decToHex

    public static String adaptAmount(String amount) {
    	String st_Amount = "" + amount;
    	
    	while(st_Amount.length() < 8) {
            st_Amount = "0" + st_Amount;
        }
    		
    	return st_Amount;
    } // eof adaptAmount

    public static byte[] encipher(byte[] abyte0, byte[] abyte1, String s, String s1) {
        byte[] abyte2 = new byte[1];
         byte[] zeroVector = new byte[8];

        try {
            byte[] exception = null;
            if(s1.compareTo("DES") == 0) {
                exception = new byte[8];
                System.arraycopy(abyte1, 0, exception, 0, 8);
            } else if(s1.compareTo("DESede") == 0) {
                exception = new byte[24];
                System.arraycopy(abyte1, 0, exception, 0, 16);
                System.arraycopy(abyte1, 0, exception, 16, 8);
            }

            SecretKeySpec secretkeyspec = new SecretKeySpec(exception, s1);
            String s2 = s1 + "/" + s + "/NoPadding";
            Cipher cipher = Cipher.getInstance(s2);
            if(s.equalsIgnoreCase("CBC")) {
                cipher.init(1, secretkeyspec, new IvParameterSpec(zeroVector));
            } else if(s.equalsIgnoreCase("ECB")) {
                cipher.init(1, secretkeyspec);
            }

            abyte2 = cipher.doFinal(abyte0);
        } catch (Exception var9) {
            var9.printStackTrace();
        }

        return abyte2;
    }

    public static byte[] hexString2byteArray(String s) {
        ByteArrayOutputStream bytearrayoutputstream = new ByteArrayOutputStream();
        Pattern pattern = Pattern.compile("[^\\s\\da-fA-F]");
        Matcher matcher = pattern.matcher(s);
        if(matcher.find()) {
            return null;
        } else {
            s = s.replaceAll("\\s+", "");
            if(s.length() % 2 != 0) {
                return null;
            } else {
                for(int i = 0; i < s.length(); i += 2) {
                    bytearrayoutputstream.write(Integer.parseInt(s.substring(i, i + 2), 16));
                }

                return bytearrayoutputstream.toByteArray();
            }
        }
    }

    public static String byteArray2hexString(byte[] abyte0) {
        return byteArray2hexString(abyte0, 0, abyte0.length);
    }

    public static String byteArray2hexString(byte[] abyte0, int i) {
        return byteArray2hexString(abyte0, i, abyte0.length - i);
    }

    public static String byteArray2hexString(byte[] abyte0, int i, int j) {
        StringBuffer stringbuffer = new StringBuffer();

        for(int k = i; k < i + j; ++k) {
            String s = Integer.toHexString(abyte0[k] & 255).toUpperCase();
            if(s.length() == 1) {
                stringbuffer.append("0" + s);
            } else {
                stringbuffer.append(s);
            }
        }

        return stringbuffer.toString();
    }

    public static Double hexToInt(String hex) {
        Long i = Long.parseLong("BF800000", 16);

        //short value = Short.parseShort(hex, 16);
        double valueAsDouble = i;

        Log.i(TAG," valueAsDouble Last res 1  "+valueAsDouble);



        long longHex = parseUnsignedHex("c0399999a0000000");

        double k = Double.longBitsToDouble(longHex);
        Log.i(TAG," kkk Last res 1  "+k);


        Log.i(TAG," iii Last res 1  "+ i);
        double d = (double)i /100;

        Float f = Float.intBitsToFloat(i.intValue());
        Log.i(TAG," fff Last res 1  "+ f);

        Log.i(TAG,Integer.toHexString(Float.floatToIntBits(f)));
        return d ;
    }

    public static long parseUnsignedHex(String text) {
        if (text.length() == 16) {
            return (parseUnsignedHex(text.substring(0, 1)) << 60)
                    | parseUnsignedHex(text.substring(1));
        }
        return Long.parseLong(text, 16);
    }

    public static byte[] toBcd(String s) {
        int size = s.length();
        byte[] bytes = new byte[(size+1)/2];
        int index = 0;
        boolean advance = size%2 != 0;
        for ( char c : s.toCharArray()) {
            byte b = (byte)( c - '0');
            if( advance ) {
                bytes[index++] |= b;
            }
            else {
                bytes[index] |= (byte)(b<<4);
            }
            advance = !advance;
        }
        return bytes;
    }


    public static int getMonthOfYear(Date date) {
        Calendar calendar = Calendar.getInstance ();
        calendar.setTime(date);
        return calendar.get (Calendar.MONTH);
    }

    public static int getDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get (Calendar.DAY_OF_MONTH);
    }

    public static int getYearDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }


    public static String unHex(String arg) {

        String str = "";
        for(int i=0;i<arg.length();i+=2)
        {
            String s = arg.substring(i, (i + 2));
            int decimal = Integer.parseInt(s, 16);
            str = str + (char) decimal;
        }
        return str;
    }



    public static void saveHashMap(Context ctx , String key , Object obj) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(ctx);
        SharedPreferences.Editor editor = prefs.edit();
        Gson gson = new Gson();
        String json = gson.toJson(obj);
        editor.putString(key,json);
        editor.apply();     // This line is IMPORTANT !!!
    }


    public static HashMap<Integer,String> getHashMapString(Context ctx , String key) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(ctx);
        Gson gson = new Gson();
        String json = prefs.getString(key,"");
        java.lang.reflect.Type type = new TypeToken<HashMap<Integer,String>>(){}.getType();
        HashMap<Integer,String> obj = gson.fromJson(json, type);
        return obj;
    }


    public static boolean checkEmpty(String text){

        return TextUtils.isEmpty(text);

    }

    public static File getSmartFilesDir(Context context, boolean ignoreSDRemovable) {
        File fileResult=context.getFilesDir();
        if (Environment.MEDIA_MOUNTED.equals(Environment.getExternalStorageState())
                &&(ignoreSDRemovable || Environment.isExternalStorageRemovable())){
            File fileTemp= context.getExternalFilesDir(null);
            if(fileTemp!=null)
                fileResult=fileTemp;//导致即使不为Null的可能
        }
        return fileResult;
    }


    public static String getExternalMounts() {
        final HashSet<String> out = new HashSet<String>();
        String reg = "(?i).*vold.*(vfat|ntfs|exfat|fat32|ext3|ext4).*rw.*";
        String s = "";
        try {
            final Process process = new ProcessBuilder().command("mount")
                    .redirectErrorStream(true).start();
            process.waitFor();
            final InputStream is = process.getInputStream();
            final byte[] buffer = new byte[1024];
            while (is.read(buffer) != -1) {
                s = s + new String(buffer);
            }
            is.close();
        } catch (final Exception e) {
            e.printStackTrace();
        }

        // parse output
        final String[] lines = s.split("\n");
        for (String line : lines) {
            if (!line.toLowerCase(Locale.US).contains("asec")) {
                if (line.matches(reg)) {
                    String[] parts = line.split(" ");
                    for (String part : parts) {
                        if (part.startsWith("/"))
                            if (!part.toLowerCase(Locale.US).contains("vold"))
                                return part ;
                    }
                }
            }
        }
        return "";
    }

    public static String getWifiIpAddress(Context context){
        String ip = "127.0.0.1";
        try{
            WifiManager wm = (WifiManager) context.getSystemService(Context.WIFI_SERVICE);
            ip = Formatter.formatIpAddress(wm.getConnectionInfo().getIpAddress());
        } catch (Exception e){
           ip = "";
        }
        return ip;
    }

    public static void createFile(Activity ctx, String mimeType, String fileName) {
        Intent intent = new Intent(Intent.ACTION_CREATE_DOCUMENT);

        // Filter to only show results that can be "opened", such as
        // a file (as opposed to a list of contacts or timezones).
        intent.addCategory(Intent.CATEGORY_OPENABLE);

        // Create a file with the requested MIME type.
        intent.setType(mimeType);
        intent.putExtra(Intent.EXTRA_TITLE, fileName);
        ctx.startActivityForResult(intent, WRITE_REQUEST_CODE);
    }

    //write data to file
    public static void writeData(Context ctx, String filePath,String data) {

        //String path = getExternalMounts() ;
        File tlcFile;

        try {
            File[] files = ctx.getExternalFilesDirs(null) ;
             //createFile(((Activity)ctx),"text/plain","log_transaction_files.txt");
            if(files != null && files.length > 0 ) {
                tlcFile = new File(files[0], "transactions");

                if (!tlcFile.exists()) {

                    tlcFile.mkdir();

                }

                File logFile = new File(tlcFile.getPath()+ "/log_transaction_files.txt");


                if (!logFile.exists()) {
                    try {
                        logFile.createNewFile();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }

                // BufferedWriter for performance, true to set append to file flag
                BufferedWriter buf = new BufferedWriter(new FileWriter(logFile, true));
                buf.append(data);
                buf.newLine();
                buf.close();

            }
        } catch (IOException e) {
            e.printStackTrace();
        }



      /*  try {
            FileOutputStream fileOutputStream = new FileOutputStream(filePath);
            fileOutputStream.write(data.getBytes());
            fileOutputStream.close();
        }catch (FileNotFoundException e) {
            e.printStackTrace();
        }catch (IOException e) {
            e.printStackTrace();
        }*/
    }

    //read data from the file
    public static void readData(String filePath) {

        StringBuilder stringBuilder = new StringBuilder();
        try {
            FileInputStream fileInputStream = new FileInputStream(filePath);
            InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream);
            BufferedReader reader = new BufferedReader(inputStreamReader);

            String temp;
            while ((temp = reader.readLine()) != null) {
                stringBuilder.append(temp);
            }
        }catch (FileNotFoundException e) {
            e.printStackTrace();
        }catch (IOException e) {
            e.printStackTrace();
        }

    }

    //checks if external storage is available for read and write
    public static boolean isExternalStorageAvailable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }

    //checks if external storage is available for read
    public static boolean isExternalStorageReadable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED_READ_ONLY.equals(state);
    }

    public static String getStorageDir(String fileName) {
        //create folder
        File file = new File(Environment.getExternalStorageDirectory(), "petrolCard");
        if (!file.mkdirs()) {
            file.mkdirs();
        }
        String filePath = file.getAbsolutePath() + "/" /*File.separator*/ + fileName;
        return filePath;
    }

    public static void goBackDialog(Context context)
    {
        try{
            new MyMaterialDialog(
                    context,
                    "Confirm",
                    "Are you sure, you want to go back?",
                    "Yes",
                    "No",
                    new MyMaterialDialogListener() {
                        @Override
                        public void onPositiveClick(@NonNull MaterialDialog dialog) {
                            dialog.dismiss();
                            ((Activity)context).finish();
                        }

                        @Override
                        public void onNegativeClick(@NonNull MaterialDialog dialog) {
                            dialog.dismiss();
                        }
                    }
            );
        } catch (Exception e){
            e.printStackTrace();
        }
    }


    //ESD Printer
    public static byte CalcChecksum(byte[] packet)
    {
        int sum = 0;
        int  checklength = packet.length;

        while((checklength--) != 0){
            sum += (packet[checklength]);
        }
        return( (byte)(( sum % 256 )% 100));
    }

    public static String parseLongString(String s){
        String result = "0";
        try{
            if(s.contains(".")){
                result = s.substring(0, s.indexOf("."));
            }
            else
            {
                result = s;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }
    public static void log(String tag,String msg)
    {
        Log.i(tag,msg);
    }


} // eof Utils


