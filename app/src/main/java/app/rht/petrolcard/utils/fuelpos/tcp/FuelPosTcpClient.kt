package app.rht.petrolcard.utils.fuelpos.tcp

import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.util.Log
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.utils.LogWriter
import extension.log
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.InetSocketAddress
import java.net.Socket
import java.net.SocketException
import java.net.UnknownHostException


enum class ConnectionError {
    TIMEOUT, CONNECTION_DENIED, UNKNOWN_HOST, INVALID_CONNECTION, DATA_SEND_FAILED
}

enum class ConnectionState {
    CONNECTING, CONNECTED, DISCONNECTED
}
class FuelPosTcpClient {

    lateinit var ip: String
    private var port: Int
    private lateinit var clientListener: FuelPosClientListener
    private var heartbeatEnabled: Boolean = true
    private var heartbeatTime : Int = 180
    private lateinit var  heartbeat: CountDownTimer

    val logWriter = LogWriter("FuelPosTcpClient")

    constructor(
        ip: String,
        port: Int,
        clientListener: FuelPosClientListener,
        heartbeatEnabled: Boolean,
        heartbeatTime: Int = 180,
    ) {
        this.ip = ip
        this.port = port
        this.clientListener = clientListener
        this.heartbeatEnabled = heartbeatEnabled
        this.heartbeatTime = heartbeatTime

        initHeartbeat()
        logWriter.appendLog(TAG,"IP:${this.ip} PORT:${this.port} LOGON_TIME:${this.heartbeatTime}")
    }

    constructor(
        ip: String,
        port: Int,
        clientListener: FuelPosClientListener
    ) {
        this.ip = ip
        this.port = port
        this.clientListener = clientListener

        initHeartbeat()
    }

    private fun initHeartbeat(){
        heartbeat = object : CountDownTimer(heartbeatTime*1000L, 1000) {
            override fun onTick(millisUntilFinished: Long) {
               if(BuildConfig.DEBUG)
                   Log.e(TAG,"Heartbeat time = ${heartbeatTime*1000} ---- $millisUntilFinished")
            }

            override fun onFinish() {
                if(BuildConfig.DEBUG)
                    Log.e(TAG,"On Heartbeat")
                clientListener.onHeartbeat()
                this.start()
            }
        }
    }

    private lateinit var socket: Socket
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null

    private lateinit var connectThread: ConnectThread
    fun connect() {
        //val thread = ConnectThread(ip, port)
        //thread.start()
        if (::connectThread.isInitialized && connectThread.isAlive) {
            disconnect()
            connectThread.interrupt()
            connectThread = ConnectThread(ip, port)
            connectThread.start()
        } else {
            connectThread = ConnectThread(ip, port)
            connectThread.start()
        }
    }

    fun disconnect() {
        if(heartbeatEnabled) {
            heartbeat.cancel()
        }
        stopConnectionChecker()
        if(::socket.isInitialized){
            if (socket.isConnected) {
                try {
                    if (inputStream != null) inputStream!!.close()
                    if (outputStream != null) outputStream!!.close()
                    socket.close()
                    Handler(Looper.getMainLooper()).post {
                        clientListener.onConnectionStateChanged(ConnectionState.DISCONNECTED)
                    }
                } catch (e: IOException) {
                    e.printStackTrace()
                    Handler(Looper.getMainLooper()).post {
                        clientListener.onError(ConnectionError.INVALID_CONNECTION)
                    }
                } catch (e : SocketException){
                    e.printStackTrace()
                    Handler(Looper.getMainLooper()).post {
                        clientListener.onError(ConnectionError.INVALID_CONNECTION)
                    }
                }
            }
        }
    }

    fun send(data: String) {
        logWriter.appendLog(TAG,"Sending: $data")
        sendData(data.toByteArray())
    }

    fun send(data: ByteArray) {
        sendData(data)
    }

    private fun sendData(data: ByteArray) {
        val thread = Thread {
            try {
                if(!::socket.isInitialized)
                    connect()

                outputStream = socket.getOutputStream()
                outputStream!!.write(data)
                clientListener.onDataSend()
                Handler(Looper.getMainLooper()).post { clientListener.onDataSend() }
            } catch (e: IOException) {
                e.printStackTrace()
                logWriter.appendLog(TAG, "Error sending data :${e.message}")
                Handler(Looper.getMainLooper()).post {
                    clientListener.onError(ConnectionError.DATA_SEND_FAILED)
                    disconnect()
                }
            } catch (e: SocketException) {
                logWriter.appendLog(TAG, "Error sending data :${e.message}")
                Handler(Looper.getMainLooper()).post {
                    clientListener.onError(ConnectionError.DATA_SEND_FAILED)
                    disconnect()
                }
            }
            catch (e: UninitializedPropertyAccessException){
                logWriter.appendLog(TAG, "Error sending data :${e.message}")
                Handler(Looper.getMainLooper()).post {
                    clientListener.onError(ConnectionError.DATA_SEND_FAILED)
                }
            }
        }
        thread.start()
        /*val thread = Thread {
            try {
                if(!::socket.isInitialized)
                    connect()

                outputStream = socket.getOutputStream()
                outputStream!!.write(data)
                Handler(Looper.getMainLooper()).post { clientListener.onDataSend() }
            } catch (e: IOException) {
                e.printStackTrace()
                Log.d(TAG, "Error sending data")
                Handler(Looper.getMainLooper()).post {
                    clientListener.onError(ConnectionError.DATA_SEND_FAILED)
                }
            } catch (e: SocketException){
                disconnect()
            } catch (e: UninitializedPropertyAccessException){
                Handler(Looper.getMainLooper()).post {
                    clientListener.onError(ConnectionError.DATA_SEND_FAILED)
                }
            }
        }
        thread.start()*/
    }

    val isConnected: Boolean
        //get() = socket.isConnected
        get() = connectionState

    interface FuelPosClientListener {
        fun onHeartbeat()
        fun onConnectionStateChanged(state: ConnectionState)
        fun onError(error: ConnectionError)
        fun onDataSend()
        fun onDataReceived(message: String, bytes: ByteArray)
    }

    internal fun ByteArray.toHexString() : String {
        return this.joinToString("") {
            java.lang.String.format("%02x", it)
        }
    }

    internal inner class ConnectThread(var ip: String, var port: Int) : Thread() {
        override fun run() {
            try { //create client socket

                //socket = new Socket(ip, port);
                socket = Socket()
                logWriter.appendLog(TAG, "Socket creation and connection.")
                logWriter.appendLog(TAG, "Connecting $ip $port")
                Handler(Looper.getMainLooper()).post {
                    clientListener.onConnectionStateChanged(ConnectionState.CONNECTING)
                    startConnectingTimer()
                }

                socket.connect(InetSocketAddress(ip, port), connectionTimeout * 1000)

                val addr = socket.inetAddress
                val tmp = addr.hostAddress
                logWriter.appendLog(TAG, "Connected $tmp")

                Handler(Looper.getMainLooper()).post {
                    stopConnectingTimer()
                    clientListener.onConnectionStateChanged(ConnectionState.CONNECTED)
                    connectionState = true
                    if(heartbeatEnabled)
                    {
                        logWriter.appendLog(TAG,"HeartbeatTimer started")
                        heartbeat.start()
                    }
                }

                startConnectionChecker()

                var buffer :ByteArray
                try {
                    logWriter.appendLog(TAG, "Ready to receive data")
                    while (true) {
                        Log.e(TAG, "Receiving data")
                        buffer = ByteArray(4096) //4MB
                        inputStream = socket.getInputStream()
                        val dataLength = inputStream!!.read(buffer)
                        if (dataLength != -1) {
                            val data = ByteArray(dataLength)
                            for (i in 0 until dataLength) data[i] = buffer[i]
                            //Log.d(TAG, "total byte received = $dataLength")
                            val received = data.toHexString()
                            //Log.i(TAG, "RECEIVED: $received")
                            val strData = String(data)
                            logWriter.appendLog(TAG, "Received: $strData")
                            Handler(Looper.getMainLooper()).post {
                                clientListener.onDataReceived(strData, data)
                            }
                        } else {
                            //disconnect()
                            break
                        }

                        reachable = socket.isConnected
                    }
                } catch (e: IOException) {
                    logWriter.appendLog(TAG, "socket channel disconnection detected: ${e.message}")
                    //disconnect()
                    /*if(heartbeatEnabled)
                    {
                        heartbeat.cancel()
                    }
                    Handler(Looper.getMainLooper()).post {
                        clientListener.onConnectionStateChanged(ConnectionState.DISCONNECTED)
                    }*/
                }
            } catch (uhe: UnknownHostException) { // Unable to identify the IP of the host (www.unknown-host.com) passed when creating the socket.
                logWriter.appendLog(TAG, "Generation Error: Unable to identify host's IP address.[$ip] (Invalid address value or hostname used)")
                Handler(Looper.getMainLooper()).post { clientListener.onError(ConnectionError.UNKNOWN_HOST) }
            } catch (ioe: IOException) { //An I/O error occurred during socket creation.
                logWriter.appendLog(TAG, " Creation Error: ${ioe.message}")
                Handler(Looper.getMainLooper()).post { clientListener.onError(ConnectionError.TIMEOUT) }
            } catch (se: SecurityException) { // Perform a function not allowed by the security manager.
                logWriter.appendLog(TAG, "Generation Error: Occurs by the Security Manager for a security violation (Proxy connection denied, not allowed function call)")
                clientListener.onError(ConnectionError.CONNECTION_DENIED)
            } catch (ne: NullPointerException) { // Perform a function not allowed by the security manager.
                logWriter.appendLog(TAG, "Generation Error: Attempt to invoke virtual method 'java.lang.String java.net.InetAddress.getHostAddress()' on a null object reference call)")
                clientListener.onError(ConnectionError.UNKNOWN_HOST)
                ne.printStackTrace()
            } catch (le: IllegalArgumentException) { // The port number (65536) passed when creating a socket is out of the allowable range (0~65535).
                logWriter.appendLog(
                    TAG,
                    "Generation Error: Occurs when an invalid parameter is passed to the method. (Use a port number outside the range of 0-65535, pass null proxy)"
                )
                Handler(Looper.getMainLooper()).post {
                    clientListener.onError(
                        ConnectionError.INVALID_CONNECTION
                    )
                }
            }
        }

        private fun startConnectingTimer(){
            stopConnectingTimer()
            lastConnectionStatus = 1
            connectingTimer.start()
        }
        private fun stopConnectingTimer() {
            try {
                lastConnectionStatus = 0
                connectingTimer.cancel()
            } catch (e:Exception) { e.printStackTrace() }
        }
        private var lastConnectionStatus = 0
        private var connectingTimer = object : CountDownTimer(connectionTimeout*1000L,1000){
            override fun onTick(milliseconds: Long) {
                val minutes = milliseconds / 1000 / 60
                val seconds = milliseconds / 1000 % 60
                logWriter.appendLog(TAG, "FUELPOS CONNECTING TIMER: $minutes:$seconds")
            }

            override fun onFinish() {                   //
                if( lastConnectionStatus != 0){
                    Handler(Looper.getMainLooper()).post { clientListener.onError(ConnectionError.TIMEOUT) }
                } else {
                    logWriter.appendLog(TAG,"FUELPOS CONNECTING STATUS: $lastConnectionStatus")
                }
            }

        }
    }

    companion object {
        private val TAG = FuelPosTcpClient::class.java.simpleName
        var connectionTimeout = 10
    }

    private fun startConnectionChecker() {
        connectionChecker.start()
    }

    private fun stopConnectionChecker() {
        connectionChecker.cancel()
    }

    var connectionState = false

    private var connectionChecker = object : CountDownTimer(3000, 1000) {
        override fun onTick(p0: Long) {}
        override fun onFinish() {
            try {
                if (::ip.isInitialized && !ping(ip)) {
                    connectionState = false
                    disconnect()
                } else {
                    this.start()
                    connectionState = true
                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
                this.start()
                connectionState = true
            }

        }
    }

    fun ping(address: String): Boolean {
        val runtime = Runtime.getRuntime()
        try {
            val mIpAddrProcess = runtime.exec("/system/bin/ping -c 1 $address")
            val mExitValue = mIpAddrProcess.waitFor()
            //println("$ip connection state ${mExitValue == 0}")
            return mExitValue == 0
        } catch (ignore: InterruptedException) {
            ignore.printStackTrace()
            println(" Exception:$ignore")
        } catch (e: IOException) {
            e.printStackTrace()
            println(" Exception:$e")
        } catch (e : Exception){
            e.printStackTrace()
            println(" Exception:$e")
        }
        return false
    }

    var reachable = false

}
