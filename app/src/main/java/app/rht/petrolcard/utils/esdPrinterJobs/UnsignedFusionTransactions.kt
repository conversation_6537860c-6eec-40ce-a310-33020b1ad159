package app.rht.petrolcard.utils.esdPrinterJobs
import com.google.gson.annotations.SerializedName


import androidx.annotation.Keep
@Keep
data class UnsignedFusionTransactions(
    @SerializedName("contenu")
    val transactions: List<UnsignedTransaction>,
    @SerializedName("error")
    val error: Any,
    @SerializedName("reponse")
    val reponse: String
)

@Keep
data class UnsignedTransaction(
    @SerializedName("amount")
    val amount: String,
    @SerializedName("dh_transaction")
    val dhTransaction: String,
    @SerializedName("hose")
    val hose: String,
    @SerializedName("pompiste")
    val pompiste: String,
    @SerializedName("produit")
    val produit: String,
    @SerializedName("pu")
    val pu: String,
    @SerializedName("pump")
    val pump: String,
    @SerializedName("quantite")
    val quantite: String,
    @SerializedName("ref_transaction")
    val refTransaction: String,
    @SerializedName("rfid")
    val rfid: String,
    @SerializedName("trxseqnr")
    var trxSeqNr: String?,
    @SerializedName("produit_id")
    var produitId: String?,
)