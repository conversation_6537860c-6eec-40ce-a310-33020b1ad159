package app.rht.petrolcard.utils.paxutils

import android.annotation.SuppressLint
import android.app.Activity
import android.app.NotificationManager
import android.content.*
import android.database.Cursor
import android.graphics.*
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.Drawable
import android.media.ExifInterface
import android.media.MediaMetadataRetriever
import android.media.ThumbnailUtils
import android.net.ConnectivityManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.provider.Settings
import android.text.InputFilter
import android.text.SpannableString
import android.text.Spanned
import android.text.TextUtils
import android.util.DisplayMetrics
import android.util.Log
import android.view.WindowManager
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.appcompat.app.AlertDialog
import androidx.core.content.FileProvider
import androidx.recyclerview.widget.LinearLayoutManager
import app.rht.petrolcard.R
import app.rht.petrolcard.utils.Utils.getTimestamp
import app.rht.petrolcard.utils.constant.AppConstant
import okhttp3.ResponseBody
import org.json.JSONArray
import java.io.*
import java.lang.Exception
import java.net.URISyntaxException
import java.text.DecimalFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import kotlin.math.log10
import kotlin.math.pow

class FilePathUtils {
    companion object {

        /* Check internet connectivity */
        fun isNetworkAvailableWithoutMessage(context: Context): Boolean {
            val connectivityManager =
                context.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val activeNetworkInfo = connectivityManager.activeNetworkInfo
            return activeNetworkInfo != null && activeNetworkInfo.isConnected
        }

        /*Method is use to disable space in edit text*/
        fun getEditTextFilterSpace(): InputFilter {
            return InputFilter { source, start, end, dest, dstart, dend ->
                for (i in start until end) {
                    if (Character.isWhitespace(source[i])) {
                        return@InputFilter ""
                    }
                }
                null
            }
        }

        /*Method is use to disable emoji in edit text*/
        fun getEditTextFilterEmoji(): InputFilter {
            return object : InputFilter {
                override fun filter(
                    source: CharSequence,
                    start: Int,
                    end: Int,
                    dest: Spanned,
                    dstart: Int,
                    dend: Int
                ): CharSequence? {
                    var source = source
                    var end = end
                    val sourceOriginal = source
                    source = replaceEmoji(source)
                    end = source.toString().length

                    if (end == 0)
                        return "" //Return empty string if the input character is already removed

                    if (sourceOriginal.toString() != source.toString()) {
                        val v = CharArray(end - start)
                        TextUtils.getChars(source, start, end, v, 0)

                        val s = String(v)

                        if (source is Spanned) {
                            val sp = SpannableString(s)
                            TextUtils.copySpansFrom(source as Spanned, start, end, null, sp, 0)
                            return sp
                        } else {
                            return s
                        }
                    } else {
                        return null // keep original
                    }
                }

                private fun replaceEmoji(source: CharSequence): String {

                    val notAllowedCharactersRegex =
                        "[^a-zA-Z0-9@#\\$%\\&\\-\\+\\(\\)\\*;:!\\?\\~`£\\{\\}\\[\\]=\\.,_/\\\\\\s'\\\"<>\\^\\|÷×]"
                    return source.toString()
                        .replace(notAllowedCharactersRegex.toRegex(), "")
                }

            }
        }

        fun getPropertySearchFilter(): InputFilter {
            return object : InputFilter {
                override fun filter(
                    source: CharSequence,
                    start: Int,
                    end: Int,
                    dest: Spanned,
                    dstart: Int,
                    dend: Int
                ): CharSequence? {
                    var source = source
                    var end = end
                    val sourceOriginal = source
                    source = replaceEmoji(source)
                    end = source.toString().length

                    if (end == 0)
                        return "" //Return empty string if the input character is already removed

                    if (sourceOriginal.toString() != source.toString()) {
                        val v = CharArray(end - start)
                        TextUtils.getChars(source, start, end, v, 0)

                        val s = String(v)

                        if (source is Spanned) {
                            val sp = SpannableString(s)
                            TextUtils.copySpansFrom(source as Spanned, start, end, null, sp, 0)
                            return sp
                        } else {
                            return s
                        }
                    } else {
                        return null // keep original
                    }
                }

                private fun replaceEmoji(source: CharSequence): String {

                    val notAllowedCharactersRegex =
                        "[^a-zA-Z0-9@#\\$%\\&\\-\\+\\(\\)\\*;:!\\?\\~`£\\{\\}\\[\\]=\\.,_/\\\\\\s'\\\"<>\\^\\|÷×" +
                                "\\á\\ñ\\é\\í\\ó\\ú\\ü\\¿\\«\\Á\\É\\Í\\Ñ\\Ó\\Ú\\Ü\\¡\\»]"
                    return source.toString()
                        .replace(notAllowedCharactersRegex.toRegex(), "")
                }

            }
        }

        fun getUserNameFilter(): InputFilter {
            return object : InputFilter {
                override fun filter(
                    source: CharSequence,
                    start: Int,
                    end: Int,
                    dest: Spanned,
                    dstart: Int,
                    dend: Int
                ): CharSequence? {
                    var source = source
                    var end = end
                    val sourceOriginal = source
                    source = replaceEmoji(source)
                    end = source.toString().length

                    if (end == 0)
                        return "" //Return empty string if the input character is already removed

                    if (sourceOriginal.toString() != source.toString()) {
                        val v = CharArray(end - start)
                        TextUtils.getChars(source, start, end, v, 0)

                        val s = String(v)

                        if (source is Spanned) {
                            val sp = SpannableString(s)
                            TextUtils.copySpansFrom(source as Spanned, start, end, null, sp, 0)
                            return sp
                        } else {
                            return s
                        }
                    } else {
                        return null // keep original
                    }
                }

                private fun replaceEmoji(source: CharSequence): String {
                    val allowedCharactersRegex =
                        "[^a-zA-Z0-9\\ñ\\-\\_\\+\\<\\>\\|\\[\\]\\{\\}]"
                    return source.toString()
                        .replace(allowedCharactersRegex.toRegex(), "")
                }

            }
        }

        fun drawableToBitmap(drawable: Drawable): Bitmap? {
            var bitmap: Bitmap? = null

            if (drawable is BitmapDrawable) {
                if (drawable.bitmap != null) {
                    return drawable.bitmap
                }
            }

            if (drawable.intrinsicWidth <= 0 || drawable.intrinsicHeight <= 0) {
                bitmap = Bitmap.createBitmap(
                    1,
                    1,
                    Bitmap.Config.ARGB_8888
                ) // Single color bitmap will be created of 1x1 pixel
            } else {
                bitmap = Bitmap.createBitmap(
                    drawable.intrinsicWidth,
                    drawable.intrinsicHeight,
                    Bitmap.Config.ARGB_8888
                )
            }

            var canvas = Canvas(bitmap)
            drawable.setBounds(0, 0, canvas.width, canvas.height)
            drawable.draw(canvas)
            return bitmap
        }

        /*This method will retur Linear layout manager*/
        fun getLayoutManager(context: Context): LinearLayoutManager {
            return LinearLayoutManager(context)
        }

        /*This method will retur Linear layout manager*/
        fun getHorizontalLayoutManager(context: Context): LinearLayoutManager {
            return LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        }

        /*This method is use to hide or close keyboard*/
        fun hideKeyboard(activity: Activity?) {
            var view = activity!!.window.currentFocus
            var imm: InputMethodManager =
                activity.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            if (view != null) {
                imm.hideSoftInputFromWindow(view.windowToken, 0)
            } else {
                imm.hideSoftInputFromWindow(
                    if (null == activity.currentFocus) null else activity.currentFocus!!
                        .windowToken,
                    InputMethodManager.HIDE_NOT_ALWAYS
                )
            }
        }

        /*get Output Media FileUri*/
        fun getOutputMediaFileUri(): Uri? {
            var file: File? = getOutputMediaFile()
            if (file != null)
                return Uri.fromFile(file)
            else
                return null
        }

        /*get Output Media FileUri*/
        fun getOutputMediaFile(): File? {
            val timeStamp=getTimestamp()
            var mediaStorageDir: File = File(
                Environment.getExternalStoragePublicDirectory(
                    Environment.DIRECTORY_PICTURES
                ), AppConstant.IMAGE_DIRECTORY_NAME
            )
            if (!mediaStorageDir.exists()) {
                if (!mediaStorageDir.mkdirs()) {
                    return null
                }
            }
            var filename= "IMG" + timeStamp + ".jpg"
            var mediaFile: File =
                File(mediaStorageDir.path + File.separator + filename)
            return mediaFile
        }

        fun getRealPathFromURI_2(context: Context, contentURI: Uri): String {
            try {
                var result: String
                var data = arrayOf(MediaStore.Images.Media.DATA)
                val cursor = context.contentResolver.query(contentURI, data, null, null, null)

                if (cursor == null) {
                    result = contentURI.path!!
                } else {
                    cursor.moveToFirst()
                    var idx: Int = cursor.getColumnIndex(MediaStore.Images.Media.DATA)
                    result = cursor.getString(idx)
                    cursor.close()
                }
                return result
            } catch (e: Exception) {
                e.printStackTrace()
                return ""
            }
        }

        /*==============================Compress Image==================================================*/

        /*This method  is use to compress or reduce size of image*/
        fun compressImage(imageUri: String, context: Context): Bitmap {

            val filePath = getRealPathFromURI(imageUri, context)
            var scaledBitmap: Bitmap? = null

            val options = BitmapFactory.Options()

            //      by setting this field as true, the actual bitmap pixels are not loaded in the memory. Just the bounds are loaded. If
            //      you try the use the bitmap here, you will get null.
            options.inJustDecodeBounds = true
            var bmp = BitmapFactory.decodeFile(filePath, options)

            var actualHeight = options.outHeight
            var actualWidth = options.outWidth

            //      max Height and width values of the compressed image is taken as 816x612

            val maxHeight = 816.0f
            val maxWidth = 612.0f
            var imgRatio = (actualWidth / actualHeight).toFloat()
            val maxRatio = maxWidth / maxHeight

            //      width and height values are set maintaining the aspect ratio of the image

            if (actualHeight > maxHeight || actualWidth > maxWidth) {
                if (imgRatio < maxRatio) {
                    imgRatio = maxHeight / actualHeight
                    actualWidth = (imgRatio * actualWidth).toInt()
                    actualHeight = maxHeight.toInt()
                } else if (imgRatio > maxRatio) {
                    imgRatio = maxWidth / actualWidth
                    actualHeight = (imgRatio * actualHeight).toInt()
                    actualWidth = maxWidth.toInt()
                } else {
                    actualHeight = maxHeight.toInt()
                    actualWidth = maxWidth.toInt()

                }
            }

            //      setting inSampleSize value allows to load a scaled down version of the original image

            options.inSampleSize = calculateInSampleSize(options, actualWidth, actualHeight)

            //      inJustDecodeBounds set to false to load the actual bitmap
            options.inJustDecodeBounds = false

            //      this options allow android to claim the bitmap memory if it runs low on memory
            options.inPurgeable = true
            options.inInputShareable = true
            options.inTempStorage = ByteArray(16 * 1024)

            try {
                //          load the bitmap from its path
                bmp = BitmapFactory.decodeFile(filePath, options)
            } catch (exception: OutOfMemoryError) {
                exception.printStackTrace()

            }

            try {
                scaledBitmap =
                    Bitmap.createBitmap(actualWidth, actualHeight, Bitmap.Config.ARGB_8888)
            } catch (exception: OutOfMemoryError) {
                exception.printStackTrace()
            }

            val ratioX = actualWidth / options.outWidth.toFloat()
            val ratioY = actualHeight / options.outHeight.toFloat()
            val middleX = actualWidth / 2.0f
            val middleY = actualHeight / 2.0f

            val scaleMatrix = Matrix()
            scaleMatrix.setScale(ratioX, ratioY, middleX, middleY)

            val canvas = Canvas(scaledBitmap!!)
            canvas.matrix.set(scaleMatrix)
            canvas.drawBitmap(
                bmp,
                middleX - bmp.width / 2,
                middleY - bmp.height / 2,
                Paint(Paint.FILTER_BITMAP_FLAG)
            )

            //      check the rotation of the image and display it properly
            val exif: ExifInterface
            try {
                exif = ExifInterface(filePath)

                val orientation = exif.getAttributeInt(
                    ExifInterface.TAG_ORIENTATION, 0
                )
                Log.d("EXIF", "Exif: " + orientation)
                val matrix = Matrix()
                if (orientation == 6) {
                    matrix.postRotate(90f)
                    Log.d("EXIF", "Exif: " + orientation)
                } else if (orientation == 3) {
                    matrix.postRotate(180f)
                    Log.d("EXIF", "Exif: " + orientation)
                } else if (orientation == 8) {
                    matrix.postRotate(270f)
                    Log.d("EXIF", "Exif: " + orientation)
                }
                scaledBitmap = Bitmap.createBitmap(
                    scaledBitmap, 0, 0,
                    scaledBitmap.width, scaledBitmap.height, matrix,
                    true
                )
            } catch (e: IOException) {
                e.printStackTrace()
            }

            var out: FileOutputStream? = null
            val filename = getFilename()
            try {
                out = FileOutputStream(filename)

                //          write the compressed bitmap at the destination specified by filename.
                scaledBitmap!!.compress(Bitmap.CompressFormat.JPEG, 100, out)

            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }

            return scaledBitmap!!

        }

        private fun getRealPathFromURI(contentURI: String, context: Context): String {
            val contentUri = Uri.parse(contentURI)
            val cursor = context.contentResolver.query(contentUri, null, null, null, null)
            if (cursor == null) {
                return contentUri.path!!
            } else {
                cursor.moveToFirst()
                val index = cursor.getColumnIndex(MediaStore.Images.ImageColumns.DATA)
                return cursor.getString(index)
            }
        }

        fun calculateInSampleSize(
            options: BitmapFactory.Options,
            reqWidth: Int,
            reqHeight: Int
        ): Int {
            val height = options.outHeight
            val width = options.outWidth
            var inSampleSize = 1

            if (height > reqHeight || width > reqWidth) {
                val heightRatio = Math.round(height.toFloat() / reqHeight.toFloat())
                val widthRatio = Math.round(width.toFloat() / reqWidth.toFloat())
                inSampleSize = if (heightRatio < widthRatio) heightRatio else widthRatio
            }
            val totalPixels = (width * height).toFloat()
            val totalReqPixelsCap = (reqWidth * reqHeight * 2).toFloat()
            while (totalPixels / (inSampleSize * inSampleSize) > totalReqPixelsCap) {
                inSampleSize++
            }

            return inSampleSize
        }

        fun getFilename(): String {
            val file =
                File(Environment.getExternalStorageDirectory().path, AppConstant.IMAGE_DIRECTORY_NAME)
            if (!file.exists()) {
                file.mkdirs()
            }
            return file.absolutePath + "/" + System.currentTimeMillis() + ".jpg"
        }

        /*=============================================================================*/

        fun getUniqueId(): String {
            return UUID.randomUUID().toString().replace("-", "").uppercase(Locale.getDefault())
        }
        fun writeFile(fileName: String, data: String, context: Context): String? {
            val writer: Writer
            var filePath: String? = null
            val root = Environment.getExternalStorageDirectory()
            val outDir = File(root.absolutePath + File.separator + AppConstant.IMAGE_DIRECTORY_NAME)
            if (!outDir.isDirectory) {
                outDir.mkdir()
            }
            try {
                if (!outDir.isDirectory) {
                    throw IOException(
                        "Unable to create directory EZ_time_tracker. Maybe the SD card is mounted?"
                    )
                }
                val outputFile = File(outDir, fileName)
                writer = BufferedWriter(FileWriter(outputFile))
                writer.write(data)
                filePath = outputFile.absolutePath
                writer.close()
            } catch (e: IOException) {
                Log.w("eztt", e.message, e)
                Toast.makeText(
                    context, e.message + " Unable to write to external storage.",
                    Toast.LENGTH_LONG
                ).show()
            }

            return filePath
        }


        @Throws(IOException::class)
        fun getBytesFromInputStream(`is`: InputStream): ByteArray {
            ByteArrayOutputStream().use { os ->
                val buffer = ByteArray(0xFFFF)

                var len: Int = 0
                while (len != -1) {
                    os.write(buffer, 0, len)
                    len = `is`.read(buffer)
                }

                os.flush()

                return os.toByteArray()
            }
        }

        // Method to save an bitmap to a file
        fun bitmapToFile(bitmap: Bitmap, context: Context): File {
            // Get the context wrapper
            val wrapper = ContextWrapper(context)

            // Initialize a new file instance to save bitmap object
            var file = wrapper.getDir("Images", Context.MODE_PRIVATE)
            file = File(file, "${UUID.randomUUID()}.jpg")

            try {
                // Compress the bitmap and save in jpg format
                val stream: OutputStream = FileOutputStream(file)
                bitmap.compress(Bitmap.CompressFormat.PNG, 100, stream)
                stream.flush()
                stream.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }

            // Return the saved bitmap uri
            return file
        }

        fun getOppositeActivity(name: String): Class<*>? {
            return Class.forName(name)
        }


        /*========== Compress video start ============*/

        @SuppressLint("NewApi")
        @Throws(URISyntaxException::class)
        fun getFilePath(context: Context, uri: Uri): String? {
            var uri = uri
            var selection: String? = null
            var selectionArgs: Array<String>? = null
            // Uri is different in versions after KITKAT (Android 4.4), we need to
            if (Build.VERSION.SDK_INT >= 19 && DocumentsContract.isDocumentUri(
                    context.applicationContext,
                    uri
                )
            ) {
                if (isExternalStorageDocument(uri)) {
                    val docId = DocumentsContract.getDocumentId(uri)
                    val split =
                        docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    return Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                } else if (isDownloadsDocument(uri)) {
                    val id = DocumentsContract.getDocumentId(uri)
                    uri = ContentUris.withAppendedId(
                        Uri.parse("content://downloads/public_downloads"),
                        java.lang.Long.valueOf(id)
                    )
                } else if (isMediaDocument(uri)) {
                    val docId = DocumentsContract.getDocumentId(uri)
                    val split =
                        docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    val type = split[0]
                    if ("image" == type) {
                        uri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                    } else if ("video" == type) {
                        uri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                    } else if ("audio" == type) {
                        uri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                    }
                    selection = "_id=?"
                    selectionArgs = arrayOf(split[1])
                }
            }
            if ("content".equals(uri.scheme!!, ignoreCase = true)) {
                val projection = arrayOf(MediaStore.Images.Media.DATA)
                var cursor: Cursor? = null
                try {
                    cursor = context.contentResolver
                        .query(uri, projection, selection, selectionArgs, null)
                    val column_index = cursor!!.getColumnIndexOrThrow(MediaStore.Images.Media.DATA)
                    if (cursor.moveToFirst()) {
                        return cursor.getString(column_index)
                    }
                } catch (e: Exception) {
                }

            } else if ("file".equals(uri.scheme!!, ignoreCase = true)) {
                return uri.path
            }
            return null
        }

        fun isExternalStorageDocument(uri: Uri): Boolean {
            return "com.android.externalstorage.documents" == uri.authority
        }

        fun isDownloadsDocument(uri: Uri): Boolean {
            return "com.android.providers.downloads.documents" == uri.authority
        }

        fun isMediaDocument(uri: Uri): Boolean {
            return "com.android.providers.media.documents" == uri.authority
        }
        /*========== Compress video end ============*/

        //Make first character capital
        @JvmStatic
        fun capitalFirstCharName(fName: String): String {
            var name = ""
            if (fName.trim { it <= ' ' }.length > 1) {
                name = fName.trim { it <= ' ' }.substring(
                                0,
                                1
                            ).uppercase(Locale.getDefault()) + fName.trim { it <= ' ' }.substring(1)
            } else if (fName.trim { it <= ' ' }.length == 1) {
                name = fName.trim { it <= ' ' }.substring(0, 1).uppercase(Locale.getDefault())
            }
            return name
        }


        fun cancelNotification(context: Context, notificationId: Int) {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(notificationId)
        }

        fun cancelAllNotification(context: Context) {
            val notificationManager =
                context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.cancel(1)
        }


        fun loadFile(file: File, context: Context) {
            var intent: Intent = Intent(Intent.ACTION_VIEW)
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            var uri: Uri = FileProvider.getUriForFile(
                context,
                context.applicationContext.packageName + ".provider",
                file
            )


            if (file.toString().contains(".doc") || file.toString().contains(".docx")) {
                // Word document
                intent.setDataAndType(uri, "application/msword")
            } else if (file.toString().contains(".pdf")) {
                // PDF file
                intent.setDataAndType(uri, "application/pdf")
            } else if (file.toString().contains(".ppt") || file.toString().contains(".pptx")) {
                // Powerpoint file
                intent.setDataAndType(uri, "application/vnd.ms-powerpoint")
            } else if (file.toString().contains(".xls") || file.toString().contains(".xlsx")) {
                // Excel file
                intent.setDataAndType(uri, "application/vnd.ms-excel")
            } else if (file.toString().contains(".zip") || file.toString().contains(".rar")) {
                // WAV audio file
                intent.setDataAndType(uri, "application/x-wav")
            } else if (file.toString().contains(".rtf")) {
                // RTF file
                intent.setDataAndType(uri, "application/rtf")
            } else if (file.toString().contains(".wav") || file.toString().contains(".mp3")) {
                // WAV audio file
                intent.setDataAndType(uri, "audio/x-wav")
            } else if (file.toString().contains(".gif")) {
                // GIF file
                intent.setDataAndType(uri, "image/gif")
            } else if (file.toString().contains(".jpg") || file.toString()
                    .contains(".jpeg") || file.toString().contains(
                    ".png"
                )
            ) {
                // JPG file
                intent.setDataAndType(uri, "image/jpeg")
            } else if (file.toString().contains(".txt")) {
                // Text file
                intent.setDataAndType(uri, "text/plain")
            } else if (file.toString().contains(".3gp") || file.toString()
                    .contains(".mpg") || file.toString().contains(
                    ".mpeg"
                ) || file.toString().contains(".mpe") || file.toString()
                    .contains(".mp4") || file.toString().contains(
                    ".avi"
                )
            ) {
                // Video files
                intent.setDataAndType(uri, "video/*")
            } else {
                //if you want you can also define the intent type for any other file
                //additionally use else clause below, to manage other unknown extensions
                //in this case, Android will show all applications installed on the device
                //so you can choose which application to use
                intent.setDataAndType(uri, "*/*")
            }

            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
            }
        }

        fun getMediaExtension(value: String): String {
            var separated = value.split(".")
            var extension = "." + separated.get(separated.size - 1)
            return extension
        }

        fun writeResponseBodyToDisk(body: ResponseBody, path: String): Boolean {
            try {
                // todo change the file location/name according to your needs

                val futureStudioIconFile = File(path)

                var inputStream: InputStream? = null
                var outputStream: OutputStream? = null

                try {
                    val fileReader = ByteArray(4096)

                    val fileSize = body.contentLength()
                    var fileSizeDownloaded: Long = 0

                    inputStream = body.byteStream()
                    outputStream = FileOutputStream(futureStudioIconFile)

                    while (true) {
                        val read = inputStream.read(fileReader)

                        if (read == -1) {
                            break
                        }

                        outputStream.write(fileReader, 0, read)

                        fileSizeDownloaded += read.toLong()

                    }

                    outputStream.flush()

                    return true
                } catch (e: IOException) {
                    return false
                } finally {
                    inputStream?.close()

                    outputStream?.close()
                }
            } catch (e: IOException) {
                return false
            }

        }

        ///////////////Show custom alert dialog///////////////////////



        fun dpToPx(context: Context, dp: Int): Int {
            val displayMetrics = context.resources.displayMetrics
            return Math.round(dp * (displayMetrics.xdpi / DisplayMetrics.DENSITY_DEFAULT)).toInt()
        }


        fun pxToDp(context: Context, px: Int): Int {
            return px / (context.resources.displayMetrics.densityDpi / DisplayMetrics.DENSITY_DEFAULT)
        }

        @JvmStatic
        fun toLowerCase(text: String): String {
            return text.lowercase(Locale.getDefault())
        }

        fun getStartEndDateOfCurrentMonth(s: String): Long {
            val calendar = Calendar.getInstance()
            if (s == "start") {
                calendar[Calendar.DATE] = calendar.getActualMinimum(Calendar.DAY_OF_MONTH)
            } else {
                calendar[Calendar.DATE] = calendar.getActualMaximum(Calendar.DAY_OF_MONTH)
            }
            calendar.timeZone = TimeZone.getTimeZone("GMT")
            calendar.set(Calendar.HOUR, 0)
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            return calendar.timeInMillis / 1000
        }

        @JvmStatic
        fun calculateDuration(context: Context, minDuration: Int): String {
            if (minDuration < 60) {
                if (minDuration <= 1)
                    return minDuration.toString() + " " + "minute".lowercase(Locale.getDefault())
                else
                    return minDuration.toString() + " " + "minutes".lowercase(Locale.getDefault())
            } else {
                var hour = minDuration / 60
                var minutes = minDuration % 60
                var hourText =
                    if (hour <= 1) "hour" else "hours"
                var minutesText =
                    if (minutes <= 1) "minute" else "minutes"
                return if (minutes > 0) (hour.toString() + " " + hourText + " & " + minDuration.toString() + " " + minutesText).lowercase(
                    Locale.getDefault()
                ) else (hour.toString() + " " + hourText).lowercase(
                    Locale.getDefault()
                )
            }
        }

        @JvmStatic
        fun getCurrentTimezoneOffset(): String {
            val timezone = TimeZone.getDefault()
            val calendar = GregorianCalendar.getInstance(timezone)
            val offsetInMillis = timezone.getOffset(calendar.timeInMillis)
            var offset = (offsetInMillis / 60000).toString()
            offset = (if (offsetInMillis >= 0) "+" else "-") + offset
            return offset
        }

        @JvmStatic
        open fun getGMTTimezoneOffset(): String? {
            val tz = TimeZone.getDefault()
            val cal = GregorianCalendar.getInstance(tz)
            val offsetInMillis = tz.getOffset(cal.timeInMillis)
            var offset = String.format(
                "%2d%02d",
                Math.abs(offsetInMillis / 3600000),
                Math.abs(offsetInMillis / 60000 % 60)
            )
            offset = (if (offsetInMillis >= 0) "+" else "-") + offset
            return offset
        }
        // value==0 get Height
        //value==1 get Width
        fun getCalendarHeightWidth(context: Context, value: Int, marginDp: Int): Int {
            val windowManager: WindowManager =
                context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
            val displayMetrics = DisplayMetrics()
            windowManager.defaultDisplay.getMetrics(displayMetrics)
            val screenWidth = displayMetrics.widthPixels - dpToPx(context, marginDp)
            var tileWidth = screenWidth / 7
            var fouthRatio = tileWidth / 4
            if (value == 0) {
                return pxToDp(context, fouthRatio * 3)
            } else {
                return pxToDp(context, tileWidth)-7 //minus -7dp according change insetLeft/Right in my_selector_inset_popup.xml
            }
        }

        fun getMediaPath(context: Context, uri: Uri): String {

            val resolver = context.contentResolver
            val projection = arrayOf(MediaStore.Video.Media.DATA)
            var cursor: Cursor? = null
            try {
                cursor = resolver.query(uri, projection, null, null, null)
                return if (cursor != null) {
                    val columnIndex = cursor.getColumnIndexOrThrow(MediaStore.Video.Media.DATA)
                    cursor.moveToFirst()
                    cursor.getString(columnIndex)

                } else ""

            } catch (e: Exception) {
                resolver.let {
                    val filePath = (context.applicationInfo.dataDir + File.separator
                            + System.currentTimeMillis())
                    val file = File(filePath)

                    resolver.openInputStream(uri)?.use { inputStream ->
                        FileOutputStream(file).use { outputStream ->
                            val buf = ByteArray(4096)
                            var len: Int
                            while (inputStream.read(buf).also { len = it } > 0) outputStream.write(
                                buf,
                                0,
                                len
                            )
                        }
                    }
                    return file.absolutePath
                }
            } finally {
                cursor?.close()
            }
        }

        fun getFileSize(size: Long): String {
            if (size <= 0)
                return "0"

            val units = arrayOf("B", "KB", "MB", "GB", "TB")
            val digitGroups = (log10(size.toDouble()) / log10(1024.0)).toInt()

            return DecimalFormat("#,##0.#").format(
                size / 1024.0.pow(digitGroups.toDouble())
            ) + " " + units[digitGroups]
        }

        //The following methods can be alternative to [getMediaPath].
// todo(abed): remove [getPathFromUri], [getVideoExtension], and [copy]
        fun getPathFromUri(context: Context, uri: Uri): String {
            var file: File? = null
            var inputStream: InputStream? = null
            var outputStream: OutputStream? = null
            var success = false
            try {
                val extension: String = getVideoExtension(uri)
                inputStream = context.contentResolver.openInputStream(uri)
                file = File.createTempFile("compressor", extension, context.cacheDir)
                file.deleteOnExit()
                outputStream = FileOutputStream(file)
                if (inputStream != null) {
                    copy(inputStream, outputStream)
                    success = true
                }
            } catch (ignored: IOException) {
            } finally {
                try {
                    inputStream?.close()
                } catch (ignored: IOException) {
                }
                try {
                    outputStream?.close()
                } catch (ignored: IOException) {
                    // If closing the output stream fails, we cannot be sure that the
                    // target file was written in full. Flushing the stream merely moves
                    // the bytes into the OS, not necessarily to the file.
                    success = false
                }
            }
            return if (success) file!!.path else ""
        }

        /** @return extension of video with dot, or default .mp4 if it none.
         */
        private fun getVideoExtension(uriVideo: Uri): String {
            var extension: String? = null
            try {
                val imagePath = uriVideo.path
                if (imagePath != null && imagePath.lastIndexOf(".") != -1) {
                    extension = imagePath.substring(imagePath.lastIndexOf(".") + 1)
                }
            } catch (e: Exception) {
                extension = null
            }
            if (extension == null || extension.isEmpty()) {
                //default extension for matches the previous behavior of the plugin
                extension = "mp4"
            }
            return ".$extension"
        }

        private fun copy(`in`: InputStream, out: OutputStream) {
            val buffer = ByteArray(4 * 1024)
            var bytesRead: Int
            while (`in`.read(buffer).also { bytesRead = it } != -1) {
                out.write(buffer, 0, bytesRead)
            }
            out.flush()
        }
    }

}