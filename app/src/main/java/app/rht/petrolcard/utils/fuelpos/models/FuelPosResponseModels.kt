package app.rht.petrolcard.utils.fuelpos.models

import androidx.annotation.Keep

@Keep
class PumpReserved{
    var trxToken:String = ""
    var pump:String = ""
    var completionCode:String = ""
}

@Keep
class PumpStarted{
    var trxToken:String = ""
    var completionCode:String = ""
}

@Keep
class PumpReleased {
    var trxToken:String = ""
    var pump:String = ""
    var completionCode:String = ""
}

@Keep
class TransactionData {
    var trxToken:String = ""
    var pump:String = ""
    var completionCode:String = ""
    var currency:String = ""
    var checksum:String = ""
    var dateTime:String = ""
    var productName:String = ""
    var productCode:String = ""
    var quantity:String = ""
    var unitPrice:String = ""
    var vatPercentage:String = ""
    var vatAmount:String =""
    var totalAmount:String = ""
    var id:Int? = null //for internal purpose
    var teleCollectStatus:Int? = null //for internal purpose
}
@Keep
class ForeCourtStatus {
    var completionCode:String = ""
    var operatingMode:String = ""
    var powerSupplyStatus:String = ""
    var pumpsStatus: ArrayList<PumpStatus> = ArrayList()
}

@Keep
class PumpStatus {
    var pump : String = ""
    var state : String = ""
    var releaseType : PumpReleaseType? = null
    var nozzle : String = ""
}

enum class PumpReleaseType {
    ExternalPOS,
    SelfService,
    Manual,
    Neutralization,
    OutdoorPaymentTerminal,
    BNA,
    Prepayment,
    EPR_OPT
}

class FillingState {
    var messageSequence : String = ""
    var transactions : ArrayList<PosTransaction> = ArrayList()
}

@Keep
class PosTransaction {
    var deviceType : String = ""
    var pumpNumber : String = ""
    var fillingAmount : String = ""
    var fillingVolume : String = ""
    var unitPrice : String = ""
    var defaultUnitPrice : String = ""
    var priceSelection : String = ""
    var fillingState : String = ""
    var claimDeviceType : String = ""
    var fillingFuelNumber : String = ""
    var fillingId : String = ""
    var unitMeasure : String = ""
    var fillingTag : String = ""
    var extPosDeviceNumber : String = ""
    var trxChecksum : String = ""
    var nozzleNumber : String = ""
    var messageSequence : String = ""
}

class PosPumpState {
    var pumpNumber : String = ""
    var pumpState : String = ""
    var nozzleNumber : String = ""
    var fuelNumber : String = ""
    var fillingAmount : String = ""
    var fillingVolume : String = ""
    var unitPrice : String = ""
    var priceSelection : String = ""
    var payableFilling : String = ""
    var claimedFilling : String = ""
    var pumpError : String = ""
    var nozzleError : String = ""
    var fuelError : String = ""
    var fuelPriceError : String = ""
    var nozzleBlocked : String = ""
    var deviceType : String = ""
    var unitMeasure : String = ""
    var releaseTag : String = ""
}


class ClaimedTransactionResult {
    var messageSequence : String = ""
    var pumpNumber : String = ""
    var fillingId : String = ""
    var completionCode : String = ""
}


class UnclaimedTransactionResult {
    var messageSequence : String = ""
    var pumpNumber : String = ""
    var fillingId : String = ""
    var completionCode : String = ""
}


class ClearTransactionResult {
    var messageSequence : String = ""
    var pumpNumber : String = ""
    var fillingId : String = ""
    var completionCode : String = ""
}

class LoginResult {
    var messageSequence : String = ""
    var completionCode : String = ""
    var messageInterfaceVersion : String = ""
}
