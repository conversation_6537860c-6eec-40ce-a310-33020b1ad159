package app.rht.petrolcard.utils.esdPrinterJobs

import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.CountDownTimer
import android.os.IBinder
import android.util.Base64
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.service.FusionService

import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.ui.transactionlist.model.*
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.LogUtils.Companion.log
import app.rht.petrolcard.utils.constant.AppConstant.RFID_ACTION_IFSF_CONNECTION_STATE
import app.rht.petrolcard.utils.constant.AppConstant.RFID_ACTION_IFSF_READ_DATA
import app.rht.petrolcard.utils.constant.AppConstant.RFID_IFSF_BYTE_MESSAGE
import app.rht.petrolcard.utils.constant.AppConstant.RFID_IFSF_CONNECTION_STATE
import app.rht.petrolcard.utils.constant.AppConstant.RFID_IFSF_STRING_MESSAGE
import com.altafrazzaque.ifsfcomm.IFSF_CONFIG
import com.altafrazzaque.ifsfcomm.IfsfService
import com.altafrazzaque.ifsfcomm.IfsfService.Companion.isRunning
import com.altafrazzaque.ifsfcomm.ifsf.models.FuelSaleTrxPrams
import com.altafrazzaque.ifsfcomm.ifsf.models.IfsfConfig
import com.google.gson.Gson
import com.google.gson.JsonSyntaxException
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.exception.ExceptionUtils
import java.lang.Exception
import java.lang.IllegalStateException
import java.util.*

class EsdSignService : Service() {


    companion object {
        private val TAG = EsdSignService::class.simpleName
        private lateinit var context : Context
        private lateinit var logWriter: LogWriter

        var fusionFuelTrxList: ArrayList<TransactionFromFcc> = ArrayList<TransactionFromFcc>()
        var fusionProductList: ArrayList<ProductPT> = ArrayList<ProductPT>()
        var transactionSequenceList: ArrayList<DeviceClassGAFT> = ArrayList<DeviceClassGAFT>()


        private fun getUnpaidTransactionDetailsFromFusion() {
            for (transactionSequence in transactionSequenceList) {
                val trxSequenceNo: String = transactionSequence.transactionSeqNo.toString() + ""
                val mPumpNumber: String = transactionSequence.pumpNo.toString() + ""
                val params = FuelSaleTrxPrams(mPumpNumber, trxSequenceNo)
                logWriter.appendLog(
                    TAG,
                    "Sending Command for Pump: $mPumpNumber SeqNo: $trxSequenceNo"
                )
                FusionService.sendFuelTrxDetailMsg(params, MainApp.appContext)
            }
        }
        private fun addFuelTransactionToList(serviceResponseGFST: ServiceResponseGFST) {
            val currentTransaction: DeviceClassFST = serviceResponseGFST.serviceResponse.fDCdata.deviceClass
            val timeStamp: String = serviceResponseGFST.serviceResponse.fDCdata.fDCTimeStamp
            val fusionTransaction = TransactionFromFcc()
            fusionTransaction.amount = currentTransaction.amount
            fusionTransaction.dh_transaction =  timeStamp.replace("T", " ")
            fusionTransaction.hose = currentTransaction.productNo
            fusionTransaction.pompiste = ""
            fusionTransaction.produit = currentTransaction.productNo.toString()
            fusionTransaction.pu =  currentTransaction.unitPrice
            fusionTransaction.pump = currentTransaction.pumpNo
            fusionTransaction.quantite = currentTransaction.volume
            fusionTransaction.ref_transaction = currentTransaction.transactionSeqNo.toString() + ""
            fusionTransaction.rfid= ""
            fusionFuelTrxList.add(fusionTransaction)
            log(TAG, "Fuel Trx added in list: " + fusionTransaction.ref_transaction)
            if (fusionFuelTrxList.size == transactionSequenceList.size) {
                log(TAG, "All Trx Added in List")
                for (trx in fusionFuelTrxList) {
                    log(TAG, "Trx Sequence: " + trx.ref_transaction)
                }
                startEsdSigningProcess()
            }
        }


        var signedTransactions: ArrayList<TransactionModel> = ArrayList<TransactionModel>()
        private fun createTransactionTaxiModel(transaction: TransactionFromFcc): TransactionModel {
            val transactionTaxi = TransactionModel()
            transactionTaxi.pumpId = transaction.pump.toString() + ""
            transactionTaxi.amount = transaction.amount
            transactionTaxi.quantite = transaction.quantite
            transactionTaxi.idProduit = (transaction.hose.toString() + "").toInt()
            transactionTaxi.sequenceController = transaction.ref_transaction.toString() + ""
            return transactionTaxi
        }

        private var oldMessageForEsd: String? = ""
        private var isEsdRequire = false
        var signCount = 0
        var message = ""

        private var isSignature = false

        private fun startEsdSigningProcess() {
            val prefs = MainApp.getPrefs()
            isEsdRequire =  prefs.getReferenceModel()!!.fiscal_printer!!.isAvailable
            log(TAG, "ESD Signature Config: $isEsdRequire")
            log(TAG, "Total trx to sign: " + fusionFuelTrxList.size)
            if (isEsdRequire && !fusionFuelTrxList.isEmpty()) {
                signTrxTask().execute()
            } else {
                log(TAG, "ESD Signature not required")
            }
        }

        private fun getBlocToSignContent(text: String): String? {
            val data = text.toByteArray() //"UTF-8" removed by altaf
            val base64 = Base64.encodeToString(data, Base64.DEFAULT)
            log("Base 64 ", base64.replace("\\s+".toRegex(), "") + "!")
            var blockToSign = base64.replace("\\s+".toRegex(), "")
            blockToSign = "\u0002&/" + blockToSign + "/" + Utils.CalcChecksum("&/$blockToSign/".toByteArray()) + "\u0003"
            return blockToSign
        }

        private var esdSignature = ""
        private lateinit var udpManager: UDPManager
        private val request1 = "\u0002{/0/09\u0003JN"
        private var messageToSend = ""
        private val request3 = "\u0002}/72\u0003NN"
        private val request4 = "\u0002^/41\u0003"
        var fiscalDate = ""
        private fun sendUdpMessage(message: String) {
            try {
                udpManager.sendMessage(message)
                logWriter.appendLog(TAG, "SENT to ESD: $message")
                Thread.sleep(500)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
        }
        internal class signTrxTask : CoroutineAsyncTask<Void?, Void?, Boolean?>() {
            override fun doInBackground(vararg params: Void?): Boolean? {
                try {
                    val prefs = MainApp.getPrefs()
                    signatureCountDownTimer.start()
                    val trx: TransactionModel = createTransactionTaxiModel(fusionFuelTrxList[0])
                    fusionFuelTrxList.removeAt(0)
                    message = Support.prepareEsdMessage(trx)

                    oldMessageForEsd = message
                    isSignature = false
                    messageToSend = getBlocToSignContent(message)!!
                    val ip: String = if (BuildConfig.DEBUG) "**************" else prefs.getReferenceModel()!!.fiscal_printer!!.IPESD
                    val port: Int = prefs.getReferenceModel()!!.fiscal_printer!!.PORTESD
                    if (udpManager == null) {
                        udpManager = UDPManager.getUdpManager(ip, port)
                        udpManager.startUDPSocket()
                    }
                   logWriter.appendLog(TAG, "ESD Printer connected: $ip:$port")
                    udpManager.setUdpReceiveCallback { data ->
                        val strReceive = String(data.data, 0, data.length)
                        logWriter.appendLog(TAG,"RECEIVED From ESD: $strReceive")
                        if (strReceive.length > 15) {
                            esdSignature = strReceive
                            //log(TAG,"Big message from ESD: "+strReceive);
                            val packet = strReceive.split("/").toTypedArray()
                            log(TAG, "Total Message part: " + packet.size)
                            for (i in packet.indices) {
                                log(TAG, "Part:" + i + " Data:" + packet[i])
                            }
                            fiscalDate = packet[5].trim { it <= ' ' }
                            esdSignature = packet[7].trim { it <= ' ' }
                            isSignature = true
                            signCount++
                            signatureCountDownTimer.cancel()
                            trx.transactionSignature = esdSignature + "_" + fiscalDate
                            val seq =
                                String.format("%04d", trx.sequenceController!!.toInt())
                            val pumpId =
                                String.format("%02d", trx.pumpId!!.toInt())
                            val productId = java.lang.String.format("%02d", trx.idProduit)
                            var serialNumber = Support.getSN()
                            val date =
                                Support.dateToString(Date())!!.replace(" ", "").replace("-", "")
                                    .replace(":", "")
                            serialNumber =
                                if (serialNumber!!.length > 4) serialNumber.substring(serialNumber.length - 4) else serialNumber
                            //ESD Serial Number(last 4 digit) TimeStamp SeqController(4 digits) pumpID(2 digits) productId (2 digits)
                            trx.reference = "ESD$serialNumber$date$seq$pumpId$productId"
                            signedTransactions.add(trx)
                            log(TAG, "SIGN RECEIVED FOR : " + trx.sequenceController.toString() + " SIGN: " + esdSignature)
                            esdSignature = ""
                        }
                    }
                    sendUdpMessage(request1)
                    sendUdpMessage(messageToSend)
                    sendUdpMessage(request3)
                    sendUdpMessage(request4)
                    while (!isSignature) {
                    }
                } catch (e: Exception) {
                    logWriter.appendLog(TAG, "Exception: " + ExceptionUtils.getStackTrace(e))
                }
                return null
            }

        }

        var signatureCountDownTimer: CountDownTimer = object : CountDownTimer( /*120000*/10000, 1000) {
            var count = 0
            override fun onTick(millisUntilFinished: Long) {
                count++
                log(TAG, "Waiting for ESD Signature: $count")
            }

            override fun onFinish() {
                try {
                    count = 0
                    log(TAG, "Sign not received for trx: $oldMessageForEsd")
                    isSignature = true
                } catch (e: IllegalStateException) {
                    log(TAG, e.message!!)
                }
            }
        }


    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        log(TAG, "ESD SignService Started")
        context = MainApp.appContext
        logWriter = LogWriter("ScheduledEsdSignJob")
        startIfsfService()
        initiateSigningProcess()
        return START_NOT_STICKY
    }

    private fun initiateSigningProcess() {
        searchTrxSequenceInFusion()
    }

    private fun searchTrxSequenceInFusion() {
        try {
            if (FusionService.ifsfManager != null) {
                FusionService.getAllAvailableTransactions()
            } else {
              logWriter.appendLog(TAG, "Fusion connection timeout")
            }
        } catch (e: Exception) {
            logWriter.appendLog(TAG, "Fusion connection timeout")
            e.printStackTrace()
        }
    }

    //region ifsf comm
    private var fusion_address = "************"
    private var fusion_port = 4710
    var senderId = ""
    private fun startIfsfService() {
        log(TAG, "Starting Ifsf Service ")
        val prefs = MainApp.getPrefs()
        val fusionUrl: Array<String> = Support.splitIpAndPortFromUrl(prefs.getFusionModel()!!.API)
        fusion_address = fusionUrl[0]
        fusion_port = fusionUrl[1].toInt()
        log(TAG, "Server IP: $fusion_address")
        val intent = Intent(this, IfsfService::class.java)
        senderId = Support.getSN()!!
        if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)
        intent.putExtra(
            IFSF_CONFIG, IfsfConfig(
                senderId,
                "TERMINAL",
                fusion_address,
                fusion_port,
                2,
                true,
                true
            )
        )
        if (!isRunning(this, )) {
            startService(intent)
        }
        startIfsfReceiver()
    }

    private fun startIfsfReceiver() {
        val filter = IntentFilter()
        filter.addAction(RFID_ACTION_IFSF_READ_DATA)
        filter.addAction(RFID_ACTION_IFSF_CONNECTION_STATE)
        registerReceiver(ifsfRecever, filter)
        log(TAG, "startIfsfReceiver started")
    }

    private fun stopIfsfReceiver() {
        try {
            unregisterReceiver(ifsfRecever)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        log(TAG, "IFSF RECEIVER STOPPED")
    }

    var ifsfRecever: BroadcastReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            val action = intent.action
            println("Receiver Action: $action")
            try {
                if (action == RFID_ACTION_IFSF_READ_DATA) {
                    val msg = intent.getStringExtra(RFID_IFSF_STRING_MESSAGE)
                    val bytes = intent.getByteArrayExtra(RFID_IFSF_BYTE_MESSAGE)
                    log(TAG, "Message: $msg")
                    performNextStep(msg!!)
                }
                if (action == RFID_ACTION_IFSF_CONNECTION_STATE) {
                    val state = intent.getStringExtra(RFID_IFSF_CONNECTION_STATE)
                    log(TAG, "CONNECTION State: $state")
                    logWriter.appendLog(
                        TAG,
                        "CONNECTION State Received: $state"
                    )
                    if (state == "TIMEOUT") {
                        logWriter.appendLog(
                            TAG,
                            "Fusion Connection Timeout"
                        )
                    }
                }
            } catch (e: Exception) {
                log(TAG, "ERROR IN BR RECEIVER")
                e.printStackTrace()
                logWriter.appendLog(TAG, "Exception: " + e.message)
            }
        }
    }
    private fun formatMessage(message: String): String {
        var message = message
        message = if (message.contains("</ServiceResponse>")) {
            //split = response.split("</ServiceResponse>");
            "<ServiceResponse " + StringUtils.substringBetween(
                message,
                "<ServiceResponse",
                "</ServiceResponse>"
            ) + "</ServiceResponse>"
        } else {
            val msgArr =
                message.split("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />").toTypedArray()
            if (msgArr.size > 2) println(msgArr[0] + ", " + msgArr[1])
            if (!msgArr[0].contains("xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />")) "<ServiceResponse>" + StringUtils.substringBetween(
                msgArr[0], "<ServiceResponse>", "</ServiceResponse>"
            ) + "</ServiceResponse>" else msgArr[0] + "xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" />"
        }
        return message
    }
    private fun performNextStep(message: String) {
        var message = message
        val errorCode = StringUtils.substringBetween(message, "<ErrorCode>", "</ErrorCode>")
        log(TAG, "Error Code: $errorCode")
        val sender = StringUtils.substringBetween(message, "ApplicationSender=\"", "\"")

        //logWriter.appendLog(TAG,"Received: "+errorCode);
        if (errorCode.contains("ERRCD_OK")) {
            if (message.contains("ServiceResponse")) {
                message = formatMessage(message)
                val overallResult = StringUtils.substringBetween(message, "OverallResult=\"", "\"")
                val requestType = StringUtils.substringBetween(message, "RequestType=\"", "\"")
                val gson = Gson()
                if (overallResult == "Success" && errorCode == "ERRCD_OK") {
                    val jsonString = Support.xmlToJsonString(message)
                    log(TAG, "JSON STRING:::: $jsonString")
                    if (requestType == "GetAvailableFuelSaleTrxs") {
                        clearFusionListTrxList()
                        try {
                            val serviceResponse: ServiceResponseGAFT = gson.fromJson(
                                jsonString,
                                ServiceResponseGAFT::class.java
                            )
                            val trxList: ArrayList<DeviceClassGAFT> = serviceResponse.serviceResponse.fDCdata.deviceClasses
                            transactionSequenceList = trxList
                        } catch (e: Exception) {
                            log(TAG, "Conversion Issue, trying single transaction obj")
                            //e.printStackTrace();
                            val stacktrace = ExceptionUtils.getStackTrace(e)
                            logWriter.appendLog(TAG, e.message + " " + stacktrace)
                            var serviceResponse: ServiceResponseGAFT2? = null
                            try {
                                serviceResponse = gson.fromJson(jsonString, ServiceResponseGAFT2::class.java)
                                val transaction: DeviceClassGAFT = serviceResponse.serviceResponse.fDCdata.deviceClass
                                val trxList: ArrayList<DeviceClassGAFT> = ArrayList<DeviceClassGAFT>()
                                trxList.add(transaction)
                                transactionSequenceList = trxList
                            } catch (ex: Exception) {
                                ex.printStackTrace()
                                val stacktrace1 = ExceptionUtils.getStackTrace(ex)
                                logWriter.appendLog(TAG, ex.message + " " + stacktrace1)
                            }
                        }
                    } else if (requestType == "GetFuelSaleTrxDetails") {
                        try {
                            val transaction: ServiceResponseGFST = gson.fromJson(
                                jsonString,
                                ServiceResponseGFST::class.java
                            )
                            log(TAG, "GetFuelSaleTrxDetails JSON STRING:::: $transaction")
                            addFuelTransactionToList(transaction)
                        } catch (e: JsonSyntaxException) {
                            e.printStackTrace()
                            val stacktrace = ExceptionUtils.getStackTrace(e)
                            logWriter.appendLog(TAG, "Json syntax exception: " + e.message + " " + stacktrace)
                        }
                    } else if (requestType == "GetProductTable") {
                        try {
                            fusionProductList = gson.fromJson(jsonString, ServiceResponseGPT::class.java).serviceResponse.fDCdata.fuelProducts.product
                        } catch (e: Exception) {
                            val stacktrace = ExceptionUtils.getStackTrace(e)
                            logWriter.appendLog(TAG, "Error occurred while fetching products from fusion: $stacktrace")
                            e.printStackTrace()
                        }
                    }
                } else {
                    logWriter.appendLog(TAG, overallResult)
                }
            }
        }
    }
    private fun clearFusionListTrxList() {
        fusionFuelTrxList.clear()
    }


    //endregion




}