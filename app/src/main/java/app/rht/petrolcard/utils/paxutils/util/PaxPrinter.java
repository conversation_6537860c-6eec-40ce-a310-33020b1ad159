package app.rht.petrolcard.utils.paxutils.util;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;

import com.pax.dal.entity.EFontTypeAscii;
import com.pax.dal.entity.EFontTypeExtCode;

import java.io.IOException;
import java.io.InputStream;
import app.rht.petrolcard.utils.paxutils.modules.printer.PrinterTester;


public class PaxPrinter {

    // PAX
    public static Bitmap printImageBase(String image, Context c){
        int result = 0;
        Bitmap bitmap = null;
        try {
            InputStream inputStream = c.getAssets().open(image);
            bitmap =  BitmapFactory.decodeStream(inputStream);
            //result = mPrinter.printImageBase(bitmap, 100, 100, Printer.Align.RIGHT, 0);
            PrinterTester.getInstance().leftIndents(Short.parseShort("110"));
            PrinterTester.getInstance().printBitmap(bitmap);

            bitmap.recycle();
        }catch (IOException e){
            e.printStackTrace();
        }

        return bitmap ;
    }

    public static void initPaxPrinter(Context context){

        PrinterTester.getInstance().init();
        PrinterTester.getInstance().fontSet(EFontTypeAscii.FONT_12_24, EFontTypeExtCode.FONT_16_16);
        PrinterTester.getInstance().step(40);

        //PrinterTester.getInstance().leftIndents((short) 3);
        PrinterTester.getInstance().setGray(1);

        //PrinterTester.getInstance().spaceSet(Byte.parseByte("1"), Byte.parseByte("1"));
        PrinterTester.getInstance().setGray(Integer.parseInt("1"));
        //PrinterTester.getInstance().setDoubleWidth(true, true);
        //PrinterTester.getInstance().setDoubleHeight(true, true);
        PrinterTester.getInstance().setInvert(false);

        PrinterTester.getInstance().leftIndents(Short.parseShort("110"));

        Bitmap bitm = printImageBase("petrol_ic.png", context);



    }

    public static void initPaxPrinter(Context context, EFontTypeAscii FONTaSCII, EFontTypeExtCode TYPEeXT){


        PrinterTester.getInstance().init();
        PrinterTester.getInstance().fontSet(FONTaSCII, TYPEeXT);
        PrinterTester.getInstance().step(40);

        //PrinterTester.getInstance().leftIndents((short) 3);
        PrinterTester.getInstance().setGray(1);

        //PrinterTester.getInstance().spaceSet(Byte.parseByte("1"), Byte.parseByte("1"));
        PrinterTester.getInstance().setGray(Integer.parseInt("1"));
        //PrinterTester.getInstance().setDoubleWidth(true, true);
        //PrinterTester.getInstance().setDoubleHeight(true, true);
        PrinterTester.getInstance().setInvert(false);

        PrinterTester.getInstance().leftIndents(Short.parseShort("110"));

        Bitmap bitm = printImageBase("petrol_ic.png", context);



    }

    public static void paxStringPrinter(String texte,short pad){

        PrinterTester.getInstance().leftIndents(pad);

        PrinterTester.getInstance().printStr(texte,null );

    }

    public static void paxStringPrinter(String texte){


        PrinterTester.getInstance().printStr( texte,null );

    }

    public static String finishPaxPrinting(){

        PrinterTester.getInstance().printStr("\n" ,null );
        PrinterTester.getInstance().printStr("\n" ,null );
        PrinterTester.getInstance().printStr("\n" ,null );
        PrinterTester.getInstance().printStr("\n" ,null );
        PrinterTester.getInstance().printStr("\n" ,null );


        PrinterTester.getInstance().step(5);

        //PrinterTester.getInstance().cutPaper(1);



        return PrinterTester.getInstance().start();


    }
}
