package app.rht.petrolcard.utils.paxutils.icc;

import static app.rht.petrolcard.utils.LogUtils.log;

import com.pax.dal.IIcc;
import com.pax.dal.entity.ApduRespInfo;
import com.pax.dal.entity.ApduSendInfo;
import com.pax.dal.exceptions.IccDevException;

import org.apache.commons.lang3.exception.ExceptionUtils;

import app.rht.petrolcard.MainApp;
import app.rht.petrolcard.utils.paxutils.system.BaseTester;


public class IccTester extends BaseTester {

    private static IccTester iccTester;
    private static final String TAG=  IccTester.class.getSimpleName();

    private final IIcc icc;
    private IccTester() {
        icc = MainApp.Companion.getDal().getIcc();
    }

    /**
     *
     * @return
     */
    public static IccTester getInstance() {
        if (iccTester == null) {
            iccTester = new IccTester();
        }
        return iccTester;
    }

    /**
     *
     * @param slot
     * @return
     */
    public byte[] init(byte slot) {
        byte[] initRes = null;
        try {
            initRes = icc.init(slot);
            logTrue("init");
            return initRes;
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("init", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     *
     * @param slot
     * @return
     */
    public boolean detect(byte slot) {
        boolean res = false;
        try {
            res = icc.detect(slot);
            logTrue("detect");
            return res;
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("detect", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            return res;
        }
    }

    /**
     *
     * @param slot
     */
    public void close(byte slot) {
        try {
            icc.close(slot);
            logTrue("close");
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("close", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     *
     * @param slot
     * @param autoresp
     */
    public void autoResp(byte slot, boolean autoresp) {
        try {
            icc.autoResp(slot, autoresp);
            logTrue("autoResp");
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("autoResp", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     *
     * @param slot
     * @param send
     * @return
     */
    public byte[] isoCommand(byte slot, byte[] send) {
        try {
            byte[] resp = icc.isoCommand(slot, send);
            logTrue("isoCommand");
            return resp;
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("isoCommand", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     *
     * @param slot
     * @param apduSendInfo
     * @return
     */
    public ApduRespInfo isoCommandByApdu(byte slot, ApduSendInfo apduSendInfo) {
        try {
            ApduRespInfo resp = icc.isoCommandByApdu(slot, apduSendInfo);
            logTrue("isoCommand");
            return resp;
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("isoCommand", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    /**
     *
     * @param flag
     */
    public void light(boolean flag){
        try {
            icc.light(flag);
            logTrue("light");
        } catch (IccDevException e) {
            e.printStackTrace();
            logErr("light", e.toString());
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
    }
}
