//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

package app.rht.petrolcard.utils.tims;

import java.util.Date;

import TremolZFP.FPcore;

public class FP extends FPcore
{
     public FP(){
        this.setVersionDef(2206201550L);
    }

    /**
     *Start WiFi test on the device the result
     * @throws Exception
     */
     public void StartWiFiTest() throws Exception {
             Do("StartWiFiTest");
     }

    /**
     *Provides information about the manufacturing number of the CU and PIN number.
     * @return CUnumbersRes
     * @throws Exception
     */
     public CUnumbersRes ReadCUnumbers() throws Exception {
             return CreateRes(Do("ReadCUnumbers"), CUnumbersRes.class);
     }

    /**
     *Store Electronic Journal Report from report from date to date to External USB Flash memory, External SD card.
     * @param  optionReportStorage
     *         2 symbols for destination: 
     *  - 'J2' - Storage in External USB Flash memory 
     *  - 'J4' - Storage in External SD card memory 
     *  - 'Jx' - Storage in External USB Flash memory for JSON 
     *  - 'JX' - Storage in External SD card memory for JSON
     * @param  startRepFromDate
     *         6 symbols for initial date in the DDMMYY format
     * @param  endRepFromDate
     *         6 symbols for final date in the DDMMYY format
     * @throws Exception
     */
     public void StoreEJByDate(OptionReportStorage optionReportStorage, Date startRepFromDate, Date endRepFromDate) throws Exception {
             Do("StoreEJByDate", "OptionReportStorage", optionReportStorage, "StartRepFromDate", startRepFromDate, "EndRepFromDate", endRepFromDate);
     }

    /**
     *Shows a 16-symbols text in the first line and last 16-symbols text in the second line of the external display lines.
     * @param  text
     *         32 symbols text
     * @throws Exception
     */
     public void DisplayTextLines1and2(String text) throws Exception {
             Do("DisplayTextLines1and2", "Text", text);
     }

    /**
     *Provides information about the current date and time.
     * @return Date
     * @throws Exception
     */
     public Date ReadDateTime() throws Exception {
             return CreateRes(Do("ReadDateTime"), Date.class);
     }

    /**
     *Available only if the receipt is not closed. Cancel all sales in the receipt and close it .
     * @throws Exception
     */
     public void CancelReceipt() throws Exception {
             Do("CancelReceipt");
     }

    /**
     *Provides information about device's idle timeout. This timeout is seconds in which the connection will be closed when there is an inactivity. This information is available if the device has LAN or WiFi. Maximal value - 7200, minimal value 1. 0 is for never close the connection.
     * @return Double
     * @throws Exception
     */
     public Double Read_IdleTimeout() throws Exception {
             return CreateRes(Do("Read_IdleTimeout"), Double.class);
     }

    /**
     *Read invoice threshold count
     * @return Double
     * @throws Exception
     */
     public Double ReadInvoice_Threshold() throws Exception {
             return CreateRes(Do("ReadInvoice_Threshold"), Double.class);
     }

    /**
     *Read time threshold minutes
     * @return Double
     * @throws Exception
     */
     public Double ReadTimeThreshold_Minutes() throws Exception {
             return CreateRes(Do("ReadTimeThreshold_Minutes"), Double.class);
     }

    /**
     *Providing information about server HTTPS address.
     * @return HTTPS_ServerRes
     * @throws Exception
     */
     public HTTPS_ServerRes ReadHTTPS_Server() throws Exception {
             return CreateRes(Do("ReadHTTPS_Server"), HTTPS_ServerRes.class);
     }

    /**
     *Program device's MAC address . To apply use - SaveNetworkSettings()
     * @param  mACAddress
     *         12 symbols for the MAC address
     * @throws Exception
     */
     public void SetDeviceTCP_MACAddress(String mACAddress) throws Exception {
             Do("SetDeviceTCP_MACAddress", "MACAddress", mACAddress);
     }

    /**
     *Register the sell (for correction use minus sign in the price field) of article with specified name, price, quantity, VAT class and/or discount/addition on the transaction.
     * @param  namePLU
     *         36 symbols for article's name
     * @param  price
     *         Up to 13 symbols for article's price
     * @param  hSCode
     *         10 symbols for HS Code in format XXXX.XX.XX
     * @param  quantity
     *         1 to 10 symbols for quantity
     * @param  discAddP
     *         1 to 7 for percentage of discount/addition
     * @throws Exception
     */
     public void SellPLUfromExtDB_HS(String namePLU, Double price, String hSCode, Double quantity, Double discAddP) throws Exception {
             Do("SellPLUfromExtDB_HS", "NamePLU", namePLU, "Price", price, "HSCode", hSCode, "Quantity", quantity, "DiscAddP", discAddP);
     }

    /**
     *Provides information about device's GRPS APN.
     * @return GPRS_APNRes
     * @throws Exception
     */
     public GPRS_APNRes ReadGPRS_APN() throws Exception {
             return CreateRes(Do("ReadGPRS_APN"), GPRS_APNRes.class);
     }

    /**
     *Providing information about device's GPRS user name.
     * @return GPRS_UsernameRes
     * @throws Exception
     */
     public GPRS_UsernameRes ReadGPRS_Username() throws Exception {
             return CreateRes(Do("ReadGPRS_Username"), GPRS_UsernameRes.class);
     }

    /**
     *After every change on Idle timeout, LAN/WiFi/GPRS usage, LAN/WiFi/TCP/GPRS password or TCP auto start networks settings this Save command needs to be execute.
     * @throws Exception
     */
     public void SaveNetworkSettings() throws Exception {
             Do("SaveNetworkSettings");
     }

    /**
     *Provides information about device's network IP address, subnet mask, gateway address, DNS address.
     * @param  optionAddressType
     *         1 symbol with value: 
     *  - '2' - IP address 
     *  - '3' - Subnet Mask 
     *  - '4' - Gateway address 
     *  - '5' - DNS address
     * @return DeviceTCP_AddressesRes
     * @throws Exception
     */
     public DeviceTCP_AddressesRes ReadDeviceTCP_Addresses(OptionAddressType optionAddressType) throws Exception {
             return CreateRes(Do("ReadDeviceTCP_Addresses", "OptionAddressType", optionAddressType), DeviceTCP_AddressesRes.class);
     }

    /**
     *Executes the direct command .
     * @param  input
     *         Raw request to FP
     * @return String
     * @throws Exception
     */
     public String DirectCommand(String input) throws Exception {
             return CreateRes(Do("DirectCommand", "Input", input), String.class);
     }

    /**
     *Program device's TCP WiFi network name where it will be connected. To apply use -SaveNetworkSettings()
     * @param  wiFiNameLength
     *         Up to 3 symbols for the WiFi network name len
     * @param  wiFiNetworkName
     *         Up to 100 symbols for the device's WiFi ssid network name
     * @throws Exception
     */
     public void SetWiFi_NetworkName(Double wiFiNameLength, String wiFiNetworkName) throws Exception {
             Do("SetWiFi_NetworkName", "WiFiNameLength", wiFiNameLength, "WiFiNetworkName", wiFiNetworkName);
     }

    /**
     *Programs HS code at a given position (HS number in order).
     * @param  hS_Number
     *         4 symbols for HS number in order in format ####
     * @return HScodeRes
     * @throws Exception
     */
     public HScodeRes ReadHScode(Double hS_Number) throws Exception {
             return CreateRes(Do("ReadHScode", "HS_Number", hS_Number), HScodeRes.class);
     }

    /**
     *Provides information about documents sending functions .
     * @return DiagnosticsRes
     * @throws Exception
     */
     public DiagnosticsRes ReadDiagnostics() throws Exception {
             return CreateRes(Do("ReadDiagnostics"), DiagnosticsRes.class);
     }

    /**
     *Shows a 20-symbols text in the upper external display line.
     * @param  text
     *         16 symbols text
     * @throws Exception
     */
     public void DisplayTextLine1(String text) throws Exception {
             Do("DisplayTextLine1", "Text", text);
     }

    /**
     *Provides information about the current VAT rates (the last value stored in FM).
     * @return VATratesRes
     * @throws Exception
     */
     public VATratesRes ReadVATrates() throws Exception {
             return CreateRes(Do("ReadVATrates"), VATratesRes.class);
     }

    /**
     *Opens a fiscal invoice debit note receipt assigned to the specified operator number and operator password with free info for customer data. The Invoice receipt can be issued only if the invoice range (start and end numbers) is set.
     * @param  companyName
     *         30 symbols for Invoice company name
     * @param  clientPINnum
     *         14 symbols for client PIN number
     * @param  headQuarters
     *         30 symbols for customer headquarters
     * @param  address
     *         30 symbols for Address
     * @param  postalCodeAndCity
     *         30 symbols for postal code and city
     * @param  exemptionNum
     *         30 symbols for exemption number
     * @param  relatedInvoiceNum
     *         19 symbols for the related invoice number in format 
     * ###################
     * @param  traderSystemInvNum
     *         15 symbols for trader system invoice number
     * @throws Exception
     */
     public void OpenDebitNoteWithFreeCustomerData(String companyName, String clientPINnum, String headQuarters, String address, String postalCodeAndCity, String exemptionNum, String relatedInvoiceNum, String traderSystemInvNum) throws Exception {
             Do("OpenDebitNoteWithFreeCustomerData", "CompanyName", companyName, "ClientPINnum", clientPINnum, "HeadQuarters", headQuarters, "Address", address, "PostalCodeAndCity", postalCodeAndCity, "ExemptionNum", exemptionNum, "RelatedInvoiceNum", relatedInvoiceNum, "TraderSystemInvNum", traderSystemInvNum);
     }

    /**
     *Read the number of HS codes.
     * @return Double
     * @throws Exception
     */
     public Double ReadHScodeNumber() throws Exception {
             return CreateRes(Do("ReadHScodeNumber"), Double.class);
     }

    /**
     *Provides information about the number of the last issued receipt.
     * @return LastAndTotalReceiptNumRes
     * @throws Exception
     */
     public LastAndTotalReceiptNumRes ReadLastAndTotalReceiptNum() throws Exception {
             return CreateRes(Do("ReadLastAndTotalReceiptNum"), LastAndTotalReceiptNumRes.class);
     }

    /**
     *Clears the external display.
     * @throws Exception
     */
     public void ClearDisplay() throws Exception {
             Do("ClearDisplay");
     }

    /**
     *Program device's TCP network DHCP enabled or disabled. To apply use -SaveNetworkSettings()
     * @param  optionDHCPEnabled
     *         1 symbol with value: 
     *  - '0' - Disabled 
     *  - '1' - Enabled
     * @throws Exception
     */
     public void SetDHCP_Enabled(OptionDHCPEnabled optionDHCPEnabled) throws Exception {
             Do("SetDHCP_Enabled", "OptionDHCPEnabled", optionDHCPEnabled);
     }

    /**
     *Program device's GPRS APN. To apply use -SaveNetworkSettings()
     * @param  gprsAPNlength
     *         Up to 3 symbols for the APN len
     * @param  aPN
     *         Up to 100 symbols for the device's GPRS APN
     * @throws Exception
     */
     public void SetGPRS_APN(Double gprsAPNlength, String aPN) throws Exception {
             Do("SetGPRS_APN", "gprsAPNlength", gprsAPNlength, "APN", aPN);
     }

    /**
     *Read GPRS APN authentication type
     * @return OptionAuthenticationType
     * @throws Exception
     */
     public OptionAuthenticationType ReadGPRS_AuthenticationType() throws Exception {
             return CreateRes(Do("ReadGPRS_AuthenticationType"), OptionAuthenticationType.class);
     }

    /**
     *Programs the customer DB for special customer receipt issuing.
     * @param  hS_Code
     *         10 symbols for HS code
     * @param  hS_Name
     *         20 symbols for name of HS group
     * @param  optionTaxable
     *         1 symbol for parameter: 
     * - '1' - Exempted 
     * - '0' - Taxable
     * @param  mesureUnit
     *         3 symbols for mesure unit of item's code
     * @param  vAT_rate
     *         Value of VAT rate from 2 to 5 symbols with format ##.##
     * @throws Exception
     */
     public void AddNewHScode(String hS_Code, String hS_Name, OptionTaxable optionTaxable, String mesureUnit, Double vAT_rate) throws Exception {
             Do("AddNewHScode", "HS_Code", hS_Code, "HS_Name", hS_Name, "OptionTaxable", optionTaxable, "MesureUnit", mesureUnit, "VAT_rate", vAT_rate);
     }

    /**
     *Stores the Manufacturing number into the operative memory.
     * @param  password
     *         6-symbols string
     * @param  serialNum
     *         20 symbols Manufacturing number
     * @throws Exception
     */
     public void SetSerialNum(String password, String serialNum) throws Exception {
             Do("SetSerialNum", "Password", password, "SerialNum", serialNum);
     }

    /**
     * Reads raw bytes from FP.
     * @param  count
     *         How many bytes to read if EndChar is not specified
     * @param  endChar
     *         The character marking the end of the data. If present Count parameter is ignored.
     * @return byte[]
     * @throws Exception
     */
     public byte[] RawRead(Double count, String endChar) throws Exception {
             return CreateRes(Do("RawRead", "Count", count, "EndChar", endChar), byte[].class);
     }

    /**
     *Scan and print available wifi networks
     * @throws Exception
     */
     public void ScanAndPrintWifiNetworks() throws Exception {
             Do("ScanAndPrintWifiNetworks");
     }

    /**
     *Provides information about device's DHCP status
     * @return OptionDHCPEnabled
     * @throws Exception
     */
     public OptionDHCPEnabled ReadDHCP_Status() throws Exception {
             return CreateRes(Do("ReadDHCP_Status"), OptionDHCPEnabled.class);
     }

    /**
     *Shows the current date and time on the external display.
     * @throws Exception
     */
     public void DisplayDateTime() throws Exception {
             Do("DisplayDateTime");
     }

    /**
     *Program device's autostart TCP conection in sale/line mode. To apply use -SaveNetworkSettings()
     * @param  optionTCPAutoStart
     *         1 symbol with value: 
     *  - '0' - No 
     *  - '1' - Yes
     * @throws Exception
     */
     public void SetTCP_AutoStart(OptionTCPAutoStart optionTCPAutoStart) throws Exception {
             Do("SetTCP_AutoStart", "OptionTCPAutoStart", optionTCPAutoStart);
     }

    /**
     *Selects the active communication module - LAN or WiFi. This option can be set only if the device has both modules at the same time. To apply use - SaveNetworkSettings()
     * @param  optionUsedModule
     *         1 symbol with value: 
     *  - '1' - LAN module 
     *  - '2' - WiFi module
     * @throws Exception
     */
     public void SetTCP_ActiveModule(OptionUsedModule optionUsedModule) throws Exception {
             Do("SetTCP_ActiveModule", "OptionUsedModule", optionUsedModule);
     }

    /**
     *Provides information about which module the device is in use: LAN or WiFi module. This information can be provided if the device has mounted both modules.
     * @return OptionUsedModule
     * @throws Exception
     */
     public OptionUsedModule ReadTCP_UsedModule() throws Exception {
             return CreateRes(Do("ReadTCP_UsedModule"), OptionUsedModule.class);
     }

    /**
     *Provides information about device's MAC address.
     * @return String
     * @throws Exception
     */
     public String ReadTCP_MACAddress() throws Exception {
             return CreateRes(Do("ReadTCP_MACAddress"), String.class);
     }

    /**
     *Closes the opened fiscal receipt and returns receipt info.
     * @return CloseReceiptRes
     * @throws Exception
     */
     public CloseReceiptRes CloseReceipt() throws Exception {
             return CreateRes(Do("CloseReceipt"), CloseReceiptRes.class);
     }

    /**
     *Opens a fiscal invoice credit note receipt assigned to the specified operator number and operator password with free info for customer data. The Invoice receipt can be issued only if the invoice range (start and end numbers) is set.
     * @param  companyName
     *         30 symbols for Invoice company name
     * @param  clientPINnum
     *         14 symbols for client PIN number
     * @param  headQuarters
     *         30 symbols for customer headquarters
     * @param  address
     *         30 symbols for Address
     * @param  postalCodeAndCity
     *         30 symbols for postal code and city
     * @param  exemptionNum
     *         30 symbols for exemption number
     * @param  relatedInvoiceNum
     *         19 symbols for the related invoice number in format 
     * ###################
     * @param  traderSystemInvNum
     *         15 symbols for trader system invoice number
     * @throws Exception
     */
     public void OpenCreditNoteWithFreeCustomerData(String companyName, String clientPINnum, String headQuarters, String address, String postalCodeAndCity, String exemptionNum, String relatedInvoiceNum, String traderSystemInvNum) throws Exception {
             Do("OpenCreditNoteWithFreeCustomerData", "CompanyName", companyName, "ClientPINnum", clientPINnum, "HeadQuarters", headQuarters, "Address", address, "PostalCodeAndCity", postalCodeAndCity, "ExemptionNum", exemptionNum, "RelatedInvoiceNum", relatedInvoiceNum, "TraderSystemInvNum", traderSystemInvNum);
     }

    /**
     *Shows a 16-symbols text in the lower external display line.
     * @param  text
     *         16 symbols text
     * @throws Exception
     */
     public void DisplayTextLine2(String text) throws Exception {
             Do("DisplayTextLine2", "Text", text);
     }

    /**
     *Program device's TCP WiFi password where it will be connected. To apply use -SaveNetworkSettings()
     * @param  passLength
     *         Up to 3 symbols for the WiFi password len
     * @param  password
     *         Up to 100 symbols for the device's WiFi password
     * @throws Exception
     */
     public void SetWiFi_Password(Double passLength, String password) throws Exception {
             Do("SetWiFi_Password", "PassLength", passLength, "Password", password);
     }

    /**
     *Provide information from the last communication with the server.
     * @param  optionServerResponse
     *         1 symbol with value 
     * - 'R' - At send receipt 
     * - 'Z' - At send EOD
     * @param  optionTransactionType
     *         1 symbol with value 
     * - 'c' - Error Code 
     * - 'm' - Error Message 
     * - 's' - Status 
     * - 'e' - Exception Message
     * @return InfoFromLastServerCommunicationRes
     * @throws Exception
     */
     public InfoFromLastServerCommunicationRes ReadInfoFromLastServerCommunication(OptionServerResponse optionServerResponse, OptionTransactionType optionTransactionType) throws Exception {
             return CreateRes(Do("ReadInfoFromLastServerCommunication", "OptionServerResponse", optionServerResponse, "OptionTransactionType", optionTransactionType), InfoFromLastServerCommunicationRes.class);
     }

    /**
     *Confirm PIN number.
     * @param  password
     *         6-symbols string
     * @throws Exception
     */
     public void ConfirmFiscalization(String password) throws Exception {
             Do("ConfirmFiscalization", "Password", password);
     }

    /**
     *Store whole Electronic Journal report to External USB Flash memory, External SD card.
     * @param  optionReportStorage
     *         2 symbols for destination: 
     *  - 'J2' - Storage in External USB Flash memory 
     *  - 'J4' - Storage in External SD card memory 
     *  - 'Jx' - Storage in External USB Flash memory for JSON 
     *  - 'JX' - Storage in External SD card memory for JSON
     * @throws Exception
     */
     public void StoreEJ(OptionReportStorage optionReportStorage) throws Exception {
             Do("StoreEJ", "OptionReportStorage", optionReportStorage);
     }

    /**
     *FlagsModule is a char with bits representing modules supported by the firmware
     * @return DeviceModuleSupportByFirmwareRes
     * @throws Exception
     */
     public DeviceModuleSupportByFirmwareRes ReadDeviceModuleSupportByFirmware() throws Exception {
             return CreateRes(Do("ReadDeviceModuleSupportByFirmware"), DeviceModuleSupportByFirmwareRes.class);
     }

    /**
     *Providing information about WiFi password where the device is connected.
     * @return WiFi_PasswordRes
     * @throws Exception
     */
     public WiFi_PasswordRes ReadWiFi_Password() throws Exception {
             return CreateRes(Do("ReadWiFi_Password"), WiFi_PasswordRes.class);
     }

    /**
     *Provides information about device's NTP address.
     * @return NTP_AddressRes
     * @throws Exception
     */
     public NTP_AddressRes ReadNTP_Address() throws Exception {
             return CreateRes(Do("ReadNTP_Address"), NTP_AddressRes.class);
     }

    /**
     *Provides information about the accumulated amounts and refunded amounts by VAT class in case that CU regularly informs about the Z report(7C)
     * @return DailyAmountsByVATRes
     * @throws Exception
     */
     public DailyAmountsByVATRes ReadDailyAmountsByVAT() throws Exception {
             return CreateRes(Do("ReadDailyAmountsByVAT"), DailyAmountsByVATRes.class);
     }

    /**
     *Reads all messages from log
     * @return String
     * @throws Exception
     */
     public String ReadTotalMessagesCount() throws Exception {
             return CreateRes(Do("ReadTotalMessagesCount"), String.class);
     }

    /**
     *Programs invoice threshold count
     * @param  value
     *         Up to 5 symbols for value
     * @throws Exception
     */
     public void SetInvoice_ThresholdCount(Double value) throws Exception {
             Do("SetInvoice_ThresholdCount", "Value", value);
     }

    /**
     *Program device used to talk with the server . To apply use - SaveNetworkSettings()
     * @param  optionModule
     *         1 symbol with value: 
     *  - '0' - GSM 
     *  - '1' - LAN/WiFi
     * @throws Exception
     */
     public void SetServer_UsedComModule(OptionModule optionModule) throws Exception {
             Do("SetServer_UsedComModule", "OptionModule", optionModule);
     }

    /**
     *Program device's TCP password. To apply use - SaveNetworkSettings()
     * @param  passLength
     *         Up to 3 symbols for the password len
     * @param  password
     *         Up to 100 symbols for the TCP password
     * @throws Exception
     */
     public void SetTCP_Password(Double passLength, String password) throws Exception {
             Do("SetTCP_Password", "PassLength", passLength, "Password", password);
     }

    /**
     *Programs GPRS APN authentication type
     * @param  optionAuthenticationType
     *         1 symbol with value: 
     * - '0' - None 
     * - '1' - PAP 
     * - '2' - CHAP 
     * - '3' - PAP or CHAP
     * @throws Exception
     */
     public void SetGPRS_AuthenticationType(OptionAuthenticationType optionAuthenticationType) throws Exception {
             Do("SetGPRS_AuthenticationType", "OptionAuthenticationType", optionAuthenticationType);
     }

    /**
     *Read the current status of the receipt.
     * @return CurrentReceiptInfoRes
     * @throws Exception
     */
     public CurrentReceiptInfoRes ReadCurrentReceiptInfo() throws Exception {
             return CreateRes(Do("ReadCurrentReceiptInfo"), CurrentReceiptInfoRes.class);
     }

    /**
     *Read whole Electronic Journal report from beginning to the end.
     * @param  optionReadEJStorage
     *         2 symbols for destination: 
     *  - 'J0' - Reading to PC 
     *  - 'JY' - Reading to PC for JSON
     * @throws Exception
     */
     public void ReadEJ(OptionReadEJStorage optionReadEJStorage) throws Exception {
             Do("ReadEJ", "OptionReadEJStorage", optionReadEJStorage);
     }

    /**
     *The device scan out the list of available WiFi networks.
     * @throws Exception
     */
     public void ScanWiFiNetworks() throws Exception {
             Do("ScanWiFiNetworks");
     }

    /**
     *Program device's network IP address, subnet mask, gateway address, DNS address. To apply use -SaveNetworkSettings()
     * @param  optionAddressType
     *         1 symbol with value: 
     *  - '2' - IP address 
     *  - '3' - Subnet Mask 
     *  - '4' - Gateway address 
     *  - '5' - DNS address
     * @param  deviceAddress
     *         15 symbols for the selected address
     * @throws Exception
     */
     public void SetDeviceTCP_Addresses(OptionAddressType optionAddressType, String deviceAddress) throws Exception {
             Do("SetDeviceTCP_Addresses", "OptionAddressType", optionAddressType, "DeviceAddress", deviceAddress);
     }

    /**
     *Provides detailed 6-byte information about the current status of the CU.
     * @return StatusRes
     * @throws Exception
     */
     public StatusRes ReadStatus() throws Exception {
             return CreateRes(Do("ReadStatus"), StatusRes.class);
     }

    /**
     *Opens a fiscal receipt assigned to the specified operator number and operator password, parameters for receipt format and VAT type.
     * @param  optionReceiptFormat
     *         1 symbol with value: 
     *  - '1' - Detailed 
     *  - '0' - Brief
     * @param  traderSystemInvNum
     *         15 symbols for trader system invoice number
     * @throws Exception
     */
     public void OpenReceipt(OptionReceiptFormat optionReceiptFormat, String traderSystemInvNum) throws Exception {
             Do("OpenReceipt", "OptionReceiptFormat", optionReceiptFormat, "TraderSystemInvNum", traderSystemInvNum);
     }

    /**
     *Sets the date and time and current values.
     * @param  dateTime
     *         Date Time parameter in format: DD-MM-YY HH:MM
     * @throws Exception
     */
     public void SetDateTime(Date dateTime) throws Exception {
             Do("SetDateTime", "DateTime", dateTime);
     }

    /**
     *Register the sell (for correction use minus sign in the price field) of article with specified name, price, quantity, VAT class and/or discount/addition on the transaction.
     * @param  namePLU
     *         36 symbols for article's name
     * @param  optionVATClass
     *         1 symbol for article's VAT class with optional values:" 
     *  - 'A' - VAT Class A 
     *  - 'B' - VAT Class B 
     *  - 'C' - VAT Class C 
     *  - 'D' - VAT Class D 
     *  - 'E' - VAT Class E
     * @param  price
     *         Up to 13 symbols for article's price
     * @param  measureUnit
     *         3 symbols for measure unit
     * @param  hSCode
     *         10 symbols for HS Code in format XXXX.XX.XX
     * @param  hSName
     *         20 symbols for HS Name
     * @param  vATGrRate
     *         Up to 5 symbols for programmable VAT rate
     * @param  quantity
     *         1 to 10 symbols for quantity
     * @param  discAddP
     *         1 to 7 for percentage of discount/addition
     * @throws Exception
     */
     public void SellPLUfromExtDB(String namePLU, OptionVATClass optionVATClass, Double price, String measureUnit, String hSCode, String hSName, Double vATGrRate, Double quantity, Double discAddP) throws Exception {
             Do("SellPLUfromExtDB", "NamePLU", namePLU, "OptionVATClass", optionVATClass, "Price", price, "MeasureUnit", measureUnit, "HSCode", hSCode, "HSName", hSName, "VATGrRate", vATGrRate, "Quantity", quantity, "DiscAddP", discAddP);
     }

    /**
     *Start GPRS test on the device the result
     * @throws Exception
     */
     public void StartGPRStest() throws Exception {
             Do("StartGPRStest");
     }

    /**
     *Opens a fiscal invoice receipt assigned to the specified operator number and operator password with free info for customer data. The Invoice receipt can be issued only if the invoice range (start and end numbers) is set.
     * @param  companyName
     *         30 symbols for Invoice company name
     * @param  clientPINnum
     *         14 symbols for client PIN number
     * @param  headQuarters
     *         30 symbols for customer headquarters
     * @param  address
     *         30 symbols for Address
     * @param  postalCodeAndCity
     *         30 symbols for postal code and city
     * @param  exemptionNum
     *         30 symbols for exemption number
     * @param  traderSystemInvNum
     *         15 symbols for trader system invoice number
     * @throws Exception
     */
     public void OpenInvoiceWithFreeCustomerData(String companyName, String clientPINnum, String headQuarters, String address, String postalCodeAndCity, String exemptionNum, String traderSystemInvNum) throws Exception {
             Do("OpenInvoiceWithFreeCustomerData", "CompanyName", companyName, "ClientPINnum", clientPINnum, "HeadQuarters", headQuarters, "Address", address, "PostalCodeAndCity", postalCodeAndCity, "ExemptionNum", exemptionNum, "TraderSystemInvNum", traderSystemInvNum);
     }

    /**
     *Program device's GPRS password. To apply use - SaveNetworkSettings()
     * @param  passLength
     *         Up to 3 symbols for the GPRS password len
     * @param  password
     *         Up to 100 symbols for the device's GPRS password
     * @throws Exception
     */
     public void SetGPRS_Password(Double passLength, String password) throws Exception {
             Do("SetGPRS_Password", "PassLength", passLength, "Password", password);
     }

    /**
     *Restore default parameters of the device.
     * @param  password
     *         6-symbols string
     * @throws Exception
     */
     public void SoftwareReset(String password) throws Exception {
             Do("SoftwareReset", "Password", password);
     }

    /**
     *Provides information for the daily fiscal report  with zeroing and fiscal memory record, preceded by Electronic Journal report.
     * @throws Exception
     */
     public void DailyReport() throws Exception {
             Do("DailyReport");
     }

    /**
     *Provides information about device's TCP password.
     * @return TCP_PasswordRes
     * @throws Exception
     */
     public TCP_PasswordRes ReadTCP_Password() throws Exception {
             return CreateRes(Do("ReadTCP_Password"), TCP_PasswordRes.class);
     }

    /**
     *Stores a block containing the values of the VAT rates into the CU
     * @param  password
     *         6-symbols string
     * @param  vATrateA
     *         Value of VAT rate A from 2 to 6 symbols with format ##.##
     * @param  vATrateB
     *         Value of VAT rate B from 2 to 6 symbols with format ##.##
     * @param  vATrateC
     *         Value of VAT rate C from 2 to 6 symbols with format ##.##
     * @param  vATrateD
     *         Value of VAT rate D from 2 to 6 symbols with format ##.##
     * @param  vATrateE
     *         Value of VAT rate E from 2 to 6 symbols with format ##.##
     * @throws Exception
     */
     public void ProgVATrates(String password, Double vATrateA, Double vATrateB, Double vATrateC, Double vATrateD, Double vATrateE) throws Exception {
             Do("ProgVATrates", "Password", password, "VATrateA", vATrateA, "VATrateB", vATrateB, "VATrateC", vATrateC, "VATrateD", vATrateD, "VATrateE", vATrateE);
     }

    /**
     *Program device's NTP address . To apply use - SaveNetworkSettings()
     * @param  addressLen
     *         Up to 3 symbols for the address length
     * @param  nTPAddress
     *         50 symbols for the device's NTP address
     * @throws Exception
     */
     public void SetDeviceNTP_Address(Double addressLen, String nTPAddress) throws Exception {
             Do("SetDeviceNTP_Address", "AddressLen", addressLen, "NTPAddress", nTPAddress);
     }

    /**
     *Read Electronic Journal Report initial date to report end date.
     * @param  optionReadEJStorage
     *         2 symbols for destination: 
     *  - 'J0' - Reading to PC 
     *  - 'JY' - Reading to PC for JSON
     * @param  startRepFromDate
     *         6 symbols for initial date in the DDMMYY format
     * @param  endRepFromDate
     *         6 symbols for final date in the DDMMYY format
     * @throws Exception
     */
     public void ReadEJByDate(OptionReadEJStorage optionReadEJStorage, Date startRepFromDate, Date endRepFromDate) throws Exception {
             Do("ReadEJByDate", "OptionReadEJStorage", optionReadEJStorage, "StartRepFromDate", startRepFromDate, "EndRepFromDate", endRepFromDate);
     }

    /**
     *Informs about the issued document
     * @throws Exception
     */
     public void InfoLastReceiptDuplicate() throws Exception {
             Do("InfoLastReceiptDuplicate");
     }

    /**
     *Program device's idle timeout setting. Set timeout for closing the connection if there is an inactivity. Maximal value - 7200, minimal value 1. 0 is for never close the connection. This option can be used only if the device has LAN or WiFi. To apply use - SaveNetworkSettings()
     * @param  idleTimeout
     *         4 symbols for Idle timeout in format ####
     * @throws Exception
     */
     public void SetIdle_Timeout(Double idleTimeout) throws Exception {
             Do("SetIdle_Timeout", "IdleTimeout", idleTimeout);
     }

    /**
     *Provides information about if the TCP connection autostart when the device enter in Line/Sale mode.
     * @return OptionTCPAutoStart
     * @throws Exception
     */
     public OptionTCPAutoStart ReadTCP_AutoStartStatus() throws Exception {
             return CreateRes(Do("ReadTCP_AutoStartStatus"), OptionTCPAutoStart.class);
     }

    /**
     *Provides information about device's GPRS password.
     * @return GPRS_PasswordRes
     * @throws Exception
     */
     public GPRS_PasswordRes ReadGPRS_Password() throws Exception {
             return CreateRes(Do("ReadGPRS_Password"), GPRS_PasswordRes.class);
     }

    /**
     *Read device communication usage with server
     * @return OptionModule
     * @throws Exception
     */
     public OptionModule ReadServer_UsedComModule() throws Exception {
             return CreateRes(Do("ReadServer_UsedComModule"), OptionModule.class);
     }

    /**
     *Reads specific message number
     * @param  messageNum
     *         2 symbols for total number of messages
     * @return SpecificMessageRes
     * @throws Exception
     */
     public SpecificMessageRes ReadSpecificMessage(String messageNum) throws Exception {
             return CreateRes(Do("ReadSpecificMessage", "MessageNum", messageNum), SpecificMessageRes.class);
     }

    /**
     *Programs server HTTPS address.
     * @param  paramLength
     *         Up to 3 symbols for parameter length
     * @param  address
     *         50 symbols for address
     * @throws Exception
     */
     public void SetHTTPS_Address(Double paramLength, String address) throws Exception {
             Do("SetHTTPS_Address", "ParamLength", paramLength, "Address", address);
     }

    /**
     *Calculate the subtotal amount with printing and display visualization options. Provide information about values of the calculated amounts. If a percent or value discount/addition has been specified the subtotal and the discount/addition value will be printed regardless the parameter for printing.
     * @param  optionDisplay
     *         1 symbol with value: 
     *  - '1' - Yes 
     *  - '0' - No
     * @param  discAddV
     *         Up to 13 symbols for the value of the 
     * discount/addition. Use minus sign '-' for discount
     * @param  discAddP
     *         Up to 7 symbols for the percentage value of the 
     * discount/addition. Use minus sign '-' for discount
     * @return Double
     * @throws Exception
     */
     public Double Subtotal(OptionDisplay optionDisplay, Double discAddV, Double discAddP) throws Exception {
             return CreateRes(Do("Subtotal", "OptionDisplay", optionDisplay, "DiscAddV", discAddV, "DiscAddP", discAddP), Double.class);
     }

    /**
     *Erase HS codes.
     * @param  password
     *         6 symbols for password
     * @throws Exception
     */
     public void EraseHScodes(String password) throws Exception {
             Do("EraseHScodes", "Password", password);
     }

    /**
     *Stores PIN number in operative memory.
     * @param  password
     *         6-symbols string
     * @param  pINnum
     *         11 symbols for PIN registration number
     * @throws Exception
     */
     public void SetPINnumber(String password, String pINnum) throws Exception {
             Do("SetPINnumber", "Password", password, "PINnum", pINnum);
     }

    /**
     *Read/Store Invoice receipt copy to External USB Flash memory, External SD card.
     * @param  optionInvoiceCopy
     *         2 symbols for destination: 
     *  - 'J0' - Reading  
     *  - 'J2' - Storage in External USB Flash memory. 
     *  - 'J4' - Storage in External SD card memory
     * @param  cUInvoiceNum
     *         10 symbols for Invoice receipt Number.
     * @throws Exception
     */
     public void ReadOrStoreInvoiceCopy(OptionInvoiceCopy optionInvoiceCopy, String cUInvoiceNum) throws Exception {
             Do("ReadOrStoreInvoiceCopy", "OptionInvoiceCopy", optionInvoiceCopy, "CUInvoiceNum", cUInvoiceNum);
     }

    /**
     *Provides information about the accumulated EOD turnover and VAT
     * @return EODAmountsRes
     * @throws Exception
     */
     public EODAmountsRes ReadEODAmounts() throws Exception {
             return CreateRes(Do("ReadEODAmounts"), EODAmountsRes.class);
     }

    /**
     *Start LAN test on the device the result
     * @throws Exception
     */
     public void StartLANtest() throws Exception {
             Do("StartLANtest");
     }

    /**
     *FlagsModule is a char with bits representing modules supported by the device.
     * @return DeviceModuleSupportRes
     * @throws Exception
     */
     public DeviceModuleSupportRes ReadDeviceModuleSupport() throws Exception {
             return CreateRes(Do("ReadDeviceModuleSupport"), DeviceModuleSupportRes.class);
     }

    /**
     *Provides information about WiFi network name where the device is connected.
     * @return WiFi_NetworkNameRes
     * @throws Exception
     */
     public WiFi_NetworkNameRes ReadWiFi_NetworkName() throws Exception {
             return CreateRes(Do("ReadWiFi_NetworkName"), WiFi_NetworkNameRes.class);
     }

    /**
     *Provides information about the device version.
     * @return String
     * @throws Exception
     */
     public String ReadVersion() throws Exception {
             return CreateRes(Do("ReadVersion"), String.class);
     }

    /**
     * Writes raw bytes to FP 
     * @param  bytes
     *         The bytes in BASE64 ecoded string to be written to FP
     * @throws Exception
     */
     public void RawWrite(byte[] bytes) throws Exception {
             Do("RawWrite", "Bytes", bytes);
     }

    /**
     *Programs HS code at a given position (HS number in order).
     * @param  hS_Number
     *         4 symbols for HS number in order in format ####
     * @param  hS_Code
     *         10 symbols for HS code
     * @param  hS_Name
     *         20 symbols for name of HS group
     * @param  optionTaxable
     *         1 symbol for parameter: 
     * - '1' - Exempted 
     * - '0' - Taxable
     * @param  mesureUnit
     *         3 symbols for mesure unit of item's code
     * @param  vAT_Rate
     *         Value of VAT rate from 2 to 5 symbols with format ##.##
     * @throws Exception
     */
     public void ProgHScode(Double hS_Number, String hS_Code, String hS_Name, OptionTaxable optionTaxable, String mesureUnit, Double vAT_Rate) throws Exception {
             Do("ProgHScode", "HS_Number", hS_Number, "HS_Code", hS_Code, "HS_Name", hS_Name, "OptionTaxable", optionTaxable, "MesureUnit", mesureUnit, "VAT_Rate", vAT_Rate);
     }

    /**
     *Programs time threshold minutes
     * @param  value
     *         Up to 5 symbols for value
     * @throws Exception
     */
     public void SetTime_ThresholdMinutes(Double value) throws Exception {
             Do("SetTime_ThresholdMinutes", "Value", value);
     }

     public void ApplyClientLibraryDefinitions() throws Exception {
         String defsPart0 = "<Defs><ServerStartupSettings><Encoding CodePage=\"1252\" EncodingName=\"Western European (Windows)\" /><GenerationTimeStamp>2206201550</GenerationTimeStamp><SignalFD>0</SignalFD><SilentFindDevice>0</SilentFindDevice><EM>0</EM></ServerStartupSettings><Command Name=\"StartWiFiTest\" CmdByte=\"0x4E\"><FPOperation>Start WiFi test on the device the result</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'W'><;><'T'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadCUnumbers\" CmdByte=\"0x60\"><FPOperation>Provides information about the manufacturing number of the CU and PIN number.</FPOperation><Response ACK=\"false\"><Res Name=\"SerialNumber\" Value=\"\" Type=\"Text\" MaxLen=\"20\"><Desc>20 symbols for individual number of the CU</Desc></Res><Res Name=\"PINnumber\" Value=\"\" Type=\"Text\" MaxLen=\"11\"><Desc>11 symbols for pin number</Desc></Res><ResFormatRaw><![CDATA[<SerialNumber[20]><;><PINnumber[11]>]]></ResFormatRaw></Response></Command><Command Name=\"StoreEJByDate\" CmdByte=\"0x7C\"><FPOperation>Store Electronic Journal Report from report from date to date to External USB Flash memory, External SD card.</FPOperation><Args><Arg Name=\"OptionReportStorage\" Value=\"\" Type=\"Option\" MaxLen=\"2\"><Options><Option Name=\"Storage in External SD card memory\" Value=\"J4\" /><Option Name=\"Storage in External SD card memory for JSON\" Value=\"JX\" /><Option Name=\"Storage in External USB Flash memory\" Value=\"J2\" /><Option Name=\"Storage in External USB Flash memory for JSON\" Value=\"Jx\" /></Options><Desc>2 symbols for destination:  - 'J2' - Storage in External USB Flash memory  - 'J4' - Storage in External SD card memory  - 'Jx' - Storage in External USB Flash memory for JSON  - 'JX' - Storage in External SD card memory for JSON</Desc></Arg><Arg Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"StartRepFromDate\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"ddMMyy\"><Desc>6 symbols for initial date in the DDMMYY format</Desc></Arg><Arg Name=\"EndRepFromDate\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"ddMMyy\"><Desc>6 symbols for final date in the DDMMYY format</Desc></Arg><ArgsFormatRaw><![CDATA[<OptionReportStorage[2]> <;> <'D'> <;> <StartRepFromDate \"DDMMYY\"> <;> <EndRepFromDate \"DDMMYY\"> ]]></ArgsFormatRaw></Args></Command><Command Name=\"DisplayTextLines1and2\" CmdByte=\"0x27\"><FPOperation>Shows a 16-symbols text in the first line and last 16-symbols text in the second line of the external display lines.</FPOperation><Args><Arg Name=\"Text\" Value=\"\" Type=\"Text\" MaxLen=\"32\"><Desc>32 symbols text</Desc></Arg><ArgsFormatRaw><![CDATA[ <Text[32]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadDateTime\" CmdByte=\"0x68\"><FPOperation>Provides information about the current date and time.</FPOperation><Response ACK=\"false\"><Res Name=\"DateTime\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"dd-MM-yyyy HH:mm\"><Desc>Date Time parameter in format: DD-MM-YY [Space] hh:mm</Desc></Res><ResFormatRaw><![CDATA[<DateTime \"DD-MM-YYYY HH:MM\">]]></ResFormatRaw></Response></Command><Command Name=\"CancelReceipt\" CmdByte=\"0x39\"><FPOperation>Available only if the receipt is not closed. Cancel all sales in the receipt and close it .</FPOperation></Command><Command Name=\"Read_IdleTimeout\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's idle timeout. This timeout is seconds in which the connection will be closed when there is an inactivity. This information is available if the device has LAN or WiFi. Maximal value - 7200, minimal value 1. 0 is for never close the connection.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'Z'><;><'I'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"IdleTimeout\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"4\" Format=\"0000\"><Desc>4 symbols for password in format ####</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'Z'><;><'I'><;><IdleTimeout[4]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadInvoice_Threshold\" CmdByte=\"0x4E\"><FPOperation>Read invoice threshold count</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'S'><;><'I'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"Value\" Value=\"\" Type=\"Decimal\" MaxLen=\"5\"><Desc>Up to 5 symbols for value</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'S'><;><'I'><;><Value[1..5]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadTimeThreshold_Minutes\" CmdByte=\"0x4E\"><FPOperation>Read time threshold minutes</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'S'><;><'T'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"Value\" Value=\"\" Type=\"Decimal\" MaxLen=\"5\"><Desc>Up to 5 symbols for value</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'S'><;><'T'><;><Value[1..5]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadHTTPS_Server\" CmdByte=\"0x4E\"><FPOperation>Providing information about server HTTPS address.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"H\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'S'><;><'H'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"C\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"ParamLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for parameter length</Desc></Res><Res Name=\"Address\" Value=\"\" Type=\"Text\" MaxLen=\"50\"><Desc>50 symbols for address</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'S'><;><'C'><;><ParamLength[1..3]><;><Address[50]>]]></ResFormatRaw></Response></Command><Command Name=\"SetDeviceTCP_MACAddress\" CmdByte=\"0x4E\"><FPOperation>Program device's MAC address . To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"6\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"MACAddress\" Value=\"\" Type=\"Text\" MaxLen=\"12\"><Desc>12 symbols for the MAC address</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'T'><;><'6'> <;><MACAddress[12]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SellPLUfromExtDB_HS\" CmdByte=\"0x31\"><FPOperation>Register the sell (for correction use minus sign in the price field) of article with specified name, price, quantity, VAT class and/or discount/addition on the transaction.</FPOperation><Args><Arg Name=\"NamePLU\" Value=\"\" Type=\"Text\" MaxLen=\"36\"><Desc>36 symbols for article's name</Desc></Arg><Arg Name=\"reservde\" Value=\" \" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"Price\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for article's price</Desc></Arg><Arg Name=\"reserved\" Value=\"   \" Type=\"OptionHardcoded\" MaxLen=\"3\" /><Arg Name=\"HSCode\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for HS Code in format XXXX.XX.XX</Desc></Arg><Arg Name=\"reserved\" Value=\"                    \" Type=\"OptionHardcoded\" MaxLen=\"20\" /><Arg Name=\"reserved\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"Quantity\" Value=\"\" Type=\"Decimal\" MaxLen=\"10\"><Desc>1 to 10 symbols for quantity</Desc><Meta MinLen=\"1\" Compulsory=\"false\" ValIndicatingPresence=\"*\" /></Arg><Arg Name=\"DiscAddP\" Value=\"\" Type=\"Decimal\" MaxLen=\"7\"><Desc>1 to 7 for percentage of discount/addition</Desc><Meta MinLen=\"1\" Compulsory=\"false\" ValIndicatingPresence=\",\" /></Arg><ArgsFormatRaw><![CDATA[ <NamePLU[36]> <;> <reservde[' ']> <;> <Price[1..13]> <;> <reserved['   ']> <;><HSCode[10]> <;> <reserved['                    ']> <;> <reserved['0']>  {<'*'> <Quantity[1..10]>} {<','> <DiscAddP[1..7]>}  ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadGPRS_APN\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's GRPS APN.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"A\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'G'><;><'A'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"A\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"gprsAPNlength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the APN length</Desc></Res><Res Name=\"APN\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>(APN) Up to 100 symbols for the device's GPRS APN</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'G'><;><'A'><;><gprsAPNlength[1..3]><;><APN[100]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadGPRS_Username\" CmdByte=\"0x4E\"><FPOperation>Providing information about device's GPRS user name.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"U\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'G'><;><'U'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"U\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"gprsUserNameLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the GPRS username length</Desc></Res><Res Name=\"Username\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's GPRS username</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'G'><;><'U'><;><gprsUserNameLength[1..3]><;><Username[100]>]]></ResFormatRaw></Response></Command><Command Name=\"SaveNetworkSettings\" CmdByte=\"0x4E\"><FPOperation>After every change on Idle timeout, LAN/WiFi/GPRS usage, LAN/WiFi/TCP/GPRS password or TCP auto start networks settings this Save command needs to be execute.</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"A\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'P'><;><'A'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadDeviceTCP_Addresses\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's network IP address, subnet mask, gateway address, DNS address.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionAddressType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"DNS address\" Value=\"5\" /><Option Name=\"Gateway address\" Value=\"4\" /><Option Name=\"IP address\" Value=\"2\" /><Option Name=\"Subnet Mask\" Value=\"3\" /></Options><Desc>1 symbol with value:  - '2' - IP address  - '3' - Subnet Mask  - '4' - Gateway address  - '5' - DNS address</Desc></Arg><ArgsFormatRaw><![CDATA[ <'R'><;><'T'><;><AddressType[1]> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionAddressType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"DNS address\" Value=\"5\" /><Option Name=\"Gateway address\" Value=\"4\" /><Option Name=\"IP address\" Value=\"2\" /><Option Name=\"Subnet Mask\" Value=\"3\" /></Options><Desc>(Address type) 1 symbol with value:  - '2' - IP address  - '3' - Subnet Mask  - '4' - Gateway address  - '5' - DNS address</Desc></Res><Res Name=\"DeviceAddress\" Value=\"\" Type=\"Text\" MaxLen=\"15\"><Desc>15 symbols for the device's addresses</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'T'><;>< AddressType[1]><;><DeviceAddress[15]>]]></ResFormatRaw></Response></Command><Command Name=\"DirectCommand\" CmdByte=\"0xF1\"><FPOperation>Executes the direct command .</FPOperation><Args><Arg Name=\"Input\" Value=\"\" Type=\"Text\" MaxLen=\"200\"><Desc>Raw request to FP</Desc></Arg></Args><Response ACK=\"false\"><Res Name=\"Output\" Value=\"\" Type=\"Text\" MaxLen=\"200\"><Desc>FP raw response</Desc></Res></Response></Command><Command Name=\"SetWiFi_NetworkName\" CmdByte=\"0x4E\"><FPOperation>Program device's TCP WiFi network name where it will be connected. To apply use -SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"WiFiNameLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the WiFi network name len</Desc></Arg><Arg Name=\"WiFiNetworkName\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's WiFi ssid network name</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'W'><;><'N'><;><WiFiNameLength[1..3]><;><WiFiNetworkName[100]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadHScode\" CmdByte=\"0x4F\"><FPOperation>Programs HS code at a given position (HS number in order).</FPOperation><Args><Arg Name=\"Option\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionR\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"HS_Number\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"4\" Format=\"0000\"><Desc>4 symbols for HS number in order in format ####</Desc></Arg><ArgsFormatRaw><![CDATA[ <Option['Z']> <;><OptionR['R']><;><HS_Number[4]>  ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"Option\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionR\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"HS_Number\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"4\" Format=\"0000\"><Desc>4 symbols for HS number in order in format ####</Desc></Res><Res Name=\"HS_Code\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for HS code</Desc></Res><Res Name=\"HS_Name\" Value=\"\" Type=\"Text\" MaxLen=\"20\"><Desc>20 symbols for name of HS group</Desc></Res><Res Name=\"OptionTaxable\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Exempted\" Value=\"1\" /><Option Name=\"Taxable\" Value=\"0\" /></Options><Desc>1 symbol for parameter: - '1' - Exempted - '0' - Taxable</Desc></Res><Res Name=\"MesureUnit\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for mesure unit of item's code</Desc></Res><Res Name=\"VAT_Rate\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>(VAT rate) Value of VAT rate from 2 to 5 symbols with format ##.##</Desc></Res><ResFormatRaw><![CDATA[<Option['Z']> <;><OptionR['R']><;><HS_Number[4]> <;> <HS_Code[10]> <;> <HS_Name[20]> <;><OptionTaxable[1]> <;> <MesureUnit[3]> <;> < VAT_Rate[2..6]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadDiagnostics\" CmdByte=\"0x22\"><FPOperation>Provides information about documents sending functions .</FPOperation><Response ACK=\"false\"><Res Name=\"OptionDeviceType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"A Type\" Value=\"1\" /><Option Name=\"B Type\" Value=\"2\" /></Options><Desc>1 symbol for device type:  - '1' - A Type  - '2' - B Type</Desc></Res><Res Name=\"SDIdxPos\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for current SD index position of last sent receipt</Desc></Res><Res Name=\"LastInvoiceCUNum\" Value=\"\" Type=\"Text\" MaxLen=\"19\"><Desc>19 symbols for number of last invoice according the CU</Desc></Res><Res Name=\"LastInvoiceDate\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6 symbols for last invoice date in the DDMMYY format</Desc></Res><Res Name=\"LastEODDate\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6 symbols for last sent EOD in the DDMMYY format</Desc></Res><Res Name=\"InvoicesSent\" Value=\"\" Type=\"Text\" MaxLen=\"4\"><Desc>4 symbold for number of invoices sent for the current day</Desc></Res><ResFormatRaw><![CDATA[<DeviceType[1]> <;> <SDIdxPos[10]> <;> <LastInvoiceCUNum[19]> <;> <LastInvoiceDate[6]> <;> <LastEODDate[6]> <;> <InvoicesSent[4]>]]></ResFormatRaw></Response></Command><Command Name=\"DisplayTextLine1\" CmdByte=\"0x25\"><FPOperation>Shows a 20-symbols text in the upper external display line.</FPOperation><Args><Arg Name=\"Text\" Value=\"\" Type=\"Text\" MaxLen=\"16\"><Desc>16 symbols text</Desc></Arg><ArgsFormatRaw><![CDATA[ <Text[16]>  ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadVATrates\" CmdByte=\"0x62\"><FPOperation>Provides information about the current VAT rates (the last value stored in FM).</FPOperation><Response ACK=\"false\"><Res Name=\"VATrateA\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"7\" Format=\"00.00%\"><Desc>(VAT rate A) Up to 7 symbols for VATrates of VAT class A in format ##.##%</Desc></Res><Res Name=\"VATrateB\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"7\" Format=\"00.00%\"><Desc>(VAT rate B) Up to 7 symbols for VATrates of VAT class B in format ##.##%</Desc></Res><Res Name=\"VATrateC\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"7\" Format=\"00.00%\"><Desc>(VAT rate C) Up to 7 symbols for VATrates of VAT class C in format ##.##%</Desc></Res><Res Name=\"VATrateD\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"7\" Format=\"00.00%\"><Desc>(VAT rate D) Up to 7 symbols for VATrates of VAT class D in format ##.##%</Desc></Res><Res Name=\"VATrateE\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"7\" Format=\"00.00%\"><Desc>(VAT rate E) Up to 7 symbols for VATrates of VAT class E in format ##.##%</Desc></Res><ResFormatRaw><![CDATA[<VATrateA[1..7]> <;> <VATrateB[1..7]> <;> <VATrateC[1..7]> <;> <VATrateD[1..7]> <;> <VATrateE[1..7]>]]></ResFormatRaw></Response></Command><Command Name=\"OpenDebitNoteWithFreeCustomerData\" CmdByte=\"0x30\"><FPOperation>Opens a fiscal invoice debit note receipt assigned to the specified operator number and operator password with free info for customer data. The Invoice receipt can be issued only if the invoice range (start and end numbers) is set.</FPOperation><Args><Arg Name=\"reserved\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"reserved\" Value=\"     0\" Type=\"OptionHardcoded\" MaxLen=\"6\" /><Arg Name=\"reserved\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"InvoiceDebitNoteType\" Value=\"@\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"CompanyName\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for Invoice company name</Desc></Arg><Arg Name=\"ClientPINnum\" Value=\"\" Type=\"Text\" MaxLen=\"14\"><Desc>14 symbols for client PIN number</Desc></Arg><Arg Name=\"HeadQuarters\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for customer headquarters</Desc></Arg><Arg Name=\"Address\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for Address</Desc></Arg><Arg Name=\"PostalCodeAndCity\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for postal code and city</Desc></Arg><Arg Name=\"ExemptionNum\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for exemption number</Desc></Arg><Arg Name=\"RelatedInvoiceNum\" Value=\"\" Type=\"Text\" MaxLen=\"19\"><Desc>19 symbols for the related invoice number in format ###################</Desc></Arg><Arg Name=\"TraderSystemInvNum\" Value=\"\" Type=\"Text\" MaxLen=\"15\"><Desc>15 symbols for trader system invoice number</Desc></Arg><ArgsFormatRaw><![CDATA[ <reserved['1']> <;> <reserved['     0']> <;> <reserved['0']> <;> <InvoiceDebitNoteType['@']> <;> <CompanyName[30]> <;> <ClientPINnum[14]> <;> <HeadQuarters[30]> <;> <Address[30]> <;> <PostalCodeAndCity[30]> <;> <ExemptionNum[30]> <;> <RelatedInvoiceNum[19]><;><TraderSystemInvNum[15]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadHScodeNumber\" CmdByte=\"0x4F\"><FPOperation>Read the number of HS codes.</FPOperation><Args><Arg Name=\"Option\" Value=\"z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionR\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <Option['z']> <;><OptionR['R']>  ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"Option\" Value=\"z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionR\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"HScodesNumber\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"4\" Format=\"0000\"><Desc>4 symbols for HS codes number in format ####</Desc></Res><ResFormatRaw><![CDATA[<Option['z']> <;><OptionR['R']><;>< HScodesNumber [4]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadLastAndTotalReceiptNum\" CmdByte=\"0x71\"><FPOperation>Provides information about the number of the last issued receipt.</FPOperation><Response ACK=\"false\"><Res Name=\"LastCUInvoiceNum\" Value=\"\" Type=\"Text\" MaxLen=\"19\"><Desc>19 symbols for the last number of invoice according the middleware, CU, internal invoice counter</Desc></Res><Res Name=\"LastReceiptNum\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"7\" Format=\"0000000\"><Desc>7 symbols for last receipt number in format #######</Desc></Res><ResFormatRaw><![CDATA[<LastCUInvoiceNum[19]> <;> <LastReceiptNum[7]>]]></ResFormatRaw></Response></Command><Command Name=\"ClearDisplay\" CmdByte=\"0x24\"><FPOperation>Clears the external display.</FPOperation></Command><Command Name=\"SetDHCP_Enabled\" CmdByte=\"0x4E\"><FPOperation>Program device's TCP network DHCP enabled or disabled. To apply use -SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionDHCPEnabled\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Disabled\" Value=\"0\" /><Option Name=\"Enabled\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '0' - Disabled  - '1' - Enabled</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'T'><;><'1'><;><DHCPEnabled[1]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetGPRS_APN\" CmdByte=\"0x4E\"><FPOperation>Program device's GPRS APN. To apply use -SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"A\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"gprsAPNlength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the APN len</Desc></Arg><Arg Name=\"APN\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's GPRS APN</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'G'><;><'A'><;><gprsAPNlength[1..3]><;><APN[100]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadGPRS_AuthenticationType\" CmdByte=\"0x4E\"><FPOperation>Read GPRS APN authentication type</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'G'><;><'N'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionAuthenticationType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"CHAP\" Value=\"2\" /><Option Name=\"None\" Value=\"0\" /><Option Name=\"PAP\" Value=\"1\" /><Option Name=\"PAP or CHAP\" Value=\"3\" /></Options><Desc>1 symbol with value: - '0' - None - '1' - PAP - '2' - CHAP - '3' - PAP or CHAP</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'G'><;><'N'><;><AuthenticationType[1]>]]></ResFormatRaw></Response></Command><Command Name=\"AddNewHScode\" CmdByte=\"0x4F\"><FPOperation>Programs the customer DB for special customer receipt issuing.</FPOperation><Args><Arg Name=\"Option\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionW\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"reserved\" Value=\"0000\" Type=\"OptionHardcoded\" MaxLen=\"4\" /><Arg Name=\"HS_Code\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for HS code</Desc></Arg><Arg Name=\"HS_Name\" Value=\"\" Type=\"Text\" MaxLen=\"20\"><Desc>20 symbols for name of HS group</Desc></Arg><Arg Name=\"OptionTaxable\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Exempted\" Value=\"1\" /><Option Name=\"Taxable\" Value=\"0\" /></Options><Desc>1 symbol for parameter: - '1' - Exempted - '0' - Taxable</Desc></Arg><Arg Name=\"MesureUnit\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for mesure unit of item's code</Desc></Arg><Arg Name=\"VAT_rate\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate from 2 to 5 symbols with format ##.##</Desc></Arg><ArgsFormatRaw><![CDATA[ <Option['Z']> <;>< OptionW['W']><;><reserved['0000']> <;> <HS_Code[10]> <;> <HS_Name[20]><;><OptionTaxable[1]><;><MesureUnit[3]><;><VAT_rate[2..6]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetSerialNum\" CmdByte=\"0x40\"><FPOperation>Stores the Manufacturing number into the operative memory.</FPOperation><Args><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6-symbols string</Desc></Arg><Arg Name=\"SerialNum\" Value=\"\" Type=\"Text\" MaxLen=\"20\"><Desc>20 symbols Manufacturing number</Desc></Arg><ArgsFormatRaw><![CDATA[ <Password[6]> <;> <SerialNum[20]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"RawRead\" CmdByte=\"0xFF\"><FPOperation> Reads raw bytes from FP.</FPOperation><Args><Arg Name=\"Count\" Value=\"\" Type=\"Decimal\" MaxLen=\"5\"><Desc>How many bytes to read if EndChar is not specified</Desc></Arg><Arg Name=\"EndChar\" Value=\"\" Type=\"Text\" MaxLen=\"1\"><Desc>The character marking the end of the data. If present Count parameter is ignored.</Desc></Arg></Args><Response ACK=\"false\"><Res Name=\"Bytes\" Value=\"\" Type=\"Base64\" MaxLen=\"100000\"><Desc>FP raw response in BASE64 encoded string</Desc></Res></Response></Command><Command Name=\"ScanAndPrintWifiNetworks\" CmdByte=\"0x4E\"><FPOperation>Scan and print available wifi networks</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'W'><;><'S'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadDHCP_Status\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's DHCP status</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'T'><;><'1'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionDHCPEnabled\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Disabled\" Value=\"0\" /><Option Name=\"Enabled\" Value=\"1\" /></Options><Desc>(Status) 1 symbols for device's DHCP status - '0' - Disabled  - '1' - Enabled</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'T'><;><'1'><;><DHCPEnabled[1]>]]></ResFormatRaw></Response></Command><Command Name=\"DisplayDateTime\" CmdByte=\"0x28\"><FPOperation>Shows the current date and time on the external display.</FPOperation></Command><Command Name=\"SetTCP_AutoStart\" CmdByte=\"0x4E\"><FPOperation>Program device's autostart TCP conection in sale/line mode. To apply use -SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"2\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionTCPAutoStart\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '0' - No  - '1' - Yes</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'Z'><;><'2'><;><TCPAutoStart[1]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetTCP_ActiveModule\" CmdByte=\"0x4E\"><FPOperation>Selects the active communication module - LAN or WiFi. This option can be set only if the device has both modules at the same time. To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"U\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionUsedModule\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"LAN module\" Value=\"1\" /><Option Name=\"WiFi module\" Value=\"2\" /></Options><Desc>1 symbol with value:  - '1' - LAN module  - '2' - WiFi module</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'Z'><;><'U'><;><UsedModule[1]><;> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadTCP_UsedModule\" CmdByte=\"0x4E\"><FPOperation>Provides information about which module the device is in use: LAN or WiFi module. This information can be provided if the device has mounted both modules.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"U\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'Z'><;><'U'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"U\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionUsedModule\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"LAN module\" Value=\"1\" /><Option Name=\"WiFi module\" Value=\"2\" /></Options><Desc>1 symbol with value:  - '1' - LAN module  - '2' - WiFi module</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'Z'><;><'U'><;><UsedModule[1]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadTCP_MACAddress\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's MAC address.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"6\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'T'><;><'6' > ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"6\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"MACAddress\" Value=\"\" Type=\"Text\" MaxLen=\"12\"><Desc>12 symbols for the device's MAC address</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'T'><;><'6'><;><MACAddress[12]>]]></ResFormatRaw></Response></Command><Command Name=\"CloseReceipt\" CmdByte=\"0x38\"><FPOperation>Closes the opened fiscal receipt and returns receipt info.</FPOperation><Response ACK=\"false\"><Res Name=\"InvoiceNum\" Value=\"\" Type=\"Text\" MaxLen=\"19\"><Desc>19 symbols for CU invoice number</Desc></Res><Res Name=\"QRcode\" Value=\"\" Type=\"Text\" MaxLen=\"128\"><Desc>128 symbols for QR code</Desc></Res><ResFormatRaw><![CDATA[<InvoiceNum[19]<;><QRcode[128]>]]></ResFormatRaw></Response></Command><Command Name=\"OpenCreditNoteWithFreeCustomerData\" CmdByte=\"0x30\"><FPOperation>Opens a fiscal invoice credit note receipt assigned to the specified operator number and operator password with free info for customer data. The Invoice receipt can be issued only if the invoice range (start and end numbers) is set.</FPOperation><Args><Arg Name=\"reserved\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"reserved\" Value=\"     0\" Type=\"OptionHardcoded\" MaxLen=\"6\" /><Arg Name=\"reserved\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"InvoiceDebitNoteType\" Value=\"A\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"CompanyName\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for Invoice company name</Desc></Arg><Arg Name=\"ClientPINnum\" Value=\"\" Type=\"Text\" MaxLen=\"14\"><Desc>14 symbols for client PIN number</Desc></Arg><Arg Name=\"HeadQuarters\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for customer headquarters</Desc></Arg><Arg Name=\"Address\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for Address</Desc></Arg><Arg Name=\"PostalCodeAndCity\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for postal code and city</Desc></Arg><Arg Name=\"ExemptionNum\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for exemption number</Desc></Arg><Arg Name=\"RelatedInvoiceNum\" Value=\"\" Type=\"Text\" MaxLen=\"19\"><Desc>19 symbols for the related invoice number in format ###################</Desc></Arg><Arg Name=\"TraderSystemInvNum\" Value=\"\" Type=\"Text\" MaxLen=\"15\"><Desc>15 symbols for trader system invoice number</Desc></Arg><ArgsFormatRaw><![CDATA[ <reserved['1']> <;> <reserved['     0']> <;> <reserved['0']> <;> <InvoiceDebitNoteType['A']> <;> <CompanyName[30]> <;> <ClientPINnum[14]> <;> <HeadQuarters[30]> <;> <Address[30]> <;> <PostalCodeAndCity[30]> <;> <ExemptionNum[30]> <;> <RelatedInvoiceNum[19]><;><TraderSystemInvNum[15]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"DisplayTextLine2\" CmdByte=\"0x26\"><FPOperation>Shows a 16-symbols text in the lower external display line.</FPOperation><Args><Arg Name=\"Text\" Value=\"\" Type=\"Text\" MaxLen=\"16\"><Desc>16 symbols text</Desc></Arg><ArgsFormatRaw><![CDATA[ <Text[16]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetWiFi_Password\" CmdByte=\"0x4E\"><FPOperation>Program device's TCP WiFi password where it will be connected. To apply use -SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"PassLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the WiFi password len</Desc></Arg><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's WiFi password</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'W'><;><'P'><;><PassLength[1..3]><;><Password[100]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadInfoFromLastServerCommunication\" CmdByte=\"0x5A\"><FPOperation>Provide information from the last communication with the server.</FPOperation><Args><Arg Name=\"Option\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionServerResponse\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"At send EOD\" Value=\"Z\" /><Option Name=\"At send receipt\" Value=\"R\" /></Options><Desc>1 symbol with value - 'R' - At send receipt - 'Z' - At send EOD</Desc></Arg><Arg Name=\"OptionTransactionType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Error Code\" Value=\"c\" /><Option Name=\"Error Message\" Value=\"m\" /><Option Name=\"Exception Message\" Value=\"e\" /><Option Name=\"Status\" Value=\"s\" /></Options><Desc>1 symbol with value - 'c' - Error Code - 'm' - Error Message - 's' - Status - 'e' - Exception Message</Desc></Arg><ArgsFormatRaw><![CDATA[ <Option['S']><;> <ServerResponse[1]><;><TransactionType[1]> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"Option\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionServerResponse\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"At send EOD\" Value=\"Z\" /><Option Name=\"At send receipt\" Value=\"R\" /></Options><Desc>1 symbol with value - 'R' - At send receipt - 'Z' - At send EOD</Desc></Res><Res Name=\"OptionTransactionType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Error Code\" Value=\"c\" /><Option Name=\"Error Message\" Value=\"m\" /><Option Name=\"Exception Message\" Value=\"e\" /><Option Name=\"Status\" Value=\"s\" /></Options><Desc>1 symbol with value - 'c' - Error Code - 'm' - Error Message - 's' - Status - 'e' - Exception Message</Desc></Res><Res Name=\"Message\" Value=\"\" Type=\"Text\" MaxLen=\"200\"><Desc>Up to 200 symbols for the message from the server</Desc></Res><ResFormatRaw><![CDATA[<Option['S']><;> <ServerResponse[1]><;><TransactionType[1]><;><Message[200]>]]></ResFormatRaw></Response></Command><Command Name=\"ConfirmFiscalization\" CmdByte=\"0x41\"><FPOperation>Confirm PIN number.</FPOperation><Args><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6-symbols string</Desc></Arg><Arg Name=\"\" Value=\"2\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <Password[6]> <;> <'2'>  ]]></ArgsFormatRaw></Args></Command><Command Name=\"StoreEJ\" CmdByte=\"0x7C\"><FPOperation>Store whole Electronic Journal report to External USB Flash memory, External SD card.</FPOperation><Args><Arg Name=\"OptionReportStorage\" Value=\"\" Type=\"Option\" MaxLen=\"2\"><Options><Option Name=\"Storage in External SD card memory\" Value=\"J4\" /><Option Name=\"Storage in External SD card memory for JSON\" Value=\"JX\" /><Option Name=\"Storage in External USB Flash memory\" Value=\"J2\" /><Option Name=\"Storage in External USB Flash memory for JSON\" Value=\"Jx\" /></Options><Desc>2 symbols for destination:  - 'J2' - Storage in External USB Flash memory  - 'J4' - Storage in External SD card memory  - 'Jx' - Storage in External USB Flash memory for JSON  - 'JX' - Storage in External SD card memory for JSON</Desc></Arg><Arg Name=\"\" Value=\"*\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <OptionReportStorage[2]><;><'*'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadDeviceModuleSupportByFirmware\" CmdByte=\"0x4E\"><FPOperation>FlagsModule is a char with bits representing modules supported by the firmware</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'D'><;><'S'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionLAN\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for LAN suppor - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"OptionWiFi\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for WiFi support - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"OptionGPRS\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for GPRS support - '0' - No  - '1' - Yes BT (Bluetooth) 1 symbol for Bluetooth support - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"OptionBT\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>(Bluetooth) 1 symbol for Bluetooth support - '0' - No  - '1' - Yes</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'D'><;><'S'><;><LAN[1]><;><WiFi>[1]><;><GPRS>[1]><;><BT[1]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadWiFi_Password\" CmdByte=\"0x4E\"><FPOperation>Providing information about WiFi password where the device is connected.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'W'><;><'P'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"PassLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the WiFi password length</Desc></Res><Res Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's WiFi password</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'W'><;><'P'><;><PassLength[1..3]><;><Password[100]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadNTP_Address\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's NTP address.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'S'><;><'N' > ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"AddressLen\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the address length</Desc></Res><Res Name=\"NTPAddress\" Value=\"\" Type=\"Text\" MaxLen=\"50\"><Desc>(NTP Address)50 symbols for the device's NTP address</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'S'><;><'N'><;><AddressLen[1..3]><;><NTPAddress[50]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadDailyAmountsByVAT\" CmdByte=\"0x6D\"><FPOperation>Provides information about the accumulated amounts and refunded amounts by VAT class in case that CU regularly informs about the Z report(7C)</FPOperation><Response ACK=\"false\"><Res Name=\"SaleVATGrA\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the accumulated VAT in group A</Desc></Res><Res Name=\"SaleVATGrB\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the accumulated VAT in group B</Desc></Res><Res Name=\"SaleVATGrC\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the accumulated VAT in group C</Desc></Res><Res Name=\"SaleVATGrD\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the accumulated VAT in group D</Desc></Res><Res Name=\"SaleTurnoverVATGrE\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the accumulated turnover in group E</Desc></Res><Res Name=\"SaleTurnoverABCD\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the sale turnover in VAT groups A, B, C, D</Desc></Res><Res Name=\"RefundVATGrA\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the refund VAT in group A</Desc></Res><Res Name=\"RefundVATGrB\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the refund VAT in group B</Desc></Res><Res Name=\"RefundVATGrC\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the refund VAT in group C</Desc></Res><Res Name=\"RefundVATGrD\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the refund VAT in group D</Desc></Res><Res Name=\"RefundTurnoverVATGrE\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the refund accumulated turnover in group E</Desc></Res><Res Name=\"RefundTurnoverABCD\" Value=\"\" Type=\"Decimal\" MaxLen=\"15\"><Desc>Up to 15 symbols for the refund turnover in VAT groups A, B, C, D</Desc></Res><ResFormatRaw><![CDATA[<SaleVATGrA[1..15]> <;> <SaleVATGrB[1..15]> <;> <SaleVATGrC[1..15]> <;><SaleVATGrD[1..15]> <;><SaleTurnoverVATGrE[1..15]> <;> <SaleTurnoverABCD[1..15]> <;> <RefundVATGrA[1..15]> <;> <RefundVATGrB[1..15]> <;> <RefundVATGrC[1..15]> <;> <RefundVATGrD[1..15]> <;> <RefundTurnoverVATGrE[1..15]> <;> <RefundTurnoverABCD[1..15]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadTotalMessagesCount\" CmdByte=\"0x4E\"><FPOperation>Reads all messages from log</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"L\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'L'><;><'0'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"L\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"Count\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for the messages count</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'L'><;><'0'><;><Count[3]>]]></ResFormatRaw></Response></Command><Command Name=\"SetInvoice_ThresholdCount\" CmdByte=\"0x4E\"><FPOperation>Programs invoice threshold count</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"Value\" Value=\"\" Type=\"Decimal\" MaxLen=\"5\"><Desc>Up to 5 symbols for value</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'S'><;><'I'><;><Value[1..5]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetServer_UsedComModule\" CmdByte=\"0x4E\"><FPOperation>Program device used to talk with the server . To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"E\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionModule\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"GSM\" Value=\"0\" /><Option Name=\"LAN/WiFi\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '0' - GSM  - '1' - LAN/WiFi</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'S'><;><'E'><;><Module[1]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetTCP_Password\" CmdByte=\"0x4E\"><FPOperation>Program device's TCP password. To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"PassLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the password len</Desc></Arg><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the TCP password</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'Z'><;><'1'><;><PassLength[1..3]><;><Password[100]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetGPRS_AuthenticationType\" CmdByte=\"0x4E\"><FPOperation>Programs GPRS APN authentication type</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionAuthenticationType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"CHAP\" Value=\"2\" /><Option Name=\"None\" Value=\"0\" /><Option Name=\"PAP\" Value=\"1\" /><Option Name=\"PAP or CHAP\" Value=\"3\" /></Options><Desc>1 symbol with value: - '0' - None - '1' - PAP - '2' - CHAP - '3' - PAP or CHAP</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'G'><;><'N'><;><AuthenticationType[1]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadCurrentReceiptInfo\" CmdByte=\"0x72\"><FPOperation>Read the current status of the receipt.</FPOperation><Response ACK=\"false\"><Res Name=\"OptionIsReceiptOpened\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"SalesNumber\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for number of sales</Desc></Res><Res Name=\"SubtotalAmountVATGA\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for subtotal by VAT group A</Desc></Res><Res Name=\"SubtotalAmountVATGB\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for subtotal by VAT group B</Desc></Res><Res Name=\"SubtotalAmountVATGC\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for subtotal by VAT group C</Desc></Res><Res Name=\"SubtotalAmountVATGD\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for subtotal by VAT group D</Desc></Res><Res Name=\"SubtotalAmountVATGE\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for subtotal by VAT group E</Desc></Res><Res Name=\"reserved1\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"reserved2\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionReceiptFormat\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Brief\" Value=\"0\" /><Option Name=\"Detailed\" Value=\"1\" /></Options><Desc>(Format) 1 symbol with value:  - '1' - Detailed  - '0' - Brief</Desc></Res><Res Name=\"reserved3\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"reserved4\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionClientReceipt\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"invoice (client) receipt\" Value=\"1\" /><Option Name=\"standard receipt\" Value=\"0\" /></Options><Desc>1 symbol with value:  - '1' - invoice (client) receipt  - '0' - standard receipt</Desc></Res><Res Name=\"OptionPowerDownInReceipt\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol with value: - '0' - No - '1' - Yes</Desc></Res><Res Name=\"reserved5\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols</Desc></Res><ResFormatRaw><![CDATA[<IsReceiptOpened[1]> <;> <SalesNumber[3]> <;> <SubtotalAmountVATGA[1..13]> <;> <SubtotalAmountVATGB[1..13]> <;> <SubtotalAmountVATGC[1..13]> <;> <SubtotalAmountVATGD[1..13]> <;> <SubtotalAmountVATGE[1..13]> <;> <reserved1['0']> <;><reserved2['0']> <;> <ReceiptFormat[1]> <;> <reserved3['0']> <;> <reserved4['0']> <;> <ClientReceipt[1]> <;> <PowerDownInReceipt[1]> <;> <reserved5[1..13]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadEJ\" CmdByte=\"0x7C\"><FPOperation>Read whole Electronic Journal report from beginning to the end.</FPOperation><Args><Arg Name=\"OptionReadEJStorage\" Value=\"\" Type=\"Option\" MaxLen=\"2\"><Options><Option Name=\"Reading to PC\" Value=\"J0\" /><Option Name=\"Reading to PC for JSON\" Value=\"JY\" /></Options><Desc>2 symbols for destination:  - 'J0' - Reading to PC  - 'JY' - Reading to PC for JSON</Desc></Arg><Arg Name=\"\" Value=\"*\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ < ReadEJStorage [2]><;><'*'> ]]></ArgsFormatRaw></Args><Response ACK=\"true\" ACK_PLUS=\"true\" /></Command><Command Name=\"ScanWiFiNetworks\" CmdByte=\"0x4E\"><FPOperation>The device scan out the list of available WiFi networks.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'W'><;><'S'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetDeviceTCP_Addresses\" CmdByte=\"0x4E\"><FPOperation>Program device's network IP address, subnet mask, gateway address, DNS address. To apply use -SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionAddressType\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"DNS address\" Value=\"5\" /><Option Name=\"Gateway address\" Value=\"4\" /><Option Name=\"IP address\" Value=\"2\" /><Option Name=\"Subnet Mask\" Value=\"3\" /></Options><Desc>1 symbol with value:  - '2' - IP address  - '3' - Subnet Mask  - '4' - Gateway address  - '5' - DNS address</Desc></Arg><Arg Name=\"DeviceAddress\" Value=\"\" Type=\"Text\" MaxLen=\"15\"><Desc>15 symbols for the selected address</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'T'><;><AddressType[1]> <;><DeviceAddress[15]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadStatus\" CmdByte=\"0x20\"><FPOperation>Provides detailed 6-byte information about the current status of the CU.</FPOperation><Response ACK=\"false\"><Res Name=\"Power_down_in_opened_fiscal_receipt\" Value=\"\" Type=\"Status\" Byte=\"0\" Bit=\"1\"><Desc>Power down in opened fiscal receipt</Desc></Res><Res Name=\"DateTime_not_set\" Value=\"\" Type=\"Status\" Byte=\"0\" Bit=\"3\"><Desc>DateTime not set</Desc></Res><Res Name=\"DateTime_wrong\" Value=\"\" Type=\"Status\" Byte=\"0\" Bit=\"4\"><Desc>DateTime wrong</Desc></Res><Res Name=\"RAM_reset\" Value=\"\" Type=\"Status\" Byte=\"0\" Bit=\"5\"><Desc>RAM reset</Desc></Res><Res Name=\"Hardware_clock_error\" Value=\"\" Type=\"Status\" Byte=\"0\" Bit=\"6\"><Desc>Hardware clock error</Desc></Res><Res Name=\"Reports_registers_Overflow\" Value=\"\" Type=\"Status\" Byte=\"1\" Bit=\"1\"><Desc>Reports registers Overflow</Desc></Res><Res Name=\"Opened_Fiscal_Receipt\" Value=\"\" Type=\"Status\" Byte=\"2\" Bit=\"1\"><Desc>Opened Fiscal Receipt</Desc></Res><Res Name=\"Receipt_Invoice_Type\" Value=\"\" Type=\"Status\" Byte=\"2\" Bit=\"2\"><Desc>Receipt Invoice Type</Desc></Res><Res Name=\"SD_card_near_full\" Value=\"\" Type=\"Status\" Byte=\"2\" Bit=\"5\"><Desc>SD card near full</Desc></Res><Res Name=\"SD_card_full\" Value=\"\" Type=\"Status\" Byte=\"2\" Bit=\"6\"><Desc>SD card full</Desc></Res><Res Name=\"CU_fiscalized\" Value=\"\" Type=\"Status\" Byte=\"3\" Bit=\"5\"><Desc>CU fiscalized</Desc></Res><Res Name=\"CU_produced\" Value=\"\" Type=\"Status\" Byte=\"3\" Bit=\"6\"><Desc>CU produced</Desc></Res><Res Name=\"Paired_with_TIMS\" Value=\"\" Type=\"Status\" Byte=\"4\" Bit=\"0\"><Desc>Paired with TIMS</Desc></Res><Res Name=\"Unsent_receipts\" Value=\"\" Type=\"Status\" Byte=\"4\" Bit=\"1\"><Desc>Unsent receipts</Desc></Res><Res Name=\"No_Sec_IC\" Value=\"\" Type=\"Status\" Byte=\"5\" Bit=\"0\"><Desc>No Sec.IC</Desc></Res><Res Name=\"No_certificates\" Value=\"\" Type=\"Status\" Byte=\"5\" Bit=\"1\"><Desc>No certificates</Desc></Res><Res Name=\"Service_jumper\" Value=\"\" Type=\"Status\" Byte=\"5\" Bit=\"2\"><Desc>Service jumper</Desc></Res><Res Name=\"Missing_SD_card\" Value=\"\" Type=\"Status\" Byte=\"5\" Bit=\"4\"><Desc>Missing SD card</Desc></Res><Res Name=\"Wrong_SD_card\" Value=\"\" Type=\"Status\" Byte=\"5\" Bit=\"5\"><Desc>Wrong SD card</Desc></Res><ResFormatRaw><![CDATA[<StatusBytes[6]>]]></ResFormatRaw></Response></Command><Command Name=\"OpenReceipt\" CmdByte=\"0x30\"><FPOperation>Opens a fiscal receipt assigned to the specified operator number and operator password, parameters for receipt format and VAT type.</FPOperation><Args><Arg Name=\"reserved\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"reserved\" Value=\"     0\" Type=\"OptionHardcoded\" MaxLen=\"6\" /><Arg Name=\"OptionReceiptFormat\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Brief\" Value=\"0\" /><Option Name=\"Detailed\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '1' - Detailed  - '0' - Brief</Desc></Arg><Arg Name=\"ReceiptType\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"TraderSystemInvNum\" Value=\"\" Type=\"Text\" MaxLen=\"15\"><Desc>15 symbols for trader system invoice number</Desc></Arg><ArgsFormatRaw><![CDATA[<reserved['1']> <;> <reserved['     0']> <;> <ReceiptFormat[1]> <;> <ReceiptType['0']><;><TraderSystemInvNum[15]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetDateTime\" CmdByte=\"0x48\"><FPOperation>Sets the date and time and current values.</FPOperation><Args><Arg Name=\"DateTime\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"dd-MM-yy HH:mm\"><Desc>Date Time parameter in format: DD-MM-YY HH:MM</Desc></Arg><ArgsFormatRaw><![CDATA[ <DateTime \"DD-MM-YY HH:MM\"> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SellPLUfromExtDB\" CmdByte=\"0x31\"><FPOperation>Register the sell (for correction use minus sign in the price field) of article with specified name, price, quantity, VAT class and/or discount/addition on the transaction.</FPOperation><Args><Arg Name=\"NamePLU\" Value=\"\" Type=\"Text\" MaxLen=\"36\"><Desc>36 symbols for article's name</Desc></Arg><Arg Name=\"OptionVATClass\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"VAT Class A\" Value=\"A\" /><Option Name=\"VAT Class B\" Value=\"B\" /><Option Name=\"VAT Class C\" Value=\"C\" /><Option Name=\"VAT Class D\" Value=\"D\" /><Option Name=\"VAT Class E\" Value=\"E\" /></Options><Desc>1 symbol for article's VAT class with optional values:\"  - 'A' - VAT Class A  - 'B' - VAT Class B  - 'C' - VAT Class C  - 'D' - VAT Class D  - 'E' - VAT Class E</Desc></Arg><Arg Name=\"Price\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for article's price</Desc></Arg><Arg Name=\"MeasureUnit\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for measure unit</Desc></Arg><Arg Name=\"HSCode\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for HS Code in format XXXX.XX.XX</Desc></Arg><Arg Name=\"HSName\" Value=\"\" Type=\"Text\" MaxLen=\"20\"><Desc>20 symbols for HS Name</Desc></Arg><Arg Name=\"VATGrRate\" Value=\"\" Type=\"Decimal\" MaxLen=\"5\"><Desc>Up to 5 symbols for programmable VAT rate</Desc></Arg><Arg Name=\"Quantity\" Value=\"\" Type=\"Decimal\" MaxLen=\"10\"><Desc>1 to 10 symbols for quantity</Desc><Meta MinLen=\"1\" Compulsory=\"false\" ValIndicatingPresence=\"*\" /></Arg><Arg Name=\"DiscAddP\" Value=\"\" Type=\"Decimal\" MaxLen=\"7\"><Desc>1 to 7 for percentage of discount/addition</Desc><Meta MinLen=\"1\" Compulsory=\"false\" ValIndicatingPresence=\",\" /></Arg><ArgsFormatRaw><![CDATA[ <NamePLU[36]> <;> <OptionVATClass[1]> <;> <Price[1..13]> <;> <MeasureUnit[3]> <;><HSCode[10]> <;> <HSName[20]> <;> <VATGrRate[1..5]>  {<'*'> <Quantity[1..10]>} {<','> <DiscAddP[1..7]>}  ]]></ArgsFormatRaw></Args></Command><Command Name=\"StartGPRStest\" CmdByte=\"0x4E\"><FPOperation>Start GPRS test on the device the result</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'G'><;><'T'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"OpenInvoiceWithFreeCustomerData\" CmdByte=\"0x30\"><FPOperation>Opens a fiscal invoice receipt assigned to the specified operator number and operator password with free info for customer data. The Invoice receipt can be issued only if the invoice range (start and end numbers) is set.</FPOperation><Args><Arg Name=\"reserved\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"reserved\" Value=\"     0\" Type=\"OptionHardcoded\" MaxLen=\"6\" /><Arg Name=\"reserved\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"InvoiceType\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"CompanyName\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for Invoice company name</Desc></Arg><Arg Name=\"ClientPINnum\" Value=\"\" Type=\"Text\" MaxLen=\"14\"><Desc>14 symbols for client PIN number</Desc></Arg><Arg Name=\"HeadQuarters\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for customer headquarters</Desc></Arg><Arg Name=\"Address\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for Address</Desc></Arg><Arg Name=\"PostalCodeAndCity\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for postal code and city</Desc></Arg><Arg Name=\"ExemptionNum\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>30 symbols for exemption number</Desc></Arg><Arg Name=\"TraderSystemInvNum\" Value=\"\" Type=\"Text\" MaxLen=\"15\"><Desc>15 symbols for trader system invoice number</Desc></Arg><ArgsFormatRaw><![CDATA[ <reserved['1']> <;> <reserved['     0']> <;> <reserved['0']> <;> <InvoiceType['1']> <;> <CompanyName[30]> <;> <ClientPINnum[14]> <;> <HeadQuarters[30]> <;> <Address[30]> <;> <PostalCodeAndCity[30]> <;> <ExemptionNum[30]> <;><TraderSystemInvNum[15]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetGPRS_Password\" CmdByte=\"0x4E\"><FPOperation>Program device's GPRS password. To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"PassLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the GPRS password len</Desc></Arg><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's GPRS password</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'G'><;><'P'><;><PassLength[1..3]><;><Password[100]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SoftwareReset\" CmdByte=\"0x3F\"><FPOperation>Restore default parameters of the device.</FPOperation><Args><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6-symbols string</Desc></Arg><ArgsFormatRaw><![CDATA[ <Password[6]>  ]]></ArgsFormatRaw></Args></Command><Command Name=\"DailyReport\" CmdByte=\"0x7C\"><FPOperation>Provides information for the daily fiscal report  with zeroing and fiscal memory record, preceded by Electronic Journal report.</FPOperation><Args><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'Z'>  ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadTCP_Password\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's TCP password.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'Z'><;><'1'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"PassLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the password length</Desc></Res><Res Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>(Password) Up to 100 symbols for the TCP password</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'Z'><;><'1'><;><PassLength[1..3]><;><Password[100]>]]></ResFormatRaw></Response></Command><Command Name=\"ProgVATrates\" CmdByte=\"0x42\"><FPOperation>Stores a block containing the values of the VAT rates into the CU</FPOperation><Args><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6-symbols string</Desc></Arg><Arg Name=\"VATrateA\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate A from 2 to 6 symbols with format ##.";
         String defsPart1 = "##</Desc></Arg><Arg Name=\"VATrateB\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate B from 2 to 6 symbols with format ##.##</Desc></Arg><Arg Name=\"VATrateC\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate C from 2 to 6 symbols with format ##.##</Desc></Arg><Arg Name=\"VATrateD\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate D from 2 to 6 symbols with format ##.##</Desc></Arg><Arg Name=\"VATrateE\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate E from 2 to 6 symbols with format ##.##</Desc></Arg><ArgsFormatRaw><![CDATA[ <Password[6]> <;> <VATrateA[1..6]> <;> <VATrateB[1..6]> <;> <VATrateC[1..6]> <;> <VATrateD[1..6]> <;><VATrateE[1..6]>  ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetDeviceNTP_Address\" CmdByte=\"0x4E\"><FPOperation>Program device's NTP address . To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"AddressLen\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the address length</Desc></Arg><Arg Name=\"NTPAddress\" Value=\"\" Type=\"Text\" MaxLen=\"50\"><Desc>50 symbols for the device's NTP address</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'S'><;><'N'> <;><AddressLen[1..3]><;><NTPAddress[50]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadEJByDate\" CmdByte=\"0x7C\"><FPOperation>Read Electronic Journal Report initial date to report end date.</FPOperation><Args><Arg Name=\"OptionReadEJStorage\" Value=\"\" Type=\"Option\" MaxLen=\"2\"><Options><Option Name=\"Reading to PC\" Value=\"J0\" /><Option Name=\"Reading to PC for JSON\" Value=\"JY\" /></Options><Desc>2 symbols for destination:  - 'J0' - Reading to PC  - 'JY' - Reading to PC for JSON</Desc></Arg><Arg Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"StartRepFromDate\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"ddMMyy\"><Desc>6 symbols for initial date in the DDMMYY format</Desc></Arg><Arg Name=\"EndRepFromDate\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"ddMMyy\"><Desc>6 symbols for final date in the DDMMYY format</Desc></Arg><ArgsFormatRaw><![CDATA[ < ReadEJStorage [2]><;><'D'><;><StartRepFromDate \"DDMMYY\"><;>  <EndRepFromDate \"DDMMYY\"> ]]></ArgsFormatRaw></Args><Response ACK=\"true\" ACK_PLUS=\"true\" /></Command><Command Name=\"InfoLastReceiptDuplicate\" CmdByte=\"0x3A\"><FPOperation>Informs about the issued document</FPOperation></Command><Command Name=\"SetIdle_Timeout\" CmdByte=\"0x4E\"><FPOperation>Program device's idle timeout setting. Set timeout for closing the connection if there is an inactivity. Maximal value - 7200, minimal value 1. 0 is for never close the connection. This option can be used only if the device has LAN or WiFi. To apply use - SaveNetworkSettings()</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"IdleTimeout\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"4\" Format=\"0000\"><Desc>4 symbols for Idle timeout in format ####</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'Z'><;><'I'><;><IdleTimeout[4]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadTCP_AutoStartStatus\" CmdByte=\"0x4E\"><FPOperation>Provides information about if the TCP connection autostart when the device enter in Line/Sale mode.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"2\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'Z'><;><'2'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"2\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionTCPAutoStart\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for TCP auto start option - '0' - No  - '1' - Yes</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'Z'><;><'2'><;><TCPAutoStart[1]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadGPRS_Password\" CmdByte=\"0x4E\"><FPOperation>Provides information about device's GPRS password.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'G'><;><'P'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"G\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"PassLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the GPRS password length</Desc></Res><Res Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>Up to 100 symbols for the device's GPRS password</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'G'><;><'P'><;><PassLength[1..3]><;><Password[100]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadServer_UsedComModule\" CmdByte=\"0x4E\"><FPOperation>Read device communication usage with server</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"E\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'S'><;><'E'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"E\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionModule\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"GSM\" Value=\"0\" /><Option Name=\"LAN/WiFi\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '0' - GSM  - '1' - LAN/WiFi</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'S'><;><'E'><;><Module [1]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadSpecificMessage\" CmdByte=\"0x4E\"><FPOperation>Reads specific message number</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"L\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"MessageNum\" Value=\"\" Type=\"Text\" MaxLen=\"2\"><Desc>2 symbols for total number of messages</Desc></Arg><ArgsFormatRaw><![CDATA[ <'R'><;><'L'><;><MessageNum[2]> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"L\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"MessageNum\" Value=\"\" Type=\"Text\" MaxLen=\"2\"><Desc>2 symbols for total number of messages</Desc></Res><Res Name=\"DateTime\" Value=\"\" Type=\"DateTime\" MaxLen=\"10\" Format=\"dd-MM-yyyy HH:mm\"><Desc>Date Time parameter</Desc></Res><Res Name=\"Type\" Value=\"\" Type=\"Text\" MaxLen=\"1\"><Desc>1 symbol for type</Desc></Res><Res Name=\"Code\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for code</Desc></Res><Res Name=\"MessageText\" Value=\"\" Type=\"Text\" MaxLen=\"128\"><Desc>Up to 128 symbols for message text</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'L'><;><MessageNum[2]><;> <DateTime \"DD-MM-YYYY HH:MM\"> <;><Type[1]><;><Code[3]> <;><MessageText[128]>]]></ResFormatRaw></Response></Command><Command Name=\"SetHTTPS_Address\" CmdByte=\"0x4E\"><FPOperation>Programs server HTTPS address.</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"H\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"ParamLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for parameter length</Desc></Arg><Arg Name=\"Address\" Value=\"\" Type=\"Text\" MaxLen=\"50\"><Desc>50 symbols for address</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'S'><;><'H'><;><ParamLength[1..3]><;><Address[50]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"Subtotal\" CmdByte=\"0x33\"><FPOperation>Calculate the subtotal amount with printing and display visualization options. Provide information about values of the calculated amounts. If a percent or value discount/addition has been specified the subtotal and the discount/addition value will be printed regardless the parameter for printing.</FPOperation><Args><Arg Name=\"OptionPrinting\" Value=\"0\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionDisplay\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol with value:  - '1' - Yes  - '0' - No</Desc></Arg><Arg Name=\"DiscAddV\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for the value of the discount/addition. Use minus sign '-' for discount</Desc><Meta MinLen=\"1\" Compulsory=\"false\" ValIndicatingPresence=\":\" /></Arg><Arg Name=\"DiscAddP\" Value=\"\" Type=\"Decimal\" MaxLen=\"7\"><Desc>Up to 7 symbols for the percentage value of the discount/addition. Use minus sign '-' for discount</Desc><Meta MinLen=\"1\" Compulsory=\"false\" ValIndicatingPresence=\",\" /></Arg><ArgsFormatRaw><![CDATA[ <OptionPrinting['0']> <;> <OptionDisplay[1]> {<':'> <DiscAddV[1..13]>} {<','> <DiscAddP[1..7]>} ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"SubtotalValue\" Value=\"\" Type=\"Decimal\" MaxLen=\"13\"><Desc>Up to 13 symbols for the value of the subtotal amount</Desc></Res><ResFormatRaw><![CDATA[<SubtotalValue[1..13]>]]></ResFormatRaw></Response></Command><Command Name=\"EraseHScodes\" CmdByte=\"0x4F\"><FPOperation>Erase HS codes.</FPOperation><Args><Arg Name=\"Option\" Value=\"z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionR\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6 symbols for password</Desc></Arg><ArgsFormatRaw><![CDATA[ <Option['z']><;><OptionR['D']><;><Password[6]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetPINnumber\" CmdByte=\"0x41\"><FPOperation>Stores PIN number in operative memory.</FPOperation><Args><Arg Name=\"Password\" Value=\"\" Type=\"Text\" MaxLen=\"6\"><Desc>6-symbols string</Desc></Arg><Arg Name=\"\" Value=\"1\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"PINnum\" Value=\"\" Type=\"Text\" MaxLen=\"11\"><Desc>11 symbols for PIN registration number</Desc></Arg><ArgsFormatRaw><![CDATA[ <Password[6]> <;> <'1'> <;> <PINnum[11]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadOrStoreInvoiceCopy\" CmdByte=\"0x7C\"><FPOperation>Read/Store Invoice receipt copy to External USB Flash memory, External SD card.</FPOperation><Args><Arg Name=\"OptionInvoiceCopy\" Value=\"\" Type=\"Option\" MaxLen=\"2\"><Options><Option Name=\"Reading\" Value=\"J0\" /><Option Name=\"Storage in External SD card memory\" Value=\"J4\" /><Option Name=\"Storage in External USB Flash memory.\" Value=\"J2\" /></Options><Desc>2 symbols for destination:  - 'J0' - Reading   - 'J2' - Storage in External USB Flash memory.  - 'J4' - Storage in External SD card memory</Desc></Arg><Arg Name=\"\" Value=\"I\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"CUInvoiceNum\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for Invoice receipt Number.</Desc></Arg><ArgsFormatRaw><![CDATA[ <OptionInvoiceCopy[2]><;><'I'><;> <CUInvoiceNum[10]>  ]]></ArgsFormatRaw></Args><Response ACK=\"true\" ACK_PLUS=\"true\" /></Command><Command Name=\"ReadEODAmounts\" CmdByte=\"0x6D\"><FPOperation>Provides information about the accumulated EOD turnover and VAT</FPOperation><Args><Arg Name=\"Option\" Value=\"d\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <Option['d']> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"Option\" Value=\"d\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"EOD_sale_turnover\" Value=\"\" Type=\"Decimal\" MaxLen=\"20\"><Desc>Up to 20 symbols for the EOD sale turnover</Desc></Res><Res Name=\"EOD_credit_turnover\" Value=\"\" Type=\"Decimal\" MaxLen=\"20\"><Desc>Up to 20 symbols for the EOD credit turnover</Desc></Res><Res Name=\"EOD_saleVAT\" Value=\"\" Type=\"Decimal\" MaxLen=\"20\"><Desc>Up to 20 symbols for the EOD VAT from sales</Desc></Res><Res Name=\"EOD_creditVAT\" Value=\"\" Type=\"Decimal\" MaxLen=\"20\"><Desc>Up to 20 symbols for the EOD VAT from credit invoices</Desc></Res><ResFormatRaw><![CDATA[<Option['d']> <;> <EOD_sale_turnover[1..20]> <;> <EOD_credit_turnover[1..20]> <;> <EOD_saleVAT[1..20]> <;> <EOD_creditVAT [1..20]> <;>]]></ResFormatRaw></Response></Command><Command Name=\"StartLANtest\" CmdByte=\"0x4E\"><FPOperation>Start LAN test on the device the result</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'T'><;><'T'> ]]></ArgsFormatRaw></Args></Command><Command Name=\"ReadDeviceModuleSupport\" CmdByte=\"0x4E\"><FPOperation>FlagsModule is a char with bits representing modules supported by the device.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'D'><;><'D'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"D\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"OptionLAN\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for LAN suppor - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"OptionWiFi\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for WiFi support - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"OptionGPRS\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>1 symbol for GPRS support - '0' - No  - '1' - Yes BT (Bluetooth) 1 symbol for Bluetooth support - '0' - No  - '1' - Yes</Desc></Res><Res Name=\"OptionBT\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"No\" Value=\"0\" /><Option Name=\"Yes\" Value=\"1\" /></Options><Desc>(Bluetooth) 1 symbol for Bluetooth support - '0' - No  - '1' - Yes</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'D'><;><'D'><;><LAN[1]><;><WiFi>[1]><;><GPRS>[1]><;><BT[1]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadWiFi_NetworkName\" CmdByte=\"0x4E\"><FPOperation>Provides information about WiFi network name where the device is connected.</FPOperation><Args><Arg Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><ArgsFormatRaw><![CDATA[ <'R'><;><'W'><;><'N'> ]]></ArgsFormatRaw></Args><Response ACK=\"false\"><Res Name=\"\" Value=\"R\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"\" Value=\"N\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Res Name=\"WiFiNameLength\" Value=\"\" Type=\"Decimal\" MaxLen=\"3\"><Desc>Up to 3 symbols for the WiFi name length</Desc></Res><Res Name=\"WiFiNetworkName\" Value=\"\" Type=\"Text\" MaxLen=\"100\"><Desc>(Name) Up to 100 symbols for the device's WiFi network name</Desc></Res><ResFormatRaw><![CDATA[<'R'><;><'W'><;><'N'><;><WiFiNameLength[1..3]><;><WiFiNetworkName[100]>]]></ResFormatRaw></Response></Command><Command Name=\"ReadVersion\" CmdByte=\"0x21\"><FPOperation>Provides information about the device version.</FPOperation><Response ACK=\"false\"><Res Name=\"Version\" Value=\"\" Type=\"Text\" MaxLen=\"30\"><Desc>Up to 30 symbols for Version name and Check sum</Desc></Res><ResFormatRaw><![CDATA[<Version[30]>]]></ResFormatRaw></Response></Command><Command Name=\"RawWrite\" CmdByte=\"0xFE\"><FPOperation> Writes raw bytes to FP </FPOperation><Args><Arg Name=\"Bytes\" Value=\"\" Type=\"Base64\" MaxLen=\"5000\"><Desc>The bytes in BASE64 ecoded string to be written to FP</Desc></Arg></Args></Command><Command Name=\"ProgHScode\" CmdByte=\"0x4F\"><FPOperation>Programs HS code at a given position (HS number in order).</FPOperation><Args><Arg Name=\"Option\" Value=\"Z\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"OptionW\" Value=\"W\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"HS_Number\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"4\" Format=\"0000\"><Desc>4 symbols for HS number in order in format ####</Desc></Arg><Arg Name=\"HS_Code\" Value=\"\" Type=\"Text\" MaxLen=\"10\"><Desc>10 symbols for HS code</Desc></Arg><Arg Name=\"HS_Name\" Value=\"\" Type=\"Text\" MaxLen=\"20\"><Desc>20 symbols for name of HS group</Desc></Arg><Arg Name=\"OptionTaxable\" Value=\"\" Type=\"Option\" MaxLen=\"1\"><Options><Option Name=\"Exempted\" Value=\"1\" /><Option Name=\"Taxable\" Value=\"0\" /></Options><Desc>1 symbol for parameter: - '1' - Exempted - '0' - Taxable</Desc></Arg><Arg Name=\"MesureUnit\" Value=\"\" Type=\"Text\" MaxLen=\"3\"><Desc>3 symbols for mesure unit of item's code</Desc></Arg><Arg Name=\"VAT_Rate\" Value=\"\" Type=\"Decimal_with_format\" MaxLen=\"6\" Format=\"00.00\"><Desc>Value of VAT rate from 2 to 5 symbols with format ##.##</Desc></Arg><ArgsFormatRaw><![CDATA[ <Option['Z']> <;><OptionW['W']><;><HS_Number[4]> <;> <HS_Code[10]> <;> <HS_Name[20]><;><OptionTaxable[1]> <;> <MesureUnit[3]> <;> < VAT_Rate[2..6]> ]]></ArgsFormatRaw></Args></Command><Command Name=\"SetTime_ThresholdMinutes\" CmdByte=\"0x4E\"><FPOperation>Programs time threshold minutes</FPOperation><Args><Arg Name=\"\" Value=\"P\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"S\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"\" Value=\"T\" Type=\"OptionHardcoded\" MaxLen=\"1\" /><Arg Name=\"Value\" Value=\"\" Type=\"Decimal\" MaxLen=\"5\"><Desc>Up to 5 symbols for value</Desc></Arg><ArgsFormatRaw><![CDATA[ <'P'><;><'S'><;><'T'><;><Value[1..5]> ]]></ArgsFormatRaw></Args></Command></Defs>";

        SendRawRequest(defsPart0 + defsPart1);
    }
     }

