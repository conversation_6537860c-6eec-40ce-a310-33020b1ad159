package app.rht.petrolcard.utils.fuelpos

import android.annotation.SuppressLint
import android.app.*
import android.content.Context
import android.content.Intent
import android.util.Log
import android.widget.Toast

import android.graphics.Color
import android.net.Uri
import androidx.annotation.RequiresApi

import androidx.core.app.NotificationCompat
import androidx.core.app.NotificationCompat.PRIORITY_MIN
import app.rht.petrolcard.utils.fuelpos.FuelPosCommands.Companion.logOn
import app.rht.petrolcard.utils.fuelpos.tcp.ConnectionError
import app.rht.petrolcard.utils.fuelpos.tcp.ConnectionState
import app.rht.petrolcard.utils.fuelpos.tcp.FuelPosTcpClient
import app.rht.petrolcard.utils.fuelpos.tcp.FuelPosTcpServer
import android.app.PendingIntent
import android.os.*
import android.util.Base64
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.database.baseclass.ESDSignatureDao
import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.database.baseclass.ProductsDao
import app.rht.petrolcard.database.baseclass.TerminalDao
import app.rht.petrolcard.service.*
import app.rht.petrolcard.ui.esdsign.model.EsdSignModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.model.FuelPOSModel
import app.rht.petrolcard.ui.reference.model.ProductModel
import app.rht.petrolcard.ui.reference.model.TerminalModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.esdPrinterJobs.PeriodicFusionChecker
import com.google.gson.Gson
import app.rht.petrolcard.utils.fuelpos.FuelPosCommands.Companion.deleteTransaction

import app.rht.petrolcard.utils.fuelpos.models.TransactionData
import net.sqlcipher.database.SQLiteDatabase
import net.sqlcipher.database.SQLiteException
import org.apache.commons.lang3.exception.ExceptionUtils
import java.lang.Exception
import java.lang.IndexOutOfBoundsException
import java.lang.NullPointerException
import java.net.*
import java.util.*
import androidx.core.content.ContextCompat
import java.lang.RuntimeException


/*
fun Context.toast(message:String){
    Toast.makeText(applicationContext,message,Toast.LENGTH_SHORT).show()
}

fun getIpAddress(): String {
    var ip = ""
    try {
        val enumNetworkInterfaces = NetworkInterface
            .getNetworkInterfaces()
        while (enumNetworkInterfaces.hasMoreElements()) {
            val networkInterface = enumNetworkInterfaces
                .nextElement()
            val enumInetAddress = networkInterface
                .inetAddresses
            while (enumInetAddress.hasMoreElements()) {
                val inetAddress = enumInetAddress.nextElement()
                if (inetAddress.isSiteLocalAddress) {
                    ip += inetAddress.hostAddress
                }
            }
        }
    } catch (e: SocketException) {
        e.printStackTrace()
        ip += "Something Wrong! ${e.message}\n"
    }
    return ip
}
*/

class FuelPosServiceBackup : Service() {

    companion object {
        private val TAG = FuelPosServiceBackup::class.simpleName
        private const val ACTION_START_FOREGROUND_SERVICE = "ACTION_START_FOREGROUND_SERVICE"
        private const val ACTION_STOP_FOREGROUND_SERVICE = "ACTION_STOP_FOREGROUND_SERVICE"
        private const val ACTION_CONNECT_FCC = "ACTION_START_FCC_SERVICE"
        private const val ACTION_DISCONNECT_FCC = "ACTION_STOP_FCC_SERVICE"
        val ESD_SIGNATURE_DATA = "esd_sign_data"
        val ESD_ERROR_ACTION = "action.esd_not_sign_received"
        val ESD_ERROR_MESSAGE = "esd_error_message"
        val ESD_SIGN_RECEIVED_ACTION = "action.esd_sign_received"
        val ESD_SIGN_NOT_RECEIVED_ACTION = "action.esd_not_sign_received"
        @SuppressLint("StaticFieldLeak")
        lateinit var context: Context
        lateinit var ip: String
        var port: Int = 7510
        var listenerPort: Int = 7778

        private val logWriter = LogWriter("FuelPosService")

        var terminal: TerminalModel? = null
        var eprUsername = ""
        var eprPassword = ""
        var prefs = MainApp.getPrefs()
        
        @JvmStatic fun isRunning(context: Context/*, serviceClass: Class<*>*/): Boolean { //for all service class use = serviceClass: Class<*> as parameter
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            for (service in activityManager.getRunningServices(Integer.MAX_VALUE)) {
                //logWriter.appendLog(TAG,"## "+serviceClass.name)
                logWriter.appendLog(TAG,"## "+ FuelPosService::class.java.name)
                if (/*serviceClass.name*/FuelPosService::class.java.name == service.service.className) {
                    return true
                }
            }
            return false
        }
        @JvmStatic fun start(context: Context){
            Companion.context = context

            fuelpos = prefs.getFuelPosModel()!!

            this.ip = fuelpos.ipAddress!!
            this.port = fuelpos.tcpPort
            this.listenerPort =  fuelpos.tcpListener
            this.eprUsername = (terminal!!.eprUsername ?: fuelpos.tcpUser) ?: "EPR_01"
            this.eprPassword =  (terminal!!.eprPassword ?: fuelpos.tcpPass) ?: "83E83B5B2"

            val mIntent = Intent(context, FuelPosService::class.java)
            mIntent.action = ACTION_START_FOREGROUND_SERVICE
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,mIntent)
            } else {
                context.startService(mIntent)
            }
        }
        @JvmStatic fun stop(context: Context){
            val intent = Intent(context, FuelPosService::class.java)
            intent.action = ACTION_STOP_FOREGROUND_SERVICE

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
                //context.startForegroundService(intent)
            } else {
                context.startService(intent)
            }
        }
        @JvmStatic fun restart(){
            logWriter.appendLog(TAG,"Restart FuelPOS called")
            stopFuelPos()
            Handler(Looper.getMainLooper()).postDelayed({
                startFuelPos()
            },2000)
        }
        @JvmStatic fun isConnected() : Boolean {
            return if(::tcpClient.isInitialized) {
                logWriter.appendLog(TAG,"CLIENT CONNECTION:::: ${tcpClient.isConnected}")
                tcpClient.isConnected
            } else {
                false
            }
        }

        @JvmStatic fun connectFcc(context: Context){
            val intent = Intent(context, FuelPosService::class.java)
            intent.action = ACTION_CONNECT_FCC
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
            } else {
                context.startService(intent)
            }
        }
        @JvmStatic fun disconnectFcc(context: Context){
            val intent = Intent(context, FuelPosService::class.java)
            intent.action = ACTION_DISCONNECT_FCC

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                ContextCompat.startForegroundService(context,intent)
            } else {
                context.startService(intent)
            }
        }

        /*@JvmStatic fun startFuelPosStateChecker(){
            try {
                fuelPosTcpClient = FuelPosTcpClient(ip,7501, posMessageListener,true,10)
                fuelPosTcpClient.connect()
            } catch (e: Exception){
                e.printStackTrace()
            }
        }

        @JvmStatic fun stopFuelPosStateChecker(){
            try {
                fuelPosTcpClient.disconnect()
            } catch (e:Exception){ e.printStackTrace() }
        }*/

        @JvmStatic fun sendViaPosProtocol(message: String){
            try {
                if(extPosClient.isConnected)
                {
                    extPosClient.send(message)
                }
            } catch (e:Exception){
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
        }

        private lateinit var extPosClient: FuelPosTcpClient
        private val pumpStateListener = object : FuelPosTcpClient.FuelPosClientListener {
            override fun onHeartbeat() {
                Log.i(TAG,"onHeartbeat:: "+FuelPosCommands.posHeartbeat())
                try{
                    if(extPosClient.isConnected)
                        extPosClient.send(FuelPosCommands.posHeartbeat())
                } catch (e:Exception){
                   e.printStackTrace()
                    logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                }
                logWriter.appendLog(TAG, "pump state heartbeat sent")

            }

            override fun onConnectionStateChanged(state: ConnectionState) {
                Log.i(TAG, "onConnectionStateChanged:: $state")
                if (state === ConnectionState.CONNECTED) {

                    var senderId = Support.getSN()!!
                    if (senderId.length > 4) senderId = senderId.substring(senderId.length - 4)

                    val extUsername = (terminal!!.extUsername ?: fuelpos.userExt) ?: "EXTPOS_1"
                    val extPassword =  (terminal!!.extPassword ?: fuelpos.passExt) ?: "8E493E2DE"

                    val message = FuelPosCommands.posLogOn(extUsername, extPassword,senderId)
                    //val message = "LOGIN 1:0|99990:EXTPOS_1|99991:8E493E2DE|99992:EXTPOS1|99993:TPEBTEST|99994:1|\r\n"
                    //val message = "LOGIN 1:0|99990:EXTPOS_1|99991:85165D19C|99992:EXTPOS1|99993:TPEBTEST|99994:1|\r\n"
                    extPosClient.send(message)
                }
            }

            override fun onError(error: ConnectionError) {
                logWriter.appendLog(TAG,"pos Connection error ${error.name}")
                if(error.name == "TIMEOUT"){
                    sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,ConnectionError.TIMEOUT.name)
                }
                else
                    sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,ConnectionState.DISCONNECTED.name)
                //startPosPumpStateClient()
            }

            override fun onDataSend() {
                logWriter.appendLog(TAG,"pos Data send success to pos")
            }

            override fun onDataReceived(message: String, bytes: ByteArray) {
                logWriter.appendLog(TAG, "POS MESSAGE: $message")
                //if(!message.contains("230 9999"))
                sendBroadCastMessage(ACTION_POS_MESSAGE, POS_MESSAGE,message)
            }

        }

        //region FuelPos Methods
        var sendCount = 0
        private lateinit var tcpClient : FuelPosTcpClient
        private lateinit var tcpServer: FuelPosTcpServer
        private val tcpClientListener = object : FuelPosTcpClient.FuelPosClientListener {
            override fun onHeartbeat() {
                //tcpClient.send(logOn("EPR_01", "83E83B5B2"))
                try{
                    tcpClient.send(logOn(fuelpos.tcpUser!!, fuelpos.tcpPass!!))
                } catch (e:Exception){
                    tcpClient.send(logOn("EPR_01", "83E83B5B2"))
                    logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
                }
                logWriter.appendLog(TAG, "heartbeat sent")
            }
            override fun onConnectionStateChanged(state: ConnectionState) {
                when (state) {
                    ConnectionState.CONNECTED -> {
                        logWriter.appendLog(TAG,"Connected")
                        sendBroadCastMessage(
                            ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,
                            FuelPosClientState.CONNECTED)
                    }
                    ConnectionState.CONNECTING -> {
                        logWriter.appendLog(TAG,"Connecting")
                        sendBroadCastMessage(
                            ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,
                            FuelPosClientState.CONNECTING)
                    }
                    else -> {
                        logWriter.appendLog(TAG,"Disconnected")
                        sendBroadCastMessage(
                            ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE,
                            FuelPosClientState.DISCONNECTED)
                    }
                }
            }
            override fun onError(error: ConnectionError) {
                logWriter.appendLog(TAG,"Error: ${error.name}")
                if(sendCount < 1 && error == ConnectionError.DATA_SEND_FAILED) {
                    sendCount++
                    sendRequest(oldRequest)
                }
                else
                {
                    sendBroadCastMessage(ACTION_FUEL_POS_CLIENT_STATE, FUEL_POS_CLIENT_STATE, FuelPosClientState.DISCONNECTED)
                }
            }
            override fun onDataSend() {
                Log.i(TAG, "Data Send Success")
                sendCount = 0
                oldRequest = ""
            }
            override fun onDataReceived(message: String, bytes: ByteArray) {
                var st = ""
                for (b in bytes) { st += "${String.format("%02X", b)} " }
                Log.i(TAG,"Packet Received: $message ")
                sendBroadCastMessage(ACTION_FUEL_POS_REPLY, FUEL_POS_REPLY,message)
            }
        }

        // as bunseng's suggestion terminal should send delete message for all the transaction which terminal is receiving from fuelPOS
        private fun deleteFuelPosTransaction(transactionData : TransactionData){
            Handler(Looper.getMainLooper()).postDelayed({
                val command = deleteTransaction(transactionData.trxToken)
                sendReplyToClient(command, fuelpos.ipAddress!!)
            },200)
        }

        private fun performNextStep(message: String) {
            if (message.contains(FuelPosReply.TRANSACTION_DATA)) {
                val item = FuelPosReply.getTransactionDataMessage(message)

                logWriter.appendLog(TAG,"TRANSACTION DATA IN FUEL POS SERVICE: $message")

                if(item!=null){
                    deleteFuelPosTransaction(item)
                } else {
                    logWriter.appendLog(TAG,"NULL TRANSACTION DATA FOUND: $message")
                }

                if (item!=null && item.totalAmount != "0" && item.completionCode == "${CompletionCode.OK}") {
                    val mPref = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))
                    val decimal = mPref.getDecimal()
                    val totalAmount = toDecimalValue(item.totalAmount, decimal)

                    //val command = deleteTransaction(item.trxToken)
                    //logWriter.appendLog(TAG,"DELETE COMMAND SENT : $command")
                    //logWriter.appendLog(TAG, command)
                    //sendReplyToClient(command, fuelpos.ipAddress!!)

                    if (item.completionCode == "0") {
                        if(fiscalPrinterModel.isAvailable) {
                            val terminalNozzleDetails = mSharedPrefsUtil.getReferenceModel()!!.RFID_TERMINALS
                            if (terminalNozzleDetails != null) {
                                val pumpsModels = terminalNozzleDetails.pumps
                                if (pumpsModels != null && pumpsModels.isNotEmpty()) {
                                    for (terminalPump in pumpsModels) {
                                        if (item.pump == "${terminalPump.pump_number}") {
                                            logWriter.appendLog(TAG,"${terminalPump.pump_number} = ${item.pump}")
                                            logWriter.appendLog(TAG,"${terminalPump.pump_number} = ${item.pump}")
                                            saveTransactionInEsdSignTable(item)

                                        }
                                        else
                                        {
                                            logWriter.appendLog(TAG,"${terminalPump.pump_number} != ${item.pump}")
                                            logWriter.appendLog(TAG,"${terminalPump.pump_number} != ${item.pump}")
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        logWriter.appendLog(TAG,"Message Status: ${CompletionCode.getMessage(item.completionCode)}")
                        logWriter.appendLog(TAG,"Message Status: ${CompletionCode.getMessage(item.completionCode)}")
                    }

                } else {
                    if (BuildConfig.DEBUG) {
                       /* Handler().postDelayed({
                            val command = deleteTransaction(item!!.trxToken)
                            logWriter.appendLog(TAG, command)
                            sendRequest(command)
                        }, 100)*/
                    }
                }
            }
        }

        private fun startFuelPos(){
            logWriter.appendLog(TAG,"startFuelPos()")
            tcpServer = FuelPosTcpServer()

            tcpServer.setOnConnectListener(object : FuelPosTcpServer.OnConnect{
                override fun connected(
                    socket: Socket?,
                    localAddress: InetAddress?,
                    port: Int,
                    localSocketAddress: SocketAddress?,
                    clientIndex: Int
                ) {
                    logWriter.appendLog(TAG,"Connected: ${socket!!.inetAddress}")
                    sendBroadCastMessage(
                        ACTION_FUEL_POS_SERVER_STATE,
                        FUEL_POS_SERVER_STATE,
                        FuelPosServerState.CLIENT_CONNECTED)
                }

            })
            tcpServer.setOnDisconnectListener(object : FuelPosTcpServer.OnDisconnect{
                override fun disconnected(
                    socket: Socket?,
                    localAddress: InetAddress?,
                    port: Int,
                    localSocketAddress: SocketAddress?,
                    clientIndex: Int
                ) {
                    logWriter.appendLog(TAG,"Disconnected: ${socket!!.inetAddress}")
                    sendBroadCastMessage(
                        ACTION_FUEL_POS_SERVER_STATE,
                        FUEL_POS_SERVER_STATE,
                        FuelPosServerState.CLIENT_DISCONNECTED)
                }

            })
            tcpServer.setOnMessageReceivedListener(object : FuelPosTcpServer.OnMessageReceived{
                override fun messageReceived(message: String?, bytes:ByteArray, clientIndex: Int) {
                    logWriter.appendLog(TAG,"FUEL POS Server Received: $message")
                    sendBroadCastMessage(ACTION_FUEL_POS_MESSAGE, FUEL_POS_MESSAGE,message!!)
                    performNextStep(message)
                }
            })
            tcpServer.setOnServerStartListener(object : FuelPosTcpServer.OnServerStart{
                override fun serverStarted(port: Int) {
                    logWriter.appendLog(TAG,"FUEL POS Server Started: $port")
                    sendBroadCastMessage(
                        ACTION_FUEL_POS_SERVER_STATE,
                        FUEL_POS_SERVER_STATE,
                        FuelPosServerState.STOPPED)
                }
            })
            tcpServer.setOnServerClosedListener(object : FuelPosTcpServer.OnServerClose{
                override fun serverClosed(port: Int) {
                    logWriter.appendLog(TAG,"FUEL POS Server Stopped: $port")
                    sendBroadCastMessage(
                        ACTION_FUEL_POS_SERVER_STATE,
                        FUEL_POS_SERVER_STATE,
                        FuelPosServerState.STARTED)
                }
            })

            initTcpClient()

            if(!tcpServer.isServerRunning){
                tcpServer.startServer(listenerPort)
            }

            //startPosPumpStateClient()
        }

        private fun initTcpClient(){
            tcpClient = FuelPosTcpClient(ip, port, tcpClientListener)
            tcpClient.connect()
        }

        @JvmStatic fun startExtPosClient(){
           try{
               this.ip = fuelpos.ipAddress!!
               this.port = fuelpos.tcpPort
               extPosClient = FuelPosTcpClient(ip, 7501, pumpStateListener,true, 10)
               extPosClient.connect()
           } catch (e: RuntimeException){
               logWriter.appendLog(TAG,"Unable to connect pump StateClient")
           }
        }

        @JvmStatic fun stopExtPosClient(){
            extPosClient.disconnect()
        }

        private fun stopFuelPos() {
            logWriter.appendLog(TAG,"stopFuelPos()")
            try{
                //
                tcpClient.disconnect()
                //stopPosPumpStateClient()
                if(::tcpServer.isInitialized && tcpServer.isServerRunning) {
                    tcpServer.closeServer()
                }
                //stopPumpStateChecker()
            } catch (e:Exception){
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
        }

        private var oldRequest = ""
        @JvmStatic fun sendRequest(request:String){
            if(tcpClient.isConnected)
                tcpClient.send(request)
            else
            {
                logWriter.appendLog(TAG,"tcpClient not connected")

                restart()
                //oldRequest = request
                //tcpClient.send(request)
            }
        }

        //private var oldServerRequest = ""
        @JvmStatic fun sendReplyToClient(message:String,clientIp:String) {
            for(client in tcpServer.getClients()){
                val ip = "${client.value.getSocket().inetAddress}".replace("/","")
                logWriter.appendLog(TAG,"CLIENT: $ip = $clientIp")
                if(ip.contains(clientIp)){
                    val thread = Thread {
                        tcpServer.send(client.key,message)
                        //tcpServer.broadcast(message)
                    }
                    thread.start()
                }
            }
        }

        private fun sendBroadCastMessage(action:String, key:String, message: String){
            val intent = Intent(action)
            intent.putExtra(key, message)
            context.sendBroadcast(intent)
        }
        //endregion

        private fun getTerminalModel():TerminalModel?{
            var terminal :TerminalModel? = null
            try {
                val mTerminalDAO = TerminalDao()
                mTerminalDAO.open()
                terminal = mTerminalDAO.getCurrent()
                mTerminalDAO.close()
            } catch (ex: SQLiteException) {
                logWriter.appendLog(TAG, ex.message!!)
            }
            return terminal
        }

        private var mSharedPrefsUtil : AppPreferencesHelper
        private var fiscalPrinterModel: FiscalPrinterModel
        private var fuelpos: FuelPOSModel
        init {
            mSharedPrefsUtil = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))
            try{ terminal = mSharedPrefsUtil.getReferenceModel()!!.terminal }
            catch (e:Exception) {
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }

            fiscalPrinterModel = mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer!!
            fuelpos = mSharedPrefsUtil.getReferenceModel()!!.FUELPOS
        }


        //region ESD Printer
        var udpManager: UDPManager? = null
        private var request1 = "\u0002{/0/09\u0003JN"
        private var messageToSend = ""
        private var request3 = "\u0002}/72\u0003NN"
        private var request4 = "\u0002^/41\u0003"
        private var fiscalDate = ""
        private var esdSignature = ""
        private var signatureCountDownTimer: CountDownTimer = object : CountDownTimer( /*120000*/10000, 1000) {
            var count = 0
            override fun onTick(millisUntilFinished: Long) {
                count++
                logWriter.appendLog(TAG, "Waiting for ESD Signature: $count")
            }

            override fun onFinish() {
                try {
                    count = 0
                    if (esdSignature.isEmpty()) {
                        sendEsdBroadcast(ESD_SIGN_NOT_RECEIVED_ACTION)
                    } else {
                        logWriter.appendLog(TAG, "ESD sign timer timeout: Sign already received")
                    }
                    PeriodicFusionChecker.startChecker()
                } catch (e: Exception) {
                    logWriter.appendLog(TAG, e.message!!)
                }
            }
        }

        private fun tryToGetSignature() {
            esdSignature = ""
            signatureCountDownTimer.cancel()

            sendUdpMessage(request1)
            sendUdpMessage(getBlocToSignContent(oldMessageForEsd))
            sendUdpMessage(request3)
            sendUdpMessage(request4)
            signatureCountDownTimer.start()
        }
        private var oldMessageForEsd = ""
        private var isSignature = false
        private var messageCount = 0
        private fun sendUdpMessage(message: String) {
            try {
                udpManager!!.sendMessage(message)
                logWriter.appendLog(TAG, "SENT to ESD: $message")
                messageCount++
                Thread.sleep(1000)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            } catch (e: NullPointerException) {
                e.printStackTrace()
            }
        }
        private fun getBlocToSignContent(text: String): String {
            /* String text ="\nREF : "+ mTransaction.getReference()+
                    //"\nPRODUCT : "+produit+
                    "\nAMOUNT : "+ mTransaction.getAmount()+
                    "\nQUANTITE : "+ mTransaction.getQuantite()+
                    "\nPU : "+ mTransaction.getUnitPrice()+
                    "\nATTENDANT : "+ mTransaction.getCodePompiste()+
                    "\nDATE : "+ mTransaction.getDateTransaction() +
                    "\nPUMP : "+ transactionToPay.getPump()
                    ;//"This is the jh kk comhhmand kknm for sending pure text data for signing to the device. To succeed, the device must have a signature block in progress (see status-2, SIP bit, and paragraph 6.8)";*/
            val data = text.toByteArray() //"UTF-8" removed by altaf
            val base64 = Base64.encodeToString(data, Base64.DEFAULT)
            Log.d("Base 64 ", base64.replace("\\s+".toRegex(), "") + "!")
            var blockToSign = base64.replace("\\s+".toRegex(), "")

            // String blockToSign = "VGhpcyBpcyB0aGUgY29tbWFuZCBmb3Igc2VuZGluZyBwdXJlIHRleHQgZGF0YSBmb3Igc2lnbmluZyB0byB0aGUgZGV2aWNlLiBUbyBzdWNjZWVkLCB0aGUgZGV2aWNlIG11c3QgaGF2ZSBhIHNpZ25hdHVyZSBibG9jayBpbiBwcm9ncmVzcyAoc2VlIHN0YXR1cy0yLCBTSVAgYml0LCBhbmQgcGFyYWdyYXBoIDYuOCk=";
            // adding suffix mentioned on fiscal printer
            blockToSign = "\u0002&/$blockToSign/" + Utils.CalcChecksum(
                "&/$blockToSign/".toByteArray()
            ) + "\u0003"
            return blockToSign
        }

        class esdSignTask : CoroutineAsyncTask<String, Void, Boolean?>(){
            var message = ""
            override fun doInBackground(vararg params: String): Boolean? {

                if (esdSignature.isEmpty()) {
                    signatureCountDownTimer.start()
                    message = params[0]
                    oldMessageForEsd = message
                    try {
                        messageToSend = getBlocToSignContent(message)
                        val ip: String
                        var port = 5445
                        if (BuildConfig.DEBUG) ip = "**************" else {
                            ip = mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer!!.IPESD
                            port = mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer!!.PORTESD
                        }
                        if (udpManager == null) {
                            udpManager = UDPManager.getUdpManager(ip, port)
                            udpManager!!.startUDPSocket()
                        }
                        logWriter.appendLog(TAG, "ESD Printer connected: $ip:$port")
                        udpManager!!.setUdpReceiveCallback { data: DatagramPacket ->
                            val strReceive =
                                String(data.data, 0, data.length)

                            logWriter.appendLog(
                                TAG,
                                "RECEIVED From ESD: $strReceive"
                            )
                            if (strReceive.length > 15) {
                                esdSignature = strReceive
                                val packet =
                                    strReceive.split("/".toRegex()).toTypedArray()
                                logWriter.appendLog(
                                    TAG,
                                    "Total Message part: " + packet.size
                                )
                                for (i in packet.indices) {
                                    logWriter.appendLog(
                                        TAG,
                                        "Part:" + i + " Data:" + packet[i]
                                    )
                                }
                                fiscalDate = packet[5].trim { it <= ' ' }
                                esdSignature = packet[7].trim { it <= ' ' }
                                isSignature = true
                                esdSignature = esdSignature + "_" + fiscalDate
                                signatureCountDownTimer.cancel()
                                if (udpManager != null)
                                    udpManager!!.stopUDPSocket()
                                sendEsdBroadcast(
                                    ESD_SIGN_RECEIVED_ACTION,
                                    ESD_SIGNATURE_DATA,
                                    esdSignature
                                )
                                updateTransactionSign(esdSignature)
                            }
                        }
                        sendUdpMessage(request1)
                        sendUdpMessage(messageToSend)
                        sendUdpMessage(request3)
                        sendUdpMessage(request4)
                        while (!isSignature) { }

                    } catch (e: IndexOutOfBoundsException) {
                        logWriter.appendLog(
                            TAG,
                            "SocketException: " + ExceptionUtils.getStackTrace(e)
                        )
                        //"Unable to process ESD response!"
                        sendEsdBroadcast(
                            ESD_ERROR_ACTION,
                            ESD_ERROR_MESSAGE,
                            "Unable to process ESD response!"
                        )
                        logWriter.appendLog(TAG, "Unable to process ESD response!")
                    } catch (e: SocketException) {
                        logWriter.appendLog(
                            TAG,
                            "SocketException: " + ExceptionUtils.getStackTrace(e)
                        )
                        //"Unable to connect ESD Printer!"
                        logWriter.appendLog(TAG, "Unable to connect ESD Printer!")
                        sendEsdBroadcast(
                            ESD_ERROR_ACTION,
                            ESD_ERROR_MESSAGE,
                            "Unable to connect ESD Printer!"
                        )
                    } catch (e: NullPointerException) {
                        logWriter.appendLog(TAG, "NullPointerException UDP Manager")
                    }
                } else {
                    logWriter.appendLog(TAG, "ESD Signature already exist")
                }
                return null
            }
        }


        var esdModel : EsdSignModel? = null
        private fun prepareEsdMessage(trxID:Int): String {
            var message = ""

            val esdSignatureDAO = ESDSignatureDao()
            esdSignatureDAO.open()
            esdModel = esdSignatureDAO.getSignedTransaction(trxID)
            esdSignatureDAO.close()

            logWriter.appendLog(TAG,"ESD TRX:: ${Gson().toJson(esdModel)}")
            val mPref = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))
            val currency = mPref.currency
            val product = getProductModel(esdModel!!.productNo!!)

            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product.productID)
                if (fusionProductName!!.isNotEmpty()) {
                    product.libelle = fusionProductName
                }
            }

            message += Support.dateToStringH(Date()) + " (" + (terminal?.terminalId ?: "***") + ")"
            message +=  "\nFUEL"
            if (terminal != null) {
                message += "\n${terminal!!.stationName} (${terminal!!.stationId})"
                message += "\n${terminal!!.address}"
            }

            message +=  "\n${if (terminal != null) terminal!!.city else "***"}"
            if (terminal != null)
                message += "\nIF : ${terminal!!.fiscalId}"

            if(product!=null)
                message += "\nPRODUCT : ${product.libelle}"

            message += "\n"+ MainApp.appContext.resources.getString(R.string.amount_pay) + " : " + esdModel!!.amount + " " + currency
            message += "\nDATE : " + esdModel!!.time
            message += "\nPU : ${esdModel!!.unitPrice} $currency"
            message += "\nQTY : ${esdModel!!.volume} L"
            message += "\nPUMP : ${esdModel!!.pumpNumber}"

            logWriter.appendLog(TAG,"ESD PACKET: $message")
            return message
        }

        private var lastEsdRecordId = 0
        private fun saveTransactionInEsdSignTable(item: TransactionData) {

            val decimal = mSharedPrefsUtil.getReferenceModel()!!.FUELPOS.decimal
            if ( item.totalAmount != "0") {
                logWriter.appendLog(TAG, "TRX Details: $item")

                val values = arrayOf(toDecimalValue(item.totalAmount, decimal).toString() + "", item.pump, item.trxToken)
                logWriter.appendLog(TAG, "Final FuelTrx amount Received: " + values[0])

                PeriodicFusionChecker.stopChecker()

                val esdSignModel = EsdSignModel(item.trxToken,item.pump,item.productCode,item.totalAmount,item.quantity,item.unitPrice)

                SQLiteDatabase.loadLibs(MainApp.appContext)
                val esdSignatureDAO = ESDSignatureDao()
                esdSignatureDAO.open()
                lastEsdRecordId = esdSignatureDAO.insert(esdSignModel)
                esdSignatureDAO.close()
                logWriter.appendLog(TAG,"Esd Sign transaction inserted in db : $lastEsdRecordId")
                val message = prepareEsdMessage(lastEsdRecordId)
                esdSignTask().execute(message)
            } else {
                logWriter.appendLog(TAG, "INVALID AMOUNT OR COMPLETION CODE : $item")
            }
        }
        private fun getProductModel(id:String) : ProductModel? {
            var product : ProductModel? = null
            try {
                val productDAO = ProductsDao()
                productDAO.open()
                product = productDAO.getProductById(id.toInt())
                productDAO.close()

            } catch (e:Exception) {
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
            return product
        }
        private fun updateTransactionSign(signature:String){
            esdSignature = ""
            try {
                val esdSignatureDAO = ESDSignatureDao()
                esdSignatureDAO.open()
                esdModel!!.signature = signature
                esdSignatureDAO.updateESDSignature(esdModel!!)
                esdSignatureDAO.close()
                logWriter.appendLog(TAG,"Signature updated in db")
                //UpdateFusionTrxTask(esdModel!!.sequenceNumber).execute()

                esdModel = null
                Support.printAllEsdTransactions()
            } catch (e:Exception) {
                e.printStackTrace()
                logWriter.appendLog(TAG, e.message+ ExceptionUtils.getStackTrace(e))
            }
        }

        var isUserAction = false
        fun sendESDSignRequest(message: String,isUserAction: Boolean) {
            Companion.isUserAction = isUserAction
            /*CoroutineScope(Dispatchers.Default).launch {
                getEsdSignature(message)
            }*/
            esdSignTask().execute(message)
        }
        fun retryESDSignRequest() {
            tryToGetSignature()
        }

        private fun sendEsdBroadcast(action:String,dataKey:String?=null,data:String?=null) {
            if(isUserAction){
                val thread = Thread {
                    try {
                        val intent = Intent(action)
                        if(dataKey!=null)
                            intent.putExtra(dataKey, data)
                        MainApp.appContext.sendBroadcast(intent)
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
                thread.start()
            }
        }
        //endregion

    }

    override fun onBind(intent: Intent?): IBinder? {
        // TODO: Return the communication channel to the service.
        //throw UnsupportedOperationException("Not yet implemented")
        return null
    }

    override fun onCreate() {
        super.onCreate()
        startForegroundService()
        logWriter.appendLog(TAG, "FuelPosService onCreate().")
    }


    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        if(intent != null) {
            when (intent.action) {
                ACTION_START_FOREGROUND_SERVICE -> {
                    startFuelPos()
                    //toast("FuelPosService started.")
                }
                ACTION_STOP_FOREGROUND_SERVICE -> {
                    stopFuelPos()
                    stopForegroundService()
                    //toast("FuelPosService stopped.")
                }
                ACTION_CONNECT_FCC -> {
                    try{
                        tcpClient.connect()
                    } catch (e:UninitializedPropertyAccessException){
                        initTcpClient()
                    }

                    //extPosClient.connect()
                }
                ACTION_DISCONNECT_FCC -> {
                    tcpClient.disconnect()
                    //extPosClient.connect()
                }
            }
        }

        return START_NOT_STICKY //super.onStartCommand(intent, flags, startId)
    }

    override fun onDestroy() {
        super.onDestroy()
        logWriter.appendLog(TAG,"On Destroy Called")
        stopFuelPos()
    }

    //region foreground notification
    private val CHANNEL_ID = "fuelPosService"
    private val CHANNEL_NAME = "FuelPosService"
    @RequiresApi(Build.VERSION_CODES.O)
    private fun createNotificationChannel(channelId: String, channelName: String): String {
        val channel = NotificationChannel(channelId, channelName, NotificationManager.IMPORTANCE_DEFAULT)
        channel.lightColor = Color.BLUE
        channel.lockscreenVisibility = Notification.VISIBILITY_PRIVATE
        val service = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        service.createNotificationChannel(channel)
        return channelId
    }
    private fun startForegroundService() {
        logWriter.appendLog(TAG, "Start FuelPosService.")

        val intent = Intent(this, MenuActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, 0)

        val defaultSoundUri = Uri.parse("android.resource://app.rht.petrolcard/" + R.raw.connect)

        val channelId =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                createNotificationChannel(CHANNEL_ID, CHANNEL_NAME)
            } else { "" }

        val notificationBuilder = NotificationCompat.Builder(this, channelId )

        val notification = notificationBuilder.setOngoing(true)
            .setSmallIcon(android.R.drawable.stat_notify_sync)
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setOnlyAlertOnce(true)
            //.setCategory(Notification.CATEGORY_SERVICE)
            .setContentTitle("FuelPOS")
            .setContentText("Service Running")
            .setSound(defaultSoundUri)
            .setSilent(true)
            .setFullScreenIntent(pendingIntent,true)
            .build()

        startForeground(101, notification)
    }
    private fun stopForegroundService() {
        logWriter.appendLog(TAG, "Stop FuelPosService.")
        stopForeground(true)
        stopSelf()
    }

    //endregion
}