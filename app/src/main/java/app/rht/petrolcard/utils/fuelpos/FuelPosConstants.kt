package app.rht.petrolcard.utils.fuelpos

import android.os.Build
import androidx.annotation.RequiresApi
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.AMOUNT
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.ARTICLE_CODE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.ARTICLE_NAME
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.CARD_APP_NAME
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.CARD_APP_TYPE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.COMPLETION_CODE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.CURRENCY_CODE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.DEVICE_CODE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.IP_ADDRESS
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.JOURNAL_TEXT
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.LOYALTY_IDENTIFIER
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.OPERATING_MODE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.PORT_NUMBER
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.POWER_SUPPLY_STATUS
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.PRESERVE_AMOUNT
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.PUMP_INFO
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.PUMP_NUMBER
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.QUANTITY_LTR
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.TOTAL_AMOUNT
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.TRANSACTION_CHECKSUM
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.TRANSACTION_DATE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.TRANSACTION_REQ
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.TRANSACTION_TOKEN
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.UNIT_PRICE
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.VAT_AMOUNT
import app.rht.petrolcard.utils.fuelpos.FuelPosField.Companion.VAT_PERCENTAGE
import app.rht.petrolcard.utils.fuelpos.PosProtocolFields.Companion.MSG_SEQ
import app.rht.petrolcard.utils.fuelpos.PosTransactionFields.Companion.DEVICE_TYPE
import app.rht.petrolcard.utils.fuelpos.models.*
import java.math.BigDecimal
import java.math.RoundingMode
import java.text.DecimalFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import kotlin.collections.ArrayList
import kotlin.math.pow
import kotlin.math.roundToInt

const val ACTION_FUEL_POS_REPLY = "action.fuel_pos_reply"
const val FUEL_POS_REPLY = "fuel_pos_reply"

const val ACTION_FUEL_POS_MESSAGE = "action.fuel_pos_message"
const val FUEL_POS_MESSAGE = "fuel_pos_message"

const val ACTION_FUEL_POS_CLIENT_STATE = "action.fuel_pos_connection_state"
const val FUEL_POS_CLIENT_STATE = "fuel_pos_connection_state"

const val ACTION_FUEL_POS_SERVER_STATE = "action.fuel_pos_server_state"
const val FUEL_POS_SERVER_STATE = "fuel_pos_server_state"

const val ACTION_POS_MESSAGE = "action.pos_message"
const val POS_MESSAGE = "pos_message"

const val ACTION_FUEL_POS_FILLING_STATE = "action.fuel_pos_filling_state"
const val FUEL_POS_FILLING_STATE = "fuel_pos_filling_state"


fun toFuelPosValue(value:String, n:Int): Int {
    val number = value.toDouble()
    return  ((number * 10.0.pow(n.toDouble())).roundToInt())
}

fun toDecimalValue(value:String, n:Int): Double {
    //val input = value.replace(",",".")
    return if(value.isNotEmpty()){
        val number = value.toDouble()

        val result = (number/10.0.pow(n.toDouble()))
        try {
            val decPlaces = "#".repeat(n)
            val df = DecimalFormat("#.$decPlaces")
            df.roundingMode = RoundingMode.FLOOR
            df.format(result).toDouble()
        } catch (e: Exception){
            //e.printStackTrace()
            result
        }
    }
    else {
        0.0
    }
}
fun Double.decimal(digits: Int) = "%.${digits}f".format(this.toString().replace(",",".").toDouble())
fun String.decimal(digits: Int) = "%.${digits}f".format(this.replace(",",".").toDouble())


fun addDecimalPlaces(value:String,n:Int): String {
    val number = value.toDouble()
    return ((number * 10.0.pow(n.toDouble())).roundToInt() / 10.0.pow(n.toDouble())).toString()
}
fun addDecimalPlaces(value:Double,n:Int): Double {
    return ((value * 10.0.pow(n.toDouble())).roundToInt() / 10.0.pow(n.toDouble()))
}

fun getDecimalValue(value:String, decimal:Int):Double {
    return if(value.length>decimal)
    {
        val unscaled = BigDecimal(value)
        val scaled: BigDecimal = unscaled.scaleByPowerOfTen(-decimal)
        println(scaled)
        scaled.toDouble()
    }
    else
    {
        //("0.$value").toDouble()
        value.toDouble()
    }
}

fun getDecimalValue(value:Double, decimal:Int) : Double {
    val newVal = String.format("%.${decimal}f", value).toDouble()
    return newVal
}

class FuelPosServerState{
    companion object {
        const val STARTED = "1"
        const val STOPPED = "0"
        const val CLIENT_CONNECTED = "3"
        const val CLIENT_DISCONNECTED = "4"
    }
}
class FuelPosClientState{
    companion object {
        const val CONNECTED = "1"
        const val CONNECTING = "2"
        const val DISCONNECTED = "3"
    }
}

class FuelPosCommands {
    companion object {

        const val LOGON = "LOGON"

        const val RESERVE_PUMP = "RESERVEPUMP"

        const val RELEASE_PUMP = "RELEASEPUMP"
        const val START_PUMP = "STARTPUMP"
        const val DELETE_TRANSACTION = "DELETETRANSACTION"

        const val FORECOURT_STATUS_REQUEST = "FORECOURTSTATUSREQUEST"

        const val LOGIN = "LOGIN"

        private const val CLRF = "\r\n"

        @JvmStatic fun logOn(username: String, password: String): String {
            return "$LOGON 200 $username $password$CLRF"
        }

        /**No use for now**/
        @JvmStatic fun reservePump (
            request: PumpReserveRequest
        ) : String {
            //RESERVEPUMP TRANSACTION_TOKEN:010123456789|PUMP_NUMBER:1|012:;7077123456=9912?|089:English language selected|089:Customer selected option 3
            //RESERVEPUMP TRANSACTION_TOKEN:010123456789|PUMP_NUMBER:1|TRANSACTION_REQ:2|LOYALTY_IDENTIFIER:0000|PRODUCT:1|AMOUNT:5|CURRENCY:SAR|IP:*************|PORT:7778|
            var command = "$RESERVE_PUMP $TRANSACTION_TOKEN:${request.trxToken}|"
            command += "$PUMP_NUMBER:${request.pumpNumber}|"
            command += "$TRANSACTION_REQ:2|"
            command += "$LOYALTY_IDENTIFIER:${request.pinNumber}|"
            command += "$ARTICLE_CODE:${request.product}|"
            command += "$AMOUNT:${request.amount}|"
            command += "$CURRENCY_CODE:${request.currency}|"

            if(request.ip.isNotEmpty())
                command += "$IP_ADDRESS:${request.ip}|"

            if(request.port.isNotEmpty())
                command += "$PORT_NUMBER:${request.port}|"

            if(request.journalText.isNotEmpty())
                command += "$JOURNAL_TEXT:${request.journalText}|"

            return command+ CLRF
        }

        @JvmStatic fun releasePump(
            request: PumpReleaseRequest
        ) : String {
            //RELEASEPUMP TRX_TOKEN:011234000001|PUMP:2|COMPLETION_CODE:0|AMOUNT:1000|CURRENCY:JOD|TRX_DATA_REQ:2|IP:***************|PORT:7778|CARD_APP_TYPE:01|DEVICE_CODE:01|CARD_APP_NAME:PREPAY CARD|
            var command = "$RELEASE_PUMP $TRANSACTION_TOKEN:${request.trxToken}|"
            command += "$PUMP_NUMBER:${request.pumpNumber}|"

            command += "$COMPLETION_CODE:0|"
            command += "$AMOUNT:${request.amount}|"
            command += "$CURRENCY_CODE:${request.currency}|"
            command += "$TRANSACTION_REQ:2|"

            if(request.ip.isNotEmpty())
                command += "$IP_ADDRESS:${request.ip}|"

            if(request.port.isNotEmpty())
                command += "$PORT_NUMBER:${request.port}|"

            command += "$PRESERVE_AMOUNT:1|" //stop pump exactly on entered amount

            command += "$LOYALTY_IDENTIFIER:${request.pinNumber}|"
            command += "$CARD_APP_TYPE:${request.paymentTypeId}|"
            command += "$DEVICE_CODE:01|"
            command += "$CARD_APP_NAME:${request.paymentType}|"
            command += "$ARTICLE_CODE:${request.product}|"

            if(request.journalText.isNotEmpty())
                command += "$JOURNAL_TEXT:${request.journalText}|"

            return command+ CLRF
        }

        /**No use for now**/
        @JvmStatic fun startPump(
            trxToken:String,
            completionCode: Int = 0, //0 = start fueling if pump released //91 = cancel trx
        ) : String {
            //STARTPUMP 001:010123456789|006:0|042:12345|045:1234|
            return "$START_PUMP $TRANSACTION_TOKEN:$trxToken|$COMPLETION_CODE:$completionCode|$CLRF"
        }

        @JvmStatic fun deleteTransaction(
            trxToken:String
        ) : String {
            //DELETETRANSACTION 001:011234000001|
            return "$DELETE_TRANSACTION $TRANSACTION_TOKEN:$trxToken|$CLRF"
        }

        @JvmStatic fun forecourtStatus() : String {
            return FORECOURT_STATUS_REQUEST
        }

        //region pos protocol commands
        private val CLAIM_TRANSACTION = "CLAIMTRANSACTION"
        private val CLEAR_TRANSACTION = "CLEARTRANSACTION"
        private val UNCLAIM_TRANSACTION = "UNCLAIMTRANSACTION"
        @JvmStatic fun posLogOn(username: String, password: String,terminal:String): String {
            //return "LOGIN 1:0|99990:EXTPOS_1|99991:8E493E2DE|99992:EXTPOS1|99993:TPEBTEST|99994:1|\r\n"
            //return "$LOGIN 1:0|99990:$username|99991:$password|99992:EXTPOS1|99993:TPEBTEST|99994:1|$CLRF"
            return "$LOGIN 1:0|99990:$username|99991:$password|99992:TERMINAL|99993:$terminal|99994:1|$CLRF"
        }

        @JvmStatic fun posHeartbeat(): String {
            return "HEARTBEAT 015:1|$CLRF"
        }

        @JvmStatic fun claimTransaction(/*msgSeq:String, */pumpNumber: String, fillingId:String): String
        {
            val msgSeq = getNewSeqToken()
            return "$CLAIM_TRANSACTION $MSG_SEQ:$msgSeq|${PosProtocolFields.PUMP_NUMBER}:$pumpNumber|${PosProtocolFields.FILLING_ID}:$fillingId|$CLRF"
        }
        @JvmStatic fun unclaimTransaction(/*msgSeq:String,*/ pumpNumber: String, fillingId:String): String
        {
            val msgSeq = getNewSeqToken()
            return "$UNCLAIM_TRANSACTION $MSG_SEQ:$msgSeq|${PosProtocolFields.PUMP_NUMBER}:$pumpNumber|${PosProtocolFields.FILLING_ID}:$fillingId|$CLRF"
        }
        @JvmStatic fun clearTransaction(/*msgSeq:String,*/ pumpNumber: String, fillingId:String): String
        {
            val msgSeq = getNewSeqToken()
            return "$CLEAR_TRANSACTION $MSG_SEQ:$msgSeq|${PosProtocolFields.PUMP_NUMBER}:$pumpNumber|${PosProtocolFields.FILLING_ID}:$fillingId|$CLRF"
        }

        @JvmStatic fun clearTransaction(/*msgSeq:String,*/ pumpNumber: String, fillingId:String,paymentMode:String,paymentName:String): String
        {
            val msgSeq = getNewSeqToken()
            return "$CLEAR_TRANSACTION $MSG_SEQ:$msgSeq|${PosProtocolFields.PUMP_NUMBER}:$pumpNumber|${PosProtocolFields.FILLING_ID}:$fillingId|${PosProtocolFields.PAYMENT_MODE}:$paymentMode|${PosProtocolFields.PAYMENT_NAME}:$paymentName|$CLRF"
        }


        @JvmStatic fun forecourtStatusRequest(): String {
            return "$FORECOURT_STATUS_REQUEST$CLRF"
        }

        //endregion
    }
}

class FuelPosReply {
    companion object {
        const val PUMP_RESERVED = "PUMPRESERVED"
        const val PUMP_RELEASED = "PUMPRELEASED"
        const val PUMP_STARTED = "PUMPSTARTED"
        const val TRANSACTION_DATA = "TRANSACTIONDATA"
        const val DELETED = "DELETED"
        const val FORECOURT_STATUS_RESPONSE = "FORECOURTSTATUSRESPONSE"

        @JvmStatic fun getLogInStatus(message:String) : Boolean {
            return if(message.isNotEmpty()) {
                val msg = message.split(" ")
                val status = msg[0]
                status == LogonResponse.SUCCESS
            } else
                false
        }

        @JvmStatic fun getPumpReleaseMessage(message:String) : PumpReleased? {
            val msg = message.replace(PUMP_RELEASED,"").replace(" ","")
            val fields = getFields(msg)
            val item = PumpReleased()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            TRANSACTION_TOKEN -> item.trxToken = field.data
                            PUMP_NUMBER -> item.pump = field.data
                            COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }

        @JvmStatic fun getPumpReservedMessage(message:String) : PumpReserved? {
            val msg = message.replace(PUMP_RESERVED,"").replace(" ","")
            val fields = getFields(msg)
            val item = PumpReserved()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            TRANSACTION_TOKEN -> item.trxToken = field.data
                            PUMP_NUMBER -> item.pump = field.data
                            COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }

        @JvmStatic fun getPumpStartedMessage(message:String) : PumpStarted? {
            val msg = message.replace(PUMP_STARTED,"").replace(" ","")
            val fields = getFields(msg)
            val item = PumpStarted()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            TRANSACTION_TOKEN -> item.trxToken = field.data
                            COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }

        @JvmStatic fun getTransactionDataMessage(message:String) : TransactionData? {
            val msg = message.replace(TRANSACTION_DATA,"").replace(" ","")
            val fields = getFields(msg)
            val item = TransactionData()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            TRANSACTION_TOKEN -> item.trxToken = field.data
                            PUMP_NUMBER -> item.pump = field.data
                            COMPLETION_CODE -> item.completionCode = field.data
                            CURRENCY_CODE -> item.currency = field.data
                            TRANSACTION_CHECKSUM -> item.checksum = field.data
                            TRANSACTION_DATE -> item.dateTime = field.data
                            ARTICLE_NAME -> item.productName = field.data
                            ARTICLE_CODE -> item.productCode = field.data
                            QUANTITY_LTR -> item.quantity = field.data
                            UNIT_PRICE -> item.unitPrice = field.data
                            VAT_PERCENTAGE -> item.vatPercentage = field.data
                            VAT_AMOUNT -> item.vatAmount = field.data
                            TOTAL_AMOUNT -> item.totalAmount = field.data
                        }
                    }
                }
                return item
            }
            return null
        }


        @RequiresApi(Build.VERSION_CODES.O)
        private fun getDate(date:String) : LocalDate {
            val localDate = LocalDate.parse(date, DateTimeFormatter.ofPattern("yyyyMMddhhmmss"))
            return localDate ?: LocalDate.parse(date)
        }

        private fun getFields(message:String) : List<String> {
            return if(message.contains("|")){
                message.split("|")
            } else
                listOf()
        }

        private fun getField(field:String) : Field? {
            return if(field.contains(":")){
                val data = field.split(":")
                Field(data[0],data[1])
            } else
                null
        }

        /*@JvmStatic fun getDecimalValue(value:String, decimal:Int):Double {
            return if(value.length>decimal)
            {
                val unscaled = BigDecimal(value)
                val scaled: BigDecimal = unscaled.scaleByPowerOfTen(-decimal)
                println(scaled)
                scaled.toDouble()
                *//*val sub = amount.substring(0, amount.length-3)
                val sub2 = amount.substring(amount.length-3, amount.length)
                "$sub.$sub2".toDouble()*//*
            }
            else
            {
                ("0.$value").toDouble()
            }
        }*/

        @JvmStatic fun getForecourtStatus(message: String) : ForeCourtStatus {
            val status = ForeCourtStatus()

            val msg = message.replace(FORECOURT_STATUS_RESPONSE,"").replace(" ","")
            val fields = getFields(msg)

            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            COMPLETION_CODE -> status.completionCode = field.data
                            OPERATING_MODE -> status.operatingMode = field.data
                            POWER_SUPPLY_STATUS -> status.powerSupplyStatus = field.data
                            PUMP_INFO -> {
                                val info = getPumpInfoField(field.data)
                                if(info!=null)
                                    status.pumpsStatus.add(info)
                            }
                        }
                    }
                }
            }
            return status
        }

        private fun getPumpInfoField(info:String) : PumpStatus? {
            if(info.isNotEmpty())
            {
                val pumpStatus = PumpStatus()
                val values = info.split(",")
                values.forEachIndexed { index, element ->
                    when(index){
                        0 -> pumpStatus.pump = element
                        1 -> pumpStatus.state = element
                        2 -> {
                            when(element){
                                "1" -> pumpStatus.releaseType = PumpReleaseType.ExternalPOS
                                "2" -> pumpStatus.releaseType = PumpReleaseType.SelfService
                                "3" -> pumpStatus.releaseType = PumpReleaseType.Manual
                                "4" -> pumpStatus.releaseType = PumpReleaseType.Neutralization
                                "5" -> pumpStatus.releaseType = PumpReleaseType.OutdoorPaymentTerminal
                                "6" -> pumpStatus.releaseType = PumpReleaseType.BNA
                                "7" -> pumpStatus.releaseType = PumpReleaseType.Prepayment
                                "8" -> pumpStatus.releaseType = PumpReleaseType.EPR_OPT
                            }
                        }
                        3 -> pumpStatus.nozzle = element
                    }
                }
                return pumpStatus
            }
            return null
        }

        //region pos protocol methods
        const val LOGIN_RESULT = "LOGINRESULT"
        const val FILLING_STATES = "FILLINGSTATES"
        const val PUMP_STATES = "PUMPSTATES"
        const val CLAIM_TRANSACTION_RESULT = "CLAIMTRANSACTIONRESULT"
        const val REJECTED_CLAIMTRANSACTION = "REJECTED CLAIMTRANSACTION"
        const val UNCLAIME_TRANSACTION_RESULT = "UNCLAIMETRANSACTIONRESULT"
        const val CLEAR_TRANSACTION_RESULT = "CLEARTRANSACTIONRESULT"
        @JvmStatic fun getPosTransactions(message: String) : ArrayList<PosTransaction>{

            val transactions = ArrayList<PosTransaction>()
            println("transactions: $transactions")
            println("transactions size: ${transactions.size}")


            val msg = message.replace(FILLING_STATES,"").replace(" ","")
            //"FILLINGSTATES 1:1|200,1,1:3|201,1,1:6520|202,1,1:326|203,1,1:20000|204,1,1:2000|205,1,1:1|206,1,1:1|208,1,1:1|209,1,1:411|210,1,1:1|213,1,1:19ABDEF7|214,1,1:1|200,1,2:3|201,1,2:14340|202,1,2:717|203,1,2:20000|204,1,2:2000|205,1,2:1|206,1,2:1|208,1,2:1|209,1,2:413|210,1,2:1|213,1,2:52F106A7|214,1,2:1|200,2,1:3|201,2,1:7200|202,2,1:360|203,2,1:20000|204,2,1:2000|205,2,1:1|206,2,1:1|208,2,1:1|209,2,1:412|210,2,1:1|213,2,1:62C662BF|214,2,1:1|\n"

            val mainFields = msg.split("|", limit = 2)

            val sequence = getField(mainFields[0])!!.data
            println("Message Sequence: $sequence")

            if(mainFields.size==2){
                val delim = "|200,"
                val list = mainFields[1].split(delim).toMutableList()
                list.forEachIndexed { index, s -> if(index>0) list[index] = "200,$s" }

                println("ITEMS $list")

                for(trx in list){
                    val trxFields = getFields(trx)
                    val transaction = PosTransaction()
                    for(field in trxFields) {
                        if(field.isNotEmpty()) {
                            try {
                                val fieldChild = field.split(",")  //[0] index //[1] (pp) parameter for pump //[2]: parameter for trx
                                when(fieldChild[0]){
                                    DEVICE_TYPE -> {
                                        transaction.messageSequence = sequence
                                        transaction.pumpNumber = fieldChild[1]
                                        transaction.deviceType = fieldChild[2].split(":")[1]
                                    }
                                    PosTransactionFields.FILLING_AMOUNT -> { transaction.fillingAmount = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.FILLING_VOLUME -> { transaction.fillingVolume = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.UNIT_PRICE -> { transaction.unitPrice = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.DEFAULT_UNIT_PRICE -> { transaction.defaultUnitPrice = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.PRICE_SELECTION -> { transaction.priceSelection = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.FILLING_STATE -> { transaction.fillingState = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.CLAIM_DEVICE_TYPE -> { transaction.claimDeviceType = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.FILLING_FUEL_NUMBER -> { transaction.fillingFuelNumber = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.FILLING_ID -> { transaction.fillingId = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.UNIT_OF_MESURE -> { transaction.unitMeasure = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.FILLING_TAG -> { transaction.fillingTag = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.EXT_POS_DEVICE_NUMBER -> { transaction.extPosDeviceNumber = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.TRANSACTION_CHECKSUM -> { transaction.trxChecksum = getFieldValue(fieldChild[2]) }
                                    PosTransactionFields.NOZZLE_NUMBER -> { transaction.nozzleNumber = getFieldValue(fieldChild[2]) }
                                }
                            } catch (e:Exception) {
                                e.printStackTrace()
                            }
                            transactions.add(transaction)
                        }
                    }

                }
            }

            return transactions
        }
        @JvmStatic fun getPosPumpStates(message: String) : ArrayList<PosPumpState>{
            val pumpStates = ArrayList<PosPumpState>()
            val msg = message.replace(PUMP_STATES,"").replace(" ","")
            //"PUMPSTATES 1:46|100,1:7|115,1:0|116,1:3|117,1:2|100,2:7|115,2:0|116,2:3|117,2:2|100,3:7|115,3:0|116,3:3|117,3:2|100,4:7|115,4:0|116,4:3|117,4:2|"

            val mainFields = msg.split("|", limit = 2)

            val sequence = getField(mainFields[0])!!.data
            println("Message Sequence: $sequence")

            if(mainFields.size==2){
                val delim = "|100,"
                val list = mainFields[1].split(delim).toMutableList()
                list.forEachIndexed { index, s -> if(index>0) list[index] = "100,$s" }

                println("ITEMS $list")

                for(trx in list){
                    val trxFields = getFields(trx)
                    val item = PosPumpState()
                    for(field in trxFields) {
                        if(field.isNotEmpty()) {
                            try {
                                val fieldChild = field.split(",")  //[0] index //[1] (pp) parameter for pump //[2]: parameter for trx
                                when(fieldChild[0]){
                                    PosPumpStateFields.PUMP_STATE -> {
                                        val raw =  fieldChild[1].split(":")
                                        item.pumpNumber = raw[0]
                                        item.pumpState = raw[1]
                                    }
                                    PosPumpStateFields.NOZZLE_NUMBER -> { item.nozzleNumber  = getFieldValue(fieldChild[1]) }
                                    PosPumpStateFields.FUEL_NUMBER -> { item.fuelNumber  = getFieldValue(fieldChild[1]) }
                                    PosPumpStateFields.MONITOR_FILLING_AMT -> { item.fillingAmount = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.MONITOR_FILLING_VOL -> { item.fillingVolume = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.FILLING_UNIT_PRICE -> { item.unitPrice = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.PRICE_SELECTION -> { item.priceSelection = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.PAYABLE_FILLING -> { item.payableFilling = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.CLAIMED_FILLING -> { item.claimedFilling = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.PUMP_ERROR -> { item.pumpError = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.NOZZLE_ERROR -> { item.nozzleError = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.FUEL_ERROR -> { item.fuelError = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.FUEL_ERROR_PRICE_SLECTION -> { item.fuelPriceError = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.NOZZLE_BLOCKED -> { item.nozzleBlocked  = getFieldValue(fieldChild[1]) }
                                    PosPumpStateFields.RELEASE_DEVIDE_TYPE -> { item.deviceType = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.UNIT_OF_MESURE -> { item.unitMeasure = getFieldValue(fieldChild[1])  }
                                    PosPumpStateFields.RELEASE_TAG -> { item.releaseTag = getFieldValue(fieldChild[1])  }
                                }
                            } catch (e:Exception) {
                                e.printStackTrace()
                            }
                        }
                    }
                    pumpStates.add(item)
                }
            }

            return pumpStates
        }

        private fun getFieldValue(field:String) : String {
            val raw = field.split(":")
            if(raw.size>1)
               return field.split(":")[1]
            return ""
        }

        @JvmStatic fun getLoginResult(message: String) : LoginResult? {
            val msg = message.replace(LOGIN_RESULT,"").replace(" ","")
            val fields = getFields(msg)
            val item = LoginResult()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            PosProtocolFields.MSG_SEQ -> item.messageSequence = field.data
                            PosProtocolFields.COMPLETION_CODE -> item.completionCode = field.data
                            PosLoginFields.MSG_INTERFACE_VERSION -> item.messageInterfaceVersion = field.data
                        }
                    }
                }
                return item
            }
            return null
        }
        @JvmStatic fun getUnclaimTransactionResult(message: String) : UnclaimedTransactionResult? {
            val msg = message.replace(UNCLAIME_TRANSACTION_RESULT,"").replace(" ","")
            val fields = getFields(msg)
            val item = UnclaimedTransactionResult()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            PosProtocolFields.MSG_SEQ -> item.messageSequence = field.data
                            PosProtocolFields.PUMP_NUMBER -> item.pumpNumber = field.data
                            PosProtocolFields.FILLING_ID -> item.fillingId = field.data
                            PosProtocolFields.COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }
        @JvmStatic fun getRejectedClaimTransactionResult(message: String) : ClaimedTransactionResult? {
            val msg = message.replace(REJECTED_CLAIMTRANSACTION,"").replace(" ","")
            val fields = getFields(msg)
            val item = ClaimedTransactionResult()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            PosProtocolFields.MSG_SEQ -> item.messageSequence = field.data
                            PosProtocolFields.PUMP_NUMBER -> item.pumpNumber = field.data
                            PosProtocolFields.FILLING_ID -> item.fillingId = field.data
                            PosProtocolFields.COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }
        @JvmStatic fun getClaimTransactionResult(message: String) : ClaimedTransactionResult? {
            val msg = message.replace(CLAIM_TRANSACTION_RESULT,"").replace(" ","")
            val fields = getFields(msg)
            val item = ClaimedTransactionResult()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            PosProtocolFields.MSG_SEQ -> item.messageSequence = field.data
                            PosProtocolFields.PUMP_NUMBER -> item.pumpNumber = field.data
                            PosProtocolFields.FILLING_ID -> item.fillingId = field.data
                            PosProtocolFields.COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }
        @JvmStatic fun getClearTransactionResult(message: String) : ClearTransactionResult? {
            val msg = message.replace(CLEAR_TRANSACTION_RESULT,"").replace(" ","")
            val fields = getFields(msg)
            val item = ClearTransactionResult()
            if(fields.isNotEmpty()){
                for(rawField in fields){
                    val field = getField(rawField)
                    if(field!=null){
                        when(field.id){
                            PosProtocolFields.MSG_SEQ -> item.messageSequence = field.data
                            PosProtocolFields.PUMP_NUMBER -> item.pumpNumber = field.data
                            PosProtocolFields.FILLING_ID -> item.fillingId = field.data
                            PosProtocolFields.COMPLETION_CODE -> item.completionCode = field.data
                        }
                    }
                }
                return item
            }
            return null
        }
        //endregion
    }
}

class FuelPosField {
    companion object {
        const val TRANSACTION_TOKEN = "001"
        const val PUMP_NUMBER = "002"
        const val TRANSACTION_REQ = "003"
        const val IP_ADDRESS = "004"
        const val PORT_NUMBER = "005"
        const val COMPLETION_CODE = "006"
        const val PROGRESS_CODE = "007"
        const val AMOUNT = "008"
        const val CURRENCY_CODE = "009"
        const val TRANSACTION_CHECKSUM = "010"

        const val LOYALTY_IDENTIFIER = "024"
        const val CARD_APP_TYPE = "030"
        const val CARD_APP_NAME = "032"
        const val DEVICE_CODE = "033"
        const val PRESERVE_AMOUNT = "034"

        const val TRANSACTION_DATE = "060"
        const val ARTICLE_NAME = "061"
        const val ARTICLE_CODE = "062"
        const val QUANTITY_LTR = "063"
        const val UNIT_PRICE = "064"
        const val VAT_PERCENTAGE = "066"
        const val VAT_AMOUNT = "067"
        const val TOTAL_AMOUNT = "069"

        const val OPERATING_MODE = "075"
        const val POWER_SUPPLY_STATUS = "076"
        const val MODEL_INFO = "077"
        const val SOFTWARE_INFO = "078"
        const val PUMP_INFO = "079" //separated by a subfield separator (‘,’)
        const val JOURNAL_TEXT = "089"
        const val SYSTEM_INFO = "090"
    }
}

class LogonResponse {
    companion object {
        const val SUCCESS = "230"
        const val ACCESS_DENIED = "530"
    }
}

class CompletionCode {
    companion object {
        const val OK = 0
        const val SYSTEM_FAILURE = 1
        const val POWER_DOWN = 2
        const val PUMP_NOT_CONFIGURED = 21
        const val PUMP_BUSY = 22
        const val PUMP_NOT_AVAILABLE = 23
        const val PUMP_NOT_EPR = 24
        const val PUMP_STOPPED = 25
        const val PUMP_INACTIVE = 26
        const val PUMP_RESERVE_TIMEOUT = 27
        const val NOTHING_STOP = 28
        const val NOZZLE_LIFT_TIMEOUT = 29
        const val INVALID_MESSAGE = 41
        const val INVALID_LOYALTY_CARD = 42
        const val INVALID_PAYMENT_CARD = 43
        const val INVALID_CURRENCY_CODE = 44
        const val INVALID_MAX_AMOUNT = 45
        const val INVALID_ITEM_CODE = 46
        const val ITEM_INFO_NOT_AVAILABLE = 47
        const val PRODUCT_NOT_ALLOWED_DISPENSING = 74 //Product not allowed by SYSDEF or Fuel POS dispensing
        const val ONLINE_AUTH_FAILED = 80
        const val ONLINE_AUTH_REFUSED = 81
        const val CANCELED_TRX = 91
        const val AMOUNT_TOO_LOW = 95
        const val AMOUNT_TOO_HIGH = 96
        const val CURRENCY_MISMATCH = 97

        @JvmStatic fun getMessage(code: String):String {
            return when(code.toInt()){
                OK -> "OK"
                SYSTEM_FAILURE -> "System Failure"
                POWER_DOWN -> "Power down"
                PUMP_NOT_CONFIGURED -> "Pump Not configured"
                PUMP_BUSY -> "Pump is busy (OPT, BNA, EPR, SelfService...)"
                PUMP_NOT_AVAILABLE -> "Pump Not available (out of order, not connected)"
                PUMP_NOT_EPR -> "Requested pump not in EPR mode"
                PUMP_STOPPED -> "Pump stopped"
                PUMP_INACTIVE -> "Invalid pump mode active"
                PUMP_RESERVE_TIMEOUT -> "Pump Reserve timeout"
                NOTHING_STOP -> "Nothing to stop"
                NOZZLE_LIFT_TIMEOUT -> "Nozzle lift timeout"
                INVALID_MESSAGE -> "Invalid Message"
                PRODUCT_NOT_ALLOWED_DISPENSING -> "Product not allowed by Fuel POS dispensing"
                INVALID_MAX_AMOUNT -> "Invalid amount"
                INVALID_CURRENCY_CODE -> "Invalid currency code"
                INVALID_ITEM_CODE -> "Invalid item code"
                ITEM_INFO_NOT_AVAILABLE -> "Item info not available"
                ONLINE_AUTH_FAILED -> "Online authorization failed"
                ONLINE_AUTH_REFUSED -> "Online authorization refused"
                CANCELED_TRX -> "Cancel transaction"
                CURRENCY_MISMATCH -> "Currency mismatch"
                AMOUNT_TOO_LOW -> "Amount too low"
                AMOUNT_TOO_HIGH -> "Amount too high"
                else -> "Error Code : $code"
            }
        }
    }
}

class ProgressCode {
    companion object {
        const val ONLINE_AUTH_STARTED = 1
        const val HOST_CONNECTED = 2
        const val HOST_TIMEOUT = 3
    }
}

class OperatingCode {
    companion object {
        const val DAY_MODE = 1
        const val NIGHT_MODE = 2
        const val DISABLED = 3
    }
}

class PowerSupplyStatus {
    companion object {
        const val OK = 0
        const val POWER_DOWN_DETECTED = 2
    }
}

class PumpState {
    companion object {
        const val NO_PUMP = "0"
        const val IDLE = "1"
        const val CONFIG_ERROR = "2"
        const val REQUESTING = "3"
        const val RESERVED = "4"
        const val RESERVED_REQ = "5"
        const val RELEASED = "6"
        const val AUTO_RELEASE = "7"
        const val FILLING = "8"
        const val BLOCKED = "9"
        const val STOPPED = "10"
        const val OFFLINE = "11"
        const val MINOR_ERROR = "12"
        const val MAJOR_ERROR = "13"
        const val SAFETY_IDLE = "14"
        const val SAFETY_REQ = "15"

        @JvmStatic fun getPumpState(code: String) : String {
            return when(code){
                NO_PUMP -> "No pump mode"
                IDLE -> "Pump idle"
                CONFIG_ERROR -> "Configuration error"
                REQUESTING -> "Requesting"
                RESERVED -> "Reserved"
                RESERVED_REQ -> "Reserved Requesting"
                RELEASED -> "Released"
                AUTO_RELEASE -> "Automatic Released"
                FILLING -> "Filling"
                BLOCKED -> "Blocked"
                STOPPED -> "Stopped"
                OFFLINE -> "Offline"
                MINOR_ERROR -> "Minor Error"
                MAJOR_ERROR -> "Major Error"
                SAFETY_IDLE -> "Safety Idle"
                SAFETY_REQ -> "Safety Requesting"
                else -> ""
            }
        }
    }
}

class EprPumpState {
    companion object {
        const val NO_PUMP = "0"
        const val IDLE = "1"
        const val CONFIG_ERROR = "2"
        const val REQUESTING = "3"
        const val RESERVED = "4"
        const val RESERVED_REQ = "5"
        const val RELEASED = "6"
        const val AUTO_RELEASE = "7"
        const val FILLING = "8"
        const val BLOCKED = "9"
        const val STOPPED = "10"
        const val OFFLINE = "11"
        const val MINOR_ERROR = "12"
        const val MAJOR_ERROR = "13"
        const val SAFETY_IDLE = "14"
        const val SAFETY_REQ = "15"

        @JvmStatic fun getEprPumpState(code: String) : String {
            return when(code){
                NO_PUMP -> "No pump mode"
                IDLE -> "Pump idle"
                CONFIG_ERROR -> "Configuration error"
                REQUESTING -> "Requesting"
                RESERVED -> "Reserved"
                RESERVED_REQ -> "Reserved Requesting"
                RELEASED -> "Released"
                AUTO_RELEASE -> "Automatic Released"
                FILLING -> "Filling"
                BLOCKED -> "Blocked"
                STOPPED -> "Stopped"
                OFFLINE -> "Offline"
                MINOR_ERROR -> "Minor Error"
                MAJOR_ERROR -> "Major Error"
                SAFETY_IDLE -> "Safety Idle"
                SAFETY_REQ -> "Safety Requesting"
                else -> ""
            }
        }
    }
}

class PumpDeviceType {
    companion object {
        const val SELF_SERVICE = 2
        const val EPR_OPT = 8
    }
}

class PosCompletionCode {
    companion object {
        const val OK = "0"
        const val REQUIRED_FIELD_MISSING = "1"
        const val UNKNOWN_FIELD = "2"
        const val FILLING_BUFFER_FULL = "3"
        const val NO_ALLOWED_FUEL = "4"
        const val INVALID_FUEL_NUMBER = "5"
        const val INVALID_PRICE_SELECTION = "6"
        const val INVALID_PUMP_NUMBER = "7"
        const val NOT_ALLOW_MODE = "8"
        const val FILLING_IN_PROGRESS = "9"
        const val FILLING_NOT_PAID = "10"
        const val INVALID_AMOUNT = "11"
        const val INVALID_VOLUME = "12"
        const val ALLOWED_FUEL_PRODUCT_NOT_VALID = "13"
        const val NOT_SUPPORTED = "14"
        const val CLAIMED_WITH_OTHER_TOKEN = "15"
        const val NO_FILLING = "16"
        const val FILLING_OPERATION_REFUSED = "17"
        const val REQUEST_REFUSED = "18"
        const val INVALID_PAYMENT_MODE_TYPE_NAME = "19"
        const val INVALID_LOYALTY_DISCOUNT_AMOUNT = "20"
        const val INVALID_LOYALTY_ID = "21"
        const val CHECKSUM_ERROR = "22"

        @JvmStatic fun getMessage(code: String):String {
            return when(code){
                OK -> "OK"
                REQUIRED_FIELD_MISSING -> "Mandatory field missing"
                UNKNOWN_FIELD -> "Unknown field"
                FILLING_BUFFER_FULL -> "All filling buffers occupied"
                NO_ALLOWED_FUEL -> "No allowed fuel"
                INVALID_FUEL_NUMBER -> "Invalid fuel number"
                INVALID_PRICE_SELECTION -> "Invalid price selection"
                INVALID_PUMP_NUMBER -> "Invalid pump number"
                NOT_ALLOW_MODE -> "Not allowed mode"
                FILLING_IN_PROGRESS -> "Filling in progress"
                FILLING_NOT_PAID -> "Filling not paid"
                INVALID_AMOUNT -> "Filling amount limit not valid"
                INVALID_VOLUME -> "Filling volume limit not valid"
                ALLOWED_FUEL_PRODUCT_NOT_VALID -> "Allowed fuel products not valid"
                NOT_SUPPORTED -> "Not supported"
                CLAIMED_WITH_OTHER_TOKEN -> "Claimed with other token"
                NO_FILLING -> "No filling"
                FILLING_OPERATION_REFUSED -> "Filling operation refused"
                REQUEST_REFUSED -> "Request refused"
                INVALID_PAYMENT_MODE_TYPE_NAME -> "Invalid payment mode type or name"
                INVALID_LOYALTY_DISCOUNT_AMOUNT -> "Invalid loyalty discount amount"
                INVALID_LOYALTY_ID -> "Invalid loyalty identification"
                CHECKSUM_ERROR -> "Checksum error"
                else -> "Error Code : $code"
            }
        }
    }
}
class PosClearTrxCompletionCode {
    companion object {
        const val OK = "00"
        const val REQUIRED_FIELD_MISSING = "01"
        const val UNKNOWN_FIELD = "02"
        const val INVALID_PUMP_NUMBER = "07"
        const val FILLING_NOT_PAID = "11"
        const val NO_FILLING = "16"
        const val FILLING_OPERATION_REFUSED = "17"
        const val INVALID_PAYMENT_MODE_TYPE_NAME = "19"
        const val INVALID_LOYALTY_DISCOUNT_AMOUNT = "20"
        const val CHECKSUM_ERROR = "21"

        @JvmStatic fun getMessage(code: String):String {
            return when(code){
                OK -> "OK"
                REQUIRED_FIELD_MISSING -> "Required field missing"
                UNKNOWN_FIELD -> "Unknown Field"
                INVALID_PUMP_NUMBER -> "Invalid Pump Number"
                FILLING_NOT_PAID -> "Filling not paid"
                NO_FILLING -> "No Filling"
                FILLING_OPERATION_REFUSED -> "Filling operation refused"
                INVALID_PAYMENT_MODE_TYPE_NAME -> "Invalid payment type / name"
                INVALID_LOYALTY_DISCOUNT_AMOUNT -> "Invalid loyalty discount amount"
                CHECKSUM_ERROR -> "Checksum Error"
                else -> "Error Code : $code"
            }
        }
    }
}
class PosProtocolFields {
    companion object {
        val MSG_SEQ = "1"
        val COMPLETION_CODE = "3"
        val PUMP_NUMBER = "4"
        val FILLING_ID = "5"
        val MAX_FILLING_AMT = "6"
        val MAX_FILLING_VOL = "7"
        val ALLOWED_FUEL_PRODUCT = "8" // 16 digits // 1111111111111111 meaning all products allowed. // 0100010000000000 Fuel products 2 and 6 are allowed.
        val APPLICATION_TAG = "9"
        val PAYMENT_MODE = "10"
        val PAYMENT_NAME = "11"
        val LOYALTY_AMT = "12"
        val LOYALTY_PAN = "13" //numeric, up to 30 characters
        val LOYALTY_NAME = "14" //Alphanumeric, up to 15 characters
        val CURRENT_STATUS_REQ = "15" // 0 No status update is requested. //1 Status update is requested.
        val VERIFICATION_CHECKSUM = "16"
    }
}
class PosPumpStateFields {
    companion object {
        //pump status fields
        val PUMP_STATE = "100" //100,pp (up to 2 digit leading with 0 allowed)
        val NOZZLE_NUMBER = "101" // 101,p (1 digit) // 0 unspecified nozzle
        val FUEL_NUMBER = "102" // 102,pp  // 00 unspecified product
        val MONITOR_FILLING_AMT = "103" //103,pp
        val MONITOR_FILLING_VOL = "104" //104,pp
        val FILLING_UNIT_PRICE = "105" //105,pp
        val DEFAULT_FILLING_UNIT_PRICE = "106" //106,pp
        val PRICE_SELECTION = "107" //106,pp //0 unknown
        val PAYABLE_FILLING = "108" //108,pp
        val CLAIMED_FILLING = "109" //109,pp
        val PUMP_ERROR = "110" //110,pp
        val NOZZLE_ERROR = "112" //112,pp // 1..6 nozzles , 1 digit
        val FUEL_ERROR = "113" // 113,pp // 2 digits leading with 0 allowed 01..16
        val FUEL_ERROR_PRICE_SLECTION = "114" // 1..8
        val NOZZLE_BLOCKED = "115" // 0 all okay, 1 One or more nozzles have a block active
        val RELEASE_DEVIDE_TYPE = "116"
        val PUMP_OPERATION_MODE = "117"
        val UNIT_OF_MESURE = "118"
        val RELEASE_TAG = "119"
    }
}
class PosTransactionFields {
    companion object {
        val DEVICE_TYPE = "200" // 200,pp,t
        val FILLING_AMOUNT = "201" // 201,pp,t
        val FILLING_VOLUME = "202"
        val UNIT_PRICE = "203"
        val DEFAULT_UNIT_PRICE = "204"
        val PRICE_SELECTION = "205"
        val FILLING_STATE = "206"
        val CLAIM_DEVICE_TYPE = "207"
        val FILLING_FUEL_NUMBER = "208"
        val FILLING_ID = "209"
        val UNIT_OF_MESURE = "210"
        val FILLING_TAG = "211"
        val EXT_POS_DEVICE_NUMBER = "212"
        val TRANSACTION_CHECKSUM = "213"
        val NOZZLE_NUMBER = "214"


    }
}

class PosDeviceType {
    companion object {
        var EXTPOS = "1"
        var FPOS_SS = "2"
        var FPOS_MANUAL = "3"
        var FPOS_NEUT = "4"
        var FPOS_OPT = "5"
        var FPOS_BNA = "6"
        var FPOS_PREPAY = "7"
    }
}

class PosFillingState {
    companion object {
        @JvmField var PAYABLE = "1"
        @JvmField var CLAIMED = "2"
        @JvmField var PAYABLE_AGAIN = "3"
        @JvmField var PAID = "4"
    }
}

class PosUnitMeasure {
    companion object {
        var PER_LTR = "1"
        val PER_KG = "2"
    }
}

class PosLoginFields {
    companion object {
        val USERNAME = "99990"
        val PASSWORD = "99991"
        val MANUFATURER = "99992"
        val DEVICE = "99993"
        val DEVICE_VERSION = "99994"
        val MSG_INTERFACE_VERSION = "99995"
    }
}

private var lastSeqToken = 1
private fun getNewSeqToken():Int {
    if(lastSeqToken >999)
        lastSeqToken = 0
    return lastSeqToken++
}
