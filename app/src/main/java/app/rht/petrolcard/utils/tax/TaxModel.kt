package app.rht.petrolcard.utils.tax

import android.os.Parcel
import android.os.Parcelable

class TaxModel(
    var taxType : Boolean, // true = inclusive // false = exculsive
    var taxPercentile : Double,
    var taxAmount : Double,
    var netAmount : Double,
    var totalAmount : Double,
) : Parcelable {
    constructor(parcel: Parcel) : this(
        parcel.readByte() != 0.toByte(),
        parcel.readDouble(),
        parcel.readDouble(),
        parcel.readDouble(),
        parcel.readDouble()
    )


    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeByte(if (taxType) 1 else 0)
        parcel.writeDouble(taxPercentile)
        parcel.writeDouble(taxAmount)
        parcel.writeDouble(netAmount)
        parcel.writeDouble(totalAmount)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<TaxModel> {
        override fun createFromParcel(parcel: Parcel): TaxModel {
            return TaxModel(parcel)
        }

        override fun newArray(size: Int): Array<TaxModel?> {
            return arrayOfNulls(size)
        }
    }
}