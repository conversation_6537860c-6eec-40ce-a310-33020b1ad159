package app.rht.petrolcard.utils.citizen

import android.content.Context
import android.graphics.Bitmap
import android.hardware.usb.UsbDevice
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.widget.Toast
import app.rht.petrolcard.R
import app.rht.petrolcard.utils.LogWriter
import com.citizen.sdk.ESCPOSConst
import com.citizen.sdk.ESCPOSPrinter

class CitizenPrint(val context: Context) {

    private val TAG =  CitizenPrint::class.simpleName

    private lateinit var printer: ESCPOSPrinter
    private var logWriter: LogWriter = LogWriter("CitizenPrintLogs")
    private var usbDevice: UsbDevice? = null
    private var result:Int = 0

    init {
        connectPrinter()
    }


    private fun connectPrinter() {
        printer = ESCPOSPrinter()
        printer.setContext(context)
        usbDevice = null
        result = printer.connect(ESCPOSConst.CMP_PORT_USB, usbDevice)

        /* if (!isConnected(context)) {
            Log.e(TAG, "USB not Connected");
        }
        else
        {
            Log.e(TAG, "Printer USB Connected");
        }*/
        if (ESCPOSConst.CMP_SUCCESS == result) {
            // Set encoding
            printer.setEncoding("ISO-8859-1")
            // Start Transaction ( Batch )
            printer.transactionPrint(ESCPOSConst.CMP_TP_TRANSACTION)
            Log.e("Max. Page area", "( x,y ) : " + printer.pageModeArea)

            // Direction set
            printer.pageModePrintDirection = ESCPOSConst.CMP_PD_TOP_TO_BOTTOM
            logWriter.appendLog(TAG, "CitizenPrint KT")

        } else {
            // Connect Error
            Log.e("Citizen_POS_sample1", "Connect or Printer Error : $result")
            logWriter.appendLog(
                TAG,
                "CitizenPrinter Connect error code: " + result + " message: " + showMessage(result)
            )

            Thread {
                Handler(Looper.getMainLooper()).post {
                    if (result != ESCPOSConst.CMP_E_CONNECTED) Toast.makeText(
                        context,
                        showMessage(result),
                        Toast.LENGTH_LONG
                    ).show()
                }
            }.start()
        }
    }
    private fun setPageMargin() {
        //printer.setPageModePrintArea( "40,0,250,0");
    }
    private fun formatText(inputText: String): String {
        return if (inputText.contains("\n\n")) """
     ${inputText.replace("\n", "")}
     
     
     """.trimIndent() else """
     ${inputText.replace("\n", "")}
     
     """.trimIndent()
        //else
        //    return inputText;
    }
    private fun getAlignment(type: AlignmentType): Int {
        return when (type) {
            AlignmentType.CENTER -> ESCPOSConst.CMP_ALIGNMENT_CENTER
            AlignmentType.RIGHT -> ESCPOSConst.CMP_ALIGNMENT_RIGHT
            else -> ESCPOSConst.CMP_ALIGNMENT_LEFT
        }
    }
    private fun showMessage(result: Int): String {
        val word: String = when (result) {
            ESCPOSConst.CMP_SUCCESS -> context.getString(R.string.the_operation_success)
            ESCPOSConst.CMP_E_CONNECTED -> context.getString(R.string.the_printer_is_already_connected)
            ESCPOSConst.CMP_E_DISCONNECT -> context.getString(R.string.the_printer_is_not_connected)
            ESCPOSConst.CMP_E_NOTCONNECT -> context.getString(R.string.failed_connection_to_printer)
            ESCPOSConst.CMP_E_CONNECT_NOTFOUND -> context.getString(R.string.failed_to_check_support_model)
            ESCPOSConst.CMP_E_CONNECT_OFFLINE -> context.getString(R.string.failed_to_check_printer_status)
            ESCPOSConst.CMP_E_ILLEGAL -> context.getString(R.string.unsupported_operation_with_this_device)
            ESCPOSConst.CMP_E_OFFLINE -> context.getString(R.string.the_printer_is_offline)
            ESCPOSConst.CMP_E_NOEXIST -> context.getString(R.string.file_name_does_not_exist)
            ESCPOSConst.CMP_E_FAILURE -> context.getString(R.string.the_service_cannot_perform)
            ESCPOSConst.CMP_E_TIMEOUT -> context.getString(R.string.server_timed_out_printer)
            ESCPOSConst.CMP_E_NO_LIST -> context.getString(R.string.printer_not_found)
            ESCPOSConst.CMP_EPTR_COVER_OPEN -> context.getString(R.string.printer_cover_open)
            ESCPOSConst.CMP_EPTR_REC_EMPTY -> context.getString(R.string.printer_out_of_paper)
            ESCPOSConst.CMP_EPTR_BADFORMAT -> context.getString(R.string.specified_file_unsupported_format)
            ESCPOSConst.CMP_EPTR_TOOBIG -> context.getString(R.string.specified_bitmap_is_too_big)
            else -> context.getString(R.string.other_error)
        }
        return word
    }

    private fun text(text: String, alignment: AlignmentType) {
        setPageMargin()
        printer.printText(
            formatText(text),
            getAlignment(alignment), ESCPOSConst.CMP_FNT_DEFAULT,
            ESCPOSConst.CMP_TXT_1WIDTH or ESCPOSConst.CMP_TXT_1HEIGHT
        )
    }
    private fun boldText(text: String, alignment: AlignmentType) {
        setPageMargin()
        printer.printText(
            formatText(text),
            getAlignment(alignment), ESCPOSConst.CMP_FNT_BOLD,
            ESCPOSConst.CMP_TXT_1WIDTH or ESCPOSConst.CMP_TXT_1HEIGHT
        )
    }
    private fun text2(text: String, alignment: AlignmentType) {
        setPageMargin()
        printer.printText(
            formatText(text),
            getAlignment(alignment),
            ESCPOSConst.CMP_FNT_DEFAULT,
            ESCPOSConst.CMP_TXT_2WIDTH or ESCPOSConst.CMP_TXT_2HEIGHT
        )
    }
    private fun boldText2(text: String, alignment: AlignmentType) {
        setPageMargin()
        printer.printText(
            formatText(text),
            getAlignment(alignment),
            ESCPOSConst.CMP_FNT_BOLD,
            ESCPOSConst.CMP_TXT_2WIDTH or ESCPOSConst.CMP_TXT_2HEIGHT
        )
    }
    private fun qrCode(text: String, alignment: AlignmentType) {
        setPageMargin()
        // Print QRcode
        printer.printQRCode(text, 6, ESCPOSConst.CMP_QRCODE_EC_LEVEL_L, getAlignment(alignment))
    }

    private fun printBitmap(bitmap:Bitmap, alignment: AlignmentType)
    {
        setPageMargin()
        printer.printBitmap(bitmap, getAlignment(alignment))
    }


    private fun printBitmap(bitmap:Bitmap, width:Int, alignment: AlignmentType)
    {
        setPageMargin()
        val result =   printer.printBitmap(bitmap, width, getAlignment(alignment))
        Log.e(TAG, "PRINT BITMAP Result: $result")
    }

    private fun printBitmap(bitmap:ByteArray, alignment: AlignmentType)
    {
        setPageMargin()
        printer.printBitmap(bitmap, getAlignment(alignment))
    }

    private fun printBitmap(bitmap:ByteArray, width:Int, alignment: AlignmentType)
    {
        setPageMargin()
        val result =   printer.printBitmap(bitmap, width, getAlignment(alignment))
        Log.e(TAG, "PRINT BITMAP Result: $result")
    }

    fun print(commands: List<PrintCmd>) {
        for (cmd in commands) {
            val text = if(cmd.text!=null) cmd.text else ""
            val size = cmd.size
            val bold = cmd.isBold
            val alignment = cmd.alignment
            val contentType = cmd.contentType
            if(contentType == PrintContentType.TEXT)
            {
                if (size == 1 && bold) boldText(text, alignment) else if (size == 1 && !bold)
                    text(text, alignment)
                else if (size == 2 && bold) boldText2(
                    text,
                    alignment
                ) else if (size == 2 && !bold) text2(
                    text, alignment
                )
            }
            else if(contentType == PrintContentType.IMAGE){
                if(cmd.bitmap!=null) {
                    printBitmap(cmd.bitmap, cmd.width, cmd.alignment)
                }
                else if(cmd.bitmapArray.isNotEmpty())
                {
                    printBitmap(cmd.bitmapArray, cmd.width, cmd.alignment)
                }
                else
                {
                    Log.e(TAG,"Bitmap: "+(cmd.bitmap!=null))
                }

            }
            else
            {
                Log.e(TAG, "Print content type: ${contentType.name}")
            }

        }
        text(" ", AlignmentType.LEFT)

        //if(!BuildConfig.DEBUG) {
            // Partial Cut with Pre-Feed
            printer.cutPaper(ESCPOSConst.CMP_CUT_PARTIAL_PREFEED)
        //}

        // End Transaction ( Batch )
        val result = printer.transactionPrint(ESCPOSConst.CMP_TP_NORMAL)

        // Disconnect
        printer.disconnect()

        if (ESCPOSConst.CMP_SUCCESS != result) {
            // Print process Error
            Log.e("Citizen_POS_sample1", "Transaction Error : $result")
            logWriter.appendLog(TAG, "Transaction Error : $result")
        } else {
            logWriter.appendLog(TAG, "print success")
        }
    }

}

class Dw14Printer() {

}