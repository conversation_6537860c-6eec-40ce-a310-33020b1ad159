package app.rht.petrolcard.utils.citizen;

import android.graphics.Bitmap;

public class PrintCmd {

    private String text;
    private Bitmap bitmap;
    private byte[] bitmapArray;
    private int width = 128;
    private int height = 128;
    private AlignmentType alignment = AlignmentType.LEFT;
    private boolean bold = false;
    private int size = 1;
    private PrintContentType contentType = PrintContentType.TEXT;

    public PrintCmd(String text, AlignmentType alignment, boolean bold, int size) {
        this.text = text;
        this.alignment = alignment;
        this.bold = bold;
        this.size = size;
    }
    public PrintCmd(String text, AlignmentType alignment, boolean bold) {
        this.text = text;
        this.alignment = alignment;
        this.bold = bold;
        this.size = size;
    }
    public PrintCmd(String text, AlignmentType alignment, int size) {
        this.text = text;
        this.alignment = alignment;
        this.size = size;
    }
    public PrintCmd(String text, AlignmentType alignment) {
        this.text = text;
        this.alignment = alignment;
    }
    public PrintCmd(String text) {
        this.text = text;
    }
    public PrintCmd(String text, int size) {
        this.text = text;
        this.size = size;
    }
    public PrintCmd(String text, boolean bold) {
        this.text = text;
        this.bold = bold;
    }
    public PrintCmd(String content, AlignmentType alignment, PrintContentType contentType) {
        this.text = content;
        this.alignment = alignment;
        this.contentType = contentType;
    }
    public PrintCmd(String content, PrintContentType contentType) {
        this.text = content;
        this.contentType = contentType;
    }

    public PrintCmd(Bitmap content, PrintContentType contentType) {
        this.bitmap = content;
        this.contentType = contentType;
        this.alignment = AlignmentType.CENTER;
    }

    public PrintCmd(Bitmap content, int width, PrintContentType contentType) {
        this.bitmap = content;
        this.width = width;
        this.contentType = contentType;
        this.alignment = AlignmentType.CENTER;
    }

    public PrintCmd(Bitmap content, AlignmentType alignment, PrintContentType contentType) {
        this.bitmap = content;
        this.alignment = alignment;
        this.contentType = contentType;
    }

    public PrintCmd(byte[] content, int width, int height, AlignmentType alignment, PrintContentType contentType) {
        this.bitmapArray = content;
        this.width = width;
        this.height = height;
        this.alignment = alignment;
        this.contentType = contentType;
    }

    public PrintCmd(Bitmap content, int width,  int height, AlignmentType alignment, PrintContentType contentType) {
        this.bitmap = content;
        this.width = width;
        this.height = height;
        this.alignment = alignment;
        this.contentType = contentType;
    }

    public byte[] getBitmapArray() {
        return bitmapArray;
    }

    public void setBitmapArray(byte[] bitmapArray) {
        this.bitmapArray = bitmapArray;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public Bitmap getBitmap() {
        return bitmap;
    }

    public void setBitmap(Bitmap bitmap) {
        this.bitmap = bitmap;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public AlignmentType getAlignment() {
        return alignment;
    }

    public void setAlignment(AlignmentType alignment) {
        this.alignment = alignment;
    }

    public boolean isBold() {
        return bold;
    }

    public void setBold(boolean bold) {
        this.bold = bold;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public PrintContentType getContentType() {
        return contentType;
    }

    public void setContentType(PrintContentType contentType) {
        this.contentType = contentType;
    }
}
