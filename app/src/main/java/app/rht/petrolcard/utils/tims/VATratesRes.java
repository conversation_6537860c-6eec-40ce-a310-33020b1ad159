package app.rht.petrolcard.utils.tims;
public class VATratesRes {
   /**
    *(VAT rate A) Up to 7 symbols for VATrates of VAT class A in format ##.##%
    */
    public Double VATrateA;
    public Double getVATrateA() {
       return VATrateA;
    }
    protected void setVATrateA(Double value) {
       VATrateA = value;
    }

   /**
    *(VAT rate B) Up to 7 symbols for VATrates of VAT class B in format ##.##%
    */
    public Double VATrateB;
    public Double getVATrateB() {
       return VATrateB;
    }
    protected void setVATrateB(Double value) {
       VATrateB = value;
    }

   /**
    *(VAT rate C) Up to 7 symbols for VATrates of VAT class C in format ##.##%
    */
    public Double VATrateC;
    public Double getVATrateC() {
       return VATrateC;
    }
    protected void setVATrateC(Double value) {
       VATrateC = value;
    }

   /**
    *(VAT rate D) Up to 7 symbols for VATrates of VAT class D in format ##.##%
    */
    public Double VATrateD;
    public Double getVATrateD() {
       return VATrateD;
    }
    protected void setVATrateD(Double value) {
       VATrateD = value;
    }

   /**
    *(VAT rate E) Up to 7 symbols for VATrates of VAT class E in format ##.##%
    */
    public Double VATrateE;
    public Double getVATrateE() {
       return VATrateE;
    }
    protected void setVATrateE(Double value) {
       VATrateE = value;
    }
}
