package app.rht.petrolcard.utils

import android.app.DownloadManager
import android.content.ContentUris
import android.content.Context
import android.database.Cursor
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.os.Environment
import android.provider.DocumentsContract
import android.provider.MediaStore
import android.util.Log
import android.webkit.MimeTypeMap
import androidx.multidex.MultiDexApplication
import app.rht.petrolcard.R
import app.rht.petrolcard.utils.common.EnumUtils
import app.rht.petrolcard.utils.constant.AppConstant
import okhttp3.ResponseBody
import java.io.*
import java.net.URLConnection
import java.nio.charset.StandardCharsets
import java.text.SimpleDateFormat
import java.util.*


object FileUtil {

    val TAG = FileUtil::class.simpleName!!

    val DIR_SUFFIX_TEXT = "/Text/"
    val DIR_SUFFIX_IMAGES = "/Images/"
    val DIR_SUFFIX_AUDIOS = "/Audios/"
    val DIR_SUFFIX_DOCUMENTS = "/Documents/"
    val DIR_SUFFIX_VIDEOS = "/Videos/"
    val DIR_SUFFIX_FONTS = "/Fonts/"
    val DIR_SUFFIX_FILES = "/Files/"

    val THUMB_COLUMNS = arrayOf(MediaStore.Video.Thumbnails.DATA)
    val MEDIA_COLUMNS = arrayOf(MediaStore.Video.Media._ID)

    val DEFAULT_TEXT_EXT = ".txt"
    val DEFAULT_IMAGE_EXT = ".jpg"
    val DEFAULT_VIDEO_EXT = ".mp4"

    val DEFAULT_TEXT_PREFIX = "TEXT_"
    val DEFAULT_IMAGE_PREFIX = ""
    val DEFAULT_VIDEO_PREFIX = "VID_"

    /**
     * Get a file path from a Uri. This will get the the path for Storage Access
     * Framework Documents, as well as the _data field for the MediaStore and
     * other file-based ContentProviders.
     *
     * Ref: @link [https://gist.github.com/tatocaster/32aad15f6e0c50311626]
     *
     * @param context The context.
     * @param uri     The Uri to query.
     * <AUTHOR>
     */
    fun getRealPath(context: Context, uri: Uri): String? {

        // DocumentProvider
        if (DocumentsContract.isDocumentUri(context, uri)) {
            when {
                isExternalStorageDocument(uri) -> {
                    val docId = DocumentsContract.getDocumentId(uri)
                    val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    val type = split[0]

                    // this does not handle non-primary volumes
                    if ("primary".equals(type, ignoreCase = true)) {
                        return Environment.getExternalStorageDirectory().toString() + "/" + split[1]
                    }
                }
                isDownloadsDocument(uri) -> {
                    val id = DocumentsContract.getDocumentId(uri)
                    val contentUri = ContentUris.withAppendedId(
                            Uri.parse("content://downloads/public_downloads"),
                        java.lang.Long.valueOf(id)
                    )
                    return getDataColumn(context, contentUri, null, null)
                }
                isMediaDocument(uri) -> {
                    val docId = DocumentsContract.getDocumentId(uri)
                    val split = docId.split(":".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                    val type = split[0]

                    var contentUri: Uri? = null
                    when (type) {
                        "image" -> contentUri = MediaStore.Images.Media.EXTERNAL_CONTENT_URI
                        "video" -> contentUri = MediaStore.Video.Media.EXTERNAL_CONTENT_URI
                        "audio" -> contentUri = MediaStore.Audio.Media.EXTERNAL_CONTENT_URI
                    }

                    val selection = "_id=?"
                    val selectionArgs = arrayOf(split[1])

                    return getDataColumn(context, contentUri, selection, selectionArgs)
                }
            }
        }

        // Return the remote address
        else if ("content".equals(uri.scheme, ignoreCase = true))
            return if (isGooglePhotosUri(uri)) uri.lastPathSegment else getDataColumn(context, uri, null, null)

        // File
        else if ("file".equals(uri.scheme, ignoreCase = true)) return uri.path

        // Default
        return null
    }

    /**
     * Get the value of the data column for this Uri. This is useful for
     * MediaStore Uris, and other file-based ContentProviders.
     *
     * @param context       The context.
     * @param uri           The Uri to query.
     * @param selection     (Optional) Filter used in the query.
     * @param selectionArgs (Optional) Selection arguments used in the query.
     * @return The value of the _data column, which is typically a file path.
     */
    private fun getDataColumn(context: Context, uri: Uri?, selection: String?, selectionArgs: Array<String>?): String? {

        var cursor: Cursor? = null
        val column = "_data"
        val projection = arrayOf(column)

        try {
            cursor = context.contentResolver.query(uri!!, projection, selection, selectionArgs, null)
            if (cursor != null && cursor.moveToFirst()) {
                val index = cursor.getColumnIndexOrThrow(column)
                return cursor.getString(index)
            }
        } finally {
            cursor?.close()
        }
        return null
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is ExternalStorageProvider.
     */
    private fun isExternalStorageDocument(uri: Uri): Boolean {
        return "com.android.externalstorage.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is DownloadsProvider.
     */
    private fun isDownloadsDocument(uri: Uri): Boolean {
        return "com.android.providers.downloads.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is MediaProvider.
     */
    private fun isMediaDocument(uri: Uri): Boolean {
        return "com.android.providers.media.documents" == uri.authority
    }

    /**
     * @param uri The Uri to check.
     * @return Whether the Uri authority is Google Photos.
     */
    private fun isGooglePhotosUri(uri: Uri): Boolean {
        return "com.google.android.apps.photos.content" == uri.authority
    }

    fun createFile(context: Context, fileType: Int, fileName: String, folderName: String = "Artelir"): File? {
        val externalStorageState = Environment.getExternalStorageState()

        val dirSuffix = when (fileType) {
            EnumUtils.FileType.TEXT.ordinal -> DIR_SUFFIX_TEXT
            EnumUtils.FileType.IMAGE.ordinal -> DIR_SUFFIX_IMAGES
            EnumUtils.FileType.AUDIO.ordinal -> DIR_SUFFIX_AUDIOS
//            EnumUtils.FileType.DOCUMENT.ordinal -> DIR_SUFFIX_DOCUMENTS
            EnumUtils.FileType.VIDEO.ordinal -> DIR_SUFFIX_VIDEOS
            else -> DIR_SUFFIX_FILES
        }

        var mFileTemp: File? = null
        if (Environment.MEDIA_MOUNTED == externalStorageState) {
            try {
                val dir = File(Environment.getExternalStorageDirectory().toString() + "/" + folderName + dirSuffix)
                dir.mkdirs()
                mFileTemp = File(dir.absolutePath, fileName)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        } else {
            try {
                val dir = File(context.filesDir.toString() + "/" + folderName + dirSuffix)
                dir.mkdirs()
                mFileTemp = File(dir.absolutePath, fileName)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
        return mFileTemp
    }

//    fun compressVideo(context: Context, inPath: String): String? {
//        Log.d(TAG, "compressVideo()")
//        try {
//            val uncompressedFile = File(inPath)
//            Log.d(TAG, "Uncompressed File Size: ${uncompressedFile.length()}")
//            FileUtil.createCacheFile(
//                    context = context,
//                    fileType = EnumUtils.FileType.VIDEO.ordinal,
//                    fileName = FileUtil.getUniqueFileName(EnumUtils.FileType.VIDEO.ordinal)!!
//            ).let {
//                val outPath = it.path
//                val isCompressed = MediaController.getInstance().convertVideo(inPath, it.path)
//                Log.d(TAG, "isCompressed: $isCompressed")
//                val compressedFile = File(outPath)
//                Log.d(TAG, "Compressed File Size: ${compressedFile.length()}")
//                if (isCompressed) return outPath
//            }
//        } catch (e: Exception) {
//            e.printStackTrace()
//            return null
//        }
//        return null
//    }

    fun getUniqueFileName(fileType: Int, prefix: String = ""): String? {
        when (fileType) {
            EnumUtils.FileType.TEXT.ordinal -> {
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.getDefault()).format(Date())
                return "$DEFAULT_TEXT_PREFIX$prefix$timeStamp$DEFAULT_TEXT_EXT"
            }

            EnumUtils.FileType.IMAGE.ordinal -> {
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.getDefault()).format(Date())
                return "$DEFAULT_IMAGE_PREFIX$prefix$timeStamp$DEFAULT_IMAGE_EXT"
            }

            EnumUtils.FileType.VIDEO.ordinal -> {
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmssSSS", Locale.getDefault()).format(Date())
                return "$DEFAULT_VIDEO_PREFIX$prefix$timeStamp$DEFAULT_VIDEO_EXT"
            }
        }
        return null
    }

    fun getExtention(context: Context, uri: Uri): String? {
        val mime = MimeTypeMap.getSingleton()
        return mime.getExtensionFromMimeType(context.contentResolver.getType(uri))
    }

    fun getMimeType(context: Context, uri: Uri): String? {
        return context.contentResolver.getType(uri)
    }

    fun isImageFile(path: String): Boolean {
        val mimeType = URLConnection.guessContentTypeFromName(path)
        return mimeType?.startsWith("image") ?: false
    }

    fun isVideoFile(path: String): Boolean {
        val mimeType = URLConnection.guessContentTypeFromName(path)
        return mimeType?.startsWith("video") ?: false
    }


    fun downloadFile(context: Context, url: String, title: String, description: String, subpath: String) {
        val downloadManager = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager
        val downloadUri = Uri.parse(url)
        val request = DownloadManager.Request(downloadUri)
        request.setTitle(title)
        request.setDescription(description)
        request.setDestinationInExternalPublicDir(Environment.DIRECTORY_DOWNLOADS, subpath)
        request.allowScanningByMediaScanner()
        request.setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
        downloadManager.enqueue(request)
    }

    fun getImageUri(inContext: Context, inImage: Bitmap): Uri {
        val bytes = ByteArrayOutputStream()
        inImage.compress(Bitmap.CompressFormat.PNG, 100, bytes)
        val path = MediaStore.Images.Media.insertImage(inContext.contentResolver, inImage, "Title", null)
        return Uri.parse(path)
    }

    fun getThumbnailPathS(context: Context, fileUri: Uri): String? {
        val fileId = getFileIdS(context, fileUri)
        if (fileId == 0L) return null

        MediaStore.Video.Thumbnails.getThumbnail(context.contentResolver, fileId, MediaStore.Video.Thumbnails.MICRO_KIND, null)

        context.contentResolver.query(
                MediaStore.Video.Thumbnails.EXTERNAL_CONTENT_URI,
                THUMB_COLUMNS,
                MediaStore.Video.Thumbnails.VIDEO_ID + " = " + fileId,
                null,
                null
        )?.use {
            if (it.moveToFirst()) {
                return it.getString(it.getColumnIndex(MediaStore.Video.Thumbnails.DATA))
            }
        }
        return null
    }

    fun getFileIdS(context: Context, fileUri: Uri): Long {
        context.contentResolver.query(fileUri, MEDIA_COLUMNS, null, null, null)?.use {
            try {
                if (it.moveToFirst()) {
                    val columnIndex = it.getColumnIndexOrThrow(MediaStore.Video.Media._ID)
                    return it.getInt(columnIndex).toLong()
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
        return 0L
    }
    // SafeFile

    // SafeFile
    fun downloadImageNew(context: Context, filename: String, downloadUrlOfImage: String) {
        try {
            val dm = context.getSystemService(Context.DOWNLOAD_SERVICE) as DownloadManager?
            val downloadUri = Uri.parse(downloadUrlOfImage)
            val request = DownloadManager.Request(downloadUri)
            request.setAllowedNetworkTypes(DownloadManager.Request.NETWORK_WIFI or DownloadManager.Request.NETWORK_MOBILE)
                .setAllowedOverRoaming(false)
                .setTitle(filename)
                .setMimeType("image/jpeg") // Your file type. You can use this code to download other file types also.
                .setNotificationVisibility(DownloadManager.Request.VISIBILITY_VISIBLE_NOTIFY_COMPLETED)
                .setDestinationInExternalPublicDir(
                    Environment.DIRECTORY_PICTURES,
                    File.separator.toString() + filename + ".jpg"
                )
            dm!!.enqueue(request)
            Log.i(TAG,"Image download started.")
        } catch (e: java.lang.Exception) {
            Log.i(TAG,"Image download failed.")
        }
    }

    fun writeResponseBodyToDisk(context: Context, body: ResponseBody, fileName: String): Boolean {
        val futureStudioIconFile: File
        return try {
            // todo change the file location/name according to your needs
            //File futureStudioIconFile = new File(context.getExternalFilesDir(null) + File.separator +fileName+ ".png");
                var path = ""
            /*if (BuildConfig.DEBUG)
                path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).toString() + File.separator + fileName + ".jpg"
            else*/
                path = context.filesDir.toString() + File.separator + fileName + ".jpg"

                futureStudioIconFile =  File(path)

            Log.i(TAG,"FILE PATH: $path")

            //File futureStudioIconFile =  new File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "LOGO");
            var inputStream: InputStream? = null
            var outputStream: OutputStream? = null
            try {
                val fileReader = ByteArray(4096)
                val fileSize = body.contentLength()
                var fileSizeDownloaded: Long = 0
                inputStream = body.byteStream()
                outputStream = FileOutputStream(futureStudioIconFile)
                while (outputStream != null) {
                    val read = inputStream.read(fileReader)
                    if (read == -1) {
                        break
                    }
                    outputStream.write(fileReader, 0, read)
                    fileSizeDownloaded += read.toLong()
                    Log.d(TAG, "file download: $fileSizeDownloaded of $fileSize")
                }
                outputStream.flush()
                val pref = AppPreferencesHelper(context.getSharedPreferences(AppConstant.PREF_NAME, MultiDexApplication.MODE_PRIVATE))
                pref.logoPath = path
                true
            } catch (e: IOException) {
                false
            } finally {
                inputStream?.close()
                outputStream?.close()
            }
        } catch (e: IOException) {
            false
        }
    }
    fun saveDefaultLogo(context: Context,fileName: String): Boolean {
        val futureStudioIconFile: File
        return try {
            var path = ""
            path = context.filesDir.toString() + File.separator + fileName + ".jpg"
            futureStudioIconFile =  File(path)
            var outputStream: OutputStream? = null
            try {
                Log.i(TAG,"FILE PATH: $path")
                val bm = BitmapFactory.decodeResource(context.resources, R.drawable.fbs_pay_small)
                outputStream = FileOutputStream(futureStudioIconFile)
                bm.compress(Bitmap.CompressFormat.PNG, 100, outputStream)
                outputStream.flush()
                outputStream.close()
                val pref = AppPreferencesHelper(context.getSharedPreferences(AppConstant.PREF_NAME, MultiDexApplication.MODE_PRIVATE))
                pref.logoPath = path
                true
            } catch (e: IOException) {
                false
            } finally {
                outputStream?.close()
            }
        } catch (e: IOException) {
            false
        }
    }
    fun loadJSONFromAsset(context: Context, fileName: String?): String? {
        var json: String? = null
        json = try {
            val `is` = context.assets.open(fileName!!)
            val size = `is`.available()
            val buffer = ByteArray(size)
            `is`.read(buffer)
            `is`.close()
            String(buffer, StandardCharsets.UTF_8)
        } catch (ex: IOException) {
            ex.printStackTrace()
            return null
        }
        return json
    }
}
