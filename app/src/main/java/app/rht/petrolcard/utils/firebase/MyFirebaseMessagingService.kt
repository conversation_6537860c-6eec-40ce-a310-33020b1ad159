package app.rht.petrolcard.utils.firebase

import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import app.rht.petrolcard.ui.common.model.NotificationModel
import app.rht.petrolcard.utils.LogUtils.Companion.log
import app.rht.petrolcard.utils.NotificationUtils
import app.rht.petrolcard.utils.constant.AppConstant
import com.google.firebase.messaging.FirebaseMessagingService
import com.google.firebase.messaging.RemoteMessage
import java.util.*

class MyFirebaseMessagingService : FirebaseMessagingService() {

    companion object {
        private val TAG = MyFirebaseMessagingService::class.simpleName

        const val NOTIFICATION_CHANNEL_ID = "general"
    }

    private lateinit var notificationManager: NotificationManager

    override fun onCreate() {
        super.onCreate()
        notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    }

    override fun onNewToken(token: String) {
        super.onNewToken(token)
        log(TAG, "Token:: ${token}")

    }

    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        log(TAG, "onMessageReceived()")
        try {
            // Check if message contains a notification payload.
            if (remoteMessage.notification != null) {
                var title = ""
                var message = ""
                var code = ""
                var payload = ""
                var timestamp = ""
                val data = remoteMessage.data
                title = data["title"]!!
                message = data["message"]!!
                code = data["code"]!!
                payload = data["payload"]!!
                timestamp = data["timestamp"]!!
                val notificationModel = NotificationModel(code, message, payload, timestamp, title)
                handleNotification(notificationModel)
            }
        } catch (e: Exception) {
            log(TAG, "Error in onMessageReceived() : ${e.message}")
        }
    }
    private fun handleNotification(message: NotificationModel) {
        if (!NotificationUtils.isAppIsInBackground(applicationContext)) {
            val pushNotification = Intent(AppConstant.PUSH_NOTIFICATION)
            pushNotification.putExtra("message", message)
            LocalBroadcastManager.getInstance(this).sendBroadcast(pushNotification)
                // play notification sound
            val notificationUtils = NotificationUtils(applicationContext)
            notificationUtils.playNotificationSound()
        } else {
            // If the app is in background, firebase itself handles the notification
        }
    }
}