package app.rht.petrolcard.utils.fuelpos.models

import androidx.annotation.Keep

@Keep
class PumpReleaseRequest {
    var trxToken: String
    var pumpNumber: String
    var product: String
    var amount: String
    var currency = "SAR"
    var ip: String
    var port: String
    var pinNumber = "0000"
    var paymentTypeId = "01"
    var paymentType = "CASH"
    var journalText = " "

    constructor(
        trxToken: String,
        pumpNumber: String,
        product: String,
        amount: String,
        currency: String,
        ip: String,
        port: String,
        pinNumber: String,
        paymentTypeId: String,
        paymentType: String,
        journalText: String
    ) {
        this.trxToken = trxToken
        this.pumpNumber = pumpNumber
        this.product = product
        this.amount = amount
        this.currency = currency
        this.ip = ip
        this.port = port
        this.pinNumber = pinNumber
        this.paymentTypeId = paymentTypeId
        this.paymentType = paymentType
        this.journalText = journalText
    }

    constructor(
        trxToken: String,
        pumpNumber: String,
        product: String,
        amount: String,
        currency: String,
        ip: String,
        port: String
    ) {
        this.trxToken = trxToken
        this.pumpNumber = pumpNumber
        this.product = product
        this.amount = amount
        this.currency = currency
        this.ip = ip
        this.port = port
    }

    //releasePump(trxToken = "300000000002",pumpNumber = "2",product = "1",amount = "5",ip = "*************",port = "7778")
    constructor(
        trxToken: String,
        pumpNumber: String,
        product: String,
        amount: String,
        ip: String,
        port: String
    ) {
        this.trxToken = trxToken
        this.pumpNumber = pumpNumber
        this.product = product
        this.amount = amount
        this.ip = ip
        this.port = port
    }
}

@Keep
class PumpReserveRequest {

    var trxToken:String
    var pumpNumber:String
    var ip:String=""
    var port:String=""
    var amount:String
    var currency: String = "SAR"
    var product:String
    var pinNumber:String="0000"
    var journalText:String=" "

    constructor(
        trxToken: String,
        pumpNumber: String,
        product: String,
        amount: String,
        currency: String,
        ip: String,
        port: String,
        pinNumber: String,
        journalText: String
    ) {
        this.trxToken = trxToken
        this.pumpNumber = pumpNumber
        this.product = product
        this.amount = amount
        this.currency = currency
        this.ip = ip
        this.port = port
        this.pinNumber = pinNumber
        this.journalText = journalText
    }

    constructor(
        trxToken: String,
        pumpNumber: String,
        product: String,
        amount: String,
        currency: String,
        ip: String,
        port: String
    ) {
        this.trxToken = trxToken
        this.pumpNumber = pumpNumber
        this.product = product
        this.amount = amount
        this.currency = currency
        this.ip = ip
        this.port = port
    }

    //reservePump(trxToken = "300000000002",pumpNumber = "2",product = "1",amount = "5",ip = "*************",port = "7778")
    constructor(
        trxToken: String,
        pumpNumber: String,
        product: String,
        amount: String,
        ip: String,
        port: String
    ) {
        this.trxToken = trxToken
        this.pumpNumber = pumpNumber
        this.product = product
        this.amount = amount
        this.ip = ip
        this.port = port
    }
}



