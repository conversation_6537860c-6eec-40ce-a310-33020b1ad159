import android.os.Handler
import android.os.Looper
import app.rht.petrolcard.utils.LogWriter
import java.io.*
import java.net.*
import java.nio.charset.StandardCharsets.UTF_8

class FuelPosSingleTcpServer(private val listener: TcpServerListener) {

    private lateinit var serverSocket: ServerSocket
    //private var lastClientIndex: Short = 0
    private val clients: MutableMap<Int, Client> = HashMap()

    private var serverThread : ServerThread? = null

    val logWriter = LogWriter("FuelPosTcpServer")

    var isServerRunning = false
        private set

    fun startServer(port: String?) {
        startServer(Integer.valueOf(port))
    }

    fun startServer(port: Int) {
        if(!isServerRunning) {
            serverThread = ServerThread(port)
            serverThread!!.start()
        }
        else {
            logWriter.appendLog(TAG, "Server already running")
        }
    }

    inner class ServerThread(val port: Int) : Thread() {
        override fun run() {
                var socket: Socket? = null
                try {
                    serverSocket = ServerSocket(port)
                } catch (e: IOException) {
                    e.printStackTrace()
                    isServerRunning = false
                }
                logWriter.appendLog(TAG, "Start fuelPOS Server")
                isServerRunning = true
                Handler(Looper.getMainLooper()).post { listener.serverStarted(port) }

                while (isServerRunning) {
                    println("Accepting client")
                    try {
                        socket = serverSocket.accept()
                        val client = Client(socket)
                        //lastClientIndex++
                        clients[0] = client
                        Thread(client).start()
                        //client.setIndex(lastClientIndex.toInt())

                        Handler(Looper.getMainLooper()).post {
                            listener.clientConnected(
                                socket,
                                socket.localAddress,
                                +socket.localPort,
                                socket.localSocketAddress,
                                //lastClientIndex.toInt()
                                0
                            ) }

                    } catch (e: IOException) {
                        isServerRunning = false
                        break
                    } catch (e: UninitializedPropertyAccessException) {
                        e.printStackTrace()
                        isServerRunning = false
                        break
                    }
                }
                Handler(Looper.getMainLooper()).post { listener.serverClosed(port) }
        }
    }

    fun closeServer() {
        try {
            logWriter.appendLog(TAG, "closeServer: ")
            isServerRunning = false
            serverSocket.close()
            kickAll()
            serverThread!!.interrupt()
        } catch (e: IOException) {
            e.printStackTrace()
        } catch (e: UninitializedPropertyAccessException) {
            e.printStackTrace()
        }
    }

    fun kickAll() {
        for (client in clients.values) client.kill()
    }

    fun kick(clientIndex: Int) {
        clients[clientIndex]!!.kill()
    }

    fun sendln(clientIndex: Int, message: String?) {
        clients[clientIndex]!!.getOutput().println(message)
        clients[clientIndex]!!.getOutput().flush()
    }

    fun send(clientIndex: Int, message: String?) {
        val writer = clients[clientIndex]!!.getOutput()
        val clientAddress = clients[clientIndex]!!.getSocket().inetAddress
        writer.print(message)
        writer.flush()
        Handler(Looper.getMainLooper()).post {
            logWriter.appendLog(TAG,"**Sent to Client $clientAddress, Message: $message")
            logWriter.appendLog(TAG,"**Has any error occurred while sending message: " + writer.checkError())
        }
    }

    fun broadcast(message: String?) {
        val thread = Thread {
            for (client in clients.values) {
                client.getOutput().print(message)
                client.getOutput().flush()
                logWriter.appendLog(TAG, "* Broadcast sent to clients: $message")
            }
        }
        thread.run()
    }

    fun broadcastln(message: String?) {
        for (client in clients.values) {
            client.getOutput().println(message)
            client.getOutput().flush()
        }
    }

    fun getClients(): Map<Int, Client> {
        return clients
    }

    fun getClientsCount(): Int {
        return clients.size
    }

    //---------------------------------------------[Interfaces]---------------------------------------------//


    interface TcpServerListener {
        fun serverStarted(port: Int)
        fun serverClosed(port: Int)

        fun clientConnected(
            socket: Socket?,
            localAddress: InetAddress?,
            port: Int,
            localSocketAddress: SocketAddress?,
            clientIndex: Int
        )

        fun clientDisconnected(
            socket: Socket?,
            localAddress: InetAddress?,
            port: Int,
            localSocketAddress: SocketAddress?,
            clientIndex: Int
        )

        fun messageReceived(message: String?, bytes:ByteArray,clientIndex: Int)
    }



    //--------------------------------------------[Client class]--------------------------------------------//
    inner class Client(clientSocket: Socket) : Runnable {
        private lateinit var output: PrintWriter
        private val socket: Socket = clientSocket
        private lateinit var input: BufferedReader
        private var clientIndex = 0
        private var inputStream: InputStream? = null
        private var outputStream: OutputStream? = null
        override fun run() {
            logWriter.appendLog(TAG, "Client Connected from  ${socket.inetAddress}")
            var buffer :ByteArray
            while (isServerRunning) {
                println("Read line (Client: $clientIndex)")
                try {
                    buffer = ByteArray(4096) //4MB
                    inputStream = socket.getInputStream()
                    val dataLength = inputStream!!.read(buffer)
                    if (dataLength != -1) {
                        val data = ByteArray(dataLength)
                        for (i in 0 until dataLength) data[i] = buffer[i]

                        Handler(Looper.getMainLooper()).post {
                            val dataString = String(data)
                            listener.messageReceived(
                                dataString,
                                data,
                                clientIndex
                            )
                            logWriter.appendLog(TAG, "Received from  ${socket.inetAddress} : $dataString")
                        }
                    } else {
                        socket.close()
                        clients.remove(clientIndex)

                            Handler(Looper.getMainLooper()).post {
                                listener.clientDisconnected(
                                    socket,
                                    socket.localAddress,
                                    +socket.localPort,
                                    socket.localSocketAddress,
                                    clientIndex)
                            }
                        break
                    }

                } catch (e: IOException) {
                    e.printStackTrace()
                    logWriter.appendLog(TAG,"TCP Server Exception ${e.message}, ${e.cause}")
                } catch (e: SocketException) {
                    logWriter.appendLog(TAG,"TCP Server Exception ${e.message}, ${e.cause}")
                    Handler(Looper.getMainLooper()).post {
                        listener.clientDisconnected(
                            socket,
                            socket.localAddress,
                            +socket.localPort,
                            socket.localSocketAddress,
                            clientIndex)
                    }
                }
            }
        }

        fun send(data:String){
            send(data.toByteArray(UTF_8))
        }

        fun send(data: ByteArray) {
            val thread = Thread {
                try {
                    outputStream = socket.getOutputStream()
                    outputStream!!.write(data)
                    Handler(Looper.getMainLooper()).post { logWriter.appendLog(TAG,"Data sent to ${socket.inetAddress}, data: ${String(data, UTF_8)}") }
                } catch (e: IOException) {
                    e.printStackTrace()
                    Handler(Looper.getMainLooper()).post {
                        logWriter.appendLog(TAG, "Error sending data")
                        logWriter.appendLog(TAG,"Error ${e.message}, ${e.cause}")
                    }
                } catch (e: SocketException){
                    kill()
                }
            }
            thread.start()
        }

        fun kill() {
            try {
                socket.shutdownInput()
            } catch (e: Exception) {
            }
            try {
                socket.shutdownOutput()
            } catch (e: Exception) { }
            try {
                if (inputStream != null) inputStream!!.close()
                if (outputStream != null) outputStream!!.close()
                socket.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }

        fun setIndex(index: Int) {
            clientIndex = index
        }

        fun getOutput(): PrintWriter {
            return output
        }

        fun getSocket(): Socket {
            return socket
        }

        init {
            try {
                input = BufferedReader(InputStreamReader(socket.getInputStream()))
                output = PrintWriter(BufferedWriter(OutputStreamWriter(socket.getOutputStream())), true)
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    companion object {
        private const val TAG = "TCPServer"
    }
}
