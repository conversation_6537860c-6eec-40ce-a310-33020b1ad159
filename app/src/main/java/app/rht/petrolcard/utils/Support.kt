package app.rht.petrolcard.utils

import TremolZFP.FPcore
import TremolZFP.FPcore.SException
import android.annotation.SuppressLint
import android.app.Activity
import android.app.Dialog
import android.app.Service
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.Color
import android.graphics.Matrix
import android.net.ConnectivityManager
import android.net.wifi.WifiManager
import android.os.Environment
import android.os.Handler
import android.os.Looper
import android.telephony.TelephonyManager
import android.util.Log
import android.view.Window
import android.widget.TextView
import android.widget.Toast
import androidx.multidex.MultiDexApplication
import androidx.preference.PreferenceManager
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.database.baseclass.*
import app.rht.petrolcard.ui.esdsign.model.EsdSignModel
import app.rht.petrolcard.ui.menu.activity.MenuActivity
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.transactionlist.model.ProductPT
import app.rht.petrolcard.ui.transactionlist.model.ServiceResponseGPT
import app.rht.petrolcard.utils.LogUtils.Companion.log
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.constant.FUSION_PAYMENT_TYPES
import com.google.android.gms.common.util.IOUtils
import com.google.common.hash.HashCode
import com.google.common.hash.Hashing
import com.google.common.io.Files
import com.google.gson.Gson
import net.sqlcipher.database.SQLiteException
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.toRequestBody
import org.apache.commons.lang3.StringUtils
import org.json.JSONException
import org.json.XML
import java.io.*
import java.lang.ref.WeakReference
import java.math.BigDecimal
import java.math.RoundingMode
import java.net.*
import java.nio.channels.FileChannel
import java.security.MessageDigest
import java.security.NoSuchAlgorithmException
import java.text.*
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.concurrent.TimeUnit
import javax.net.ssl.HttpsURLConnection


class Support {
    companion object {
        private val TAG = Support::class.simpleName
        fun convertToTimeStamp(transactionDate: String):String
        {
            var currentTimeStamp = System.currentTimeMillis()
            try {
                val formatter: java.text.DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm",Locale.ENGLISH)
                val date = formatter.parse(transactionDate) as Date
                val output = date.time / 1000L
                val str = output.toString()
                currentTimeStamp = str.toLong() * 1000
            }
            catch (e:Exception)
            {
                e.printStackTrace()
                return currentTimeStamp.toString()
            }
            Log.i(TAG, "transactionDate:: $transactionDate")
            Log.i(TAG, "Transaction currentTimeStamp:: $currentTimeStamp")
            return currentTimeStamp.toString()
        }
        fun alert(message: String,context: Context) {
            Handler(Looper.getMainLooper()).post {
                Log.i("ERROR", "Error Message::: $message")
                val dialog = Dialog(context)
                dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                dialog.setCancelable(false)
                dialog.setContentView(R.layout.dialog_failed_message)
                dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
                val tvTitle = dialog.findViewById<TextView>(R.id.title)
                val tvMessage = dialog.findViewById<TextView>(R.id.message)
                val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

                tvTitle.text = MainApp.appContext.getString(R.string.fiscal_printer_error)
                tvMessage.text = message

                dialogButton.setOnClickListener {
                    dialog.dismiss()
                }
                dialog.show()
            }
         
        }
        fun handleException(ex: java.lang.Exception,context:Context) {
            if (ex is SException) {
                val sx = ex
                if (sx.isFpException) {
                    /**
                     * Possible reasons:
                     * sx.getSTE1() =                                              sx.getSTE2() =
                     * 0x30 OK                                                   0x30 OK
                     * 0x31 Out of paper, printer failure                        0x31 Invalid command
                     * 0x32 Registers overflow                                   0x32 Illegal command
                     * 0x33 Clock failure or incorrect date&time                 0x33 Z daily report is not zero
                     * 0x34 Opened fiscal receipt                                0x34 Syntax error
                     * 0x35 Payment residue account                              0x35 Input registers overflow
                     * 0x36 Opened non-fiscal receipt                            0x36 Zero input registers
                     * 0x37 Registered payment but receipt is not closed         0x37 Unavailable transaction for correction
                     * 0x38 Fiscal memory failure                                0x38 Insufficient amount on hand
                     * 0x39 Incorrect password                                   0x3A No access
                     * 0x3a Missing external display
                     * 0x3b 24hours block – missing Z report
                     * 0x3c Overheated printer thermal head.
                     * 0x3d Interrupt power supply in fiscal receipt (one time until status is read)
                     * 0x3e Overflow EJ
                     * 0x3f Insufficient conditions
                     */
                    if (sx.stE1 == 0x30 && sx.stE2 == 0x32) alert("sx.getSTE1() == 0x30 - command is OK  AND  sx.getSTE2() == 0x32 - command is Illegal in current context",context) else if (sx.stE1 == 0x30 && sx.stE2 == 0x33) alert(
                        "sx.getSTE1() == 0x30 - command is OK  AND sx.getSTE2() == 0x33 - make Z report"
                    ,context) else if (sx.stE1 == 0x34 && sx.stE2 == 0x32) alert("sx.getSTE1() == 0x34 - Opened fiscal receipt  AND  sx.getSTE2() == 0x32 - command is Illegal in current context",context) else if (sx.stE1 == 0x30 && sx.STE2 == 0x3A) alert(
                        "sx.getSTE1() == 0x30 - command is OK AND sx.STE2 == 0x3A - Invoice number not found!"
                    ,context) else if (sx.stE1 == 0x30 && sx.STE2 == 0x3B) alert("sx.getSTE1() == 0x30 - command is OK AND sx.STE2 == 0x3B - Wrong client PIN!",context) else if (sx.stE1 == 0x30 && sx.STE2 == 0x3C) alert(
                        "sx.getSTE1() == 0x30 - command is OK AND sx.STE2 == 0x3C - Missing defined item!"
                    ,context) else if (sx.stE1 == 0x30 && sx.STE2 == 0x3D) alert("sx.getSTE1() == 0x30 - command is OK AND sx.STE2 == 0x3D - Item quantity too large!",context) else if (sx.stE1 == 0x30 && sx.STE2 == 0x3E) alert(
                        "sx.getSTE1() == 0x30 - command is OK AND sx.STE2 == 0x3E - Item amount too large!"
                    ,context) else {
                        alert(""" ${sx.message} STE1=${sx.stE1}, STE2=${sx.stE2} """.trimIndent(),context)
                    }
                } else if (sx.ErrType == FPcore.SErrorType.ServerDefsMismatch) {
                   alert("The current library version and server definitions version do not match",context)
                } else if (sx.ErrType == FPcore.SErrorType.ServMismatchBetweenDefinitionAndFPResult) {
                   alert("The current library version and the fiscal device firmware is not matching",context)
                } else if (sx.ErrType == FPcore.SErrorType.ServerAddressNotSet) {
                   alert("Specify server ServerAddress property ",context)
                } else if (sx.ErrType == FPcore.SErrorType.ServerConnectionError) {
                   alert("Connection from this app to the server is not established",context)
                } else if (sx.ErrType == FPcore.SErrorType.ServSockConnectionFailed) {
                   alert("Server can not connect to the fiscal device",context)
                } else if (sx.ErrType == FPcore.SErrorType.ServTCPAuth) {
                   alert("Wrong device ТCP password",context)
                } else if (sx.ErrType == FPcore.SErrorType.ServWaitOtherClientCmdProcessingTimeOut) {
                   alert("Processing of other clients command is taking too long",context)
                } else {
                   alert(sx.message!!,context)
                }
            } else {
                var msg = ex.message
                if (msg == null || msg == "") {
                    msg = ex.toString()
                }
               alert(msg,context)
            }
        }
        fun generateInvoiceNumber(saleID:String=""): String {
            var reference = ""
                val sn = getSN()!!
                var serialNumber = sn
                if(sn.isNotEmpty() && sn.length>4){
                    serialNumber = sn.takeLast(4)
                }
                val seconds = System.currentTimeMillis()/1000
            reference = saleID.ifEmpty {
                serialNumber + seconds
            }
            val data = StringUtils.leftPad(reference, 15, "0")
            Log.i(TAG,"invNo : $data")
            return reference
        }
        fun getFormatTrxDate(date: Date): String {
            val sdfDate2 = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            val strDate2 = sdfDate2.format(date)
            Log.v("getDateComparison", "date String -> $strDate2")
            return strDate2
        }
        open fun getRandomNumber(digCount: Int): String? {
            val rnd = Random()
            val sb = StringBuilder(digCount)
            for (i in 0 until digCount) sb.append(('0' + rnd.nextInt(10)))
            return sb.toString()
        }

        fun getFormattedTotal(d1: Double, bd: String?, d2: Double): String? {
            val result = BigDecimal.valueOf(d1 + d2)
            return String.format(Locale.US, "%.2f", result)
        }
        fun formatString(d1: Double): String? {
            //return String.format(Locale.US, "%.2f", d1)
            return d1.toBigDecimal().setScale(2, RoundingMode.DOWN).toDouble().toString()           // taking lower decimal rounding off
        }
        fun formatString(d1: String): String {
            return try{
                //return String.format(Locale.US, "%.2f", d1)
                d1.toBigDecimal().setScale(2, RoundingMode.DOWN).toDouble().toString()           // taking lower decimal rounding off
            } catch (e:Exception){
                "0.0"
            }
        }
        fun formatStringNoDecimal(d1: Double): String? {
            return String.format(Locale.US, "%.0f", d1)
        }
        fun getDateComparison(date: Date?): Date? {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
            val strDate2 = sdfDate.format(date)
            var date2: Date? = null
            try {
                date2 = sdfDate.parse(strDate2)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            return date2
        } // eof getDateComparison
        fun dateToStringX(date: String): String? {
            return date.replace("-".toRegex(), "").replace(" ".toRegex(), "")
                .replace(":".toRegex(), "")
        }
        fun dateToStringX(date: Date?): String {
            val sdfDate =
                SimpleDateFormat("yyyyMMddHHmmss", Locale.ENGLISH)
            return sdfDate.format(date)
        }
        fun formatDouble(d: Double): String {
            val formatter = NumberFormat.getInstance(Locale.US) as DecimalFormat
            val symbols = formatter.decimalFormatSymbols
            symbols.groupingSeparator = ' '
            formatter.decimalFormatSymbols = symbols
            return formatter.format(Math.abs(d)) //.replaceAll(","," ");
        }
        fun dateToStringPlafondCompt(date: Date?): String? {
            val sdfDate =
                SimpleDateFormat("yyyyMMdd", Locale.ENGLISH)
            return sdfDate.format(date)
        }
        fun getSN(): String? {
            return MainApp.sn
        }
        fun dateToStringH(date: Date?): String? {
            val sdfDate =
                SimpleDateFormat("dd/MM/yyyy  HH:mm:ss", Locale.ENGLISH)
            return sdfDate.format(date)
        }
        fun dateToString(date: Date?): String? {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            return sdfDate.format(date)
        }
        /**accept only yyyyMMddHHmmss and return only yyyy-MM-dd HH:mm:ss**/
        fun fuelPosDateToDateString(dateString: String) :String? {
            val date = SimpleDateFormat("yyyyMMddHHmmss").parse(dateString)!!
            return dateToString(date)
        }

        fun dateToStringFormat(date: Date?,dateFormat: String): String? {
            val sdfDate = SimpleDateFormat(dateFormat, Locale.ENGLISH)
            return sdfDate.format(date)
        }


        fun dateToStringArabic(date: Date?): String? {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale("ar"))
            return sdfDate.format(date)
        }
        fun dateToStringHour(date: Date?): String? {
            val sdfDate =
                SimpleDateFormat("HH:mm:ss", Locale.ENGLISH)
            return sdfDate.format(date)
        }
        fun dateToStringPlafond(date: Date?): String {
            val sdfDate =
                SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
            return sdfDate.format(date)
        }
        fun checkGreyList(c: Context, mPan: String?): Boolean {

            var mListeGrise: GreyListModel? = null
            try {
                val mListeGriseDAO = GreyListDao()
                mListeGriseDAO.open()
                mListeGrise = mListeGriseDAO.getGreyListByPan(mPan!!)
                mListeGriseDAO.close()
            } catch (Ex: SQLiteException) {
                log(TAG, "checkGreyList : "+Ex.message!!)
            }
            return if (mListeGrise == null) {
                log(TAG, "\"grey list\" gray list null")
                false
            } else {
                log(TAG, mListeGrise.toString())
                true
            }
        } // eof checkGreyList
        fun checkBlackList(c: Context, mPan: String?): Boolean {

            var mListeNoire: BlackListModel? = null
            try {
                val mListeNoireDAO = BlackListDao()
                mListeNoireDAO.open()
                mListeNoire = mListeNoireDAO.selectionnerByPan(mPan!!)
                mListeNoireDAO.close()
            } catch (Ex: SQLiteException) {
                log(TAG, "checkBlackList: "+Ex.message!!)
            }
            return if (mListeNoire == null) {
                log(TAG, "\"black list\" null")
                false
            } else {
                log(TAG, mListeNoire.toString())
                log(TAG, "La carte est black listée")
                true
            }
        } // eof checkBlackList

        fun getFormattedValue(context: Context, value:String): String {
            //val ctx = WeakReference(context).get()!!
            /*return if(LocaleManager.getLanguage(context) == LocaleManager.LANGUAGE_FRENCH)
                value.format("#,##")
            else
                value.format("#.##")*/
           return if(LocaleManager.getLanguage(MainApp.appContext) == LocaleManager.LANGUAGE_FRENCH)
                value.replace(".",",")
            else
                value.replace(",",".")
        }
        fun getFormattedValue(value:String): String {
            //val ctx = WeakReference(context).get()!!
            /*return if(LocaleManager.getLanguage(context) == LocaleManager.LANGUAGE_FRENCH)
                value.format("#,##")
            else
                value.format("#.##")*/
            return if(LocaleManager.getLanguage(MainApp.appContext) == LocaleManager.LANGUAGE_FRENCH)
                value.replace(".",",")
            else
                value.replace(",",".")
        }

        fun generateReference(): String {
            var reference = ""
            val serialNumber: String = getSN()!!
            val seconds = TimeUnit.SECONDS.toSeconds(System.currentTimeMillis())/1000
            reference = serialNumber + seconds
            return reference
        }
        fun getFusionPaymentType(paymentId:String):String
        {
            var paymentType = FUSION_PAYMENT_TYPES.OTHER_VALUE
            when (paymentId) {
                AppConstant.CASH_VALUE -> {
                    paymentType=  FUSION_PAYMENT_TYPES.CASH_VALUE
                }
                AppConstant.CARD_VALUE -> {
                    paymentType=  FUSION_PAYMENT_TYPES.CARD_VALUE
                }
                AppConstant.BANK_VALUE, AppConstant.VISA_VALUE -> {
                    paymentType=  FUSION_PAYMENT_TYPES.BANK_VALUE
                }
                AppConstant.MOBILE_VALUE,AppConstant.MTN_PAY_VALUE -> {
                    paymentType=  FUSION_PAYMENT_TYPES.MOBILE_VALUE
                }
                AppConstant.RFID_VALUE -> {
                    paymentType=  FUSION_PAYMENT_TYPES.PASSIVE_TAG
                }
            }
            return paymentType
        }

        fun generateReference(context: Context,isrequiredNew:Boolean = false): String {
            val ctx = WeakReference(context).get()!!
            val pref = AppPreferencesHelper(ctx.getSharedPreferences(AppConstant.PREF_NAME,
                MultiDexApplication.MODE_PRIVATE
            ))
            var reference = ""
            if(isrequiredNew || pref.logReferenceNo.isNullOrEmpty())
            {

                val sn = getSN()!!
                val stationCode = pref.getReferenceModel()!!.terminal!!.stationId
                var serialNumber = sn
                if(sn.isNotEmpty() && sn.length>4){
                    serialNumber = sn.takeLast(4)
                }
                val seconds = TimeUnit.SECONDS.toSeconds(System.currentTimeMillis())/1000
                reference = "$stationCode"+serialNumber + seconds
            }
            else
            {
                reference = pref.logReferenceNo
            }


            return reference
        }
        fun generateNewReferenceNumber(context: Context):String{
            val ctx = WeakReference(context).get()!!
            val pref = AppPreferencesHelper(ctx.getSharedPreferences(AppConstant.PREF_NAME,
                MultiDexApplication.MODE_PRIVATE
            ))
            var reference = ""
                val sn = getSN()!!
                val stationCode = pref.getReferenceModel()!!.terminal!!.stationId
                var serialNumber = sn
                if(sn.isNotEmpty() && sn.length>4){
                    serialNumber = sn.takeLast(4)
                }
                val seconds = TimeUnit.SECONDS.toSeconds(System.currentTimeMillis())/1000
                reference = "$stationCode"+serialNumber + seconds
                pref.logReferenceNo = reference
            return reference
        }

        private fun dateToStringForReferenceTeleCollect(date: String): String {
            return date.replace("-".toRegex(), "").replace(" ".toRegex(), "").replace(":".toRegex(), "")
        }

        fun stringToDate(strDate: String?): Date? {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH)
            var date: Date? = null
            try {
                date = sdfDate.parse(strDate)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            return date
        }

        fun stringToTime(strDate: String?): Date? {
            val sdfDate = SimpleDateFormat("HH:mm:ss", Locale.ENGLISH)
            var date: Date? = null
            try {
                date = sdfDate.parse(strDate)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            return date
        }

        fun writeFile(data: String, fileName: String?, mContext: Context) {
            //String baseDir = Environment.getExternalStorageDirectory().getAbsolutePath();
            val ctx = WeakReference(mContext).get()!!
            val outputStream: FileOutputStream?
            try { //*** *** ***
                outputStream = ctx.openFileOutput(fileName, Context.MODE_PRIVATE)
                if (outputStream != null) {
                    outputStream.write(data.toByteArray())
                    outputStream.close()
                }
            } catch (e: Exception) {
                e.printStackTrace()
                log(TAG, "problem occurred while writing file")
            }
        }

        @Throws(NoSuchAlgorithmException::class, IOException::class)
        fun generateSHA1(file: File?): String? {
            val messageDigest = MessageDigest.getInstance("SHA1")
            BufferedInputStream(FileInputStream(file)).use { `is` ->
                val buffer = ByteArray(1024)
                var read = 0
                while (`is`.read(buffer).also { read = it } != -1) {
                    messageDigest.update(buffer, 0, read)
                }
            }
            Formatter().use { formatter ->
                for (b in messageDigest.digest()) {
                    formatter.format("%02x", b)
                }
                return formatter.toString()
            }
        }

        fun generateMD5(s: String): String? {
            try {
                // Create MD5 Hash
                val digest = MessageDigest.getInstance("MD5")
                digest.update(s.toByteArray())
                val messageDigest = digest.digest()

                // Create Hex String
                val hexString = StringBuffer()
                for (i in messageDigest.indices) hexString.append(
                    Integer.toHexString(
                        0xFF and messageDigest[i]
                            .toInt()
                    )
                )
                return hexString.toString()
            } catch (e: NoSuchAlgorithmException) {
                e.printStackTrace()
            }
            return ""
        }

        fun getUrlWS(c: Context): String? {
            val ctx = WeakReference(c).get()!!
            // shared preferences file
            val sharedPreferences = PreferenceManager.getDefaultSharedPreferences(ctx)
            val urlWS = sharedPreferences.getString(AppConstant.URL_WS, null)
            log(TAG, "urlWS => $urlWS")
            return urlWS
        }
        fun updateBlockage(c: Context?) {
            val ctx = WeakReference(c).get()!!
            val sharedPreferences = PreferenceManager.getDefaultSharedPreferences(ctx)
            val editor = sharedPreferences.edit()
            editor.putBoolean("blockage", false)
            editor.putLong("tlcTimeStamp", System.currentTimeMillis())
            editor.apply()
        }
        fun getFormatDate(date: Date?): String? {
            val sdfDate2 = SimpleDateFormat("yyyy/MM/dd - HH:mm:ss", Locale.ENGLISH)
            val strDate2 = sdfDate2.format(date)
            Log.v("getDateComparison", "date String -> $strDate2")
            return strDate2
        }

        fun xmlToJsonString(message: String): String? {
            var obj = ""
            try {
                obj = XML.toJSONObject(message.trim { it <= ' ' }).toString()
            } catch (e: JSONException) {
                e.printStackTrace()
            }
            return obj
        }
        fun splitIpAndPortFromUrl(url: String): Array<String> {
            val regex = "^[0-9-+().:]*$".toRegex()
            var url = url
            log(TAG, "%%%%%%%%%%%%%%%%%%% $url %%%%%%%%%%%%%%%%%%%")
            val fusion_url = arrayOf("", "")
            if (!url.matches(regex)) {
                val data = url.replace(":/", "")
                    .toCharArray() //removing http https from url and converting  string to array
                url = ""
                for (ch in data) {
                    if ((ch.toString() + "").matches(regex)) url += ch
                }
            }
            if (url.contains(":")) {
                try {
                    val fusionUrl = url.split(":").toTypedArray()
                    fusion_url[0] = fusionUrl[0]
                    fusion_url[1] = fusionUrl[1]
                } catch (e: Exception) {
                    fusion_url[0] = url
                    fusion_url[1] = "4710"
                }
            } else {
                fusion_url[0] = url
                fusion_url[1] = "4710"
            }
            return fusion_url
        }
        fun dateToStringForReferenceTelecollecte(date: String): String? {
            return date.replace("-".toRegex(), "").replace(" ".toRegex(), "")
                .replace(":".toRegex(), "")
        }
        fun formatDoubleAffichage(d: Double): String? {
            val formatter = NumberFormat.getInstance(Locale.US) as DecimalFormat
            val symbols = formatter.decimalFormatSymbols
            symbols.groupingSeparator = ' '
            formatter.decimalFormatSymbols = symbols
            return formatter.format(d) //.replaceAll(","," ");
        }
        fun setNavigationStatus(c: Context?, data: Boolean) {
            val ctx = WeakReference(c).get()!!
            val sharedPreferences = PreferenceManager.getDefaultSharedPreferences(ctx)
            val editor = sharedPreferences.edit()
            editor.putBoolean("navigationStatus", data)
            editor.apply()
            log(TAG, "navigationStatus => $data")
        }
        fun getFusionProductName(context: Context,productNo: Int): String? {
            val ctx = WeakReference(context).get()!!
            val pref = AppPreferencesHelper(ctx.getSharedPreferences(AppConstant.PREF_NAME,
                MultiDexApplication.MODE_PRIVATE
            ))
            var fusionProductList: List<ProductPT?> = ArrayList<ProductPT?>()

            val jsonString = pref.getProductTable()
            try {
                if (jsonString != null && jsonString.isNotEmpty()) {
                    fusionProductList = Gson().fromJson(
                        jsonString,
                        ServiceResponseGPT::class.java
                    ).serviceResponse.fDCdata.fuelProducts.product
                    if (fusionProductList.isNotEmpty()) {
                        for (product in fusionProductList) {

                            if (productNo == product.productNo) {
                                log(TAG, "Fusion Product found.")
                                return product.productName
                            }
                        }
                    }
                }
            } catch (e: NullPointerException) {
                e.printStackTrace()
            }
            return ""
        }
        fun getFusionProductName(productNo: Int): String? {
            val pref = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME,
                MultiDexApplication.MODE_PRIVATE
            ))
            var fusionProductList: List<ProductPT?> = ArrayList<ProductPT?>()

            val jsonString = pref.getProductTable()
            try {
                if (jsonString != null && jsonString.isNotEmpty()) {
                    fusionProductList = Gson().fromJson(jsonString, ServiceResponseGPT::class.java).serviceResponse.fDCdata.fuelProducts.product
                    if (fusionProductList.isNotEmpty()) {
                        for (product in fusionProductList) {

                            if (productNo == product.productNo) {
                                log(TAG, "Fusion Product found.")
                                return product.productName
                            }
                        }
                    }
                }
            } catch (e: NullPointerException) {
                e.printStackTrace()
            }
            return ""
        }
        fun log(tag: String?,message: String){
//            if(!BuildConfig.DEBUG){
//                log( tag, message)
//            }
        }
        fun showExceptionErrorPrint(ctx: Context, activity: Activity) {
            gotoAbortMessageActivity(ctx.getString(R.string.error),ctx.getString(R.string.get_your_receipt_from_duplicate))
        }
        open fun gotoAbortMessageActivity(title: String, msg: String?) {
            try {
                if (!(this as Activity).isFinishing) {
                    val dialog = Dialog(this)
                    dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
                    dialog.setCancelable(false)
                    dialog.setContentView(R.layout.dialog_failed_message)
                    dialog.window!!.setBackgroundDrawableResource(android.R.color.transparent)
                    val tvTitle = dialog.findViewById<TextView>(R.id.title)
                    val tvMessage = dialog.findViewById<TextView>(R.id.message)
                    val dialogButton = dialog.findViewById<TextView>(R.id.action_done)

                    tvTitle.text = title
                    tvMessage.text = msg

                    dialogButton.setOnClickListener {

                               val intent= Intent(this, MenuActivity::class.java)
                                startActivity(intent)
                                finish()
                            }
                    dialog.show()
                    }
            } catch (e:Exception){
                e.printStackTrace()
            }
        }
        fun showExceptionError(ctx: Context, activity: Activity) {
            gotoAbortMessageActivity(ctx.getString(R.string.error),ctx.getString(R.string.try_again))
        }
        /*fun hashPan(etText: String?): String {
            var text = etText!!
            return if(text!=null && text.isNotEmpty() && (text.length == 19 || text.length == 3)) {
                text = if (text.length == 3) {
                    "XXXXXXXXXXXXXXX" + text.substring(0, 3)
                } else {
                    "XXXXXXXXXXXXXXX" + text.substring(15, 19)
                }
                text
            } else
                ""
        }*/
        fun hashPan(text: String?, lastDigits:Int = 6): String {
            var text = text
            return if(text!=null && text.isNotEmpty() && (text.length == 19 || text.length == 3)) {
                text = if (text.length == 3) {
                    "********" + text.substring(0, 3)
                } else {
                    "*****" + text.takeLast(lastDigits)
                }
                text
            } else
                text!!
        }
        fun maskString(data:String, maskedChar: String, unmaskedLength:Int): String {
            return try {
                data.replace("\\w(?=\\w{$unmaskedLength})".toRegex(),maskedChar)
            } catch (e:Exception){
                data
            }
        }

        fun getDesignationProductFromId(idProduct: Int): String {
            var carburant = ""
            carburant = when (idProduct) {
                1 -> "SUPER SANS PLOMB"
                2 -> "GASOIL"
                3 -> "MELANGE"
                4 -> "BOUTIQUE"
                5 -> "LAVAGE"
                6 -> "LUBRIFIANTS"
                7 -> "VIDANGE"
                8 -> "PNEUMATIQUE"
                9 -> "ANNULATION TRX"
                10 -> "RECHARGE"
                11 -> "RECHARGE"
                else -> "INCONNU"
            }
            return carburant
        }

        fun getResizedBitmap(bm: Bitmap, newWidth: Int, newHeight: Int): Bitmap? {
            val width = bm.width
            val height = bm.height
            val scaleWidth = newWidth.toFloat() / width
            val scaleHeight = newHeight.toFloat() / height
            // CREATE A MATRIX FOR THE MANIPULATION
            val matrix = Matrix()
            // RESIZE THE BIT MAP
            matrix.postScale(scaleWidth, scaleHeight)

            // "RECREATE" THE NEW BITMAP
            val resizedBitmap = Bitmap.createBitmap(
                bm, 0, 0, width, height, matrix, false
            )
            bm.recycle()
            return resizedBitmap
        }


        fun processingBitmap(src: Bitmap): Bitmap? {
            val dest = Bitmap.createBitmap(
                src.width, src.height, src.config
            )
            for (x in 0 until src.width) {
                for (y in 0 until src.height) {
                    val pixelColor = src.getPixel(x, y)
                    val pixelAlpha = Color.alpha(pixelColor)
                    val pixelRed = Color.red(pixelColor)
                    val pixelGreen = Color.green(pixelColor)
                    val pixelBlue = Color.blue(pixelColor)
                    val pixelBW = (pixelRed + pixelGreen + pixelBlue) / 3
                    val newPixel = Color.argb(
                        pixelAlpha, pixelBW, pixelBW, pixelBW
                    )
                    dest.setPixel(x, y, newPixel)
                }
            }
            return dest
        }
        fun logoImageBytes(context: Context): ByteArray? {
            var bytes: ByteArray? = null
            try {

                val mPref = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, Service.MODE_PRIVATE))

                /*val inputStream: InputStream = if (BuildConfig.DEBUG) FileInputStream(
                    Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).toString() + File.separator + AppConstant.LOGO_NAME+".png") else FileInputStream(
                    context.filesDir.toString() + File.separator + AppConstant.LOGO_NAME+".png")*/

                val inputStream: InputStream =
                    if (BuildConfig.DEBUG) FileInputStream(
                    mPref.logoPath)
                    else FileInputStream(mPref.logoPath)
                bytes = IOUtils.toByteArray(inputStream)
            } catch (e: FileNotFoundException) {
                log(TAG,"File not found")
                //e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
            //printCmdList.add(new PrintCmd(bytes,128, AlignmentType.CENTER, PrintContentType.IMAGE));
            return bytes
        }

        fun footerImageBytes(context: Context): ByteArray? {
            val ctx = WeakReference(context).get()!!
            var bytes: ByteArray? = null
            try {
                val inputStream: InputStream = ctx.assets.open(context.resources.getString(R.string.ticket_footer))
                bytes = IOUtils.toByteArray(inputStream)
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            } catch (e: IOException) {
                e.printStackTrace()
            } catch (e: FileNotFoundException) {
                e.printStackTrace()
            }
            //printCmdList.add(new PrintCmd(bytes,128, AlignmentType.CENTER, PrintContentType.IMAGE));
            return bytes
        }

        fun byteListToByteArray(arrayList: ArrayList<Byte>): ByteArray {
            val array = ByteArray(arrayList.size)
            for (i in arrayList.indices) {
                array[i] = arrayList[i]
            }
            return array
        }


        fun getProductModel(id:String) : ProductModel? {
            var product : ProductModel? = null
            try {
                val productDAO = ProductsDao()
                productDAO.open()
                product = productDAO.getProductById(id.toInt())
                productDAO.close()

            } catch (e:Exception) {
                e.printStackTrace()
            }
            return product
        }

        fun getTerminalModel(): TerminalModel? {
            var terminal: TerminalModel? = null
            try {
                val mTerminalDAO = TerminalDao()
                if (!mTerminalDAO.isOpen()) mTerminalDAO.open()
                terminal = mTerminalDAO.getCurrent()
                mTerminalDAO.close()
            } catch (ex: Exception) {
                log(TAG, ex.message!!)
            }
            return terminal
        }

        var esdModel : EsdSignModel? = null
        fun prepareEsdMessage(trxID:Int): String {
            var message = ""

            val esdSignatureDAO = ESDSignatureDao()
            esdSignatureDAO.open()
            esdModel = esdSignatureDAO.getSignedTransaction(trxID)
            esdSignatureDAO.close()

            val terminal: TerminalModel = getTerminalModel()!!

            log(TAG,"ESD TRX:: ${Gson().toJson(esdModel)}")

            val product = getProductModel(esdModel!!.productNo!!)
            val prefs = MainApp.getPrefs()
            val currency = prefs.currency

            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product.libelle = fusionProductName
                }
            }

            message += dateToStringH(Date()) + " (" + terminal.terminalId + ")"
            message +=  "\nFUEL"
            if (terminal != null) {
                message += "\n${terminal.stationName} (${terminal.stationId})"
                message += "\n${terminal.address}"
            }

            message +=  "\n${if (terminal != null) terminal.city else "***"}"
            if (terminal != null)
                message += "\nIF : ${terminal.fiscalId}"

            if(product!=null)
                message += "\nPRODUCT : ${product.libelle}"

            message += "\n"+MainApp.appContext.resources.getString(R.string.amount_pay) + " : " + esdModel!!.amount + " " + currency
            message += "\nDATE : " + esdModel!!.time
            message += "\nPU : ${esdModel!!.unitPrice} $currency"
            message += "\nQTY : ${esdModel!!.volume} L"
            message += "\nPUMP : ${esdModel!!.pumpNumber}"

            log(TAG,"ESD PACKET: $message")
            return message
        }
        fun prepareEsdMessage(trx:TransactionModel): String {
            var message = ""

            val terminal: TerminalModel = getTerminalModel()!!

            val product = getProductModel(trx.idProduit.toString())
            val prefs = MainApp.getPrefs()
            val currency = prefs.currency

            if (product != null) {
                val fusionProductName = Support.getFusionProductName(product.fcc_prod_id)
                if (fusionProductName!!.isNotEmpty()) {
                    product.libelle = fusionProductName
                }
            }

            message += dateToStringH(Date()) + " (" + terminal.terminalId + ")"
            message +=  "\nFUEL"
            if (terminal != null) {
                message += "\n${terminal.stationName} (${terminal.stationId})"
                message += "\n${terminal.address}"
            }

            message +=  "\n${if (terminal != null) terminal.city else "***"}"
            if (terminal != null)
                message += "\nIF : ${terminal.fiscalId}"

            if(product!=null)
                message += "\nPRODUCT : ${product.libelle}"

            message += "\n"+MainApp.appContext.resources.getString(R.string.amount_pay) + " : " + esdModel!!.amount + " " + currency
            message += "\nDATE : " + trx.dateTransaction
            message += "\nPU : ${trx.unitPrice} $currency"
            message += "\nQTY : ${trx.quantite} L"
            message += "\nPUMP : ${trx.pumpId}"

            log(TAG,"ESD PACKET: $message")
            return message
        }
        fun printAllEsdTransactions() {
            val esdSignatureDAO = ESDSignatureDao()
            if (!esdSignatureDAO.isOpen()) esdSignatureDAO.open()
            val transactions: List<EsdSignModel> = esdSignatureDAO.getAllTransactions()
            esdSignatureDAO.close()
            for (trx in transactions) {
                log(TAG,
                    "ESD TRX:: " + trx.id.toString() + ", " + trx.sequenceNumber
                        .toString() + ", " + trx.pumpNumber
                        .toString() + ", " + trx.productNo + ", " + trx.amount
                        .toString() + ", " + trx.volume.toString() + ", " + trx.time
                        .toString() + ", " + trx.signature
                        .toString() + ", TEL_STATUS: " + trx.flag
                )
            }
        }

        fun createTransactionTaxiModel(transaction: EsdSignModel): TransactionModel {
            val transactionTaxi = TransactionModel()

            transactionTaxi.pumpId =  transaction.pumpNumber+""
            transactionTaxi.amount = transaction.amount!!.toDouble()
            transactionTaxi.quantite = transaction.volume!!.toDouble()
            transactionTaxi.idProduit = transaction.productNo!!.toInt()
            transactionTaxi.sequenceController = transaction.sequenceNumber

            return transactionTaxi
        }

        fun getDateTicket(date: Date): String? {
            val dateFormat: DateFormat = SimpleDateFormat("dd/MM/yyyy HH:mm:ss",Locale.ENGLISH)
            return dateFormat.format(date)
        }
        fun toGrayscale(bmp: Bitmap): Bitmap? {
            val width = bmp.width
            val height = bmp.height
            val pixels = IntArray(width * height)
            bmp.getPixels(pixels, 0, width, 0, 0, width, height)
            val alpha = 0xFF shl 24 // ?bitmap?24?
            for (i in 0 until height) {
                for (j in 0 until width) {
                    var grey = pixels[width * i + j]
                    val red = grey and 0x00FF0000 shr 16
                    val green = grey and 0x0000FF00 shr 8
                    val blue = grey and 0x000000FF
                    grey = (red * 0.3 + green * 0.59 + blue * 0.11).toInt()
                    grey = alpha or (grey shl 16) or (grey shl 8) or grey
                    pixels[width * i + j] = grey
                }
            }
            val newBmp = Bitmap.createBitmap(width, height, Bitmap.Config.RGB_565)
            newBmp.setPixels(pixels, 0, width, 0, 0, width, height)
            return newBmp
        }
        fun appendZeroRequestID(string: String):String
        {
            val data = StringUtils.leftPad(string, 32, "0")
            Log.i(TAG,"RequestID : $data")
            return data

        }
        fun getExternalSdCardAppDir(mContext: Context, isOffline: Boolean): String? {
            //create folder
            var dirName = "/OfflineTeleCollect Files"
            if (!isOffline) dirName = "/TeleCollect Files"
            val file = File(
                Environment.getExternalStorageDirectory()
                    .toString() + "/" + mContext.getString(R.string.app_name) + dirName
            )
            if (!file.mkdirs()) {
                file.mkdirs()
            }
            //String filePath = file.getAbsolutePath() + File.separator + fileName;
            return file.absolutePath
        }

        private fun getSHA1(file: File): String? {
            return try {
                val hc: HashCode = Files.asByteSource(file).hash(Hashing.sha1())
                val sha1: String = hc.toString()
                Log.e(TAG, "Checksum SHA1: $sha1")
                sha1
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }

        fun exportFileToSdCard(src: File, mContext: Context, isOfflineDir: Boolean): File? {
            return try {
                val checksum: String? = getSHA1(src)
                val extension: String = Files.getFileExtension(src.name)
                val fileName: String = Files.getNameWithoutExtension(src.name)
                Log.e(
                    "exportFileToSdCard",
                    "==================> copy file to sd card: $fileName ---- $extension"
                )
                Log.e(
                    "exportFileToSdCard",
                    "==================> path sd card: " + getExternalSdCardAppDir(
                        mContext,
                        isOfflineDir
                    )
                )
                val dst = File(getExternalSdCardAppDir(mContext, isOfflineDir))
                //File dst = SDCard.findSdCardPath(mContext);

                //if folder does not exist
                if (!dst.exists()) {
                    if (!dst.mkdir()) {
                        return null
                    }
                }
                val expFile =
                    File(dst.path + File.separator.toString() + fileName + "_" + checksum + "." + extension)
                var inChannel: FileChannel? = null
                var outChannel: FileChannel? = null
                try {
                    inChannel = FileInputStream(src).channel
                    outChannel = FileOutputStream(expFile).channel
                } catch (e: FileNotFoundException) {
                    e.printStackTrace()
                }
                try {
                    inChannel!!.transferTo(0, inChannel.size(), outChannel)
                } finally {
                    if (inChannel != null) inChannel.close()
                    if (outChannel != null) outChannel.close()
                }
                expFile
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }

        fun getExternalSdCardAppDir(dirName:String, mContext: Context): String? {
            val ctx = WeakReference(mContext).get()!!
            //create folder
            val file = File(Environment.getExternalStorageDirectory().toString() + "/" + ctx.getString(R.string.app_name) + "/" + dirName)
            if (!file.mkdirs()) {
                file.mkdirs()
            }
            //String filePath = file.getAbsolutePath() + File.separator + fileName;
            return file.absolutePath
        }
        fun exportRefundFile(src: File, mContext: Context): File? {
            return try {
                val checksum: String? = getSHA1(src)
                val extension: String = Files.getFileExtension(src.name)
                val fileName: String = Files.getNameWithoutExtension(src.name)

                val dst = File(getExternalSdCardAppDir("Refund Files",mContext))
                //File dst = SDCard.findSdCardPath(mContext);

                //if folder does not exist
                if (!dst.exists()) {
                    if (!dst.mkdir()) {
                        return null
                    }
                }
                val expFile = File(dst.path + File.separator.toString() + fileName + "_" + checksum + "." + extension)
                var inChannel: FileChannel? = null
                var outChannel: FileChannel? = null
                try {
                    inChannel = FileInputStream(src).channel
                    outChannel = FileOutputStream(expFile).channel
                } catch (e: FileNotFoundException) {
                    e.printStackTrace()
                }
                try {
                    inChannel!!.transferTo(0, inChannel.size(), outChannel)
                } finally {
                    if (inChannel != null) inChannel.close()
                    if (outChannel != null) outChannel.close()
                }
                expFile
            } catch (e: IOException) {
                e.printStackTrace()
                null
            }
        }


    }
}

class Connectivity {
    companion object {
        private val  TAG = Connectivity::class.simpleName
        fun disableWifi(c: Context) {
            val ctx = WeakReference(c).get()!!
            val wifiManager = ctx.getSystemService(Context.WIFI_SERVICE) as WifiManager
            if (wifiManager.isWifiEnabled) {
                wifiManager.isWifiEnabled = false
            }
        }
        fun isOnlinePing(): Boolean {
            val runtime = Runtime.getRuntime()
            try {
                val ipProcess = runtime.exec("/system/bin/ping -c 1 *******")
                val exitValue = ipProcess.waitFor()
                return exitValue == 0
            } catch (e: IOException) {
                e.printStackTrace()
            } catch (e: InterruptedException) {
                e.printStackTrace()
            }
            return false
        }
        fun isOnlineSocket(ctx: Context): Boolean {
            return try {
                val timeoutMs = 2500
                val sock = Socket()
                val sockAddress: SocketAddress = InetSocketAddress(Support.getUrlWS(ctx)!!.replace("http://", "").replace("https://", "").replace("/", ""), 80)
                sock.connect(sockAddress, timeoutMs)
                sock.close()
                log(TAG, "isOnlineSocket true")
                true
            } catch (e: IOException) {
                log(TAG, "isOnlineSocket false")
                false
            }
        }
        fun checkActiveInternetConnection(context: Context): Boolean {
            if (isNetworkAvailable(context)) {
                try {
                    val urlc = URL("https://www.google.com").openConnection() as HttpsURLConnection
                    urlc.setRequestProperty("User-Agent", "Test")
                    urlc.setRequestProperty("Connection", "close")
                    urlc.connectTimeout = 1500
                    urlc.connect()
                    return urlc.responseCode == 200
                } catch (e: IOException) {
                    log(TAG, "WIFI CONNECTION Error: "+e.message+" "+e.cause)
                }
            } else {
                log(TAG, "CONNECTION WIFI : No network present")
            }
            return false
        }
         fun isNetworkAvailable(context: Context): Boolean {
             val ctx = WeakReference(context).get()!!
            val manager = ctx.getSystemService(Context.CONNECTIVITY_SERVICE) as ConnectivityManager
            val networkInfo = manager.activeNetworkInfo
            var isAvailable = false
            if (networkInfo != null && networkInfo.isConnected) {
                // Network is present and connected
                isAvailable = true
            }
            return isAvailable
        }
        fun isConnected(host:String): Boolean {
            return try {
                val p1 = Runtime.getRuntime().exec("ping -c 1 $host")
                val returnVal = p1.waitFor()
                returnVal == 0
            } catch (e: Exception) {
                false
            }
        }

        fun isHostReachable(hostUrl: String?): Boolean {
            try {
                val url = URL(hostUrl)
                val urlc = url.openConnection() as HttpURLConnection
                urlc.setRequestProperty("User-Agent", "Android Application")
                urlc.setRequestProperty("Connection", "close")
                urlc.connectTimeout = 10 * 1000
                urlc.connect()
                if (urlc.responseCode === 200) {
                    return true
                }
            } catch (e: Throwable) {
                Log.d("Connection = > ", e.message!!)
                //e.printStackTrace();
            }
            return false
        }

        fun isConnectionFast(type: Int, subType: Int): Boolean {
            return if (type == ConnectivityManager.TYPE_WIFI) {
                true
            } else if (type == ConnectivityManager.TYPE_MOBILE) {
                when (subType) {
                    TelephonyManager.NETWORK_TYPE_1xRTT -> false // ~ 50-100 kbps
                    TelephonyManager.NETWORK_TYPE_CDMA -> false // ~ 14-64 kbps
                    TelephonyManager.NETWORK_TYPE_EDGE -> false // ~ 50-100 kbps
                    TelephonyManager.NETWORK_TYPE_EVDO_0 -> true // ~ 400-1000 kbps
                    TelephonyManager.NETWORK_TYPE_EVDO_A -> true // ~ 600-1400 kbps
                    TelephonyManager.NETWORK_TYPE_GPRS -> false // ~ 100 kbps
                    TelephonyManager.NETWORK_TYPE_HSDPA -> true // ~ 2-14 Mbps
                    TelephonyManager.NETWORK_TYPE_HSPA -> true // ~ 700-1700 kbps
                    TelephonyManager.NETWORK_TYPE_HSUPA -> true // ~ 1-23 Mbps
                    TelephonyManager.NETWORK_TYPE_UMTS -> true // ~ 400-7000 kbps
                    TelephonyManager.NETWORK_TYPE_EHRPD -> true // ~ 1-2 Mbps
                    TelephonyManager.NETWORK_TYPE_EVDO_B -> true // ~ 5 Mbps
                    TelephonyManager.NETWORK_TYPE_HSPAP -> true // ~ 10-20 Mbps
                    TelephonyManager.NETWORK_TYPE_IDEN -> false // ~25 kbps
                    TelephonyManager.NETWORK_TYPE_LTE -> true // ~ 10+ Mbps
                    TelephonyManager.NETWORK_TYPE_UNKNOWN -> false
                    else -> false
                }
            } else {
                false
            }
        }

    }

}