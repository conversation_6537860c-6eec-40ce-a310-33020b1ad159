package app.rht.petrolcard.utils.esdPrinterJobs
import com.google.gson.annotations.SerializedName


data class PaidTransactionModel(
    @SerializedName("contenu")
    val contenu: List<PaidTransaction>?,
    @SerializedName("error")
    val error: Any?,
    @SerializedName("reponse")
    val reponse: String?
)

data class PaidTransaction(
    @SerializedName("amount")
    val amount: Double?,
    @SerializedName("productId")
    val productId: Int?,
    @SerializedName("pumpId")
    val pumpId: Any?,
    @SerializedName("trxSeq")
    val trxSeq: Any?
)