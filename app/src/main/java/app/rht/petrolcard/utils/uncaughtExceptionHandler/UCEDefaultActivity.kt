/*
 *
 *  * Copyright © 2018-19 <PERSON><PERSON><PERSON>.
 *  *
 *  *    Licensed under the Apache License, Version 2.0 (the "License");
 *  *    you may not use this file except in compliance with the License.
 *  *    You may obtain a copy of the License at
 *  *
 *  *        http://www.apache.org/licenses/LICENSE-2.0
 *  *
 *  *    Unless required by applicable law or agreed to in writing, software
 *  *    distributed under the License is distributed on an "AS IS" BASIS,
 *  *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  *    See the License for the specific language governing permissions and
 *  * limitations under the License.
 *
 */
package app.rht.petrolcard.utils.uncaughtExceptionHandler

import android.annotation.SuppressLint
import android.app.AlarmManager
import android.app.AlertDialog
import android.app.PendingIntent
import android.content.*
import android.content.pm.PackageManager
import android.os.AsyncTask
import android.os.Build
import android.os.Bundle
import android.os.Environment
import android.text.TextUtils
import android.util.Log
import android.util.TypedValue
import android.view.View
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.FileProvider
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.R
import app.rht.petrolcard.baseClasses.activity.BaseActivity
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.ui.startup.activity.SplashScreenActivity
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extractEmails
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.lang.ref.WeakReference
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*


class UCEDefaultActivity : BaseActivity<CommonViewModel>(CommonViewModel::class) {
    private var txtFile: File? = null
    private var strCurrentErrorLog: String? = null

    @SuppressLint("PrivateResource")
    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(android.R.style.Theme_Holo_Light_DarkActionBar)
        super.onCreate(savedInstanceState)
        setContentView(R.layout.default_error_activity)

        //saveErrorLogToFile(true); //saving logs directly
        emailErrorLog()
        findViewById<View>(R.id.button_restart_application).setOnClickListener { restartApplication() }
        findViewById<View>(R.id.button_close_app).setOnClickListener {
            UCEHandler.closeApplication(this@UCEDefaultActivity)
        }
        findViewById<View>(R.id.button_copy_error_log).setOnClickListener { copyErrorToClipboard() }
        findViewById<View>(R.id.button_share_error_log).setOnClickListener { shareErrorLog() }
        findViewById<View>(R.id.button_save_error_log).setOnClickListener {
            saveErrorLogToFile(true)
        }
        findViewById<View>(R.id.button_email_error_log).setOnClickListener { emailErrorLog() }
        findViewById<View>(R.id.button_view_error_log).setOnClickListener {
            val dialog =
                AlertDialog.Builder(this@UCEDefaultActivity)
                    .setTitle("Error Log")
                    .setMessage(getAllErrorDetailsFromIntent(this@UCEDefaultActivity, intent))
                    .setPositiveButton("Copy Log & Close"
                    ) { dialog, which ->
                        copyErrorToClipboard()
                        dialog.dismiss()
                    }
                    .setNeutralButton("Close"
                    ) { dialog, which -> dialog.dismiss() }
                    .show()
            val textView = dialog.findViewById<TextView>(android.R.id.message)
            textView?.setTextSize(TypedValue.COMPLEX_UNIT_SP, 16f)
        }
    }

    private fun restartApplication() {
        prefs.isRestartApplication = "true"
        val mStartActivity = Intent(this@UCEDefaultActivity, SplashScreenActivity::class.java)
        val mPendingIntentId = 123456
        val mPendingIntent = PendingIntent.getActivity(this@UCEDefaultActivity,
            mPendingIntentId,
            mStartActivity,
            PendingIntent.FLAG_CANCEL_CURRENT)
        val mgr = this.getSystemService(ALARM_SERVICE) as AlarmManager
        mgr[AlarmManager.RTC, System.currentTimeMillis() + 100] = mPendingIntent
        System.exit(0)
        Runtime.getRuntime().gc()
    }

    fun getApplicationName(context: Context): String {
        val applicationInfo = context.applicationInfo
        val stringId = applicationInfo.labelRes
        return if (stringId == 0) applicationInfo.nonLocalizedLabel.toString() else context.getString(
            stringId)
    }

    private fun getVersionName(context: Context): String {
        return try {
            val ctx = WeakReference(context).get()!!
            val packageInfo = ctx.packageManager.getPackageInfo(ctx.packageName, 0)
            packageInfo.versionName
        } catch (e: Exception) {
            "Unknown"
        }
    }

    private fun getVersionCode(context: Context): String {
        return try {
            val ctx = WeakReference(context).get()!!
            val packageInfo = ctx.packageManager.getPackageInfo(ctx.packageName, 0)
            packageInfo.versionName
        } catch (e: Exception) {
            "Unknown"
        }
    }

    private fun getActivityLogFromIntent(intent: Intent): String? {
        return intent.getStringExtra(UCEHandler.EXTRA_ACTIVITY_LOG)
    }

    private fun getStackTraceFromIntent(intent: Intent): String? {
        return intent.getStringExtra(UCEHandler.EXTRA_STACK_TRACE)
    }



    inner class SendMailTask(var emailConfig: EmailConfig) : AsyncTask<Void?, Void?, Void?>() {
        var error: java.lang.Exception? = null
        var username = ""
        var pass =  ""
        var subject = ""
        var texts = ""
        var toAddress = ""

        @Throws(java.lang.Exception::class)
        private fun sendMail() {
            val tos = extractEmails(toAddress)

            val m = EmailSender(username, pass)
            m._from = username
            m._to = tos
            m._subject = subject
            m.body = texts


            if (m.send()) {
                log("UCEDefaultActivity","ANR Email sent")
            } else {
                log("UCEDefaultActivity","ANR Email failed to send.")
            }

            /*// 1 - Create one instance
            val m =
                EmailSender()

            // 2 - Set addressees
            m.setCredentials(username, pass).setToAddresses(tos)

            // 3 - Set the content of the mail
            m.setSubject(subject).setMailText(texts)

            // 4 - Attach files if you want
            m.attachFile("ErrorLog.log", txtFile!!.absolutePath)

            // 5 - Set properties to use and send
            m.useMailPropertiesGMail().send()
            //m.useMailPropertiesSNMP(emailConfig.serverHost,emailConfig.serverPort.toInt(),emailConfig.serverPort.toInt(),true).send()*/
        }

        override fun onPreExecute() {

            username = emailConfig.username
            pass =  emailConfig.password
            subject = getApplicationName(this@UCEDefaultActivity) + " Application Crash Error Log"
            texts = getMailBody()
            toAddress = emailConfig.semicolonSeparatedEmailAddresses

            if (username.isEmpty()
                || pass.isEmpty()
                || subject.isEmpty()
                || texts.isEmpty()
                || toAddress.isEmpty()
            ) {
                Toast.makeText(MainApp.appContext, "ERROR: Fill all the fields", Toast.LENGTH_LONG).show()
            } else {
                super.onPreExecute()
            }
        }

        override fun doInBackground(vararg p0: Void?): Void? {
                try {
                    sendMail()
                } catch (e: Exception) {
                    error = e
                }
            return null
        }

        override fun onPostExecute(result: Void?) {
            super.onPostExecute(result)
            if (error == null) {
                Toast.makeText(MainApp.appContext, getString(R.string.mail_sent_success), Toast.LENGTH_LONG).show()
            } else {
                Toast.makeText(MainApp.appContext, getString(R.string.email_not_sent) + error!!.message, Toast.LENGTH_LONG).show()
            }
        }

    }

    private fun getMailBody() : String {
        return  getString(R.string.email_welcome_note) + getAllErrorDetailsFromIntent(this@UCEDefaultActivity, intent)+""
    }

    private fun emailErrorLog() {
        saveErrorLogToFile(true)
        val errorLog = getAllErrorDetailsFromIntent(this@UCEDefaultActivity, intent)
        if (UCEHandler.EMAIL_CONFIG != null) {
            //if(!BuildConfig.DEBUG){
                SendMailTask(UCEHandler.EMAIL_CONFIG).execute()
            /*} else {
                Log.e("Email Sender","------------ Crash log email will send only in release mode ------------")
            }*/

        } else {
            val emailAddressArray = extractEmails(UCEHandler.COMMA_SEPARATED_EMAIL_ADDRESSES)
            val emailIntent = Intent(Intent.ACTION_SEND)
            emailIntent.type = "plain/text"
            emailIntent.putExtra(Intent.EXTRA_EMAIL, emailAddressArray)
            emailIntent.putExtra(Intent.EXTRA_SUBJECT,
                getApplicationName(this@UCEDefaultActivity) + " Application Crash Error Log")
            emailIntent.putExtra(Intent.EXTRA_TEXT,
                getString(R.string.email_welcome_note) + errorLog)
            if (txtFile!!.exists()) {
                val filePath = FileProvider.getUriForFile(this,
                    this.applicationContext.packageName + ".provider",
                    txtFile!!)
                emailIntent.putExtra(Intent.EXTRA_STREAM, filePath)
            }
            emailIntent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
            startActivity(Intent.createChooser(emailIntent, "Email Error Log"))
        }
    }

    private fun saveErrorLogToFile(isShowToast: Boolean) {
        val isSDPresent = Environment.getExternalStorageState() == Environment.MEDIA_MOUNTED
        if (isSDPresent && isExternalStorageWritable) {
            val currentDate = Date()
            val dateFormat: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
            var strCurrentDate = dateFormat.format(currentDate)
            strCurrentDate = strCurrentDate.replace(" ", "_")
            val errorLogFileName =
                getApplicationName(this@UCEDefaultActivity) + "_Error-Log_" + strCurrentDate
            val errorLog = getAllErrorDetailsFromIntent(this@UCEDefaultActivity,
                intent)
            val fullPath =
                Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
                    .toString() + File.separator + AppConstant.LOG_FOLDER_NAME + File.separator /*Environment.getExternalStorageDirectory() + "/AppErrorLogs_UCEH/"*/
            val outputStream: FileOutputStream
            try {
                val file = File(fullPath)
                file.mkdir()
                txtFile = File("$fullPath$errorLogFileName.log")
                txtFile!!.createNewFile()
                outputStream = FileOutputStream(txtFile)
                outputStream.write(errorLog!!.toByteArray())
                outputStream.close()
                if (txtFile!!.exists() && isShowToast) {
                    Toast.makeText(this, R.string.file_saved_successfully, Toast.LENGTH_SHORT).show()
                }
            } catch (e: IOException) {
                Log.e("REQUIRED",
                    "This app does not have write storage permission to save log file.")
                if (isShowToast) {
                    Toast.makeText(this, "Storage Permission Not Found", Toast.LENGTH_SHORT).show()
                }
                e.printStackTrace()
            }
        }
    }

    private fun shareErrorLog() {
        val errorLog = getAllErrorDetailsFromIntent(this@UCEDefaultActivity,
            intent)
        val share = Intent(Intent.ACTION_SEND)
        share.type = "text/plain"
        share.addFlags(Intent.FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET)
        share.putExtra(Intent.EXTRA_SUBJECT, "Application Crash Error Log")
        share.putExtra(Intent.EXTRA_TEXT, errorLog)
        startActivity(Intent.createChooser(share, "Share Error Log"))
    }

    private fun copyErrorToClipboard() {
        val errorInformation = getAllErrorDetailsFromIntent(this@UCEDefaultActivity,
            intent)
        val clipboard = getSystemService(CLIPBOARD_SERVICE) as ClipboardManager
        if (clipboard != null) {
            val clip = ClipData.newPlainText("View Error Log", errorInformation)
            clipboard.setPrimaryClip(clip)
            Toast.makeText(this@UCEDefaultActivity, "Error Log Copied", Toast.LENGTH_SHORT).show()
        }
    }

    private fun getAllErrorDetailsFromIntent(context: Context, intent: Intent): String? {
        return if (TextUtils.isEmpty(strCurrentErrorLog)) {
            val LINE_SEPARATOR = "\n"
            val errorReport = StringBuilder()
            errorReport.append("\n***** EXCEPTION \n")
            errorReport.append("\n***** DEVICE INFO \n")
            errorReport.append("Brand: ")
            errorReport.append(Build.BRAND)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("Device: ")
            errorReport.append(Build.DEVICE)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("Model: ")
            errorReport.append(Build.MODEL)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("Serial No: ")
            errorReport.append(Build.SERIAL)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("Manufacturer: ")
            errorReport.append(Build.MANUFACTURER)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("Product: ")
            errorReport.append(Build.PRODUCT)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("SDK: ")
            errorReport.append(Build.VERSION.SDK)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("Release: ")
            errorReport.append(Build.VERSION.RELEASE)
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("\n***** APP INFO \n")
            val versionName = getVersionName(context)
            errorReport.append("Version: ")
            errorReport.append(versionName)
            errorReport.append(LINE_SEPARATOR)
            val versionCode = getVersionCode(context)
            errorReport.append("Version Code: ")
            errorReport.append(versionCode)
            errorReport.append(LINE_SEPARATOR)
            try {
                errorReport.append("Endpoint: ")
                errorReport.append(prefs.baseUrl)
                errorReport.append(LINE_SEPARATOR)
            } catch (e : Exception){
                e.printStackTrace()
            }
            val currentDate = Date()
            val dateFormat: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
            val firstInstallTime = getFirstInstallTimeAsString(context, dateFormat)
            if (!TextUtils.isEmpty(firstInstallTime)) {
                errorReport.append("Installed On: ")
                errorReport.append(firstInstallTime)
                errorReport.append(LINE_SEPARATOR)
            }
            val lastUpdateTime = getLastUpdateTimeAsString(context, dateFormat)
            if (!TextUtils.isEmpty(lastUpdateTime)) {
                errorReport.append("Updated On: ")
                errorReport.append(lastUpdateTime)
                errorReport.append(LINE_SEPARATOR)
            }
            errorReport.append("Current Date: ")
            errorReport.append(dateFormat.format(currentDate))
            errorReport.append(LINE_SEPARATOR)
            errorReport.append("\n***** ERROR LOG \n")
            errorReport.append(getStackTraceFromIntent(intent))
            errorReport.append(LINE_SEPARATOR)
            val activityLog = getActivityLogFromIntent(intent)
            errorReport.append(LINE_SEPARATOR)
            if (activityLog != null) {
                errorReport.append("\n***** USER ACTIVITIES \n")
                errorReport.append("User Activities: ")
                errorReport.append(activityLog)
                errorReport.append(LINE_SEPARATOR)
            }
            errorReport.append("\n***** END OF LOG *****\n")
            strCurrentErrorLog = errorReport.toString()
            strCurrentErrorLog
        } else {
            strCurrentErrorLog
        }
    }

    private fun getFirstInstallTimeAsString(context: Context, dateFormat: DateFormat): String {
        val firstInstallTime: Long
        return try {
            firstInstallTime = context
                .packageManager
                .getPackageInfo(context.packageName, 0).firstInstallTime
            dateFormat.format(Date(firstInstallTime))
        } catch (e: PackageManager.NameNotFoundException) {
            ""
        }
    }

    private fun getLastUpdateTimeAsString(context: Context, dateFormat: DateFormat): String {
        val lastUpdateTime: Long
        return try {
            lastUpdateTime = context
                .packageManager
                .getPackageInfo(context.packageName, 0).lastUpdateTime
            dateFormat.format(Date(lastUpdateTime))
        } catch (e: PackageManager.NameNotFoundException) {
            ""
        }
    }

    val isExternalStorageWritable: Boolean
        get() {
            val state = Environment.getExternalStorageState()
            return Environment.MEDIA_MOUNTED == state
        }

    override fun setObserver() {

    }
}