package app.rht.petrolcard.utils.helpers;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.SharedPreferences;
import android.net.ConnectivityManager;
import android.net.wifi.WifiManager;
import android.util.Log;

import androidx.preference.PreferenceManager;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import app.rht.petrolcard.MainApp;
import app.rht.petrolcard.utils.constant.AppConstant;


public class HelpersLoyalty {

    private static final String TAG = HelpersLoyalty.class.getSimpleName();

    public static String dateToString(Date date) {
        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        String strDate = sdfDate.format(date);
        return strDate;
    }

    public static String getSN(Context c) {
        return MainApp.Companion.getSn() ;
    } // eof getSN

    public static void setServerUrl(Context c, String url) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(c);
        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("serverUrl", url);
        editor.apply();
    } // eof setServerUrl

    public static String getServerUrl(Context c) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(c);
        String serverUrl = sharedPreferences.getString(AppConstant.URL_WS, null ).replaceAll("-tpe","");//Constantes.URL_WS); Constantes.URL_LOYALTY
        Log.i(TAG, serverUrl);
        return serverUrl;
    } // eof getServerUrl

    public static String getUrlUpdate(Context c) {
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(c);
        String urlUpdate = sharedPreferences.getString(AppConstant.URL_UPDATE, null );

        Log.i(TAG, urlUpdate);
        return urlUpdate;
    } // eof getUrlUpdate

    public static String getHashKey(Context c) {
        return HelpersLoyalty.md5("abcde" + HelpersLoyalty.getSN(c) + "fghij");
    } // eof getSN

    public static String getDateTicket(Date date) {
        DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss",Locale.ENGLISH);
        return dateFormat.format(date);
    } // eof getDateTicket

    public static String getDateUsInStringFormat(Date date) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",Locale.ENGLISH);
        return dateFormat.format(date);
    } // eof getDateUsInStringFormat

    public static String maskPan(String s) {
        int length = s.length();
        //Check whether or not the string contains at least eight characters; if not, this method is useless
        if (length >= 8) {
            return s.substring(0, length - 8) + "XXXXXXXX";
        } else {
            return s;
        }
    } // eof maskPan

    public static String maskCin(String s) {
        int length = s.length();
        //Check whether or not the string contains at least eight characters; if not, this method is useless
        if (length >= 3) {
            return s.substring(0, length - 3) + "XXX";
        } else {
            return s;
        }
    } // eof maskPan

    public static String formatDouble(double d) {

        return String.format(Locale.US,"%.2f", d);


        //NumberFormat formatter = new DecimalFormat("#0.00");
        //String s = String.format("%.2f", d);

        //System.out.println(formatter.format(d));

       /* NumberFormat nf = NumberFormat.getNumberInstance(Locale.US);
        DecimalFormat formatter = (DecimalFormat) nf;
        formatter.applyPattern("###,###.##");*/
        //String fString = formatter.format(vc);

       /* DecimalFormat formatter = (DecimalFormat) NumberFormat.getInstance(Locale.US);
        DecimalFormatSymbols symbols = formatter.getDecimalFormatSymbols();

        symbols.setGroupingSeparator(' ');
        formatter.setDecimalFormatSymbols(symbols);

        return formatter.format(d)  ; //.replaceAll(","," ");*/

        //return formatter.format(d);//String.format("%.2f", d);
    } // eof formatDouble

    public static Date stringToDate(String strDate) {
        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        Date date = null;
        try {
            date = sdfDate.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return date;
    } // eof stringToDate

    public static String md5(String s) {
        try {
            // Create MD5 Hash
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(s.getBytes());
            byte[] messageDigest = digest.digest();

            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            for (int i=0; i<messageDigest.length; i++)
                hexString.append(Integer.toHexString(0xFF & messageDigest[i]));
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    } // eof md5

    public static String stringToDateToStringForScreen(String strDate) {
        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        Date date = null;
        try {
            date = sdfDate.parse(strDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        String s = "";

        DateFormat dateFormat1 = new SimpleDateFormat("dd/MM/yyyy");
        s += "Le " + dateFormat1.format(date);

        DateFormat dateFormat2 = new SimpleDateFormat("HH:mm:ss");
        s += "\nÀ " + dateFormat2.format(date);

        return s;
    } // eof stringToDate

    public static void disableWifi(Context c) {
        @SuppressLint("WifiManagerLeak") WifiManager wifiManager = (WifiManager) c.getSystemService(Context.WIFI_SERVICE);

        if(wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(false);
        }
    } // eof disableWifi

    public static void enableWifi(Context c) {
        @SuppressLint("WifiManagerLeak") WifiManager wifiManager = (WifiManager) c.getSystemService(Context.WIFI_SERVICE);

        if(!wifiManager.isWifiEnabled()) {
            wifiManager.setWifiEnabled(true);
        }
    } // eof enableWifi

    public static void typeOfNetwork(int type, int subType){
        if(type == ConnectivityManager.TYPE_WIFI){
            Log.e(TAG, "WIFI");
        }else if(type == ConnectivityManager.TYPE_MOBILE){
            Log.e(TAG, "Mobile");
        }else{
            Log.e(TAG, "Autre");
        }
    }





    public static String formatDate(Date mTime){

        String myFormat = "dd/MM/yyyy";
        SimpleDateFormat sdf = new SimpleDateFormat(myFormat, Locale.FRANCE);

        return sdf.format(mTime);
    }






} // eof Helpers
