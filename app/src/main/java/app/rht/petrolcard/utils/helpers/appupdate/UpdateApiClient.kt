package ma.petrol.petrolCardxx.helpers.appupdate

import app.rht.petrolcard.utils.helpers.appupdate.UpdateResponse
import okhttp3.OkHttpClient
import retrofit2.Response
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.http.GET

interface UpdateApiClient {
    @GET("/app-update.json")
    suspend fun onUpdateResponse(): Response<UpdateResponse>
}

object UpdateApiAdapter {
    val apiClient: UpdateApiClient = Retrofit.Builder()
        .baseUrl("https://updatechecker.free.beeceptor.com")
        .client(OkHttpClient())
        .addConverterFactory(GsonConverterFactory.create())
        .build()
        .create(UpdateApiClient::class.java)
}