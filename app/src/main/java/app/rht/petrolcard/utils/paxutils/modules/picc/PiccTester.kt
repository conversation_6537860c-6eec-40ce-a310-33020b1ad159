package app.rht.petrolcard.utils.paxutils.modules.picc

import android.os.Handler
import android.os.Message
import android.os.SystemClock
import android.util.Log
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.utils.paxutils.system.SysTester
import app.rht.petrolcard.utils.paxutils.util.BaseTester
import app.rht.petrolcard.utils.paxutils.util.Convert
import app.rht.petrolcard.utils.paxutils.util.IApdu
import app.rht.petrolcard.utils.paxutils.util.Packer
import com.pax.dal.IPicc
import com.pax.dal.entity.*
import com.pax.dal.exceptions.EPiccDevException
import com.pax.dal.exceptions.PiccDevException
import java.lang.Exception

class PiccTester constructor(type: EPiccType) : BaseTester() {
    private val picc: IPicc?

    fun setUp(): PiccPara? {
        try {
            val readParam = picc!!.readParam()
            logTrue("readParam")
            return readParam
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("readParam", e.toString())
            return null
        }
    }

    fun open() {
        try {
            picc!!.open()
            logTrue("open")
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("open", e.toString())
        }
    }

    fun detect(mode: EDetectMode?): PiccCardInfo? {
        try {
            val cardInfo = picc!!.detect(mode)
            logTrue("detect")
            return cardInfo
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("detect", e.toString())
            return null
        }
    }

    fun isoCommand(cid: Byte, send: ByteArray?): ByteArray? {
        try {
            val isoCommand = picc!!.isoCommand(cid, send)
            logTrue("isoCommand")
            return isoCommand
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("isoCommand", e.toString())
            return null
        }
    }

    fun remove(mode: EPiccRemoveMode?, cid: Byte) {
        try {
            picc!!.remove(mode, cid)
            logTrue("remove")
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("remove", e.toString())
        }
    }

    fun setLed(led: Byte) {
        try {
            picc!!.setLed(led)
            logTrue("setLed")
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("setLed", e.message)
        }
    }

    fun close() {
        try {
            if (picc != null) {
                picc.close()
                logTrue("close")
            }
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("close", e.toString())
        }
    }

    fun m1Auth(type: EM1KeyType?, blkNo: Byte, pwd: ByteArray?, serialNo: ByteArray?) {
        try {
            picc!!.m1Auth(type, blkNo, pwd, serialNo)
            logTrue("m1Auth")
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("m1Auth", e.toString())
        }
    }

    fun m1Read(blkNo: Byte): ByteArray? {
        try {
            val result = picc!!.m1Read(blkNo)
            logTrue("m1Read")
            return result
        } catch (e: PiccDevException) {
            e.printStackTrace()
            logErr("m1Read", e.toString())
            return null
        }
    }

    fun detectAorBandCommand(handler: Handler): Int {
        var cardInfo: PiccCardInfo?
        if (null != detect(EDetectMode.ISO14443_AB).also { cardInfo = it }) {
            // open blue and yellow light
            // setLed((byte) 0x0c);
            val apdu: IApdu = Packer.getInstance().apdu
            var apduReq: IApdu.IApduReq? = null
            if (piccType == EPiccType.INTERNAL) {
                // 选择文件名为‘1PAY.SYS.DDF01’的支付系统环境
                apduReq = apdu.createReq(
                    0x00.toByte(), 0xa4.toByte(), 0x04.toByte(), 0x00.toByte(),
                    "1PAY.SYS.DDF01".toByteArray(), 256.toShort()
                )
            } else if (piccType == EPiccType.EXTERNAL) {
                apduReq = apdu.createReq(
                    0x00.toByte(), 0xa4.toByte(), 0x04.toByte(), 0x00.toByte(),
                    "2PAY.SYS.DDF01".toByteArray(), 256.toShort()
                )
            }

            val apduSendInfo = ApduSendInfo()
            apduSendInfo.command = byteArrayOf(
                0x00.toByte(),
                0xa4.toByte(), 0x04.toByte(), 0x00.toByte()
            )
            apduSendInfo.dataIn = "1PAY.SYS.DDF01".toByteArray()
            apduSendInfo.lc = "1PAY.SYS.DDF01".toByteArray().size
            apduSendInfo.le = 256
            var cardString: String = ""
                try {
                var ret: ByteArray? = null
                for (i in 0..99) {
                    ret = picc!!.isoCommand(0.toByte(), apduReq!!.pack())
                    if (null != ret && ret.size > 0) {
                        break
                    }
                }
                if (ret != null && ret.size > 0) {
                    val dataout = ByteArray(ret.size - 2)
                    System.arraycopy(ret, 0, dataout, 0, dataout.size)
                    cardString += "DataOut:" + Convert.getInstance().bcdToStr(dataout)
                    cardString += " \nSWA:" + Convert.getInstance()
                        .bcdToStr(byteArrayOf(ret[ret.size - 2]))
                    cardString += " \nSWB:" + Convert.getInstance()
                        .bcdToStr(byteArrayOf(ret[ret.size - 1]))
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }

            val message = Message.obtain()
            message.what = 0
            message.obj = cardString
            handler.sendMessage(message)
            while (true) {
                try {
                    picc!!.remove(EPiccRemoveMode.REMOVE, 0.toByte())
                    logTrue("remove")
                } catch (e: PiccDevException) {
                    if (e.errCode == EPiccDevException.PICC_ERR_CARD_SENSE.errCodeFromBasement) {
                        continue
                    } else {
                        break
                    }
                }
                break
            }
            // open blue,yellow,green light
            // setLed((byte) 0x0e);
            SysTester.getInstance().beep(EBeepMode.FREQUENCE_LEVEL_1, 500)
            SystemClock.sleep(1000)
            // setLed((byte) 0x00);
            return 1
        }
        return 0
    }

    fun detectM(handler: Handler, type: EM1KeyType, blockNum: Int, password: ByteArray?) {
        // byte[] pwd = { (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff, (byte) 0xff };
        var cardInfo: PiccCardInfo? = null
        if (null != detect(EDetectMode.ONLY_M).also { cardInfo = it }) {
            logTrue(
                "cardtype:"
                        + cardInfo!!.cardType
                        + " SerialInfo:"
                        + Convert.getInstance().bcdToStr(
                    if (cardInfo!!.serialInfo == null) "".toByteArray() else cardInfo!!.serialInfo
                )
                        + " cid:"
                        + cardInfo!!.cid
                        + " Other:"
                        + Convert.getInstance().bcdToStr(
                    if (cardInfo!!.other == null) "".toByteArray() else cardInfo!!.other
                )
            )
            // read
            var str = ""
            var errStr: String = ""
            // byte[] value = m1Read((byte) blockNum);
            var value: ByteArray? = null
            try {
                Log.i("Test", ("keyType:" + type.name + " blockNum:" + blockNum + " password:" + Convert.getInstance().bcdToStr(password)))
                picc!!.m1Auth(type, blockNum.toByte(), password, cardInfo!!.serialInfo)
                value = picc.m1Read(blockNum.toByte())
            } catch (e: PiccDevException) {
                e.printStackTrace()
                errStr = ("[errCode:" + e.errCode + " errMsg:" + e.errMsg + "]")
            }
            if (value != null) {
                str += (Convert.getInstance().bcdToStr(value).toString() + "\n")
            } else {
                // String errStr = "block " + blockNum + " read null";
                str += if (errStr == null || errStr.isEmpty()) "null" else errStr
                // logErr("m1Read", errStr);
            }
            var cardString: String = "" // 卡片的信息
            cardString += ("cardType:" + String(byteArrayOf(cardInfo!!.cardType)) + "\n")
            cardString += ("block $blockNum read message:$str")
            val message = Message.obtain()
            message.what = 0
            message.obj = cardString
            handler.sendMessage(message)
        } else {
            Message.obtain(handler, 0, "can't find card !").sendToTarget()
        }
    }

    fun detectPaxTAG(): String {
        var cardInfo: PiccCardInfo? = null
        if (null != (detect(EDetectMode.ONLY_M).also { cardInfo = it })) {
            logTrue(
                ("cardtype:"
                        + cardInfo!!.cardType
                        + " SerialInfo:"
                        + Convert.getInstance().bcdToStr(
                    if ((cardInfo!!.serialInfo == null)) "".toByteArray() else cardInfo!!.serialInfo
                )
                        + " cid:"
                        + cardInfo!!.cid
                        + " Other:"
                        + Convert.getInstance().bcdToStr(
                    if ((cardInfo!!.other == null)) "".toByteArray() else cardInfo!!.other
                ))
            )
            return Convert.getInstance().bcdToStr(
                if ((cardInfo!!.serialInfo == null)) "".toByteArray() else cardInfo!!.serialInfo
            )
            // read
        } else {
            return ""
        }
    }

    companion object {
        private var piccTester: PiccTester? = null
        private var piccType: EPiccType? = null
        fun getInstance(type: EPiccType): PiccTester {
            if (piccTester == null || type != piccType) {
                piccTester = PiccTester(type)
            }
            return piccTester!!
        }
    }

    init {
        piccType = type
        picc = MainApp.getDal()!!.getPicc(piccType)
    }
}
