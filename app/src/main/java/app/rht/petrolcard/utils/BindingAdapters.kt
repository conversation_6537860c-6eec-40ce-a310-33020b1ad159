package app.rht.petrolcard.utils

import android.graphics.Bitmap
import android.text.TextUtils
import android.util.Log
import android.view.View
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.BindingAdapter
import androidx.databinding.ObservableBoolean
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import app.rht.petrolcard.R
import app.rht.petrolcard.utils.common.NonScrollListView
import app.rht.petrolcard.utils.customView.EndlessRecyclerView
import com.bumptech.glide.Glide

@BindingAdapter("setCustomHeight")
fun setCustomHeight(imageView: ImageView, tvSequence: Int) {
    imageView.layoutParams.height =
        ((imageView.context.resources.displayMetrics.heightPixels / 4).toInt())
}

@BindingAdapter("setNewsCredit")
fun setNewsCredit(view: TextView, newsCredit: String?) {
    view.text = ("" + newsCredit)
}

@BindingAdapter("setRefresh")
fun setRefresh(swipeRefresh: SwipeRefreshLayout, isRefresh: ObservableBoolean) {
    if (!isRefresh.get()) {
        swipeRefresh.isRefreshing = false
    }
}

@BindingAdapter("setRefresh")
fun setRefresh(swipeRefresh: EndlessRecyclerView, isRefresh: ObservableBoolean) {
    if (!isRefresh.get()) {
        swipeRefresh.isRefreshing = false
    }
}
@BindingAdapter("setAdapter")
fun setAdapter(recyclerview: NonScrollListView, file_name: ArrayList<String>) {
   /* if (file_name.size>0) {
        val recyclerViewArrayAdapter = UploadDocumentAdapter(file_name);
        recyclerview.layoutManager =
            LinearLayoutManager(recyclerview.context, LinearLayoutManager.HORIZONTAL, false)
        recyclerview.adapter = recyclerViewArrayAdapter
    }*/
}

@BindingAdapter("setNormalImage")
fun setNormalImage(iv: ImageView?, imageUrl: String?) {
    Log.e("imageURL",imageUrl+"")
    try {
        if (!TextUtils.isEmpty(imageUrl)) {
            iv?.visibility = View.VISIBLE
            iv?.let { imgView ->
                Glide.with(imgView.context)
                    .load(imageUrl).placeholder(R.drawable.image_notavailable)
                    .error(R.drawable.image_notavailable)
                    .into(imgView)
            }
        } else {
            iv?.visibility = View.GONE
        }

    } catch (e: Exception) {
        LogUtils.e("Exception", "img load failed " + e.message)
    }

}
@BindingAdapter("setFuelProduct")
fun setFuelProduct(iv: ImageView?, imageUrl: String?) {
    Log.e("imageURL",imageUrl+"")
    try {
        if (!TextUtils.isEmpty(imageUrl)) {
            iv?.visibility = View.VISIBLE
            iv?.let { imgView ->
                Glide.with(imgView.context)
                    .load(imageUrl).placeholder(R.drawable.ic_fuel_nozzle)
                    .error(R.drawable.ic_fuel_nozzle)
                    .into(imgView)
            }
        } else {
            Glide.with(iv!!.context)
                .load(imageUrl).placeholder(R.drawable.ic_fuel_nozzle)
                .error(R.drawable.ic_fuel_nozzle)
                .into(iv)
        }

    } catch (e: Exception) {
        LogUtils.e("Exception", "img load failed " + e.message)
    }

}
@BindingAdapter("setProfileImage")
fun setProfileImage(iv: ImageView?, imageUrl: String?) {
    Log.e("imageURL",imageUrl+"")
    try {
        if (!TextUtils.isEmpty(imageUrl)) {
            iv?.visibility = View.VISIBLE
            iv?.let { imgView ->
                Glide.with(imgView.context)
                    .load(imageUrl).placeholder(R.drawable.ic_user_placeholder)
                    .error(R.drawable.ic_user_placeholder)
                    .into(imgView)
            }
        } else {
            iv?.visibility = View.GONE
        }

    } catch (e: Exception) {
        LogUtils.e("Exception", "img load failed " + e.message)
    }
}
@BindingAdapter("setBitmap")
fun setBitmap(iv: ImageView?, imageBitmap: Bitmap?) {
    Log.e("imageURL", imageBitmap.toString())
    try {
        if (imageBitmap != null) {
            iv?.visibility = View.VISIBLE
            iv?.let { imgView ->
                imgView.setImageBitmap(imageBitmap)
            }
        } else {
            iv?.visibility = View.GONE
        }

    } catch (e: Exception) {
        LogUtils.e("Exception", "img load failed " + e.message)
    }
}
