package app.rht.petrolcard.utils.common

object EnumUtils {
    val TAG = EnumUtils::class.simpleName!!

    enum class FileType {
        TEXT, IMAGE, VIDEO, AUDIO, FONT
    }
    enum class PostStatus(val value: Int) {
        TEXT(1),
        IMAGE(2),
        VIDEO(3)
    }
    enum class PostStatusTextType(val value: String) {
        BOLD("1"),
        NORMAL("2"),
        ITALIC("3"),
        ITALIC_BOLD("4")
    }
}



