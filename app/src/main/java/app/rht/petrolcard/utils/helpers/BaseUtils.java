package app.rht.petrolcard.utils.helpers;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Environment;
import android.util.Log;

import androidx.preference.PreferenceManager;

import java.lang.ref.WeakReference;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import app.rht.petrolcard.MainApp;
import app.rht.petrolcard.utils.constant.AppConstant;

public class BaseUtils {
    public static byte[] toBcd(String s) {
        int size = s.length();
        byte[] bytes = new byte[(size+1)/2];
        int index = 0;
        boolean advance = size%2 != 0;
        for ( char c : s.toCharArray()) {
            byte b = (byte)( c - '0');
            if( advance ) {
                bytes[index++] |= b;
            }
            else {
                bytes[index] |= (byte)(b<<4);
            }
            advance = !advance;
        }
        return bytes;
    }

    public static boolean isSdCARD() {
        Context context = MainApp.Companion.getAppContext();
        return  BaseUtils.isExternalStorageAvailable()  && getSdCARD(context);
    }

    //checks if external storage is available for read and write
    private static boolean isExternalStorageAvailable() {
        String state = Environment.getExternalStorageState();
        return Environment.MEDIA_MOUNTED.equals(state);
    }
    private static boolean getSdCARD(Context c) {
        // shared preferences file
        SharedPreferences sharedPreferences = PreferenceManager.getDefaultSharedPreferences(c);
        boolean sdCARD = sharedPreferences.getBoolean(AppConstant.USE_SD_CARD, true);
        Log.e("sdCARD => ", sdCARD + "");
        return sdCARD;
    }

    public static String dateToString(Date date) {
        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.ENGLISH);
        String strDate = sdfDate.format(date);
        return strDate;
    }

    public static String getDateTicket(Date date) {
        DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss",Locale.ENGLISH);
        //Date date = new Date();
        return dateFormat.format(date);
    }

    public static String loadStringSharedPrefences(Context ctx, String cash){
        SharedPreferences sharedPreferences = ctx.getSharedPreferences("shared preferences", Context.MODE_PRIVATE);
        return sharedPreferences.getString(cash, null);
    }
}
