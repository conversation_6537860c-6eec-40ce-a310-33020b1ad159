package app.rht.petrolcard.utils.constant

import android.Manifest
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.database.baseclass.MainDataBaseClass
import java.io.File

object AppConstant {
    const val PREF_NAME = "${BuildConfig.APPLICATION_ID}_app"
    const val PREF_AUTHORITY_NAME = "app.rht.authority"
    const val PERMISSIONS_REQUEST_CAMERA_IMAGE = 12
    val PERMISSIONS_CAMERA = arrayOf(Manifest.permission.CAMERA, Manifest.permission.WRITE_EXTERNAL_STORAGE)
    const val NOTIFICATION_ID = 100
    const val NOTIFICATION_ID_BIG_IMAGE = 101
    const val CEILING_TRANSACTION = "10000"
    const val MAX_REFILL_AMNT = "10000"
    const val VERSION = "2.7.0"
    const val STATION_NAME = "Test station" // "Station de test";
    const val ID_STATION = 13 // station de test => 13
    const val BADGE_GRANTS = "NO_BADGE"
    const val LANG = "LANG"
    const val SECURE_INTERNAL_STORAGE = "/storage/emulated/0/storage/secure/"
    const val SD_CARD = "/storage/sdcard1/"
    const val USE_SD_CARD = "sd_card_use"
    const val USE_SECURE_INTERNAL_STORAGE = false
    const val MODE_PAY_KEY = "payment_mode"
    const val MODE_CURRENCY = "currency_mode"
    const val URL_WS = "urlWS"
    const val SERIAL_NUMBER_TPE = "sn_tpe"
    const val LANDI_SERIAL_NUMBER = "PP35271806013470"
    const val IS_DEBUGGABLE = "isDebug"
    const val PREFERENCE_RESTRICTION_SCHEDULE= "restriction_hours" //RESTRICTIONS_HORAIRE
    const val PREFERENCE_RESTRICTION_PRODUCT = "product_restrictions" //RESTRICTIONS_ARTICLE
    const val PREFERENCE_RESTRICTION_STATION = "station_restrictions" //RESTRICTIONS_STATION
    const val PREFERENCE_RESTRICTION_HOLIDAY = "free_hours_restrictions" //RESTRICTIONS_FERIE
    const val PREFERENCE_RESTRICTION_SECTOR = "sector_restrictions" //RESTRICTIONS_SECTEUR
    const val PREFERENCE_PUMP_DETAILS = "pump_details"
    const val PREFERENCE_PRODUCT_COLORS= "product_colors"
    const val PREFERENCE_CATEGORY_LIST= "category_list"
    const val PREFERENCE_PRODUCT_LIST= "fuel_product_list"
    const val PREFERENCE_DISCOUNT_DETAILS= "pref_discount_details"
    const val PREFERENCE_AUDIT_LOGS= "audit_logs"
    const val PREFERENCE_GREYLIST_MODEL= "greylist_model"
    const val PREFERENCE_MTN_PAY_CREDENTIALS= "preference_mtn_pay_credentials"
    const val PREFERENCE_SDCARD_PATH= "PREFERENCE_SDCARD_PATH"
    const val PREFERENCE_TERMINAL_ID= "TERMINAL_ID"
    const val FUSION_API = "fusion_api"
    const val COMPANY_RELOAD = "company_reload"
    const val COMPANY_LOGO = "company_logo"
    const val COMPANY_FOOTER = "company_footer"
    const val LOGO_NAME = "logo_client"
    const val IS_LOGGABLE = "is_loggable"
    const val IS_LOGTCP = "is_log_tcp"
    const val IS_4G = "is_4g"
    const val IS_OFFLINE_ONLY = "is_offline_only"
    const val wITHPICTURE = "with_picture"
    const val IS_ASK_FOR_BADGE = "is_ask_for_badge"
    const val PRICE_CONSOLATION = "priceAdvice"
    const val MIME_TYPE_PLAIN_TEXT = "text/plain"
    const val BLACK_LIST_VERSION = "black_list_version"
    const val GREY_LIST_VERSION = "grey_list_version"
    const val TRANSACTION_COUNT = "transaction_count_storage"
    const val IS_FIRST_TIME = "is_first_time"
    const val IS_CURRENT_ACTIVITY_IS_MENU = "is_restart"
    const val IS_SIGN_IN_BACKROUND = "is_sign_in_backround"
    const val IS_TIMS_STARTED = "is_tims_started"
    const val IS_APP_UPDATE_AVAILABLE = "is_app_update_available"
    const val IS_TIMS_SERVER_INSTALLED = "is_tims_server_installed"
    const val IS_RESTART_APPLICATION = "is_restart_application_menu"
    const val IS_MENU_ACTIVITY = "is_menu_activity"
    const val IS_RESTART_FROM_TICKET = "is_restart_from_ticket"
    const val INTENT_EXTRAS_MODEL = "intentExtrasModel"
    const val FUEL_POS_PORT = 7501
    const val PIN_ERROR = "pinErrone"
    const val PREFERENCES_MANAGER_ATTENDANT = "preferences_manager_attendant"
    const val PREFERENCES_MANAGER_STATION = "preferences_manager_station"
    const val PREFERENCES_MANAGER_COMPANY = "preferences_manager_company"
    const val EXTERNAL_STORAGE_DB_PATH = "/Android/data/app.rht.petrolcard/FBSPAYV3.db"
    const val LOG_DELETE_SCHEDULE_HOUR = "LOG_DELETE_SCHEDULE_HOUR"

    const val LOGO_PATH = "logoPath"
    const val CURRENT_ACTIVITY_PREFS = "prefs_current_activity"

    const val CASH_VALUE = "2"
    const val CARD_VALUE = "1"
    const val REFUND_VALUE = "9"
    const val MOBILE_VALUE = "3"
    const val QRCODE_VALUE = "4"
    const val RFID_VALUE = "5"
    const val LOYALTY_VALUE = "6"
    const val VISA_VALUE = "7"
    const val SPLIT_PAYMENT = "8"
    const val DISCOUNT_PAYMENT = "9"
    const val BANK_VALUE = "10"
    const val MTN_PAY_VALUE = "13"
    const val MOBILE_PAYMENT_REFERENCE = "20"

    const val MADA_APP_PACKAGE = "com.pax.edc"
    const val MADA_PURCHASE = "com.pax.edc.PURCHASE"
    const val MADA_REFUND = "com.pax.edc.REFUND"
    const val MADA_REVERSAL = "com.pax.edc.REVERSAL"

    const val OFFLINE_TRX_MODE = 1
    const val BEFORE_TRX_MODE = 2
    const val AFTER_TRX_MODE = 3
    const val CONNECT_TIMEOUT = 60 * 1000
    const val READ_TIMEOUT = 60 * 1000
    const val WRITE_TIMEOUT = 60 * 1000

    // broadcast receiver intent filters
    const val REGISTRATION_COMPLETE = "registrationComplete"
    const val PUSH_NOTIFICATION = "pushNotification"
    const val TOPIC_GLOBAL = "global"

    //Abort Error
    const val ABORT_TITLE = "abort_title"
    const val ABORT_MESSAGE = "abort_message"
    const val ABORT_MESSAGE_TYPE = "abort_message_type"

    //RFID Service
    const val RFID_ACTION_IFSF_CONNECTION_STATE = "RFID_ACTION_IFSF_CONNECTION_STATE"
    const val RFID_ACTION_IFSF_READ_DATA = "RFID_ACTION_IFSF_READ_DATA"
    const val RFID_IFSF_BYTE_MESSAGE = "RFID_IFSF_BYTE_MESSAGE"
    const val RFID_IFSF_CONFIG = "RFID_IFSF_CONFIG"
    const val RFID_IFSF_CONNECTION_STATE = "RFID_IFSF_CONNECTION_STATE"
    const val RFID_IFSF_STRING_MESSAGE = "RFID_IFSF_STRING_MESSAGE"
    const val MASTER_TERMINAL_IP = "master_terminal_ip_address"
    const val MASTER_TERMINAL_PORT = "master_terminal_port"
    const val RFID_RESULT_CODE = 121
    const val DEFAULT_CONNECTION_TIMEOUT_SEC = 30L
    const val DEFAULT_READ_TIMEOUT_SEC = 30L
    const val DEFAULT_WRITE_TIMEOUT_SEC = 30L
    const val ACTION_READ_RFID_DATA = "read_rfid_data"
    const val ACTION_RFID_PACKET_DATA = "read_rfid_data"
//    const val TPE_APP = BuildConfig.APPLICATION_ID //for testing purpose commented this
    const val TPE_APP = "app2" //for testing purpose commented this
    const val IS_RFID_AVAILABLE = "isRFID"
    const val TPE_APP_id = 0
    const val pinCount = 3
    const val TLK = "22222222222222222222222222222222"
    const val TMK = "87432C07DA6BC82DCB48C1168061EEEE"
    const val B_TPE = "B_TPE"
    const val PAX = "PAX"
    const val URL_UPDATE = "urlUpdate" // "http://***************/"; // "http://update.attarikpro.ma/";
    const val YES = "yes"
    const val NO = "no"
    const val SDCARD_RESULT_CODE = 42

    const val LOYALTY_CARD = 3
    const val PREPAID_CARD = 1
    const val POSTPAID_CARD = 2
    const val LOYALTY_CARD_TEXT = "Loyalty"
    const val PREPAID_CARD_TXT = "Pre-Paid"
    const val POSTPAID_CARD_TXT = "Post-Paid"
    const val CODE = "CODE"
    const val DETAIL_ARTICLE = "loy"

    const val BASE_URL_MPESA_TEST = "https://sandbox.safaricom.co.ke/"
    const val BASE_URL_MPESA_PRODUCTION = "https://api.safaricom.co.ke/"
    const val MPESA_AUTHENTICATION_URL = "oauth/v1/generate?grant_type=client_credentials"
    const val MPESA_PROCESSREQUEST_URL = "mpesa/stkpush/v1/processrequest"
    const val CARD_UPDATE_CODE_RESULT = 10001
    const val CARD_NFC_BADGE = 10002

    const val LOYALTY_CARD_REQUEST = "activation_request"
    const val LOYALTY_ACTIVATION_REQUEST = "activation_request"
    const val LOYALTY_CARD_NUMBER = "loyalty_card_number"
    const val LOYALTY_CARD_HOLDER = "loyalty_card_holder"
    const val CARD_NFC_TAG = "card_nfc"
    const val CARD_NUMBER = "card_number"
    const val TAG_AUTHORISED = "nfc_authorized"
    const val ENCRYPTION_KEY = "864446699"
    const val QR_CODE_SIZE = 1000 // 1000
    val IMAGE_DIRECTORY_NAME = "petroCardApp"
    val PHOTO_DIRECTORY_NAME = "petroCardPhotos"
    val FUEL_SERVICE_LOG_NAME = "FuelServiceLogs"
    val FLEET_CARD_LOG_NAME = "FleetCardLogs"

    const val PRINTER_TYPE = "printer_type"
    const val CITIZEN_PRINTER = 1 //original 1
    const val PT102_PRINTER = 2 // original 2
    const val DW14_PRINTER = 3 // original 2

    const val IS_QTY_SELECTED = "fuelQty"
    const val FUEL_QTY_UNIT = "fuel_qty_unit"

    const val FUELLING_LIMIT_TYPE = "fuelling_limit_type"
    const val FULL_TANK_BY_AMOUNT = "1"
    const val FULL_TANK_BY_VOLUME = "2"
    const val INTENT_KEY_DATA = "network_response"
    const val ATTENDANT_MODE = 1
    const val UN_ATTENDANT_MODE = 2  //2 actual value // 1 for testing purpose
    const val AUTH_CODE = "AuthCodeBankCard"
    const val REFERENCE_NO = "referenceNo"
    const val VOUCHER_NO = "voucherNo"
    const val ECR_NO = "ecr_no"
    const val CUSTOMER = 1
    const val ATTENDANT = 2
    const val TICKET_PREVIEW = 3
    const val BITMAP = 3
    const val PRINT_RECEIPT_REQUIRED = 0  //0 OPTIONAL
    const val PRINT_RECEIPT_NOT_REQUIRED = 1
    const val PRINT_RECEIPT_MANDATORY = 2

    const val REFUND_REQUEST = 1
    const val VOID_REQUEST = 2
    const val SALE_REQUEST = 3
    const val DEVICE_STATUS_BAR = "PAX_STATUS_BAR"
    const val DEVICE_NAVIGATION_BAR = "PAX_NAVIGATION_BAR"
    const val STOP_FUEL_POINT = "STOP_FUEL_POINT"
    const val START_FUEL_POINT = "START_FUEL_POINT"
    const val MANAGER_ROLE = "Menage"
    const val ATTENDANT_ROLE = "Attendant"
    const val MANAGER_ROLE_ID= 2
    const val DISCOUNT_ALL_DAY= 1
    const val DISCOUNT_SPECIFIC_DAY= 2
    const val FIXED= 2
    const val PERCENTAGE= 1
    const val SPECIFIC_CARDS= 0
    const val ALL_PREPAID_CARDS= 2
    const val ALL_POSTPAID_CARDS= 1
    const val ALL_CARDS= 3
    const val DO_CASHTRX_UNATTENDANT= "1"
    const val LOG_FOLDER_NAME= "FBSPAYLOGS"
    const val TEMP_LOG_FOLDER_NAME= "TEMP_FBSPAYLOGS"
    const val RECEIPT_JSON= "receipt_layout_2.json"
    const val ESD_REQUIRED= "0"
    const val TIMS_REQUIRED= "1"
    const val DEFAULT_BUYER_PIN= "0000"
    const val LOG_EMAIL_CONFIG = "email_config"

    const val IS_PUMP_ERROR = "is_pump_error"
    const val IS_POWERCUT_GETFUELSALETRXMSGSENT = "is_power_cut_getfuelsaletrxmsgsent"
    const val IS_DELETE_LOG_SUCCESS_TRANSACTION = "is_delete_log_success_transaction"
    const val IS_TRANSACTION_CREATED = "isTransactionCreated"
    const val IS_PAYMENT_DONE = "isPaymentDone"
    const val IS_PAYMENT_METHOD_CLICKED = "isPaymentMethodClicked"
    const val RECHARGE_TRANSACTION= 1
    const val REFUND_TRANSACTION= 2
}

object CancelPrintReceipt {
    const val DISABLED = 0
    const val ENABLED = 1
    const val OPTIONAL = 2
}
object DISCOUNT_TYPE {
    const val NO_DISCOUNT = 0
    const val INSTANT_DISCOUNT = 1
    const val REBATE_DISCOUNT = 2
}

enum class EnterFuelType {
    AMOUNT,
    QUANTITY,
    FULL_TANK
}

object Workflow
{
    const val SETTINGS_RECHARGE_CARD = "taxi_charging_carte"
    const val SETTINGS_CARD_HISTORY = "taxi_card_history" // taxi_historique_carte
    const val SETTINGS_CARD_UPDATE = "settings_card_update"
    const val TAXI_FUEL = "taxi_fuel"//taxi_carburants
    const val SETTINGS_CARD_CHANGE_PIN = "settings_card_change_pin"
    const val OTHER_PRODUCTS = "OTHER_PRODUCTS"
    const val SHOP_PRODUCTS = "SHOPS"
    const val SETTINGS = "SETTINGS"
    const val PRICE_CHANGE = "PRICE_CHANGE"
    const val FUEL_FULL_TANK = "fuel_full_tank"
    const val FUEL_ENTER_AMOUNT = "fuel_enter_amount"
    const val FUEL_ENTER_QTY = "fuel_enter_qty"
    const val SETTINGS_UPDATE_MILEAGE = "settings_update_mileage"
    const val SETTINGS_DISPUTED_TRANSACTION = "settings_disputed_transaction"
}
object PRODUCT {
    const val FUEL_CATEGORY_ID = 10
    const val SERVICE_CATEGORY_ID = 14
    const val SHOP_CATEGORY_ID = 18
    const val RECHARGE = 17
}
object PrinterTypes {
    const val WPOS = 0
    const val A920 = 1
    const val CITIZEN = 2
    const val PT102 = 3
}

object TaxType {
    const val INCLUSIVE = 0
    const val EXCLUSIVE = 1
}
object CustomerAuthentication
{
    const val REQUIRED = "2"
    const val OPTIONAL = "1"
    const val NOT_REQUIRED = "3"
}
object Events {
    const val START_ACTION = "START_ACTION"
    const val STOP_ACTION = "STOP_ACTION"
    const val PAUSE_ACTION = "PAUSE_ACTION"
    const val RESUME_ACTION = "RESUME_ACTION"
    const val DESTROY_ACTION = "DESTROY_ACTION"
}
object LAYOUT {
    const val LAYOUT_1 = 1
    const val LAYOUT_2 = 2
}
object TIMS_METHODS {
    const val fiscalDeviceSetting = "fiscalDeviceSetting"
    const val readPrinterStatus = "readPrinterStatus"
    const val readSerialNumber = "readSerialNumber"
    const val openReceiptWithCustomerData = "openReceiptWithCustomerData"
    const val createServiceInvoice = "createServiceInvoice"
    const val createTicketInvoice = "createTicketInvoice"
    const val closeReceipt = "closeReceipt"
    const val updateTransactionData = "updateTransactionData"
    const val insertTransactionData = "insertTransactionData"
}
object FUSION_PAYMENT_TYPES
{
    const val CASH_VALUE = "Cash"
    const val CARD_VALUE = "FleetCard"
    const val BANK_VALUE = "BankCard"
    const val MOBILE_VALUE = "MobileWallet"
    const val PASSIVE_TAG = "PassiveTag"
    const val OTHER_VALUE = "Pos"
}
object ReceiptFields {
    const val LOGO_R = "logo"
    const val TELECOLLECT_STATION_NAME = "telecollect_station_name"
    const val TELECOLLECT_ADDRESS = "telecollect_address"
    const val HEADER_1 = "header_1"
    const val TRX_ID = "trx_id"
    const val FPOS_SEQUENCE_NO = "fpos_sequence_no"
    const val SALE_ID = "sale_id"
    const val DATE = "date"
    const val TIME = "time"
    const val TRANSACTION_BY = "transaction_by"
    const val PAN = "pan"
    const val CUSTOMER_PIN = "customer_pin"
    const val PAYMENT_TYPE = "payment_type"
    const val LINE = "line"
    const val PRODUCT_NAME = "product_name"
    const val PUMP_NO = "pump_no"
    const val QTY = "qty"
    const val AMOUNT = "amount"
    const val HEADER_2 = "header_2"
    const val NET_AMOUNT = "net_amount"
    const val VAT_NAME = "vat_name"
    const val VAT = "vat"
    const val DISCOUNT = "discount"
    const val TOTAL = "total"
    const val PAN_LIMITS = "pan_limit"
    const val DAILY_LIMIT = "daily_limit"
    const val WEEKLY_LIMIT = "weekly_limit"
    const val MONTHLY_LIMIT = "monthly_limit"
    const val CARD_BALANCE = "card_balance"
    const val TERMINAL_SN = "terminal_sn"
    const val CU_SN = "cu_sn"
    const val CU_INVOICE_NO = "cu_invoice_no"
    const val CU_SIGN = "cu_signature"
    const val QR_CODE = "qr_code"
    const val HEADER_3 = "header_3"
    const val HEADER_4 = "header_4"
    const val CUSTOMER_MESSAGE = "customer_message"
    const val CUSTOMER_RECEIPT_COPY = "customer_receipt_copy"
    const val DUPLICATE_RECEIPT_COPY = "duplicate_receipt_copy"
    const val ATTENDANT_RECEIPT_COPY = "attendant_receipt_copy"
    const val REFERENCE = "reference"
    const val PAN_LOYALTY = "pan_loyalty"
    const val PAN_HOLDER_NAME = "pan_holder_name"
    const val VAT_DETAILS = "vat_details"
    const val VAT_GROUP_1 = "vat_group_1"
    const val VAT_GROUP_2 = "vat_group_2"
    const val VAT_GROUP_3 = "vat_group_3"
    const val VAT_GROUP_4 = "vat_group_4"
    const val PRODUCT_DETAILS = "product_line_details"
    const val FISCAL_ID = "fiscal_id"
    const val UNIT_PRICE = "unit_price"
    const val CARD_EXPIRY = "card_expiry"
    const val TAG_NFC = "tag_nfc"
    const val MILEAGE = "mileage"
    const val VEHICLE_NUMBER = "vehicle_no"
    const val DUPLICATE_CUSTOMER_MESSAGE = "customer_message_yes"
    const val ROUNDING_ADJUSTMENT = "rounding_adjustment"
    const val RECEIPT_NO = "receipt_no"
    const val SUB_INC_TAX_AMOUNT = "sub_inc_tax_amount"
    const val SUB_EXC_TAX_AMOUNT = "sub_exc_tax_amount"
    const val PAN_LIMIT_RECHARGE = "pan_limit_recharge"
    const val NEW_LINE = "new_line"
    const val DISPUTED_TRANSACTION = "disputed_transaction_message"

    const val BANK_AUTH_CODE = "bank_auth_code"
    const val BANK_VOUCHER_NUMBER = "bank_voucher_no"
    const val PAYMENT_REFERENCE_NUMBER = "payment_reference_number"
    const val TELECOLLECT_COMPANY_NAME = "telecollect_company_name"
    const val TRX_ID_LEFT = "trx_id_left"
    const val REFUND_AMOUNT= "refund_amount"




}