package app.rht.petrolcard.utils

import android.util.Log
import app.rht.petrolcard.BuildConfig
import java.util.*
import java.util.regex.Pattern

class LogUtils {
    companion object {

        @JvmStatic
        fun log(tag: String?, msg: String?) {
            if(msg != null) {
               /* val logWriter = LogWriter(tag!!)
                logWriter.appendLog(tag, msg)*/
                Log.i(tag, msg)
            }
        }

        @JvmStatic
        fun e(key:String,value:String){
            if(BuildConfig.LOG){
                Log.e(key,value)
            }
        }
        @JvmStatic
        fun d(key:String,value:String){
            if(BuildConfig.LOG){
                Log.d(key,value)
            }
        }
        @JvmStatic
        fun i(key:String,value:String){
            if(BuildConfig.LOG){
                Log.i(key,value)
            }
        }
    }
}

fun extractEmails(all: String): Array<String> {
    val EMAIL_REXP = "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)" +
            "*@" + "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$"

    val pattern: Pattern = Pattern.compile(EMAIL_REXP)
    val tokenizer = StringTokenizer(all, ";")
    val mailList: MutableList<String> = ArrayList()
    var currentMail: String
    while (tokenizer.hasMoreElements()) {
        currentMail = tokenizer.nextElement() as String
        if (pattern.matcher(currentMail.trim { it <= ' ' }).matches()) {
            mailList.add(currentMail)
        }
    }
    return mailList.toTypedArray()
}