package app.rht.petrolcard.utils;

import android.content.Context;
import android.os.RemoteException;
import android.util.Log;

import com.google.gson.JsonArray;
import com.google.gson.JsonIOException;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.pax.dal.entity.EBeepMode;
import com.pax.dal.entity.EPedType;
import com.usdk.apiservice.aidl.data.ApduResponse;
import com.usdk.apiservice.aidl.data.BytesValue;
import com.usdk.apiservice.aidl.data.IntValue;
import com.usdk.apiservice.aidl.emv.ActionFlag;
import com.usdk.apiservice.aidl.emv.UEMV;
import com.usdk.apiservice.aidl.icreader.ICError;
import com.usdk.apiservice.aidl.icreader.PowerMode;
import com.usdk.apiservice.aidl.icreader.UICCpuReader;
import com.usdk.apiservice.aidl.icreader.Voltage;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.json.JSONArray;
import org.json.JSONException;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;

import app.rht.petrolcard.BuildConfig;
import app.rht.petrolcard.MainApp;
import app.rht.petrolcard.ui.iccpayment.model.CardStaticStructureModel;
import app.rht.petrolcard.ui.iccpayment.model.ChipResponse;
import app.rht.petrolcard.ui.updatecard.model.VehicleModel;
import app.rht.petrolcard.ui.nfc.model.NFCEnum;
import app.rht.petrolcard.ui.nfc.model.NfcTagModel;
import app.rht.petrolcard.ui.reference.model.RestrictedSchedulesModel;
import app.rht.petrolcard.ui.reference.model.RestrictedHolidaysModel;
import app.rht.petrolcard.ui.reference.model.RestrictedProductsModel;
import app.rht.petrolcard.ui.reference.model.RestrictedSectorsModel;
import app.rht.petrolcard.ui.reference.model.RestrictedStationModel;
import app.rht.petrolcard.utils.constant.AppConstant;
import app.rht.petrolcard.utils.paxutils.icc.IccTester;
import app.rht.petrolcard.utils.paxutils.modules.ped.PedTester;
import app.rht.petrolcard.utils.paxutils.system.SysTester;
import wangpos.sdk4.libbasebinder.BankCard;
import wangpos.sdk4.libbasebinder.Core;



/**
 * Created on 2019-10-10.
 */
public class UtilsCardInfo {



    private static final String TAG = UtilsCardInfo.class.getSimpleName();
    private static boolean detected = false ;


    /**
     *
     * @param card
     * @param icCpuReader
     * @param context
     * @return
     */
    public static  boolean selectApp(BankCard card ,UICCpuReader icCpuReader, Context context) {
        ChipResponse myResponse = new ChipResponse();
        myResponse = null;
        String Credit_Data = "00A4040008D1D2D3D4D5D6D7D800";
        //String Credit_Data = "D1D2D3D4D5D6D7D8";


        byte[] bytes = Utils.hexStringToByteArray(Credit_Data);
        myResponse = executeApduSizeGlobal(card,icCpuReader,bytes,bytes.length,Integer.parseInt(Credit_Data.length()+"",16)*2,context);

        log("selectApp apdu", Credit_Data);
        log("selectApp response", myResponse.toString());

        return myResponse.getStatus() != null && myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000");
    } // eof selectApp

    /**
     *
     * @param mCore
     * @param Pan
     * @return
     */
    public static String genKey(Core mCore,String Pan) {
        final int len = 8;
        byte[] pbOut = new byte[len];
        int[] pwOutLen = new int[1];

        byte[] vectordata = new byte[8];
        int vectorLen = 8;
        for (int i = 0; i < vectordata.length; i++)
            vectordata[i] = 0;
        final int lenin = 8;


        byte[] datain = new byte[len];

        datain = Utils.toBcd(Pan.substring(3,19));

        String hex = Utils.byteArrayToHex(datain);

        int datalen = len;

        int pddmode = 0;
        int encryptmode = 1;

        int operationmode = 0;

        int ret = -1;
        try {
            ret = mCore.dataEnDecryptEx(
                    Core.ALGORITHM_3DES,
                    operationmode, // operationmode -> 0
                    AppConstant.TPE_APP,//"petroKeyApp",
                    encryptmode, // encryptmode -> 1
                    vectorLen, // vectorLen -> 8
                    vectordata, // vectordata -> array of 8 bytes
                    datalen, // datalen -> 8
                    datain, // pan sur 16 in BCD
                    pddmode, // pddmode -> 0
                    pbOut, // pbOut -> array of 8 bytes
                    pwOutLen // pwOutLen -> array of 1 integer
            );
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        StringBuilder builder = new StringBuilder();

        StringBuilder builderout = new StringBuilder();
        if (ret == 0) {
            builder.append("success");
            for (int i = 0; i < datain.length; i++) {
                builder.append(datain[i] + ",");
            }
            for (int i = 0; i < pbOut.length; i++) {
                builderout.append(pbOut[i] );

            }
            String encrypt= Utils.byteArrayToHex(pbOut);
            //String encrypt= Utils.bytes2HexString(pbOut);
            return encrypt;
        } else {
            return builderout.toString();
        }
    }

    /**
     *
     * @param pan
     * @return
     */
    public static String genKeyPAX(String pan) {

        log(TAG, "#### PAN: "+pan);
        String datain = pan.substring(3,19);
        log(TAG, "#### datain: "+datain);
        String key1 = "" ;
        byte [] panByte = Utils.hexStringToByteArray(datain) ;

        byte[] bytes_d = PedTester.getInstance(EPedType.INTERNAL).calcDes( panByte);

        key1 = Utils.byteArrayToHex(bytes_d) ;
        //log("key generated",key1) ;

        //byte[] panDkt = PedTester.getInstance(EPedType.INTERNAL).calcDesDecryptage( bytes_d);

        //log("decrypt pan generate",Utils.ByteArraytoHexString(panDkt)) ;

        return key1 ;

    }


    /**
     *
     * @param mCore
     * @param sn
     * @return
     */
    public static String genEncryptKey(Core mCore,String sn) {
        final int len = 8;
        byte[] pbOut = new byte[len];
        int[] pwOutLen = new int[1];

        byte[] vectordata = new byte[8];
        int vectorLen = 8;
        for (int i = 0; i < vectordata.length; i++)
            vectordata[i] = 0;
        final int lenin = 8;


        byte[] datain = new byte[len];

        datain = Utils.toBcd(sn);

        String hex = Utils.byteArrayToHex(datain);

        int datalen = len;

        int pddmode = 0;
        int encryptmode = 1;

        int operationmode = 0;

        int ret = -1;
        try {
            if(mCore != null)
                ret = mCore.dataEnDecryptEx(
                        Core.ALGORITHM_3DES,
                        operationmode, // operationmode -> 0
                        AppConstant.TPE_APP,//"petroKeyApp",
                        encryptmode, // encryptmode -> 1
                        vectorLen, // vectorLen -> 8
                        vectordata, // vectordata -> array of 8 bytes
                        datalen, // datalen -> 8
                        datain, // pan sur 16 in BCD
                        pddmode, // pddmode -> 0
                        pbOut, // pbOut -> array of 8 bytes
                        pwOutLen // pwOutLen -> array of 1 integer
                );
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        StringBuilder builder = new StringBuilder();

        StringBuilder builderout = new StringBuilder();
        if (ret == 0) {
            builder.append("success");
            for (int i = 0; i < datain.length; i++) {
                builder.append(datain[i] + ",");
            }
            for (int i = 0; i < pbOut.length; i++) {
                builderout.append(pbOut[i] );

            }
            String encrypt= Utils.byteArrayToHex(pbOut);
            return encrypt;
        } else {
            return builderout.toString();
        }
    }

    /**
     *
     * @param mCore
     */
    public static void beep(Core mCore) {
        try {
            //mCore.buzzer();
            MainApp.Companion.getBeeper().stopBeep();
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     *
     * @param mCore
     * @param temp
     */
    public static void beep(Core mCore,int temp) {
        try {

            if(mCore != null)
                mCore.buzzerEx(temp);
            else {
                if (BuildConfig.POS_TYPE.equals("LANDI"))
                    MainApp.Companion.getBeeper().startBeep(80);
                else if (BuildConfig.POS_TYPE.equals("PAX") && !BuildConfig.DEBUG)
                    SysTester.getInstance().beep(EBeepMode.FREQUENCE_LEVEL_1, 100);
            }

        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
    }


    /**
     *
     * @param card
     * @param icCpuReader
     * @param Key1
     * @param context
     * @return
     */
    public static boolean verifyKey1( BankCard card,UICCpuReader icCpuReader,String Key1 , Context context) {

        ChipResponse myResponse = new ChipResponse();
        ChipResponse myResponse1 = new ChipResponse();



        String selectKeyFile="00A40000022F02";
        String headerP="**********";


        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);

        myResponse = executeApduSizeGlobal(card,icCpuReader,bytes,bytes.length,Integer.parseInt(selectKeyFile.length()+"",16)*2,context);

        byte[] bytesHeader = Utils.hexStringToByteArray(headerP+Key1);

        myResponse1 = executeApduSizeSecondGlobal(card,icCpuReader,bytesHeader,bytesHeader.length,Integer.parseInt("28",16)*2,context);

        return myResponse.getStatus().equalsIgnoreCase("9000") &&
                ((!BuildConfig.POS_TYPE.equals("LANDI") && myResponse1.getData().substring(0, 4).equalsIgnoreCase("9000")) ||
                        ((BuildConfig.POS_TYPE.equals("LANDI") && myResponse1.getStatus().substring(0, 4).equalsIgnoreCase("9000")))
                );


    }

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param Key1
     * @param context
     * @return
     */
    public static boolean externalAuth1( BankCard card,UICCpuReader uicCpuReader,String Key1 , Context context) {


        try {

            ChipResponse myResponse = new ChipResponse();
            ChipResponse myResponseChallenge = new ChipResponse();
            ChipResponse myResponseResult = new ChipResponse();



            String selectKeyFile="00A40000022F02";
            String getChallenge = "**********";
            byte [] chal = new byte[8];
            String headerP="**********";


            byte[] bytes = Utils.hexString2byteArray(selectKeyFile);

            myResponse = executeApduGlobal(card,uicCpuReader,bytes,bytes.length,context);


            byte[] challange = Utils.hexString2byteArray(getChallenge);

            myResponseChallenge = executeApduGlobal(card,uicCpuReader,challange,challange.length,context);


            byte[] challange1 = Utils.hexString2byteArray(myResponseChallenge.getData());

            System.arraycopy(challange1, 0, chal, 0, 8);

            //String chllg =  Utils.byteArray2hexString(chal).substring(0,16);

            byte[]  dataEncrypted =   Utils.encipher(chal, Utils.hexString2byteArray(Key1), "ECB", "DES");

            String data = Utils.byteArray2hexString(dataEncrypted);

            byte[] bytesHeader = Utils.hexString2byteArray(headerP+data.substring(0, 12));

            myResponseResult = executeApduGlobal(card,uicCpuReader,bytesHeader,bytesHeader.length,context);

            if( !myResponseResult.getStatus().isEmpty() && myResponseResult.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                return true;
            } {
                return true;
            }

        }catch (Exception ex){

            ex.printStackTrace();
            log(TAG, ex + ExceptionUtils.getStackTrace(ex));
        }


        return false;


    }

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param Key2
     * @param context
     * @return
     */
    public static boolean verifyKey2( BankCard card,UICCpuReader uicCpuReader,String Key2 , Context context) {

        ChipResponse myResponse = new ChipResponse();
        ChipResponse myResponse1 = new ChipResponse();


        String selectKeyFile="00A40000022F02";
        String headerP="**********";


        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);

        myResponse = executeApduSizeGlobal(card,uicCpuReader,bytes,bytes.length,Integer.parseInt(selectKeyFile.length()+"",16)*2,context);

        byte[] bytesHeader = Utils.hexStringToByteArray(headerP+Key2);

        myResponse1 = executeApduSizeSecondGlobal(card,uicCpuReader,bytesHeader,bytesHeader.length,Integer.parseInt("28",16)*2,context);

        return myResponse.getStatus().equalsIgnoreCase("9000") &&
                ((!BuildConfig.POS_TYPE.equals("LANDI") && myResponse1.getData().substring(0, 4).equalsIgnoreCase("9000")) ||
                        ((BuildConfig.POS_TYPE.equals("LANDI") && myResponse1.getStatus().substring(0, 4).equalsIgnoreCase("9000")))
                );


    }

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param Key1
     * @param context
     * @return
     */
    public static boolean externalAuth2( BankCard card,UICCpuReader uicCpuReader,String Key1 , Context context) {


        try {

            ChipResponse myResponse = new ChipResponse();
            ChipResponse myResponseChallenge = new ChipResponse();
            ChipResponse myResponseResult = new ChipResponse();



            String selectKeyFile="00A40000022F02";
            String getChallenge = "**********";
            byte [] chal = new byte[8];
            String headerP="**********";


            byte[] bytes = Utils.hexString2byteArray(selectKeyFile);

            myResponse = executeApduGlobal(card,uicCpuReader,bytes,bytes.length,context);


            byte[] challange = Utils.hexString2byteArray(getChallenge);

            myResponseChallenge = executeApduGlobal(card,uicCpuReader,challange,challange.length,context);


            byte[] challange1 = Utils.hexString2byteArray(myResponseChallenge.getData());

            System.arraycopy(challange1, 0, chal, 0, 8);

            //String chllg =  Utils.byteArray2hexString(chal).substring(0,16);

            byte[]  dataEncrypted =   Utils.encipher(chal, Utils.hexString2byteArray(Key1), "ECB", "DES");

            String data = Utils.byteArray2hexString(dataEncrypted);

            byte[] bytesHeader = Utils.hexString2byteArray(headerP+data.substring(0, 12));

            myResponseResult = executeApduGlobal(card,uicCpuReader,bytesHeader,bytesHeader.length,context);


            if(!myResponseResult.getStatus().isEmpty() && (myResponseResult.getStatus().substring(0,4).equalsIgnoreCase("9000"))) {
                return true;
            }

        }catch (Exception ex){

            ex.printStackTrace();
            log(TAG, ex + ExceptionUtils.getStackTrace(ex));
            return false ;
        }


        return false;


    }

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param oldPIN
     * @param context
     * @return
     */
    public static boolean verifyPIN( BankCard card,UICCpuReader uicCpuReader,String oldPIN , Context context) {

        ChipResponse myResponse = new ChipResponse();
        ChipResponse myResponse1 = new ChipResponse();




        String selectPinFile="00A40000022F01";
        String PinEncoded =  Utils.encodeHexString(oldPIN);

        String headerP="**********";
        String paddF="FFFFFFFF";




        byte[] bytes = Utils.hexStringToByteArray(selectPinFile);

        myResponse = executeApduGlobal(card,uicCpuReader,bytes,bytes.length,context);

        byte[] bytesHeader = Utils.hexStringToByteArray(headerP+PinEncoded+paddF);

        myResponse1 = executeApduGlobal(card,uicCpuReader,bytesHeader,bytesHeader.length,context);

        return myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")
                && myResponse1.getStatus().substring(0, 4).equalsIgnoreCase("9000");


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param oldPIN
     * @param newPIN
     * @param context
     * @return
     */
    public static boolean changePIN(BankCard mBankCard,UICCpuReader uicCpuReader,  String oldPIN,String newPIN , Context context) {

        ChipResponse myResponse = new ChipResponse();
        ChipResponse myResponse1 = new ChipResponse();



        String selectKeyFile="00A40000022F01";
        String headerP="**********";
        String paddF="FFFFFFFF";


        String PinEncodedNew =  Utils.encodeHexString(newPIN);
        String PinEncodedOld =  Utils.encodeHexString(oldPIN);



        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);

        myResponse = executeApduGlobal(mBankCard,uicCpuReader,bytes,bytes.length,context);

        byte[] bytesHeader = Utils.hexStringToByteArray(headerP+PinEncodedOld+paddF+PinEncodedNew+paddF);

        myResponse1 = executeApduGlobal(mBankCard,uicCpuReader,bytesHeader,bytesHeader.length,context);

        return myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")
                && myResponse1.getStatus().substring(0, 4).equalsIgnoreCase("9000");


    }


    /**
     *
     * @param card
     * @param icCpuReader
     * @param context
     * @return
     */
    // todo : Zone carte Attarik Pro
    public static String pan(BankCard card ,UICCpuReader icCpuReader, Context context) {
        ChipResponse myResponse = new ChipResponse();
        String rc = "00A40000022F09";
        String recods = "00B2020428";

        byte[] record1 = Utils.hexStringToByteArray(rc);
        if(selectApp(card,icCpuReader,context)) {
            myResponse = executeApduSizeGlobal(card,icCpuReader,record1, record1.length,Integer.parseInt(rc.length()+"",16)*2,context); // data : 0000200520700071003000020052043FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF31122019311020190
            // log("Resp File => ", myResponse.toString());
            log("pan apdu 1", rc);
            log("pan response 1", myResponse.toString());

            if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                byte[] Data1 = Utils.hexStringToByteArray(recods);
                myResponse = executeApduSizeSecondGlobal(card,icCpuReader,Data1, Data1.length,Integer.parseInt("28",16)*2,context);
                // log("Resp Record => ", myResponse.toString());
                log("pan apdu 2", recods);
                log("pan response 2", myResponse.toString());

                if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                    log("pan response data", myResponse.getData());

                    return myResponse.getData().substring(0, 19);
                }
            }
        }

        return null;
    } // eof pan


    /**
     *
     * @param card
     * @param icCpuReader
     * @param context
     * @return
     */

    // todo : Zone carte Attarik Pro
    public static String getCardInfo(BankCard card ,UICCpuReader icCpuReader, Context context) {
        ChipResponse myResponse = new ChipResponse();
        String rc = "00A40000023F00";

        String rc1 = "00A40000022F09";

        String rc3 = "00B2020428";


        if(selectApp(card,icCpuReader,context)) {

            if(BuildConfig.POS_TYPE.equals("PAX")) {
                byte[] record1 = Utils.hexStringToByteArray(rc);
                myResponse = executeApduSizeGlobal(card, icCpuReader, record1, record1.length, Integer.parseInt(rc.length() + "", 16) * 2, context); // data : 0000200520700071003000020052043FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF31122019311020190
                // log("Resp File => ", myResponse.toString());
                log("pan apdu 1", rc);
                log("pan response 1", myResponse.toString());

            }

            if (  BuildConfig.POS_TYPE.equals("B_TPE") || (myResponse != null  && myResponse.getStatus() != null && myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000") )) {
                byte[] Data1 = Utils.hexStringToByteArray(rc1);
                myResponse = executeApduSizeSecondGlobal(card,icCpuReader,Data1, Data1.length,Integer.parseInt("28",16)*2,context);
                // log("Resp Record => ", myResponse.toString());
                log("pan apdu 2", rc1);
                log("pan response 2", myResponse.toString());

                if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000") || myResponse.getData().substring(0, 4).equalsIgnoreCase("9000")) {
                    byte[] Data2 = Utils.hexStringToByteArray(rc3);
                    myResponse = executeApduSizeSecondGlobal(card,icCpuReader,Data2, Data2.length,Integer.parseInt("28",16)*2,context);
                    // log("Resp Record => ", myResponse.toString());
                    log("pan apdu 3", rc3);
                    log("pan response 3", myResponse.toString());

                    if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000") || myResponse.getData().substring(0, 4).equalsIgnoreCase("9000")) {
                        log("pan response data", myResponse.getData());

                        return myResponse.getData();
                    }
                }
            }


        }

        return null;
    } // eof pan

    /**
     *
     * @param mToday
     * @param dateExp
     * @return
     */
    public static boolean isCardExpired(Date mToday,String dateExp){


        SimpleDateFormat sdfDate = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
        Date expDate = null;
        try {
            expDate = sdfDate.parse(dateExp);
        } catch (ParseException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        if (mToday.after(expDate)) {
            log("isCardExpired", "card expired");
            return true ;
        }

        if (mToday.before(expDate)) {
            log("isCardExpired", "card  not expired ");
            return false ;


        }

        return true ;
    }

    // Binary record for nfc tags // card

    /**
     *
     * @param card
     * @param data
     * @param length
     * @param context
     * @return
     */
    public static ChipResponse executeApdu(BankCard card, byte[] data, int length , Context context ) {


        ChipResponse myResponse = new ChipResponse();
        byte[] respdata = new byte[100];
        int[] resplen = new int[1];

        String St = "";
        int apdufilepinret = -1;





        try {
            apdufilepinret = card.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen);
            St = Utils.byteArrayToHex(respdata);




            myResponse.setStatus(St);
            myResponse.setData(St);
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return myResponse;
    } // eof executeApdu

    /**
     *
     * @param card
     * @param data
     * @param length
     * @param context
     * @return
     */
    public static byte[] executeApduByte(BankCard card, byte[] data, int length , Context context ) {


        ChipResponse myResponse = new ChipResponse();
        byte[] respdata = new byte[100];
        int[] resplen = new int[1];

        String St = "";
        int apdufilepinret = -1;




        try {
            apdufilepinret = card.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen);
            St = Utils.byteArrayToHex(respdata);




            myResponse.setStatus(St);
            myResponse.setData(St);
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return respdata;
    } // eof executeApdu

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param data
     * @param length
     * @param context
     * @return
     */
    public static ChipResponse executeApduGlobal(BankCard card,UICCpuReader uicCpuReader , byte[] data, int length , Context context ) {

        if(BuildConfig.POS_TYPE.equals("LANDI"))
            return executeApduSizeLANDI(uicCpuReader,data,context) ;

        else if(BuildConfig.POS_TYPE.equals("PAX"))
            return executeApduSizePAX(data,context) ;

        else return executeApdu(card,data,length,context) ;

    }
    // LANDI Functions

    /**
     *
     * @param card
     * @param icCpuReader
     * @param context
     * @return
     */
    public static  boolean selectAppLANDI(UEMV card ,UICCpuReader icCpuReader, Context context) {
        ChipResponse myResponse = new ChipResponse();
        myResponse = null;
        String Credit_Data = "00A4040008D1D2D3D4D5D6D7D800";

        try {

            addAIDLANDI(card,Credit_Data,true);

            byte[] bytes = Utils.hexStringToByteArray(Credit_Data);


            myResponse = executeApduSizeLANDI(icCpuReader,bytes,context);

            log("selectApp apdu", Credit_Data);
            log("selectApp response", myResponse.toString());

            if(myResponse.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                return true;
            }

        }catch (RemoteException ex){

            ex.printStackTrace();
            log(TAG, ex + ExceptionUtils.getStackTrace(ex));

        }



        return false;
    } // eof selectApp

    /**
     *
     * @param card
     * @param icCpuReader
     * @param context
     * @return
     */
    public static String getCardInfoLANDI(UEMV card ,UICCpuReader icCpuReader , Context context) {
        ChipResponse myResponse = new ChipResponse();
        String rc = "00A40000022F09";
        String recods = "00B2020428";

        byte[] record1 = Utils.hexStringToByteArray(rc);
        if(selectAppLANDI(card,icCpuReader,context)) {
            myResponse = executeApduSizeLANDI(icCpuReader,record1,context);
            // log("Resp File => ", myResponse.toString());
            log("pan apdu 1", rc);
            log("pan response 1", myResponse.toString());

            if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                byte[] Data1 = Utils.hexStringToByteArray(recods);
                myResponse = executeApduSizeLANDI(icCpuReader,Data1,context);
                // log("Resp Record => ", myResponse.toString());
                log("pan apdu 2", recods);
                log("pan response 2", myResponse.toString());

                if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                    log("pan response data", myResponse.getData());

                    return myResponse.getData();
                }
            }
        }

        return null;
    } // eof pan

    /**
     *
     * @param emv
     * @param aid
     * @param partSlt
     * @throws RemoteException
     */
    public static void addAIDLANDI(UEMV emv ,String aid, boolean partSlt) throws RemoteException {
        emv.manageAID(ActionFlag.ADD, aid, partSlt);
    }

    /**
     *
     * @param icCpuReader
     * @throws RemoteException
     */
    public static void powerUpLANDI(UICCpuReader icCpuReader) throws RemoteException {
        icCpuReader.initModule(PowerMode.EMV, Voltage.ICCpuCard.VOL_3);
    }

    /**
     *
     * @param icCpuReader
     * @throws RemoteException
     */
    public static void powerONLANDI(UICCpuReader icCpuReader) throws RemoteException {
        BytesValue atr = new BytesValue();
        IntValue protocol = new IntValue();
        int ret = icCpuReader.powerUp(atr, protocol);
        if (ret != ICError.SUCCESS) {
            log("ERROR POWER ON !","POWER ON : "+ret+"") ;
        }
    }

    /**
     *
     * @param icCpuReader
     * @throws RemoteException
     */
    public static void powerDownLANDI(UICCpuReader icCpuReader) throws RemoteException {
        int ret = icCpuReader.powerDown();
        if (ret != ICError.SUCCESS) {
            log("ERROR POWER DOWN !","POWER DOWN : "+ret+"") ;
        }
    }

    /**
     *
     * @param icCpuReader
     * @return
     * @throws RemoteException
     */
    public static boolean isCardInLANDI(UICCpuReader icCpuReader) throws RemoteException {
        return icCpuReader.isCardIn();
    }

    /**
     *
     * @param apdu
     * @param icCpuReader
     * @return
     * @throws RemoteException
     */
    public static ApduResponse exchangeApduLANDI(byte[] apdu, UICCpuReader icCpuReader) throws RemoteException {
        ApduResponse response = icCpuReader.exchangeApdu(apdu);
        int ret = response.getAPDURet();
        if (ret != ICError.SUCCESS && ret != ICError.ERROR_IC_SWDIFF) {
            log("exchangeApdu LANDI","Error response : "+ret);
        }
        return response;
    }

    /**
     *
     * @param icCpuReader
     * @param data
     * @param context
     * @return
     */
    public static ChipResponse executeApduSizeLANDI( UICCpuReader icCpuReader,byte[] data , Context context) {


        ChipResponse myResponse = new ChipResponse();
        String St = "";



        ApduResponse response ;


        try {
            //apdufilepinret = exchangeApduLANDI(data,icCpuReader).getAPDURet() ;
            response = exchangeApduLANDI(data,icCpuReader) ;

            if(response.getData() != null)
                St = Utils.byteArrayToHex(response.getData());

            if(response.getAPDURet() == ICError.ERROR_IC_SWDIFF || response.getAPDURet() == ICError.SUCCESS )
                myResponse.setStatus("9000");
            if(!St.isEmpty())
                myResponse.setData(St);
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return myResponse;
    } // eof executeApdu

    /**
     *
     * @param icCpuReader
     * @param data
     * @param context
     * @return
     */
    public static ChipResponse executeApduSizeSecondLANDI(UICCpuReader icCpuReader,byte[] data, Context context) {


        ChipResponse myResponse = new ChipResponse();
        String St = "";




        ApduResponse response ;


        try {
            //apdufilepinret = exchangeApduLANDI(data,icCpuReader).getAPDURet() ;
            response = exchangeApduLANDI(data,icCpuReader) ;

            if(response.getData() != null)
                St = Utils.byteArrayToHex(response.getData());




            if(response.getAPDURet() == ICError.ERROR_IC_SWDIFF || response.getAPDURet() == ICError.SUCCESS )
                myResponse.setData("9000");
            if(!St.isEmpty())
                myResponse.setStatus(St);
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        return myResponse;
    } // eof executeApdu


    // EOF LANDI FUNCTIONS

    /**
     *
     * @param card
     * @param data
     * @param length
     * @param size
     * @param context
     * @return
     */
    public static ChipResponse executeApduSize(BankCard card,byte[] data, int length , int size , Context context) {


        ChipResponse myResponse = new ChipResponse();
        byte[] respdata = new byte[size];
        int[] resplen = new int[1];

        String St = "";
        int apdufilepinret = -1;





        try {
            apdufilepinret = card.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen);
            St = Utils.byteArrayToHex(respdata);





            myResponse.setStatus(St.substring(0,size).substring(0,4));
            myResponse.setData(St.substring(size,size*2));
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return myResponse;
    } // eof executeApdu

    /**
     *
     * @param card
     * @param data
     * @param length
     * @param size
     * @param context
     * @return
     */
    public static ChipResponse executeApduSizeSecond(BankCard card,byte[] data, int length , int size , Context context) {


        ChipResponse myResponse = new ChipResponse();
        byte[] respdata = new byte[size];
        int[] resplen = new int[1];

        String St = "";
        int apdufilepinret = -1;


        try {



            apdufilepinret = card.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen);
            St = Utils.byteArrayToHex(respdata);



            myResponse.setStatus(St.substring(size,size*2).substring(0,4));
            log("executeApduSizeSecond  ","executeApduSizeSecond status :"+myResponse.getStatus().substring(0,4));
            myResponse.setData(St.substring(0,size));
            log("executeApduSizeSecond  ","executeApduSizeSecond data :"+myResponse.getData());

        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return myResponse;
    } // eof executeApdu

    /**
     *
     * @param card
     * @param data
     * @param length
     * @param size
     * @param context
     * @return
     */
    public static byte[] executeApduSizeSecondByte(BankCard card,byte[] data, int length , int size , Context context) {


        ChipResponse myResponse = new ChipResponse();
        byte[] respdata = new byte[size];
        int[] resplen = new int[1];

        String St = "";
        int apdufilepinret = -1;


        try {




            apdufilepinret = card.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen);
            St = Utils.byteArrayToHex(respdata);



            myResponse.setStatus(St.substring(size,size*2).substring(0,4));
            log("executeApduSizeSecond  ","executeApduSizeSecond status :"+myResponse.getStatus().substring(0,4));
            myResponse.setData(St.substring(0,size));
            log("executeApduSizeSecond  ","executeApduSizeSecond data :"+myResponse.getData());

        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return respdata;
    } // eof executeApdu


    /**
     *
     * @param card
     * @param icCpuReader
     * @param data
     * @param length
     * @param size
     * @param context
     * @return
     */
    public static ChipResponse executeApduSizeSecondGlobal(BankCard card,UICCpuReader icCpuReader,byte[] data, int length , int size , Context context) {


        if(BuildConfig.POS_TYPE.equals("LANDI") && icCpuReader != null)
            return executeApduSizeLANDI(icCpuReader,data,context) ;

        else if(BuildConfig.POS_TYPE.equals("PAX") )
            return executeApduSizePAX(data,context) ;

        else return executeApduSizeSecond(card,data,length,size,context);

    } // eof executeApdu

    /**
     *
     * @param card
     * @param icCpuReader
     * @param data
     * @param length
     * @param size
     * @param context
     * @return
     */
    public static ChipResponse executeApduSizeGlobal(BankCard card,UICCpuReader icCpuReader,byte[] data, int length , int size , Context context) {


        if(BuildConfig.POS_TYPE.equals("LANDI") && icCpuReader != null)
            return executeApduSizeLANDI(icCpuReader,data,context) ;
        else if(BuildConfig.POS_TYPE.equals("PAX") )
            return executeApduSizePAX(data,context) ;

        else return executeApduSize(card,data,length,size,context);

    } // eof executeApdu


    /**
     *
     * @param card
     * @param data
     * @param length
     * @return
     */
    public static ChipResponse executeApdu2(BankCard card,byte[] data, int length) {
        ChipResponse myResponse = new ChipResponse();
        byte[] respdata = new byte[190];
        int[] resplen = new int[1];

        String St = "";
        int apdufilepinret = -1;

        try {
            apdufilepinret = card.sendAPDU(BankCard.CARD_MODE_ICC, data, length, respdata, resplen);
            St = Utils.byteArrayToHex(respdata);
            myResponse.setStatus(St);
            myResponse.setData(St);
        } catch (RemoteException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }

        return myResponse;
    } // eof executeApdu

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param fileBinary
     * @param length
     * @param context
     * @return
     */
    public static String readBinaryFile(BankCard card,UICCpuReader uicCpuReader,String fileBinary,String length , Context context) {

        byte [] res=null;

        String selectKeyFile="00A4000002"+fileBinary;
        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduSizeGlobal(card,uicCpuReader,bytes,bytes.length,Integer.parseInt(selectKeyFile.length()+"",16)*2,context);


        String headerP="00B00000"+length;
        byte[] lastTrans = Utils.hexStringToByteArray(headerP);
        ChipResponse lastTransRS = new ChipResponse();
        lastTransRS = executeApduSizeSecondGlobal(card,uicCpuReader,lastTrans,lastTrans.length,Integer.parseInt(length,16)*2,context);

        if(lastTransRS.getData() != null) {
            byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
            log("readBinaryFile ", "----> readBinaryFile :" + lastTransRS.getData());

            String result = Utils.byteArray2hexString(resLastTrans);
            return result ;
        }


        return "" ;


            /*if(resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")
                    && resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                this.resultat = 0 ;
                return true;
            }

            return false;*/


    }

    /**
     *
     * @param card
     * @param uicCpuReader
     * @param fileBinary
     * @param len
     * @param data
     * @param context
     * @return
     */
    public static String updateBinaryFile(BankCard card,UICCpuReader uicCpuReader,String fileBinary,String len,String data , Context context) {

        byte [] res=null;

        String selectKeyFile="00A4000002"+fileBinary;
        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduSizeGlobal(card,uicCpuReader,bytes,bytes.length,Integer.parseInt(selectKeyFile.length()+"",16)*2,context);

        String headerP="00D60000"+len;
        byte[] lastTrans = Utils.hexStringToByteArray(headerP+data);
        log("headerP+data ", " updateBinaryFile headerP+data :" + Utils.byteArray2hexString(lastTrans));

        ChipResponse lastTransRS = new ChipResponse();
        lastTransRS = executeApduSizeSecondGlobal(card,uicCpuReader,lastTrans,lastTrans.length,headerP.length()+Integer.parseInt(len,16)*2,context);

        if(lastTransRS.getStatus() != null)
            log("updateBinaryFile ","----> updateBinaryFile readRecordLinear status :"+lastTransRS.getStatus());


        if(lastTransRS.getData() != null) {
            byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
            log("updateBinaryFile ","----> updateBinaryFile readRecordLinear data:"+lastTransRS.getData());

            String result = Utils.byteArray2hexString(resLastTrans);
            String resultNFC = UtilsCardInfo.readBinaryFile(card, uicCpuReader, "2F20", "5F", context);
            log("UpdatedResultNFC :: ",resultNFC);
            return result ;
        }


        return "" ;

            /*if(resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")
                    && resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                this.resultat = 0 ;
                return true;
            }

            return false;*/


    }

    /**
     *
     * @param ncftag
     * @param record
     * @param position
     * @param ncfCounter
     * @return
     */
    public static String addnfctorecord(NfcTagModel ncftag, String record, int position, int ncfCounter){


        String newRecord=null;
        String temp = ncftag.getNfc()+ncftag.getRfid()+ncftag.getGps()+ncftag.getKm()+ncftag.getMatricule()+ncftag.getKmValeur(); //
        log(TAG,"addnfctorecord ---> "+temp);

        if (position==1){
            if (ncfCounter<1) ncfCounter=ncfCounter+1;

            newRecord = temp+record.substring((temp.length()), record.length()-2)+"0"+ncfCounter;

        }else  if (position==2){
            if (ncfCounter<2) ncfCounter=ncfCounter+1;
            newRecord = record.substring(0, temp.length())+temp+record.substring((temp.length())*2, record.length()-2)+"0"+ncfCounter;

        }else  if (position==3){
            if (ncfCounter<3) ncfCounter=ncfCounter+1;

            newRecord = record.substring(0, (temp.length())*2)+temp+record.substring((temp.length())*3, record.length()-2)+"0"+ncfCounter;

        }

        return newRecord;

    }

    /**
     *
     * @param ncftag
     * @param record
     * @param position
     * @return
     */
    public static String updateKM(NfcTagModel ncftag, String record, int position){

        Log.w(" updateKM nfc "," updateKM nfc tag : "+ncftag != null ? ncftag.toString() : "null");
        String newRecord=null;
        String temp = ncftag.getNfc()+ncftag.getRfid()+ncftag.getGps()+ncftag.getKm()+ncftag.getMatricule()+ Utils.padZeroLEFT(ncftag.getKmValeur(),6);
        String count = record.substring(189,190);
        int nfcCounter = 0;
        if(!count.equals("F"))
        {
            nfcCounter = Integer.parseInt(record.substring(189,190));
        }
        if (position==1){

            newRecord = temp+record.substring(temp.length(), record.length()-2)+"0"+nfcCounter;

        }else  if (position==2){
            newRecord = record.substring(0, temp.length())+temp+record.substring(temp.length()*2, record.length()-2)+"0"+nfcCounter;

        }else  if (position==3){

            newRecord = record.substring(0, temp.length()*2)+temp+record.substring(temp.length()*3, record.length()-2)+"0"+nfcCounter;

        }


        return newRecord;

    }

    /**
     *
     * @param recordBinary
     * @return
     */
    public static ArrayList<NfcTagModel> getNFCList(String recordBinary) {

        log(TAG,"********* getNFCList() RecordBinary: "+recordBinary);
        int length = 54;
        int record = 1;
        int startpost = 0;
        String data = "";
        ArrayList<NfcTagModel>  nfc = new ArrayList<>();

        while (record<=3){

            data = recordBinary.substring(startpost,length);
            log("tags list","tag num "+record+" --->"+data);
            if (!data.equals("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF")){
                String nfc1 = data.substring(0, 14);
                String rfid = data.substring(14,21);
                String gps = data.substring(21,22);
                String km = data.substring(22,24);
                String matricule = data.substring(24,48);
                String kmkmaleur = data.substring(48,54);

                //   NfcTags nfctag1 = new NfcTags("AAEF35AE4FA4AA", "9999999", "0", "01", "6354.B.54","444444","PAD");

                nfc.add(new NfcTagModel(nfc1, rfid, gps, km, matricule, kmkmaleur,record));
            }


            startpost = startpost+54;
            length = length+54;
            record++;

        }


        return nfc;

    }

    /**
     *
     * @param recordBinary
     * @return
     */
    public static ArrayList<NfcTagModel> getNFCListWithMatricule(String recordBinary) {

        int length = 54;
        int record = 1;
        int startpost = 0;
        String data = "";
        ArrayList<NfcTagModel>  nfc = new ArrayList<>();

        while (record<=3){

            data = recordBinary.substring(startpost,length);
            log("tags list","tag num "+record+" --->"+data);
            if (!data.equals("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF")){
                String nfc1 = data.substring(0, 14);
                String rfid = data.substring(14,21);
                String gps = data.substring(21,22);
                String km = data.substring(22,24);
                String matricule = Utils.unHex(data.substring(24,48));
                String kmkmaleur = data.substring(48,54);

                //   NfcTags nfctag1 = new NfcTags("AAEF35AE4FA4AA", "9999999", "0", "01", "6354.B.54","444444","PAD");

                nfc.add(new NfcTagModel(nfc1, rfid, gps, km, matricule, kmkmaleur,record));
            }

            startpost = startpost+54;
            length = length+54;
            record++;

        }


        return nfc;

    }

    /**
     *
     * @param list
     * @param nfcenum
     * @param valeur
     * @return
     */
    public static NfcTagModel searchforNFCTag(List<NfcTagModel> list, NFCEnum nfcenum, String valeur){

        NfcTagModel nfc = null;

        Iterator<NfcTagModel> it = list.iterator();
        while ( it.hasNext()){

            NfcTagModel nfctag = it.next();
            if(NFCEnum.Matricule==nfcenum){
                String matricule = nfctag.getMatricule();

                if (Utils.encodeHexString(Utils.matriculepad(valeur)).toUpperCase().equals(matricule)) nfc = nfctag;

            }else if(NFCEnum.NFC==nfcenum){
                String nfctmp = nfctag.getNfc();


                if (nfctmp.contains(valeur)) nfc = nfctag;

            }



        }

        return nfc;
    }

    /**
     *
     * @param record
     * @param position
     * @return
     */
    public static String removenfctorecord(String record,int position){


        String newRecord=null;
        String pad = "FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF";
        String pad2="FFFFFFFFFFFFFFFFFFFFFFFFFF";

        int cnfc = Integer.parseInt(record.substring(189, 190));
        cnfc--;

        if (position==1){

            newRecord = pad+ record.substring(54, 162)+pad2+"0"+cnfc;

        }else  if (position==2){

            newRecord = record.substring(0, 54)+pad+record.substring(108, 162)+pad2+"0"+cnfc;

        }else  if (position==3){


            newRecord = record.substring(0, 108)+pad+pad2+"0"+cnfc;

        }


        return newRecord;

    }

    /**
     *
     * @param vehicleModel
     * @param i
     * @param kmkvaleur
     * @return
     */
    public static NfcTagModel initNfcTAG(VehicleModel vehicleModel, int i, String kmkvaleur){
        String nfc = Utils.padZero(vehicleModel != null ? vehicleModel.getTagnfc().toUpperCase() : "0",14);
        String rfid = "9999999";//Utils.padZero(0+"",7);
        String gps = "0";//Utils.padZero(0+"",1);
        String km = Utils.padZero(vehicleModel.getKilometrage()+"",2);//"01";//
        log(TAG,"v.getKilometrage() ---> "+vehicleModel.getKilometrage());
        log(TAG,"km ---> "+km);
        String matricule = vehicleModel.getMatricule();
        if(matricule != null && matricule.length() > 12)
        {
            matricule = matricule.substring(0,12);
        }
        NfcTagModel nfcTagModel = new NfcTagModel(nfc != null ? nfc.toUpperCase():"", rfid, gps, km, matricule, kmkvaleur,i,"PAD");
        log(TAG,"NFC Tag Model v---> "+vehicleModel);
        log(TAG,"NFC Tag Model ---> "+nfcTagModel);
        return nfcTagModel ;
    }


    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param record
     * @param context
     * @return
     */
    public static String lastTransc(BankCard mBankCard ,UICCpuReader uicCpuReader, String record  , Context context) {

        byte [] res=null;

        String SelectEperseFile="00A40000020005";
        //byte[] bytes = Utils.hexStringToByteArray(SelectEperseFile);
        byte[] bytes = Utils.hexString2byteArray(SelectEperseFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduGlobal(mBankCard,uicCpuReader,bytes,bytes.length,context);

        if(myResponse.getStatus().substring(0,4).equalsIgnoreCase("9000")) {

            log("lastTransc  ","lastTransc success :"+myResponse.getStatus().substring(0,4));




            String readLastTransaction="00B2"+record+"04"+"30";
            String data=null;
            //byte[] lastTrans = Utils.hexStringToByteArray(readLastTransaction);
            byte[] lastTrans = Utils.hexString2byteArray(readLastTransaction);
            ChipResponse lastTransRS = new ChipResponse();
            lastTransRS = executeApduSizeSecondGlobal(mBankCard,uicCpuReader,lastTrans,lastTrans.length,Integer.valueOf("1E",16)*2,context);
            if(lastTransRS.getStatus().startsWith("9000")) {
                //byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
                byte[] resLastTrans = Utils.hexString2byteArray(lastTransRS.getData());
                data = Utils.byteArray2hexString(resLastTrans);
                log("lastTransc data ","lastTransc data :"+data);
            }



            return data ;

        } else return null ;




            /*if(resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")
                    && resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                this.resultat = 0 ;
                return true;
            }

            return false;*/


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param debut
     * @param fin
     * @param context
     * @return
     */
    public static double getAmount(BankCard mBankCard ,UICCpuReader uicCpuReader,int debut, int fin , Context context){
        UtilsCardInfo.getAmountCardString(mBankCard ,uicCpuReader,debut, fin , context);
        String mont = lastTransc(mBankCard,uicCpuReader,"01",context) ;
        log(TAG,"Amount Card String 1::: "+ UtilsCardInfo.getAmountCardString(mBankCard ,uicCpuReader,debut, fin , context));
        String amountHex = (mont != null && !mont.isEmpty() ) ? mont.substring(debut,fin) : "";
        log(TAG,"AMOUNT Card:::: "+mont);
        double montant ;
        if(!amountHex.isEmpty()) {
            Long mount = Long.parseLong(amountHex, 16);

            montant = (double) mount / 100;

        }
        else montant = 0;

        return  Math.max(0, montant) ;


    }
    public static BigDecimal getAmountCardString(BankCard mBankCard , UICCpuReader uicCpuReader, int debut, int fin , Context context){

        String mont = lastTransc(mBankCard,uicCpuReader,"01",context) ;

        String amountHex = (mont != null && !mont.isEmpty() ) ? mont.substring(debut,fin) : "";
        log(TAG,"Amount Card String ::: "+mont);
        double montant ;
        if(!amountHex.isEmpty()) {
            Long mount = Long.parseLong(amountHex, 16);

            montant = (double) mount / 100;

        }
        else montant = 0;

        return  BigDecimal.valueOf(Math.max(0, montant)) ;


    }
    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param context
     * @return
     */
    public  static String getCardInfos(BankCard mBankCard ,UICCpuReader uicCpuReader, Context context) {
        ChipResponse myResponse = new ChipResponse();
        myResponse = null;
        String rc = "00A40000022F09";
        String recods = "00B2020428";

        byte[] record1 = Utils.hexStringToByteArray(rc);
        if(selectApp(mBankCard,uicCpuReader,context)) {
            myResponse = executeApduSizeGlobal(mBankCard,uicCpuReader,record1, record1.length,Integer.parseInt(rc.length()+"",16)*2,context);
            // log("Resp File => ", myResponse.toString());
            log("pan apdu 1", rc);
            log("pan response 1", myResponse.toString());

            if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                byte[] Data1 = Utils.hexStringToByteArray(recods);
                myResponse = executeApduSizeSecondGlobal(mBankCard,uicCpuReader,Data1, Data1.length,Integer.parseInt("28",16)*2,context);
                // log("Resp Record => ", myResponse.toString());
                log("pan apdu 2", recods);
                log("pan response 2", myResponse.toString());

                if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                    return myResponse.getData();
                }
            }
        }

        return null;
    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param fileLinear
     * @param recordNumber
     * @param length
     * @param context
     * @return
     */
    public static String readRecordLinear(BankCard mBankCard,UICCpuReader uicCpuReader, String fileLinear,String recordNumber,String length , Context context ) {

        byte [] res=null;

        String selectKeyFile="00A4000002"+fileLinear;
        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduSizeGlobal(mBankCard,uicCpuReader,bytes,bytes.length,Integer.parseInt(length,16)*2,context);

        if (myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {


            String headerP = "00B2" + recordNumber + "04" + length;
            byte[] lastTrans = Utils.hexStringToByteArray(headerP);
            ChipResponse lastTransRS = new ChipResponse();
            lastTransRS = executeApduSizeSecondGlobal(mBankCard,uicCpuReader, lastTrans, lastTrans.length, Integer.parseInt(length, 16) * 2,context);


            byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
            //log("readRecordLinear ","----> readRecordLinear :"+lastTransRS.getData());


            String result = Utils.byteArray2hexString(resLastTrans);

            log("pan response 1","readRecordLinear ----> "+ result);

            if (lastTransRS.getStatus().substring(0, 4).equalsIgnoreCase("9000")) {
                return result;
            }
            return "-1";
        }

        return "-1";

    }

    public static int getNbreTrx(String plafondCarte ,int debut, int fin){
        String plf = plafondCarte.substring(debut,fin);
        return Integer.valueOf(plf) ;
    }



    // eof pan

    /**
     *
     * @param mBankCard
     * @param infoCarte
     * @param recordNumber
     * @return
     */



    // eof pan

    /**
     *
     * @param mBankCard
     * @param infoCarte
     * @param recordNumber
     * @return
     */
    public static Double getAmountCreditPostP(BankCard mBankCard,String infoCarte,String recordNumber){

        //String infoCarte = readRecordLinear(mBankCard,"2F07", recordNumber, "32");

        String dateLastplf = infoCarte.substring(12,24);
        //log("date last plf carte ","----> date last plf carte :"+dateLastplf);

        return Double.parseDouble(dateLastplf)/100 ;


    }

    /**
     *
     * @param infoCarte
     * @return
     */
    public static long getCompteurTransactionPanDebit(String infoCarte){


        String comptD = infoCarte.substring(24,36);
        log("date last plf carte ","----> Compteur TransactionPan Debit :"+comptD);

        return Long.parseLong(comptD) ;

    }

    /**
     *
     * @param infoCarte
     * @return
     */
    public static long getCompteurTransactionPanCredit(String infoCarte){

        String comptC = infoCarte.substring(36,48);
        log("date last plf carte ","----> Compteur TransactionPan Credit :"+comptC);

        return Long.parseLong(comptC) ;

    }


    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param context
     * @return
     */
    public static  int  getPinTryCounter(BankCard mBankCard  ,UICCpuReader uicCpuReader, Context context) {

        byte [] res=null;

        int pinCounter=0;


        String selectKeyFile="00A40000022F01";
        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduSizeGlobal(mBankCard,uicCpuReader,bytes,bytes.length,Integer.parseInt(selectKeyFile.length()+"", 16)*2,context);


        String pinTryCounter="00CA000001";
        byte[] lastTrans = Utils.hexStringToByteArray(pinTryCounter);
        ChipResponse lastTransRS = new ChipResponse();
        lastTransRS = executeApduSizeSecondGlobal(mBankCard,uicCpuReader,lastTrans,lastTrans.length,Integer.parseInt(pinTryCounter.length()+"", 16)*2,context);

        byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
        //log("readRecordLinear ","----> readRecordLinear :"+lastTransRS.getData());

        String	result= Utils.byteArray2hexString(resLastTrans);
        //log("pinCounter "," result pinCounter ---->  :"+result);

        pinCounter = Integer.parseInt( Utils.byteArray2hexString(resLastTrans).substring(0, 2),16);
        //log("pinCounter ","pinCounter ---->  :"+pinCounter);


        return pinCounter;


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param puk
     * @param context
     * @return
     */
    public static boolean unblockPin(BankCard mBankCard ,UICCpuReader uicCpuReader, String puk , Context context) {


        String selectKeyFile="00A40000022F01";
        byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduGlobal(mBankCard,uicCpuReader,bytes,bytes.length,context);

        String headerP="002C010108";
        byte[] lastTrans = Utils.hexStringToByteArray(headerP+puk);
        ChipResponse lastTransRS = new ChipResponse();
        lastTransRS = executeApduGlobal(mBankCard,uicCpuReader,lastTrans,lastTrans.length,context);


        byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
        log("readRecordLinear ","----> readRecordLinear :"+lastTransRS.getData());


        String	result= Utils.byteArray2hexString(resLastTrans);
        log("unblockPin "," result unblockPin ---->  :"+result);

        if(result != null && result.startsWith("9000")){

            log("unblockPin "," unblockPin ---->  : OKKK");

            return true;

        }
        else return false;



    }

    public static String getDateExpCard(BankCard mBankCard,String infoCarte  ){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");


        String dateExpiration = infoCarte.substring(46,50)+
                "-"+infoCarte.substring(50,52)+
                "-"+infoCarte.substring(52,54);
        //log("dateExpiration carte ","----> dateExpiration carte :"+dateExpiration);

        return dateExpiration ;


    }

    // Restrictions Card

    public static int getRestStationCard(String plf ) {
        String idprof_GEOG = plf.substring(0, 32);
        int result = 0;
        try {
            result = Integer.valueOf(idprof_GEOG);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    public static int getHourRestriction(String plf ) {
        String idprofil_HOR = plf.substring(32, 42);
        int result = 0;
        try {
            result = Integer.valueOf(idprofil_HOR);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        return result;
    }

    public static int getHolidayRestriction(String plf ) {
        String id_PRF_JRS = plf.substring(42, 52);
        int result = 0;
        try {
            result = Integer.valueOf(id_PRF_JRS);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        return result;    }
    public static int getSectorRestriction(String plf ) {
        String rest_ZONE = plf.substring(52,66);
        int result = 0;
        try {
            result = Integer.valueOf(rest_ZONE);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        return result;      }


    public static int getArticalRestriction(String plf) {
        String rest_ARTICLE = plf.substring(66,80);
        int result = 0;
        try {
            result = Integer.valueOf(rest_ARTICLE);
        } catch (NumberFormatException e) {
            e.printStackTrace();
            log(TAG, e + ExceptionUtils.getStackTrace(e));
        }
        return result;     }

    public static boolean isStationRestriction(int idRestCard,int stationId,  List<RestrictedStationModel> mRestrictionsStation){

        ArrayList<Integer> listdata = new ArrayList<>();



        for(RestrictedStationModel item :  mRestrictionsStation ){
            if(item.getId() == idRestCard){
                try {
                    //JSONObject json = new JSONObject(item.toString());
                    JSONArray jArray = new JSONArray(item.getOptions());
                    if (jArray != null) {
                        for (int i=0;i<jArray.length();i++){
                            log(TAG,"STATION ID: "+jArray.getInt(i));
                            listdata.add(jArray.getInt(i));
                        }
                    }

                    if(listdata.contains(stationId))
                        return false ;

                }catch (JSONException E){

                    log("json exce",E.getMessage());

                    log(TAG, E + ExceptionUtils.getStackTrace(E));
                }


            }

        }

        return true;


    }

    /**
     *
     * @param idRestCard
     * @param secteurId
     * @param mRestrictionsSecteur
     * @return
     */
    public static boolean isSecteurRestriction(int idRestCard,int secteurId, List<RestrictedSectorsModel> mRestrictionsSecteur){

        ArrayList<Integer> listdata = new ArrayList<>();


        for(RestrictedSectorsModel item :  mRestrictionsSecteur ){

            if(item.getId() == idRestCard){
                try {
                    //JSONObject json = new JSONObject(item.toString());
                    JSONArray jArray = new JSONArray(item.getOptions());
                    if (jArray != null) {
                        for (int i=0;i<jArray.length();i++){

                            listdata.add(jArray.getInt(i));
                        }
                    }

                    if(listdata.contains(secteurId))
                        return false ;

                }catch (JSONException E){

                    log("json exce",E.getMessage());

                    log(TAG, E + ExceptionUtils.getStackTrace(E));
                }


            }

        }

        return true;


    }

    /**
     *
     * @param idRestCard
     * @param mRestrictionsHolidays
     * @param dateToday
     * @return
     */
    public static boolean isHolidaysRestriction(int idRestCard, List<RestrictedHolidaysModel> mRestrictionsHolidays, String dateToday) {

        String s ;
        boolean isIT = false ;

        dateToday =getCurrentDateAndTime();

        for(RestrictedHolidaysModel item :  mRestrictionsHolidays ){

            if(item.getId() == idRestCard){
                try {
                    s = item.getOptions();
                    log("isHolidaysRestriction","options ---> "+s);
                    log("dateToday","options ---> "+dateToday);
                    String dater=s.replace("\\", "");
                    log("dater","options ---> "+dater);
                    isIT = dater.contains(dateToday + "");

                }
                catch (Exception e)
                {
                    e.printStackTrace();
                    log(TAG, e + ExceptionUtils.getStackTrace(e));
                }

            }
        }

        return isIT ;


    }
    //int x = 0;
    public static String getCurrentDateAndTime(){
        Date c = Calendar.getInstance().getTime();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd/MM/yyyy");
        String formattedDate = simpleDateFormat.format(c);
        return formattedDate;
    }
    /**
     *
     * @param idRestCard
     * @param idProduit
     * @param mRestrictionsArticles
     * @return
     */
    public static boolean isArticleRestriction(int idRestCard,int idProduit, List<RestrictedProductsModel> mRestrictionsArticles){

        ArrayList<Integer> listdata = new ArrayList<>();


        for(RestrictedProductsModel item :  mRestrictionsArticles ){


            if(item.getId() == idRestCard){
                try {
                    //JSONObject json = new JSONObject(item.toString());
                    JSONArray jArray = new JSONArray(item.getOptions());
                    if (jArray != null) {
                        for (int i=0;i<jArray.length();i++){

                            listdata.add(jArray.getInt(i));
                        }
                    }

                    if(listdata.contains(idProduit))
                        return false ;

                }catch (JSONException E){

                    log("json exce",E.getMessage());
                    log(TAG, E + ExceptionUtils.getStackTrace(E));
                }


            }
        }

        return true;


    }
    public static boolean isHourRestricted(int idRestCard, List<RestrictedSchedulesModel> mRestrictionsHoraire){
        String s ;
        boolean isIT = false ;
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        String debut ;
        String fin;
        int todayNR = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (todayNR == 0)
            todayNR = 7;
        for(RestrictedSchedulesModel item :  mRestrictionsHoraire ){
            if(item.getId() == idRestCard){
                s = item.getOptions();
                log("isHoraireRestriction","options ---> "+s);
                JsonObject jsonObject = new JsonParser().parse(s).getAsJsonObject();
                if(jsonObject.has(todayNR+""))
                {
                    try{
                        JsonArray jsonObject1 = jsonObject.get(todayNR+"").getAsJsonArray();
                        debut = jsonObject1.get(0).getAsString();
                        fin = jsonObject1.get(1).getAsString();
                        isIT = UtilsDate.isDateHourbetween(Support.Companion.dateToStringHour(new Date()), debut,fin);
                    }catch (JsonIOException E){

                        log(TAG, E + ExceptionUtils.getStackTrace(E));
                    }

                }else isIT = true ;
            }
        }

        return isIT ;

    }
    public static String readRestrictionCard(BankCard mBankCard ,UICCpuReader uicCpuReader ,Context context) {

        String plf = readRecordLinear(mBankCard,uicCpuReader,"2F09", "03", "28",context);
        /*log(" restrictions carte ", "----> restrictions carte :" + plf);

        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        System.out.println(" date" + datepersomm.replaceAll("-", ""));


        String idprof_GEOG = plf.substring(0, 32);
        log(" idprof_GEOG carte ", "----> idprof_GEOG carte :" + idprof_GEOG);

        String idprofil_HOR = plf.substring(32, 42);
        log("idprofil_HOR carte ", "----> idprofil_HOR carte :" + idprofil_HOR);

        String id_PRF_JRS = plf.substring(42, 52);
        log("id_PRF_JRS carte ", "----> id_PRF_JRS carte :" + id_PRF_JRS);

        String rest_ZONE = plf.substring(52, 66);
        log("rest_ZONE carte ", "----> rest_ZONE carte :" + rest_ZONE);

        String rest_ARTICLE = plf.substring(66, 80);
        log("rest_ARTICLE carte ", "----> rest_ARTICLE carte :" + rest_ARTICLE);*/


        return plf;
    }

    // isNFC

    /**
     *
     * @param mBankCard
     * @param infoCarte
     * @return
     */
    public static  int getNFCCard(BankCard mBankCard,String infoCarte){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");


        String nfcCard = infoCarte.substring(69,70);
        log("NFC carte ","----> is  NFC carte :"+nfcCard);

        return Integer.parseInt(nfcCard);


    }

    // eof nfc

    // isLitre unite

    /**
     *
     * @param mBankCard
     * @param infoCarte
     * @return
     */
    public static String getUnitPLF(BankCard mBankCard,String infoCarte){ // 0 DH , 1 Litre

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");


        String nfcCard = infoCarte.substring(70,71);
        //log("Unit Plafund ","----> Unit Plafund  :"+(nfcCard.equalsIgnoreCase("1") ? "Litre" : "Dirhams"));

        return nfcCard ;


    }

    // eof isLitre unite

    // status CARD

    /**
     *
     * @param infoCarte
     * @return
     */
    public static  String getStatusCard (String infoCarte){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");
        log(TAG,"SetStatusCard:::: "+infoCarte);

        String statutCard = infoCarte.substring(19,20);
        log("status carte ","----> status carte :"+statutCard);

        return statutCard ;


    }

    // eof statut card

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param infoCarte
     * @param etat
     * @param context
     * @return
     */
    public static boolean setStatusCard(BankCard mBankCard,UICCpuReader uicCpuReader,String infoCarte,int etat , Context context){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");

        String prepareData = prepareRecordLinear(infoCarte,19,20,etat+"");

        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28", prepareData,context);

        log(TAG,"SetStatusCard:::: "+infoCarte);


        String statutCard = infoCarte.substring(19,20);
        log("status carte ","----> status carte :"+statutCard);


        return updateResult3.contains("9000");

    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param infoCarte
     * @param etat
     * @param context
     * @return
     */
    public static boolean changeTypeCard(BankCard mBankCard,UICCpuReader uicCpuReader,String infoCarte,int etat , Context context){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");

        String prepareData = prepareRecordLinear(infoCarte,68,69,"F");

        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28", prepareData,context);


        String statutCard = infoCarte.substring(68,69);
        log("status carte ","----> status carte :"+statutCard);


        return updateResult3.contains("9000");

    }


    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param infoCarte
     * @param lastDate
     * @param montant
     * @param compteurTrx
     * @param context
     * @return
     */
    public static boolean updateCreditPostPayee(BankCard mBankCard,UICCpuReader uicCpuReader,String infoCarte,String lastDate ,String montant,long compteurTrx, Context context){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");


        String prepareData = prepareRecordLinear(infoCarte, 12, 24, Utils.padZero(((long)(Double.parseDouble(montant)*100))+"",12));

        String prepareData1 = prepareRecordLinear(prepareData,36,48, Utils.padZero(compteurTrx+"",12));

        String prepareData2 = prepareRecordLinear(prepareData1,72,80,lastDate);

        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F07", "06", "32", prepareData2,context);


        return updateResult3.contains("9000");

    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param infoCarte
     * @param compteurTrx
     * @param context
     * @return
     */
    public static boolean updateCompteurTrxDebit(BankCard mBankCard,UICCpuReader uicCpuReader,String infoCarte,long compteurTrx , Context context){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");

        String prepareData = prepareRecordLinear(infoCarte,24,36, Utils.padZero(compteurTrx+"",12));

        log("status carte ","----> CompteurTrxDebit :"+prepareData);


        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F07", "06", "32", prepareData,context);

        return updateResult3.contains("9000");

    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param infoCarte
     * @param compteurTrx
     * @param context
     * @return
     */
    public static boolean updateCompteurTrxCredit(BankCard mBankCard,UICCpuReader uicCpuReader,String infoCarte,long compteurTrx , Context context){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");

        String prepareData = prepareRecordLinear(infoCarte,36,48, Utils.padZero(compteurTrx+"",12));

        log("status carte ","----> CompteurTrxCredit :"+prepareData);


        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F07", "06", "32", prepareData,context);

        return updateResult3.contains("9000");

    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param infoCarte
     * @param etatF
     * @param context
     * @return
     */
    public static boolean setStatusCardF(BankCard mBankCard,UICCpuReader uicCpuReader,String infoCarte,String etatF , Context context){

        //String infoCarte = readRecordLinear(mBankCard,"2F09", "02", "28");

        String prepareData = prepareRecordLinear(infoCarte,19,20,etatF);

        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28", prepareData,context);


        String statutCard = infoCarte.substring(19,20);
        log("status carte ","----> status carte :"+statutCard);


        return updateResult3.contains("9000");

    }


    /**
     *
     * @param context
     * @param uicCpuReader
     * @param mBankCard
     * @param infoCarte
     * @param etatCarte
     * @param idclient
     * @param prePOSTPAYE
     * @param plafundUNIT
     * @param blockCARTE
     * @param isNFC
     * @return
     */
    public static boolean updateCarteTotal(Context context , UICCpuReader uicCpuReader,BankCard mBankCard, String infoCarte,int etatCarte, int idclient,int prePOSTPAYE ,int plafundUNIT, int blockCARTE, int isNFC,int discountType, int deleted, int online) {

        String updatedRecord1 ;

        updatedRecord1 = prepareRecordLinear(infoCarte, 19, 20,etatCarte+""); // hardcode

        updatedRecord1 = prepareRecordLinear(updatedRecord1, 54, 64, Utils.padZero(idclient+"",10));
        if(isNFC == 0 || isNFC == 1  || isNFC == 2)
            updatedRecord1 = prepareRecordLinear(updatedRecord1, 69,70,isNFC+"");
        if(prePOSTPAYE == 2 && (plafundUNIT == 0 || plafundUNIT == 1))
            updatedRecord1 = prepareRecordLinear(updatedRecord1, 70, 71,plafundUNIT+"");

        if(prePOSTPAYE == 1)
            updatedRecord1 = prepareRecordLinear(updatedRecord1, 70, 71,0+"");

        updatedRecord1 = prepareRecordLinear(updatedRecord1, 71, 72,blockCARTE+"");
        updatedRecord1 = prepareRecordLinear(updatedRecord1, 77, 80, Utils.padZero(discountType+"",3));

        /* added by altaf and commented because the default  file length is on 80 chars */
        if(updatedRecord1.length()>80){
            if(deleted == 1 || deleted == 0) {
                updatedRecord1 = prepareRecordLinear(updatedRecord1, 81,82,"0"+deleted);
            } else {
                updatedRecord1 = prepareRecordLinear(updatedRecord1, 81,82,"F");
            }

            if(online == 1 || online == 0) {
                updatedRecord1 = prepareRecordLinear(updatedRecord1, 82,83,"0"+online);
            } else {
                updatedRecord1 = prepareRecordLinear(updatedRecord1, 82,83,"F");
            }
        }
        /* addtion end here */

        log(TAG,"discountType = "+discountType);
        log("update Etat_Card ", "----> update Status (blocked/not blocked) carte  carte :" + updatedRecord1);
        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28", updatedRecord1,context) ;
        log("Rst updateEtatCarte", "----> updateResult updateEtatCarte   carte :" + updateResult3+ " lenght :  "+updateResult3.length());
        if(BuildConfig.DEBUG)
        {
            readInfoCarte(mBankCard,uicCpuReader,context) ;
        }

        if(updateResult3.contains("9000")){
            log(TAG," infos carte changed Okkkk ");

            return true ;
        }
        else return false;
    }


    // 1 prepaye, 2 postpaye, 3 loyalty

    /**
     *
     * @param context
     * @param uicCpuReader
     * @param mBankCard
     * @param infoCarte
     * @param prePOSTPAYE
     * @return
     */
    public static String updateCarteType(Context context ,UICCpuReader uicCpuReader, BankCard mBankCard, String infoCarte,int prePOSTPAYE ) {

        String updatedRecord1 ;


        updatedRecord1 = prepareRecordLinear(infoCarte, 68, 69,prePOSTPAYE+"");



        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28", updatedRecord1,context) ;

        log("Rst updateEtatCarte", "----> updateResult updateEtatCarte   carte :" + updateResult3+ " lenght :  "+updateResult3.length());

        if(updateResult3.contains("9000")){
            log(TAG," Type carte changed Okkkk ");


            return updatedRecord1 ;
        }
        else {


            return infoCarte;

        }
    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param montantHex
     * @param key1
     * @param dataTrans
     * @param context
     * @return
     */
    public static boolean credit(BankCard mBankCard,UICCpuReader uicCpuReader,String montantHex,String key1, String dataTrans , Context context) {

        if(montantHex == null || (montantHex != null && montantHex.isEmpty()))
            return false ;

        String SelectEperseFile="00A40000020005";
        byte[] bytes = Utils.hexString2byteArray(SelectEperseFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduGlobal(mBankCard,uicCpuReader,bytes,bytes.length,context);
        String readLastTransaction="00B2000030";
        byte[] lastTrans = Utils.hexString2byteArray(readLastTransaction);
        ChipResponse lastTransRS = new ChipResponse();
        lastTransRS = executeApduGlobal(mBankCard,uicCpuReader,lastTrans,lastTrans.length,context);
        byte[] resLastTrans = Utils.hexString2byteArray(lastTransRS.getData());
        String	seq= Utils.byteArray2hexString(resLastTrans).substring(0,4);
        String getChalangeCMD="**********";
        byte[] chalange = Utils.hexString2byteArray(getChalangeCMD);
        ChipResponse chalangeRS = new ChipResponse();
        chalangeRS = executeApduGlobal(mBankCard,uicCpuReader,chalange,chalange.length,context);
        byte[] resChalange = Utils.hexString2byteArray(chalangeRS.getData());
        String	chlg= Utils.byteArray2hexString(resChalange).substring(0,16);
        log("chlg ","chlg :"+chlg);
        byte[] xorData = Utils.xor2(Utils.hexStringToByteArray(chlg),
                Utils.hexStringToByteArray(seq+montantHex+"5200"));
        log("xorData ","xorData :"+ Utils.byteArray2hexString(xorData));
        byte[] key1byte = Utils.hexString2byteArray(key1);
        byte[]  DataEncryptedt =   Utils.encipher(xorData, key1byte, "ECB", "DES");
        String DataEncrypted = Utils.byteArray2hexString(DataEncryptedt);
        log("DataEncrypted ","DataEncrypted :"+DataEncrypted);
        byte[] resultat = Utils.hexString2byteArray("**********"+montantHex+DataEncrypted.substring(0, 12)+dataTrans);
        ChipResponse resultRS = new ChipResponse();
        log("resultat ","resultat :"+ Utils.byteArray2hexString(resultat));
        resultRS = executeApduGlobal(mBankCard,uicCpuReader,resultat,resultat.length,context);
        log("recharge ","recharge :"+resultRS.getData());
        log("CREDIT recharge ","CREDIT resultat :"+resultRS.getStatus().substring(0,4));
        return resultRS.getStatus().substring(0, 4).equalsIgnoreCase("9000");


    }

    public static boolean resetCredit(BankCard mBankCard,UICCpuReader uicCpuReader,String montantHex,String key1, String dataTrans , Context context) {

        if(montantHex == null || (montantHex != null && montantHex.isEmpty()))
            return false ;

        String SelectEperseFile="00A40000020005";
        byte[] bytes = Utils.hexString2byteArray(SelectEperseFile);
        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduGlobal(mBankCard,uicCpuReader,bytes,bytes.length,context);
        String readLastTransaction="00B2000030";
        byte[] lastTrans = Utils.hexString2byteArray(readLastTransaction);
        ChipResponse lastTransRS = new ChipResponse();
        lastTransRS = executeApduGlobal(mBankCard,uicCpuReader,lastTrans,lastTrans.length,context);
        byte[] resLastTrans = Utils.hexString2byteArray(lastTransRS.getData());
        String	seq= Utils.byteArray2hexString(resLastTrans).substring(0,4);
        String getChalangeCMD="**********";
        byte[] chalange = Utils.hexString2byteArray(getChalangeCMD);
        ChipResponse chalangeRS = new ChipResponse();
        chalangeRS = executeApduGlobal(mBankCard,uicCpuReader,chalange,chalange.length,context);
        byte[] resChalange = Utils.hexString2byteArray(chalangeRS.getData());
        String	chlg= Utils.byteArray2hexString(resChalange).substring(0,16);
        log("chlg ","chlg :"+chlg);
        byte[] xorData = Utils.xor2(Utils.hexStringToByteArray(chlg),
                Utils.hexStringToByteArray(seq+montantHex+"5200"));
        log("xorData ","xorData :"+ Utils.byteArray2hexString(xorData));
        byte[] key1byte = Utils.hexString2byteArray(key1);
        byte[]  DataEncryptedt =   Utils.encipher(xorData, key1byte, "ECB", "DES");
        String DataEncrypted = Utils.byteArray2hexString(DataEncryptedt);
        log("DataEncrypted ","DataEncrypted :"+DataEncrypted);
        byte[] resultat = Utils.hexString2byteArray("**********"+montantHex+DataEncrypted.substring(0, 12)+dataTrans);
        ChipResponse resultRS = new ChipResponse();
        log("resultat ","resultat :"+ Utils.byteArray2hexString(resultat));
        resultRS = executeApduGlobal(mBankCard,uicCpuReader,resultat,resultat.length,context);
        log("recharge ","recharge :"+resultRS.getData());
        log("CREDIT recharge ","CREDIT resultat :"+resultRS.getStatus().substring(0,4));
        return resultRS.getStatus().substring(0, 4).equalsIgnoreCase("9000");


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param montantHex
     * @param key1
     * @param dataTrans
     * @param context
     * @return
     */
    public static boolean debit(BankCard mBankCard,UICCpuReader uicCpuReader,String montantHex,String key1, String dataTrans , Context context) {
        try {
            String SelectEperseFile="00A40000020005";
            byte[] bytes = Utils.hexStringToByteArray(SelectEperseFile);
            ChipResponse myResponse = new ChipResponse();
            myResponse = UtilsCardInfo.executeApduSizeGlobal(mBankCard,uicCpuReader,bytes,bytes.length,Integer.parseInt(SelectEperseFile.length()+"",16)*2,context);
            String readLastTransaction="00B2000030";
            byte[] lastTrans = Utils.hexStringToByteArray(readLastTransaction);
            ChipResponse lastTransRS = new ChipResponse();
            lastTransRS = UtilsCardInfo.executeApduSizeSecondGlobal(mBankCard,uicCpuReader,lastTrans,lastTrans.length,Integer.parseInt(readLastTransaction.length()+"",16)*2,context);
            byte[] resLastTrans = Utils.hexStringToByteArray(lastTransRS.getData());
            String	seq= Utils.byteArray2hexString(resLastTrans).substring(0,4);
            String getChalangeCMD="**********";
            byte[] chalange = Utils.hexStringToByteArray(getChalangeCMD);
            ChipResponse chalangeRS = new ChipResponse();
            chalangeRS = UtilsCardInfo.executeApduSizeSecondGlobal(mBankCard,uicCpuReader,chalange,chalange.length,Integer.parseInt(getChalangeCMD.length()+"",16)*2,context);
            byte[] resChalange = Utils.hexStringToByteArray(chalangeRS.getData());
            String	chlg= Utils.byteArray2hexString(resChalange).substring(0,16);
            log("chlg ","chlg :"+chlg);
            byte[] xorData = Utils.xor(Utils.hexStringToByteArray(chlg),
                    Utils.hexStringToByteArray(seq+montantHex+"5400"));
            byte[] key1byte = Utils.hexStringToByteArray(key1);
            byte[]  DataEncryptedt =   Utils.encipher(xorData, key1byte, "ECB", "DES");
            String DataEncrypted = Utils.byteArray2hexString(DataEncryptedt);
            log("DataEncrypted ","DataEncrypted :"+DataEncrypted);
            log("resultat ","resultat :"+"**********"+montantHex+DataEncrypted.substring(0, 12)+dataTrans);
            byte[] resultat = Utils.hexStringToByteArray("**********"+montantHex+DataEncrypted.substring(0, 12)+dataTrans); //+"FFFF");
            ChipResponse resultRS = new ChipResponse();
            resultRS = UtilsCardInfo.executeApduSizeGlobal(mBankCard,uicCpuReader,resultat,resultat.length,Integer.parseInt("28",16)*2,context);
            log("recharge ","recharge data :"+resultRS.getData());
            log("recharge ","recharge status :"+resultRS.getStatus());
            log("resultat DEBIT ","DEBIT resultat :"+resultRS.getStatus().substring(0,4));
            return resultRS.getStatus().substring(0, 4).equalsIgnoreCase("9000");

        }
        catch (Exception e)
        {
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            e.printStackTrace();
            return false;
        }
    }

    /**
     *
     * @param res
     * @param start
     * @param length
     * @param value
     * @return
     */
    public static String prepareRecordLinear(String res, int start, int length, String value ) {

        String data = null;
        // 70008200110000000281202008150000000000000000002022073100000000040001F00100000000
        if (res.endsWith("9000")){
            data = res.substring(0, start)+value+res.substring(length,res.length()-4);
        }else  data = res.substring(0, start)+value+res.substring(length);

        return  data;


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param fileLinear
     * @param recordNumber
     * @param len
     * @param data
     * @param context
     * @return
     */
    public static String updateRecordLinear(BankCard mBankCard,UICCpuReader uicCpuReader,String fileLinear,String recordNumber,String len,String data , Context context ) {


        String selectKeyFile="00A4000002"+fileLinear;
        //byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        byte[] bytes = Utils.hexString2byteArray(selectKeyFile);

        byte[] resLastTrans ;

        log("selectKeyFile"," \"00A4000002\"+fileLinear :"+ Utils.byteArray2hexString(bytes));


        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduSizeGlobal(mBankCard,uicCpuReader,bytes,bytes.length,Integer.parseInt(len, 16)*2,context);

        if(myResponse.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
            try {

                String headerP = "00DC" + recordNumber + "04" + len;
                log("headerP+data ", "headerP+data :" + headerP + data);

                byte[] resultat = Utils.hexString2byteArray(headerP + data);


                ChipResponse resultatRS = new ChipResponse();

                resultatRS = executeApduSizeSecondGlobal(mBankCard, uicCpuReader, resultat, resultat.length, Integer.parseInt(len, 16) * 2, context);

                if (resultatRS.getData() != null) {
                    resLastTrans = Utils.hexString2byteArray(resultatRS.getData());

                    log("resultat ", "resultat update :" + Utils.byteArray2hexString(resLastTrans));


                    log("updateRecord carte  ", "----> updateRecordLinear carte resultatRS.getData() :" + resultatRS.getData());

                    String result = Utils.byteArray2hexString(resLastTrans);
                    log("updateRecord carte ", "----> updateRecordLinear carte :" + result);


                }


                if ((resultatRS.getData() != null && resultatRS.getData().contains("9000")) || (resultatRS.getStatus() != null && resultatRS.getStatus().equalsIgnoreCase("9000")))
                    return "9000";
                else return "";

            }
            catch (Exception e)
            {
                log(TAG, e + ExceptionUtils.getStackTrace(e));
                e.printStackTrace();
                return "";
            }
        }

        else return "";

            /*if(resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")
                    && resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                this.resultat = 0 ;
                return true;
            }

            return false;*/


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param context
     * @return
     */
    public static String readPlafondParamCarte(BankCard mBankCard ,UICCpuReader uicCpuReader, Context context){



        String plafondCarte = readRecordLinear(mBankCard,uicCpuReader,"2F09", "05", "28",context);
        SimpleDateFormat sm = new  SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        log(TAG," date"+datepersomm.replaceAll("-", ""));


        log("Result plafond max","----> updateResult plafond max carte :"+plafondCarte);

        return  plafondCarte ;

        //String res5 = readRecordLinear( "2F09", "05", "28");

        //System.out.println("PLF_MAX_PREPAYE "+res5.substring(12,24));

    }

    /**
     *
     * @param mBankCard
     * @param plf
     * @param recordNumber
     * @return
     */

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param readCard
     * @param COMPTEUR_NBR_TRX_OFF
     * @param context
     * @return
     */

    public static boolean updateNbreTrxOFFCompteur(BankCard mBankCard,UICCpuReader uicCpuReader,String readCard,String COMPTEUR_NBR_TRX_OFF , Context context) {

        String updatedRecord1 ;



        log(" COMPTEUR_NBR_TRX_OFF ", "---->  COMPTEUR_NBR_TRX_OFF carte : $$$$ " + COMPTEUR_NBR_TRX_OFF);

        updatedRecord1 = prepareRecordLinear(readCard, 31, 35, Utils.padZero(COMPTEUR_NBR_TRX_OFF, 4));
        log("COMPTEUR_NBR_TRX_OFF ", "----> update COMPTEUR_NBR_TRX_OFF   carte :" + updatedRecord1);

        //String [] dat = updatedRecord1.split("90000000000000000000");
        log("update PLF_MAX_PREPAYE ", "----> update PLF_MAX_PREPAYE carte  carte :" + updatedRecord1);


        String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F09", "05", "28",updatedRecord1,context) ;
        log("Rst updInfoPlafdCardPre", "----> updateResult updateInfoPlafondCardPre   carte :" + updateResult3+ " lenght :  "+updateResult3.length());

        return updateResult3.contains("9000");
    }

    /**
     *
     * @param text
     * @return
     */
    public static boolean checkEmptyOrNul(String text){
        if(text!=null){
            return text.isEmpty() || Double.parseDouble(text) == 0;
        }

        return false ;

    }

    public static String updateRecordLinearSize(BankCard mBankCard,UICCpuReader uicCpuReader,String fileLinear,String recordNumber,String len,String data ,int size , Context context ) {


        String selectKeyFile="00A4000002"+fileLinear;
        //byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);
        byte[] bytes = Utils.hexString2byteArray(selectKeyFile);

        log("selectKeyFile"," \"00A4000002\"+fileLinear :"+ Utils.byteArray2hexString(bytes));


        ChipResponse myResponse = new ChipResponse();
        myResponse = executeApduSizeGlobal(mBankCard,uicCpuReader,bytes,bytes.length,size,context);


        String headerP="00DC"+recordNumber+"04"+len;
        //byte[] resultat = Utils.hexStringToByteArray(headerP+data);
        byte[] resultat = Utils.hexString2byteArray(headerP+data);

        log("headerP+data ","headerP+data :"+ Utils.byteArray2hexString(resultat));

        ChipResponse resultatRS = new ChipResponse();
        resultatRS = executeApduSizeSecondGlobal(mBankCard,uicCpuReader,resultat,resultat.length,size,context);


        //byte[] resLastTrans = Utils.hexStringToByteArray(resultatRS.getData());
        byte[] resLastTrans = Utils.hexString2byteArray(resultatRS.getData());

        log("resultat ","resultat update :"+ Utils.byteArray2hexString(resLastTrans));


        log("updateRecord carte  ","----> updateRecordLinear carte resultatRS.getData() :"+resultatRS.getData());



        String	result= Utils.byteArray2hexString(resLastTrans);

        log("updateRecord carte ","----> updateRecordLinear carte :"+result);


        return result ;

            /*if(resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")
                    && resultRS.getStatus().substring(0,4).equalsIgnoreCase("9000")) {
                this.resultat = 0 ;
                return true;
            }

            return false;*/


    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param context
     * @return
     */
    public static String readInfoPlafondCardPre(BankCard mBankCard ,UICCpuReader uicCpuReader, Context context) {

        String plf = readRecordLinear(mBankCard,uicCpuReader,"2F09", "05", "28",context);
        log(" plafond carte ", "----> plafond carte :" + plf);

        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        log(TAG," date" + datepersomm.replaceAll("-", ""));


        String PLF_MIN_PREPAYE = plf.substring(0, 12);
        log(" PLF_MIN_PREPAYE carte ", "----> PLF_MIN_PREPAYE carte :" + PLF_MIN_PREPAYE);

        String PLF_MAX_PREPAYE = plf.substring(12, 24);
        log("PLF_MAX_PREPAYE carte ", "----> PLF_MAX_PREPAYE carte :" + PLF_MAX_PREPAYE);

        String NBR_TRS_OFF = plf.substring(24,28);
        log("NBR_TRS_OFF carte ", "----> NBR_TRS_OFF carte :" + NBR_TRS_OFF);

        String FLAG_MAJ = plf.substring(28, 29);
        log("FLAG_MAJ carte ", "----> FLAG_MAJ carte :" + FLAG_MAJ);

        String FLAG_UNLOCK = plf.substring(29, 30);
        log("FLAG_UNLOCK carte ", "----> FLAG_UNLOCK carte :" + FLAG_UNLOCK);

        String OPTION_CUMUL = plf.substring(30, 31);
        log("OPTION_CUMUL carte ", "----> OPTION_CUMUL carte :" + OPTION_CUMUL);

        String COMPTEUR_NBR_TRX_OFF = plf.substring(31, 35);
        log("COMPTR_NBR_TRX_OFF card", "----> COMPTEUR_NBR_TRX_OFF carte :" + COMPTEUR_NBR_TRX_OFF);

        return plf;
    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param context
     * @return
     */
    public static String readInfoPlafondCardPost(BankCard mBankCard ,UICCpuReader uicCpuReader, Context context) {

        String plf = readRecordLinear(mBankCard,uicCpuReader,"2F07", "02", "32",context);
        log(" plafond carte ", "----> plafond carte :" + plf);

        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        log(TAG," date" + datepersomm.replaceAll("-", ""));


        String PLF_MENSUEL = plf.substring(0, 12);
        log(" PLF_ANNUEL carte ", "----> PLF_MENSUEL carte :" + PLF_MENSUEL);

        String PLF_HEBDO = plf.substring(12, 24);
        log("PLF_MENSUEL carte ", "----> PLF_HEBDO carte :" + PLF_HEBDO);

        String PLF_JOURNALIER = plf.substring(24, 36);
        log("PLF_JOURNALIER carte ", "----> PLF_JOURNALIER carte :" + PLF_JOURNALIER);

        String NBR_TRS_MENSUEL = plf.substring(36, 44);
        log("NBR_TRS_MENSUEL carte ", "----> NBR_TRS_MENSUEL carte :" + NBR_TRS_MENSUEL);

        String NBR_TRS_HEBDO = plf.substring(44, 52);
        log("NBR_TRS_HEBDO carte ", "----> NBR_TRS_HEBDO carte :" + NBR_TRS_HEBDO);

        String NBR_TRS_JOURNALIER = plf.substring(52, 60);
        log("NBR_TRS_JOURNALIER card", "----> NBR_TRS_JOURNALIER carte :" + NBR_TRS_JOURNALIER);

        String dateplfrecord2 = plf.substring(72, 80);
        log("dateplfrecord2 carte ", "----> dateplfrecord2 carte :" + dateplfrecord2);

        return plf;
    }

    /**
     *
     * @param mBankCard
     * @param uicCpuReader
     * @param context
     */
    public static void readInfoCarte(BankCard mBankCard , UICCpuReader uicCpuReader,Context context){

        String infoCarte = readRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28",context);
        SimpleDateFormat sm = new  SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        log(TAG," date"+datepersomm.replaceAll("-", ""));

        String pan       = infoCarte.substring(0, 19);
        log("pan carte ","----> pan carte :"+pan);

        String etatCarte = infoCarte.substring(19,20);
        log("etatCarte carte ","----> etatCarte carte :"+etatCarte);

        String datePerso = infoCarte.substring(20,28);
        log("datePerso carte ","----> datePerso carte :"+datePerso);

        String dateActivation = infoCarte.substring(28,36);
        log("dateActivation carte ","----> dateActivation carte :"+dateActivation);

        String dateopposition = infoCarte.substring(36,44);
        log("dateopposition carte ","----> dateopposition carte :"+dateopposition);

        String motifopposition = infoCarte.substring(44,46);
        log("motifopposition carte ","----> motifopposition carte :"+motifopposition);

        String dateExpiration = infoCarte.substring(46,54);
        log("dateExpiration carte ","----> dateExpiration carte :"+dateExpiration);

        String idclient = infoCarte.substring(54,64);
        log("idclient carte ","----> idclient carte :"+idclient);

        String idORGANISME = infoCarte.substring(64,68);
        log("idORGANISME carte ","----> idORGANISME carte :"+idORGANISME);

        String prePOSTPAYE = infoCarte.substring(68,69); // 1 ou 2
        log("prePOSTPAYE carte ","----> prePOSTPAYE carte :"+prePOSTPAYE);

        String renouvellable = infoCarte.substring(69,70); // 1 ou 2
        log("renouvellable carte ","----> renouvellable carte :"+renouvellable);

        String plafundUNIT = infoCarte.substring(70,71); // Unité de plafond (  1 Dirhams / 2 Litres)
        log("plafundUNIT carte ","----> plafundUNIT carte :"+plafundUNIT);

        String block_CARTE =  infoCarte.substring(71,72); //
        log("block_CARTE carte ","----> block_CARTE carte :"+block_CARTE);

        String code_SAP =  infoCarte.substring(72,77); //
        log("code_SAP carte ","----> code_SAP carte :"+code_SAP);

        String nbr_DISCOUNT =  infoCarte.substring(77,80);
        log("nbr_DISCOUNT carte ","----> nbr_DISCOUNT carte :"+nbr_DISCOUNT);

        String isNFC =  infoCarte.substring(69,70);
        log("isNFC carte ","----> isNFC carte :"+isNFC);

        /*String isDeleted =  infoCarte.substring(81,82);
        log("isDeleted carte ","----> isDeleted carte :"+isDeleted);

        String isOnline =  infoCarte.substring(82,83);
        log("isOnline carte ","----> isOnline carte :"+isOnline);*/

        /* added by altaf and commented above because the default file length is on 80 chars */
        if(infoCarte.length()>80) {
            String isDeleted =  infoCarte.substring(81,82);
            log("isDeleted carte ","----> isDeleted carte :"+isDeleted);

            String isOnline =  infoCarte.substring(82,83);
            log("isOnline carte ","----> isOnline carte :"+isOnline);
        }
        /* addtion end here */
    }



    // PAX ICC FUNCTIONS


    public static void connectPAX(){
        int paxCardDectectTimeout = 10; //added by altaf for pax  card detect timeout

        IccTester.getInstance().light(true);

        detected = IccTester.getInstance().detect((byte) 0) ;


        while (!detected && paxCardDectectTimeout>0/*added by altaf for card detect timeout*/) {
            try {
                Thread.sleep(1000);
                paxCardDectectTimeout--;
                System.out.println("detect time: "+paxCardDectectTimeout);
                detected = IccTester.getInstance().detect((byte) 0);
            } catch (InterruptedException e) {
                log(TAG, e + ExceptionUtils.getStackTrace(e));
                e.printStackTrace();
            }
        }

        if(detected){

            byte[] res = IccTester.getInstance().init((byte) 0);
            if (res == null) {
                log("Test", "init ic card,but no response");
            }
            IccTester.getInstance().autoResp((byte) 0, true);

        }

    }
    public static void connectPAX(int seconds){
        int paxCardDectectTimeout = seconds; //added by altaf for pax  card detect timeout
        if(BuildConfig.DEBUG)
            paxCardDectectTimeout = seconds;

        IccTester.getInstance().light(true);

        detected = IccTester.getInstance().detect((byte) 0) ;


        while (!detected && paxCardDectectTimeout>0/*added by altaf for card detect timeout*/) {
            try {
                Thread.sleep(1000);
                paxCardDectectTimeout--;
                System.out.println("detect time: "+paxCardDectectTimeout);
                detected = IccTester.getInstance().detect((byte) 0);
            } catch (InterruptedException e) {
                log(TAG, e + ExceptionUtils.getStackTrace(e));
                e.printStackTrace();
            }
        }

        if(detected){

            byte[] res = IccTester.getInstance().init((byte) 0);
            if (res == null) {
                log("Test", "init ic card,but no response");
            }
            IccTester.getInstance().autoResp((byte) 0, true);

        }

    }
    public static void disconnectPAX(){
        IccTester.getInstance().close((byte) 0);
        IccTester.getInstance().light(false);
    }


    public static ChipResponse exchangeApduPAX(byte[] data) throws RemoteException {

        boolean b = false;

        //ChipResponse apduResp = null ;

        ChipResponse apduResp = new ChipResponse();

        // 设置iccIsoCommand函数是否自动发送GET RESPONSE指令。
        //IApdu apdu = Packer.getInstance().getApdu();
        //IApdu.IApduReq apduReq = apdu.createReq((byte) 0x00, (byte) 0xa4, (byte) 0x04, (byte) 0x00,
        //                 data, (byte) 0);

        // IApdu.IApduReq apduReq = apdu.createReq((byte) 0x00,(byte) 0xa4, data);

        //byte[] req = apduReq.pack();

        // byte[] req =
        // getObject.getIGLs().getConvert().strToBcd("00A404000E315041592E5359532E444446303100",
        // EPaddingPosition.PADDING_LEFT);
        // new TestLog().logTrue("apduReq"+getObject.getIGLs().getConvert().bcdToStr(apdu.pack(apduReq)));

        //            this.resultat = 0;
        //            this.respdata = new byte[80];
        //            this.resplen = new int[1];
        //            this.retvalue = -1;
        //
        //            this.sn = new byte[16];
        //            this.pes = new int[1];
        //            this.resSN = 0;


        //byte[] isoRes = new byte[80];


        //byte[] record1 = Utils.hexStringToByteArray("00A4040008D1D2D3D4D5D6D7D800");


        byte[] isoRes = IccTester.getInstance().isoCommand((byte) 0, data);

        if (isoRes != null) {
            System.out.print("data : "+ Utils.byteArray2hexString(isoRes));

            log(TAG,"data isoRes : "+ Utils.byteArray2hexString(isoRes));

            String resData = Utils.byteArray2hexString(isoRes) ;
            String resStatut = resData.substring(resData.length() - 4) ;
            // 700071002100000014812020083100000000000000000020220831000000000400012001000000009000
            apduResp.setData(resData);
            apduResp.setStatus(resStatut);

        }

            /*if (isoRes != null) {
                apduResp = apdu.unpack(isoRes);
                String isoStr = null;
                try {
                    isoStr = "isocommand response:" + " Data:" + new String(apduResp.getData(), "iso8859-1")
                            + " Status:" + apduResp.getStatus() + " StatusString:" + apduResp.getStatusString();
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }*/



        return apduResp ;

    }

    public static ChipResponse executeApduSizePAX( byte[] data , Context context) {


        ChipResponse myResponse = new ChipResponse();
        String St = "";

        try {
            //apdufilepinret = exchangeApduLANDI(data,icCpuReader).getAPDURet() ;
            myResponse = exchangeApduPAX(data) ;
        } catch (RemoteException e) {
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            e.printStackTrace();
        }

        return myResponse;
    } // eof executeApdu

    public static CardStaticStructureModel readCardStaticStructureInfo(BankCard mBankCard , UICCpuReader uicCpuReader,Context context){

        String infoCarte = readRecordLinear(mBankCard,uicCpuReader,"2F09", "02", "28",context);
        SimpleDateFormat sm = new  SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        log(TAG," date"+datepersomm.replaceAll("-", ""));

        String cardNumber       = infoCarte.substring(0, 19);

        String cardStatus = infoCarte.substring(19,20);

        String personalCardDate = infoCarte.substring(20,28);

        String activationDate = infoCarte.substring(28,36);

        String blockedDate = infoCarte.substring(36,44);

        String blockedReason = infoCarte.substring(44,46);

        String expiredDate = infoCarte.substring(46,54);
        String expiredDateSplit = infoCarte.substring(46, 50) +
                "-" + infoCarte.substring(50, 52) +
                "-" + infoCarte.substring(52, 54);

        String cleintId = infoCarte.substring(54,64);

        String organizationCardId = infoCarte.substring(64,68);

        String cardType = infoCarte.substring(68,69); // 1 ou 2

        String renouvellable = infoCarte.substring(69,70); // 1 ou 2

        String cardCeilingUnit = infoCarte.substring(70,71); // Unité de plafond (  1 Dirhams / 2 Litres)

        String flagBlocking =  infoCarte.substring(71,72); //

        String cardCode =  infoCarte.substring(72,77); //

        String discountType =  infoCarte.substring(77,80);

        String verificationType =  infoCarte.substring(69,70);

        return new CardStaticStructureModel(activationDate,blockedDate,blockedReason,cardCeilingUnit,cardCode,cardNumber,cardStatus,cardType,
                cleintId,expiredDateSplit,flagBlocking,discountType,organizationCardId,personalCardDate,verificationType);

    }
    public static void Log(String TAG, String message) {
        int maxLogSize = 2000;
        for(int i = 0; i <= message.length() / maxLogSize; i++) {
            int start = i * maxLogSize;
            int end = (i+1) * maxLogSize;
            end = end > message.length() ? message.length() : end;
            Log.d(TAG, message.substring(start, end));
        }
    }
    public static double getCardCeilings(String plafondCarte,int debut, int fin){
        String plf = plafondCarte.substring(debut,fin);
        return Double.parseDouble(plf)/100 ;
    }
    public static int getCardCeilingCount(String plafondCarte ,int debut, int fin){
        String plf = plafondCarte.substring(debut,fin);
        return Integer.valueOf(plf) ;
    }
    public static String getDateLastPLF(String infoCarte){
        String dateLastplf = infoCarte.substring(72,76)+
                "-"+infoCarte.substring(76,78)+
                "-"+infoCarte.substring(78,80);
        return dateLastplf ;
    }
    public static String getCardCeilingsBigDecimal(String plafondCarte,int debut, int fin) {
        String plf = plafondCarte.substring(debut, fin);
        BigDecimal result = BigDecimal.valueOf(Double.parseDouble(plf) / 100);
        return String.format(Locale.US, "%.2f", result);
    }
    public static double getPreCardCeiling(String plafondCarte, int debut, int fin){
        String plf = plafondCarte.substring(debut,fin);
        return Double.parseDouble(plf)/100 ;
    }
    public static String readCardHolderName(BankCard mBankCard , UICCpuReader uicCpuReader, Context context  ) {
        String res4 = readRecordLinear(mBankCard, uicCpuReader,"2F09", "04", "28",context);
        String nomPorteur = res4.substring(0, 60);// valeur en HEX
        return Utils.unHex(nomPorteur).trim() ;

    }
    public static String readCardCeilingLimits(String plf) {
        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String datepersomm = sm.format(date);
        String PLF_MENSUEL = plf.substring(0, 12);
        String COMPTEUR_PLF_HEBDO = plf.substring(12, 24);
        String COMPTEUR_PLF_JOURNALIER = plf.substring(24, 36);
        String COMPTEUR_NBR_TRS_MENSUEL = plf.substring(36, 44);
        String COMPTEUR_NBR_TRS_HEBDO = plf.substring(44, 52);
        String COMPTEUR_NBR_TRS_JOURNALIER = plf.substring(52, 60);
        String COMPTEUR_DATE_PLF = plf.substring(72, 80);

        return plf;
    }
    public static boolean updateInfoPlafondCardCompt(BankCard mBankCard,UICCpuReader uicCpuReader,String readCard,String recordNumber,String plfMensuel,String plfHebdo,String plafondJour,
                                                     String COMPTEUR_NBR_TRS_MENSUEL,String COMPTEUR_NBR_TRS_HEBDO,String COMPTEUR_NBR_TRS_JOURNALIER , Context context) {
        try {
            String updatedRecord1 ;
            String nowDate = /*"**************";*/ Support.Companion.dateToStringX(new Date());
            updatedRecord1 = prepareRecordLinear(readCard, 0, 60, Utils.padZero(plfMensuel, 12)+ Utils.padZero(plfHebdo, 12) + Utils.padZero(plafondJour, 12)+ Utils.padZero(COMPTEUR_NBR_TRS_MENSUEL, 8)+ Utils.padZero(COMPTEUR_NBR_TRS_HEBDO, 8)+ Utils.padZero(COMPTEUR_NBR_TRS_JOURNALIER, 8));
            updatedRecord1 = prepareRecordLinear(updatedRecord1, 72, 80, Utils.padZero(nowDate.substring(0,8), 8));
            String updateResult3 = updateRecordLinear(mBankCard,uicCpuReader,"2F07", recordNumber, "32", updatedRecord1,context);
            return updateResult3.substring(0, 4).equalsIgnoreCase("9000");
        }
        catch (Exception e)
        {
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            e.printStackTrace();
            return false;
        }

    }
    public static boolean updateCarteNomPorteur(String nomPorteur,BankCard mBankCard,UICCpuReader icCpuReader,Context context ) {

        String updatedRecord1 ;

        String res4 = readRecordLinear( mBankCard,icCpuReader,"2F09", "04", "28", context);
        String nom_Porteur = res4.substring(0, 60);// valeur en HEX
        nomPorteur =  Utils.encodeHexString(nomPorteur).toUpperCase();
        updatedRecord1 = prepareRecordLinear(res4, 0, 60,Utils.padZero(nomPorteur,60));
        String updateResult3 = updateRecordLinear(mBankCard,icCpuReader,"2F09", "04", "28",updatedRecord1 ,context) ;
        return updateResult3.contains("9000");
    }
    public static boolean updateRestrictionsCarte(String idprof_GEOG,String idprofil_HOR,String id_PRF_JRS,String rest_ZONE ,String id_ARTICLE,BankCard mBankCard,UICCpuReader icCpuReader,Context context ) {
        String plf = readRecordLinear(mBankCard,icCpuReader,"2F09", "03", "28",context);
        String updatedRecord1 = prepareRecordLinear(plf, 0, 32,Utils.padZero(idprof_GEOG+"",32));
        String updatedRecord2 = prepareRecordLinear(updatedRecord1, 32, 42,Utils.padZero(idprofil_HOR+"",10));
        String updatedRecord3 = prepareRecordLinear(updatedRecord2, 42, 52,Utils.padZero(id_PRF_JRS+"",10));
        String updatedRecord4 = prepareRecordLinear(updatedRecord3, 52, 66,Utils.padZero(rest_ZONE+"",14));
        String updatedRecord5 = prepareRecordLinear(updatedRecord4, 66, 80,Utils.padZero(id_ARTICLE+"",14));
        String updateResult3 = updateRecordLinear(mBankCard,icCpuReader,"2F09", "03", "28",updatedRecord5,context) ;
        return updateResult3.contains("9000");
    }
     public static void log(String tag,String msg)
    {
        Log.i(tag,msg);
        LogWriter logWriter=new LogWriter(MainApp.Companion.getFleetCardLogName());
        logWriter.appendLog(tag, msg);
    }
    public static boolean updateInfoPlafondCardPost(String readCard,String plfMensuel,String plfHebdo,String plafondJour,
                                             String NBR_TRS_MENSUEL,String NBR_TRS_HEBDO,String NBR_TRS_JOURNALIER,BankCard mBankCard,UICCpuReader icCpuReader,Context context ) {

        String updatedRecord1 ;
        SimpleDateFormat sm = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        String dateplfrecord2 = sm.format(date).replaceAll("-", "");
        
        updatedRecord1 =prepareRecordLinear(readCard, 0, 12, Utils.padZero(plfMensuel, 12));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 12, 24, Utils.padZero(plfHebdo, 12));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 24, 36, Utils.padZero(plafondJour, 12));
        
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 36, 44, Utils.padZero(NBR_TRS_MENSUEL, 8));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 44, 52, Utils.padZero(NBR_TRS_HEBDO, 8));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 52, 60, Utils.padZero(NBR_TRS_JOURNALIER, 8));
        String updateResult3 =updateRecordLinear(mBankCard,icCpuReader,"2F07", "02", "32", updatedRecord1,context);

        return updateResult3.contains("9000");
    }
    public static boolean updateCardCounts(String readCard, String NBR_TRS_MENSUEL,String NBR_TRS_HEBDO,String NBR_TRS_JOURNALIER,BankCard mBankCard,UICCpuReader icCpuReader,Context context ) {

        String updatedRecord1 ;
        updatedRecord1 =prepareRecordLinear(readCard, 36, 44, Utils.padZero(NBR_TRS_MENSUEL, 8));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 44, 52, Utils.padZero(NBR_TRS_HEBDO, 8));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 52, 60, Utils.padZero(NBR_TRS_JOURNALIER, 8));
        String updateResult3 =updateRecordLinear(mBankCard,icCpuReader,"2F07", "02", "32", updatedRecord1,context);
        return updateResult3.contains("9000");
    }
    public static boolean updateInfoPlafondCardPre(String readCard,String PLF_MIN_PREPAYE,String PLF_MAX_PREPAYE,String NBR_TRS_OFF,String FLAG_MAJ,
                                            String FLAG_UNLOCK,String OPTION_CUMUL,String discountID,BankCard mBankCard,UICCpuReader icCpuReader,Context context) {

        String updatedRecord1 ;
        
        updatedRecord1 =prepareRecordLinear(readCard, 0, 12, Utils.padZero(PLF_MIN_PREPAYE, 12));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 12, 24, Utils.padZero(PLF_MAX_PREPAYE, 12));

        updatedRecord1 =prepareRecordLinear(updatedRecord1, 24, 28, Utils.padZero(NBR_TRS_OFF, 4));

        updatedRecord1 =prepareRecordLinear(updatedRecord1, 28, 29, Utils.padZero(FLAG_MAJ, 1));

        updatedRecord1 =prepareRecordLinear(updatedRecord1, 29, 30, Utils.padZero(FLAG_UNLOCK, 1));

        updatedRecord1 =prepareRecordLinear(updatedRecord1, 30, 31, Utils.padZero(OPTION_CUMUL, 1));
        updatedRecord1 =prepareRecordLinear(updatedRecord1, 35, 45, Utils.padZero(discountID, 10));

        String updateResult3 =updateRecordLinear(mBankCard,icCpuReader,"2F09", "05", "28", updatedRecord1,context) ;
        String readCardRecord = readRecordLinear(mBankCard,icCpuReader,"2F09", "05", "28",context);
        log(TAG,"Updated Record Data ::: "+readCardRecord);
        log(TAG,"Updated Discount ID :: "+getDiscountID(mBankCard,icCpuReader,context));
        return updateResult3.contains("9000");
    }

    public static int getDiscountID(BankCard mBankCard , UICCpuReader uicCpuReader, Context context  ) {
        String res4 = readRecordLinear(mBankCard, uicCpuReader,"2F09", "05", "28",context);
        log(TAG,"readCard :: "+res4);
        String discountID = res4.substring(35, 45);
        return Integer.parseInt(discountID);
    }

    public static boolean modifyPIN( String newPIN,BankCard mBankCard ,UICCpuReader uicCpuReader,Context context) {
        try {

            ChipResponse myResponse = new ChipResponse();
            ChipResponse myResponse1 = new ChipResponse();
            String selectKeyFile="00A40000022F01";
            String headerP="00CF000008";
            String paddF="FFFFFFFF";
            String PinEncoded =  Utils.encodeHexString(newPIN);
            byte[] bytes = Utils.hexStringToByteArray(selectKeyFile);

            myResponse = executeApduGlobal(mBankCard,uicCpuReader,bytes,bytes.length,context);
            byte[] bytesHeader = Utils.hexStringToByteArray(headerP+PinEncoded+paddF);

            myResponse1 = executeApduGlobal(mBankCard,uicCpuReader,bytesHeader,bytesHeader.length,context);
            return myResponse.getStatus().substring(0, 4).equalsIgnoreCase("9000")
                    && myResponse1.getStatus().substring(0, 4).equalsIgnoreCase("9000");
        }
        catch (Exception e)
        {
            log(TAG, e + ExceptionUtils.getStackTrace(e));
            e.printStackTrace();
            return false;
        }
    }

}
