package app.rht.petrolcard.utils.tax

import app.rht.petrolcard.ui.ticket.activity.format

class TaxUtils {
    companion object{
        /*@JvmStatic*/ fun calculate(amount: Double, taxPercentile:Double, isInclusive: Boolean) : TaxModel {

            var taxAmount = 0.0
            var netAmount = 0.0
            var totalAmount = 0.0

            var vatType = if(isInclusive) 0 else 1

            if(isInclusive){
                // Tax amount = Value inclusive of tax X tax rate ÷ (100 + tax rate)
                // VAT amount = 50,000*5/105 = AED 2,381
                taxAmount = (amount*taxPercentile)/(100.0+taxPercentile)
                netAmount = (amount - taxAmount)
                totalAmount = amount
            }
            else {
                val vatPer = (taxPercentile / 100.0)
                taxAmount = (amount*vatPer)
                netAmount = amount
                totalAmount = (netAmount+taxAmount)
            }
            return TaxModel(taxType = isInclusive, taxPercentile = taxPercentile, taxAmount = taxAmount.format(3), netAmount = netAmount/*.format(3)*/, totalAmount = totalAmount/*.format(2)*/)
        }

        /*private fun calculateVat() {
            if(intentExtrasModel!!.workFlowTransaction == Workflow.TAXI_FUEL && fuelVat.enabled) {
                val vatPercentage = fuelVat.percentage!!
                if(fuelVat.type == VatType.INCLUSIVE){
                    // Tax amount = Value inclusive of tax X tax rate ÷ (100 + tax rate)
                    // VAT amount = 50,000*5/105 = AED 2,381
                    val vatAmount = (mTransaction!!.amount!!*vatPercentage)/(100.0+vatPercentage)
                    mTransaction!!.vatAmount = vatAmount.format(2).toString()
                    mTransaction!!.netAmount = (mTransaction!!.amount!! - vatAmount).format(2).toString()
                }
                else if(fuelVat.type == VatType.EXCLUSIVE){
                    val vatPer = (vatPercentage / 100.0)
                    val vatAmount = (mTransaction!!.amount!!*vatPer)
                    mTransaction!!.vatAmount = vatAmount.format(2).toString()
                    mTransaction!!.netAmount = mTransaction!!.amount!!.format(2).toString()
                    mTransaction!!.amount = (mTransaction!!.netAmount!!.toDouble()+vatAmount).format(2)
                }
            }
            else if((intentExtrasModel!!.workFlowTransaction == Workflow.SHOP_PRODUCTS || intentExtrasModel!!.workFlowTransaction == Workflow.OTHER_PRODUCTS) && shopVat.enabled){
                val vatPercentage = shopVat.percentage!!
                if(shopVat.type == VatType.INCLUSIVE){
                    // Tax amount = Value inclusive of tax X tax rate ÷ (100 + tax rate)
                    // VAT amount = 50,000*5/105 = AED 2,381
                    val vatAmount = (mTransaction!!.amount!!*vatPercentage)/(100.0+vatPercentage)
                    mTransaction!!.vatAmount = vatAmount.format(2).toString()
                    mTransaction!!.netAmount = (mTransaction!!.amount!! - vatAmount).format(2).toString()
                }
                else if(shopVat.type == VatType.EXCLUSIVE){
                    val vatPer = (vatPercentage / 100.0)
                    val vatAmount = mTransaction!!.amount!! * vatPer
                    mTransaction!!.vatAmount = vatAmount.format(2).toString()
                    mTransaction!!.netAmount = mTransaction!!.amount!!.toString()
                    mTransaction!!.amount = (mTransaction!!.netAmount!!.toDouble()+vatAmount).format(2)
                }
            }
        }*/
    }
}