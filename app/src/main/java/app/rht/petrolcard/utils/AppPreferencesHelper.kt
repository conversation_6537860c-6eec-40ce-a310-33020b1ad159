package app.rht.petrolcard.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.baseClasses.model.FuelPump
import app.rht.petrolcard.ui.common.model.IntentExtrasModel
import app.rht.petrolcard.ui.common.model.TransactionStepLog
import app.rht.petrolcard.ui.menu.model.MenuCategoryModel
import app.rht.petrolcard.ui.nfc.model.NfcTagModel
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.startup.model.PreferenceModel
import app.rht.petrolcard.ui.transactionlist.model.ProductDSP
import app.rht.petrolcard.ui.transactionlist.model.TransactionFromFcc
import app.rht.petrolcard.utils.constant.AppConstant
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken


class AppPreferencesHelper

internal constructor(val mPrefs: SharedPreferences) {
    inline fun <reified T> Gson.fromJson(json: String) =
        this.fromJson<T>(json, object : TypeToken<T>() {}.type)

    private val TAG = AppPreferencesHelper::class.simpleName

    companion object {
        private const val KEY_PREFERENCE_MODEL = "preference_model"
        private const val KEY_RESTRICTION_HOURS = "restriction_hours"
        private const val KEY_MENU_CATEGORIES_LIST = "menu_category_list"
        private const val KEY_STATION_MODEL = "station_model"
        private const val KEY_FUSION_MODEL = "fusion_model"
        private const val KEY_FUEL_POS_MODEL = "fuel_pos_model"
        private const val KEY_SERVER_MODEL = "server_model"

        const val APP_LANGUAGE = "APP_LANGUAGE"
        const val ENGLISH = "en"
        const val FRENCH = "fr"
    }

    var authToken: String
        get() = mPrefs.getString("USER-AUTH-TOKEN", "").toString()
        set(value) = mPrefs.edit().putString("USER-AUTH-TOKEN", value).apply()
    var timeZone: String
        get() = mPrefs.getString("TIME_ZONE", " ").toString()
        set(value) = mPrefs.edit().putString("TIME_ZONE", value).apply()
    var isLogin: Boolean
        get() = mPrefs.getBoolean("USER-IS_LOGIN", false)
        set(value) = mPrefs.edit().putBoolean("USER-IS_LOGIN", value).apply()
    var isFirstTimeLoadingApp: Boolean
        get() = mPrefs.getBoolean("USER-IS_FIRST_TIME_LOADING", true)
        set(value) = mPrefs.edit().putBoolean("USER-IS_FIRST_TIME_LOADING", value).apply()
    var isReferenceLoad: Boolean
        get() = mPrefs.getBoolean("IS-REFERENCER-LOAD", true)
        set(value) = mPrefs.edit().putBoolean("IS-REFERENCER-LOAD", value).apply()
    var navigationStatus: Boolean
        get() = mPrefs.getBoolean("NAVIGATION-STATUS", true)
        set(value) = mPrefs.edit().putBoolean("NAVIGATION-STATUS", value).apply()
    var selectedLanguage: String?
        get() = mPrefs.getString(AppConstant.LANG, "en")
        set(value) = mPrefs.edit().putString(AppConstant.LANG, value).apply()
    var isUseSdCard: Boolean
        get() = mPrefs.getBoolean(AppConstant.USE_SD_CARD, false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.USE_SD_CARD, value).apply()
    var fireBaseToken: String
        get() = mPrefs.getString("FIREBASE-AUTH-TOKEN", "").toString()
        set(value) = mPrefs.edit().putString("FIREBASE-AUTH-TOKEN", value).apply()
    var baseUrl: String
        get() = mPrefs.getString("APP-BASE-URL", "").toString()
        set(value) = mPrefs.edit().putString("APP-BASE-URL", value).apply()
    var logReferenceNo: String
        get() = mPrefs.getString("LOG_REFERENCE_NO", "").toString()
        set(value) = mPrefs.edit().putString("LOG_REFERENCE_NO", value).apply()
    var logCount: Long
        get() = mPrefs.getLong("LOG_COUNT",1).toLong()
        set(value) = mPrefs.edit().putLong("LOG_COUNT", value).apply()
    /*var badge: String
        get() = mPrefs.getString("BADGE-NUMBER", "").toString()
        set(value) = mPrefs.edit().putString("BADGE-NUMBER", value).apply()*/

    var currency: String
        get() = mPrefs.getString(AppConstant.MODE_CURRENCY, "").toString()
        set(value) = mPrefs.edit().putString(AppConstant.MODE_CURRENCY, " $value").apply()

    var blackListVersion: Int
        get() = mPrefs.getInt(AppConstant.BLACK_LIST_VERSION,0)
        set(value) = mPrefs.edit().putInt(AppConstant.BLACK_LIST_VERSION, value).apply()

    var greyListVersion: Int
        get() = mPrefs.getInt(AppConstant.GREY_LIST_VERSION,0)
        set(value) = mPrefs.edit().putInt(AppConstant.GREY_LIST_VERSION, value).apply()

    var isFirstTime: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_FIRST_TIME,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_FIRST_TIME, value).apply()
    var isKeyImported: Boolean
        get() = mPrefs.getBoolean("IS_KEY_IMPORTED", false)
        set(value) = mPrefs.edit().putBoolean("IS_KEY_IMPORTED", value).apply()
    var isNightMode: Boolean
        get() = mPrefs.getBoolean("isNightMode", false)
        set(value) = mPrefs.edit().putBoolean("isNightMode", value).apply()

    var logoPath: String?
        get() = mPrefs.getString(AppConstant.LOGO_PATH,"")
        set(value) = mPrefs.edit().putString(AppConstant.LOGO_PATH, value).apply()
    var isCurrentActivityisMenu: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_CURRENT_ACTIVITY_IS_MENU,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_CURRENT_ACTIVITY_IS_MENU, value).apply()

    var isRestartApplication: String
        get() = mPrefs.getString(AppConstant.IS_RESTART_APPLICATION, "false").toString()
        set(value) = mPrefs.edit().putString(AppConstant.IS_RESTART_APPLICATION, value).apply()

    var transactionCount: Int
        get() = mPrefs.getInt(AppConstant.TRANSACTION_COUNT,0)
        set(value) = mPrefs.edit().putInt(AppConstant.TRANSACTION_COUNT, value).apply()

    var isSignInBackground: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_SIGN_IN_BACKROUND,true)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_SIGN_IN_BACKROUND, value).apply()
    var isTimsStarted: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_TIMS_STARTED,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_TIMS_STARTED, value).apply()
    var isAppUpdateAvailable: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_APP_UPDATE_AVAILABLE,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_APP_UPDATE_AVAILABLE, value).apply()
    var isTimsServerInstalled: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_TIMS_SERVER_INSTALLED,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_TIMS_SERVER_INSTALLED, value).apply()
    var mCurrentActivity: String?
        get() = mPrefs.getString(AppConstant.CURRENT_ACTIVITY_PREFS,"")
        set(value) = mPrefs.edit().putString(AppConstant.CURRENT_ACTIVITY_PREFS, value).apply()
    var isSendLogs: Int
        get() = mPrefs.getInt("LOG-FILE-PREF",0)
        set(value) = mPrefs.edit().putInt("LOG-FILE-PREF", value).apply()
    fun savePreferenceModel(pModel: PreferenceModel) {
        val edit = mPrefs.edit()
        edit.putString(KEY_PREFERENCE_MODEL, Gson().toJson(pModel))
        edit.apply()
    }

    fun getPreferenceModel(): PreferenceModel? {
        val pModel = mPrefs.getString(KEY_PREFERENCE_MODEL, null) ?: return null
        return Gson().fromJson<PreferenceModel>(pModel)
    }

    fun saveStationModel(sModel: StationModel) {
        Log.i(TAG, "Staving station model $sModel")
        val edit = mPrefs.edit()
        edit.putString(KEY_STATION_MODEL, Gson().toJson(sModel))
        edit.apply()
    }

    fun getStationModel(): StationModel? {
        val sModel = mPrefs.getString(KEY_STATION_MODEL, null) ?: return null
        return Gson().fromJson<StationModel>(sModel)
    }

    fun saveFusionModel(sModel: FusionModel) {
        val edit = mPrefs.edit()
        edit.putString(KEY_FUSION_MODEL, Gson().toJson(sModel))
        edit.apply()
    }

    fun getFusionModel(): FusionModel? {
        val pModel = mPrefs.getString(KEY_FUSION_MODEL, null) ?: return null
        return Gson().fromJson<FusionModel>(pModel)
    }

    fun saveFuelPosModel(pModel: FuelPOSModel) {
        val edit = mPrefs.edit()
        edit.putString(KEY_FUEL_POS_MODEL, Gson().toJson(pModel))
        edit.apply()
    }

    fun getFuelPosModel(): FuelPOSModel? {
        val pModel = mPrefs.getString(KEY_FUEL_POS_MODEL, null) ?: return null
        return Gson().fromJson<FuelPOSModel>(pModel)
    }

    fun saveJsonData(jsonData: String, keyName: String) {
        val edit = mPrefs.edit()
        edit.putString(keyName, jsonData)
        edit.apply()
    }

    fun getMenuCategoriesListModel(): ArrayList<MenuCategoryModel>? {
        val pModel = mPrefs.getString(KEY_MENU_CATEGORIES_LIST, null) ?: return null
        return Gson().fromJson<ArrayList<MenuCategoryModel>>(pModel)
    }
    fun saveMenuCategoryList(categoriesJson: String) {
        val edit = mPrefs.edit()
        edit.putString(KEY_MENU_CATEGORIES_LIST, categoriesJson)
        edit.apply()
    }

    fun getJsonData(jsonData: String, keyName: String) {
        val edit = mPrefs.edit()
        edit.putString(keyName, jsonData)
        edit.apply()
    }

    fun loadIntegerSharedPrefences(ctx: Context, key: String?): Int {
        val sharedPreferences = ctx.getSharedPreferences("shared preferences", Context.MODE_PRIVATE)
        return sharedPreferences.getInt(key, 1)
    }

    fun loadStringSharedPrefences(ctx: Context, key: String?): String? {
        val sharedPreferences = ctx.getSharedPreferences("shared preferences", Context.MODE_PRIVATE)
        return sharedPreferences.getString(key,null)
    }

    fun getSN(): String? {
        return MainApp.sn
    }

    fun saveStringSharedPreferences(keyName: String?, textData: String?) {
        if (textData != null) {
            mPrefs.edit().putString(keyName, textData).apply()
        }
    }

    fun getStringSharedPreferences(keyName: String?): String {
        return mPrefs.getString(keyName!!, "").toString()
    }

    fun saveIntegerSharedPreferences(keyName: String?, textData: Int?) {
        if (textData != null) {
            mPrefs.edit().putInt(keyName, textData).apply()
        }
    }

    fun getIntegerSharedPreferences(keyName: String?): Int {
        return mPrefs.getInt(keyName!!, 1)
    }

    fun saveBooleanSharedPreferences(keyName: String?, textData: Boolean?) {
        if (textData != null) {
            mPrefs.edit().putBoolean(keyName, textData).apply()
        }
    }

    fun getBooleanSharedPreferences(keyName: String?): Boolean {
        return mPrefs.getBoolean(keyName!!, false)
    }

    fun saveFloatSharedPreferences(keyName: String?, textData: Float?) {
        if (textData != null) {
            mPrefs.edit().putFloat(keyName, textData).apply()
        }
    }

    fun getFloatSharedPreferences(keyName: String?): Float {
        return mPrefs.getFloat(keyName!!, 0F)
    }

    fun saveServerModel(serverModel: ServerModel) {
        val edit = mPrefs.edit()
        edit.putString(KEY_SERVER_MODEL, Gson().toJson(serverModel))
        edit.apply()
    }

    fun getServerModel(): ServerModel? {
        val serverModel = mPrefs.getString(KEY_SERVER_MODEL, null) ?: return null
        return Gson().fromJson<ServerModel>(serverModel)
    }
    fun saveTransactionModel(transactionFromFcc: TransactionFromFcc?) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("FUEL-TRANSACTION-STATUS-MODEL", Gson().toJson(transactionFromFcc))
        edit.apply()
    }

    fun getTransactionModel(): TransactionFromFcc? {
        val transaction: String = mPrefs.getString("FUEL-TRANSACTION-STATUS-MODEL", null)!!
        return Gson().fromJson(transaction, TransactionFromFcc::class.java)
    }
    fun saveVerificationTagList(nfctags: ArrayList<NfcTagModel>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("VERIFICATION-TAG-LIST-MODEL", Gson().toJson(nfctags))
        edit.apply()
    }
    fun getVerificationTagList(): ArrayList<NfcTagModel> {
        val tagList: String = mPrefs.getString("VERIFICATION-TAG-LIST-MODEL", null)!!
        val type = object : TypeToken<ArrayList<NfcTagModel?>?>() {}.type
        return Gson().fromJson(tagList, type)
    }

   fun saveProductTable(productTable: String?) {
            val edit: SharedPreferences.Editor = mPrefs.edit()
            edit.putString("PRODUCT-TABLE", productTable)
            edit.apply()
   }

   fun getProductTable(): String? {
            return mPrefs.getString("PRODUCT-TABLE", "")
   }

    fun saveMasterTerminalIp(ip: String) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.MASTER_TERMINAL_IP, ip)
        edit.apply()
    }

    fun getMasterTerminalIp(): String {
        return mPrefs.getString(AppConstant.MASTER_TERMINAL_IP, "")!!
    }

    fun saveMasterTerminalPort(port: Int) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putInt(AppConstant.MASTER_TERMINAL_PORT, port)
        edit.apply()
    }

    fun getMasterTerminalPort(): Int {
        return mPrefs.getInt(AppConstant.MASTER_TERMINAL_PORT,9876)
    }
    fun saveProductColors(arrayList: ArrayList<ProductDSP>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.PREFERENCE_PRODUCT_COLORS, Gson().toJson(arrayList))
        edit.apply()
    }
    fun getProductColors(): ArrayList<ProductDSP>? {
        val arrayList: String = mPrefs.getString(AppConstant.PREFERENCE_PRODUCT_COLORS, null)!!
        val type = object : TypeToken<ArrayList<ProductDSP?>?>() {}.type
        return Gson().fromJson(arrayList, type)
    }
    fun saveFuelProductList(arrayList: ArrayList<SubProduct>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.PREFERENCE_PRODUCT_LIST, Gson().toJson(arrayList))
        edit.apply()
    }

    fun getFuelProductList(): ArrayList<SubProduct>? {
        val arrayList: String = mPrefs.getString(AppConstant.PREFERENCE_PRODUCT_LIST, null)!!
        val type = object : TypeToken<ArrayList<SubProduct>>() {}.type
        return Gson().fromJson(arrayList, type)
    }

    fun saveReferenceModel(referenceModel: ReferenceModel) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("REFERENCE-MODEL", Gson().toJson(referenceModel))
        edit.apply()
    }

    fun getReferenceModel(): ReferenceModel? {
        val model: String? = mPrefs.getString("REFERENCE-MODEL", null)
        return if(model!=null) Gson().fromJson(model, ReferenceModel::class.java) else null
    }
    fun saveApiEndPoints(apiEndpointsModel: ApiEndPointsModel) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("ApiEndPointsModel", Gson().toJson(apiEndpointsModel))
        edit.apply()
    }

    fun getApiEndpoints(): ApiEndPointsModel? {
        val model: String? = mPrefs.getString("ApiEndPointsModel", null)
        return if(model!=null) Gson().fromJson(model, ApiEndPointsModel::class.java) else null
    }


    fun saveManagerBadges(badges: ArrayList<ManagerBadge>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("MANAGER-BADGE-MODEL", Gson().toJson(badges))
        edit.apply()
    }

    fun getManagerBadges(): ArrayList<ManagerBadge> {
        val arrayList: String = mPrefs.getString("MANAGER-BADGE-MODEL", null)!!
        val type = object : TypeToken<ArrayList<ManagerBadge?>?>() {}.type
        return Gson().fromJson(arrayList, type)
    }


    var remCardMonthlyLimit: Double
        get() = if(mPrefs.getString("remCardMonthlyLimit","0")!=null) mPrefs.getString("remCardMonthlyLimit","0")!!.toDouble() else "0".toDouble()
        set(value) = mPrefs.edit().putString("remCardMonthlyLimit", value.toString()).apply()

    var remCardWeeklyLimit: Double
        get() = if(mPrefs.getString("remCardWeeklyLimit","0")!=null) mPrefs.getString("remCardWeeklyLimit","0")!!.toDouble() else "0".toDouble()
        set(value) = mPrefs.edit().putString("remCardWeeklyLimit", value.toString()).apply()

    var remCardDailyLimit: Double
        get() = if(mPrefs.getString("remCardDailyLimit","0")!=null) mPrefs.getString("remCardDailyLimit","0")!!.toDouble() else "0".toDouble()
        set(value) = mPrefs.edit().putString("remCardDailyLimit", value.toString()).apply()

    var remCardMonthlyCount: Int
        get() = mPrefs.getInt("remCardMonthlyCount",0)
        set(value) = mPrefs.edit().putInt("remCardMonthlyCount", value).apply()

    var remCardWeeklyCount: Int
        get() = mPrefs.getInt("remCardWeeklyCount",0)
        set(value) = mPrefs.edit().putInt("remCardWeeklyCount", value).apply()

    var remCardDailyCount: Int
        get() = mPrefs.getInt("remCardDailyCount",0)
        set(value) = mPrefs.edit().putInt("remCardDailyCount", value).apply()

    var remCardCeiling : String?
        get() = mPrefs.getString("remCardCeiling","")
        set(value) = mPrefs.edit().putString("remCardCeiling", value).apply()
    fun isAttendantMode() : Boolean {
        return try{
            MainApp.getPrefs().getReferenceModel()!!.TERMINAL_TYPE == AppConstant.UN_ATTENDANT_MODE
        } catch (e:Exception) {
            e.printStackTrace()
            false
        }
    }

    fun saveBlackListData(data :ArrayList<BlackListModel>){

    }

    fun getPompisteId(code :String):String{
        var id = "0"
        val item = MainApp.getPrefs().getReferenceModel()
        if(item!=null){
            val list = item.pompiste
            if(!list.isNullOrEmpty()) {
                for(attendant in list){
                    if(attendant.codepompiste == code || attendant.tag.equals(code)){
                        id = attendant.id.toString()
                    }
                }
            }
        }
        return id
    }

    fun getDecimal():Int{
        val referenceModel = getReferenceModel()
        return referenceModel!!.FUELPOS.decimal
    }


    fun savePumpModel(pumpsModel: PumpsModel?) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("SELECTED-PUMP-MODEL", Gson().toJson(pumpsModel))
        edit.apply()
    }
    fun getPumpModel(): PumpsModel? {
        val model: String? = mPrefs.getString("SELECTED-PUMP-MODEL", null)
        return Gson().fromJson(model, PumpsModel::class.java)
    }

    var isPumpError: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_PUMP_ERROR,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_PUMP_ERROR, value).apply()

    var isPowerCutGetFuelSaleTrxMsgSent: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_POWERCUT_GETFUELSALETRXMSGSENT,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_POWERCUT_GETFUELSALETRXMSGSENT, value).apply()

    var isDeleteLogSuccessTransaction: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_DELETE_LOG_SUCCESS_TRANSACTION,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_DELETE_LOG_SUCCESS_TRANSACTION, value).apply()

    var logDeleteScheduleHour: Long
        get() = mPrefs.getLong(AppConstant.LOG_DELETE_SCHEDULE_HOUR,76)
        set(value) = mPrefs.edit().putLong(AppConstant.LOG_DELETE_SCHEDULE_HOUR, value).apply()

    var isTransactionCreated: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_TRANSACTION_CREATED,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_TRANSACTION_CREATED, value).apply()

    var isPaymentDone: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_PAYMENT_DONE,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_PAYMENT_DONE, value).apply()

    var isPaymentMethodClicked: Boolean
        get() = mPrefs.getBoolean(AppConstant.IS_PAYMENT_METHOD_CLICKED,false)
        set(value) = mPrefs.edit().putBoolean(AppConstant.IS_PAYMENT_METHOD_CLICKED, value).apply()

    fun saveNozzleModel(nozzleModel: NozzelsModel?) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("SELECTED-NOZZLE-MODEL", Gson().toJson(nozzleModel))
        edit.apply()
    }
    fun getNozzleModel(): NozzelsModel? {
        val model: String? = mPrefs.getString("SELECTED-NOZZLE-MODEL", null)
        return Gson().fromJson(model, NozzelsModel::class.java)
    }

    fun saveIntentModel(intentModel: IntentExtrasModel?) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("INTENT-MODEL", Gson().toJson(intentModel))
        edit.apply()
    }
    fun getIntentModel(): IntentExtrasModel? {
        val model: String? = mPrefs.getString("INTENT-MODEL", null)
        return Gson().fromJson(model, IntentExtrasModel::class.java)
    }

    fun saveFuellingStates(fuellingStates: Array<Boolean>?) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        val states = "fuellingState"
        var values = ""
        if(fuellingStates!=null){
            for (i in fuellingStates.indices)
            {
                values += if(fuellingStates[i]) "1" else "0"
                values +=":"
            }
        }
        edit.putString(states, values)
        edit.apply()
    }
    fun getFuellingStates(): Array<Boolean> {
        val states = "fuellingState"
        val values = mPrefs.getString(states, "")
        var fuelingStates = arrayOf(false, false, false, false, false)
        if(!values.isNullOrEmpty()){
            val items = values.split(":")
            for(i in 0..4 ){
                fuelingStates[i] = items[i] == "1"
            }
        }
        return fuelingStates
    }

    fun saveNozzleClicked(clicked: Boolean) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putBoolean("nozzleClicked", clicked)
        edit.apply()
    }
    fun getNozzleClicked(): Boolean {
        return mPrefs.getBoolean("nozzleClicked", false)
    }
    fun saveDiscountDetails(arrayList: ArrayList<DiscountDetailModel>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.PREFERENCE_DISCOUNT_DETAILS, Gson().toJson(arrayList))
        edit.apply()
    }

    fun getDiscountDetails(): ArrayList<DiscountDetailModel>? {
        val arrayList: String = mPrefs.getString(AppConstant.PREFERENCE_DISCOUNT_DETAILS, ArrayList<DiscountDetailModel>().toString())!!
        val type = object : TypeToken<ArrayList<DiscountDetailModel>>() {}.type
        return Gson().fromJson(arrayList, type)
    }

    fun saveFuelProductPrices(arrayList: ArrayList<FuelPump>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString("FUEL_PRODUCT_PRICES", Gson().toJson(arrayList))
        edit.apply()
    }

    fun getFuelProductPrices(): ArrayList<FuelPump>? {
        val arrayList: String = mPrefs.getString("FUEL_PRODUCT_PRICES", ArrayList<FuelPump>().toString())!!
        val type = object : TypeToken<ArrayList<FuelPump>>() {}.type
        return Gson().fromJson(arrayList, type)
    }
    fun saveAuditLog(arrayList: TransactionStepLog) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.PREFERENCE_AUDIT_LOGS, Gson().toJson(arrayList))
        edit.apply()
    }

    fun getAuditLog(): TransactionStepLog? {
        val model: String? = mPrefs.getString(AppConstant.PREFERENCE_AUDIT_LOGS, null)
        return Gson().fromJson(model, TransactionStepLog::class.java)
    }

    /*fun saveCrashLogEmailConfig(config: CrashLogEmailConfig?) {
        if(config!=null){
            val edit: SharedPreferences.Editor = mPrefs.edit()
            edit.putString(AppConstant.LOG_EMAIL_CONFIG, Gson().toJson(config))
            edit.apply()
        }
    }

    fun getCrashLogEmailConfig(): CrashLogEmailConfig? {
        val model: String? = mPrefs.getString(AppConstant.LOG_EMAIL_CONFIG, null)
        return Gson().fromJson(model, CrashLogEmailConfig::class.java)
    }*/
    fun saveMtnPayCredentials(arrayList: MtnPayCredentials) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.PREFERENCE_MTN_PAY_CREDENTIALS, Gson().toJson(arrayList))
        edit.apply()
    }

    fun getMtnPayCredentials(): MtnPayCredentials? {
        val model: String? = mPrefs.getString(AppConstant.PREFERENCE_MTN_PAY_CREDENTIALS, MtnPayCredentials().toString())
        return Gson().fromJson(model, MtnPayCredentials::class.java)
    }

    fun saveGreyList(arrayList: ArrayList<GreyListModel>) {
        val edit: SharedPreferences.Editor = mPrefs.edit()
        edit.putString(AppConstant.PREFERENCE_GREYLIST_MODEL, Gson().toJson(arrayList))
        edit.apply()
    }

    fun getGreyList(): ArrayList<GreyListModel>? {
        val arrayList: String = mPrefs.getString(AppConstant.PREFERENCE_GREYLIST_MODEL, ArrayList<GreyListModel>().toString())!!
        val type = object : TypeToken<ArrayList<GreyListModel>>() {}.type
        return Gson().fromJson(arrayList, type)
    }

    var backupDbKey : String
        get() = mPrefs.getString("backupDbKey","")?:""
        set(value) = mPrefs.edit().putString("backupDbKey", value).apply()
}
