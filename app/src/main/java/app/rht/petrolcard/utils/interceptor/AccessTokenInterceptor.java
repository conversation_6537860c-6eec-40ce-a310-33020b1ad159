package app.rht.petrolcard.utils.interceptor;

import android.util.Base64;

import androidx.annotation.NonNull;

import java.io.IOException;

import app.rht.petrolcard.BuildConfig;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

public class AccessTokenInterceptor implements Interceptor {
    private final String consumer_key;
    private final String consumer_secret;
    public AccessTokenInterceptor(String mconsumer_key,String mconsumer_secret) {
        consumer_key=mconsumer_key;
        consumer_secret=mconsumer_secret;
    }

    @Override
    public Response intercept(@NonNull Chain chain) throws IOException {

        String keys = consumer_key + ":" + consumer_secret;

        Request request = chain.request().newBuilder()
                .addHeader("Authorization", "Basic " + Base64.encodeToString(keys.getBytes(), Base64.NO_WRAP))
                .build();
        return chain.proceed(request);
    }
}