package app.rht.petrolcard.utils.tims;
public class DailyAmountsByVATRes {
   /**
    *Up to 15 symbols for the accumulated VAT in group A
    */
    public Double SaleVATGrA;
    public Double getSaleVATGrA() {
       return SaleVATGrA;
    }
    protected void setSaleVATGrA(Double value) {
       SaleVATGrA = value;
    }

   /**
    *Up to 15 symbols for the accumulated VAT in group B
    */
    public Double SaleVATGrB;
    public Double getSaleVATGrB() {
       return SaleVATGrB;
    }
    protected void setSaleVATGrB(Double value) {
       SaleVATGrB = value;
    }

   /**
    *Up to 15 symbols for the accumulated VAT in group C
    */
    public Double SaleVATGrC;
    public Double getSaleVATGrC() {
       return SaleVATGrC;
    }
    protected void setSaleVATGrC(Double value) {
       SaleVATGrC = value;
    }

   /**
    *Up to 15 symbols for the accumulated VAT in group D
    */
    public Double SaleVATGrD;
    public Double getSaleVATGrD() {
       return SaleVATGrD;
    }
    protected void setSaleVATGrD(Double value) {
       SaleVATGrD = value;
    }

   /**
    *Up to 15 symbols for the accumulated turnover in group E
    */
    public Double SaleTurnoverVATGrE;
    public Double getSaleTurnoverVATGrE() {
       return SaleTurnoverVATGrE;
    }
    protected void setSaleTurnoverVATGrE(Double value) {
       SaleTurnoverVATGrE = value;
    }

   /**
    *Up to 15 symbols for the sale turnover in VAT groups A, B, C, D
    */
    public Double SaleTurnoverABCD;
    public Double getSaleTurnoverABCD() {
       return SaleTurnoverABCD;
    }
    protected void setSaleTurnoverABCD(Double value) {
       SaleTurnoverABCD = value;
    }

   /**
    *Up to 15 symbols for the refund VAT in group A
    */
    public Double RefundVATGrA;
    public Double getRefundVATGrA() {
       return RefundVATGrA;
    }
    protected void setRefundVATGrA(Double value) {
       RefundVATGrA = value;
    }

   /**
    *Up to 15 symbols for the refund VAT in group B
    */
    public Double RefundVATGrB;
    public Double getRefundVATGrB() {
       return RefundVATGrB;
    }
    protected void setRefundVATGrB(Double value) {
       RefundVATGrB = value;
    }

   /**
    *Up to 15 symbols for the refund VAT in group C
    */
    public Double RefundVATGrC;
    public Double getRefundVATGrC() {
       return RefundVATGrC;
    }
    protected void setRefundVATGrC(Double value) {
       RefundVATGrC = value;
    }

   /**
    *Up to 15 symbols for the refund VAT in group D
    */
    public Double RefundVATGrD;
    public Double getRefundVATGrD() {
       return RefundVATGrD;
    }
    protected void setRefundVATGrD(Double value) {
       RefundVATGrD = value;
    }

   /**
    *Up to 15 symbols for the refund accumulated turnover in group E
    */
    public Double RefundTurnoverVATGrE;
    public Double getRefundTurnoverVATGrE() {
       return RefundTurnoverVATGrE;
    }
    protected void setRefundTurnoverVATGrE(Double value) {
       RefundTurnoverVATGrE = value;
    }

   /**
    *Up to 15 symbols for the refund turnover in VAT groups A, B, C, D
    */
    public Double RefundTurnoverABCD;
    public Double getRefundTurnoverABCD() {
       return RefundTurnoverABCD;
    }
    protected void setRefundTurnoverABCD(Double value) {
       RefundTurnoverABCD = value;
    }
}
