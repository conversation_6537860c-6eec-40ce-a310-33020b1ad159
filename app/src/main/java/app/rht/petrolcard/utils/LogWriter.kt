package app.rht.petrolcard.utils

import android.os.Build
import android.os.Environment
import android.util.Log
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.utils.constant.AppConstant
import java.io.*
import java.text.SimpleDateFormat
import java.util.*


class LogWriter(var fileName:String) {
    var includeDate:Boolean = true

    companion object{

        private val TAG = LogWriter::class.simpleName
        private var folderPath = ""
        private var filePath = ""
        fun getTodaysLog() : ArrayList<File> {
            var todayFiles = ArrayList<File>()
            val date = SimpleDateFormat("dd-MM-yyyy", Locale.getDefault()).format(Calendar.getInstance().time)
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
            folderPath = path+File.separator+AppConstant.LOG_FOLDER_NAME
            val directory = File(folderPath)

            if(directory.exists()){
                val files = directory.listFiles()
                // Log.e(TAG,"FIELS::: ${files.size}")
                for(file in files){
                    if(file.name.contains("$date.log")){
                        //   Log.e(TAG,"Today's file : ${file.name}")
                        todayFiles.add(file)
                    }
                    else {
                        // Log.e(TAG, file.name)
                    }
                }
            }
            return todayFiles
        }
        fun getAllLogs() : ArrayList<File> {
            var todayFiles = ArrayList<File>()
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
            folderPath = path+File.separator+AppConstant.LOG_FOLDER_NAME
            val directory = File(folderPath)
            if(directory.exists()){
                val files = directory.listFiles()
                for(file in files!!){
                    Log.i(TAG,"Extension :: "+file.extension)
                    if(file.extension == "log") {
                        todayFiles.add(file)
                    }
                }
            }
            return todayFiles
        }

        fun getTempLogDir() : String {
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path

            val dirPath = path+File.separator+AppConstant.TEMP_LOG_FOLDER_NAME

            val directory = File(dirPath)

            if(!directory.exists()){
                val result = directory.mkdir()
                Log.e(TAG,"Log temp dir created : $result")
            }

            return dirPath
        }

        fun getLogDir() : String {
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
            val dirPath = path+File.separator+AppConstant.TEMP_LOG_FOLDER_NAME
            val directory = File(dirPath)

            if(!directory.exists()){
                val result = directory.mkdir()
                Log.e(TAG,"Log dir created : $result")
            }
            return dirPath
        }

        fun deleteLogDir(){
            try{
                val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
                val logDirectory = File(path+File.separator+AppConstant.LOG_FOLDER_NAME)
                val data = logDirectory.deleteRecursively()
                Log.e(TAG,"Log dir deleted : $data")
            } catch (e: Exception) { e.printStackTrace() }
        }

        fun deleteTempLogDir(){
            try{
                val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
                val logDirectory = File(path+File.separator+AppConstant.TEMP_LOG_FOLDER_NAME)
                val data = logDirectory.deleteRecursively()
                Log.e(TAG,"Temp Log dir deleted : $data")
            } catch (e: Exception) { e.printStackTrace() }
        }
        fun deleteCurrentTransactionLogs(fileName: String)
        {
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
            val logFile = File(path+File.separator+AppConstant.LOG_FOLDER_NAME+File.separator+fileName)
            if (logFile.exists()) {
                if (logFile.delete()) {
                    println("file Deleted :$logFile")
                } else {
                    println("file not Deleted :$logFile")
                }
            }
        }

        fun getAllLogsFromTemp() : ArrayList<File> {
            val todayFiles = ArrayList<File>()
            val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
           val folderPath = path+File.separator+AppConstant.TEMP_LOG_FOLDER_NAME
            val directory = File(folderPath)
            if(directory.exists()){
                val files = directory.listFiles()
                for(file in files!!){
                    Log.i(TAG,"Extension :: "+file.extension)
                    if(file.extension == "log") {
                        todayFiles.add(file)
                    }
                }
            }
            return todayFiles
        }
    }


    init {
        val path = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).path
        folderPath = path+File.separator+AppConstant.LOG_FOLDER_NAME
        val logFolder = File(folderPath)
        if (!logFolder.exists()) {
            logFolder.mkdirs()
        }
    }

    fun appendLog(tag: String?, log: String?) {
        try {
            Log.e("$tag", "$log")
            if(MainApp.getPrefs().isSendLogs == 1) {
                if (fileName.contains(AppConstant.FUEL_SERVICE_LOG_NAME)) {
                    fileName = MainApp.FuelServiceName
                }
                val logFolder = File(folderPath)
                if (!logFolder.exists()) {
                    logFolder.mkdirs()
                }
                filePath = folderPath + File.separator + "TRX" + fileName + ".log"
                val logFile = File(filePath)
                var isNewFileCreated = false
                if (!logFile.exists()) {
                    logFile.createNewFile()
                    isNewFileCreated = true
                }

                //BufferedWriter for performance, true to set append to file flag
                val buf = BufferedWriter(FileWriter(logFile, true))
                val calendar: Calendar = Calendar.getInstance()
                if (isNewFileCreated) {
                    val appDetails = printVersionDetails()
                    buf.append(appDetails)
                }
                buf.append(
                    SimpleDateFormat("dd-MM-yyyy hh:mm:ss:SSS", Locale.getDefault()).format(
                        calendar.time
                    )
                )
                buf.append(" $tag")
                buf.append(" - ")
                buf.append(log)
                buf.newLine()
                buf.close()
            }
            //Log.i(TAG,"Log Stored: $filePath\tContent: $log")
        } catch (e: IOException) {
            e.printStackTrace()
        }
    }


    private fun printVersionDetails() : String {
        val builder = StringBuilder()
        val lineSeparator = "\n"
        builder.append("Brand: ")
        builder.append(Build.BRAND)
        builder.append(lineSeparator)
        builder.append("Device: ")
        builder.append(Build.DEVICE)
        builder.append(lineSeparator)
        builder.append("Model: ")
        builder.append(Build.MODEL)
        builder.append(lineSeparator)
        builder.append("Serial No: ")
        builder.append(Build.SERIAL)
        builder.append(lineSeparator)
        builder.append("Manufacturer: ")
        builder.append(Build.MANUFACTURER)
        builder.append(lineSeparator)
        builder.append("Product: ")
        builder.append(Build.PRODUCT)
        builder.append(lineSeparator)
        builder.append("SDK: ")
        builder.append(Build.VERSION.SDK)
        builder.append(lineSeparator)
        builder.append("Release: ")
        builder.append(Build.VERSION.RELEASE)
        builder.append(lineSeparator)
        builder.append("\n****** APP INFO ******\n")
        val versionName = getVersionName()
        builder.append("Version: ")
        builder.append(versionName)
        builder.append(lineSeparator)
        val versionCode = getVersionCode()
        builder.append("Version Code: ")
        builder.append(versionCode)
        builder.append("\n****** APP INFO END ******\n\n")

        return builder.toString()
    }

    private fun getVersionName(): String {
        return try {
            val ctx = MainApp.appContext
            val packageInfo = ctx.packageManager.getPackageInfo(ctx.packageName, 0)
            packageInfo.versionName
        } catch (e: Exception) {
            "Unknown"
        }
    }
    private fun getVersionCode(): String {
        return try {
            val ctx = MainApp.appContext
            val packageInfo = ctx.packageManager.getPackageInfo(ctx.packageName, 0)
            ""+packageInfo.versionCode
        } catch (e: Exception) {
            "Unknown"
        }
    }

}
