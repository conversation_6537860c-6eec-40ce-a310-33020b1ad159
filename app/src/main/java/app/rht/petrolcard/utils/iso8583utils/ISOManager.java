package app.rht.petrolcard.utils.iso8583utils;

import android.content.Context;
import android.content.res.AssetManager;

import org.jpos.iso.ISOException;
import org.jpos.iso.ISOMsg;
import org.jpos.iso.packager.GenericPackager;

import java.io.IOException;
import java.io.InputStream;

public class ISOManager {
    private final String TAG = ISOManager.class.getSimpleName();
    private final String xmlFile = "iso8583JPosXml.xml";
    private GenericPackager packager;
    private final Context context;

    public ISOManager(Context context) {
        this.context = context;
        try {
            System.setProperty("org.xml.sax.driver", "org.xmlpull.v1.sax2.Driver");
            AssetManager assetManager = context.getAssets();
            InputStream xmlDoc = assetManager.open(xmlFile);
            packager = new GenericPackager(xmlDoc);
        } catch (IOException | ISOException e) {
            e.printStackTrace();
        }
    }

    public String pack(ISOMsg isoMessage){
        String packedMessage = "";
        try {
            isoMessage.setPackager(packager);
            byte[] bIsoMsg = isoMessage.pack();

            for (byte b : bIsoMsg) {
                packedMessage += (char) b;
            }
        } catch (ISOException e) {
            e.printStackTrace();
        }
        return packedMessage;
    }

    public void unpack(String isoMessage){
        try {
            ISOMsg isoMsg = new ISOMsg();
            isoMsg.setPackager(packager);

            byte[] bIsoMessage = new byte[isoMessage.length()];
            for (int i = 0; i < bIsoMessage.length; i++) {
                bIsoMessage[i] = (byte) (int) isoMessage.charAt(i);
            }
            isoMsg.unpack(bIsoMessage);

            System.out.println("MTI='"+isoMsg.getMTI()+"'");
            for(int i=1; i<=isoMsg.getMaxField(); i++){
                if(isoMsg.hasField(i))
                    System.out.println(i+"='"+isoMsg.getString(i)+"'");
            }

        } catch (ISOException e) {
            e.printStackTrace();
        }
    }

    public void unpack(byte[] isoMessage){
        try {
            ISOMsg isoMsg = new ISOMsg();
            isoMsg.setPackager(packager);

            isoMsg.unpack(isoMessage);

            System.out.println("MTI='"+isoMsg.getMTI()+"'");
            for(int i=1; i<=isoMsg.getMaxField(); i++){
                if(isoMsg.hasField(i))
                    System.out.println(i+"='"+isoMsg.getString(i)+"'");
            }

        } catch (ISOException e) {
            e.printStackTrace();
        }
    }
}
