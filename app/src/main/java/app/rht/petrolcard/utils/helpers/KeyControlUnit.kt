package app.rht.petrolcard.utils.helpers

object KeyControlUtil {
    var WangPosManagerClass: Class<*>? = null
    var WangPosManager: Any? = null
    fun initWpos() {
        try {
            WangPosManagerClass = Class.forName("android.os.WangPosManager")
            WangPosManager = WangPosManagerClass!!.newInstance()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /*
    * 0 is open
    * 1 is close
    * */
    fun setADBMode(type: Int) {
        initWpos()
        try {
            val method = WangPosManagerClass!!.getMethod(
                "setAdbMode",
                Int::class.javaPrimitiveType
            )
            method.invoke(WangPosManager, type)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * power button  on-off
     *
     * @param type true - on ;false - off
     */
    fun setPropForControlPowerKey(type: Boolean) {
        initWpos()
        try {
            val method = WangPosManagerClass!!.getMethod(
                "setPropForControlPowerKey",
                Boolean::class.javaPrimitiveType
            )
            method.invoke(WangPosManager, type)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * set SystemUi's statusbar available(This systemui can't be a third party app)
     *
     * @param mode if the mode is 0, statusbar available
     * if the mode is 1, statusbar unavailable
     */
    fun setStatusbarMode(mode: Int) {
        initWpos()
        try {
            val method = WangPosManagerClass!!.getMethod(
                "setStatusbarMode",
                Int::class.javaPrimitiveType
            )
            method.invoke(WangPosManager, mode)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Set the back key is available or unavailable
     *
     * @param type if type is true, back key is unavailable
     * else back key is available.
     */
    fun setPropForControlBackKey(type: Boolean) {
        initWpos()
        try {
            val method = WangPosManagerClass!!.getMethod(
                "setPropForControlBackKey",
                Boolean::class.javaPrimitiveType
            )
            method.invoke(WangPosManager, type)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Set the home key is available or unavailable
     *
     * @param type if type is true, home key is unavailable
     * else home key is available.
     */
    fun setPropForControlHomeKey(type: Boolean) {
        initWpos()
        try {
            val method = WangPosManagerClass!!.getMethod(
                "setPropForControlHomeKey",
                Boolean::class.javaPrimitiveType
            )
            method.invoke(WangPosManager, type)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Set the menu key is available or unavailable
     *
     * @param type if type is true, menu key is unavailable
     * else menu key is available.
     */
    fun setPropForControlMenuKey(type: Boolean) {
        initWpos()
        try {
            val method = WangPosManagerClass!!.getMethod(
                "setPropForControlMenuKey",
                Boolean::class.javaPrimitiveType
            )
            method.invoke(WangPosManager, type)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}
