package app.rht.petrolcard.utils.im30UsbComm

import android.content.Context
import android.hardware.usb.UsbManager
import android.util.Log
import androidx.appcompat.app.AppCompatActivity
import app.rht.petrolcard.utils.LogUtils.Companion.log
import tw.com.prolific.pl2303gmultilib.PL2303GMultiLib
import java.util.*

class USBComm(val context: Context) {
    //region USB Comm
    private val TAG = USBComm::class.simpleName
    private var readDataBufferSize = 64
    private var deviceIndex1 = 0
    private var MAX_DEVICE_COUNT = 4
    private var ACTION_USB_PERMISSION = "com.prolific.pl2300G_multisimpletest.USB_PERMISSION"

    private var gUARTInfoList = arrayOfNulls<UARTSettingInfo>(MAX_DEVICE_COUNT)

    private var iDeviceCount = 0
    private var bDeviceOpened = BooleanArray(MAX_DEVICE_COUNT)

    private var gThreadStop = BooleanArray(MAX_DEVICE_COUNT)
    private var gRunningReadThread = BooleanArray(MAX_DEVICE_COUNT)
    private var enableFixedCOMPortMode = false

    private var mSerialMulti : PL2303GMultiLib? = null

    var readTimer: Timer? = null
    var data: String? = null
    var packet = ArrayList<Byte>()
    private fun stopReadMessage() {
        if(readTimer!=null){
            readTimer!!.cancel()
        }
    }
    fun initUSBComm(){
        log(TAG,"INIT USB COMM")
        mSerialMulti = PL2303GMultiLib(context.getSystemService(AppCompatActivity.USB_SERVICE) as UsbManager, context, ACTION_USB_PERMISSION)
        gUARTInfoList = arrayOfNulls<UARTSettingInfo>(MAX_DEVICE_COUNT)
        for (i in 0 until MAX_DEVICE_COUNT) {
            gUARTInfoList[i] = UARTSettingInfo()
            gUARTInfoList[i]!!.iPortIndex = i
            gThreadStop[i] = false
            gRunningReadThread[i] = false
            bDeviceOpened[i] = false
        }
        getUsbDataInResume()
    }
    fun closeUSBComm(){
        if (mSerialMulti != null) {
            stopReadMessage()
            //stopHeartBit()
            for (i in 0 until MAX_DEVICE_COUNT) {
                gThreadStop[i] = true
            }
            mSerialMulti!!.PL2303Release()
            mSerialMulti = null
        }
    }
    fun sendUsbSerial(message: ByteArray){
        println("Send Result: $message -- ${String(message)}")
        val res = mSerialMulti!!.PL2303Write(deviceIndex1, message)
        println("Send Result: $res")
        if (res < 0) {
            log(TAG,"w: fail to write: $res")
        }
    }
    fun sendUsbSerial(message: String){
        sendUsbSerial(message.toByteArray(Charsets.UTF_8))
    }
    private fun delayTime(dwTimeMS: Int) {
        //Thread.yield();
        var CheckTime: Long
        if (0 == dwTimeMS) {
            Thread.yield()
            return
        }
        //Returns milliseconds running in the current thread
        val StartTime: Long = System.currentTimeMillis()
        do {
            CheckTime = System.currentTimeMillis()
            Thread.yield()
        } while (CheckTime - StartTime <= dwTimeMS)
    }
    fun getUsbDataInResume(){
        log(TAG,"RESUME METHOD")
        synchronized(context) {
            reSetStatus()
            iDeviceCount = mSerialMulti!!.PL2303Enumerate()
            delayTime(60)
            log(TAG,"enumerate Count=$iDeviceCount")
            if (0 == iDeviceCount) {
                //SetEnabledDevControlPanel(DeviceOrderIndex.DevOrder1, false, false)
                showToast( "no more devices found")
                log(TAG,"no more devices found")
            } else {
                log(TAG,"DevOpen[0]=" + bDeviceOpened[deviceIndex1])
                if (!bDeviceOpened[deviceIndex1]) {
                    log(TAG,"iDeviceCount(=1)=$iDeviceCount")
                    /*  SetEnabledDevControlPanel(DeviceOrderIndex.DevOrder1,
                          bOpen = true,
                          bWrite = false
                      )*/
                    if (enableFixedCOMPortMode) {
                        if (mSerialMulti!!.PL2303getCOMNumber(0) !== null || mSerialMulti!!.PL2303getCOMNumber(0) !== "") {
                            log(TAG,"Button1_COM Number: " + mSerialMulti!!.PL2303getCOMNumber(0))
                        }
                    }
                }
                showToast("The $iDeviceCount devices are attached")
                log(TAG,"The $iDeviceCount devices are attached")
            }
        }

        openUARTDevice(deviceIndex1)

    }
    private fun openUARTDevice(index: Int) {
        log(TAG,"Enter OpenUARTDevice: $index")
        if (mSerialMulti == null) {
            log(TAG,"Error: mSerialMulti==null")
            return
        }
        if (!mSerialMulti!!.PL2303IsDeviceConnectedByIndex(index)) {
            log(TAG,"Error: !AP_mSerialMulti.PL2303IsDeviceConnectedByIndex(index)")
            return
        }
        val res: Boolean
        val info = gUARTInfoList[index]!!

        log(TAG,"UARTSettingInfo: index:" + java.lang.String.valueOf(info.iPortIndex))
        res = mSerialMulti!!.PL2303OpenDevByUARTSetting(
            index, info.mBaudrate, info.mDataBits, info.mStopBits,
            info.mParity, info.mFlowControl
        )
        if (!res) {
            log(TAG,"Error: fail to PL2303OpenDevByUARTSetting")
            return
        }
        /*if (DeviceIndex1 == index) {
            SetEnabledDevControlPanel(DeviceOrderIndex.DevOrder1, false, true)
        }*/
        bDeviceOpened[index] = true

        readMessage()
        //startHeartBit()
        log(TAG,"Open [" + mSerialMulti!!.PL2303getDevicePathByIndex(index) + "] successfully!")
        log(TAG,"Open [" + mSerialMulti!!.PL2303getCOMNumber(index) + "] successfully!")
        showToast("Open [" + mSerialMulti!!.PL2303getDevicePathByIndex(index) + "] successfully!",)

        return
    }
    private var ReadLen1 = 0
    private val ReadBuf1 = ByteArray(readDataBufferSize)
    fun readMessage() {
        readTimer = Timer()
        readTimer!!.scheduleAtFixedRate(
            object : TimerTask() {
                override fun run() {
                    ReadLen1 = mSerialMulti!!.PL2303Read(deviceIndex1, ReadBuf1)
                    if (ReadLen1 > 0) {
                        //ReadBuf1[ReadLen1] = 0;
                        //System.out.println("Reading Port 1 data::: "+ReadLen1);
                        //log("Read  Length : $ReadLen1")
                        if (ReadLen1 > 0) {
                            data = ""
                            println("Read $ReadLen1 bytes.")
                            for (j in 0 until ReadLen1) {
                                packet.add(ReadBuf1[j])
                                data += toHex(ReadBuf1[j])
                                //System.out.print( mtProtocol.byteToHex(ReadBuf1[j]) + " ");
                            }
                            println("$data ")
                            packet.clear()
                            data = ""

                        }
                    } //if (len > 0)
                }
            }, 0, 60
        )
    }
    private fun reSetStatus() {
        log(TAG,"-->> ReSetStatus")
        log(TAG,"-->> PL2303HXD_ReSetStatus")
        mSerialMulti!!.PL2303HXD_ReSetStatus()
        log(TAG,"<<-- PL2303HXD_ReSetStatus")
        if (bDeviceOpened[deviceIndex1]) {
            log(TAG,"DeviceIndex1 is open")
            if (!mSerialMulti!!.PL2303IsDeviceConnectedByIndex(0)) {
                log(TAG,"DeviceIndex1: disconnect")
                //SetEnabledDevControlPanel(DeviceOrderIndex.DevOrder1, false, false)
                /*spBaudRate1.setEnabled(false);*/bDeviceOpened[deviceIndex1] = false
                //if (enableFixedCOMPortMode) btnUsbConnect.text = "Open"
            }
        }
        log(TAG,"<<-- ReSetStatus")
    }

    //endregion

    private fun ByteArray.toHex(): String = joinToString(separator = "") { eachByte -> "%02x".format(eachByte) }
    private fun toHex(byte:Byte): String {
        return "%02x".format(byte)
    }

    private fun showToast(text:String){
        /*Toast.makeText(context,
             text,
             Toast.LENGTH_LONG)*/
    }
}