package app.rht.petrolcard.utils.iso8583utils.gtSocket

import android.os.Handler
import android.os.Looper
import android.util.Log
import app.rht.petrolcard.utils.LogUtils.Companion.log
import java.io.IOException
import java.io.InputStream
import java.io.OutputStream
import java.net.InetSocketAddress
import java.net.Socket
import java.net.SocketException
import java.net.UnknownHostException


internal class TcpClient(private var ip: String, private var port: Int, private var tcpClientListener: TcpClientListener) {

    private lateinit var socket: Socket
    private var inputStream: InputStream? = null
    private var outputStream: OutputStream? = null

    fun connect() {
        val thread = ConnectThread(ip, port)
        thread.start()
    }

    fun disconnect() {
        if (socket.isConnected) {
            try {
                if (inputStream != null) inputStream!!.close()
                if (outputStream != null) outputStream!!.close()
                socket.close()
                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onConnectionStateChanged(
                        ConnectionState.DISCONNECTED
                    )
                }
            } catch (e: IOException) {
                e.printStackTrace()
                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onError(
                        ConnectionError.INVALID_CONNECTION
                    )
                }
            }
        }
    }

    fun send(data: String) {
        sendData(data.toByteArray())
    }

    fun send(data: ByteArray) {
        sendData(data)
    }

    private fun sendData(data: ByteArray) {
        val thread = Thread {
            try {
                outputStream = socket.getOutputStream()
                outputStream!!.write(data)
                tcpClientListener.onDataSend()
                Handler(Looper.getMainLooper()).post { tcpClientListener.onDataSend() }
            } catch (e: IOException) {
                e.printStackTrace()
                log(TAG, "Error sending data")
                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onError(
                        ConnectionError.DATA_SEND_FAILED
                    )
                }
            } catch (e: SocketException){
                disconnect()
            }
        }
        thread.start()
    }


    val isConnected: Boolean
        get() = socket.isConnected

    interface TcpClientListener {
        fun onConnectionStateChanged(state: ConnectionState)
        fun onError(error: ConnectionError)
        fun onDataSend()
        fun onDataReceived(message: String, bytes: ByteArray):Boolean
    }

    internal fun ByteArray.toHexString() : String {
        return this.joinToString("") {
            java.lang.String.format("%02x", it)
        }
    }

    internal inner class ConnectThread(var ip: String, var port: Int) : Thread() {
        override fun run() {
            try { //create client socket

                //socket = new Socket(ip, port);
                socket = Socket()
                log(TAG, "Socket creation and connection.")
                log(TAG, "Connecting")
                Handler(Looper.getMainLooper()).post { tcpClientListener.onConnectionStateChanged(
                    ConnectionState.CONNECTING
                ) }

                socket.connect(InetSocketAddress(ip, port), connectionTimeout * 1000)
                val addr = socket.inetAddress
                val tmp = addr.hostAddress
                log(TAG, "Connected $tmp")

                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onConnectionStateChanged(
                        ConnectionState.CONNECTED
                    )
                }
                var buffer :ByteArray
                try {
                    //log(TAG, "Ready to receive data")
                    while (socket.isConnected) {
                        buffer = ByteArray(4096) //6MB
                        inputStream = socket.getInputStream()
                        val dataLength = inputStream!!.read(buffer)
                        if (dataLength != -1) {
                            val data = ByteArray(dataLength)
                            for (i in 0 until dataLength) data[i] = buffer[i]
                            //log(TAG, "total byte received = $dataLength")
                            val received = data.toHexString()
                            //log(TAG, "RECEIVED: $received")
                            Handler(Looper.getMainLooper()).post {
                                tcpClientListener.onDataReceived(
                                    String(data),
                                    data
                                )
                            }
                        } else {
                            disconnect()
                            break
                        }


                    }
                } catch (e: IOException) {
                    log(TAG, "Disconnected")
                    disconnect()
                    Handler(Looper.getMainLooper()).post {
                        tcpClientListener.onConnectionStateChanged(
                            ConnectionState.DISCONNECTED
                        )
                    }
                }
            } catch (uhe: UnknownHostException) { // Unable to identify the IP of the host (www.unknown-host.com) passed when creating the socket.
                log(
                    TAG, "Generation Error: Unable to identify host's IP address.[$ip] (Invalid address value or hostname used)")
                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onError(
                        ConnectionError.UNKNOWN_HOST
                    )
                }
            } catch (ioe: IOException) { //An I/O error occurred during socket creation.
                log(TAG, " Creation Error: No network response")
                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onError(
                        ConnectionError.TIMEOUT
                    )
                }
            } catch (se: SecurityException) { // Perform a function not allowed by the security manager.
                log(
                    TAG,
                    "Generation Error: Occurs by the Security Manager for a security violation (Proxy connection denied, not allowed function call)"
                )
                tcpClientListener.onError(ConnectionError.CONNECTION_DENIED)
            } catch (le: IllegalArgumentException) { // The port number (65536) passed when creating a socket is out of the allowable range (0~65535).
                log(
                    TAG,
                    "Generation Error: Occurs when an invalid parameter is passed to the method. (Use a port number outside the range of 0-65535, pass null proxy)"
                )
                Handler(Looper.getMainLooper()).post {
                    tcpClientListener.onError(
                        ConnectionError.INVALID_CONNECTION
                    )
                }
            }
        }
    }

    companion object {
        private val TAG = TcpClient::class.java.simpleName
        var connectionTimeout = 10
    }
}
