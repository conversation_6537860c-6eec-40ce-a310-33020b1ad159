package app.rht.petrolcard.utils

import app.rht.petrolcard.MainApp
import app.rht.petrolcard.ui.menu.model.TeleCollectFormatModel
import app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel
import app.rht.petrolcard.utils.Support.Companion.log
import app.rht.petrolcard.utils.constant.AppConstant
import com.google.gson.GsonBuilder
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.MultipartBody
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.File
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*

class AppUtils {
    companion object {
        private val TAG = AppUtils::class.simpleName
        fun generateTeleCollectJson(teleCollectFormatModel: TeleCollectFormatModel): String {
            var data = ""
            val gsonBuilder = GsonBuilder()
            val gson = gsonBuilder.setDateFormat("yyyy-MM-dd HH:mm:ss").create()
            data = gson.toJson(teleCollectFormatModel)
            return data
        }

        fun createFormData(content: String) =
            content.toRequestBody(AppConstant.MIME_TYPE_PLAIN_TEXT.toMediaTypeOrNull())

        fun createFormData(imageFile: File?, name: String, mimeType: String) =
            when (imageFile) {
                null -> null
                else -> MultipartBody.Part.createFormData(
                    name,
                    imageFile.name,
                    imageFile.asRequestBody(mimeType.toMediaTypeOrNull())
                )
            }

        fun checkAppExpiry():Boolean
        {
            val referenceModel = MainApp.getPrefs().getReferenceModel()
            return if(referenceModel?.app_expiry_date != null) {
                val today = Support.getDateComparison(Date())
//            if(BuildConfig.DEBUG)
//            {
//                val dtStart = "2023-05-11"
//                val format = SimpleDateFormat("yyyy-MM-dd")
//                try {
//                    today = format.parse(dtStart)
//                } catch (e: ParseException) {
//                    e.printStackTrace()
//                }
//            }

                isExpired(today!!,referenceModel.app_expiry_date)
            } else {
                false
            }
        }

        fun isExpired(mToday: Date, dateExp: String): Boolean {
            val sdfDate = SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH)
            var expDate: Date? = null
            try {
                expDate = sdfDate.parse(dateExp)
            } catch (e: ParseException) {
                e.printStackTrace()
            }
            if (mToday.after(expDate)) {
                log(TAG,"App Expired")
                generateLogs("App Expired",1)
                return true
            }
            if (mToday.before(expDate)) {
                log(TAG,"App Not Expired")
                return false
            }
            return true
        }

        fun generateLogs(message:String,isErrorLog:Int,isDeleteLog:Boolean?=false) {
            val prefs =  MainApp.getPrefs()
            val model = CommonViewModel(MainApp.longApiService,prefs)
            model.generateLogs(message,isErrorLog,isDeleteLog)

        }
    }


}