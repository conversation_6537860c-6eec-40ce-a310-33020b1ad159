package app.rht.petrolcard.utils

import android.annotation.SuppressLint
import android.app.AlertDialog
import android.content.Context
import android.content.res.Resources
import android.graphics.*
import android.graphics.drawable.ColorDrawable
import android.view.LayoutInflater
import android.view.View
import android.widget.*
import androidx.core.content.ContextCompat
import app.rht.petrolcard.BuildConfig
import com.bumptech.glide.Glide
import com.google.android.material.bottomnavigation.BottomNavigationItemView
import com.google.android.material.bottomnavigation.BottomNavigationMenuView
import com.google.android.material.bottomnavigation.BottomNavigationView
import app.rht.petrolcard.R


@Suppress("unused")
object ViewUtils {
    val TAG = ViewUtils::class.simpleName!!

    fun resizeView(view: View, newHeight: Int) {
        val params = view.layoutParams
        params.height = newHeight.toPx
        view.layoutParams = params
    }

    fun viewToBitmap(view: View): Bitmap {
        val bitmap = Bitmap.createBitmap(view.width, view.height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        view.draw(canvas)
        return bitmap
    }
}

fun  TextView.setTextViewDrawableColor(color: Int) {
    for (drawable in compoundDrawables) {
        if (drawable != null) {
            drawable.colorFilter = PorterDuffColorFilter(ContextCompat.getColor(context, color), PorterDuff.Mode.SRC_IN)
        }
    }
}
val CheckedTextView.toggle: Unit
    get() {
        this.isChecked = !this.isChecked
    }

var ImageView.loadImage : String?
    get() {
        return ""
    }
    set(image) {
        if (image == null ) return
        Glide.with(this.context).load(BuildConfig.BASE_IMAGE_URL+image).into(this)
        System.out.println("ImageURL"+BuildConfig.BASE_IMAGE_URL+image)
    }

val TextView.underline: Unit
    get() {
        this.paintFlags = this.paintFlags or Paint.UNDERLINE_TEXT_FLAG
    }

val Int.toPx: Int
    get() = (this * Resources.getSystem().displayMetrics.density).toInt()

val Int.toDp: Int
    get() = (this / Resources.getSystem().displayMetrics.density).toInt()

val Float.toPx: Int
    get() = (this * Resources.getSystem().displayMetrics.density).toInt()

fun AlertDialog.Builder.setEditText(editText: EditText): AlertDialog.Builder {
    val container = FrameLayout(context)
    container.addView(editText)
    val containerParams = FrameLayout.LayoutParams(
            FrameLayout.LayoutParams.MATCH_PARENT,
            FrameLayout.LayoutParams.WRAP_CONTENT
    )
    val marginHorizontal = 48F
    val marginTop = 16F
    containerParams.topMargin = (marginTop / 2).toPx
    containerParams.leftMargin = marginHorizontal.toInt()
    containerParams.rightMargin = marginHorizontal.toInt()
    container.layoutParams = containerParams

    val superContainer = FrameLayout(context)
    superContainer.addView(container)

    setView(superContainer)

    return this
}

@SuppressLint("RestrictedApi")
fun BottomNavigationView.disableShiftMode() {
    val menuView = getChildAt(0) as BottomNavigationMenuView
    try {
        val shiftingMode = menuView::class.java.getDeclaredField("mShiftingMode")
        shiftingMode.isAccessible = true
        shiftingMode.setBoolean(menuView, false)
        shiftingMode.isAccessible = false
        for (i in 0 until menuView.childCount) {
            val item = menuView.getChildAt(i) as BottomNavigationItemView
//            item.setShiftingMode(false)
            // set once again checked value, so view will be updated
            item.setChecked(item.itemData.isChecked)
        }
    } catch (e: NoSuchFieldException) {
    } catch (e: IllegalStateException) {
    }
}

@SuppressLint("UseCompatLoadingForDrawables")
fun getCommonProgressBar(title:String, subTitle:String, context: Context) : AlertDialog {
    val alertCustomDialog: View = LayoutInflater.from(context).inflate(R.layout.commom_progress_dialog, null)

    val alert = AlertDialog.Builder(context)

    alert.setView(alertCustomDialog)
    val tvTitle = alertCustomDialog.findViewById<View>(R.id.tvTitle) as TextView
    val tvSubTitle = alertCustomDialog.findViewById<View>(R.id.tvSubTitle) as TextView

    tvTitle.text = title
    tvSubTitle.text = subTitle

    val dialog = alert.create()
    dialog.window!!.setBackgroundDrawable(context.getDrawable(R.drawable.rounded_rectangle))
    dialog.setCancelable(false)
    dialog.show()
    return dialog
}