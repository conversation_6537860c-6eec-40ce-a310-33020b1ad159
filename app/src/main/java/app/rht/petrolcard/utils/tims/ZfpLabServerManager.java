package app.rht.petrolcard.utils.tims;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;

import android.os.Build;

import app.rht.petrolcard.MainApp;
import app.rht.petrolcard.utils.Support;

public class ZfpLabServerManager {
    final static String serverName = "com.tremol.zfplabserver";


    public static void startServerService(Activity activity) {
        if (!isServerInstalled(activity)) {
         //  Support.Companion.alert("ZfpLabServer is not installed", MainApp.Companion.getAppContext());
            return;
        }

        try {
            Intent intent = new Intent(serverName);
            intent.setPackage(serverName);

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                activity.startForegroundService(intent);
            } else {
                activity.startService(intent);
            }
        } catch (Exception ex) {
            //Support.Companion.alert("ZfpLabServer service is not running", MainApp.Companion.getAppContext());
        }
    }

    public static void stopServerService(Activity activity) {
        Intent intent = new Intent(serverName);
        intent.setPackage(serverName);
        activity.stopService(intent);
    }

    public static void openServerUI(Activity activity) {
        if (!isServerInstalled(activity)) {
          //  Support.Companion.alert("ZfpLabServer is not installed", MainApp.Companion.getAppContext());
            return;
        }

        Intent intent = activity.getPackageManager().getLaunchIntentForPackage(serverName);
        activity.startActivity(intent);
    }

    public static boolean isServerInstalled(Activity activity) {
        try {
            PackageManager pm = activity.getPackageManager();
            PackageInfo pi = pm.getPackageInfo(serverName, 0);

        } catch (PackageManager.NameNotFoundException e) {
            return false;
        }

        return true;
    }
}