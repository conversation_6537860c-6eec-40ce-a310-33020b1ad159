package app.rht.petrolcard.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

public class Encryptor {

    private static byte[] exatobyte(String data){


        byte[] dataBy = new byte[data.length() / 2];
        for (int i = 0; i < dataBy.length; i++) {
            //key[i] = (byte) Integer.parseInt(hex.substring(i, i+2),16);
            dataBy[i] = (byte) Integer.parseInt(data.substring(2*i, 2*i+2),16);
        }
        return dataBy;

    }



    public static String encrypt(String data, String cle)throws Exception {

        byte[] key = new byte[cle.length() / 2];
        for (int i = 0; i < key.length; i++) {
            key[i] = (byte) Integer.parseInt(cle.substring(2*i, 2*i+2),16);
        }
        SecretKeySpec secretKey = new SecretKeySpec(key,"DESede");//DES
        Cipher cipher = Cipher.getInstance("DESede/ECB/Nopadding");//"DESede/ECB/Nopadding 3DES/ECB/PKCS5Padding

        cipher.init(Cipher.ENCRYPT_MODE,secretKey);
        byte[] result = cipher.doFinal(exatobyte(data));


        return ByteUtil.byteArrayToHex(result).toUpperCase();

    }

}