package app.rht.petrolcard.utils.esdPrinterJobs

import android.os.CountDownTimer
import android.util.Base64
import android.util.Log
import app.rht.petrolcard.BuildConfig
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.database.baseclass.ESDSignatureDao
import app.rht.petrolcard.database.baseclass.FiscalPrinterModel
import app.rht.petrolcard.service.model.RFIDPumpsModel
import app.rht.petrolcard.ui.esdsign.model.EsdSignModel
import app.rht.petrolcard.ui.reference.model.TransactionModel
import app.rht.petrolcard.utils.*
import app.rht.petrolcard.utils.LogUtils.Companion.log
import com.google.gson.Gson
import java.io.BufferedReader
import java.io.InputStreamReader
import java.io.OutputStream
import java.io.OutputStreamWriter
import java.lang.NullPointerException
import java.net.DatagramPacket
import java.util.*
import java.util.concurrent.TimeUnit
import kotlin.collections.ArrayList
import java.net.Socket

class PeriodicFusionChecker {

    companion object{
        var logWriter : LogWriter = LogWriter("PeriodicFusionCheckerLogs")

        private val TAG = PeriodicFusionChecker::class.simpleName


        //private var terminal : Terminal? = null
        private var fiscalPrinterModel : FiscalPrinterModel? = null


        var esdSignature = ""
        var udpManager: UDPManager? = null
        private var request1 = "\u0002{/0/09\u0003JN"
        private var messageToSend = ""
        private var request3 = "\u0002}/72\u0003NN"
        private var request4 = "\u0002^/41\u0003"
        private var fiscalDate = ""

        private fun tryToGetSignature() {
            esdSignature = ""
            fusionSignCountDownTimer.cancel()
            sendUdpMessage(request1)
            sendUdpMessage(getBlocToSignContent(oldMessageForEsd))
            sendUdpMessage(request3)
            sendUdpMessage(request4)
            fusionSignCountDownTimer.start()
        }


        private var isSignature = false
        private var messageCount = 0

        private fun sendUdpMessage(message: String) {
            try {
                udpManager!!.sendMessage(message)
                logWriter.appendLog(TAG, "SENT to ESD: $message")
                messageCount++
                Thread.sleep(1000)
            } catch (e: InterruptedException) {
                e.printStackTrace()
            } catch (e: NullPointerException) {
                e.printStackTrace()
            }
        }

        private fun getBlocToSignContent(text: String): String {
            val data = text.toByteArray() //"UTF-8" removed by altaf
            val base64 = Base64.encodeToString(data, Base64.DEFAULT)
            log("Base 64 ", base64.replace("\\s+".toRegex(), "") + "!")
            var blockToSign = base64.replace("\\s+".toRegex(), "")
            blockToSign = "\u0002&/$blockToSign/" + Utils.CalcChecksum(
                "&/$blockToSign/".toByteArray()
            ) + "\u0003"
            return blockToSign
        }

        //region unsignedFusionTrxCheck
        private val periodicSignTimer = object : CountDownTimer(30*60*1000,1000){
            override fun onTick(millis: Long) {
                val remainingTime = String.format("%02d:%02d:%02d", TimeUnit.MILLISECONDS.toHours(millis),
                    TimeUnit.MILLISECONDS.toMinutes(millis) % TimeUnit.HOURS.toMinutes(1),
                    TimeUnit.MILLISECONDS.toSeconds(millis) % TimeUnit.MINUTES.toSeconds(1))
                log(TAG,"ESD SIGN Will start in $remainingTime")
                val mSharedPrefsUtil = MainApp.getPrefs()
                fiscalPrinterModel = mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer
                if(!fiscalPrinterModel!!.isAvailable)
                    this.cancel()
            }

            override fun onFinish() {
                log(TAG,"Terminal is inactive...")
                CheckUnsignedFusionTrxTask().execute()
            }
        }
        private var fusionSignCountDownTimer: CountDownTimer = object : CountDownTimer( /*120000*/10000, 1000) {
            var count = 0
            override fun onTick(millisUntilFinished: Long) {
                count++
                log(TAG, "Waiting for ESD Signature: $count")
            }

            override fun onFinish() {
                count = 0
                breakSignLoop = true
                if (esdSignature.isEmpty()) {
                    log(TAG, "ESD sign timer timeout: Sign data empty")
                } else {
                    log(TAG, "ESD sign timer timeout: Sign already received")
                }

                if(unsignedTransactions.isEmpty()) {
                    unSignedFusionTrxTaskRunning = false
                }
            }
        }

        fun resetPeriodicSignTimer(){
            val mSharedPrefsUtil = MainApp.getPrefs()
            fiscalPrinterModel =  mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer
            if(fiscalPrinterModel!!.isAvailable && !unSignedFusionTrxTaskRunning && !blockedByOtherTask){
                periodicSignTimer.cancel()
                periodicSignTimer.start()
            }
            else
            {
                stopChecker()
            }
        }

        private var blockedByOtherTask = false
        fun stopChecker(){
            blockedByOtherTask = true
            periodicSignTimer.cancel()
        }


        fun startChecker(){
           blockedByOtherTask = false
           resetPeriodicSignTimer()
        }

        private var unSignedFusionTrxTaskRunning = false

        var unsignedTransactions = ArrayList<EsdSignModel>()
        private var breakSignLoop = false
        private class CheckUnsignedFusionTrxTask: CoroutineAsyncTask<Void, Void, Void?>() {

            override fun doInBackground(vararg params: Void): Void? {
                unSignedFusionTrxTaskRunning = true
                unsignedTransactions.clear()
                val prefs = MainApp.getPrefs()
                val fusionUrl = Support.splitIpAndPortFromUrl(prefs.getFusionModel()!!.API)
                val fusionAddress = fusionUrl[0]
                try{
                    var socket = Socket(fusionAddress,3456)
                    var response: String
                    var x: Int

                    val outputStream: OutputStream = socket.getOutputStream()
                    val outputStreamWriter = OutputStreamWriter(outputStream)
                    outputStreamWriter.write("OP_CARBURANT")
                    outputStreamWriter.flush() // request is sent
                    val inputStream = socket.getInputStream()
                    val bufferedReader =  BufferedReader(InputStreamReader(inputStream))
                    var byteArray = ArrayList<Byte>()
                    while(true)
                    {
                        x = bufferedReader.read()
                        if(x==-1) break
                        byteArray.add(x.toByte())
                    }

                    response = String(Support.byteListToByteArray(byteArray))//byteArray
                    log(TAG,"#### $response")
                    val fusionResponse = Gson().fromJson(response, UnsignedFusionTransactions::class.java)

                    if(fusionResponse!=null){
                        val mSharedPrefsUtil = MainApp.getPrefs()
                        val connectedPumps :List<RFIDPumpsModel> = mSharedPrefsUtil.getReferenceModel()!!.RFID_TERMINALS!!.pumps
                        if (connectedPumps.isNotEmpty() && fusionResponse.transactions.isNotEmpty()) {
                            for (pump in connectedPumps) {
                                for (transaction in fusionResponse.transactions) {
                                    log(TAG,"TERMINAL PUMP: ${transaction.pump} TRX PUMP: ${pump.pump_number}")
                                    if (transaction.pump == "${pump.pump_number}") {
                                        log(TAG,"${transaction.pump} = ${pump.pump_number}")
                                        val item = EsdSignModel(
                                            sequenceNumber = transaction.refTransaction,
                                            pumpNumber = transaction.pump,
                                            productNo = transaction.hose,
                                            amount = transaction.amount,
                                            volume = transaction.quantite,
                                            unitPrice = transaction.pu
                                        )
                                        unsignedTransactions.add(item)
                                    }
                                    else
                                    {
                                        log(TAG,"${transaction.pump} != ${pump.pump_number}")
                                    }
                                }
                            }
                        }
                    }
                }
                catch (e:Exception){
                    e.printStackTrace()
                }

                return null
            }

            override fun onPostExecute(result: Void?) {
                super.onPostExecute(result)
                log(TAG,"Total unsigned trx found in fusion ${unsignedTransactions.size}")
                if(unsignedTransactions.isNotEmpty()){
                    SignFusionTrxsTask().execute()
                }
                else
                {
                    unSignedFusionTrxTaskRunning = false
                    startChecker()
                }
            }
        }
        var oldMessageForEsd = ""

        private class SignFusionTrxsTask : CoroutineAsyncTask<Void?, Void?, Boolean>() {
            override fun doInBackground(vararg params: Void?): Boolean {

                try {
                    fusionSignCountDownTimer.start()
                    val esdSignModel: EsdSignModel = unsignedTransactions[0]
                    val trx: TransactionModel = Support.createTransactionTaxiModel(esdSignModel)
                    unsignedTransactions.removeAt(0)

                    val esdSignatureDAO = ESDSignatureDao()
                    if(!esdSignatureDAO.isOpen())
                        esdSignatureDAO.open()
                    val id = esdSignatureDAO.insert(esdSignModel)
                    esdSignModel.id = id
                    log(TAG,"Transaction inserted in db : $id")
                    esdSignatureDAO.close()

                    val message = Support.prepareEsdMessage(esdSignModel.id)
                    oldMessageForEsd = message
                    log(TAG,"#$#$ Trying to sign trx: " + trx.sequenceController)
                    breakSignLoop = false
                    messageToSend = getBlocToSignContent(message)
                    val mSharedPrefsUtil = MainApp.getPrefs()
                    val ip: String = if (BuildConfig.DEBUG) "**************" else  mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer!!.IPESD
                    val port =  mSharedPrefsUtil.getReferenceModel()!!.fiscal_printer!!.PORTESD
                    if (udpManager == null) {
                        udpManager = UDPManager.getUdpManager(ip, port)
                        udpManager!!.startUDPSocket()
                    }
                    udpManager!!.setUdpReceiveCallback { data: DatagramPacket ->
                        val strReceive = String(data.data, 0, data.length)
                        if (strReceive.length > 15) {
                            esdSignature = strReceive
                            //log(TAG,"Big message from ESD: "+strReceive);
                            val packet =
                                strReceive.split("/".toRegex()).toTypedArray()
                            log(TAG,"Total Message part: " + packet.size)
                            for (i in packet.indices) {
                                log(TAG, "Part:" + i + " Data:" + packet[i])
                            }
                            fiscalDate = packet[5].trim { it <= ' ' }
                            esdSignature = packet[7].trim { it <= ' ' }
                            breakSignLoop = true
                            fusionSignCountDownTimer.cancel()
                            trx.transactionSignature = esdSignature + "_" + fiscalDate
                            val seq = String.format("%04d", trx.sequenceController!!.toInt())
                            val pumpId = String.format("%02d", trx.pumpId!!.toInt())
                            val productId = String.format("%02d", trx.idProduit)
                            var serialNumber = Support.getSN()
                            val date = Support.dateToString(Date())!!.replace(" ", "").replace("-", "").replace(":", "")
                            serialNumber =  if (serialNumber!!.length > 4) serialNumber.substring(serialNumber.length - 4) else serialNumber
                            //ESD Serial Number(last 4 digit) TimeStamp SeqController(4 digits) pumpID(2 digits) productId (2 digits)
                            trx.reference = "ESD$serialNumber$date$seq$pumpId$productId"
                            //signedTransactions.add(trx);
                            log(TAG, "SIGN RECEIVED FOR : " + trx.sequenceController + " SIGN: " + esdSignature)
                            esdSignModel.signature = trx.transactionSignature
                            updateTransactionSign(esdSignModel)
                            esdSignature = ""
                        }
                    }
                    sendUdpMessage(request1)
                    sendUdpMessage(messageToSend)
                    sendUdpMessage(request3)
                    sendUdpMessage(request4)
                    while (!breakSignLoop) {
                    }
                }
                catch (e: Exception) {
                    e.printStackTrace()
                }
                return false
            }

            override fun onPostExecute(result: Boolean?) {
                super.onPostExecute(result)
                Log.e(TAG, "Remaining trx to sign " + unsignedTransactions.size)
                if (unsignedTransactions.size > 0) {
                    unSignedFusionTrxTaskRunning = true
                    Support.printAllEsdTransactions()
                    SignFusionTrxsTask().execute()
                } else {
                    if (udpManager != null) udpManager!!.stopUDPSocket()
                    log(TAG, "All trx signed Success")
                    unSignedFusionTrxTaskRunning = false
                    resetPeriodicSignTimer()
                }
            }
        }
        //endregion

        private fun updateTransactionSign(esdSignModel: EsdSignModel){
            esdSignature = ""
            try {
                val esdSignatureDAO = ESDSignatureDao()
                if(!esdSignatureDAO.isOpen())
                    esdSignatureDAO.open()
                esdSignatureDAO.updateESDSignature(esdSignModel)
                esdSignatureDAO.close()
                log(TAG,"Signature updated in db")
                UpdateFusionTrxTask(esdSignModel.sequenceNumber!!).execute()
                Support.printAllEsdTransactions()
            } catch (e:Exception) {
                e.printStackTrace()
            }
        }

        private class UpdateFusionTrxTask(val seqNumber:String): CoroutineAsyncTask<Void,Void,Void?>() {
            override fun doInBackground(vararg params: Void): Void? {
                val prefs = MainApp.getPrefs()
                val fusionUrl = Support.splitIpAndPortFromUrl(
                    prefs.getFusionModel()!!.API
                )
                val fusionAddress = fusionUrl[0]
                try {
                    val socket = Socket(fusionAddress, 3456)
                    var response: String
                    var x: Int

                    val outputStream: OutputStream = socket.getOutputStream()
                    val outputStreamWriter = OutputStreamWriter(outputStream)
                    outputStreamWriter.write("SIGNED#$seqNumber#")
                    outputStreamWriter.flush() // request is sent
                    val inputStream = socket.getInputStream()
                    val bufferedReader =  BufferedReader(InputStreamReader(inputStream))
                    var byteArray = ArrayList<Byte>()
                    while(true)
                    {
                        x = bufferedReader.read()
                        if(x==-1) break
                        byteArray.add(x.toByte())
                    }
                    response = String(Support.byteListToByteArray(byteArray))//byteArray
                    log(TAG,"#### $response")
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                return null
            }

            override fun onPostExecute(result: Void?) {
                super.onPostExecute(result)
                unSignedFusionTrxTaskRunning = if(unsignedTransactions.isNotEmpty())
                    true
                else {
                    resetPeriodicSignTimer()
                    false
                }
            }


        }
    }
}