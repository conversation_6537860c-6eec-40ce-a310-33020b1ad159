package app.rht.petrolcard.utils.paxutils.modules.printer;

import android.content.Context;
import android.graphics.Bitmap;

import com.pax.dal.IPrinter;
import com.pax.dal.entity.EFontTypeAscii;
import com.pax.dal.entity.EFontTypeExtCode;
import com.pax.dal.exceptions.PrinterDevException;

import app.rht.petrolcard.MainApp;
import app.rht.petrolcard.R;
import app.rht.petrolcard.utils.paxutils.util.BaseTester;
import kotlin.jvm.JvmName;

public class PrinterTester extends BaseTester {

    private static PrinterTester printerTester;
    private final IPrinter printer;

    private PrinterTester() {
        printer =  MainApp.Companion.getDal().getPrinter();
    }

    public static PrinterTester getInstance() {
        if (printerTester == null) {
            printerTester = new PrinterTester();
        }
        return printerTester;
    }

    /**
     *
     */
    public void init() {
        try {
            printer.init();
            logTrue("init");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("init", e.toString());
        }
    }

    /**
     *
     * @return
     */
    public int getStatus() {
        try {
            int status = printer.getStatus();
            logTrue("getStatus");
            return status;
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("getStatus", e.toString());
            return 12;
        }

    }

    /**
     *
     * @param asciiFontType
     * @param cFontType
     */
    public void fontSet(EFontTypeAscii asciiFontType, EFontTypeExtCode cFontType) {
        try {
            printer.fontSet(asciiFontType, cFontType);
            logTrue("fontSet");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("fontSet", e.toString());
        }

    }

    /**
     *
     * @param wordSpace
     * @param lineSpace
     */
    public void spaceSet(byte wordSpace, byte lineSpace) {
        try {
            printer.spaceSet(wordSpace, lineSpace);
            logTrue("spaceSet");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("spaceSet", e.toString());
        }
    }

    /**
     *
     * @param str
     * @param charset
     */
    public void printStr(String str, String charset) {
        try {
            printer.printStr(str, charset);
            logTrue("printStr");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("printStr", e.toString());
        }

    }

    /**
     *
     * @param b
     */
    public void step(int b) {
        try {
            printer.step(b);
            logTrue("setStep");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("setStep", e.toString());
        }
    }

    /**
     *
     * @param bitmap
     */
    public void printBitmap(Bitmap bitmap) {
        try {
            printer.printBitmap(bitmap);
            logTrue("printBitmap");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("printBitmapError", e.toString());
        }
    }

    /**
     *
     * @return
     */
    public String start() {
        try {
            int res = printer.start();
            logTrue("start");
            return statusCode2Str(res);
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("start", e.toString());
            return "";
        }

    }

    /**
     *
     * @param indent
     */
    public void leftIndents(short indent) {
        try {
            printer.leftIndent(indent);
            logTrue("leftIndent");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("leftIndent", e.toString());
        }
    }

    /**
     *
     * @return
     */
    public int getDotLine() {
        try {
            int dotLine = printer.getDotLine();
            logTrue("getDotLine");
            return dotLine;
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("getDotLine", e.toString());
            return -2;
        }
    }

    /**
     *
     * @param level
     */
    public void setGray(int level) {
        try {
            printer.setGray(level);
            logTrue("setGray");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("setGray", e.toString());
        }

    }

    /**
     *
     * @param isAscDouble
     * @param isLocalDouble
     */
    public void setDoubleWidth(boolean isAscDouble, boolean isLocalDouble) {
        try {
            printer.doubleWidth(isAscDouble, isLocalDouble);
            logTrue("doubleWidth");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("doubleWidth", e.toString());
        }
    }

    /**
     *
     * @param isAscDouble
     * @param isLocalDouble
     */
    public void setDoubleHeight(boolean isAscDouble, boolean isLocalDouble) {
        try {
            printer.doubleHeight(isAscDouble, isLocalDouble);
            logTrue("doubleHeight");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("doubleHeight", e.toString());
        }

    }

    /**
     *
     * @param isInvert
     */
    public void setInvert(boolean isInvert) {
        try {
            printer.invert(isInvert);
            logTrue("setInvert");
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("setInvert", e.toString());
        }

    }

    /**
     *
     * @param mode
     * @return
     */
    public String cutPaper(int mode) {
        try {
            printer.cutPaper(mode);
            logTrue("cutPaper");
            return "cut paper successful";
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("cutPaper", e.toString());
            return e.toString();
        }
    }

    /**
     *
     * @return
     */
    public String getCutMode() {
        String resultStr = "";
        try {
            int mode = printer.getCutMode();
            logTrue("getCutMode");
            switch (mode) {
                case 0:
                    resultStr = "Only support full paper cut";
                    break;
                case 1:
                    resultStr = "Only support partial paper cutting ";
                    break;
                case 2:
                    resultStr = "support partial paper and full paper cutting ";
                    break;
                case -1:
                    resultStr = "No cutting knife,not support";
                    break;
                default:
                    break;
            }
            return resultStr;
        } catch (PrinterDevException e) {
            e.printStackTrace();
            logErr("getCutMode", e.toString());
            return e.toString();
        }
    }

    /**
     *
     * @param status
     * @return
     */
    public String statusCode2Str(int status) {
        Context context = MainApp.Companion.getAppContext();
        String res = "";
        switch (status) {
            case 0:
                res = context.getString(R.string.submit); //"Success ";
                break;
            case 1:
                res = context.getString(R.string.printer_busy); //"Printer is busy ";
                break;
            case 2:
                res = context.getString(R.string.out_of_paper); //"Out of paper ";
                break;
            case 3:
                res = context.getString(R.string.printer_format_error); //"The format of print data packet error ";
                break;
            case 4:
                res = context.getString(R.string.printer_malfunction); //"Printer malfunctions ";
                break;
            case 8:
                res = context.getString(R.string.printer_over_heat); //"Printer over heats ";
                break;
            case 9:
                res = context.getString(R.string.printer_voltage_low); //"Printer voltage is too low";
                break;
            case 240:
                res = context.getString(R.string.printer_unfinished); //"Printing is unfinished ";
                break;
            case 252:
                res = context.getString(R.string.printer_font_not_installed); //" The printer has not installed font library ";
                break;
            case 254:
                res = context.getString(R.string.data_package_long); //"Data package is too long ";
                break;
            default:
                break;
        }
        return res;
    }
}
