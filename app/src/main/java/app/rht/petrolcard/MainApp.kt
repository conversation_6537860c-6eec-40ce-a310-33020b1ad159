package app.rht.petrolcard

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.job.JobInfo
import android.app.job.JobScheduler
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.os.*
import android.text.TextUtils
import android.util.Log
import android.widget.Toast
import androidx.annotation.RequiresApi
import androidx.multidex.MultiDex
import androidx.multidex.MultiDexApplication
import app.rht.petrolcard.service.NetworkSchedulerService
import app.rht.petrolcard.module.appModule
import app.rht.petrolcard.module.myViewModel
import app.rht.petrolcard.module.networkModule
import app.rht.petrolcard.networkRequest.ApiService
import app.rht.petrolcard.service.LongApiService
import app.rht.petrolcard.service.scheduleTeleCollect.ScheduledTeleCollectService
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.AppUtils
import app.rht.petrolcard.utils.StringUtility
import app.rht.petrolcard.utils.constant.AppConstant
import app.rht.petrolcard.utils.extensions.showToast
import app.rht.petrolcard.utils.job.*
import app.rht.petrolcard.utils.extensions.showToast
import app.rht.petrolcard.utils.paxutils.system.SysTester
import app.rht.petrolcard.utils.tims.FP
import app.rht.petrolcard.utils.uncaughtExceptionHandler.EmailConfig
import app.rht.petrolcard.utils.uncaughtExceptionHandler.EmailSender
import app.rht.petrolcard.utils.uncaughtExceptionHandler.UCEHandler
import com.elvishew.xlog.LogConfiguration
import com.elvishew.xlog.LogLevel
import com.elvishew.xlog.XLog
import com.elvishew.xlog.printer.AndroidPrinter
import com.elvishew.xlog.printer.ConsolePrinter
import com.elvishew.xlog.printer.Printer
import com.elvishew.xlog.printer.file.FilePrinter
import com.elvishew.xlog.printer.file.backup.NeverBackupStrategy
import com.elvishew.xlog.printer.file.naming.DateFileNameGenerator
import com.github.anrwatchdog.ANRError
import com.github.anrwatchdog.ANRWatchDog
import com.google.android.material.snackbar.Snackbar
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.pax.dal.IDAL
import com.pax.neptunelite.api.NeptuneLiteUser
import com.testfairy.TestFairy
import com.usdk.apiservice.aidl.UDeviceService
import com.usdk.apiservice.aidl.beeper.UBeeper
import com.usdk.apiservice.aidl.device.DeviceInfo
import com.usdk.apiservice.aidl.device.UDeviceManager
import com.usdk.apiservice.aidl.led.Light
import com.usdk.apiservice.aidl.led.ULed
import com.usdk.apiservice.aidl.printer.UPrinter
import github.nisrulz.easydeviceinfo.base.EasyDeviceMod
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.gotev.uploadservice.UploadServiceConfig
import net.sqlcipher.database.SQLiteDatabase
import org.koin.android.ext.android.get
import org.koin.android.ext.koin.androidContext
import org.koin.android.ext.koin.androidLogger
import org.koin.core.context.startKoin
import org.koin.core.logger.Level
import wangpos.sdk4.libbasebinder.Core
import java.io.File
import java.io.PrintWriter
import java.io.StringWriter
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit


class MainApp : MultiDexApplication() {
    private var deviceService: UDeviceService? = null
    private var deviceManager: UDeviceManager? = null

    // Called when the application is starting, before any other application objects have been created.
    // Overriding this method is totally optional!
    var notificationChannelID = "TestChannel"

    companion object
    {
        lateinit var appContext: Context
            private set

        var FuelServiceName = ""
            private set

        var fleetCardLogName = ""
            private set
        var sn: String? = null
        var dal: IDAL? = null

        var mFirebaseAnalytics: FirebaseAnalytics? = null
        var deviceName = ""

        var mCore : Core? = null
            private set

        var fp: FP? = null
        private const val TAG = "MainApp"
        /*fun newInstance(c: Context?): Core? {
            try{
                if (mCore == null) {
                    if (BuildConfig.POS_TYPE == "B_TPE") {
                        object : Thread() {
                            override fun run() {
                                mCore = Core(c)
                            }
                        }.start()
                    }
                }
            }
            catch (e:Exception){
                e.printStackTrace()
            }
            return mCore
        }*/
        /*fun getContext(): Context {
            return MainApp().applicationContext
        }*/
        fun logFireBase(text: String?, context: Context?) {
            val pref = AppPreferencesHelper(context!!.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))
            if (mFirebaseAnalytics != null) {
                val parameters = Bundle()
                parameters.putString("sn_", sn)
                parameters.putString("station_gas", pref.getReferenceModel()!!.terminal!!.stationName)
                parameters.putString("content", text)
                mFirebaseAnalytics!!.logEvent("logs_tpe", parameters)
                //mFirebaseAnalytics.setDefaultEventParameters(parameters);
            }
        }

        fun startKoins(context: Context) {
            startKoin {
                androidLogger(Level.NONE)
                //androidLogger()
                androidContext(context)
                modules(appModule, networkModule,myViewModel)
            }
        }
        @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
        private fun scheduleNetworkJob() {
            val myJob : JobInfo = JobInfo.Builder(0, ComponentName(appContext, NetworkSchedulerService::class.java))
                .setRequiresCharging(true)
                .setMinimumLatency(1000)
                .setOverrideDeadline(2000)
                .setRequiredNetworkType(JobInfo.NETWORK_TYPE_ANY)
                .setPersisted(true)
                .build()
            val jobScheduler: JobScheduler =  appContext.getSystemService(Context.JOB_SCHEDULER_SERVICE) as JobScheduler
            jobScheduler.schedule(myJob)

        }

        //region LANDI POS
        private var deviceService: UDeviceService? = null
        private var printer: UPrinter? = null
        private var deviceManager: UDeviceManager? = null
        var beeper: UBeeper? = null
        fun getDeviceService(): UDeviceService? {
            return deviceService
        }

        @Throws(RemoteException::class)
        fun getDeviceManager(): UDeviceManager? {
            if (deviceManager == null) {
                deviceManager = UDeviceManager.Stub.asInterface(deviceService!!.deviceManager)
            }
            return deviceManager
        }

        @Throws(RemoteException::class)
        fun getPrinter(): UPrinter? {
            if (printer == null) {
                printer = UPrinter.Stub.asInterface(deviceService!!.printer)
            }
            return printer
        }
        fun setDeviceService(deviceService: UDeviceService?) {
            this.deviceService = deviceService
        }

        @Throws(RemoteException::class)
        fun getDeviceInfo(): DeviceInfo? {
            return deviceManager!!.deviceInfo
        }

        @Throws(RemoteException::class)
        fun getSerialNo(): String? {
            return getDeviceManager()!!.deviceInfo.serialNo
        }
        //endregion
        val longApiService by lazy {
            LongApiService.create()
        }
        fun getApiService(): ApiService {
            return longApiService
        }

        // End Upload lib functions
        @JvmName("getDal1")
        fun getDal(): IDAL? {
          if (dal == null) {
                try {
                    val start = System.currentTimeMillis()
                    dal = NeptuneLiteUser.getInstance().getDal(appContext)
                    Log.i("Test", "get dal cost:" + (System.currentTimeMillis() - start) + " ms")
                } catch (e: Exception) {
                    e.printStackTrace()
                    Toast.makeText(appContext, "error occurred,DAL is null.", Toast.LENGTH_LONG).show()
                }
            }
            return dal
        }

        fun getPrefs(): AppPreferencesHelper {
            return MainApp().get()
        }

        //var screenCountDownTimer: CountDownTimer? = null
        //    private set
        lateinit var emailConfig : EmailConfig
            private set
    }



    /*fun setScreenCountDownTimer(timer: CountDownTimer) {
        screenCountDownTimer = timer
    }*/

    fun setFuelServiceName(name:String){
        FuelServiceName = name
    }
    fun setFleetCardLogName(name:String){
        fleetCardLogName = name
    }
    //endregion

    override fun attachBaseContext(base: Context) {
        super.attachBaseContext(base)
        MultiDex.install(this)
    }


    private fun initMCore() {
        if (BuildConfig.POS_TYPE == "B_TPE") {
            object : Thread() {
                override fun run() {
                    mCore = Core(appContext)
                }
            }.start()
        } else {
            mCore = null
        }
    }

    override fun onCreate() {
        super.onCreate()
        fp = FP()
        appContext = applicationContext
        startKoins(appContext)

        setUpPosType()
        setupFireBase()

        setupExceptionHandler()

        setUpPrinterConfiguration()
        if(!BuildConfig.DEBUG)
        {
            setupTestFairy()
        }

        SQLiteDatabase.loadLibs(this)

        scheduleNetworkJob()
        logFireBase("FBS Pay",this)
        //scheduleTelecollect()
        initMCore()
        deleteLogFiles()
        //if (BuildConfig.DEBUG) {
             ANRWatchDog(10000)
                 .setIgnoreDebugger(true)
                 .setANRListener {
                     val details = getANRDetailsFromIntent(it!!)
                     emailErrorLog(details)
                 }
                 .setReportThreadNamePrefix("APP_ANR:")
                 .start()
        //}
    }

    fun deleteLogFiles()
    {
        scheduleLogDeleteJob()
        val jobId = 1
        val job = QuickPeriodicJob(jobId, object : PeriodicJob() {
            override fun execute(callback: QuickJobFinishedCallback) {
                Log.i(TAG,"JOB EXECUTED")
                AppUtils.generateLogs("", 0, true)
                callback.jobFinished()
            }
        })
        QuickPeriodicJobCollection.addJob(job)
    }
    fun scheduleLogDeleteJob() {
        if(getPrefs().getReferenceModel() != null) {
            val hours = getPrefs().logDeleteScheduleHour
            val REFRESH_INTERVAL=  TimeUnit.HOURS.toMillis(hours)
            Log.i(TAG,"REFRESH_INTERVAL $REFRESH_INTERVAL")
            val jobScheduler = QuickPeriodicJobScheduler(MainApp.appContext)
            jobScheduler.start(1, REFRESH_INTERVAL.toLong()) // Run job with jobId=1 every 60 seconds
        }
    }
    private fun getANRDetailsFromIntent(error: ANRError): String {
        var strCurrentErrorLog = ""
        val sw = StringWriter()
        val pw = PrintWriter(sw)
        error.printStackTrace(pw)
        val anrString = sw.toString()
        return if (!TextUtils.isEmpty(anrString)) {
            val lineSeparator = "\n"
            val errorReport = StringBuilder()
            errorReport.append("\n***** ANR LOG \n")
            errorReport.append("\n***** DEVICE INFO \n")
            errorReport.append("Brand: ")
            errorReport.append(Build.BRAND)
            errorReport.append(lineSeparator)
            errorReport.append("Device: ")
            errorReport.append(Build.DEVICE)
            errorReport.append(lineSeparator)
            errorReport.append("Model: ")
            errorReport.append(Build.MODEL)
            errorReport.append(lineSeparator)
            errorReport.append("Serial No: ")
            errorReport.append(Build.SERIAL)
            errorReport.append(lineSeparator)
            errorReport.append("Manufacturer: ")
            errorReport.append(Build.MANUFACTURER)
            errorReport.append(lineSeparator)
            errorReport.append("Product: ")
            errorReport.append(Build.PRODUCT)
            errorReport.append(lineSeparator)
            errorReport.append("SDK: ")
            errorReport.append(Build.VERSION.SDK)
            errorReport.append(lineSeparator)
            errorReport.append("Release: ")
            errorReport.append(Build.VERSION.RELEASE)
            errorReport.append(lineSeparator)
            errorReport.append("\n***** APP INFO \n")
            val versionName = BuildConfig.VERSION_NAME
            errorReport.append("Version: ")
            errorReport.append(versionName)
            errorReport.append(lineSeparator)
            val versionCode = BuildConfig.VERSION_CODE
            errorReport.append("Version Code: ")
            errorReport.append(versionCode)
            errorReport.append(lineSeparator)
            try {
                errorReport.append("Endpoint: ")
                errorReport.append(getPrefs().baseUrl)
                errorReport.append(lineSeparator)
            } catch (e : Exception){
                e.printStackTrace()
            }
            val currentDate = Date()
            val dateFormat: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US)
            errorReport.append("Current Date: ")
            errorReport.append(dateFormat.format(currentDate))
            errorReport.append(lineSeparator)
            errorReport.append("\n***** ANR LOG \n")
            errorReport.append(anrString)
            errorReport.append(lineSeparator)
            errorReport.append("\n***** END OF LOG *****\n")
            strCurrentErrorLog = errorReport.toString()

            //Log.e(TAG,"ANR LOG:::::: ${ errorReport}")
            strCurrentErrorLog
        } else {
            strCurrentErrorLog
        }
    }
    private fun emailErrorLog(errorLog:String) {
        val referenceModel = getPrefs().getReferenceModel()
        val emails = if(BuildConfig.DEBUG) {
            "<EMAIL>"
        } else "<EMAIL>;<EMAIL>;<EMAIL>"

        emailConfig = EmailConfig(
            "smtp.gmail.com",
            "465",
            "<EMAIL>",
            "sfcbjhzhvihyuiym",
            emails
        )

        if(referenceModel!=null){
            val config = referenceModel.crashLogEmailConfig
            if(config!=null){
                if(!config.host.isNullOrEmpty() && !config.port.isNullOrEmpty() && !config.username.isNullOrEmpty() && !config.password.isNullOrEmpty() && !config.emailsWithSemicolon.isNullOrEmpty())
                    emailConfig.serverHost = config.host!!
                emailConfig.serverPort = config.port!!
                emailConfig.username = config.username!!
                emailConfig.password = config.password!!
                emailConfig.semicolonSeparatedEmailAddresses = config.emailsWithSemicolon!!
            }
        }

        //if(!BuildConfig.DEBUG){
            CoroutineScope(Dispatchers.IO).launch {   //Background Thread
                val username = emailConfig.username
                val pass =  emailConfig.password
                val subject = <EMAIL>(R.string.app_name) + " Application ANR Log"
                val texts =  getString(R.string.email_anr_welcome_note) + errorLog +""
                val toAddress = emailConfig.semicolonSeparatedEmailAddresses

                if (username.isEmpty()
                    || pass.isEmpty()
                    || subject.isEmpty()
                    || texts.isEmpty()
                    || toAddress.isEmpty()
                ) {
                    Toast.makeText(appContext, "ERROR: Fill all the fields", Toast.LENGTH_LONG).show()
                }

                val tos = StringUtility.extractEmails(toAddress)

                val m = EmailSender(username, pass)
                m._from = username
                m._to = tos
                m._subject = subject
                m.body = texts


                if (m.send()) {
                    Log.e(TAG,"ANR Email sent")
                } else {
                    Log.e(TAG,"ANR Email failed to send.")
                }

                /*// 1 - Create one instance
                val m = MailSender()

                // 2 - Set addressees
                m.setCredentials(username, pass).setToAddresses(tos)

                // 3 - Set the content of the mail
                m.setSubject(subject).setMailText(texts)

                // 4 - Attach files if you want
                //m.attachFile("ErrorLog.log", txtFile!!.absolutePath)

                // 5 - Set properties to use and send
                m.useMailPropertiesGMail().send()
                //m.useMailPropertiesSNMP(emailConfig.serverHost,emailConfig.serverPort.toInt(),emailConfig.serverPort.toInt(),true).send()*/
            }
        /*} else {
            Log.e("Email Sender","------------ ANR log email will send only in release mode ------------")
        }*/
    }



    private val defaultTestFairyKey = "SDK-yI8L1qcQ"
    private fun setupTestFairy(){
        val referenceModel = getPrefs().getReferenceModel()
        val testFairyEnabled = if(referenceModel!=null) referenceModel.enableTestFairy ?: false else true
        val testFairyToken = if(referenceModel!=null) referenceModel.testFairyToken ?: defaultTestFairyKey else defaultTestFairyKey
        if(testFairyEnabled){
            TestFairy.disableCrashHandler()
            TestFairy.begin(this, testFairyToken)
            TestFairy.setUserId(sn)
        }

    }
    var emails = ""

    private lateinit var emailConfig : EmailConfig
    private fun setupExceptionHandler(){

        //Initialize UCE Handler library
        val emails = if(BuildConfig.DEBUG) {
            "<EMAIL>"
        } else "<EMAIL>;<EMAIL>;<EMAIL>"

        val referenceModel = getPrefs().getReferenceModel()
        emailConfig = EmailConfig(
            "smtp.gmail.com",
            "465",
            "<EMAIL>",
            "sfcbjhzhvihyuiym",
            emails
        )

        if(referenceModel!=null){
            val config = referenceModel.crashLogEmailConfig
            if(config!=null){
                if(!config.host.isNullOrEmpty() && !config.port.isNullOrEmpty() && !config.username.isNullOrEmpty() && !config.password.isNullOrEmpty() && !config.emailsWithSemicolon.isNullOrEmpty())
                    emailConfig.serverHost = config.host!!
                    emailConfig.serverPort = config.port!!
                    emailConfig.username = config.username!!
                    emailConfig.password = config.password!!
                    emailConfig.semicolonSeparatedEmailAddresses = config.emailsWithSemicolon!!
            }
        }

        UCEHandler.Builder(applicationContext)
            .setTrackActivitiesEnabled(true)
            .addEmailConfig(emailConfig)
            //.addSemicolonSeparatedEmailAddresses("<EMAIL>,<EMAIL>,<EMAIL>")
            .build()
    }


    private fun setUpPrinterConfiguration()
    {
        val xLogFolder: File = File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS), "XLogFile")

        val config: LogConfiguration = LogConfiguration.Builder()
            .logLevel(LogLevel.ALL) // Specify log level, logs below this level won't be printed, default: LogLevel.ALL
            .tag(BuildConfig.APPLICATION_ID) // Specify TAG, default: "X-LOG"
            .build()

        val androidPrinter: Printer = AndroidPrinter() // Printer that print the log using android.util.Log
        val consolePrinter: Printer = ConsolePrinter() // Printer that print the log to console using System.out

        val filePrinter: Printer =
            FilePrinter.Builder(xLogFolder.path) // Printer that print the log to the file system
                // Specify the path to save log file
                .fileNameGenerator(DateFileNameGenerator()) // Default: ChangelessFileNameGenerator("log")
                .backupStrategy(NeverBackupStrategy()) // Default: FileSizeBackupStrategy(1024 * 1024)
                .build()
        XLog.init( // Initialize XLog
            config,  // Specify the log configuration, if not specified, will use new LogConfiguration.Builder().build()
            androidPrinter,  // Specify printers, if no printer is specified, AndroidPrinter(for Android)/ConsolePrinter(for java) will be used.
            consolePrinter,
            filePrinter
        )
        // eof Xlog

        /*// B TPE
        if (BuildConfig.POS_TYPE == "B_TPE") {
            object : Thread() {
                override fun run() {
                    mCore = Core(applicationContext)
                }
            }.start()
        }*/
    }
    private fun setupFireBase()
    {
        FirebaseApp.initializeApp(this)
        createNotificationChannel()
        UploadServiceConfig.initialize(this, notificationChannelID, BuildConfig.DEBUG)
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= 26) {
            val channel = NotificationChannel(
                notificationChannelID,
                "TestApp Channel",
                NotificationManager.IMPORTANCE_MAX
            )
            val manager = getSystemService(NOTIFICATION_SERVICE) as NotificationManager
            manager.createNotificationChannel(channel)
        }
    }
    private fun setUpPosType()
    {
        try {
            //val prefs: AppPreferencesHelper = get()
            //val pref = AppPreferencesHelper(context!!.getSharedPreferences(AppConstant.PREF_NAME, MODE_PRIVATE))s
            when (BuildConfig.POS_TYPE) {
                "B_TPE" -> {
                    val easyDeviceMod = EasyDeviceMod(this)
                    sn = easyDeviceMod.serial

               /* if(BuildConfig.DEBUG){
                    sn = "PP35271805004718"
                }*/

                    deviceName = easyDeviceMod.device
                }
                "PAX" -> {
                    dal = getDal()
                    if (SysTester.getInstance() != null) sn = SysTester.getInstance().snPax
                    /*if(BuildConfig.DEBUG)
                       sn ="PP35271805004718"*/
                    deviceName = SysTester.getInstance().model
                }
                else -> bindSdkLANDIDeviceService()
            } // Binding LANDI POS SDK
        }
        catch (e:Exception)
        {
            e.printStackTrace()
            showToast("Invalid POS, No access for this terminal")
        }
    }

    // LANDI POS
    private fun bindSdkLANDIDeviceService() {
        val intent = Intent()
        intent.action = "com.usdk.apiservice"
        intent.setPackage("com.usdk.apiservice")
        Log.d(TAG, "binding sdk device service...")
        val flag = bindService(intent, connection!!, BIND_AUTO_CREATE)
        if (!flag) {
            Log.d(TAG, "SDK service binding failed.")
            return
        }
        Log.d(TAG, "SDK service binding successfully.")
    }
    private var connection: ServiceConnection? = object : ServiceConnection {
        override fun onServiceDisconnected(name: ComponentName) {
            Log.d(TAG, "SDK service disconnected.")
            unbindService(this)
        }

        override fun onServiceConnected(name: ComponentName, service: IBinder) {
            Log.d(TAG, "SDK service connected.")

            // Get device service object
            deviceService = UDeviceService.Stub.asInterface(service)
            try {
                deviceService!!.register(null, service)
                deviceService!!.setLogLevel(
                    com.usdk.apiservice.aidl.constants.LogLevel.EMVLOG_REALTIME,
                    com.usdk.apiservice.aidl.constants.LogLevel.USDKLOG_VERBOSE
                )
                // Get LED device object
                val led: ULed = ULed.Stub.asInterface(
                    deviceService!!.getLed(Bundle())
                )
                getBeeper()!!.startBeep(200)
                // Turn on the red light
                led.turnOn(Light.RED)
                sn = getSerialNo()
                Log.i(TAG, "LANDI SN : $sn")
            } catch (e: RemoteException) {
                e.printStackTrace()
            }
        }
    }


    @Throws(RemoteException::class)
    fun getBeeper(): UBeeper? {
        if (beeper == null) {
            beeper = UBeeper.Stub.asInterface(deviceService!!.beeper)
        }
        return beeper
    }

    @Throws(RemoteException::class)
    fun getSerialNo(): String? {
        return getDeviceManager()!!.deviceInfo.serialNo
    }

    @Throws(RemoteException::class)
    fun getDeviceManager(): UDeviceManager? {
        if (deviceManager == null) {
            deviceManager = UDeviceManager.Stub.asInterface(deviceService!!.deviceManager)
        }
        return deviceManager
    }

}
fun setBeeper(beeper: UBeeper?) {
    MainApp.beeper = beeper
}