package app.rht.petrolcard.networkRequest;




import android.content.Context;

import com.readystatesoftware.chuck.ChuckInterceptor;

import java.util.concurrent.TimeUnit;

import app.rht.petrolcard.utils.constant.AppConstant;
import app.rht.petrolcard.utils.interceptor.AccessTokenInterceptor;
import app.rht.petrolcard.utils.interceptor.AuthInterceptor;
import app.rht.petrolcard.utils.mpesaservices.STKPushService;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;




/**
 * API Client helper class used to configure Retrofit object.
 *
 * <AUTHOR>
 */

public class ApiClient {

    private Retrofit retrofit;
    private boolean isDebug;
    private boolean isGetAccessToken;
    private String mAuthToken;
    private String consumer_key;
    private String consumer_secret;
    private final HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();

    /**
     * Set the {@link Retrofit} log level. This allows one to view network traffic.
     *
     * @param isDebug If true, the log level is set to
     *                {@link HttpLoggingInterceptor.Level#BODY}. Otherwise
     *                {@link HttpLoggingInterceptor.Level#NONE}.
     */
    public ApiClient setIsDebug(boolean isDebug) {
        this.isDebug = isDebug;
        return this;
    }

    /**
     * Helper method used to set the authenication Token
     *
     * @param authToken token from api
     */
    public ApiClient setAuthToken(String authToken) {
        mAuthToken = authToken;
        return this;
    }

    /**
     * Helper method used to determine if get token enpoint has been invoked. This should be called
     * only when requesting of an accessToken
     *
     * @param getAccessToken {@link Boolean}
     */
    public ApiClient setGetAccessToken(boolean getAccessToken) {
        isGetAccessToken = getAccessToken;
        return this;
    }

    /**
     * Configure OkHttpClient
     *
     * @return OkHttpClient
     */
    private OkHttpClient.Builder okHttpClient(Context ctx) {
        OkHttpClient.Builder okHttpClient = new OkHttpClient.Builder();
        okHttpClient
                .connectTimeout(AppConstant.CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(AppConstant.WRITE_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(AppConstant.READ_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(new ChuckInterceptor(ctx).showNotification(true))
                .addInterceptor(httpLoggingInterceptor);


        return okHttpClient;
    }

    /**
     * Return the current {@link Retrofit} instance. If none exists (first call, API key changed),
     * builds a new one.
     * <p/>
     * When building, sets the endpoint and a {@link HttpLoggingInterceptor} which adds the API key as query param.
     */
    private Retrofit getRestAdapter(Context ctx) {

        Retrofit.Builder builder = new Retrofit.Builder();
        builder.baseUrl(AppConstant.BASE_URL_MPESA_TEST);
        builder.addConverterFactory(GsonConverterFactory.create());

        if (isDebug) {
            httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        }

        OkHttpClient.Builder okhttpBuilder = okHttpClient(ctx);

        if (isGetAccessToken) {
            okhttpBuilder.addInterceptor(new AccessTokenInterceptor(consumer_key,consumer_secret));
        }

        if (mAuthToken != null && !mAuthToken.isEmpty()) {
            okhttpBuilder.addInterceptor(new AuthInterceptor(mAuthToken));
        }

        builder.client(okhttpBuilder.build());

        retrofit = builder.build();

        return retrofit;
    }

    /**
     * Create service instance.
     *
     * @return STKPushService Service.
     */
    public STKPushService mpesaService(Context ctx) {
        return getRestAdapter(ctx).create(STKPushService.class);
    }
    public ApiClient setConsumerCredentials(String mConsumer_key,String mConsumer_secret) {
        consumer_key = mConsumer_key;
        consumer_secret = mConsumer_secret;
        return this;
    }
}
