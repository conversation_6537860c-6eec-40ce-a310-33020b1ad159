package app.rht.petrolcard.networkRequest;

import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Objects;

import javax.inject.Inject;
import javax.inject.Singleton;

import app.rht.petrolcard.BuildConfig;
import app.rht.petrolcard.utils.AppPreferencesHelper;
import okhttp3.HttpUrl;
import okhttp3.Interceptor;
import okhttp3.Request;

/*
  Solution from @swankjesse
  Host Selection Retrofit
  More at @link https://github.com/square/retrofit/issues/1404#issuecomment-*********
*/
@Singleton
public class HostSelectionInterceptor implements Interceptor {
    AppPreferencesHelper preferenceHelper;
    private volatile HttpUrl host = HttpUrl.parse(NetworkRequestEndPoints.BASE_URL);

    @Inject
    public HostSelectionInterceptor(AppPreferencesHelper preferenceHelper){
      this.preferenceHelper = preferenceHelper;
      setHostBaseUrl(preferenceHelper.getBaseUrl());
    }

    public void setHostBaseUrl(String url) {
      this.host = HttpUrl.parse(url);
    }

    @NotNull
    @Override
    public okhttp3.Response intercept(Chain chain) throws IOException {

        Request request = chain.request();
        if (host != null) {
            HttpUrl newUrl = null;
            try {
                newUrl = request.url().newBuilder()
                        .scheme(host.scheme())
                        .host(host.url().toURI().getHost())
                        .build();
            } catch (URISyntaxException e) {
                e.printStackTrace();
            }
            assert newUrl != null;
            request = request.newBuilder()
                    .url(newUrl)
                    .build();
        }
        return chain.proceed(request);
    }
}