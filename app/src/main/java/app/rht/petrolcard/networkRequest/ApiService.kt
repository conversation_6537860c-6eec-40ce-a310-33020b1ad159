package app.rht.petrolcard.networkRequest

import ReplaceCardResponse
import app.rht.petrolcard.apimodel.apiresponsel.BaseResponse
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketRequest
import app.rht.petrolcard.ui.qrcodeticket.model.QRCodeTicketResponse
import app.rht.petrolcard.service.model.MainResponseGetIP
import app.rht.petrolcard.service.model.ResponseIPContentu
import app.rht.petrolcard.ui.iccpayment.model.ActivationDetails
import app.rht.petrolcard.ui.iccpayment.model.DiscountResponse
import app.rht.petrolcard.ui.iccpayment.model.StoreCardDataModel
import app.rht.petrolcard.ui.loyalty.model.*
import app.rht.petrolcard.ui.modepay.model.MtnPayAccessTokenResponse
import app.rht.petrolcard.ui.modepay.model.MtnRequestToPayBody
import app.rht.petrolcard.ui.modepay.model.MtnResponseModel
import app.rht.petrolcard.ui.reference.model.*
import app.rht.petrolcard.ui.settings.appupdate.model.UpdateResponse
import app.rht.petrolcard.ui.settings.card.unblockcard.model.UnblockResponseModel
import app.rht.petrolcard.ui.updatecard.model.CardDetailsResponse
import okhttp3.MultipartBody
import okhttp3.RequestBody
import okhttp3.ResponseBody
import retrofit2.Call
import retrofit2.http.*
import rx.Observable

interface ApiService  {
    companion object {
        const val PRIORITY_DEFAULT = 0
        const val PRIORITY_HIGH = 1
        const val PRIORITY_MEDIUM = 2
        const val PRIORITY_LOW = 3
    }

    @FormUrlEncoded
    @POST
    fun getURLInitial(
        @Url url: String,
        @Field("sn") sn: String
    ): Observable<BaseResponse<BaseUrlModel>>

    @POST
    fun getMasterFusionIp(
        @Url url: String,
        @Query("sn") sn: String?
    ): Observable<BaseResponse<ResponseIPContentu>>

    //@POST(NetworkRequestEndPoints.GET_ALL_REFERENCE_DATA)
    @POST
    fun getAllReferenceData(
        @Url url: String,
        @Query("sn") sn: String,
        @Query("blacklist") blackListVersion: String,
        @Query("greylist") greyListVersion: String,
        @Query("language") language: String,/* = "en"*/

    ): Observable<BaseResponse<ReferenceModel>>

    @POST("Api/GetMasterTerminalIP")
    fun getMasterFusionIp(
        @Query("sn") sn: String?
    ): Call<MainResponseGetIP?>?

    
    //@Streaming
    @GET
    fun downloadLogo(@Url fileUrl: String?): Observable<ResponseBody?>

    @Multipart
    @POST
    fun sendTeleCollectFile(
        @Url url: String,
        @Part file: MultipartBody.Part?,
        @Part("sn") sn: RequestBody?,
        @Part("key") key: RequestBody?,
        @Part("hash") hash: RequestBody?
    ): Observable<BaseResponse<String>>

    @FormUrlEncoded
    @POST
    fun sendLastTrx(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("dth") dth: String?,
        @Field("version") version: String?
    ): Observable<BaseResponse<String>>

    @FormUrlEncoded
    @POST
    fun rechargeCard(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?,
        @Field("mnt") montant: String?
    ): Observable<BaseResponse<String>>

    @FormUrlEncoded
    @POST
    fun updateCard(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?,
        @Field("status") statusCarte: String?
    ): Observable<BaseResponse<CardDetailsResponse>>

    @FormUrlEncoded
    @POST
    fun storeCardData(
        @Url url: String,
        @Body storeCardDataModel: StoreCardDataModel
    ): Observable<BaseResponse<String>>

  @FormUrlEncoded
  @POST
    fun injectKeyImport(
        @Url url: String,
        @Field("sn") sn: String?
    ): Observable<InjectKeyResponseModel>


    @FormUrlEncoded
    @POST
    fun activationDetails(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?
    ): Observable<BaseResponse<ActivationDetails>>

    @FormUrlEncoded
    @POST
    fun getCategories(
        @Url url: String,
        @Field("sn") sn: String?
    ): Observable<BaseResponse<List<String>>>

    @FormUrlEncoded
    @POST
    fun getLoyaltyProducts(
        @Url url: String,
        @Field("sn") sn: String?
    ): Observable<BaseResponse<List<LoyaltyProduct>>>

    @FormUrlEncoded
    @POST
    fun getStations(
        @Url url: String,
        @Field("sn") sn: String?
    ): Observable<BaseResponse<List<Station>>>

    @POST
    fun getLetters(
        @Url url: String,
    ): Observable<BaseResponse<List<Letters>>>


    @FormUrlEncoded
    @POST
    fun updateCardNotification(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?,
        @Field("dt") dt: String?
    ): Observable<BaseResponse<String>>

    @FormUrlEncoded
    @POST
    fun rechargeMassValidate(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?,
        @Field("id") id: Int,
        @Field("checksum") checksum: String?
    ): Observable<BaseResponse<String>>

    @FormUrlEncoded
    @POST
    fun updateCreditStatus(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?,
        @Field("id") id: Int,
        @Field("checksum") checksum: String?,
        @Field("status") status: Int?
    ): Observable<BaseResponse<String>>

    @Multipart
    @POST
    fun getLoyaltyCustomer(
        @Url url: String,
        @Part("sn") sn: RequestBody?,
        @Part("pan") pan: RequestBody?
    ): Observable<BaseResponse<List<LoyaltyBalance>>>

    @Multipart
    @POST
    fun getGiftList(
        @Url url: String,
        @Part("sn") sn: RequestBody?,
        @Part("pan") pan: RequestBody?
    ): Observable<BaseResponse<List<LoyaltyGift>>>

    @Multipart
    @POST
    fun getGiftHistoryList(
        @Url url: String,
        @Part("sn") sn: RequestBody?,
        @Part("pan") pan: RequestBody?
    ): Observable<BaseResponse<List<LoyaltyGiftHistory>>>

    @Multipart
    @POST
    fun generateGiftToken(
        @Url url: String,
        @Part("sn") sn: RequestBody?,
        @Part("pan") pan: RequestBody?,
        @Part("id_gift") idGift: RequestBody?
    ): Observable<BaseResponse<GiftTokenResponse>>

    @Multipart
    @POST
    fun redeemGift(
        @Url url: String,
        @Part("sn") sn : RequestBody,
        @Part("pan") pan : RequestBody,
        @Part("token") token : RequestBody
    ): Observable<BaseResponse<String>>

    @Multipart
    @POST
    fun getLoyaltyCardDetails(
        @Url url: String,
        @Part("sn") sn : RequestBody,
        @Part("cin") driverId : RequestBody
    ): Observable<BaseResponse<LoyaltyCardDetailsModel>>


    @Multipart
    @POST
    fun replaceLoyaltyCard(
        @Url url: String,
        @Part("sn") sn : RequestBody,
        @Part("pan") driverIdDoc : RequestBody,
        @Part("newpan") vehicleRegId : RequestBody,
        @Part oldCardNumber : MultipartBody.Part,
        @Part newCardNumber : MultipartBody.Part,
    ): Observable<BaseResponse<ReplaceCardResponse>>


    @Multipart
    @POST
    fun loyaltyActivation(
        @Url url: String,
        @Part cin_file : MultipartBody.Part?,
        @Part souche_file : MultipartBody.Part?,
        @Part carte_grise_file : MultipartBody.Part?,
        @Part("sn")  sn: RequestBody?,
        @Part("key")  hashKey: RequestBody?,
        @Part("cin")  cin: RequestBody?,
        @Part("nom")  nom: RequestBody?,
        @Part("prenom")  prenom: RequestBody?,
        @Part("date_naissance")  date_naissance: RequestBody?,
        @Part("lieu_naissance")  lieu_naissance: RequestBody?,
        @Part("tel")  tel: RequestBody?,
        @Part("adresse")  adresse: RequestBody?,
        @Part("ville")  ville: RequestBody?,
        @Part("num_permis")  num_permis: RequestBody?,
        @Part("categorie")  categorie: RequestBody?,
        @Part("pan")  pan: RequestBody?,
        @Part("station")  station: RequestBody?,
        @Part("matricule")  matricule: RequestBody?,
        @Part("num_carte_grise")  num_carte_grise: RequestBody?,
        @Part("agrement")  agrement: RequestBody?,
        @Part("cv_fiscaux")  cv_fiscaux: RequestBody?,
        @Part("carburant")  carburant: RequestBody?,
        @Part("tag")  tag: RequestBody?,
        @Part("badge")  badge: RequestBody?,
        @Part("version")  version : RequestBody?,
        @Header("connection") connection: RequestBody?
    ): Observable<BaseResponse<String>>

    @FormUrlEncoded
    @POST
    fun unBlockCard(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?
    ): Observable<BaseResponse<UnblockResponseModel>>

    @FormUrlEncoded
    @POST
    fun unlockCard(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?
    ): Observable<BaseResponse<String>>


    @FormUrlEncoded
    @POST
    fun unlockCardNotification(
        @Url url: String,
        @Field("sn") sn: String?,
        @Field("pan") pan: String?,
        @Field("dt") date: String?
    ): Observable<BaseResponse<String>>

    @POST
    fun getAppUpdateDetails(
        @Url url: String,
        @Query("build_type") build_type: String?
    ): Observable<BaseResponse<UpdateResponse>>

    @FormUrlEncoded
    @POST
    fun authorizeDiscount(
        @Url url: String,
        @Field("panNumber") pan: String?,
        @Field("amount") amount: String?,
        @Field("sn") sn: String?
    ): Observable<BaseResponse<DiscountResponse>>

    @FormUrlEncoded
    @POST
    fun getDiscountBalance(
        @Url url: String,
        @Field("panNumber") pan: String?,
        @Field("sn") sn: String?
    ): Observable<BaseResponse<DiscountResponse>>

    @POST
    fun sendTransactionOnline(
        @Url url: String,
        @Body mTaxis: SendTransactionModel,
    ): Observable<BaseResponse<String>>


    @Multipart
    @POST
    fun uploadLogFiles(
        @Url url: String,
        @Part("sn") sn: RequestBody?,
        @Part("nbr") nbr:RequestBody?,
        @Part("key") key:RequestBody?,
        @Part file : MultipartBody.Part,
    ): Observable<BaseResponse<String>>

    @GET
    fun restartFusionTcpService(
        @Url url: String,
    ): Observable<String>

    @Multipart
    @POST
    fun offlinePhotoUpload(
        @Url url: String,
        @Part file: MultipartBody.Part?,
        @Part("sn") sn: RequestBody?,
        @Part("key") hashkey: RequestBody?,
        @Part("name") name: RequestBody?
    ): Observable<BaseResponse<String>>

    @POST
    fun getAllDiscountDetails(
        @Url url: String,
        @Query("sn") sn: String?
    ): Observable<DiscountBaseResponse>

    @Multipart
    @POST
    fun sendLogFiles(
        @Url url: String,
        @Part("sn") sn: RequestBody?,
        @Part("nbr") nbr: RequestBody?,
        @Part("key") hashkey: RequestBody?,
        @Part("message") message: RequestBody?,
        @Part("is_error_log") is_error_log : RequestBody?,
        @Header("Connection") connection: String,
        @Part file: MultipartBody.Part?
    ): Observable<BaseResponse<String>>


    @POST
    fun getMtnPayAccessToken(
        @Url url: String,
        @Header("Authorization") authorization:String,
        @Header("Ocp-Apim-Subscription-Key") subscriptionKey:String
    ): Observable<MtnPayAccessTokenResponse>

    @POST
    fun mtnRequestToPay(
        @Url url: String,
        @Header("Authorization") authorization:String,
        @Header("Ocp-Apim-Subscription-Key") subscriptionKey:String,
        @Header("X-Reference-Id") referenceID:String,
        @Header("X-Target-Environment") targetEnvironment:String,
        @Header("Connection") connection: String,
        @Body body: MtnRequestToPayBody
    ): Observable<Void>

    @GET
    fun getMtnReferenceID(
        @Url url: String,
        @Header("Connection") connection: String
    ):Observable<String>

    @GET
    fun getMTNPayResponseStatus(
        @Url url: String,
        @Header("Connection") connection: String,
        @Header("Authorization") authorization:String,
        @Header("Ocp-Apim-Subscription-Key") subscriptionKey:String,
        @Header("X-Target-Environment") targetEnvironment:String
    ): Observable<MtnResponseModel>


    @POST
    fun generateQrCodeTicket(
        @Url url: String,
        @Body QRCodeTicketRequest: QRCodeTicketRequest
    ): Observable<QRCodeTicketResponse>


}