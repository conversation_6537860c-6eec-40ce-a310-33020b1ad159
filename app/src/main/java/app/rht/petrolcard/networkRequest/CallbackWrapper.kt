package app.rht.petrolcard.networkRequest

import android.util.Log
import app.rht.petrolcard.MainApp
import com.google.gson.Gson
import app.rht.petrolcard.apimodel.apiresponsel.CommonResponseModel
import app.rht.petrolcard.utils.LogWriter
import org.json.JSONException
import org.json.JSONObject
import retrofit2.HttpException
import rx.Subscriber
import java.net.ConnectException

/**
 * Will handel all api response and error
 */
abstract class CallbackWrapper<T> : Subscriber<T>() {
    abstract fun onSuccess(t: T)
    abstract fun onError(message: String?, statusCode: Int?)
    override fun onCompleted() {}
    override fun onError(e: Throwable) {
        if(e.message != null) {
            when {
                e.message!!.contains("No Internet Connection") -> {
                    onError(e.message, -500)
                }
                e.message!!.contains("No address associated with hostname") -> {
                    onError(e.message, -300)
                }
                e.message!!.contains("Failed to connect") -> {
                    onError(e.message, -400)
                }
                else -> {
                    handleError(e)

                }
           }
        }
    }

    private fun handleError(error: Throwable) {
        try {
            error.printStackTrace()
            when (error) {
                is HttpException -> {
                    try {
                        when (error.code()) {
                            500 -> {
                                onError("Server Configuration Issue - 500",500)
                            }
                            404 -> {
                                onError("Server Configuration Issue - 404",404)
                            }
                            else -> {
                                val gsonInstance = Gson()
                                val errorBody = gsonInstance.fromJson(error.response()?.errorBody()?.charStream(), CommonResponseModel::class.java)
                                onError(errorBody.message,error.code())
                            }
                        }
                    } catch (exception: Exception) {
//                        SnackbarUtils.somethingWentWrong(view)
                        onError("Failed to connect server, please check terminal network",error.code())
                        exception.printStackTrace()
                    }
                }
                is ConnectException ->  onError("Server is in maintenance mode. it will back to normal mode within some time. please wait.",-300)
             else -> try { if(error.message != null && error.message!!.contains("timeout")) onError("Not able connect with the server - Timeout",-200) else onError("Server Configuration Issue",-200) }
             catch (e:Exception) {
                 e.printStackTrace()
                 onError("Server Configuration Issue",-200)
             }
            }
        } catch (e: Exception) {
            e.printStackTrace()
            onError("Failed to connect server, please check terminal network",-300)
        }
    }

    override fun onNext(t: T) {
       // val errorCheckModel = checkError(t)
        onSuccess(t)
      /*  try {
            if(t != null && t != "")
            {
                val logWriter = LogWriter("ApiResponse")
                logWriter.appendLog(TAG, "API LOG $t")
                logWriter.appendLog(TAG, "API LOG %${Gson().toJson(t)}")
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        } Commented this create more log file due to storage issue*/


        //  if (errorCheckModel.status) {

        /* } else {
            onError(errorCheckModel.message,errorCheckModel.statusCode);
        }*/
    }

    private fun checkError(data: Any): ErrorCheckModel {
        var errorCheckModel = ErrorCheckModel()
        try {
            var objectv = JSONObject(Gson().toJson(data))
            errorCheckModel.message = objectv.getString("message")
            errorCheckModel.status = objectv.getBoolean("status")
            errorCheckModel.statusCode = objectv.getInt("statusCode")
        } catch (e: JSONException) {
        }
        return errorCheckModel
    }

    private inner class ErrorCheckModel {
        var status = false
        var statusCode = 0
        var message = ""
    }

    companion object {
        private val TAG = CallbackWrapper::class.java.simpleName
    }
}