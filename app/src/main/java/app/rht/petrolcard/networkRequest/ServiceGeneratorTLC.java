package app.rht.petrolcard.networkRequest;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.readystatesoftware.chuck.ChuckInterceptor;

import java.util.concurrent.TimeUnit;

import app.rht.petrolcard.BuildConfig;
import okhttp3.CertificatePinner;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

public class ServiceGeneratorTLC {

    //OkHttpClient httpClient;
    private final OkHttpClient.Builder httpClient;
    private final Context context ;
    private final Gson gson;
    private Retrofit.Builder builder;

    public ServiceGeneratorTLC(Context ctx) {
        this.context = ctx ;
        httpClient = new OkHttpClient.Builder();
        this.gson = new GsonBuilder().setLenient().create();
    }

    public <S> S createService(Class<S> serviceClass, String server) {
        this.builder = new Retrofit.Builder()
                .baseUrl(server)
                .addConverterFactory(GsonConverterFactory.create(gson));
        return retrofit().create(serviceClass);
    }

    public<S> S createService(Class<S> serviceClass) {
        this.builder = new Retrofit.Builder()
                .addConverterFactory(GsonConverterFactory.create(gson));
        return retrofit().create(serviceClass);
    }

    private Retrofit retrofit() {

        if (BuildConfig.DEBUG) {
            HttpLoggingInterceptor httpLoggingInterceptor = new HttpLoggingInterceptor();
            httpLoggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
            httpClient.addInterceptor(httpLoggingInterceptor);

            ChuckInterceptor chuckInterceptor = new ChuckInterceptor(context);
            chuckInterceptor.showNotification(true);
            httpClient.addInterceptor(chuckInterceptor);

        }
        // SSL pining
        CertificatePinner certificatePinner = new CertificatePinner.Builder()
                .add("getserver.fuelbusinesssuite.com", "sha256/CHmPoNU9rDMm62uFRtFbqupMnzE6xX27UQJWv+kINnQ=",
                                                                        "sha256/FEzVOUp4dF3gI0ZVPRJhFbSJVXR+uQmMH65xhs1glH4=",
                                                                        "sha256/Y9mvm0exBk1JoQ57f9Vm28jKo5lFm/woKcVxrYxu80o=")
                .build();
        httpClient.certificatePinner(certificatePinner);
        httpClient.readTimeout(1, TimeUnit.MINUTES)
                .addInterceptor(new ChuckInterceptor(context).showNotification(true))
                .addInterceptor(new HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
                .connectTimeout(1, TimeUnit.MINUTES)
                .writeTimeout(1, TimeUnit.MINUTES)

                .build();

        return builder.client(this.httpClient.build()).build();
    }
} // endOf ServiceGenerator class
