package app.rht.petrolcard.networkRequest

import androidx.multidex.MultiDexApplication
import app.rht.petrolcard.MainApp
import app.rht.petrolcard.utils.AppPreferencesHelper
import app.rht.petrolcard.utils.constant.AppConstant

object NetworkRequestEndPoints {
    const val BASE_URL = "https://getserver.fuelbusinesssuite.com/whoami.php/"
    const val INJECT_KEY_BASE_URL = "http://key.fuelbusinesssuite.com/keymaster/index.php"
    const val MTNPAY_ACCESSTOKEN = "token/"
    const val MTNPAY_REQUEST_DATA= "v1_0/requesttopay"
/*    val SEND_TELECOLLECT_FILE = Telecollecte()
    val SEND_TRANSACTION_ONLINE = sendTransactionsOnline()
     val GET_DISCOUNT_BALANCE = getDiscountBalance()
     val AUTHORIZE_DISCOUNT = authorizeDiscount()
     val GET_ALL_REFERENCE_DATA = getReferenceData()
     val UPDATE_CARD = updateCard()*/
    const val GET_ALL_REFERENCE_DATA = "Api/ParametrageTPE"
    const val UPDATE_CARD = "Api/ParametrageCarte"
//    const val SEND_TELECOLLECT_FILE = "Api/v348/Telecollecte"
//    const val AUTHORIZE_DISCOUNT = "Api/v348/authorizeDiscount"
//    const val GET_DISCOUNT_BALANCE = "Api/v348/getDiscountBalance"
//    const val SEND_TRANSACTION_ONLINE = "Api/v348/sendTransactionsOnline"
    const val SEND_TELECOLLECT_FILE = "Api/Telecollecte"
    const val AUTHORIZE_DISCOUNT = "Api/authorizeDiscount"
    const val GET_DISCOUNT_BALANCE = "Api/getDiscountBalance"
    const val SEND_TRANSACTION_ONLINE = "Api/sendTransactionsOnline"
    const val RECHARGE_CARD = "Api/RechargeCarte"
    const val UPDATE_CARD_NOTIFICATION = "Api/ParametrageCarteNotification"
    const val RECHARGE_MASS_VALIDATE = "Api/RechargeMassValidate"
    const val UPDATE_CREDIT_STATUS = "Api/updateCreditStatus"
    const val GET_LOYALTY_BALANCE = "api/loyalty/get-customers-solde"
    const val GET_LOYALTY_GIFTS = "api/loyalty/get-list-gift"
    const val GET_LOYALTY_GIFTS_HISTORY = "api/loyalty/get-historique-gift"
    const val GET_REDEEM_GIFT = "api/loyalty/post-gift"
    const val LOYALTY_CATEGORIES = "api/Categories?key=123"
    const val LOYALTY_PRODUCTS = "api/Carburants?key=123"
    const val LOYALTY_ACTIVATE = "api/loyalty/New"
    const val LOYALTY_UPDATE = "api/loyalty/update"
    const val GET_LETTERS = "api/Lettres"
    const val GET_ACTIVATION_DETAILS = "api/loyalty/activation-details"
    const val GET_LOYALTY_CARD_DETAILS = "api/loyalty/get-pan"
    const val GET_REPLACE_LOYALTY_CARD = "api/loyalty/get-changement-cart"
    const val REDEEM_GIFT = "api/loyalty/get-gift"
    const val UNLOCK_CARD = "Api/DeverouillageCarte"
    const val UNBLOCK_CARD_NOTIFICATION = "Api/DeverouillageCarteNotification"
    const val UNLOCK_CARD_PIN = "Api/DeverouillagePin"
    const val UNLOCK_CARD_NOTIFICATION = "Api/DeverouillagePinNotification"
    const val GET_APP_UPDATE_DETAILS = "Api/getAppUpdateDetails"
    const val GET_RFID_MASTER_IP = "Api/GetMasterTerminalIP"
    const val OFFLINEUPLOAD = "Api/OfflineUpload"
    const val GET_DISCOUNT_DETAILS = "Api/getAllDiscounts"
    const val SENDLOGFILES = "Api/FUELPOS/SyncFiles"
    const val VERIFY_PIN = "/pin"
    const val CREATE_INVOICE = "/invoices"
    const val ACTIVATION_DETAILS = "api/loyalty/activation-details"
}
/*
const val GET_ALL_REFERENCE_DATA = "Api/ParametrageTPE"
const val UPDATE_CARD = "Api/ParametrageCarte"
const val SEND_TELECOLLECT_FILE = "Api/v348/Telecollecte"
const val AUTHORIZE_DISCOUNT = "Api/v348/authorizeDiscount"
const val GET_DISCOUNT_BALANCE = "Api/v348/getDiscountBalance"
const val SEND_TRANSACTION_ONLINE = "Api/v348/sendTransactionsOnline"
val pref = AppPreferencesHelper(MainApp.appContext.getSharedPreferences(AppConstant.PREF_NAME, MultiDexApplication.MODE_PRIVATE))

    fun Telecollecte():String
    {
        val endPointName = SEND_TELECOLLECT_FILE
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.Telecollecte!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
    fun sendTransactionsOnline():String
    {
         val endPointName = SEND_TRANSACTION_ONLINE
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.sendTransactionsOnline!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
    fun getDiscountBalance():String
    {
        val endPointName = GET_DISCOUNT_BALANCE
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getDiscountBalance!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
    fun authorizeDiscount():String
    {
        val endPointName = AUTHORIZE_DISCOUNT
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.authorizeDiscount!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
    fun getReferenceData():String
    {
        val endPointName = GET_ALL_REFERENCE_DATA
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getReferenceData!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
    fun updateCard():String
        {
            val endPointName = UPDATE_CARD
            try {
                if(pref.getApiEndpoints() != null)
                {
                    return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.updateCard!!
                }
            }
            catch (e:Exception)
            {
                e.printStackTrace()
            }
            return endPointName
        }
*/

/*
  fun rechargeCard():String
    {
        val endPointName = RECHARGE_CARD
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.rechargeCard!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
    fun updateCardNotification():String
    {
        val endPointName = UPDATE_CARD_NOTIFICATION
        try {
            if(pref.getApiEndpoints() != null)
            {
                return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.updateCardNotification!!
            }
        }
        catch (e:Exception)
        {
            e.printStackTrace()
        }
        return endPointName
    }
fun getLoyaltyActivationDetails():String
{
    val endPointName = GET_ACTIVATION_DETAILS
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getLoyaltyActivationDetails!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun getLetters():String
{
    val endPointName = GET_LETTERS
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getLetters!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun loyaltyUpdate():String
{
    val endPointName = LOYALTY_UPDATE
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.loyaltyUpdate!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun loyaltyActivate():String
{
    val endPointName = LOYALTY_ACTIVATE
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.loyaltyActivate!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun loyaltyProducts():String
{
    val endPointName = LOYALTY_PRODUCTS
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.loyaltyProducts!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun loyaltyCategories():String
{
    val endPointName = LOYALTY_CATEGORIES
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.loyaltyCategories!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun getRedeemGift():String
{
    val endPointName = GET_REDEEM_GIFT
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getRedeemGift!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun getLoyaltyGiftsHistory():String
{
    val endPointName = GET_LOYALTY_GIFTS_HISTORY
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getLoyaltyGiftsHistory!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun getLoyaltyGifts():String
{
    val endPointName = GET_LOYALTY_GIFTS
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getLoyaltyGifts!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}
fun getLoyaltyBalance():String
{
    val endPointName = GET_LOYALTY_BALANCE
    try {
        if(pref.getApiEndpoints() != null)
        {
            return MainApp.getPrefs().getReferenceModel()!!.api_end_points!!.getLoyaltyBalance!!
        }
    }
    catch (e:Exception)
    {
        e.printStackTrace()
    }
    return endPointName
}*/


