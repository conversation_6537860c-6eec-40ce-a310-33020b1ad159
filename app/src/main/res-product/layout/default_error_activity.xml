<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="16dp">

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fadeScrollbars="false"
            android:scrollbars="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical">


                <ImageView
                    android:layout_width="180dp"
                    android:layout_height="180dp"
                    android:src="@drawable/exception_handler_icon"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:text="@string/ask_for_error_log"
                    android:textColor="#212121"
                    android:textSize="16sp"/>

                <Button
                    android:id="@+id/button_view_error_log"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="View Error Log"
                    android:visibility="gone"
                    android:textColor="#212121"/>

                <Button
                    android:id="@+id/button_copy_error_log"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Copy Error Log"
                    android:visibility="gone"
                    android:textColor="#212121"/>

                <Button
                    android:id="@+id/button_share_error_log"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Share Error Log"
                    android:visibility="gone"
                    android:textColor="#212121"/>

                <Button
                    android:id="@+id/button_email_error_log"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Email Error Log"
                    android:visibility="gone"
                    android:textColor="#212121"/>

                <Button
                    android:id="@+id/button_save_error_log"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="4dp"
                    android:text="Save Error Log"
                    android:visibility="gone"
                    android:textColor="#212121"/>

                <Button
                    android:id="@+id/button_close_app"
                    android:layout_width="150dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Close App"
                    android:visibility="gone"
                    android:textColor="#212121"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/button_restart_application"
                    style="@style/Widget.MaterialComponents.Button.UnelevatedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/restart_app"
                    app:strokeColor="@color/colorAccent"
                    android:layout_margin="10dp"
                    app:cornerRadius="24dp"
                    />

            </LinearLayout>
        </ScrollView>

    </androidx.appcompat.widget.LinearLayoutCompat>

    <!--<TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="@string/copyright_info"
        android:textColor="#212121"
        android:textSize="14sp"/>-->
</LinearLayout>
