<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.reference.model.NozzelsModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/pumpListLayout"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="@dimen/_10sdp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        android:gravity="center">

        <LinearLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="3dp"
            android:orientation="vertical">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="5dp">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/iconBg"
                    android:layout_width="@dimen/_70sdp"
                    android:layout_height="@dimen/_70sdp"
                    android:layout_gravity="center"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:tint="@{item.getColor}"
                    android:layout_centerInParent="true"
                    android:src="@drawable/circular_bg"
                    />

                <ImageView
                    android:id="@+id/iconImage"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_centerInParent="true"
                    android:layout_marginStart="7dp"
                    android:layout_marginTop="7dp"
                    android:layout_marginEnd="7dp"
                    android:layout_marginBottom="7dp"
                    android:src="@drawable/ic_fuel"
                    app:tint="@color/white"
                    android:contentDescription="@string/pump" />

            </RelativeLayout>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/titleTextView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:text="@{item.Name}"
                tools:text="Product Name"
                android:textAlignment="center"
                android:layout_marginEnd="5dp"
                android:textColor="@color/black"
                android:maxLines="3"
                android:textSize="14sp"
                android:textStyle="normal" />


        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>
</layout>