<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

<LinearLayout android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_height="match_parent">
    <include layout="@layout/toolbar" android:id="@+id/toolbarPumpList"> </include>
        <LinearLayout
        android:id="@+id/fcc_connection_layout"
        android:gravity="center"
        android:clickable="true"
        android:visibility="gone"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical"
            android:focusable="true">

        <ImageView
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:visibility="visible"
            android:layout_margin="10dp"
            android:src="@drawable/ic_no_fcc_connected"
            android:contentDescription="@string/pump" />


        <TextView
            android:id="@+id/tvFccConnectionMessage"
            android:text="@string/opps_fcc_disconnected"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />

        <TextView
            android:text="@string/transaction_not_completed_msg"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:weightSum="1"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_40sdp"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="wrap_content"
                android:text="@string/cancel"
                android:textAllCaps="true"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/red"
                android:id="@+id/btnVoid"
                android:textColor="@color/white" />
            <TextView

                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/try_again"
                android:textAllCaps="true"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginStart="@dimen/_20sdp"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:id="@+id/btnRetryFcc"
                android:textColor="@color/white" />
        </LinearLayout>


        <ProgressBar
            android:id="@+id/fccProgressBar"
            android:layout_width="wrap_content"
            android:layout_margin="20dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="invisible"
            android:indeterminateTint="@color/colorPrimary"/>

    </LinearLayout>
    <LinearLayout
        android:id="@+id/productLayout"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
    <TextView
        android:text="@string/select_pump"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/_20sdp"
        android:textSize="@dimen/_12sdp"
        android:layout_gravity="center"
        android:textColor="@color/colorPrimary"
        android:id="@+id/prompt" />
    <androidx.recyclerview.widget.RecyclerView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:id="@+id/mListView"
        android:layout_marginStart="@dimen/_30sdp"
        android:layout_marginEnd="@dimen/_30sdp"
        android:orientation="horizontal"
        app:spanCount="2"
        android:layout_marginTop="@dimen/_20sdp"
        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
        tools:listitem="@layout/item_pump_list"> </androidx.recyclerview.widget.RecyclerView>
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/emptyList"
        android:layout_width="match_parent"
        android:text="@string/pumps_not_available"
        android:gravity="center"
        android:visibility="gone"
        android:textSize="@dimen/_16sdp"
        android:textColor="@color/black"
        android:layout_height="match_parent">

    </androidx.appcompat.widget.AppCompatTextView>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/white"
        android:id="@+id/loading"
        android:visibility="gone"
        android:gravity="center">

        <ProgressBar
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_margin="10dp"
            android:progressTint="@color/colorPrimary"/>

        <TextView
            android:text="@string/connecting_to_pump_please_wait"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textSize="18sp"
            android:layout_gravity="center"
            android:textColor="@color/colorPrimary"/>

    </LinearLayout>
    </LinearLayout>
</LinearLayout>
</layout>