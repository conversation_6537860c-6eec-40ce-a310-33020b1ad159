<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

<RelativeLayout android:layout_width="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_height="match_parent">

    <include
        android:id="@+id/toolbarPumpList"
        layout="@layout/toolbar" />

    <!--<RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar"
        android:id="@+id/toolbarPumpList">

        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="3dp"
            android:id="@+id/btnBack"
            android:src="@drawable/ic_arrow_left"
            android:layout_centerVertical="true"
            android:layout_marginStart="12dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_gravity="center"
            android:layout_marginEnd="5dp"
            android:layout_toStartOf="@+id/rightSideLayout"
            android:textAlignment="center"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:visibility="visible"
            app:textAllCaps="true"
            android:layout_toEndOf="@+id/btnBack"
            tools:text="Title" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:id="@+id/rightSideLayout"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="14dp"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/toolbar_right_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:onClick="onClick"
                android:layout_marginEnd="5dp"
                android:text="@string/label_titre_refresh"
                android:textColor="@color/black"
                android:visibility="gone"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/toolbar_right_image"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:layout_alignParentEnd="true"
                android:padding="5dp"
                android:src="@drawable/ic_baseline_refresh_24"
                android:visibility="invisible"
                app:tint="@color/black" />

        </LinearLayout>

    </RelativeLayout>-->

    <LinearLayout
        android:id="@+id/productLayout"
        android:layout_below="@+id/toolbarPumpList"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/border_material_design"
            android:backgroundTint="@color/lightGrey"
            android:orientation="horizontal"
            android:paddingTop="8dp"
            android:layout_margin="@dimen/_10sdp"
            android:paddingBottom="@dimen/dp10"
            android:paddingStart="@dimen/dp10"
            android:paddingEnd="@dimen/dp10"
            android:layout_marginBottom="5dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="@string/current_pump"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvPump"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginStart="5dp"
                    android:text=" "
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="end"
                android:id="@+id/authTimerLayout"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:text="@string/timeout"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvAuthTimeout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginStart="5dp"
                    android:text="00"
                    android:textColor="@color/colorPrimary"
                    android:textSize="14sp"
                    android:textStyle="bold" />
            </LinearLayout>


        </LinearLayout>

        <TextView

            android:text="@string/select_product"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textStyle="bold"
            android:layout_marginTop="20dp"
            android:textSize="@dimen/_14sdp"
            android:layout_gravity="center"
            android:textColor="@color/colorPrimary"
            android:id="@+id/prompt" />
        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:id="@+id/mListView"
            android:orientation="horizontal"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            app:spanCount="2"
            android:layout_marginTop="@dimen/_20sdp"
            tools:listitem="@layout/item_product_list"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/emptyList"
            android:layout_width="match_parent"
            android:text="@string/no_products_found"
            android:gravity="center"
            android:visibility="gone"
            android:textSize="@dimen/_16sdp"
            android:textColor="@color/black"
            android:layout_height="match_parent"/>
    </LinearLayout>
    <LinearLayout
        android:id="@+id/progressBarLayout"
        android:layout_below="@+id/toolbarPumpList"
        android:gravity="center"
        android:visibility="gone"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="@string/current_pump"
            android:textColor="@color/greenLight"
            android:textSize="20sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/tvPump2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_marginBottom="30dp"
            android:text=" "
            android:textColor="@color/greenLight"
            android:textSize="20sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/nozzle"
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:visibility="visible"
            android:adjustViewBounds="false"
            android:cropToPadding="false"
            android:src="@drawable/pump_nozzzle" />


        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:visibility="gone"
            android:layout_height="wrap_content"
            />

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/animation_view_online"
            android:layout_width="220dp"
            android:layout_height="220dp"
            app:lottie_rawRes="@raw/plain_loader"
            app:lottie_repeatMode="restart"
            android:visibility="gone"
            app:lottie_enableMergePathsForKitKatAndAbove="true"
            app:lottie_loop="true"
            app:lottie_speed="1"
            android:layout_marginBottom="-25dp"
            android:layout_gravity="center"
            app:lottie_autoPlay="true"/>

        <TextView
            android:id="@+id/progres_msg"
            android:text="@string/processing"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/greenLight"
            android:textStyle="bold"
            android:textSize="20sp"
            android:textAlignment="center"
            android:layout_marginHorizontal="@dimen/_15sdp"
            android:layout_height="wrap_content"
            android:layout_below="@+id/progressBar"
            android:layout_centerInParent="true"
            />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/disputed_transaction_layout"
        android:layout_below="@+id/toolbarPumpList"
        android:gravity="center"
        android:clickable="true"
        android:visibility="gone"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ImageView
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:visibility="visible"
            android:layout_margin="10dp"
            android:src="@drawable/ic_no_fcc_connected" />


        <TextView
            android:text="@string/no_transaction_found"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />

        <TextView
            android:text="@string/there_is_no_pending_transaction_please_contact_station_manager"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:weightSum="1"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_40sdp"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/complete_as_disputed_trx"
                android:textAllCaps="true"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginStart="@dimen/_20sdp"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:id="@+id/markDisputed"
                android:textColor="@color/white" />
        </LinearLayout>


    </LinearLayout>
    <LinearLayout
        android:id="@+id/fcc_connection_layout"
        android:layout_below="@+id/toolbarPumpList"
        android:gravity="center"
        android:clickable="true"
        android:visibility="gone"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ImageView
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:visibility="visible"
            android:layout_margin="10dp"
            android:src="@drawable/ic_no_fcc_connected" />


        <TextView
            android:id="@+id/tvFccConnectionMessage"
            android:text="@string/opps_fcc_disconnected"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />

        <TextView
            android:text="@string/transaction_not_completed_msg"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:weightSum="1"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_40sdp"
            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="@dimen/_50sdp"
                android:text="@string/cancel"
                android:textAllCaps="true"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/red"
                android:id="@+id/btnVoid"
                android:textColor="@color/white" />
            <TextView
                android:layout_width="0dp"
                android:layout_height="@dimen/_50sdp"
                android:text="@string/try_again"
                android:textAllCaps="true"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginStart="@dimen/_20sdp"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:id="@+id/btnRetryFcc"
                android:textColor="@color/white" />
        </LinearLayout>


        <ProgressBar
            android:id="@+id/fccProgressBar"
            android:layout_width="wrap_content"
            android:layout_margin="20dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="invisible"
            android:indeterminateTint="@color/colorPrimary"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/pumpDisconnectedLayout"
        android:layout_below="@+id/toolbarPumpList"
        android:gravity="center"
        android:clickable="true"
        android:visibility="gone"
        android:layout_gravity="center"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:orientation="vertical">

        <ImageView
            android:layout_width="220dp"
            android:layout_height="220dp"
            android:layout_gravity="center"
            android:visibility="visible"
            android:layout_margin="10dp"
            android:src="@drawable/ic_no_fcc_connected" />

        <TextView
            android:id="@+id/tvPumpMessage"
            android:text="@string/pump_connection_error"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="20sp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />

        <TextView
            android:text="@string/transaction_not_completed_msg"
            android:layout_width="wrap_content"
            android:visibility="visible"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:layout_marginHorizontal="30dp"
            android:layout_marginVertical="5dp"
            android:textAlignment="center"
            android:layout_height="wrap_content" />

        <!--<LinearLayout
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:gravity="center"
            android:weightSum="1"
            android:layout_marginHorizontal="@dimen/_20sdp"
            android:layout_marginTop="@dimen/_40sdp"

            android:layout_height="wrap_content">
            <TextView
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_height="wrap_content"
                android:text="@string/cancel"
                android:textAllCaps="true"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:layout_gravity="center"
                android:gravity="center"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/red"
                android:id="@+id/btnVoid"
                android:textColor="@color/white" />
            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="@string/try_again"
                android:textAllCaps="true"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginStart="@dimen/_20sdp"
                android:textStyle="bold"
                android:paddingVertical="10dp"
                android:paddingHorizontal="30dp"
                android:background="@drawable/round_valid_btn"
                android:backgroundTint="@color/colorPrimary"
                android:id="@+id/btnRetryFcc"
                android:textColor="@color/white" />
        </LinearLayout>


        <ProgressBar
            android:id="@+id/fccProgressBar"
            android:layout_width="wrap_content"
            android:layout_margin="20dp"
            android:layout_height="wrap_content"
            android:indeterminate="true"
            android:visibility="invisible"
            android:indeterminateTint="@color/colorPrimary"/>-->

    </LinearLayout>


</RelativeLayout>
</layout>