<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.iccpayment.viewmodel.PaymentViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:background="@color/background_light_white"
        android:layout_height="match_parent"> 
    <include
        android:id="@+id/toolbarCeilingLimit"
        layout="@layout/toolbar" />
        <ScrollView
            android:id="@+id/CeilingLayout"
            android:layout_width="match_parent"
            android:visibility="gone"
            android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="wrap_content"
       >
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_height="match_parent">
            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_margin="10dp"
                android:padding="10dp"
                android:background="@drawable/ic_card_bg"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">
                    <ImageView
                        android:layout_width="40dp"
                        android:layout_gravity="start"
                        android:src="@drawable/chip"
                        android:onClick="sampleCode"
                        android:layout_height="30dp">

                    </ImageView>
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/cardType"
                        android:layout_width="match_parent"
                        android:text="----"
                        android:layout_gravity="end|center"
                        android:gravity="end|center"
                        android:textSize="14sp"
                        android:textColor="@color/white"
                        android:layout_height="wrap_content">

                    </androidx.appcompat.widget.AppCompatTextView>

                </LinearLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="match_parent"
                    android:text="@string/fleet_card_number"
                    android:layout_marginTop="@dimen/_5sdp"
                    android:textColor="@color/white"
                    android:layout_height="wrap_content">
                </androidx.appcompat.widget.AppCompatTextView>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/cardNumber"
                    android:layout_width="match_parent"
                    android:text="----------"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:layout_marginTop="5dp"
                    android:textStyle="bold"
                    android:layout_height="wrap_content">
                </androidx.appcompat.widget.AppCompatTextView>
                <View
                    android:layout_width="wrap_content"
                    android:background="#B8ABAB"
                    android:layout_marginTop="5dp"
                    android:layout_height="1dp">
                </View>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="2"
                    android:orientation="horizontal"
                    android:layout_height="wrap_content">
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1.4"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:text="@string/card_holder_name"
                            android:layout_marginTop="20dp"
                            android:textColor="@color/white"
                            android:layoutDirection="ltr"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/cardHolderName"
                            android:layout_width="match_parent"
                            android:text="----------"
                            android:textColor="@color/white"
                            android:textSize="14sp"
                            android:layoutDirection="ltr"
                            android:maxLines="1"
                            android:layout_marginTop="5dp"
                            android:textStyle="bold"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <View
                            android:layout_width="wrap_content"
                            android:background="#B8ABAB"
                            android:layout_marginTop="5dp"
                            android:layout_height="1dp">
                        </View>

                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="0.6"
                        android:layout_marginStart="10dp"
                        android:orientation="vertical"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="match_parent"
                            android:text="@string/expiry_date"
                            android:layout_marginTop="20dp"
                            android:layoutDirection="ltr"
                            android:layout_gravity="end"
                            android:gravity="start"
                            android:textColor="@color/white"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/expiryDate"
                            android:layout_width="match_parent"
                            android:text="----------"
                            android:textColor="@color/white"
                            android:layoutDirection="ltr"
                            android:textSize="14sp"
                            android:maxLines="1"
                            android:layout_marginTop="5dp"
                            android:textStyle="bold"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <View
                            android:layout_width="wrap_content"
                            android:background="#B8ABAB"
                            android:layout_marginTop="5dp"
                            android:layout_height="1dp">
                        </View>

                    </LinearLayout>

                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:visibility="visible"
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:paddingHorizontal="10dp"
                android:layout_height="wrap_content">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:id="@+id/ceilingLimitLayout"
                    android:orientation="vertical">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="match_parent"
                        android:layout_gravity="center"
                        android:textStyle="bold"
                        android:textSize="14sp"
                        android:id="@+id/tvCeilingLimitText"
                        android:textColor="@color/black"
                        android:gravity="center"
                        android:text="@string/card_ceiling_limits"
                        android:layout_height="wrap_content">
                    </androidx.appcompat.widget.AppCompatTextView>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:background="@color/white"
                        android:padding="20dp"
                        android:weightSum="2"
                        android:id="@+id/monthlyLayout"
                        android:layout_marginTop="10dp"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:text="@string/monthly_limit_text"
                            android:textStyle="bold"
                            android:layout_weight="1"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/monthlyCeilingTxt"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:layout_gravity="end"
                            android:text="----------"
                            android:textStyle="bold"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:background="@color/white"
                        android:padding="20dp"
                        android:weightSum="2"
                        android:id="@+id/weeklyLayout"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:text="@string/weekly_limit_text"
                            android:textStyle="bold"
                            android:layout_weight="1"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/weeklyCeilingTxt"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:layout_gravity="end"
                            android:text="----------"
                            android:textStyle="bold"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                    </LinearLayout>
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:background="@color/white"
                        android:padding="20dp"
                        android:weightSum="2"
                        android:id="@+id/dailyLayout"
                        android:layout_marginTop="2dp"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:text="@string/daily_limit_text"
                            android:textStyle="bold"
                            android:layout_weight="1"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/dailyCeilingTxt"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:gravity="end"
                            android:layout_gravity="end"
                            android:text="----------"
                            android:textStyle="bold"
                            android:textSize="14sp"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">
                        </androidx.appcompat.widget.AppCompatTextView>
                    </LinearLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:layout_marginTop="6dp"
                    android:id="@+id/discountTab"
                    android:padding="@dimen/_5sdp"
                    android:visibility="gone"
                    android:background="@color/white"
                    android:orientation="vertical">
                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="match_parent"
                        android:orientation="horizontal"
                        android:layout_weight="2"
                        android:visibility="visible"
                        android:layout_marginVertical="@dimen/_5sdp"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/transaction_amount"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_height="match_parent">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/transactionAmount"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="0"
                            android:layout_gravity="end"
                            android:gravity="end"
                            android:textSize="@dimen/_12sdp"
                            android:textStyle="bold"
                            android:textColor="@color/green"
                            android:layout_height="match_parent">

                        </androidx.appcompat.widget.AppCompatTextView>
                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <androidx.appcompat.widget.SwitchCompat
                        android:layout_width="match_parent"
                        android:text="@string/pay_by_card_balance"
                        android:id="@+id/cardSwitch"
                        android:checked="true"
                        android:textStyle="bold"
                        android:textColor="@color/black"
                        app:thumbTint="@color/colorPrimary"
                        android:layout_height="wrap_content">

                    </androidx.appcompat.widget.SwitchCompat>
                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/discountSwitch"
                        android:layout_width="match_parent"
                        android:textStyle="bold"
                        android:checked="false"
                        android:textColor="@color/black"
                        app:thumbTint="@color/colorPrimary"
                        android:text="@string/pay_by_discount_balance"
                        android:layout_height="wrap_content">
                    </androidx.appcompat.widget.SwitchCompat>
                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="match_parent"
                        android:orientation="horizontal"
                        android:layout_weight="2"
                        android:visibility="gone"
                        android:id="@+id/discountBalanceLayout"
                        android:layout_marginVertical="@dimen/_5sdp"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="@string/discount_balance"
                            android:textStyle="bold"
                            android:textColor="@color/black"
                            android:layout_height="match_parent">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/discountBalance"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:text="0 AED"
                            android:layout_gravity="end"
                            android:gravity="end"
                            android:textSize="@dimen/_12sdp"
                            android:textStyle="bold"
                            android:textColor="@color/green"
                            android:layout_height="match_parent">

                        </androidx.appcompat.widget.AppCompatTextView>
                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="match_parent"
                        android:orientation="horizontal"
                        android:layout_weight="2"
                        android:visibility="gone"
                        android:id="@+id/progressDiscount"
                        android:layout_marginVertical="@dimen/_5sdp"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="0dp"
                            android:layout_weight="1.8"
                            android:text="@string/please_wait_we_are_fetching_your_discount_balance"
                            android:textColor="@color/black"
                            android:layout_height="wrap_content">

                        </androidx.appcompat.widget.AppCompatTextView>
                        <ProgressBar
                            android:layout_width="0dp"
                            android:layout_weight="0.2"
                            android:layout_gravity="end"
                            android:gravity="end"
                            android:layout_height="match_parent">

                        </ProgressBar>
                    </androidx.appcompat.widget.LinearLayoutCompat>
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/discountWarningMsg"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:gravity="center"
                        android:padding="5dp"
                        android:text="@string/warning_n_discount_balance_not_available_on_your_card"
                        android:textColor="@color/red"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        android:visibility="gone" />

                    </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:weightSum="2"
                    android:layout_marginTop="6dp"
                    android:padding="@dimen/_5sdp"
                    android:background="@color/white"
                    android:orientation="horizontal">
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:orientation="vertical"
                        android:id="@+id/layoutAllowedBalance"
                        android:layout_height="wrap_content">
                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/amount"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:gravity="center"
                            android:layout_marginTop="@dimen/_6sdp"
                            android:text="@string/amount_pay_allowed"
                            android:textColor="@color/black"
                            android:textSize="16sp"
                            android:textStyle="bold" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/allowedBalnceTxt"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:padding="5dp"
                            android:text="---"
                            android:textColor="@color/greenLight"
                            android:textSize="14sp"
                            android:textStyle="bold" />
                    </LinearLayout>

                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:visibility="gone"
                        android:id="@+id/layoutCardBalance"
                        android:orientation="horizontal"
                        android:layout_height="wrap_content">
                        <View
                            android:id="@+id/view"
                            android:layout_width="1dp"
                            android:layout_marginVertical="@dimen/_10sdp"
                            android:layout_height="match_parent"
                            android:background="@color/darkGreyLight"/>
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:orientation="vertical"
                            android:layout_height="wrap_content">
                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:gravity="center"
                                android:layout_marginTop="@dimen/_6sdp"
                                android:text="@string/card_balance_t"
                                android:textColor="@color/black"
                                android:textSize="16sp"
                                android:textStyle="bold" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/tvCardBalance"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:gravity="center"
                                android:padding="5dp"
                                android:text="---"
                                android:textColor="@color/greenLight"
                                android:textSize="14sp"
                                android:textStyle="bold" />
                        </LinearLayout>
                    </LinearLayout>

                </LinearLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/amount_warning_msg"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_margin="10dp"
                    android:gravity="center"
                    android:padding="5dp"
                    android:text="@string/soldeErr"
                    android:textColor="@color/red"
                    android:textSize="12sp"
                    android:textStyle="bold"
                    android:visibility="gone" />
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_marginBottom="@dimen/_10sdp"
                    android:layout_marginHorizontal="10dp"
                    android:layout_marginTop="10dp"
                    android:layout_height="wrap_content">
                    <Button
                        android:id="@+id/cancelBtn"
                        android:layout_width="0dp"
                        android:text="@string/cancel_ntransaction"
                        android:layout_weight="1"
                        android:backgroundTint="#F44336"
                        android:layout_height="wrap_content"/>
                    <Button
                        android:id="@+id/submitBtn"
                        android:layout_width="0dp"
                        android:text="@string/proceed_for_npayment"
                        android:layout_weight="1"
                        android:layout_marginStart="20dp"
                        android:backgroundTint="#4CAF50"
                        android:layout_height="wrap_content"/>
                    <Button
                        android:id="@+id/modePayBtn"
                        android:layout_width="0dp"
                        android:text="@string/change_payment_method"
                        android:layout_weight="1"
                        android:visibility="gone"
                        android:layout_marginStart="20dp"
                        android:backgroundTint="@color/colorPrimary"
                        android:layout_height="wrap_content"/>
                </LinearLayout>

            </LinearLayout>


        </LinearLayout>


    </RelativeLayout>
        </ScrollView>
        <FrameLayout
            android:id="@+id/progressBar"
            android:layout_width="match_parent"
            android:background="@color/white"
            android:layout_height="match_parent"
            >

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/message"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="@string/please_wait_verifying_card_limits_and_balance"
                android:gravity="center"
                android:textSize="@dimen/_14sdp"
                android:layout_marginTop="@dimen/_100sdp"
                android:textColor="@color/colorPrimary"
                android:textStyle="bold"
                android:layout_marginHorizontal="@dimen/_20sdp"
             />
            <ImageView
                android:layout_width="@dimen/dp50"
                android:layout_gravity="center"
                android:visibility="gone"
                android:src="@drawable/ic_credit_card"
                android:layout_height="@dimen/dp80">

            </ImageView>
            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationView"
                android:layout_width="match_parent"
                android:layout_height="@dimen/_350sdp"
                android:layout_marginTop="@dimen/_70sdp"
                android:layout_marginHorizontal="@dimen/_30sdp"
                android:layout_gravity="center"
                app:lottie_rawRes="@raw/card_processing2"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"/>

        </FrameLayout>
    </LinearLayout>
</layout>