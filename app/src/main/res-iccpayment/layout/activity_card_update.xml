<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent"
        tools:context=".MainActivity">
        <include
            android:id="@+id/toolbarPayment"
            layout="@layout/toolbar" />
            <LinearLayout
                android:id="@+id/insertCardImageLayout"
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:visibility="visible"
                android:layout_height="match_parent">
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/progressMessage"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/please_wait_your_card_update_is_under_process"
                    android:gravity="center"
                    android:textSize="@dimen/_14sdp"
                    android:layout_marginTop="@dimen/_100sdp"
                    android:textColor="@color/colorPrimary"
                    android:textStyle="bold"
                    android:layout_marginHorizontal="@dimen/_20sdp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.airbnb.lottie.LottieAnimationView
                    android:id="@+id/animationView"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/_200sdp"
                    android:layout_marginTop="@dimen/_70sdp"
                    android:layout_marginHorizontal="@dimen/_30sdp"
                    android:layout_gravity="center"
                    app:lottie_rawRes="@raw/card_processing3"
                    app:lottie_autoPlay="true"
                    app:lottie_loop="true"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/please_don_t_remove_the_card"
                    android:gravity="center"
                    android:textSize="@dimen/_14sdp"
                    android:textColor="@color/red"
                    android:textStyle="bold"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:layout_marginHorizontal="@dimen/_20sdp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
                </LinearLayout>

             <LinearLayout
        android:id="@+id/sucessLayout"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/card_updated_successfully"
            android:gravity="center"
            android:textSize="@dimen/_12sdp"
            android:layout_marginTop="50dp"
            android:textColor="@color/green"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <com.airbnb.lottie.LottieAnimationView
            android:layout_width="280dp"
            android:layout_height="280dp"
            android:visibility="visible"
            app:lottie_rawRes="@raw/success"
            app:lottie_repeatMode="restart"
            app:lottie_enableMergePathsForKitKatAndAbove="true"
            app:lottie_loop="false"
            app:lottie_speed="1"
            android:layout_gravity="center"
            app:lottie_autoPlay="false"/>
    </LinearLayout>
    </LinearLayout>
</layout>