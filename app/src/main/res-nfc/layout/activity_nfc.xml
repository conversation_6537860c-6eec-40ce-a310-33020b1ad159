<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".ui.nfc.activity.NfcActivity">
    <include
        android:id="@+id/toolbarNFc"
        layout="@layout/toolbar" />
    <TextView
        android:text="@string/scan_your_nfc_tag"
        android:layout_width="match_parent"
        android:gravity="center"
        android:textStyle="bold"
        android:layout_marginTop="@dimen/_30sdp"
        android:textColor="@color/colorPrimary"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        > </TextView>
    <LinearLayout
        android:id="@+id/insertCardImageLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="30dp"
        android:gravity="center"
        android:orientation="vertical"
        android:padding="20dp">

        <ImageView
            android:id="@+id/insertCardImageView"
            android:layout_width="300dp"
            android:layout_height="300dp"
            android:layout_gravity="center"
            android:adjustViewBounds="false"
            android:cropToPadding="false"
            android:src="@drawable/nfc_payment" />
    </LinearLayout>


</LinearLayout>
</layout>