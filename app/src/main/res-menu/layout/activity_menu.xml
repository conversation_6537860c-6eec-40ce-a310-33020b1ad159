<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel" />

    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        android:background="@color/colorLightGrey"
      >
        <ImageView
            android:id="@+id/ivTicketPreview"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        </ImageView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/headerLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivMenuBg"
                android:layout_width="match_parent"
                android:layout_height="230dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_menu_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <RelativeLayout
                android:id="@+id/headerContent"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="30dp"
                android:layout_marginEnd="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/logo"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/circular_border"
                    android:padding="11dp"
                    android:src="@drawable/rht_logo" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvStationName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="@string/station_name"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ivTransactionMode"
                            android:layout_width="10dp"
                            android:layout_height="10dp"
                            android:layout_marginEnd="5dp"
                            android:src="@drawable/circle_image"
                            app:tint="@color/red" />

                        <TextView
                            android:id="@+id/tvTransactionMode"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="1dp"
                            android:text="@string/offline"
                            android:textColor="@color/white"
                            android:textSize="14sp" />

                    </LinearLayout>


                    <TextView
                        android:id="@+id/tvAppVersion"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="1dp"
                        android:layout_marginStart="15dp"
                        android:text="v 0.0.0"
                        android:textColor="@color/white_transparency"
                        android:textSize="14sp"/>

                    <TextView
                        android:id="@+id/tvTimeStamp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="ddMMyyyy hh:mm:ss aa"
                        android:layout_marginStart="15dp"
                        android:textColor="@color/white_transparency"
                        android:textSize="12sp"/>

                    <TextView
                        android:id="@+id/tvTerminalSn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="1dp"
                        android:text="*****0000"
                        android:textColor="@color/white_transparency"
                        android:layout_marginStart="15dp"
                        android:textSize="12sp" />

                </LinearLayout>


            </RelativeLayout>


            <View
                android:id="@+id/blankView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginBottom="50dp"
                app:layout_constraintBottom_toBottomOf="@+id/cardsLayout"
                app:layout_constraintEnd_toEndOf="@+id/cardsLayout"
                app:layout_constraintStart_toStartOf="@+id/cardsLayout"
                app:layout_constraintTop_toTopOf="@+id/cardsLayout" />

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/cardsLayout"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="6dp"
                android:visibility="visible"
                app:cardUseCompatPadding="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/headerContent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp">


                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="25sp"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical"
                            android:text="@string/categories"
                            android:textColor="@color/text_color"
                            android:textStyle="bold" />

                        <com.google.android.material.button.MaterialButton
                            android:id="@+id/btnMoreCategory"
                            style="@style/Widget.MaterialComponents.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="35sp"
                            android:layout_alignParentEnd="true"
                            android:clickable="true"
                            android:focusable="true"
                            android:onClick="showMoreCategories"
                            android:text="@string/more"
                            android:textColor="@color/colorPrimary"
                            android:textSize="11dp"
                            tools:ignore="TouchTargetSizeCheck" />

                    </RelativeLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/noCategories"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:text="@string/no_categories_found"
                        android:textColor="@color/redLight"
                        android:visibility="gone">

                    </androidx.appcompat.widget.AppCompatTextView>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rvCategories"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/dashboard_card_height"
                        app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        tools:listitem="@layout/item_category_menu"
                        android:scrollbars="none"
                        android:layout_gravity="center"/>

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="32sp"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:layout_marginBottom="5dp"
                        android:gravity="center_vertical"
                        android:text="@string/manage"
                        android:textColor="@color/text_color"
                        android:textStyle="bold" />

                   <!-- <HorizontalScrollView
                        android:id="@+id/manageScrollView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:scrollbars="none">



                    </HorizontalScrollView>-->

                    <LinearLayout
                        android:layout_width="fill_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardSettings"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dashboard_card_height"
                            android:layout_weight="1"
                            android:minWidth="@dimen/dashboard_card_width"
                            android:layout_marginEnd="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:onClick="onManageItemClicked"
                            app:cardBackgroundColor="#FDEAD4"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="25dp"
                                    android:src="@drawable/ic_settings" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="15dp"
                                    android:gravity="center"
                                    android:text="@string/settings"
                                    android:textColor="@color/text_color"
                                    android:textSize="10sp" />

                            </RelativeLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardTeleCollect"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dashboard_card_height"
                            android:layout_weight="1"
                            android:minWidth="@dimen/dashboard_card_width"
                            android:layout_marginEnd="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:onClick="onManageItemClicked"
                            app:cardBackgroundColor="#FFDADA"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="25dp"
                                    android:src="@drawable/ic_telecollect" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="15dp"
                                    android:gravity="center"
                                    android:text="@string/telecollect"
                                    android:textColor="@color/text_color"
                                    android:textSize="10sp" />

                            </RelativeLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/cardLoyalty"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/dashboard_card_height"
                            android:layout_weight="1"
                            android:minWidth="@dimen/dashboard_card_width"
                            android:layout_marginEnd="10dp"
                            android:clickable="true"
                            android:focusable="true"
                            android:onClick="onManageItemClicked"
                            app:cardBackgroundColor="#E4F6F9"
                            app:cardCornerRadius="6dp"
                            app:cardElevation="0dp"
                            app:cardPreventCornerOverlap="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginTop="25dp"
                                    android:src="@drawable/ic_loyalty_card" />

                                <TextView
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="15dp"
                                    android:gravity="center"
                                    android:text="@string/loyalty_card"
                                    android:textColor="@color/text_color"
                                    android:textSize="10sp" />

                            </RelativeLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>


            <ScrollView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:scrollbars="none"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/cardsLayout"
                app:layout_constraintStart_toStartOf="@+id/cardsLayout"
                app:layout_constraintTop_toBottomOf="@+id/blankView">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="50dp"
                    android:orientation="vertical"
                    android:paddingBottom="50dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="125dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="3dp"
                            android:clickable="true"
                            android:id="@+id/totalTrxCard"
                            app:cardPreventCornerOverlap="true"
                            app:cardUseCompatPadding="true"
                            android:focusable="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:foreground="?attr/selectableItemBackgroundBorderless">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_marginStart="16dp"
                                    android:layout_marginTop="16dp"
                                    android:layout_marginEnd="16dp"
                                    android:layout_marginBottom="16dp"
                                    android:src="@drawable/ic_fuel" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_above="@+id/tvTitleTotTrx"
                                    android:layout_marginHorizontal="10dp"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tvTotalTrxCount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentStart="true"
                                        android:layout_centerHorizontal="true"
                                        android:layout_marginBottom="-7dp"
                                        android:fontFamily="@font/bold"
                                        android:gravity="center_vertical"
                                        android:text="000"
                                        android:textColor="@color/text_color"
                                        android:textSize="35sp" />

                                    <TextView
                                        android:id="@+id/tvTrxWorthAmount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_above="@+id/tvTitleTotTrx"
                                        android:layout_marginStart="2dp"
                                        android:layout_marginEnd="10dp"
                                        android:layout_marginBottom="3dp"
                                        android:layout_toEndOf="@+id/tvTotalTrxCount"
                                        android:fontFamily="@font/bold"
                                        android:gravity="center_vertical"
                                        android:text="000000000"
                                        android:textColor="#ffb13e"
                                        android:textSize="@dimen/worth_label_size" />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tvTitleTotTrx"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:gravity="center_vertical"
                                    android:text="@string/total_transactions"
                                    android:textColor="@color/grey"
                                    android:textSize="@dimen/menu_card_subtitle" />

                            </RelativeLayout>

                        </androidx.cardview.widget.CardView>

                        <androidx.cardview.widget.CardView
                            android:layout_width="match_parent"
                            android:layout_height="125dp"
                            android:layout_weight="1"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="3dp"
                            android:id="@+id/totalRechCard"
                            app:cardPreventCornerOverlap="true"
                            app:cardUseCompatPadding="true">

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:clickable="true"
                                android:focusable="true"
                                android:foreground="?attr/selectableItemBackgroundBorderless">

                                <ImageView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentTop="true"
                                    android:layout_alignParentEnd="true"
                                    android:layout_marginStart="16dp"
                                    android:layout_marginTop="16dp"
                                    android:layout_marginEnd="16dp"
                                    android:layout_marginBottom="16dp"
                                    android:src="@drawable/ic_recharges" />

                                <LinearLayout
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_above="@+id/tvTitleTotRecharge"
                                    android:layout_marginHorizontal="10dp"
                                    android:orientation="vertical">

                                    <TextView
                                        android:id="@+id/tvTotalRechargeCount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_marginBottom="-7dp"
                                        android:fontFamily="@font/bold"
                                        android:gravity="center_vertical"
                                        android:text="000"
                                        android:textColor="@color/text_color"
                                        android:textSize="35sp" />

                                    <TextView
                                        android:id="@+id/tvRechWorthAmount"
                                        android:layout_width="wrap_content"
                                        android:layout_height="wrap_content"
                                        android:layout_above="@+id/tvTitleTotRecharge"
                                        android:layout_marginStart="2dp"
                                        android:layout_marginEnd="10dp"
                                        android:layout_marginBottom="3dp"
                                        android:layout_toEndOf="@+id/tvTotalRechargeCount"
                                        android:fontFamily="@font/bold"
                                        android:gravity="center_vertical"
                                        android:text="000000000"
                                        android:textColor="#2fbbd0"
                                        android:textSize="@dimen/worth_label_size" />

                                </LinearLayout>

                                <TextView
                                    android:id="@+id/tvTitleTotRecharge"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentBottom="true"
                                    android:layout_centerHorizontal="true"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="10dp"
                                    android:gravity="center_vertical"
                                    android:text="@string/total_recharges"
                                    android:textColor="@color/grey"
                                    android:textSize="@dimen/menu_card_subtitle" />

                            </RelativeLayout>

                        </androidx.cardview.widget.CardView>

                    </LinearLayout>

                    <androidx.cardview.widget.CardView
                        android:layout_width="match_parent"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        app:cardCornerRadius="12dp"
                        app:cardElevation="3dp"
                        app:cardUseCompatPadding="true">


                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center_horizontal"
                            android:orientation="horizontal">

                            <!--<com.taosif7.android.ringchartlib.RingChart
                                android:id="@+id/ring_chart"
                                android:layout_width="170dp"
                                android:layout_height="170dp"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true" />-->
                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical"
                                android:visibility="gone">

                                <TextView
                                    android:id="@+id/tvTitleTotSale"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentStart="true"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginTop="10dp"
                                    android:layout_marginEnd="10dp"
                                    android:layout_marginBottom="5dp"
                                    android:gravity="center_vertical"
                                    android:text="@string/total_sale"
                                    android:textColor="@color/subtitle_text_color"
                                    android:textSize="10sp" />

                                <LinearLayout
                                    android:id="@+id/pumpSaleHeader"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:layout_below="@+id/tvTitleTotSale"
                                    android:layout_marginStart="10dp"
                                    android:layout_marginBottom="6dp"
                                    android:layout_toStartOf="@+id/barChart"
                                    android:clickable="false"
                                    android:focusable="false"
                                    android:gravity="center_vertical"
                                    android:orientation="horizontal">

                                    <TextView
                                        android:id="@+id/tvFuelTitle"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentBottom="true"
                                        android:layout_centerHorizontal="true"
                                        android:layout_weight="1"
                                        android:gravity="start"
                                        android:lineSpacingExtra="10dp"
                                        android:text="@string/pump"
                                        android:textColor="@color/text_color"
                                        android:textSize="10sp" />

                                    <TextView
                                        android:id="@+id/tvFuelQty"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentBottom="true"
                                        android:layout_centerHorizontal="true"
                                        android:layout_marginStart="5dp"
                                        android:layout_weight="1"
                                        android:gravity="start"
                                        android:lineSpacingExtra="10dp"
                                        android:text="@string/qty"
                                        android:textColor="@color/text_color"
                                        android:textSize="10sp" />

                                    <TextView
                                        android:id="@+id/tvFuelAmt"
                                        android:layout_width="match_parent"
                                        android:layout_height="wrap_content"
                                        android:layout_alignParentBottom="true"
                                        android:layout_centerHorizontal="true"
                                        android:layout_marginStart="5dp"
                                        android:layout_marginEnd="5dp"
                                        android:layout_weight="1"
                                        android:gravity="start"
                                        android:lineSpacingExtra="10dp"
                                        android:text="@string/amt"
                                        android:textColor="@color/text_color"
                                        android:textSize="10sp" />

                                </LinearLayout>

                                <androidx.recyclerview.widget.RecyclerView
                                    android:id="@+id/rvFuels"
                                    android:layout_width="match_parent"
                                    android:layout_height="match_parent"
                                    android:layout_below="@+id/pumpSaleHeader"
                                    android:layout_alignParentStart="true"
                                    android:layout_toStartOf="@+id/barChart"
                                    android:orientation="vertical"
                                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                                    tools:listitem="@layout/item_fuel_sale" />
                            </LinearLayout>

                            <lecho.lib.hellocharts.view.ColumnChartView
                                android:id="@+id/barChart"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:visibility="gone" />

                          <!--  <com.github.aachartmodel.aainfographics.aachartcreator.AAChartView
                                android:id="@+id/aa_chart_view"
                                android:layout_width="match_parent"
                                android:layout_height="180dp"
                                android:layout_margin="5dp"
                                android:visibility="gone" />-->

                        </LinearLayout>

                    </androidx.cardview.widget.CardView>

                </LinearLayout>

            </ScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>
        <RelativeLayout
            android:id="@+id/loadingLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:visibility="gone"
            android:background="@color/white">

            <com.airbnb.lottie.LottieAnimationView
                android:id="@+id/animationView"
                android:layout_width="250dp"
                android:layout_height="180dp"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:layout_marginTop="90dp"
                android:layout_marginEnd="10dp"
                app:lottie_autoPlay="true"
                app:lottie_loop="true"
                app:lottie_rawRes="@raw/referencing_amination" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/animationView"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="-30dp"
                android:layout_marginBottom="20dp"
                android:text="@string/processing"
                android:id="@+id/tvProcessing"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_below="@+id/tvProcessing"
                android:gravity="center"
                android:visibility="gone"
                android:id="@+id/tvReferenceBatch"
                android:text="@string/reference_batch"
                android:textColor="@color/grey"/>

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="90dp"
                android:gravity="center"
                android:text="@string/please_wait_until_nfinishes_telecollect"
                android:textColor="@color/grey"/>

        </RelativeLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:visibility="gone"
            android:orientation="vertical">


        </LinearLayout>

    </RelativeLayout>

</layout>
