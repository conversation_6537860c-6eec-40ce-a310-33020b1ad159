<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel" />

    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.menu.activity.MenuActivity">
        <include
            android:id="@+id/toolbarLoyalty"
            layout="@layout/toolbar" />

        <FrameLayout
            android:id="@+id/headerLayout"
            android:layout_width="match_parent"
            android:layout_marginTop="50dp"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/ivMenuBg"
                android:layout_width="match_parent"
                android:layout_height="220dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_menu_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <RelativeLayout
                android:id="@+id/headerContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:layout_marginTop="0dp"
                android:layout_marginEnd="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/logo"
                    android:layout_width="70dp"
                    android:layout_height="70dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/circular_border"
                    android:padding="11dp"
                    android:src="@drawable/rht_logo" />

                <LinearLayout
                    android:id="@+id/station"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tvStationName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        tools:text="Station Name"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        android:textStyle="bold" />


                </LinearLayout>
                <LinearLayout
                    android:layout_marginTop="@dimen/_90sdp"
                    android:layout_width="match_parent"
                    android:gravity="center"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="@string/loyalty_dashboard"
                        android:textColor="@color/white"
                        android:textSize="17sp"
                        android:textStyle="bold" />


                </LinearLayout>


            </RelativeLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="12dp">

                    <RelativeLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="35sp"
                            android:layout_alignParentStart="true"
                            android:layout_centerVertical="true"
                            android:gravity="center_vertical"
                            android:text="@string/categories"
                            android:textColor="@color/text_color"
                            android:textSize="@dimen/_16sdp"
                            android:textStyle="bold" />

                    </RelativeLayout>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/noCategories"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/_10sdp"
                        android:gravity="center"
                        android:text="@string/no_categories_found"
                        android:textColor="@color/redLight"
                        android:visibility="gone">

                    </androidx.appcompat.widget.AppCompatTextView>


                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvCategories"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/_5sdp"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:spanCount="3"
                    tools:listitem="@layout/item_loyalty_menu" />


            </LinearLayout>


        </FrameLayout>


    </RelativeLayout>
</layout>
