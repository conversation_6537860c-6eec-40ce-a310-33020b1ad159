<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.reference.model.SubProduct" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />


        <import type="android.view.View"/>
    </data>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="@dimen/loyalty_dashboard_card_height"
        app:cardCornerRadius="6dp"
        app:cardElevation="0dp"
        android:layout_weight="1"
        android:layout_marginVertical="5dp"
        android:layout_marginHorizontal="@dimen/_5sdp"
        android:id="@+id/activationCard"
        app:cardPreventCornerOverlap="true"
        android:clickable="true"
        android:focusable="true"
        app:cardBackgroundColor="@{item.cardBgColor}"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
      >

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:targetApi="m">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="vertical">

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="45dp"
                    android:layout_height="45dp"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginTop="25dp"
                    android:layout_marginBottom="5dp"
                    setNormalImage="@{item.icon}"
                    tools:src="@drawable/ic_activate"
                    />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/ivCategoryImage"
                    android:layout_alignParentBottom="true"
                    android:layout_centerHorizontal="true"
                    android:layout_marginStart="10dp"
                    android:layout_marginEnd="10dp"
                    android:layout_marginBottom="15dp"
                    android:gravity="center"
                    android:text="@{item.label}"
                    tools:text="Item Name"
                    android:lineSpacingExtra="-5dp"
                    android:textColor="@color/text_color"
                    android:textSize="@dimen/loyalty_card_label_size" />
            </LinearLayout>

        </RelativeLayout>

    </com.google.android.material.card.MaterialCardView>


</layout>