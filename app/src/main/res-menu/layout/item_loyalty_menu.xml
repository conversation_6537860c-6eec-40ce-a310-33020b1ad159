<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.reference.model.CategoryListModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:orientation="vertical"
    android:layout_margin="@dimen/_5sdp"
    android:layout_height="@dimen/_130sdp">

    <com.google.android.material.card.MaterialCardView

        android:layout_width="@dimen/_80sdp"
        android:layout_height="@dimen/_80sdp"
        app:cardCornerRadius="6dp"
        app:cardElevation="0dp"
        android:id="@+id/categoryCard"
        app:cardPreventCornerOverlap="true"
        android:layout_marginEnd="10dp"
        android:clickable="true"
        android:focusable="true"
        tools:cardBackgroundColor ="#9B76E9"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        app:cardBackgroundColor="@{item.cardBgColor}">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/categoryItem"
            tools:targetApi="m">

            <ImageView
                android:id="@+id/ivCategoryImage"
                setNormalImage="@{item.icon}"
                android:layout_width="match_parent"
                tools:src="@drawable/ic_fuel"
                android:layout_height="match_parent"
                tools:tint="@color/white"
                android:padding="@dimen/_15sdp"
               />

        </LinearLayout>
    </com.google.android.material.card.MaterialCardView>
    <TextView
        android:id="@+id/tvCategoryTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        tools:text="FUEL"
        android:layout_marginTop="@dimen/_5sdp"
        android:layout_gravity="center"
        android:textStyle="bold"
        android:textAllCaps="true"
        android:text="@{item.category_name}"
        android:textColor="@color/black"
        android:textSize="14sp" />

</LinearLayout>


</layout>
