<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
   >
    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="320dp"
            android:layout_height="320dp"
            android:layout_centerInParent="true"
            android:background="@drawable/formulaire_activation_fidelity"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <androidx.appcompat.widget.AppCompatImageView
                android:layout_width="@dimen/dp100"
                android:layout_height="@dimen/dp100"
                android:src="@drawable/app_subscription_end">

            </androidx.appcompat.widget.AppCompatImageView>


            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/subscription_expired"
                android:textAlignment="center"
                android:textAllCaps="false"
                android:textColor="@color/red"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/message2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/your_subscription_has_expired_please_contact_rising_hightech"
                android:textColor="@color/greyDark"
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10sdp"
                android:gravity="center"
                android:text="@string/ok"
                android:textColor="@color/white"
                android:textSize="12sp"
                android:background="@drawable/bg_update_btn"
                android:textStyle="bold"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title" />


        </LinearLayout>

    </RelativeLayout>
</layout>