<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_weight="1">

    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.reference.model.CategoryListModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>

    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="@dimen/dashboard_card_height"
        app:cardCornerRadius="6dp"
        app:cardElevation="0dp"
        android:minWidth="@dimen/dashboard_card_width"

        android:id="@+id/categoryCard"
        app:cardPreventCornerOverlap="true"
        android:layout_marginEnd="10dp"
        android:clickable="true"
        android:focusable="true"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        app:cardBackgroundColor="@{item.cardBgColor}">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/categoryItem"
            tools:targetApi="m">

            <ImageView
                android:id="@+id/ivCategoryImage"
                setNormalImage="@{item.icon}"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="25dp" />

            <TextView
                android:id="@+id/tvCategoryTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="15dp"
                android:gravity="center"
                android:lineSpacingExtra="10dp"
                android:text="@{item.category_name}"
                android:textColor="@color/text_color"
                android:textSize="10sp" />

        </RelativeLayout>

    </com.google.android.material.card.MaterialCardView>

</layout>
