<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.menu.model.MenuCategoryModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:clickable="false"
        android:focusable="false"
        android:gravity="center_vertical"
        android:layout_marginStart="10dp"
       
        android:layout_marginBottom="6dp">

        <TextView
            android:id="@+id/tvFuelTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:gravity="start"
            android:lineSpacingExtra="10dp"
            android:text="@{item.name}"
            android:textColor="@color/text_color"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/tvFuelQty"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="5dp"
            android:gravity="start"
            android:lineSpacingExtra="10dp"
            android:text="@{item.name}"
            android:textColor="@color/text_color"
            android:textSize="10sp" />

        <TextView
            android:id="@+id/tvFuelAmt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:gravity="start"
            android:lineSpacingExtra="10dp"
            android:text="@string/qty"
            android:textColor="@color/text_color"
            android:textSize="10sp" />


    </LinearLayout>

</layout>
