<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <LinearLayout android:layout_width="match_parent"
        xmlns:tools="http://schemas.android.com/tools"
        android:orientation="vertical"

        android:layout_height="match_parent">
        <include layout="@layout/toolbar" android:id="@+id/toolbarServices"> </include>
        <TextView
            android:text="@string/select_services"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textStyle="bold"
            android:visibility="gone"
            android:layout_marginTop="@dimen/_20sdp"
            android:textSize="@dimen/_12sdp"
            android:layout_gravity="center"
            android:textColor="@color/colorPrimary"
            android:id="@+id/prompt" />
        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/mListView"
            android:layout_marginHorizontal="@dimen/_10sdp"
            app:spanCount="2"
            app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
            tools:listitem="@layout/item_service_list">
        </androidx.recyclerview.widget.RecyclerView>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/emptyList"
            android:layout_width="match_parent"
            android:text="No Service Found"
            android:gravity="center"
            android:visibility="gone"
            android:textSize="@dimen/_16sdp"
            android:textColor="@color/black"
            android:layout_height="match_parent">

        </androidx.appcompat.widget.AppCompatTextView>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:background="@color/white"
            android:id="@+id/loading"
            android:visibility="gone"
            android:gravity="center">

            <ProgressBar
                android:layout_width="50dp"
                android:layout_height="50dp"
                android:layout_margin="10dp"
                android:progressTint="@color/colorPrimary"/>

            <TextView
                android:text="@string/connecting_to_pump_please_wait"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:textSize="18dp"
                android:layout_gravity="center"
                android:textColor="@color/colorPrimary"/>

        </LinearLayout>

    </LinearLayout>
</layout>