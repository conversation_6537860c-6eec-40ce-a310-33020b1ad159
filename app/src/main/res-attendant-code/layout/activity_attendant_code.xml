<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarAttendantCode"
            layout="@layout/toolbar" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_above="@+id/keypad"
            android:layout_below="@+id/toolbarAttendantCode"
            android:layout_marginTop="@dimen/_20sdp"
            android:background="#ffffff"
            android:gravity="center"
            android:orientation="vertical"
            tools:context=".MainActivity">

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scrollbars="none">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical">

                    <ImageView
                        android:id="@+id/ivAttendant"
                        android:layout_width="@dimen/_80sdp"
                        android:layout_height="@dimen/_80sdp"
                        android:layout_marginTop="@dimen/_20sdp"
                        android:src="@drawable/ic_attendant"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/pinMessage"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:gravity="center"
                        android:textColor="@color/red"
                        android:visibility="visible">

                    </androidx.appcompat.widget.AppCompatTextView>





                    <app.rht.petrolcard.utils.passwordview.PasswordView
                        android:id="@+id/password_view"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:layout_constraintEnd_toEndOf="@id/ivAttendant"
                        app:layout_constraintStart_toStartOf="@id/ivAttendant"
                        app:layout_constraintTop_toBottomOf="@id/ivAttendant"
                        app:password_between_margin="20dp"
                        app:password_correct_color="@color/green"
                        app:password_count="4"
                        app:password_filled_color="@color/light_blue"
                        app:password_incorrect_color="@color/red" />

                    <ProgressBar
                        android:id="@+id/progressBar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_margin="10dp"
                        android:visibility="gone">

                    </ProgressBar>

                </LinearLayout>

            </ScrollView>

        </LinearLayout>
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:gravity="bottom"
            android:id="@+id/keypad"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_height="wrap_content"
                tools:ignore="UselessParent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/text_1"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="1"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />
                    <TextView
                        android:id="@+id/text_2"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="2"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_3"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="3"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/text_4"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="4"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_5"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="5"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_6"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="6"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">
                    <TextView
                        android:id="@+id/text_7"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="7"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_8"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="8"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_9"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="9"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>
                <LinearLayout
                    android:layout_width="match_parent"
                    android:weightSum="3"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/text_d"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        app:tint="@color/red"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:padding="18dp"
                        android:src="@drawable/ic_delete_key"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_0"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="0"
                        android:onClick="btnClick"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />


                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/text_submit"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        app:tint="@color/green"
                        android:padding="18dp"
                        android:src="@drawable/ic_tick_done"
                        android:textSize="@dimen/input_button_text_size"
                        android:visibility="visible" />
                </LinearLayout>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/btnTagVerification"
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="5dp"
                    android:layout_marginBottom="5dp"
                    app:cardCornerRadius="8dp"
                    app:cardElevation="0dp"
                    android:visibility="visible"
                    app:cardBackgroundColor="@color/recycler_view_bg"
                    android:gravity="center">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:padding="5dp"
                        android:orientation="vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:text="@string/don_t_have_code"
                            android:backgroundTint="@color/white"
                            android:layout_height="wrap_content"/>

                        <TextView
                            android:layout_width="wrap_content"
                            android:text="@string/goto_tag_verification"
                            android:textAllCaps="true"
                            android:textColor="@color/colorPrimary"
                            android:backgroundTint="@color/white"
                            android:layout_height="wrap_content"/>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>
</layout>