<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <LinearLayout
        android:id="@+id/activity_etat"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:context=".ui.badge.activity.BadgeActivity">
        <include
            android:id="@+id/toolbarBadge"
            layout="@layout/toolbar" />
        <LinearLayout
            android:id="@+id/promptLayout"
            android:layout_marginBottom="10dp"
            android:gravity="center"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            >

            <TextView
                android:text="@string/scan_the_attendant_tag"
                android:layout_width="wrap_content"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/_12sdp"
                android:layout_height="wrap_content"
                android:textStyle="bold"
                android:id="@+id/prompt" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/insertCardImageLayout"
            android:layout_below="@+id/abortMessageLayout"
            android:gravity="center"
            android:layout_marginBottom="10dp"
            android:padding="10dp"
            android:layout_margin="@dimen/_40sdp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/insertCardImageView"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_gravity="center"
                android:adjustViewBounds="false"
                android:cropToPadding="false"
                android:src="@drawable/ic_attendant_nfc" />


            <com.github.ybq.android.spinkit.SpinKitView
                android:id="@+id/spin_kit"
                style="@style/SpinKitView.Large.ThreeBounce"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                app:SpinKit_Color="@color/colorPrimary"
                android:layout_marginTop="20dp"/>
        </LinearLayout>

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/btnCodeVerification"
            android:orientation="vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="5dp"
            android:layout_marginBottom="5dp"
            app:cardCornerRadius="8dp"
            app:cardElevation="0dp"
            android:visibility="gone"
            app:cardBackgroundColor="@color/recycler_view_bg"
            android:gravity="center">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:padding="5dp"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:text="@string/don_t_have_tag"
                    android:backgroundTint="@color/white"
                    android:layout_height="wrap_content"/>

                <TextView
                    android:layout_width="wrap_content"
                    android:text="@string/verify_your_code"
                    android:textAllCaps="true"
                    android:textColor="@color/colorPrimary"
                    android:backgroundTint="@color/white"
                    android:layout_height="wrap_content"/>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

    </LinearLayout>
</layout>
