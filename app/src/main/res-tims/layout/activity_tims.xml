<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <RelativeLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarTims"
            layout="@layout/toolbar" />
        <ScrollView
            android:layout_width="match_parent"
            android:layout_marginTop="@dimen/_35sdp"
            android:layout_marginBottom="@dimen/_20sdp"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:orientation="vertical"
                android:padding="20dp">

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/enter_customer_pin"
                    android:layout_marginBottom="@dimen/_5sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/customerPin"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:backgroundTint="@color/lightGrey"
                        android:textAlignment="textStart"
                        android:textColor="@color/black"
                        android:textColorHint="@color/grey"
                        android:maxLength="14" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/exemption_number"
                    android:layout_marginBottom="@dimen/_3sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/extemptionNumber"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/lightGrey"
                    android:textAlignment="textStart"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey"
                    android:maxLength="30"
                   />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/company_name"
                    android:layout_marginBottom="@dimen/_3sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/companyName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/lightGrey"
                    android:textAlignment="textStart"
                    android:textColor="@color/black"
                    android:maxLength="30"
                    android:textColorHint="@color/grey"
       />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/head_quarters"
                    android:layout_marginBottom="@dimen/_3sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/headQuarters"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/lightGrey"
                    android:textAlignment="textStart"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey"
                    android:maxLength="30"
                 />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/address"
                    android:layout_marginBottom="@dimen/_3sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/lightGrey"
                    android:textAlignment="textStart"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey"
                    android:maxLength="30"
                     />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/postal_code"
                    android:layout_marginBottom="@dimen/_3sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/postalCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/lightGrey"
                    android:textAlignment="textStart"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey"
                    android:inputType="number"
                    android:maxLength="30"
                    />
                </com.google.android.material.textfield.TextInputLayout>
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:hint="@string/city"
                    android:layout_marginBottom="@dimen/_3sdp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_height="wrap_content">
                <androidx.appcompat.widget.AppCompatEditText
                    android:id="@+id/city"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:backgroundTint="@color/lightGrey"
                    android:textAlignment="textStart"
                    android:textColor="@color/black"
                    android:textColorHint="@color/grey"
                    android:maxLength="20" />
                </com.google.android.material.textfield.TextInputLayout>
                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/errorMessage"
                    android:layout_width="wrap_content"
                    tools:text="@string/error"
                    android:textSize="11sp"
                    android:layout_margin="@dimen/_5sdp"
                    android:textColor="@color/red"
                    android:layout_height="wrap_content">

                </androidx.appcompat.widget.AppCompatTextView>


            </LinearLayout>
        </ScrollView>
        <LinearLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:layout_gravity="center"
            android:gravity="center"
            android:layout_margin="@dimen/_5sdp"
            android:layout_alignParentBottom="true"
            android:layout_marginTop="@dimen/_10sdp"
            android:weightSum="1"
            android:background="@color/white"
            android:layout_height="wrap_content">

            <Button
                android:id="@+id/cancelButton"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_height="@dimen/_45sdp"
                app:backgroundTint="@color/light_red"
                android:stateListAnimator="@null"
                android:text="@string/cancel"
                android:textAllCaps="true"
                android:textColor="#fff" />
            <com.google.android.material.button.MaterialButton
                android:id="@+id/submitButton"
                android:layout_width="0dp"
                android:layout_weight="0.5"
                android:layout_gravity="center"
                android:gravity="center"
                android:layout_marginStart="@dimen/_10sdp"
                android:layout_height="@dimen/_45sdp"
                android:stateListAnimator="@null"
                android:text="@string/submit"
                android:textAllCaps="true"
                android:textColor="#fff" />

        </LinearLayout>

    </RelativeLayout>


</layout>