<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.ticket.viewmodel.LoyaltyDialogViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_gravity="center"
        android:layout_width="wrap_content"
        android:layout_height="match_parent">


        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@android:color/transparent"
            android:layout_centerInParent="true"
            android:gravity="center"
            tools:ignore="UselessParent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginEnd="15dp"
                android:layout_marginStart="15dp"
                android:padding="10dp"
                android:backgroundTint="@color/colorWhite"
                android:background="@drawable/rounded_rectangle"
                android:id="@+id/relativeLayout">

                <TextView
                    android:id="@+id/confirmation"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerHorizontal="true"
                     android:layout_marginStart="-3dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginEnd="-3dp"
                    android:gravity="center"
                    android:text="@string/do_you_have_loyalty_card"
                    android:textAlignment="center"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold" />


                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_confirm"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/confirmation"
                    android:layout_marginStart="40dp"
                    android:layout_marginTop="30dp"
                    android:layout_marginEnd="40dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/rounded_rectangle_button"
                    android:backgroundTint="@color/gold"
                    android:elevation="0dp"
                    android:enabled="true"
                    android:padding="12dp"
                    android:text="@string/yes"
                    android:textAllCaps="false"
                    android:textColor="@color/colorWhite"
                    android:textSize="18sp"
                    android:visibility="visible"
                    tools:ignore="TextContrastCheck,TextContrastCheck" />

                <androidx.appcompat.widget.AppCompatButton
                    android:id="@+id/btn_cancel"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/btn_confirm"
                    android:layout_marginStart="40dp"
                    android:layout_marginEnd="40dp"
                    android:layout_marginBottom="20dp"
                    android:background="@android:color/transparent"
                    android:enabled="true"
                    android:padding="12dp"
                    android:text="@string/no"
                    android:textAllCaps="false"
                    android:textColor="#DD2C00"
                    android:textSize="18sp"
                    android:visibility="visible" />


                <RelativeLayout
                    android:visibility="gone"
                    android:layout_below="@+id/confirmation"
                    android:id="@+id/Card_relativeLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="15dp"
                    android:layout_marginStart="15dp"
                    android:padding="15dp"
                    android:backgroundTint="@color/colorWhite"
                    android:background="@drawable/rounded_corner_no_active"
                    >

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/arrow_animation"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:layout_marginBottom="-100dp"
                        android:rotation="90"
                        android:visibility="visible"
                        android:layout_gravity="center"
                        app:lottie_rawRes="@raw/back_back"
                        app:lottie_repeatMode="restart"
                        app:lottie_enableMergePathsForKitKatAndAbove="true"
                        app:lottie_loop="true"
                        app:lottie_speed="0.9"
                        app:lottie_autoPlay="true"/>

                    <ImageView
                        android:id="@+id/img_card_loy"
                        android:layout_width="wrap_content"
                        android:layout_below="@+id/arrow_animation"
                        android:layout_marginBottom="-40dp"
                        android:scaleY="0.7"
                        android:scaleX="0.7"
                        android:layout_height="wrap_content"
                        android:src="@drawable/card_loyalty_insert"
                        android:contentDescription="@string/card" />



                </RelativeLayout>

                <RelativeLayout
                    android:visibility="gone"
                    android:layout_below="@+id/confirmation"
                    android:id="@+id/searching_layout"
                    android:layout_width="match_parent"
                    android:layout_height="250dp"
                    android:layout_marginEnd="15dp"
                    android:layout_marginStart="15dp"
                    android:padding="15dp"
                    android:backgroundTint="@color/colorWhite"
                    android:background="@drawable/rounded_corner_no_active">

                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/searching_customer_animation"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:rotation="90"
                        android:visibility="visible"
                        android:layout_gravity="center"
                        app:lottie_rawRes="@raw/searching_customer"
                        app:lottie_repeatMode="restart"
                        app:lottie_enableMergePathsForKitKatAndAbove="true"
                        app:lottie_loop="true"
                        app:lottie_speed="0.9"
                        app:lottie_autoPlay="true"/>


                </RelativeLayout>


            </RelativeLayout>


            <RelativeLayout
                android:layout_width="96dp"
                android:layout_height="96dp"
                android:layout_marginTop="-50dp"
                android:background="@drawable/circular_bg"
                android:backgroundTint="@color/gold"
                android:layout_alignTop="@+id/relativeLayout"
                android:layout_centerHorizontal="true"
                >

                <ImageView
                    android:id="@+id/img"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:contentDescription="@string/card"
                    android:scaleX="1.5"
                    android:scaleY="1.5"
                    android:src="@drawable/ic_loyalty_card"
                    app:tint="@color/white"
                    tools:ignore="ImageContrastCheck" />


            </RelativeLayout>




        </RelativeLayout>
    </RelativeLayout>
</layout>

