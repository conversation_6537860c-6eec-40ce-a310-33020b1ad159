<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <LinearLayout
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:background="@color/trans_success_stroke_color"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/ticketLayout"
            android:orientation="vertical"
            android:background="@color/white"
            android:layout_width="match_parent"
            android:visibility="visible"
            android:layout_height="match_parent">
            <include
                android:id="@+id/toolbarTicket"
                layout="@layout/toolbar" />
            <LinearLayout
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_50sdp"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:id="@+id/layoutPrintingProgress">

                <LinearLayout
                    android:id="@+id/promptLayout"
                    android:layout_marginBottom="10dp"
                    android:gravity="center"
                    android:padding="10dp"
                    android:layout_marginTop="5dp"
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:text="@string/ticket_printing"
                        android:layout_width="wrap_content"
                        android:layout_marginStart="5dp"
                        android:layout_margin="0dp"
                        android:maxLines="2"
                        android:gravity="center"
                        android:textSize="20sp"
                        android:textAlignment="center"
                        android:textColor="@color/colorPrimary"
                        android:layout_height="wrap_content"
                        android:id="@+id/prompt" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/insertCardImageLayout"
                    android:gravity="center"
                    android:layout_marginBottom="10dp"
                    android:layout_marginTop="0dp"
                    android:visibility="visible"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">
                    <com.airbnb.lottie.LottieAnimationView
                        android:id="@+id/printing_view"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_gravity="center"
                        android:layout_marginTop="-50dp"
                        android:visibility="visible"
                        app:lottie_autoPlay="true"
                        app:lottie_enableMergePathsForKitKatAndAbove="true"
                        app:lottie_loop="false"
                        app:lottie_rawRes="@raw/printing"
                        app:lottie_repeatMode="restart" />


                </LinearLayout>

            </LinearLayout>

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_below="@id/toolbarTicket">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible"
                    android:padding="0dp"
                    android:layout_gravity="top"
                    android:layout_margin="10dp"
                    android:id="@+id/ivTicketPreview"/>

            </ScrollView>



        </RelativeLayout>
    </LinearLayout>

</layout>