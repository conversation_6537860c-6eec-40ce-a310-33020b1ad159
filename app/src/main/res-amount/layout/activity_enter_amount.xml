<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/toolbarEnterAmount"
            layout="@layout/toolbar" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/toolbarEnterAmount"
            android:layout_marginTop="@dimen/_20sdp"
            android:layout_marginBottom="@dimen/_10sdp"
            android:background="#ffffff"
            android:gravity="center"
            android:orientation="vertical"
            tools:context=".MainActivity">

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_margin="@dimen/_10sdp"
                android:layout_marginBottom="@dimen/_15sdp"
                android:background="@null"
                android:gravity="center"
                android:text="@string/enter_amount"
                android:textColor="@color/colorPrimary"
                android:textSize="@dimen/_16sdp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/ivAttendant"
                android:layout_width="@dimen/_70sdp"
                android:layout_height="@dimen/_70sdp"
                android:src="@drawable/ic_money"
                app:tint="@color/colorPrimary"
                android:contentDescription="@string/amount_pay" />

            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone">

            </ProgressBar>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_alignParentBottom="true"
            android:gravity="bottom"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:ignore="UselessParent">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:orientation="horizontal"
                    android:layout_gravity="center"
                    android:gravity="center"
                    android:paddingStart="10dp"
                    android:layout_marginVertical="10dp"
                    android:layout_marginHorizontal="10dp"
                    android:background="@drawable/rounded_corner_button"
                    android:backgroundTint="@color/lightGrey"
                    android:layout_height="wrap_content"
                    tools:ignore="RtlSymmetry">
                    <androidx.appcompat.widget.AppCompatEditText
                        android:id="@+id/amountTxt"
                        android:layout_width="0dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:textStyle="bold"
                        tools:text="11111"
                        android:cursorVisible="false"
                        android:textAlignment="textEnd"
                        android:textColor="@color/black"
                        android:textSize="@dimen/_20sdp"
                        android:layout_height="wrap_content"/>
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/currencyTxt"
                        android:layout_width="wrap_content"
                        android:background="@null"
                        android:textStyle="bold"
                        android:gravity="center"
                        tools:text="AED"
                        android:layout_marginStart="@dimen/_5sdp"
                        android:textColor="@color/black"
                        android:layout_gravity="center"
                        android:textSize="@dimen/_20sdp"
                        android:layout_height="wrap_content"/>
                    <ImageView
                        android:id="@+id/text_d"
                        android:layout_width="80dp"
                        android:layout_height="50dp"
                        android:layout_margin="@dimen/input_button_margin"
                        android:gravity="center"
                        android:padding="12dp"
                        android:src="@drawable/ic_delete_key"
                        android:textSize="@dimen/input_button_text_size"
                        app:tint="@color/red"
                        android:contentDescription="@string/delete" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="3">

                    <TextView
                        android:id="@+id/text_1"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_2"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="2"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_3"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="3"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="3">

                    <TextView
                        android:id="@+id/text_4"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="4"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_5"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="5"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_6"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="6"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="3">

                    <TextView
                        android:id="@+id/text_7"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="7"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_8"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="8"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                    <TextView
                        android:id="@+id/text_9"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="9"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:weightSum="3">

                    <TextView
                        android:id="@+id/text_dot"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="@string/decimal_dot"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size"
                        />

                    <TextView
                        android:id="@+id/text_0"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/new_border_gray_line"
                        android:gravity="center"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textSize="@dimen/input_button_text_size" />


                    <ImageView
                        android:id="@+id/text_submit"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/input_button_height"
                        android:layout_margin="@dimen/input_button_margin"
                        android:layout_weight="1"
                        android:background="@drawable/rounded_corner_button"
                        android:gravity="center"
                        android:padding="18dp"
                        android:src="@drawable/ic_tick_done"
                        android:textSize="@dimen/input_button_text_size"
                        android:visibility="visible"
                        app:tint="@color/white"
                        android:contentDescription="@string/submit" />
                </LinearLayout>
            </LinearLayout>

        </RelativeLayout>

    </RelativeLayout>
</layout>