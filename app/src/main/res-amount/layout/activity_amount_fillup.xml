<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>
    <RelativeLayout
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <include
            android:id="@+id/toolbarAmountFillup"
            layout="@layout/toolbar"
            android:layout_alignParentTop="true"
            android:layout_height="wrap_content"
            android:layout_width="match_parent"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/toolbarAmountFillup"
            android:orientation="vertical"
            android:padding="10dp">

            <ImageView
                android:layout_width="220dp"
                android:layout_height="200dp"
                android:layout_gravity="center"
                android:src="@drawable/ic_fuelling" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@string/please_select_fuelling_type"
                android:textAlignment="center"
                android:textColor="@color/colorPrimary"
                android:textSize="17sp" />

            <ScrollView
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/_10sdp"
                    android:orientation="vertical">

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/layout_amount_pay"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginVertical="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:onClick="onClick"
                        android:visibility="gone"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="10dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:layout_marginEnd="20dp"
                                android:padding="@dimen/_12sdp"
                                android:scaleX="1"
                                android:scaleY="1"
                                android:src="@drawable/ic_money"
                                app:tint="@color/colorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="3dp"
                                android:gravity="center"
                                android:text="@string/enter_amount"
                                android:textColor="@color/black"
                                android:textSize="16sp" />
                        </LinearLayout>


                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/layout_plain_qty"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginVertical="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:onClick="onClick"
                        android:visibility="gone"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="10dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:layout_marginEnd="20dp"
                                android:padding="18dp"
                                android:scaleX="1"
                                android:scaleY="1"
                                android:src="@drawable/enter_qty"
                                app:tint="@color/colorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="2dp"
                                android:gravity="center"
                                android:text="@string/enter_quantity"
                                android:textColor="@color/black"
                                android:textSize="16sp" />

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                    <com.google.android.material.card.MaterialCardView
                        android:id="@+id/layout_plain"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginVertical="5dp"
                        android:layout_weight="1"
                        android:clickable="true"
                        android:onClick="onClick"
                        android:visibility="gone"
                        app:cardCornerRadius="10dp"
                        app:cardElevation="10dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="horizontal">

                            <ImageView
                                android:layout_width="60dp"
                                android:layout_height="60dp"
                                android:layout_marginEnd="20dp"
                                android:padding="@dimen/_12sdp"
                                android:scaleX="1"
                                android:scaleY="1"
                                android:src="@drawable/ic_fulltank"
                                app:tint="@color/colorPrimary" />

                            <androidx.appcompat.widget.AppCompatTextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_gravity="center"
                                android:layout_marginTop="2dp"
                                android:gravity="center"
                                android:text="@string/full_tank"
                                android:textColor="@color/black"
                                android:textSize="16sp" />

                        </LinearLayout>
                    </com.google.android.material.card.MaterialCardView>

                </LinearLayout>

            </ScrollView>

        </LinearLayout>

    </RelativeLayout>

</layout>

