<vector android:autoMirrored="true" android:height="96.9dp"
    android:viewportHeight="96.9" android:viewportWidth="116.55"
    android:width="116.55dp"
    xmlns:aapt="http://schemas.android.com/aapt" xmlns:android="http://schemas.android.com/apk/res/android">
    <path android:fillColor="#c0ecfc" android:fillType="evenOdd" android:pathData="M59.17,28.94c-30.94,0 -56,13.3 -56,29.71s25.09,29.71 56,29.71 56,-13.31 56,-29.71S90.11,28.94 59.17,28.94Z"/>
    <path android:fillColor="#dadada" android:fillType="evenOdd" android:pathData="M10.35,22.44l0,-7.91l5.13,-3.17l3.03,6.44l-8.16,4.64z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M15.48,11.36l3.03,6.44l0,-10.65l-3.03,4.21z"/>
    <path android:fillColor="#1d1d1b" android:fillType="evenOdd" android:pathData="M18.51,7.15l-8.16,4.83l0,4.19l5.73,-3.54l2.43,-5.48z"/>
    <path android:fillColor="#dadada" android:fillType="evenOdd" android:pathData="M15.5,10.56l0.28,0.16c-0.72,0.61 -0.84,2.53 -1.29,2.27l-0.23,-0.14C13.39,12.36 14.64,10.06 15.5,10.56Z"/>
    <path android:fillColor="#7f7f7f" android:fillType="evenOdd" android:pathData="M15.18,10.82c0.51,-0.3 0.93,-0.07 0.93,0.52a2,2 0,0 1,-0.93 1.59c-0.51,0.3 -0.92,0.07 -0.92,-0.51A2.06,2.06 0,0 1,15.18 10.82Z"/>
    <path android:fillColor="#1d1d1b" android:fillType="evenOdd" android:pathData="M15.25,11.09c0.42,-0.24 0.76,-0.05 0.76,0.42a1.65,1.65 0,0 1,-0.76 1.3c-0.41,0.25 -0.75,0.06 -0.75,-0.42A1.7,1.7 0,0 1,15.25 11.09Z"/>
    <path android:fillColor="#dadada" android:fillType="evenOdd" android:pathData="M13.49,11.8l0.28,0.17c-0.72,0.61 -0.84,2.53 -1.29,2.26l-0.23,-0.13C11.38,13.61 12.63,11.31 13.49,11.8Z"/>
    <path android:fillColor="#7f7f7f" android:fillType="evenOdd" android:pathData="M13.17,12.07c0.51,-0.3 0.93,-0.07 0.93,0.51a2.06,2.06 0,0 1,-0.93 1.6c-0.51,0.3 -0.92,0.07 -0.92,-0.51A2,2 0,0 1,13.17 12.07Z"/>
    <path android:fillColor="#1d1d1b" android:fillType="evenOdd" android:pathData="M13.24,12.34c0.42,-0.24 0.76,-0.06 0.76,0.42a1.67,1.67 0,0 1,-0.76 1.3c-0.41,0.24 -0.75,0 -0.75,-0.42A1.69,1.69 0,0 1,13.24 12.34Z"/>
    <path android:fillColor="#dadada" android:fillType="evenOdd" android:pathData="M11.48,13.05l0.28,0.16c-0.72,0.61 -0.84,2.53 -1.29,2.27l-0.12,-0.07v-1.8C10.66,13.15 11.11,12.84 11.48,13.05Z"/>
    <path android:fillColor="#7f7f7f" android:fillType="evenOdd" android:pathData="M11.16,13.31c0.51,-0.3 0.93,-0.07 0.93,0.52a2.07,2.07 0,0 1,-0.93 1.6c-0.35,0.21 -0.66,0.16 -0.81,-0.09v-1A2.06,2.06 0,0 1,11.16 13.31Z"/>
    <path android:fillColor="#1d1d1b" android:fillType="evenOdd" android:pathData="M11.23,13.59c0.42,-0.25 0.76,-0.06 0.76,0.42a1.65,1.65 0,0 1,-0.76 1.29c-0.41,0.25 -0.75,0.06 -0.75,-0.41A1.67,1.67 0,0 1,11.23 13.59Z"/>
    <path android:fillColor="#db9669" android:fillType="evenOdd" android:pathData="M21.87,21.51c2.58,-0.15 7,-2.1 7.32,-2.11 0.5,-0.62 0.61,-2.07 -0.16,-1.77 -3,1.2 -5.12,0 -9.15,1 -0.57,0.16 -0.87,1.08 -0.8,2C19.2,21 20,21.61 21.87,21.51Z"/>
    <path android:fillColor="#db9669" android:fillType="evenOdd" android:pathData="M28.59,18c0.29,-0.43 0.48,-0.6 0.84,-0.6 0.75,0.09 2.91,0.27 3,0.28s0,0 0.22,0 0.24,0.32 -0.11,0.61c-0.06,0.53 -0.33,0.54 -0.71,0.6 0.08,0.29 -0.07,0.46 -0.92,0.53 -0.09,0.26 -0.06,0.24 -0.24,0.28 -0.56,0.05 -1.46,0.16 -1.88,-0.24l-0.46,-0.05a0.91,0.91 0,0 1,0 -1.44Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M29.51,17.14a8.64,8.64 0,0 1,1.38 -0.47c0.4,-0.1 0.65,-0.06 1.06,-0.16 0,0.33 -0.13,0.58 -0.88,0.69 -1.07,0.32 -1.13,1.25 -2,0.89C28.67,17.92 28.53,17.65 29.51,17.14Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M22.83,21a2.78,2.78 0,0 1,0.92 -0.21,5.63 5.63,0 0,0 1.16,0c0,0.35 -0.2,0.58 -1,0.6 -1,0.22 -0.81,0.71 -1.57,0.45 -0.25,-0.09 -0.56,-0.06 -0.4,-0.27A1.59,1.59 0,0 1,22.83 21Z"/>
    <path android:fillColor="#db9669" android:fillType="evenOdd" android:pathData="M16.37,13.46c-0.15,2 2.43,7.86 3.41,7.65a2.84,2.84 0,0 0,1.06 -2.43c-0.07,-2.1 0.41,-3.62 -0.92,-5.79 0.35,-4 -1.42,-4.5 -2.4,-4.29C17.4,9 14.18,9.21 16.37,13.46Z"/>
    <path android:fillColor="#d42000" android:fillType="evenOdd" android:pathData="M16.06,14.69a7.61,7.61 0,0 0,4.65 -1c-0.28,-2.33 -0.61,-6.8 -4,-5.82C13.73,9 15.45,12.6 16.06,14.69Z"/>
    <path android:fillColor="#db4d00" android:fillType="evenOdd" android:pathData="M12.35,9a20.84,20.84 0,0 0,-4.58 2.84A3.82,3.82 0,0 0,6.7 14.08c0,1 1.85,8.51 2.06,9.85s-0.59,1.95 0.54,2.53c1.6,3.81 8.41,6.28 9.14,3.11 -0.58,-2.75 0.82,-10.26 1,-14.92 0,-1.12 0.8,-5 -0.88,-6.46a3,3 0,0 0,-1.81 -0.6A14.78,14.78 0,0 0,12.35 9Z"/>
    <path android:fillColor="#dccf71" android:fillType="evenOdd" android:pathData="M17.33,52.56s0.54,2.44 -0.53,2c-0.46,-0.19 -0.35,0.75 -0.29,1.09a4.09,4.09 0,0 0,1.82 1.05c0.3,0 0.44,-0.15 0.71,0.12s0.18,0.56 0.61,0.9a3.79,3.79 0,0 0,3.51 0.33c0.65,-0.31 1,-0.52 0.75,-1.18a13.55,13.55 0,0 0,-2.38 -1.38,4.4 4.4,0 0,1 -2,-2.29c-0.12,-0.31 -0.52,-1.24 -0.52,-1.24S18.48,51.79 17.33,52.56Z"/>
    <path android:fillColor="#5a556e" android:fillType="evenOdd" android:pathData="M17.33,52.56A3.83,3.83 0,0 0,16.65 55a3.92,3.92 0,0 0,1.7 1.17c0.67,0 0.88,0.64 1.34,1a3.33,3.33 0,0 0,3.95 0.16c0.31,-0.29 0.12,-0.8 -0.06,-1 -0.32,-0.41 -1.55,-0.57 -2,-0.82a4.23,4.23 0,0 1,-2 -2.78,3.81 3.81,0 0,0 -0.51,-0.75S18.48,51.79 17.33,52.56Z"/>
    <path android:fillColor="#fc0" android:pathData="M20.82,56.24a0.07,0.07 0,0 0,0 0.09s0.08,0 0.09,0a1.24,1.24 0,0 1,0.44 -0.56,0.82 0.82,0 0,1 0.56,-0.13 0,0 0,0 0,0.06 -0.06,0.08 0.08,0 0,0 -0.07,-0.07 1,1 0,0 0,-0.64 0.14A1.34,1.34 0,0 0,20.82 56.24Z"/>
    <path android:fillColor="#fc0" android:pathData="M20.5,56s0,0.06 0,0.08a0.05,0.05 0,0 0,0.08 0,1.15 1.15,0 0,1 0.37,-0.49 0.75,0.75 0,0 1,0.49 -0.1,0.06 0.06,0 0,0 0.06,-0.06 0.09,0.09 0,0 0,-0.07 -0.06,0.86 0.86,0 0,0 -0.55,0.12A1.21,1.21 0,0 0,20.5 56Z"/>
    <path android:fillColor="#fc0" android:pathData="M20.17,55.68a0.06,0.06 0,0 0,0 0.08s0.07,0 0.08,0a1.07,1.07 0,0 1,0.38 -0.48,0.75 0.75,0 0,1 0.49,-0.11 0,0 0,0 0,0.05 0,0.09 0.09,0 0,0 -0.06,-0.07 0.84,0.84 0,0 0,-0.56 0.13A1.12,1.12 0,0 0,20.17 55.68Z"/>
    <path android:fillColor="#fc0" android:pathData="M19.85,55.4s0,0.06 0,0.08a0.05,0.05 0,0 0,0.08 0,1.09 1.09,0 0,1 0.38,-0.49 0.71,0.71 0,0 1,0.48 -0.1,0.05 0.05,0 0,0 0.06,-0.06 0.09,0.09 0,0 0,-0.07 -0.06,0.86 0.86,0 0,0 -0.55,0.12A1.15,1.15 0,0 0,19.85 55.4Z"/>
    <path android:fillColor="#fc0" android:pathData="M19.52,55.11a0.07,0.07 0,0 0,0 0.08,0 0,0 0,0 0.07,0 1.14,1.14 0,0 1,0.38 -0.48,0.76 0.76,0 0,1 0.49,-0.11s0.06,0 0.05,0a0.09,0.09 0,0 0,-0.06 -0.07,0.84 0.84,0 0,0 -0.56,0.13A1.12,1.12 0,0 0,19.52 55.11Z"/>
    <path android:fillColor="#fc0" android:pathData="M19.2,54.83a0.07,0.07 0,0 0,0 0.08s0.07,0 0.08,0a1.09,1.09 0,0 1,0.38 -0.49,0.71 0.71,0 0,1 0.48,-0.1 0.05,0.05 0,0 0,0.06 -0.06,0.07 0.07,0 0,0 -0.07,-0.06 0.86,0.86 0,0 0,-0.55 0.12A1.15,1.15 0,0 0,19.2 54.83Z"/>
    <path android:fillColor="#fc0" android:pathData="M18.87,54.54a0.07,0.07 0,0 0,0 0.08,0.05 0.05,0 0,0 0.08,0 1.06,1.06 0,0 1,0.37 -0.48,0.76 0.76,0 0,1 0.49,-0.11s0.06,0 0,0a0.09,0.09 0,0 0,-0.06 -0.07,0.84 0.84,0 0,0 -0.56,0.13A1.19,1.19 0,0 0,18.87 54.54Z"/>
    <path android:fillColor="#dccf71" android:fillType="evenOdd" android:pathData="M6.75,57.45s0.54,2.44 -0.53,2c-0.46,-0.19 -0.35,0.75 -0.29,1.08A4,4 0,0 0,7.75 61.6c0.3,0 0.44,-0.15 0.71,0.12s0.18,0.56 0.61,0.9a3.79,3.79 0,0 0,3.51 0.33c0.65,-0.31 1,-0.52 0.75,-1.18A13.55,13.55 0,0 0,11 60.39,4.4 4.4,0 0,1 9,58.1c-0.12,-0.31 -0.52,-1.24 -0.52,-1.24S7.9,56.68 6.75,57.45Z"/>
    <path android:fillColor="#5a556e" android:fillType="evenOdd" android:pathData="M6.75,57.45a3.83,3.83 0,0 0,-0.68 2.46,3.92 3.92,0 0,0 1.7,1.17c0.67,0 0.88,0.64 1.34,1a3.33,3.33 0,0 0,4 0.16c0.31,-0.29 0.12,-0.8 -0.06,-1 -0.32,-0.41 -1.55,-0.57 -2.05,-0.82a4.23,4.23 0,0 1,-2 -2.78,3.81 3.81,0 0,0 -0.51,-0.75S7.9,56.68 6.75,57.45Z"/>
    <path android:fillColor="#fc0" android:pathData="M10.24,61.13a0.07,0.07 0,0 0,0 0.09s0.08,0 0.09,0a1.24,1.24 0,0 1,0.44 -0.56,0.82 0.82,0 0,1 0.56,-0.13 0,0 0,0 0,0.06 -0.06,0.08 0.08,0 0,0 -0.07,-0.07 1,1 0,0 0,-0.64 0.14A1.34,1.34 0,0 0,10.24 61.13Z"/>
    <path android:fillColor="#fc0" android:pathData="M9.92,60.86s0,0.06 0,0.08a0.05,0.05 0,0 0,0.08 0,1.15 1.15,0 0,1 0.37,-0.49 0.75,0.75 0,0 1,0.49 -0.1,0.06 0.06,0 0,0 0.06,-0.06 0.09,0.09 0,0 0,-0.07 -0.06,0.86 0.86,0 0,0 -0.55,0.12A1.21,1.21 0,0 0,9.92 60.86Z"/>
    <path android:fillColor="#fc0" android:pathData="M9.59,60.57a0.06,0.06 0,0 0,0 0.08s0.07,0 0.08,0a1.07,1.07 0,0 1,0.38 -0.48,0.75 0.75,0 0,1 0.49,-0.11 0,0 0,0 0,0 0,0.09 0.09,0 0,0 -0.06,-0.07 0.84,0.84 0,0 0,-0.56 0.13A1.12,1.12 0,0 0,9.59 60.57Z"/>
    <path android:fillColor="#fc0" android:pathData="M9.27,60.29s0,0.06 0,0.08a0.05,0.05 0,0 0,0.08 0,1.09 1.09,0 0,1 0.38,-0.49 0.71,0.71 0,0 1,0.48 -0.1,0.05 0.05,0 0,0 0.06,-0.06 0.09,0.09 0,0 0,-0.07 -0.06,0.86 0.86,0 0,0 -0.55,0.12A1.15,1.15 0,0 0,9.27 60.29Z"/>
    <path android:fillColor="#fc0" android:pathData="M8.94,60a0.07,0.07 0,0 0,0 0.08,0 0,0 0,0 0.07,0 1.14,1.14 0,0 1,0.38 -0.48,0.76 0.76,0 0,1 0.49,-0.11s0.06,0 0.05,0a0.09,0.09 0,0 0,-0.06 -0.07,0.84 0.84,0 0,0 -0.56,0.13A1.12,1.12 0,0 0,8.94 60Z"/>
    <path android:fillColor="#fc0" android:pathData="M8.62,59.72a0.07,0.07 0,0 0,0 0.08s0.07,0 0.08,0a1.09,1.09 0,0 1,0.38 -0.49,0.71 0.71,0 0,1 0.48,-0.1 0.05,0.05 0,0 0,0.06 -0.06,0.07 0.07,0 0,0 -0.07,-0.06 0.8,0.8 0,0 0,-0.55 0.12A1.15,1.15 0,0 0,8.62 59.72Z"/>
    <path android:fillColor="#fc0" android:pathData="M8.29,59.43a0.07,0.07 0,0 0,0 0.08,0.05 0.05,0 0,0 0.08,0A1.06,1.06 0,0 1,8.78 59a0.76,0.76 0,0 1,0.49 -0.11s0.06,0 0.05,0a0.09,0.09 0,0 0,-0.06 -0.07,0.84 0.84,0 0,0 -0.56,0.13A1.19,1.19 0,0 0,8.29 59.43Z"/>
    <path android:fillColor="#357cbc" android:fillType="evenOdd" android:pathData="M12.47,30.56c0.53,1.61 0.92,3.13 1.32,4.77 0.65,2 2.11,4.57 1.87,7 -0.43,4.41 0.46,7.7 0.37,11.58 1.15,0.9 4.68,0.2 4.1,-1.26C19.76,48 20.06,42.5 19.93,41c0,-0.18 -0.14,-1.36 -0.13,-2A73.73,73.73 0,0 0,18.48 25.6a13.55,13.55 0,0 1,-4.33 1.5C14.13,27.68 12.4,30.33 12.47,30.56Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M8.34,44.25l-0.06,0.56c-1.69,2.92 -2,10.47 -3,13 0.57,1.18 3.65,2 4.68,0.77a64.18,64.18 0,0 1,2.16 -10.2c0.08,-0.22 0.73,-1.74 0.8,-2 1.29,-4.31 2.67,-5.82 3.14,-13.23 2.42,-0.37 1.18,-3.89 1.31,-7 -4.05,2.7 -7.09,1.64 -8.09,0.86 -1.35,2.15 -0.89,6.24 -0.11,6.74 -0.38,3.74 -0.23,7.26 -0.58,10Z"/>
    <path android:fillColor="#357cbc" android:fillType="evenOdd" android:pathData="M9.12,27.32c1.46,1.27 7.57,1.69 9.53,-1 0,-0.31 0.06,-0.89 0,-1.2 -2.22,1.61 -6.92,2.76 -9.39,1.37Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M14.75,8.91c1.83,-0.56 3.37,2.26 3,7.9 0.61,0.23 0.85,-0.16 1.57,-0.35 0.25,-4.67 0.14,-7.81 -2.48,-8.51A5.61,5.61 0,0 0,14.75 8.91Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M10.86,27c1.77,-2.32 2.17,-6.15 1.38,-10.66a11.7,11.7 0,0 0,7.2 -3.41c-0.1,3.51 0.27,10.27 -0.86,12.41C17.62,26.91 13.44,27.79 10.86,27Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M7.9,11.72c1.67,-0.56 3.67,1.24 5,6.89A3.89,3.89 0,0 0,15 18.44c-1.1,-4.68 -2.57,-8.63 -4.83,-8.26A6.77,6.77 0,0 0,7.9 11.72Z"/>
    <path android:fillColor="#357cbc" android:fillType="evenOdd" android:pathData="M14.11,24.06a18.09,18.09 0,0 0,-0.19 -6.68,8.9 8.9,0 0,0 4.59,-2.32c-0.07,2.47 0.67,5.76 -0.13,7.26A11,11 0,0 1,14.11 24.06Z"/>
    <path android:fillColor="#357cbc" android:fillType="evenOdd" android:pathData="M11.78,44.16a16.42,16.42 0,0 1,0.87 -5.8A6.38,6.38 0,0 1,9 36.43c-0.46,2.46 -1.21,5.67 -0.33,6.3A6.8,6.8 0,0 0,11.78 44.16Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M12.24,38.43A8.63,8.63 0,0 1,8.88 36.6c-0.2,0.49 -0.41,1 -0.62,1.48A6.76,6.76 0,0 0,11.62 40,4.39 4.39,0 0,0 12.24,38.43Z"/>
    <path android:fillColor="#357cbc" android:fillType="evenOdd" android:pathData="M15.11,28.86a29.32,29.32 0,0 1,-0.77 8,7.32 7.32,0 0,1 -4.58,-1.08c0.13,-1.21 0.22,-2 0.44,-3.29a4.27,4.27 0,0 0,3 -3.68A6.66,6.66 0,0 0,15.11 28.86Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M14.21,17.63a10.52,10.52 0,0 0,4.13 -2.26l0.77,1.83A8.48,8.48 0,0 1,15 19.58,5.26 5.26,0 0,1 14.21,17.63Z"/>
    <path android:fillColor="#1e3b4c" android:fillType="evenOdd" android:pathData="M9.18,38.48c0.08,-0.09 0,-0.29 -0.09,-0.44s-0.31,-0.21 -0.4,-0.12 0,0.29 0.09,0.45S9.09,38.57 9.18,38.48Z"/>
    <path android:fillColor="#1e3b4c" android:fillType="evenOdd" android:pathData="M11.51,39.71c0.09,-0.09 0.05,-0.29 -0.09,-0.44s-0.31,-0.21 -0.39,-0.12 0,0.29 0.08,0.45S11.43,39.8 11.51,39.71Z"/>
    <path android:fillColor="#1e3b4c" android:fillType="evenOdd" android:pathData="M17.93,17.5c-0.08,-0.09 0,-0.29 0.09,-0.44s0.31,-0.21 0.4,-0.12 0,0.29 -0.09,0.45S18,17.59 17.93,17.5Z"/>
    <path android:fillColor="#1e3b4c" android:fillType="evenOdd" android:pathData="M15.28,19c-0.09,-0.09 0,-0.29 0.09,-0.44s0.31,-0.21 0.39,-0.11 0.05,0.29 -0.09,0.44S15.36,19.11 15.28,19Z"/>
    <path android:fillColor="#db9669" android:fillType="evenOdd" android:pathData="M12.33,6.09c0,1 -0.35,2.2 -0.35,3.21 0,0.79 1.36,1.5 1.79,1.75 2,1.17 2.58,-0.11 2.42,-1.11a8.34,8.34 0,0 0,-0.37 -1.45c-0.17,-0.5 -1.13,-0.42 -1.13,-0.94Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M19.13,3.22A7.49,7.49 0,0 0,19.18 5c0.14,0.7 -0.71,0.77 -0.25,1.79a7.15,7.15 0,0 1,-0.64 2.34,2.21 2.21,0 0,1 -1.19,1.54c-1.27,0.59 -2.31,-0.73 -3.41,-1.31s-0.36,-1.94 -0.57,-2.95c-0.38,-1.82 0.06,-4.15 2.08,-4.61a5.12,5.12 0,0 1,3.14 0.31C18.63,2.27 19.26,2.62 19.13,3.22Z"/>
    <path android:fillColor="#87470f" android:fillType="evenOdd" android:pathData="M14.89,5.22c-0.35,0.1 -0.43,0.13 -0.59,0.53a3.49,3.49 0,0 0,0.21 2.41c0,0.08 -0.68,-0.89 -1.06,-1.15 -0.75,-1.13 -0.42,1.25 -1.4,1.83a2.55,2.55 0,0 1,-1 -0.54c1.59,-1.58 -0.53,-5.32 1.31,-6.88 2.1,0.48 2.84,0.61 3.47,1.91A2.31,2.31 0,0 1,14.89 5.22Z"/>
    <path android:fillColor="#87470f" android:fillType="evenOdd" android:pathData="M16.29,0.51c3,1 3.77,3.16 2.85,4 -0.42,0.38 -1.45,-0.26 -3.28,-0.95 -0.49,-1 -1.36,-1.52 -3.47,-2.15C13.23,-0.23 15.52,0.25 16.29,0.51Z"/>
    <path android:fillColor="#221f1f" android:fillType="evenOdd" android:pathData="M16.2,8.3l0,0.09S16.21,8.33 16.2,8.3Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M12.37,6.27c0.14,0.66 0.5,1.14 0.79,1.07s0.48,-0.68 0.33,-1.33 -0.58,-1.11 -0.86,-1S12.22,5.61 12.37,6.27Z"/>
    <path android:fillColor="#357cbc" android:fillType="evenOdd" android:pathData="M19.12,4.38a14.14,14.14 0,0 1,1.79 2.17c-1.32,-0.69 -2.52,0 -3.35,0.21s-2,-1.5 -2.82,-2.11C17.31,5.26 18.55,4.85 19.12,4.38Z"/>
    <path android:fillColor="#449eff" android:fillType="evenOdd" android:pathData="M19.2,4.71c-1.48,0.87 -5.44,0 -7.64,-1.37 0.24,-3.4 2.7,-3.66 4.87,-3.12C18.84,0.82 20.29,2.92 19.2,4.71Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M8.14,17.83c0.25,2 3.92,7.23 4.84,6.83a2.82,2.82 0,0 0,0.57 -2.59c-0.48,-2.05 -0.3,-3.63 -2,-5.5 -0.44,-4 -2.27,-4.13 -3.19,-3.74C8.28,13.22 5.17,14.08 8.14,17.83Z"/>
    <path android:fillColor="#f60" android:fillType="evenOdd" android:pathData="M7.78,18.84a7.58,7.58 0,0 0,4.52 -1.51C11.78,15 11,10.63 7.72,12 4.86,13.39 7,16.82 7.78,18.84Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M14.51,24.88c2.58,0.05 7.14,-1.57 7.46,-1.55 0.54,-0.59 0.76,-2 0,-1.78 -3.05,1 -5.11,-0.41 -9.2,0.3 -0.58,0.12 -1,1 -1,1.91C11.88,24.17 12.68,24.85 14.51,24.88Z"/>
    <path android:fillColor="#fff" android:pathData="M17.74,4.14L17.74,3.89h0.13c0,-0.17 0,-0.35 -0.05,-0.52s0,-0.35 -0.05,-0.52h0l0,0.21L17.77,2.43l0.24,0 0.12,1c0,0.34 0.07,0.68 0.11,1l-0.29,0 0,-0.37ZM18.4,3.86L18.4,3.68l0.29,-0.05v0.21a0.52,0.52 0,0 0,0 0.24,0.07 0.07,0 0,0 0.1,0A0.14,0.14 0,0 0,18.92 4a0.47,0.47 0,0 0,0 -0.19,0.49 0.49,0 0,0 0,-0.22 0.7,0.7 0,0 0,-0.14 -0.17l-0.14,-0.13a0.65,0.65 0,0 1,-0.16 -0.21,0.55 0.55,0 0,1 0,-0.27 0.68,0.68 0,0 1,0.11 -0.4,0.51 0.51,0 0,1 0.33,-0.18c0.16,0 0.26,0 0.32,0.07a0.73,0.73 0,0 1,0.06 0.4v0.13L19,2.94L19,2.79a0.56,0.56 0,0 0,0 -0.21s0,-0.05 -0.09,0a0.16,0.16 0,0 0,-0.11 0.07,0.33 0.33,0 0,0 0,0.15 0.26,0.26 0,0 0,0 0.16l0.11,0.12 0.16,0.15a0.76,0.76 0,0 1,0.18 0.23,0.54 0.54,0 0,1 0.05,0.29 0.94,0.94 0,0 1,-0.11 0.46,0.49 0.49,0 0,1 -0.34,0.22c-0.16,0 -0.27,0 -0.32,-0.09A0.75,0.75 0,0 1,18.4 3.86ZM16.55,4.51a0.4,0.4 0,0 1,-0.29 -0.2,1 1,0 0,1 -0.08,-0.5c0,-0.15 0,-0.3 0,-0.45l0,-0.45a0.69,0.69 0,0 1,0.12 -0.41,0.38 0.38,0 0,1 0.35,-0.11 0.37,0.37 0,0 1,0.37 0.25,1.35 1.35,0 0,1 0,0.38v0.15l-0.28,0c0,-0.07 0,-0.15 0,-0.22a0.53,0.53 0,0 0,0 -0.2,0.14 0.14,0 0,0 -0.11,-0.07c-0.05,0 -0.09,0 -0.11,0.06a0.54,0.54 0,0 0,0 0.24c0,0.15 0,0.3 0,0.45s0,0.3 0,0.45a0.77,0.77 0,0 0,0 0.29,0.12 0.12,0 0,0 0.11,0.09 0.1,0.1 0,0 0,0.12 -0.07,0.65 0.65,0 0,0 0.05,-0.29L16.8,3.6h-0.15L16.65,3.36l0.42,0L17,4c0,0.19 0,0.38 0,0.57h-0.13l0,-0.21a0.31,0.31 0,0 1,-0.11 0.16,0.31 0.31,0 0,1 -0.19,0ZM17.74,2.43v0.62l0,0.32c0,0.17 -0.06,0.35 -0.08,0.52h0.13v0.25h-0.17l-0.06,0.38h-0.29l0.18,-1.05 0.18,-1Z"/>
    <path android:fillColor="#333" android:pathData="M21,26.31a0.54,0.54 0,0 0,-0.08 -0.75,0.53 0.53,0 0,0 -0.75,0.08 39,39 0,0 0,-4.38 7.89C12,41.65 2.64,47.1 1.16,36.14c-0.77,-7 4.17,-11 7.54,-12.56l-0.29,-1.39c-5.83,3 -9,7.58 -8.31,14.06C1.7,48.45 12.43,43.36 16.79,34A37.64,37.64 0,0 1,21 26.31Z"/>
    <path android:fillColor="#7f7f7f" android:fillType="evenOdd" android:pathData="M22.11,23.13l-1.83,2.2h0c-0.12,0.15 0,0.46 0.29,0.69s0.61,0.3 0.74,0.15h0L23.14,24Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M21.57,24.05 L20.3,25.57a1,1 0,0 0,0.31 0.4l0,0L22,24.38Z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M25.43,21.22c0.15,-0.14 0.32,-0.26 0.42,-0.22l0.43,0.23a5.8,5.8 0,0 0,-0.85 1.46L25.43,24.1a12,12 0,0 1,1.16 -2.51c0.5,-0.8 1.23,0.35 1,0.81l-1.52,2.75a2.18,2.18 0,0 1,-0.63 0v0.48l0.38,0.09 -0.38,0.74v0.9c1,-1.78 1.85,-3.31 2.85,-5.09l-0.66,-1.5L26,19.93l-0.27,0.29 -0.32,0.61ZM25.05,21.53c-0.09,0.18 0.13,-0.09 0.38,-0.31v-0.39l-0.38,0.7ZM25.43,22.69a9.66,9.66 0,0 0,-0.61 2.16,3.42 3.42,0 0,1 -1.45,-1.29l-0.43,0.52a4.76,4.76 0,0 0,1.72 1.3,3 3,0 0,0 0.13,1.2 0.36,0.36 0,0 1,-0.68 0.24c-0.13,-0.23 -0.25,-0.46 -0.37,-0.7 -0.29,-0.46 -0.69,0.05 -0.45,0.43 0.6,0.91 1.68,1.58 2.05,0.91l0.09,-0.15v-0.9l-0.18,0.35a2.54,2.54 0,0 1,-0.2 -1.27l0.38,0.09L25.43,25.1L25.16,25a8.62,8.62 0,0 1,0.27 -0.92Z"/>
    <path android:fillColor="#b2b2b2" android:pathData="M36,16.8l-3.59,-0.06h-0.11l-0.09,0 -4.3,1.57 0.38,1 4.21,-1.54 3.48,0.06C36.57,17.9 36.56,16.81 36,16.8Z"/>
    <path android:fillColor="#fff" android:pathData="M36.16,17.2a0.1,0.1 0,0 0,0.1 -0.09,0.11 0.11,0 0,0 -0.1,-0.11l-3.79,-0.07h0l-3.48,1.27a0.09,0.09 0,0 0,-0.05 0.12,0.09 0.09,0 0,0 0.12,0.06l3.47,-1.26Z"/>
    <path android:fillColor="#706f6f" android:fillType="evenOdd" android:pathData="M25.58,21.47c0.14,-0.12 0.29,-0.2 0.42,-0.15l0.43,0.23A6.12,6.12 0,0 0,25.58 23v1.41a12,12 0,0 1,1.16 -2.51c0.51,-0.8 1.23,0.35 1,0.81l-1.52,2.75a2.21,2.21 0,0 1,-0.63 0v0.48L26,26l-0.38,0.75v0.89c1,-1.78 1.85,-3.31 2.85,-5.09L27.78,21l-1.6,-0.79 -0.28,0.29 -0.32,-0.27ZM24.75,19.58L21.31,23.7h0c-0.16,0.2 0,0.62 0.4,0.94s0.83,0.4 1,0.2h0l2.51,-3a2.26,2.26 0,0 1,0.37 -0.39v-1.2l-0.83,-0.69ZM25.58,23v1.41c-0.11,0.31 -0.19,0.62 -0.27,0.92l0.27,0.08v0.48l-0.38,-0.08a2.52,2.52 0,0 0,0.2 1.26l0.18,-0.34v0.89l-0.08,0.15c-0.38,0.68 -1.45,0 -2.05,-0.91 -0.24,-0.38 0.15,-0.89 0.44,-0.43l0.37,0.7a0.36,0.36 0,0 0,0.68 -0.24,3.11 3.11,0 0,1 -0.13,-1.19 4.79,4.79 0,0 1,-1.72 -1.31l0.43,-0.52A3.48,3.48 0,0 0,25 25.17,10.22 10.22,0 0,1 25.58,23Z"/>
    <path android:fillColor="#f60" android:fillType="evenOdd" android:pathData="M28.25,18.08 L25,18.5a4.22,4.22 0,0 0,-1.56 1.58c0.15,0.14 0.31,0.37 0.46,0.51 0.77,0 1.57,-0.19 2,0.2 1.12,0.94 1.23,0.41 1.73,0.77a2.22,2.22 0,0 1,0.76 1.09l0.21,-0.4a4,4 0,0 0,-0.42 -1.81l-0.35,-0.54 0.94,-0.43C29.14,19.3 28.75,18 28.25,18.08Z"/>
    <path android:fillColor="#d42000" android:fillType="evenOdd" android:pathData="M23.39,20.08a2.21,2.21 0,0 0,1.67 -0.56,0.33 0.33,0 0,1 0.55,0.09c0.38,0.65 1.9,-0.33 3.13,-0.7a0.89,0.89 0,0 1,0 0.56l-0.94,0.43 0.35,0.54a4,4 0,0 1,0.42 1.81l-0.21,0.4a2.22,2.22 0,0 0,-0.76 -1.09c-0.5,-0.36 -0.61,0.17 -1.73,-0.77 -0.46,-0.39 -1.26,-0.21 -2,-0.2C23.7,20.45 23.54,20.22 23.39,20.08Z"/>
    <path android:fillColor="#fff" android:pathData="M28.49,18.31c0.08,0 0,-0.15 -0.12,-0.14l-3,0.41c-0.23,0 0.08,0.41 0.19,0.39Z"/>
    <path android:fillColor="#db9669" android:fillType="evenOdd" android:pathData="M24.26,23.58a1,1 0,0 0,0.49 -0.21c0.08,-0.08 0.18,-0.23 0.08,-0.31 0.21,-0.11 0.34,-0.46 0.24,-0.57 0.15,-0.13 0.37,-0.28 0.23,-0.62 0,-0.11 -0.1,-0.38 -0.15,-0.37A2.43,2.43 0,0 0,24.26 23.58Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M21.66,22.19a1.23,1.23 0,0 1,0.67 -1,2.83 2.83,0 0,1 2.54,0c0.32,0.12 0.44,0.06 0.27,0.4 0.17,0.53 0,0.61 -0.38,0.83 0.13,0.14 0,0.59 -0.34,0.73 0,0.3 -0.09,0.4 -0.4,0.45a4,4 0,0 1,-1.67 -0.13C21.22,23.27 21.26,22.8 21.66,22.19Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M24.79,21.86l1.28,0.33c0.39,0.1 0.48,-0.38 0.1,-0.51L25,21.27C24.57,21.14 24.4,21.76 24.79,21.86Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M32.7,17.69l-1.24,-0.15c-0.38,0 -0.41,0.41 0,0.49l1.21,0.24C33,18.34 33.08,17.74 32.7,17.69Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M32.27,18.16 L30.89,18c-0.42,0 -0.45,0.46 0,0.54l1.34,0.27C32.61,18.88 32.69,18.21 32.27,18.16Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M31.69,18.73l-1.16,-0.14c-0.36,0 -0.38,0.39 0,0.46l1.13,0.22C32,19.34 32,18.78 31.69,18.73Z"/>
    <path android:fillColor="#f2b387" android:fillType="evenOdd" android:pathData="M30.81,19.26l-0.9,-0.39c-0.27,-0.12 -0.4,0.21 -0.13,0.35l0.85,0.45C30.9,19.81 31.09,19.38 30.81,19.26Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M60.45,84 L49.86,78.69l8.34,-5.3C61.27,75.84 61.87,79.46 60.45,84Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M52.48,77.59c-6.61,-0.72 -11.84,13.08 -7.77,16.09l4.65,2.7 9,-15.52Z"/>
    <path android:fillColor="#616160" android:fillType="evenOdd" android:pathData="M50.19,96.74c2.88,0.9 6.8,-2.08 8.74,-6.63s1.18,-9 -1.7,-9.87 -6.8,2.08 -8.74,6.64S47.31,95.85 50.19,96.74Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M111.95,55.69l-10.65,-5.62l8.35,-5.3l2.3,10.92z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M103,49.2c-6.61,-0.72 -11.84,13.07 -7.77,16.09L99.92,68l9,-15.52Z"/>
    <path android:fillColor="#616160" android:fillType="evenOdd" android:pathData="M100.75,68.35c2.88,0.89 6.8,-2.08 8.74,-6.64s1.18,-9 -1.71,-9.86S101,53.93 99,58.49 97.87,67.46 100.75,68.35Z"/>
    <path android:fillColor="#00a3df" android:fillType="evenOdd" android:pathData="M71.44,25c4.06,-1.07 9.38,-4.06 13.4,-4.7a16.66,16.66 0,0 1,8.33 2c12.07,2.6 15.94,7.27 20.12,12.42 1.78,2 2.57,3.65 2.63,5 0.2,4.47 -0.9,11.65 -4,16 -2.29,-11.61 -14.32,-4.09 -15.73,8.05L60.34,84.1c0.4,-10.65 -11.93,-3.92 -17.23,9.78l-7.25,-1.65c-11.18,-3.45 -20.95,-8.81 -24.29,-13.55 -2.19,-4.34 -2.29,-8.57 -1.08,-10.85 -0.4,-3.7 0.81,-7.93 3.13,-12.53Z"/>
    <path android:fillColor="#007fc6" android:fillType="evenOdd" android:pathData="M115.94,39.59c0.42,3 0.82,8.11 0.49,9.69 -0.5,2.36 -3.08,4.85 -4.48,6.41 -2,-11.45 -14.32,-4.09 -15.73,8.05L60.34,84.1c0.41,-10.65 -11.93,-3.92 -17.23,9.78l-7.25,-1.65c0.58,-2.94 1.13,-5.52 1.69,-7.74h0c-13,-2.6 -23.24,-7.23 -27.2,-16.37 0,-0.1 0.09,-0.2 0.14,-0.3s0,-0.38 0,-0.57C10.2,64 11.82,59.56 13,56.59c2.49,11 23.89,19.91 34.7,15.24 14.1,-7.49 16.87,-6.63 29.19,-12.64C88,53.79 97.21,49.23 109.4,43c0.94,-0.48 1.84,-0.71 2.26,-0.08C114.72,47.52 115.28,43.54 115.94,39.59Z"/>
    <path android:fillColor="#005ca8" android:fillType="evenOdd" android:pathData="M96.44,62.33c-0.09,0.47 -0.16,0.94 -0.22,1.41L60.34,84.1a13.07,13.07 0,0 0,0 -1.57Z"/>
    <path android:fillColor="#1f52b2" android:fillType="evenOdd" android:pathData="M13.62,55.3c2.36,6 11.74,12.37 21.93,15.31a11.9,11.9 0,0 0,6.7 -0.53L46.4,59C36.94,55 29.59,51.26 22.33,45A61.23,61.23 0,0 0,13.62 55.3Z"/>
    <path android:fillColor="#1f52b2" android:fillType="evenOdd" android:pathData="M52.6,54.87c-0.23,3.85 0.88,9.12 3.19,10.37 4.37,-2 8.24,-3.52 14.29,-6.32C82.33,53.26 86.73,50.38 92.92,46c4.09,-2.89 4.44,-7.21 3.2,-9.68C92.74,29.59 82.69,25 72.42,24.77l-11.19,0.57C56.84,25.56 57.47,46.71 52.6,54.87Z"/>
    <path android:fillColor="#00a3df" android:fillType="evenOdd" android:pathData="M97.16,41.88c4.65,-2 10.71,-5.21 15.58,-7.21a11.78,11.78 0,0 1,-1.07 6.43c-4.92,2.42 -9.25,5 -15.85,7.31C97.7,46.72 96.69,42.78 97.16,41.88Z"/>
    <path android:fillColor="#2daae2" android:fillType="evenOdd" android:pathData="M80,37.23l15.67,6.52c-0.59,1.61 -4,3 -2.7,2.25l0.39,-0.23L78,39.15C79,38.56 79.14,38.14 80,37.23Z"/>
    <path android:fillColor="#007fc6" android:fillType="evenOdd" android:pathData="M93.28,45.79 L77.79,39.26l0.36,-0.21a15.41,15.41 0,0 0,2 -1.32l15,6.38C95,44.63 94,45.24 93.28,45.79Z"/>
    <path android:fillColor="#005ca8" android:pathData="M94.82,64.25l-0.12,0c0.73,-4.12 3,-16.63 -0.07,-19.49L79.29,38.39l0,-0.11L94.7,44.65C97.47,47.23 96.1,57.07 94.82,64.25Z"/>
    <path android:fillColor="#005fa7" android:fillType="evenOdd" android:pathData="M73.78,65.07c0.55,0.28 2.31,-0.92 3,-1.74a1.52,1.52 0,0 0,-0.63 -2.26c-0.72,-0.25 -2.06,0.71 -2.54,1.51S73.23,64.79 73.78,65.07Z"/>
    <path android:fillColor="#00a3df" android:fillType="evenOdd" android:pathData="M77.35,62.69A22.38,22.38 0,0 1,75.25 64a19.23,19.23 0,0 1,-2.21 0.89,0.5 0.5,0 0,1 -0.21,-0.52 1.52,1.52 0,0 1,0.52 -0.65c0.41,-0.27 1,-0.58 1.61,-1a25.92,25.92 0,0 0,2.12 -1.59,0.74 0.74,0 0,1 0.48,0.57A1.58,1.58 0,0 1,77.35 62.69Z"/>
    <path android:fillColor="#d6e8f9" android:fillType="evenOdd" android:pathData="M76.91,62.23c-0.47,0.27 -1.08,0.69 -1.72,1s-1.3,0.59 -1.75,0.8c-0.35,-0.27 0.53,-0.58 1.43,-1.11A19,19 0,0 0,77 61.41c0.23,0.11 0.32,0.25 0.3,0.39A0.67,0.67 0,0 1,76.91 62.23Z"/>
    <path android:fillColor="#00a3df" android:fillType="evenOdd" android:pathData="M80,25a38.57,38.57 0,0 1,11.41 -2.23c11.09,1.69 14.45,5.09 18.92,9.35 -1,1.15 -9.17,7.2 -12.56,8.49C98.61,31.56 88.16,25.58 80,25Z"/>
    <path android:fillColor="#ecf0f4" android:fillType="evenOdd" android:pathData="M113.29,34.64a12.18,12.18 0,0 1,-1.06 6.51,4.23 4.23,0 0,0 1.62,2.91c0.77,0.6 1.46,-2.62 1.38,-4.74C115.25,38 114.46,37.11 113.29,34.64Z"/>
    <path android:fillColor="#4785dd" android:fillType="evenOdd" android:pathData="M21.76,46.44a45,45 0,0 0,-7.33 8.88,23.13 23.13,0 0,0 10.74,9.6c11.09,5.19 16.61,-0.91 19.35,-5.78C33,56.74 22.33,49.24 21.76,46.44Z"/>
    <path android:fillColor="#007fc6" android:fillType="evenOdd" android:pathData="M47.63,57.68a14.18,14.18 0,0 1,5 -2.81c-0.13,4.45 0.9,9 4.1,11.07 -3.39,0.94 -10.25,4.88 -12.3,4.45C45,66.73 46.88,61.22 47.63,57.68Z"/>
    <path android:fillColor="#005ca8" android:fillType="evenOdd" android:pathData="M93.49,41.59c-1.28,-0.57 -1.68,3.51 -0.68,5C93.68,48 97,49.11 98,49.09c0.39,0 0.5,-2.2 0.33,-2.81C97.67,44.08 95.11,42.3 93.49,41.59Z"/>
    <path android:fillColor="#007fc6" android:fillType="evenOdd" android:pathData="M93.49,41.59c-1.27,-0.56 -1.67,3.5 -0.68,5 0.23,0.66 4,2.56 5,2.16 0.44,-0.17 0.41,-1.45 0.35,-2.54A10.51,10.51 0,0 0,93.49 41.59Z"/>
    <path android:fillColor="#d6e8f9" android:fillType="evenOdd" android:pathData="M93.5,42c-0.44,-0.2 -0.78,0.2 -1,0.85 1.35,-1.05 4.37,1.71 5.35,2.86A11.21,11.21 0,0 0,93.5 42Z"/>
    <path android:fillType="evenOdd" android:pathData="M93.56,42.91c-1.06,-0.42 -1.1,2.64 -0.54,3.81 0.39,0.81 4.46,2.36 4.77,1.88a4.05,4.05 0,0 0,0.07 -2.31C97.15,44.69 94.89,43.44 93.56,42.91Z">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="97.75" android:endY="49.05"
                android:startX="92.42" android:startY="42.92" android:type="linear">
                <item android:color="#FF706F6F" android:offset="0"/>
                <item android:color="#FFDADADA" android:offset="1"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#4785dd" android:fillType="evenOdd" android:pathData="M82.06,37.13c8.84,-0.15 12,-5.61 1.2,-10C77.74,25 73.39,24.8 64.59,25.54 74.78,28.17 81.54,34.05 82.06,37.13Z"/>
    <path android:fillColor="#4785dd" android:fillType="evenOdd" android:pathData="M53.23,56.4C60.13,52 69.61,47 79.33,41.18a74.71,74.71 0,0 0,10 3.7c-16.48,8.25 -23.2,10 -35.74,14.94C53.1,59 53.29,57.4 53.23,56.4Z"/>
    <path android:fillColor="#005ca8" android:fillType="evenOdd" android:pathData="M65.42,47.86 L66.64,61c0.18,-0.1 0.58,0.15 0.85,0.13a9.41,9.41 0,0 1,2.59 -2.16l-2,-12.45C67.12,47 66.42,47.39 65.42,47.86Z"/>
    <path android:fillColor="#007fc6" android:fillType="evenOdd" android:pathData="M67.49,61.08l2.72,-1.6 -2.1,-13 -2.23,1.09Z"/>
    <path android:fillColor="#005ca8" android:pathData="M70.41,77.4l-0.12,0c0.09,-0.48 0.17,-1 0.26,-1.43 0.82,-4.49 1.6,-8.72 0.82,-13.06a6.73,6.73 0,0 0,-1.48 -2.45c-0.2,-0.25 -0.37,-0.46 -0.5,-0.64l0.1,-0.07c0.13,0.18 0.3,0.39 0.5,0.64a6.86,6.86 0,0 1,1.5 2.5c0.79,4.36 0,8.61 -0.82,13.1C70.58,76.44 70.49,76.92 70.41,77.4Z"/>
    <path android:fillColor="#a72323" android:fillType="evenOdd" android:pathData="M110.46,43.22l-2.68,1.38a0.48,0.48 0,0 0,0 0.77,0.36 0.36,0 0,0 0.35,0l2.57,-1.27C111.09,43.9 110.93,43 110.46,43.22Z"/>
    <path android:fillColor="#d62a27" android:fillType="evenOdd" android:pathData="M110.74,43.31 L108,44.73c-0.4,0.21 -0.14,1 0.34,0.78l2,-0.59C111.09,44.68 111.21,43.07 110.74,43.31Z"/>
    <path android:fillColor="#007fc6" android:fillType="evenOdd" android:pathData="M38.32,86.36l-0.92,6.22 -1.54,-0.35c-11.18,-3.45 -20.95,-8.81 -24.29,-13.55a16.1,16.1 0,0 1,-1.85 -7.23C12.85,76.82 30.37,84.6 38.32,86.36Z"/>
    <path android:fillColor="#00a3df" android:fillType="evenOdd" android:pathData="M9.72,71.45C13.81,77.55 25.05,84.35 36,87c3.62,0.88 11.12,-3.09 9.17,-3.17a16.72,16.72 0,0 1,-8.95 0.38c-11.46,-3 -23.6,-11.09 -25.72,-16.39C9.53,69 9.5,69.83 9.72,71.45Z"/>
    <path android:fillColor="#005ca8" android:fillType="evenOdd" android:pathData="M13.88,77.16A45.7,45.7 0,0 0,24.65 83.6v4.26l-0.55,-0.21A44.72,44.72 0,0 1,13.88 81.2Z"/>
    <path android:fillColor="#dadada" android:fillType="evenOdd" android:pathData="M14.41,78.1a44.3,44.3 0,0 0,9.72 5.81V87a46.71,46.71 0,0 1,-9.72 -5.81Z"/>
    <path android:fillColor="#bf1c1f" android:fillType="evenOdd" android:pathData="M30.68,73.31a9.7,9.7 0,0 0,-0.8 4.73,1.19 1.19,0 0,0 0.94,1.07c2.79,0.63 7.47,1.4 9.66,0.09 1.29,-0.76 1.84,-2.92 1.81,-4.54A35.29,35.29 0,0 1,30.68 73.31Z"/>
    <path android:fillColor="#bf1c1f" android:fillType="evenOdd" android:pathData="M12.82,62.05l-0.36,4.33c0,0.54 -0.24,0.6 -0.51,0.43a3.87,3.87 0,0 1,-1.46 -2.13A25.8,25.8 0,0 1,12.39 58,12.11 12.11,0 0,0 12.82,62.05Z"/>
    <path android:fillColor="#a72323" android:fillType="evenOdd" android:pathData="M12.65,61.35l-0.37,2.36c-0.08,0.53 -0.24,0.6 -0.51,0.43a3.5,3.5 0,0 1,-1 -1.12A18.73,18.73 0,0 1,12.39 58,12.38 12.38,0 0,0 12.65,61.35Z"/>
    <path android:fillColor="#005ca8" android:pathData="M69.38,59.77c-0.26,-2.05 -0.6,-4.36 -0.93,-6.61s-0.66,-4.57 -0.93,-6.62l0.12,0c0.27,2 0.61,4.38 0.93,6.63s0.67,4.56 0.93,6.6Z"/>
    <path android:fillColor="#00a3df" android:fillType="evenOdd" android:pathData="M22,44.74C24,41.38 38.9,31 54.29,25.45c1.81,-0.65 5.65,-0.46 8.25,0.09 5.53,1.19 13.64,4.38 18,10 1.59,2.06 0.63,3.69 -2.82,5.67L46.4,59C36.94,57.38 22.6,49.71 22,44.74Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M58.41,84c0.78,2.54 -0.54,6.72 -3,9.33s-5,2.66 -5.81,0.11 0.53,-6.72 3,-9.33S57.62,81.41 58.41,84Z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l2.28,-1.85l2.27,-1.85l-0.28,-0.9l-0.27,-0.89l-2,2.74l-2,2.75z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l1.42,-3.22l1.42,-3.22l-0.8,0.02l-0.8,0.01l-0.62,3.21l-0.62,3.2z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l-0.27,-2.7l-0.27,-2.7l-0.85,0.92l-0.86,0.91l1.13,1.79l1.12,1.78z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l-1.8,-0.6l-1.8,-0.6l-0.41,1.28l-0.4,1.28l2.2,-0.68l2.21,-0.68z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l-2.28,1.86l-2.27,1.85l0.27,0.89l0.28,0.9l2,-2.75l2,-2.75z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l-1.42,3.22l-1.42,3.22l0.8,-0.01l0.8,-0.02l0.62,-3.2l0.62,-3.21z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l0.27,2.7l0.27,2.7l0.85,-0.91l0.85,-0.92l-1.12,-1.78l-1.12,-1.79z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M53.99,88.58l1.8,0.6l1.8,0.6l0.4,-1.28l0.41,-1.28l-2.21,0.68l-2.2,0.68z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M58.24,83.85a5.19,5.19 0,0 1,0.22 1.29v0.74a12.42,12.42 0,0 1,-3.18 7.3,7.51 7.51,0 0,1 -1.43,1.23v-0.72a7.14,7.14 0,0 0,1.25 -1.08c2.13,-2.29 3.29,-6 2.6,-8.18 -0.56,-1.79 -2.14,-2.11 -3.85,-1v-0.72C55.8,81.43 57.61,81.8 58.24,83.85ZM53.85,94.41c-1.94,1.3 -3.75,0.93 -4.39,-1.11A5.87,5.87 0,0 1,49.24 92v-0.72A12.48,12.48 0,0 1,52.42 84a8.18,8.18 0,0 1,1.43 -1.23v0.72a7.94,7.94 0,0 0,-1.25 1.08c-2.12,2.29 -3.28,6 -2.59,8.18 0.55,1.79 2.14,2.11 3.84,1Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M58.41,84a5.12,5.12 0,0 1,0.21 1.29V86a12.39,12.39 0,0 1,-3.18 7.3A7.79,7.79 0,0 1,54 94.52V93.8a7.94,7.94 0,0 0,1.25 -1.08c2.12,-2.29 3.28,-6 2.59,-8.18 -0.55,-1.79 -2.14,-2.11 -3.84,-1v-0.72C56,81.54 57.77,81.91 58.41,84ZM54,94.52c-2,1.3 -3.76,0.93 -4.4,-1.12a5.2,5.2 0,0 1,-0.21 -1.3v-0.72a12.33,12.33 0,0 1,3.18 -7.31A7.51,7.51 0,0 1,54 82.84v0.72a7.14,7.14 0,0 0,-1.25 1.08c-2.13,2.29 -3.29,6 -2.6,8.18 0.56,1.79 2.14,2.11 3.85,1Z"/>
    <path android:fillType="evenOdd" android:pathData="M56.3,86.93l0.12,-0.1L58.61,85c0,-0.22 0,-0.43 -0.09,-0.64l-0.1,-0.32 -0.17,-0.53a3,3 0,0 0,-0.15 -0.31l-1.8,2.47v1.22ZM56.3,92.24v-0.13l0,0.07 0,0.06ZM56.3,89.4L56.3,88h0.05l2.1,-0.65a12.24,12.24 0,0 1,-0.79 2.5L56.3,89.4ZM56.3,83.79L56.3,82.25L57,82.25ZM54.14,88.68 L56.3,86.93L56.3,85.71l-0.16,0.22 -2,2.75h0l-2.2,1.8v1.23l0.2,-0.28 2,-2.75L52.73,91.9l-0.79,1.79v1.42h1l0.62,-3.21 0.62,-3.2ZM56.3,82.25v1.54l-0.74,1.67 -1.42,3.22 0.62,-3.2 0.62,-3.21 0.8,0ZM56.3,88.01L56.3,89.4l-0.36,-0.12 -1.79,-0.6h0l1.13,1.78 1,1.65v0.13l-0.48,0.62 -0.29,0.31 -0.46,0.5 -0.39,0.35 -0.27,-2.64 -0.27,-2.7h0L56.3,88ZM51.94,89.36L51.94,87.94l0.4,0.14 1.8,0.6L53,86.9l-1.08,-1.73v-0.1l0.81,-0.87 0.86,-0.92 0.26,2.7 0.27,2.7ZM51.94,90.48 L51.87,90.48 49.59,92.34 49.87,93.23 50.14,94.13 51.94,91.66L51.94,90.48ZM51.94,93.69v1.42L51.3,95.11l0.64,-1.43ZM51.94,85.07v0.1l0,-0.06 0,0ZM51.94,87.94v1.42h0L49.73,90l0.41,-1.28 0.4,-1.28Z">
        <aapt:attr name="android:fillColor">
            <gradient android:endX="76.8" android:endY="126.45"
                android:startX="45.7" android:startY="74.69" android:type="linear">
                <item android:color="#FFDADADA" android:offset="0"/>
                <item android:color="#FFDADADA" android:offset="0.4"/>
                <item android:color="#FF706F6F" android:offset="1"/>
            </gradient>
        </aapt:attr>
    </path>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M58,85.51l0.57,-0.46a6.37,6.37 0,0 0,-0.09 -0.65l-0.1,-0.32 -0.17,-0.54c0,-0.1 -0.1,-0.2 -0.15,-0.3l-0.5,0.69a3.11,3.11 0,0 1,0.26 0.61A4.77,4.77 0,0 1,58 85.51Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M56.63,83l0.35,-0.8h-0.8l-0.8,0 -0.14,0.72A2.18,2.18 0,0 1,56.63 83Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M53.66,83.82l0,-0.54 -0.86,0.92 -0.85,0.91 0.21,0.33a7.19,7.19 0,0 1,0.66 -0.8A7.45,7.45 0,0 1,53.66 83.82Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M50.86,87.58l-0.32,-0.1 -0.4,1.28L49.73,90l0.39,-0.12A10.21,10.21 0,0 1,50.86 87.58Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M50,92.05l-0.42,0.34 0.28,0.89 0.27,0.9 0.41,-0.56a2.83,2.83 0,0 1,-0.38 -0.8A3.75,3.75 0,0 1,50 92.05Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M51.62,94.39l-0.32,0.73 0.8,0h0.8l0.16,-0.8A2.42,2.42 0,0 1,51.62 94.39Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M54.61,93.35l0.07,0.67 0.38,-0.34 0.47,-0.51 0.29,-0.31c0.18,-0.22 0.36,-0.45 0.52,-0.68l-0.28,-0.45a9.52,9.52 0,0 1,-0.79 1Q54.94,93.07 54.61,93.35Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M57.21,89.7l0.45,0.15a12.24,12.24 0,0 0,0.79 -2.5l-0.55,0.17A10.78,10.78 0,0 1,57.21 89.7Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M58,85.51l0.57,-0.46a6.37,6.37 0,0 0,-0.09 -0.65l-0.1,-0.32 -0.17,-0.54c0,-0.1 -0.1,-0.2 -0.15,-0.3l-0.5,0.69a3.11,3.11 0,0 1,0.26 0.61A4.77,4.77 0,0 1,58 85.51Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M56.63,83l0.35,-0.8h-0.8l-0.8,0 -0.14,0.72A2.18,2.18 0,0 1,56.63 83Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M53.66,83.82l0,-0.54 -0.86,0.92 -0.85,0.91 0.21,0.33a7.19,7.19 0,0 1,0.66 -0.8A7.45,7.45 0,0 1,53.66 83.82Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M50.86,87.58l-0.32,-0.1 -0.4,1.28L49.73,90l0.39,-0.12A10.21,10.21 0,0 1,50.86 87.58Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M50,92.05l-0.42,0.34 0.28,0.89 0.27,0.9 0.41,-0.56a2.83,2.83 0,0 1,-0.38 -0.8A3.75,3.75 0,0 1,50 92.05Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M51.62,94.39l-0.32,0.73 0.8,0h0.8l0.16,-0.8A2.42,2.42 0,0 1,51.62 94.39Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M54.61,93.35l0.07,0.67 0.38,-0.34 0.47,-0.51 0.29,-0.31c0.18,-0.22 0.36,-0.45 0.52,-0.68l-0.28,-0.45a9.52,9.52 0,0 1,-0.79 1Q54.94,93.07 54.61,93.35Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M57.21,89.7l0.45,0.15a12.24,12.24 0,0 0,0.79 -2.5l-0.55,0.17A10.78,10.78 0,0 1,57.21 89.7Z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M54.81,87.82a2,2 0,0 1,-0.54 1.69c-0.43,0.48 -0.91,0.49 -1.05,0a2,2 0,0 1,0.54 -1.69C54.2,87.37 54.67,87.36 54.81,87.82Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M54.72,88.05a1.58,1.58 0,0 1,-0.43 1.34c-0.35,0.37 -0.72,0.38 -0.83,0a1.54,1.54 0,0 1,0.42 -1.34C54.23,87.69 54.6,87.68 54.72,88.05Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M54.81,88.11a1.58,1.58 0,0 1,-0.43 1.34c-0.35,0.38 -0.72,0.39 -0.83,0A1.56,1.56 0,0 1,54 88.13C54.32,87.76 54.69,87.75 54.81,88.11Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M108.85,55.46c0.79,2.54 -0.54,6.72 -3,9.32s-5,2.67 -5.82,0.12 0.54,-6.72 3,-9.33S108.06,52.91 108.85,55.46Z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l2.28,-1.85l2.28,-1.85l-0.28,-0.9l-0.28,-0.89l-2,2.74l-2,2.75z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l1.42,-3.22l1.42,-3.22l-0.8,0.02l-0.8,0.01l-0.62,3.21l-0.62,3.2z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l-0.27,-2.7l-0.27,-2.7l-0.85,0.92l-0.85,0.91l1.12,1.79l1.12,1.78z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l-1.8,-0.6l-1.8,-0.6l-0.4,1.28l-0.41,1.28l2.21,-0.68l2.2,-0.68z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l-2.27,1.86l-2.28,1.85l0.28,0.89l0.27,0.9l2,-2.75l2,-2.75z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l-1.41,3.22l-1.43,3.22l0.8,-0.01l0.8,-0.02l0.62,-3.2l0.62,-3.21z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l0.27,2.7l0.27,2.7l0.85,-0.91l0.86,-0.92l-1.12,-1.78l-1.13,-1.79z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M104.43,60.08l1.8,0.6l1.8,0.6l0.41,-1.28l0.41,-1.28l-2.21,0.68l-2.21,0.68z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M108.69,55.35a5.19,5.19 0,0 1,0.21 1.29v0.74a12.3,12.3 0,0 1,-3.18 7.3,7.79 7.79,0 0,1 -1.42,1.23v-0.72a7.94,7.94 0,0 0,1.25 -1.08c2.12,-2.29 3.28,-6 2.59,-8.18 -0.55,-1.79 -2.14,-2.11 -3.84,-1v-0.72C106.24,52.93 108.05,53.3 108.69,55.35ZM104.3,65.91c-2,1.3 -3.76,0.93 -4.39,-1.11a4.92,4.92 0,0 1,-0.22 -1.31v-0.72a12.35,12.35 0,0 1,3.18 -7.31,7.51 7.51,0 0,1 1.43,-1.23V55A7.14,7.14 0,0 0,103.05 56c-2.13,2.29 -3.29,5.95 -2.6,8.18 0.56,1.79 2.14,2.11 3.85,1Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M108.85,55.45a5.78,5.78 0,0 1,0.22 1.3v0.74a12.45,12.45 0,0 1,-3.18 7.3A8.18,8.18 0,0 1,104.46 66V65.3a7.94,7.94 0,0 0,1.25 -1.08c2.12,-2.29 3.29,-5.95 2.6,-8.18 -0.56,-1.79 -2.15,-2.11 -3.85,-1v-0.72C106.41,53 108.22,53.41 108.85,55.45ZM104.46,66c-1.95,1.3 -3.76,0.93 -4.39,-1.12a4.77,4.77 0,0 1,-0.22 -1.3v-0.72A12.39,12.39 0,0 1,103 55.57a8.18,8.18 0,0 1,1.43 -1.23v0.72a7.52,7.52 0,0 0,-1.25 1.08c-2.13,2.29 -3.29,6 -2.6,8.18 0.56,1.79 2.14,2.11 3.85,1Z"/>
    <path android:fillType="evenOdd" android:pathData="M106.74,58.43l0.12,-0.1 2.19,-1.79c0,-0.22 0,-0.43 -0.09,-0.64l-0.1,-0.32 -0.16,-0.53a3,3 0,0 0,-0.16 -0.31l-1.8,2.47v1.22ZM106.74,63.74v-0.13l0.05,0.07 -0.05,0.06ZM106.74,60.9L106.74,59.51h0.05l2.11,-0.65a12.94,12.94 0,0 1,-0.8 2.5l-1.36,-0.45ZM106.74,55.29L106.74,53.75h0.69ZM104.59,60.18 L106.74,58.43L106.74,57.21l-0.16,0.22 -2,2.75h0L102.38,62v1.23l0.21,-0.28 2,-2.75 -1.42,3.22 -0.79,1.79v1.42h1l0.61,-3.21 0.63,-3.2ZM106.74,53.75v1.54L106,57l-1.42,3.22 0.62,-3.2 0.62,-3.21 0.8,0ZM106.74,59.51L106.74,60.9l-0.35,-0.12 -1.8,-0.6h0L105.71,62l1,1.65v0.13c-0.15,0.21 -0.31,0.42 -0.48,0.62l-0.28,0.31 -0.47,0.5 -0.39,0.35 -0.26,-2.64 -0.27,-2.7h0l2.15,-0.67ZM102.38,60.86L102.38,59.44l0.41,0.14 1.8,0.6 -1.13,-1.78 -1.08,-1.73v-0.1l0.82,-0.88 0.85,-0.91 0.27,2.7 0.27,2.7ZM102.38,61.98 L102.31,62.03L100,63.89l0.28,0.89 0.28,0.89 1.79,-2.46L102.35,62ZM102.38,65.19v1.42h-0.63l0.63,-1.43ZM102.38,56.57v0.1l0,-0.06 0,0ZM102.38,59.44v1.42h0l-2.21,0.68 0.41,-1.28L101,59Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M108.48,57l0.57,-0.47c0,-0.22 0,-0.43 -0.09,-0.64l-0.1,-0.32L108.7,55c0,-0.1 -0.1,-0.2 -0.16,-0.3l-0.5,0.69a3.12,3.12 0,0 1,0.27 0.61A4.18,4.18 0,0 1,108.48 57Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M107.07,54.54l0.36,-0.8h-0.8l-0.8,0 -0.14,0.72A2.15,2.15 0,0 1,107.07 54.54Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M104.1,55.32l0,-0.54 -0.85,0.91 -0.86,0.92 0.21,0.33c0.21,-0.28 0.43,-0.55 0.66,-0.8A6.7,6.7 0,0 1,104.1 55.32Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M101.3,59.08 L101,59l-0.41,1.28 -0.41,1.28 0.39,-0.12A10.73,10.73 0,0 1,101.3 59.08Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M100.45,63.54l-0.42,0.35 0.28,0.89 0.28,0.89 0.4,-0.55a3.13,3.13 0,0 1,-0.38 -0.8A5.65,5.65 0,0 1,100.45 63.54Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M102.07,65.89l-0.32,0.73 0.8,0h0.8l0.15,-0.8A2.39,2.39 0,0 1,102.07 65.89Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M105.05,64.85l0.07,0.67c0.13,-0.1 0.25,-0.22 0.38,-0.34l0.48,-0.51 0.28,-0.31c0.19,-0.22 0.36,-0.45 0.53,-0.68l-0.28,-0.45a11.15,11.15 0,0 1,-0.8 1Q105.38,64.57 105.05,64.85Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M107.65,61.2l0.45,0.15a12.24,12.24 0,0 0,0.79 -2.5l-0.55,0.17A10.78,10.78 0,0 1,107.65 61.2Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M108.48,57l0.57,-0.47c0,-0.22 0,-0.43 -0.09,-0.64l-0.1,-0.32L108.7,55c0,-0.1 -0.1,-0.2 -0.16,-0.3l-0.5,0.69a3.12,3.12 0,0 1,0.27 0.61A4.18,4.18 0,0 1,108.48 57Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M107.07,54.54l0.36,-0.8h-0.8l-0.8,0 -0.14,0.72A2.15,2.15 0,0 1,107.07 54.54Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M104.1,55.32l0,-0.54 -0.85,0.91 -0.86,0.92 0.21,0.33c0.21,-0.28 0.43,-0.55 0.66,-0.8A6.7,6.7 0,0 1,104.1 55.32Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M101.3,59.08 L101,59l-0.41,1.28 -0.41,1.28 0.39,-0.12A10.73,10.73 0,0 1,101.3 59.08Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M100.45,63.54l-0.42,0.35 0.28,0.89 0.28,0.89 0.4,-0.55a3.13,3.13 0,0 1,-0.38 -0.8A5.65,5.65 0,0 1,100.45 63.54Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M102.07,65.89l-0.32,0.73 0.8,0h0.8l0.15,-0.8A2.39,2.39 0,0 1,102.07 65.89Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M105.05,64.85l0.07,0.67c0.13,-0.1 0.25,-0.22 0.38,-0.34l0.48,-0.51 0.28,-0.31c0.19,-0.22 0.36,-0.45 0.53,-0.68l-0.28,-0.45a11.15,11.15 0,0 1,-0.8 1Q105.38,64.57 105.05,64.85Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M107.65,61.2l0.45,0.15a12.24,12.24 0,0 0,0.79 -2.5l-0.55,0.17A10.78,10.78 0,0 1,107.65 61.2Z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M105.25,59.32a2,2 0,0 1,-0.53 1.69c-0.44,0.48 -0.91,0.49 -1.06,0a2,2 0,0 1,0.54 -1.69C104.64,58.87 105.11,58.86 105.25,59.32Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M105.16,59.55a1.59,1.59 0,0 1,-0.43 1.34c-0.34,0.37 -0.72,0.38 -0.83,0a1.6,1.6 0,0 1,0.42 -1.34C104.67,59.19 105.05,59.18 105.16,59.55Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M105.25,59.61a1.58,1.58 0,0 1,-0.43 1.34c-0.34,0.38 -0.72,0.39 -0.83,0a1.62,1.62 0,0 1,0.42 -1.34C104.76,59.26 105.14,59.25 105.25,59.61Z"/>
    <path android:fillColor="#a72323" android:fillType="evenOdd" android:pathData="M31.77,74a7.78,7.78 0,0 0,-0.64 3.77,0.93 0.93,0 0,0 0.76,0.85c2.22,0.5 6,1.12 7.7,0.08 1,-0.61 1.46,-2.33 1.44,-3.63A28.07,28.07 0,0 1,31.77 74Z"/>
    <path android:fillColor="#962727" android:fillType="evenOdd" android:pathData="M35,78.63c-0.64,0.2 -1.5,-0.46 -1.93,-1.47s-0.26,-2 0.37,-2.18 1.5,0.46 1.93,1.47S35.64,78.43 35,78.63Z"/>
    <path android:fillColor="#bf1c1f" android:fillType="evenOdd" android:pathData="M35.48,78.21A2.47,2.47 0,0 1,34 76.73,2.23 2.23,0 0,1 33.9,75a2.41,2.41 0,0 1,1.48 1.48A2.2,2.2 0,0 1,35.48 78.21Z"/>
    <path android:fillColor="#ef7d00" android:fillType="evenOdd" android:pathData="M38.11,78c-0.52,0.16 -1.23,-0.37 -1.58,-1.2s-0.21,-1.62 0.31,-1.78 1.23,0.37 1.58,1.2S38.63,77.88 38.11,78Z"/>
    <path android:fillColor="#f7ab60" android:fillType="evenOdd" android:pathData="M38.5,77.69a2,2 0,0 1,-1.22 -1.21,1.77 1.77,0 0,1 -0.08,-1.43 2,2 0,0 1,1.22 1.21A1.81,1.81 0,0 1,38.5 77.69Z"/>
    <path android:fillColor="#4c4c4c" android:fillType="evenOdd" android:pathData="M19.4,69.87A2.59,2.59 0,0 0,20.09 72c0.56,0.61 1.17,0.62 1.35,0a2.52,2.52 0,0 0,-0.69 -2.17C20.19,69.29 19.58,69.28 19.4,69.87Z"/>
    <path android:fillColor="#3c3c3b" android:fillType="evenOdd" android:pathData="M19.52,70.16a2,2 0,0 0,0.55 1.72c0.45,0.48 0.93,0.49 1.07,0a2,2 0,0 0,-0.54 -1.72C20.15,69.7 19.67,69.69 19.52,70.16Z"/>
    <path android:fillColor="#ccc" android:fillType="evenOdd" android:pathData="M19.41,70.24A2,2 0,0 0,20 72c0.45,0.49 0.93,0.49 1.08,0a2.07,2.07 0,0 0,-0.55 -1.73C20,69.78 19.55,69.77 19.41,70.24Z"/>
    <path android:fillColor="#c5d1d9" android:fillType="evenOdd" android:pathData="M113.59,43.84c0.59,-0.49 1,-3 1,-4.73 0,-1 -0.45,-1.78 -1.21,-3.23a12.21,12.21 0,0 1,-1.13 5.27A4.21,4.21 0,0 0,113.59 43.84Z"/>
</vector>
