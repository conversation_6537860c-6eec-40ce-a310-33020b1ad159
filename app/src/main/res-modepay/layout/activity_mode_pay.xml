<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.modepay.viewmodel.ModePayViewmodel" />
    </data>

<LinearLayout xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/activity_main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_gravity="center"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <include android:id="@+id/toolbarModepay" layout="@layout/toolbar" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/mListView"
        android:padding="5dp"
        android:visibility="visible"
        android:background="@color/recycler_view_bg"
        tools:listitem="@layout/item_modepay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/emptyList"
        android:layout_width="match_parent"
        android:text="@string/mode_of_payment_list_not_available"
        android:gravity="center"
        android:visibility="gone"
        android:textSize="@dimen/_16sdp"
        android:textColor="@color/black"
        android:layout_height="match_parent">

    </androidx.appcompat.widget.AppCompatTextView>

</LinearLayout>
</layout>



