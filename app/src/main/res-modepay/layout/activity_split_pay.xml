<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.reference.viewmodel.CommonViewModel" />
    </data>

    <LinearLayout xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/activity_main"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_gravity="center"
        android:gravity="center_vertical"
        android:orientation="vertical">

        <include android:id="@+id/toolbarModepay" layout="@layout/toolbar" />
        <LinearLayout
            android:layout_width="match_parent"
            android:background="@color/colorPrimary"
            android:weightSum="2"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:padding="@dimen/_10sdp"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:textSize="@dimen/_14sdp"
                android:text="@string/total_payment"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="0.1"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:textSize="@dimen/_14sdp"
                android:text="-"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/totalAmount"
                android:layout_width="0dp"
                android:layout_weight="0.9"
                android:textColor="@color/white"
                android:textStyle="bold"
                android:gravity="center"
                android:textSize="@dimen/_14sdp"
                android:text="0 AED"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:background="#E6ECF3"
            android:weightSum="2"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:padding="@dimen/_10sdp"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="@dimen/_12sdp"
                android:text="@string/paid_amount"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="0.1"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="@dimen/_12sdp"
                android:text="-"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/paidAmount"
                android:layout_width="0dp"
                android:layout_weight="0.9"
                android:textColor="@color/greenLight"
                android:textStyle="bold"
                android:gravity="center"
                android:textSize="@dimen/_12sdp"
                android:text="0 AED"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>

        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:background="#63D9ECFD"
            android:weightSum="2"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:padding="@dimen/_10sdp"
            android:layout_height="wrap_content">
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="@dimen/_12sdp"
                android:text="@string/payable_amount"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_weight="0.1"
                android:textColor="@color/black"
                android:textStyle="bold"
                android:textSize="@dimen/_12sdp"
                android:text="-"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/payableAmount"
                android:layout_width="0dp"
                android:layout_weight="0.9"
                android:textColor="@color/greenLight"
                android:textStyle="bold"
                android:gravity="center"
                android:textSize="@dimen/_12sdp"
                android:text="0 AED"
                android:layout_height="wrap_content">

            </androidx.appcompat.widget.AppCompatTextView>

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/msgText"
            android:layout_width="match_parent"
            android:textColor="@color/colorPrimary"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:textSize="@dimen/_12sdp"
            android:text="@string/please_select_your_first_payment_method"
            android:layout_height="wrap_content"/>
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/mListView"
            android:padding="5dp"
            android:visibility="visible"
            android:layout_marginTop="@dimen/_10sdp"
            android:background="@color/recycler_view_bg"
            tools:listitem="@layout/item_modepay"
            android:layout_width="match_parent"
            android:layout_gravity="center"
            android:layout_height="match_parent"
            app:spanCount="3"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/emptyList"
            android:layout_width="match_parent"
            android:text="Mode of payment List Not Available"
            android:gravity="center"
            android:visibility="gone"
            android:textSize="@dimen/_16sdp"
            android:textColor="@color/black"
            android:layout_height="match_parent">

        </androidx.appcompat.widget.AppCompatTextView>

    </LinearLayout>
</layout>



