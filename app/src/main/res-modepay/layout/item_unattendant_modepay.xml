<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <variable
            name="item"
            type="app.rht.petrolcard.ui.modepay.model.ModePaymentModel" />

        <variable
            name="itemClickListener"
            type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

        <import type="android.view.View"/>
    </data>
    <com.google.android.material.card.MaterialCardView
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="@dimen/_60sdp"
        app:cardCornerRadius="6dp"
        android:layout_marginHorizontal="@dimen/_10sdp"
        app:cardElevation="0dp"
        android:padding="@dimen/_10sdp"
        android:background="@color/white"
        android:layout_marginTop="20dp"
        android:id="@+id/categoryCard"
        app:cardPreventCornerOverlap="true"
        android:layout_marginEnd="10dp"
        android:clickable="true"
        android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
        android:focusable="true"

        >
    <LinearLayout

        android:layout_width="match_parent"
        android:orientation="horizontal"
        android:weightSum="2"
        android:gravity="center"
        android:layout_gravity="center"
        android:layout_margin="@dimen/_5sdp"
        android:layout_height="match_parent">
        <LinearLayout
                android:layout_width="0dp"
                  android:layout_weight="0.5"
                android:layout_height="wrap_content"
                android:id="@+id/categoryItem"
                tools:targetApi="m">

                <ImageView
                    android:id="@+id/ivCategoryImage"
                    app:imageResource="@{item.icon}"
                    android:layout_width="match_parent"
                    tools:src="@drawable/ic_tokheim"
                    android:layout_height="match_parent"
                    />

            </LinearLayout>
        <TextView
            android:id="@+id/tvCategoryTitle"
            android:layout_width="0dp"
            android:layout_weight="1.5"
            android:layout_height="match_parent"
            android:gravity="center|start"
            tools:text="Fleet card"
            android:layout_marginStart="@dimen/_10sdp"
            android:layout_gravity="start|center"
            android:textStyle="bold"
            android:textAllCaps="true"
            android:text="@{item.payment_name}"
            android:textColor="@color/black"
            android:textSize="14sp" />

    </LinearLayout>
    </com.google.android.material.card.MaterialCardView>

</layout>
