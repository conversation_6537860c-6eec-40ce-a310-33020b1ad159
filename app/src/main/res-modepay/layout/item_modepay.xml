<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">
        <data>
                <variable
                    name="item"
                    type="app.rht.petrolcard.ui.modepay.model.ModePaymentModel" />

                <variable
                    name="itemClickListener"
                    type="app.rht.petrolcard.baseClasses.adapter.RecyclerViewArrayAdapter.OnItemClickListener" />

                <import type="android.view.View"/>
        </data>

<com.google.android.material.card.MaterialCardView
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:id="@+id/modePayLayout"
    android:onClick="@{(v) -> itemClickListener.onItemClick(v,item)}"
    app:cardCornerRadius="12dp"
    app:cardElevation="0dp"
    android:layout_marginStart="8dp"
    android:layout_marginEnd="8dp"
    android:layout_marginTop="4dp"
    android:layout_marginBottom="4dp">
       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="match_parent"
           android:layout_height="match_parent"
           android:padding="12dp"
           android:orientation="horizontal">

           <androidx.appcompat.widget.AppCompatImageView
               android:id="@+id/ivIconColor"
               android:layout_width="50dp"
               android:layout_height="50dp"
               android:src="@drawable/rounded_rectangle_button"
               app:layout_constraintBottom_toBottomOf="parent"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toTopOf="parent"
               tools:tint="#F3D8F8"
               android:tint="@{item.getColorInt}" />

           <androidx.appcompat.widget.AppCompatImageView
               android:layout_width="0dp"
               android:layout_height="0dp"
               android:id="@+id/ivIcon"
               android:layout_margin="10dp"
               app:layout_constraintStart_toStartOf="@id/ivIconColor"
               app:layout_constraintEnd_toEndOf="@id/ivIconColor"
               app:layout_constraintTop_toTopOf="@id/ivIconColor"
               app:layout_constraintBottom_toBottomOf="@id/ivIconColor"
               tools:src="@drawable/ic_split_payment"
               app:imageResource="@{item.icon}" />

               <TextView
                   android:layout_width="wrap_content"
                   android:text="@{item.payment_name}"
                   android:textColor="@color/black"
                   android:gravity="center"
                   android:layout_height="wrap_content"
                   app:layout_constraintStart_toEndOf="@id/ivIconColor"
                   app:layout_constraintTop_toTopOf="@id/ivIconColor"
                   app:layout_constraintBottom_toBottomOf="@id/ivIconColor"
                   android:layout_margin="10dp"
                   />
       </androidx.constraintlayout.widget.ConstraintLayout>
</com.google.android.material.card.MaterialCardView>


</layout>