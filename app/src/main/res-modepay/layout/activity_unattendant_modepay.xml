<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <data>
        <variable
            name="model"
            type="app.rht.petrolcard.ui.menu.viewmodel.MenuViewModel" />

    </data>
    <LinearLayout

        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="@color/background_light_white"
        tools:context=".ui.menu.activity.MenuActivity">


        <FrameLayout
            android:id="@+id/headerLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/ivMenuBg"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:scaleType="fitXY"
                android:src="@drawable/ic_menu_bg"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <RelativeLayout
                android:id="@+id/headerContent"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_marginStart="30dp"
                android:layout_marginTop="0dp"
                android:layout_marginEnd="30dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <ImageView
                    android:id="@+id/logo"
                    android:layout_width="65dp"
                    android:layout_height="65dp"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/circular_border"
                    android:padding="10dp"
                    android:src="@drawable/fueluplogo" />

                <LinearLayout
                    android:id="@+id/station"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@+id/logo"
                    android:layout_toStartOf="@id/btnTeleCollect"
                    android:orientation="vertical"
                    android:paddingStart="10dp">

                    <TextView
                        android:id="@+id/tvStationName"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="@string/station_name"
                        android:textColor="@color/white"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTimeStamp"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="2dp"
                        android:text="ddMMyyyy hh:mm:ss aa"
                        android:textColor="@color/white"
                        android:textSize="12sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tvTransactionMode"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="1dp"
                        android:text="version 3.0"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/tvTerminalSn"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="1dp"
                        android:text="*****0000"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                </LinearLayout>

                <ImageView
                    android:id="@+id/btnTeleCollect"
                    android:layout_width="50dp"
                    android:layout_height="50dp"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/circular_border"
                    android:padding="10dp"
                    android:src="@drawable/cloud_sync"
                    app:tint="@color/colorPrimary"
                    android:onClick="btnClick"/>

            </RelativeLayout>

        </FrameLayout>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="35sp"
            android:gravity="center_vertical"
            android:text="@string/mode_of_payment"
            android:textColor="@color/text_color"
            android:textSize="@dimen/_16sdp"
            android:layout_marginTop="@dimen/_10sdp"
            android:layout_marginHorizontal="@dimen/_10sdp"
            android:textStyle="bold" />
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/noPayments"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/_10sdp"
            android:gravity="center"
            android:background="@color/white"
            android:text="@string/no_payments_found"
            android:textColor="@color/redLight"
            android:visibility="gone"> </androidx.appcompat.widget.AppCompatTextView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:visibility="gone"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/connect_nfusion"
                    android:id="@+id/btnConnect"
                    android:onClick="btnClick"/>

                <View android:layout_width="10dp" android:layout_height="5dp"/>

                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/disconnect_nfusion"
                    android:id="@+id/btnDisconnect"
                    android:onClick="btnClick"/>

            </LinearLayout>


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/authorize_npump"
                    android:layout_weight="1"
                    android:id="@+id/btnAuth"
                    android:onClick="btnClick"/>

                <View android:layout_width="10dp" android:layout_height="5dp"/>

                <Button
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/connection_nstate"
                    android:id="@+id/btnConState"
                    android:onClick="btnClick"/>

            </LinearLayout>

        </LinearLayout>

        <ScrollView
            android:layout_width="match_parent"
            android:layout_marginBottom="@dimen/_50sdp"
            android:layout_height="match_parent">
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rvModePayments"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_10sdp"
                android:orientation="vertical"
                android:layout_marginTop="@dimen/_10sdp"
                android:scrollbars="vertical"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:spanCount="1"
                tools:itemCount="3"
                tools:listitem="@layout/item_unattendant_modepay" />
        </ScrollView>


    </LinearLayout>
</layout>
