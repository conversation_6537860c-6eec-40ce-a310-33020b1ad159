buildscript {
    repositories {
        maven { url "https://jitpack.io" }
        mavenCentral()
    }
}

plugins {
    id 'com.android.application'
    id 'kotlin-android'
    // id 'kotlin-android-extensions' // Deprecated - using view binding instead
    id 'kotlin-kapt'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
}

android {
    signingConfigs {
        config {
            storeFile file('../keystore/fleetcard.jks')
            storePassword 'rht2022'
            keyAlias 'rht2022'
            keyPassword 'rht2022'
        }
    }

    compileSdkVersion 34
    useLibrary 'org.apache.http.legacy'

    defaultConfig {
        applicationId "app.rht.petrolcard"
        minSdk 22
        targetSdkVersion 34
        versionCode 105
        versionName "3.4.22"
        multiDexEnabled true
        proguardFiles 'proguard-rules.pro'
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        signingConfig signingConfigs.config
        ndk {
            abiFilters "armeabi", "armeabi-v7a", "x86", "mips"
        }
    }


    packagingOptions {
        exclude "lib/armeabi/**"
        exclude 'META-INF/DEPENDENCIES'
        exclude 'META-INF/LICENSE'
        exclude 'META-INF/LICENSE.txt'
        exclude 'META-INF/license.txt'
        exclude 'META-INF/NOTICE'
        exclude 'META-INF/NOTICE.txt'
        exclude 'META-INF/notice.txt'
        exclude 'META-INF/ASL2.0'
        pickFirst 'lib/armeabi-v7a/libassmidi.so'
        pickFirst 'lib/x86/libassmidi.so'
        exclude 'META-INF/rxjava.properties'
        exclude 'isoparser-default.properties'
        exclude("META-INF/*.kotlin_module")
        exclude 'META-INF/**'
    }


    flavorDimensions "default"

    productFlavors {
        modeN_LANDI_ {
            applicationId "app.rht.petrolcard.modeN"
            buildConfigField("String", "TCP_TYPE", '"Authorize"')
            buildConfigField("String", "POS_TYPE", '"LANDI"')
            buildConfigField "boolean", "KIOSK", "true"
            buildConfigField("Integer", "MODE", "0")
            buildConfigField("Integer", "PORT", "7510")
            buildConfigField("Integer", "PORTDelete", "7778")
            buildConfigField("String", "HOST_NAME", '"www.fuelbusinesssuite.com"')
            buildConfigField("String", "URL_INITIAL", '"https://getserver.fuelbusinesssuite.com/"')
            buildConfigField "boolean", "REBATE_DISCOUNT_REQUIRED", "true"
            buildConfigField("String", "RELEASE_PUMP_CMD", '"|030:1|033:1|034:1|\\r\\n"')
        }

        modeN_BLEU_TPE_ {
            applicationId "app.rht.petrolcard.modeN"
            buildConfigField("String", "TCP_TYPE", '"clear"')
            buildConfigField("String", "POS_TYPE", '"B_TPE"')
            buildConfigField "boolean", "KIOSK", "false"
            buildConfigField("Integer", "MODE", "0")
            buildConfigField("Integer", "PORT", "7510")
            buildConfigField("Integer", "PORTDelete", "7778")
            buildConfigField("String", "URL_INITIAL", '"https://getserver.fuelbusinesssuite.com/"')
            buildConfigField("String", "HOST_NAME", '"www.fuelbusinesssuite.com"')
            buildConfigField "boolean", "REBATE_DISCOUNT_REQUIRED", "true"
            buildConfigField("String", "RELEASE_PUMP_CMD", '"|030:1|033:1|034:1|\\r\\n"')
        }

        modeN_PAX {
            applicationId "app.rht.petrolcard.modeN"
            buildConfigField("String", "POS_TYPE", '"PAX"')
            buildConfigField("String", "TCP_TYPE", '"clear"')
            buildConfigField("Integer", "MODE", "0")
            buildConfigField "boolean", "KIOSK", "false"
            buildConfigField("Integer", "PORT", "7510")
            buildConfigField("Integer", "PORTDelete", "7778")
            buildConfigField("String", "URL_INITIAL", '"https://getserver.fuelbusinesssuite.com/"')
            buildConfigField("String", "HOST_NAME", '"www.fuelbusinesssuite.com"')
            buildConfigField "boolean", "REBATE_DISCOUNT_REQUIRED", "true"
            buildConfigField("String", "RELEASE_PUMP_CMD", '"|030:1|033:1|034:1|\\r\\n"')
        }
    }

    buildTypes {
        release {
            multiDexEnabled true
            shrinkResources true
            minifyEnabled true
            proguardFiles getDefaultProguardFile("proguard-android.txt"), "proguard-rules.pro"
            buildConfigField "boolean", "LOG", "true"
            buildConfigField "String", "GET_BASE_API_URL", '"https://getserver.fuelbusinesssuite.com/"'
            buildConfigField "String", "BASE_IMAGE_URL", '"https://apiurl.com/"'
            signingConfig signingConfigs.config
            buildConfigField "String", "ONLINE_PAYMENT_HOST_NAME", "\"**************\""
            buildConfigField "Integer", "ONLINE_PAYMENT_PORT", "8080"
        }

        debug {
            buildConfigField "boolean", "LOG", "true"
            buildConfigField "String", "GET_BASE_API_URL", '"https://getserver.fuelbusinesssuite.com/"'
            buildConfigField "String", "BASE_IMAGE_URL", '"https://apiurl.com/"'
            buildConfigField "String", "ONLINE_PAYMENT_HOST_NAME", "\"**************\""
            buildConfigField "Integer", "ONLINE_PAYMENT_PORT", "8080"
        }
    }

    buildFeatures {
        dataBinding = true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    sourceSets {
        main {
            jniLibs.srcDirs = ['libs']
            res.srcDirs = [
                    'src/main/res',
                    'src/main/res-common',
                    'src/main/res-startup',
                    'src/main/res-reference',
                    'src/main/res-menu',
                    'src/main/res-attendant-code',
                    'src/main/res-transactionlist',
                    'src/main/res-modepay',
                    'src/main/res-cardpincode',
                    'src/main/res-abort-error',
                    'src/main/res-select-pump',
                    'src/main/res-select-product',
                    'src/main/res-settings',
                    'src/main/res-nfc',
                    'src/main/res-settings',
                    'src/main/res-iccpayment',
                    'src/main/res-iccpayment',
                    'src/main/res-esdsign',
                    'src/main/res-ticket',
                    'src/main/res-tims',
                    'src/main/res-amount',
                    'src/main/res-loyalty',
                    'src/main/res-product',
                    'src/main/res-badge',
                    'src/main/res-settings/res-card',
                    'src/main/res-settings/res-common',
                    'src/main/res-settings/res-operations',
                    'src/main/res-settings/res-backup',
                    'src/main/res-settings/res-maintenance'
            ]
        }
    }

    bundle {
        language {
            enableSplit = false
        }
    }

    lintOptions {
        checkReleaseBuilds true
        abortOnError true
    }
}

archivesBaseName = "FBSPay-v-${android.defaultConfig.versionName}-${android.defaultConfig.versionCode}"



ext {
    koin_version = "2.1.5"
    retrofit = "2.9.0"
    daggerVersion = "2.24"
    rxAndroidVersion = "2.9.0"
    }

dependencies {

    implementation fileTree(include: ['*.jar'], dir: 'libs')
    //noinspection GradleCompatible
    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    androidTestImplementation('androidx.test.espresso:espresso-core:3.2.0', {
        exclude group: 'com.android.support', module: 'support-annotations'
    })
    api fileTree(include: ['*.aar'], dir: 'libs')
    implementation files('libs/ifsfcomm-release-v2.4.aar')
    implementation 'org.springframework.android:spring-android-rest-template:1.0.1.RELEASE'
    implementation ('org.simpleframework:simple-xml:2.7.1',{
        exclude group:'stax', module:'stax'
        exclude group:'stax', module:'stax-api'
        exclude group:'xpp3', module:'xpp3'
    })

    // Architecture Components
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation "androidx.lifecycle:lifecycle-extensions:2.2.0"
    implementation 'com.google.android.gms:play-services-location:18.0.0'
    kapt "androidx.lifecycle:lifecycle-compiler:2.3.1"

    implementation 'com.intuit.sdp:sdp-android:1.0.6'
    implementation 'com.jakewharton.rxbinding2:rxbinding:2.1.1'

    // Koin for Kotlin
    implementation "org.koin:koin-androidx-scope:$koin_version"
    implementation "org.koin:koin-androidx-viewmodel:$koin_version"
    implementation "org.koin:koin-androidx-fragment:$koin_version"

    //Kotlin Coroutines
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:1.4.1"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.4.1"

    //Glide
    implementation 'com.github.bumptech.glide:glide:4.11.0'
    implementation 'jp.wasabeef:glide-transformations:4.0.0'

    //permission
    implementation 'com.nabinbhandari.android:permissions:3.8'
    testImplementation 'junit:junit:4.13'
    androidTestImplementation 'androidx.test:runner:1.4.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0'

    implementation 'androidx.appcompat:appcompat:1.3.0'
    implementation 'androidx.core:core-ktx:1.6.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.0.4'
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.google.android.material:material:1.3.0'

    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'io.reactivex:rxandroid:1.2.1'
    implementation 'io.reactivex:rxjava:1.3.8'
    implementation "com.squareup.retrofit2:adapter-rxjava:$rxAndroidVersion"

    //retrofit2
    implementation "com.squareup.retrofit2:converter-scalars:$rxAndroidVersion"
    implementation 'com.squareup.okhttp3:logging-interceptor:4.4.0'
    implementation "com.squareup.retrofit2:retrofit:$retrofit"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit"
    implementation('com.squareup.retrofit2:converter-simplexml:2.5.0') {
        exclude group: 'stax', module: 'stax-api'
        exclude group: 'stax', module: 'stax'
        exclude group: 'xpp3', module: 'xpp3'
        }

    implementation 'tech.hibk.searchablespinnerlibrary:searchablespinnerlibrary:1.0.1'
    kapt 'com.github.bumptech.glide:compiler:4.11.0'
    implementation 'jp.wasabeef:glide-transformations:4.0.0'
    implementation 'com.rishabhharit.roundedimageview:RoundedImageView:0.8.4'
    implementation 'com.baoyz.pullrefreshlayout:library:1.2.0'
    implementation 'pub.devrel:easypermissions:1.3.0'
    implementation 'com.google.android.gms:play-services-basement:17.6.0'
    implementation 'com.theartofdev.edmodo:android-image-cropper:2.6.0'
    implementation 'org.jsoup:jsoup:1.10.2'
    implementation('com.shagi:material-datepicker:1.3') {
        exclude group: 'com.android.support'
        }

    implementation 'com.github.tntkhang:full-screen-image-view-library:1.1.0'
    implementation 'testfairy:testfairy-android-sdk:1.+@aar'
    implementation 'com.igreenwood.loupe:loupe:1.2.1'
    implementation 'com.igreenwood.loupe:extensions:1.0.0'
    implementation 'net.zetetic:android-database-sqlcipher:4.5.1@aar'
    implementation 'androidx.sqlite:sqlite-ktx:2.2.0'
    // DEVICE SN INFORMATION
    implementation 'com.github.nisrulz:easydeviceinfo-base:2.3.3'
    implementation 'commons-codec:commons-codec:20041127.091804'
    implementation 'com.an.deviceinfo:deviceinfo:0.1.5'

    // Upload library
    def uploadServiceVersion = "4.5.5"
    implementation "net.gotev:uploadservice:$uploadServiceVersion"
    implementation "net.gotev:uploadservice-ftp:$uploadServiceVersion"

    // Xlog
    implementation 'com.elvishew:xlog:1.6.1'
    //Job Scheduler
    implementation 'com.evernote:android-job:1.4.2'

    implementation 'androidx.preference:preference-ktx:1.1.1'

    implementation "com.airbnb.android:lottie:3.4.0"
    implementation 'in.arjsna:passcodeview:1.2.1'
    implementation 'org.apache.commons:commons-lang3:3.9'
    //mpesa
    implementation 'com.github.ybq:Android-SpinKit:1.4.0'
    implementation 'com.jakewharton.timber:timber:4.6.0'
    implementation 'com.github.thomper:sweet-alert-dialog:1.4.0'
    //chuck
    debugImplementation 'com.readystatesoftware.chuck:library:1.1.0'
    releaseImplementation 'com.readystatesoftware.chuck:library-no-op:1.1.0'
    implementation 'com.github.rehmanmuradali:ticker:1.0.1'

    implementation 'com.github.lecho:hellocharts-library:1.5.8@aar'
    //implementation 'com.github.AAChartModel:AAChartCore-Kotlin:-SNAPSHOT'
    implementation 'com.wuhenzhizao:titlebar:1.2.0'
    implementation 'com.afollestad.material-dialogs:core:3.3.0'
    implementation 'com.afollestad.material-dialogs:input:3.3.0'
    implementation 'com.github.pvdberg1998:pnet:1.5.10'
    implementation 'com.facebook.stetho:stetho:1.5.0'
    implementation 'com.facebook.stetho:stetho-okhttp3:1.5.0'

    implementation('org.jpos:jpos:2.1.4') {
        exclude group: 'junit', module: 'junit'
        exclude group: 'com.sleepycat', module: 'je'
        }


    implementation 'com.whiteelephant:gifplayer:1.2.0'
    implementation 'com.github.basusingh:BeautifulProgressDialog:1.001'
    implementation 'com.github.MindorksOpenSource:ViewColorGenerator:v0.1'
    implementation 'com.daimajia.androidanimations:library:2.4@aar'
    implementation 'com.github.385841539:MarqueeView:1.0.0'
    implementation 'com.google.zxing:core:3.3.3'
    implementation('com.journeyapps:zxing-android-embedded:3.6.0') { transitive = false     }
    implementation 'me.dm7.barcodescanner:zxing:1.9.13'
    implementation 'com.datatheorem.android.trustkit:trustkit:1.1.3'
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.22'
    implementation 'com.teprinciple:updateapputils:2.3.0'
    implementation 'com.kaopiz:kprogresshud:1.0.2'
    //firebase
    implementation 'com.google.firebase:firebase-messaging:23.0.7'
    implementation 'com.google.firebase:firebase-core:21.1.0'

    implementation files('libs/pl2303g_multilib.jar')
    implementation files('libs/pl2303g_driver.jar')
    implementation files('libs/NeptuneLiteApi_V3.21.00_20210120.jar')
    implementation files('libs/EBEUnifiedLibrary_06022022.jar')

    implementation 'com.github.wwdablu:wZip:1.1.1'
    implementation 'commons-net:commons-net:3.6'
    implementation 'com.github.danielfelgar:draw-receipt:0.1.3'
    implementation 'com.github.ramt57:EasyLocaleApp:v1.0.3'
    implementation 'com.crossbowffs.remotepreferences:remotepreferences:0.8'
    implementation group: 'com.google.guava', name: 'guava', version: '27.1-jre'
    implementation "androidx.lifecycle:lifecycle-runtime-ktx:2.4.1"
    implementation 'com.github.evrencoskun:TableView:v0.8.9.4'
    implementation 'com.amitshekhar.android:android-networking:1.0.2'
    implementation 'com.github.jhg023:SimpleNet:1.6.6'

    implementation 'com.github.DantSu:ESCPOS-ThermalPrinter-Android:3.2.0'

    //Google Crashlytics
    implementation platform('com.google.firebase:firebase-bom:30.4.0')
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.sun.mail:android-mail:1.6.2'
    implementation 'com.sun.mail:android-activation:1.6.2'
    implementation 'com.tzx.json:jsonhandleview:1.2.2'
    implementation 'com.github.razir.progressbutton:progressbutton:2.1.0'
    implementation 'com.github.poovamraj:PinEditTextField:1.2.6'
    implementation files('libs/FPcore.jar')
    implementation 'com.github.anrwatchdog:anrwatchdog:1.4.0'
    
}
